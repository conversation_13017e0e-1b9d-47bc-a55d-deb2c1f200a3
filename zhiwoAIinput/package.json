{"name": "knowmetype", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "start:clear": "expo start --clear", "start:tunnel": "expo start --tunnel --clear", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "postinstall": "patch-package"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/ngrok": "^4.1.3", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/slider": "4.5.5", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.2.3", "@reduxjs/toolkit": "^2.6.1", "@supabase/auth-js": "^2.69.1", "@supabase/supabase-js": "^2.49.4", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "expo": "~52.0.40", "expo-apple-authentication": "~7.1.3", "expo-auth-session": "~6.0.3", "expo-av": "~15.0.2", "expo-blur": "~14.0.3", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.19", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-local-authentication": "^16.0.5", "expo-localization": "~16.0.1", "expo-router": "~4.0.19", "expo-speech-recognition": "^1.1.1", "expo-splash-screen": "~0.29.22", "expo-sqlite": "^15.2.14", "expo-status-bar": "~2.0.1", "expo-store-review": "~8.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.2", "i18next": "^22.5.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "lottie-react-native": "^7.2.3", "nanoid": "^3.3.6", "nativewind": "^2.0.11", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^12.3.1", "react-native": "0.76.9", "react-native-draglist": "^3.9.5", "react-native-gesture-handler": "~2.20.2", "react-native-modal": "^14.0.0-rc.1", "react-native-permissions": "^5.3.0", "react-native-purchases": "^8.9.5", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-sfsymbols": "^1.2.2", "react-native-share": "^12.0.10", "react-native-toast-message": "^2.3.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "react-redux": "^9.2.0", "tailwindcss": "^3.3.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-native": "^0.73.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.3.0", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "fs-extra": "^11.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react-native-dotenv": "^3.4.11", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "^5.8.2"}, "private": true}