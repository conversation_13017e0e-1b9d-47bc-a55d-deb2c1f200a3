import { ExpoConfig, ConfigContext } from 'expo/config';
import * as dotenv from 'dotenv';

// 根据环境加载不同的配置文件
const env = process.env.APP_ENV || process.env.NODE_ENV || 'development';

// 尝试加载对应环境的配置文件
const envFiles = [
  `.env.${env}`,           // 例如: .env.development, .env.staging, .env.production
  '.env.local',            // 本地覆盖文件
  '.env'                   // 通用配置文件
];

// 按优先级加载环境文件
envFiles.forEach(file => {
  try {
    dotenv.config({ path: file });
    console.log(`✅ 已加载环境配置: ${file}`);
  } catch (error) {
    // 忽略文件不存在的错误
  }
});

// 环境标识
const appEnv = process.env.APP_ENV || 'development';

// 基础配置（直接从环境文件读取，无需复杂逻辑）
const supabaseUrl = process.env.SUPABASE_URL || 'https://yenwfmoubflrluhqsyim.supabase.co';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InllbndmbW91YmZscmx1aHFzeWltIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MjM2NDMsImV4cCI6MjA1ODk5OTY0M30.P9t2dkOhfgPnt53WpDB2gQ6OLWJNvfPNFMqmihmTzXQ';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// 应用商店配置
const iosAppId = process.env.IOS_APP_ID || '6744967748';
const androidPackageName = process.env.ANDROID_PACKAGE_NAME || 'com.mindpowerhk.knowmetype';

// 环境配置
const isDevEnv = process.env.IS_DEV_ENV === 'true' || appEnv === 'development';
const showDevLogin = process.env.SHOW_DEV_LOGIN === 'true';

// 本地Supabase配置
const useLocalSupabase = process.env.USE_LOCAL_SUPABASE === 'true';
const localIp = process.env.LOCAL_IP || '*************';

// RevenueCat配置 (所有环境都读取，但优先从环境变量获取)
const revenueCatIosApiKey = process.env.REVENUE_CAT_IOS_API_KEY || '';
const revenueCatAndroidApiKey = process.env.REVENUE_CAT_ANDROID_API_KEY || '';

// 打印配置信息（仅开发环境）
if (appEnv === 'development') {
  console.log(`🔧 当前环境: ${appEnv}`);
  console.log(`🔧 Supabase URL: ${supabaseUrl}`);
  console.log(`🔧 开发模式: ${isDevEnv}`);
  console.log(`🔧 显示开发登录: ${showDevLogin}`);
  console.log(`🔧 使用本地Supabase: ${useLocalSupabase}`);
  console.log(`🔧 本地IP: ${localIp}`);
  console.log(`🔧 Supabase连接: ${useLocalSupabase ? '本地数据库' : '云端数据库'}`);

  // 添加环境变量调试信息
  console.log('=== 环境变量调试 ===');
  console.log(`process.env.IS_DEV_ENV = "${process.env.IS_DEV_ENV}"`);
  console.log(`process.env.SHOW_DEV_LOGIN = "${process.env.SHOW_DEV_LOGIN}"`);
  console.log(`computed isDevEnv = ${isDevEnv}`);
  console.log(`computed showDevLogin = ${showDevLogin}`);
  console.log('====================');
}

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: "KnowmeType",
  slug: "knowmetype",
  scheme: "knowmetype",
  owner: "mindpower-hongkong-limited",
  version: "1.0.0",
  orientation: "portrait",
  icon: "./assets/images/icon.png",
  userInterfaceStyle: "automatic",
  splash: {
    resizeMode: "contain",
    backgroundColor: "#ffffff", // 浅色模式背景
    dark: {
      backgroundColor: "#151718" // 深色模式背景
    }
  },

  extra: {
    // 环境标识
    appEnv,

    // Supabase配置
    supabaseUrl,
    supabaseAnonKey,
    supabaseServiceKey,

    // 环境配置
    isDevEnv,
    showDevLogin,

    // 本地Supabase配置
    useLocalSupabase,
    localIp,

    // 应用商店配置
    iosAppId,
    androidPackageName,

    // RevenueCat配置 (所有环境都传递，通过EAS环境变量配置)
    revenueCatIosApiKey,
    revenueCatAndroidApiKey,

    // EAS项目配置
    eas: {
      projectId: "882a46a1-9f61-423d-9d58-f5f653564a28",
    },

    // 重要说明：
    // AI服务密钥不再在客户端配置，统一通过Supabase边缘函数代理访问
    // 这样可以确保密钥的安全性和统一管理
  },

  plugins: [
    "expo-router"
  ],

  ios: {
    supportsTablet: true,
    bundleIdentifier: "com.mindpowerhk.knowmetype",
    infoPlist: {
      ITSAppUsesNonExemptEncryption: false,
      CFBundleAllowMixedLocalizations: true,
      CFBundleDevelopmentRegion: "en",
      CFBundleLocalizations: ["en", "zh-Hans", "zh-Hant"],
      LSHasLocalizedDisplayName: true,
      NSMicrophoneUsageDescription: "$(PRODUCT_NAME) needs access to your microphone for voice input",
      NSCameraUsageDescription: "$(PRODUCT_NAME) needs access to your camera for scanning QR codes",
      NSPhotoLibraryUsageDescription: "$(PRODUCT_NAME) needs access to your photo library to save and share images",
      NSUserTrackingUsageDescription: "This allows us to provide you with a better user experience",
      NSSpeechRecognitionUsageDescription: "$(PRODUCT_NAME) needs access to speech recognition for voice input",
      UIBackgroundModes: ["audio"]
    },
    entitlements: {
      "com.apple.security.device.microphone": true,
      "com.apple.security.device.camera": true,
      "com.apple.security.personal-information.photos-library": true
    }
  },
  android: {
    package: androidPackageName,
    adaptiveIcon: {
      foregroundImage: "./assets/images/adaptive-icon.png",
      backgroundColor: "#ffffff"
    }
  },

  // 添加网页支持（如果需要）
  web: {
    favicon: "./assets/images/favicon.png",
    bundler: "metro"
  }
}); 