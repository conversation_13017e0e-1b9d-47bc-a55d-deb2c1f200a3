# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# Expo / React Native
ios/
android/
node_modules/

# iOS
Pods/
*.xcworkspace
DerivedData/

# Android
.gradle/
build/
local.properties

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local
.env
.env.development
.env.staging  
.env.production

# 注意：模板文件应该被提交到git
# env-config.template

# Supabase边缘函数环境变量
supabase/.env.local
supabase/.env

# typescript
*.tsbuildinfo

app-example

# google client secret文件
client_secret_835907801081-ic1gnk12vrr430vg98cv2kqapqktiir3.apps.googleusercontent.com.json

# Supabase相关文件
supabase/.deno_cache/
supabase/.temp/
supabase/.branches/
backups/

# Deno
.deno_cache/
.deno/
.deno/* 

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
*.log

# 软件著作权文档鉴别材料
软件著作权文档鉴别材料.md
