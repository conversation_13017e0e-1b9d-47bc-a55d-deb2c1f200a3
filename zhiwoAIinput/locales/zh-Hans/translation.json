{"common": {"save": "保存", "cancel": "取消", "delete": "删除", "edit": "编辑", "confirm": "确认", "back": "返回", "next": "下一步", "previous": "上一步", "loading": "加载中...", "error": "错误", "success": "成功", "warning": "警告", "info": "信息", "retry": "重试", "ok": "确定", "skip": "跳过", "done": "完成", "search": "搜索", "noResults": "未找到结果", "close": "关闭", "submit": "提交", "continue": "继续", "select": "选择", "languageChangeSuccess": "语言已更改。请重启应用使更改完全生效。", "languageChangeFailed": "更改语言失败，请稍后重试。", "upgradeNow": "立即升级", "copySuccess": "已复制到剪贴板", "basicModel": "(基础模型)", "premiumModel": "(高级模型)", "default": "默认", "sensitiveWord": {"filterTitle": "优化失败", "filterMessage": "优化失败，请重新录制", "filterMessageShort": "优化失败，请重试", "optimizationFailed": "优化失败，请重试"}, "permissions": {"microphone": {"title": "需要麦克风权限", "description": "应用需要访问您的麦克风才能进行语音输入，请在设置中开启麦克风权限。", "usage": "麦克风权限用于录制语音"}, "speech": {"title": "需要语音识别权限", "description": "应用需要语音识别权限才能将语音转换为文字，请在设置中开启语音识别权限。", "usage": "语音识别权限用于语音转文字"}, "both": {"title": "需要麦克风和语音识别权限", "description": "应用需要访问您的麦克风和语音识别功能才能正常工作，请在设置中开启相关权限。"}, "guide": {"openSettings": "前往设置", "skipForNow": "稍后再说", "checkingPermissions": "检查权限中..."}}}, "home": {"whisperModelEnabled": "已为你启用高级语音模型（Whisper）", "title": "知我AI输入法", "inputTitle": "点击下方按钮开始输入语音", "inputSubtitle": "支持多段语音输入，AI将自动优化文本", "startRecording": "开始录音", "stopRecording": "停止录音", "processing": "处理中...", "optimize": "优化", "copy": "复制", "share": "分享", "clear": "清空", "selectTemplate": "选择模板", "noTemplate": "无模板", "optimizedText": "优化后文本", "originalText": "原始文本", "editHint": "您可以在此编辑文本内容", "recording": "正在录音...", "transcribing": "正在转录...", "selectStyle": "选择风格", "optimizationResult": "优化结果", "aiOptimizing": "AI正在优化文本...", "vipExclusive": "VIP专属权益生效中", "vipHint": "VIP会员可使用更快速的高级模型", "regenerate": "重新生成", "selectTemplateHint": "选择风格模板", "moreStyles": "更多风格", "noVoiceDetected": "没有检测到语音内容", "recordingError": "录音失败", "recordingErrorMsg": "无法启动语音识别，请检查应用权限设置。", "recordingTimeout": "录音超时", "recordingTimeoutMsg": "长时间未检测到语音输入，请选择继续录音或结束当前录音。", "stopRecordingBtn": "结束录音", "continueRecordingBtn": "继续录音", "speakNow": "请说话", "tapToPause": "轻触麦克风可以暂停/继续录音", "clickToStartRecording": "点击下方按钮开始语音输入", "recordingHint": "支持多段语音输入，AI将自动优化文本", "inputOrEditText": "输入或编辑文本...", "newVoiceInput": "新的语音输入", "finish": "完成", "pause": "暂停", "continue": "继续", "newInput": "新的语音输入", "retry": "重试", "upgradeNow": "立即升级", "upgradeToVIP": "升级为VIP", "vipUpgradeDescription": "无限使用高级模型、自定义风格模板、优先处理", "optimizingText": "正在优化...", "retryFailed": "重试失败", "audioFileCorrupted": "音频文件可能已损坏，请重新录音", "audioFileNotFound": "找不到音频文件，请重新录音", "retryTranscriptionFailed": "重试转写调用失败", "retryTranscriptionFailedMsg": "重试转写调用失败，请稍后再试", "retryFailedWithError": "重试失败", "featureTags": {"smartReworking": "智能润色", "sceneAdaptation": "场景适配", "personalizedTemplates": "个性化模板"}, "textDisplay": {"emptyText": "请点击重新生成按钮重试", "speakNow": "请开始说话...", "listening": "正在聆听中...", "pausedRecording": "录音已暂停", "clickTemplate": "点击选择模板", "waiting": "等待中...", "recording": "正在录音中...", "transcribing": "正在转写中...", "transcribingWithModel": "正在使用{{model}}模型转录...", "retryTranscribing": "正在重试使用{{model}}模型转写...", "speechRecognitionFailed": "语音转写失败", "noVoiceContentDetected": "没有检测到语音内容", "noVoiceContentDetectedRetry": "没有检测到语音内容，请重试", "requestTimeout": "请求超时，请重试", "networkError": "网络连接错误，请重试", "processing": "正在处理...", "retryTranscribingShort": "正在重试转写...", "retryTranscriptionShort": "正在重试转录...", "transcribingShort": "转录中...", "transcriptionShort": "转录中...", "recordingWithTime": "正在录音中...{{time}}秒", "listeningWithTime": "正在聆听中...已录制{{time}}秒", "retryTranscribingWithModel": "正在重试使用{{model}}模型转录...", "processingProgress": "处理中... {{percentage}}% (预计还需{{seconds}}秒)", "uploadingAudio": "正在上传音频文件 ({{size}}MB)...", "processingAudio": "正在处理音频: {{message}}", "noResponse": "太久没响应？点击重新输入语音", "directTranscription": "直接转录", "usingDefaultTemplate": "使用默认模板进行风格优化", "usingDefaultModel": "使用默认模型进行风格优化"}, "shareOptions": {"title": "选择分享内容", "originalContent": "原始内容", "optimizedContent": "AI优化内容", "originalAudio": "原始音频", "cancel": "取消"}, "networkError": {"title": "网络连接失败", "message": "语音转写过程中遇到网络问题，请检查网络连接后重试", "tip1": "• 检查网络连接是否正常", "tip2": "• 确认已连接WiFi或移动数据", "tip3": "• 稍后重试或重新录音", "retryButton": "重试转写", "rerecordButton": "重新录音", "retrySuccess": "重试成功", "retryFailed": "重试失败，请确保网络畅通后再试", "audioCorrupted": "多次重试失败，建议重新发起录音", "retryFailedWithNetwork": "重试失败，请确保网络畅通后再试。如多次尝试失败，建议重新发起录音"}, "transcribingStatus": "转录中...", "transcribingNoResponse": "太久没响应？点击重新输入语音", "whisperLimit": {"loginPrompt": "请先登录后升级VIP以解锁更多语音转录额度", "switchToNative": "已切换为设备语音识别。", "title": "已达到今日使用限制", "message": "您今日的高级语音转写次数已用完", "upgradeOption": "升级VIP享无限次数", "switchOption": "改用设备语音识别", "remainingCount": "今日剩余{{count}}次", "vipUnlimited": "VIP用户无使用限制", "usageStats": "今日已使用 {{used}}/{{limit}} 次", "tip1": "• 升级VIP享受无限制语音转录", "tip2": "• 或改用设备语音识别继续使用", "tip3": "• 明日将重新获得{{limit}}次免费额度"}}, "history": {"detail": {"jumpToResult": "跳转到结果", "title": "历史详情", "backToList": "返回列表", "goToResultPage": "跳转到结果页", "originalContent": "原始内容", "optimizedContent": "AI优化内容", "optimizationSuccess": "优化成功", "optimizationSuccessDesc": "已使用新模板生成优化结果，点击查看详情页", "optimizationFailed": "优化失败", "optimizationFailedDesc": "请稍后再试", "reoptimize": "二次优化", "share": "分享", "noOriginalContent": "暂无原始语音内容", "noOptimizedContent": "暂无AI优化内容", "optimizationHistory": "为你保留所有历史优化记录", "copy": "复制", "reoptimizedInfo": {"title": "二次优化记录", "description": "此记录通过文本二次优化生成，无原始音频", "viewOriginal": "查看原始记录", "titleSimple": "二次优化记录", "viewOriginalSimple": "原始记录"}, "sections": {"originalAudio": "原始语音内容", "aiOptimized": "AI优化结果"}, "actions": {"copyText": "复制文本", "reoptimize": "二次优化", "shareResult": "分享结果"}, "templateModal": {"title": "选择优化模板", "sourceText": "优化源文本：", "optimizedText": "优化后文本", "originalText": "原始文本", "optimizeButton": "开始优化", "cancelButton": "取消", "optimizing": "优化中...", "selectTemplate": "选择模板风格：", "startOptimize": "开始优化"}, "toast": {"copySuccess": "已复制到剪贴板", "selectSingleResult": "请选择单个优化结果进行复制", "optimizationSuccess": "优化成功", "optimizationSuccessMessage": "已使用新模板生成优化结果，返回列表查看", "optimizationFailed": "优化失败", "optimizationFailedMessage": "请稍后再试", "viewHistoryList": "查看历史列表"}, "errors": {"templateNotFound": "未找到选中的模板"}, "sourceRecordDeleted": "原始记录已删除", "sourceRecordDeletedDesc": "此二次优化记录的原始记录已被删除，无法查看原始内容"}, "today": "今天", "yesterday": "昨天", "dayBeforeYesterday": "前天", "thisWeek": "本周", "thisMonth": "本月", "earlier": "更早", "expand": "展开", "collapse": "收起", "share": "分享", "delete": "删除", "copy": "复制", "all": "全部", "workRelated": "工作相关", "email": "邮件", "meetingNotes": "会议记录", "chat": "聊天", "important": "重要", "personal": "个人", "draft": "草稿", "archived": "已归档", "title": "历史记录", "noRecords": "暂无历史记录", "deleteConfirm": "删除记录不可恢复，请确认", "deleteConfirmMultiple": "确定要删除选中的{{count}}条记录吗？", "deleteConfirmTitle": "确定要删除吗？", "deleteSuccess": "删除成功", "deleteSuccessMultiple": "已成功删除{{count}}条记录", "copySuccess": "已复制到剪贴板", "shareSuccess": "分享成功", "searchPlaceholder": "搜索历史记录...", "selectAll": "全选", "deselectAll": "取消全选", "select": "选择", "cancel": "取消", "vipAllRecords": "已显示全部 {{count}} 条记录", "freeUserLimitedRecords": "已显示最近 {{max}} 条记录（升级VIP查看全量历史记录）", "freeUserAllRecords": "已显示全部 {{count}} 条记录", "templateAllRecords": "已显示\"{{template}}\"的全部 {{count}} 条记录", "templateLimitedRecords": "已显示\"{{template}}\"的 {{count}} 条记录(前50条中)", "deleteSelected": "删除选中项", "noRecordsFound": "未找到匹配的记录", "loadMore": "加载更多...", "loading": "加载中...", "deleteAllData": "删除所有数据", "deleteAllDataConfirm": "确定要删除所有数据吗？", "deleteAllDataSuccess": "所有数据已删除", "deleteAllDataSuccessDesc": "所有数据已删除，您可以重新开始。", "pullToRefresh": "下拉刷新", "releaseToRefresh": "释放刷新", "refreshing": "刷新中...", "emptyStateTitle": "暂无历史记录", "emptyStateSubtitle": "您还没有任何历史记录，点击下方按钮开始记录", "startRecording": "开始输入", "selectMode": "选择模式", "selectedCount": "已选择 {{count}} 项", "vipUpgradeTitle": "升级VIP解锁更多记录", "vipUpgradeSubtitle": "还有 {{count}} 条历史记录被隐藏", "vipFeature1": "无限历史记录保存", "vipFeature2": "高级AI模型优化", "vipFeature3": "更多个性化模板", "upgradeNow": "立即升级", "vipUpgrade": {"title": "查看更多历史记录", "subtitle": "还有 {{count}} 条记录需要VIP权限查看", "upgradeButton": "升级VIP"}}, "settings": {"appName": "知我AI输入法", "languageChangeFailed": "切换语言失败，请稍后再试", "wechatLogin": "微信登录", "appleLogin": "Apple登录", "googleLogin": "Google登录", "emailLogin": "邮箱登录", "phoneLogin": "手机号登录", "title": "设置", "account": "账号", "selectTranscribeModel": "选择转写模型", "selectLanguageModel": "选择语言模型", "uiLanguage": "界面语言", "selectLanguage": "选择语言", "themeStyle": "界面风格", "selectThemeStyle": "选择界面风格", "themeSystemStyle": "系统风格", "themeSystemStyleDesc": "根据系统设置自动切换", "themeDarkStyle": "深色风格", "themeDarkStyleDesc": "始终使用深色界面", "themeLightStyle": "浅色风格", "themeLightStyleDesc": "始终使用浅色界面", "fastMode": "极速模式", "fastModeDescription": "开启后无需每次选择风格模板", "fastModeDisabledDescription": "需要先开启风格模式", "styleOptimization": "风格模式", "styleOptimizationDescription": "开启后会使用AI对文本进行风格优化", "appLock": {"title": "应用加密", "description": "开启后进入历史页面需要生物识别验证", "notSupported": "设备不支持", "notSupportedDesc": "您的设备不支持生物识别认证功能", "testAuth": "请验证您的身份以启用应用加密", "disableAuth": "请验证您的身份以关闭应用加密", "usePassword": "使用密码", "authFailed": "认证失败", "authFailedDesc": "生物识别认证失败，请重试", "enabled": "应用加密已启用", "disabled": "应用加密已关闭", "toggleFailed": "切换应用加密失败，请重试", "authPage": {"checkingAccess": "正在检查访问权限", "pleaseWait": "请稍候...", "appLocked": "应用已加密", "verifyIdentity": "请验证您的身份以访问历史记录", "authenticating": "正在验证...", "retryAuth": "重新验证"}}, "styleTemplate": "风格模板", "templatePersonalization": "模板个性化设置", "templatePersonalizationSubtitle": "自定义模板显示和排序", "templatePersonalizationPortal": "管理", "templateManagerInstructions": "• 长按模板可以拖拽调整显示顺序\n• 点击右上角 ⊗ 隐藏模板，点击 ⊕ 显示模板\n• 默认模板始终位于第一位，无法移动或隐藏", "templateManagerLoading": "加载模板数据...", "templateManagerSaving": "正在保存设置...", "templateManagerLoadError": "加载模板数据失败，请重试", "templateManagerSaveError": "保存设置失败，请重试", "templateManagerSystemTag": "系统", "templateManagerCustomTag": "自定义", "templateManagerDefaultTag": "默认", "templateManagerDeletedTemplate": "已删除的模板", "templateManagerDeletedTemplateDesc": "此模板已被删除，但仍保留在偏好设置中", "templateManagerOrderExplanation": "模板显示顺序：1-3为选择风格页面第一排，4-6为第二排，7+为「更多风格」区域", "templateManagerDragHint": "💡 长按模板卡片可拖拽排序", "templateManagerDefaultNotMovable": "默认模板无法移动", "templateManagerDefaultNotMovableMessage": "默认模板始终位于第一位，无法移动或调整顺序", "templateManagerCannotReplaceDefault": "无法替换默认模板位置", "templateManagerCannotReplaceDefaultMessage": "第一位为默认模板专用位置，其他模板无法移动到此位置", "transcribeModel": "语音转写模型", "languageModel": "语言模型", "theme": "主题", "about": "关于", "feedback": {"modalTitle": "我们需要您的反馈❤️", "modalDescription": "您的反馈是我们进步的动力", "rateButton": "给个好评", "rateDescription": "在应用商店为我们评分", "feedbackButton": "问题反馈", "feedbackDescription": "向我们反馈使用问题或建议", "cancel": "取消"}, "rateUs": "给个好评", "rateUsDescription": "喜欢我们的应用吗？给我们评分吧！", "rateInAppStore": "在App Store中评价", "rateInGooglePlay": "在Google Play中评价", "thankYouForRating": "感谢您的评价！", "ratingNotAvailable": "评分功能暂时不可用", "openStoreError": "打开应用商店失败，请稍后重试", "privacyPolicy": "隐私政策", "termsOfService": "使用协议", "logout": "退出登录", "logoutConfirm": "确定要退出登录吗？", "switchingLanguage": "正在切换界面语言...", "deleteAllData": "删除所有数据", "dataManagement": "数据管理", "version": "版本 {{version}}", "versionUpdate": "版本更新", "vipExclusiveFeature": "VIP专属功能", "onlyVIPUsersCanUseThisModel": "只有VIP用户才能使用此模型", "upgradeToUseVipModel": "此模型仅限VIP用户使用，是否升级VIP会员以解锁高级模型？", "vipBadgeText": "知我VIP", "vipCardTitle": "解锁所有高级功能", "vipCardSubtitle": "享受无限AI优化和高级模型", "unlimitedCustomTemplate": "无限自定义模板", "advancedAIModel": "高级AI模型", "priorityRequest": "优先请求", "fullHistoryRecord": "完整历史记录", "upgradeNow": "立即升级", "vipSubscribed": "已开通VIP", "checkVipDetail": "查看VIP权益", "vipSubscribedCardTitle": "尊贵会员专享权益", "vipSubscribedCardSubtitle": "享受无限AI优化和高级模型", "loginGuideTitle": "登录以解锁全部功能", "loginGuideSubtitle": "自定义模板、自定义AI模型", "loginGuideButtonText": "登录", "toast": {"settingsSaved": "设置已保存", "settingsSaveSuccess": "设置已保存并同步", "settingsSaveFailed": "设置保存失败，请稍后再试", "localSettingsSaveFailed": "本地设置保存失败", "cloudSyncFailed": "云端同步失败，但本地设置已保存", "settingsSavedWithIssue": "设置已保存，但同步可能有问题", "languageChangeFailed": "切换语言失败，请稍后重试", "voiceModelSwitched": "已切换为设备语音识别。"}, "vipFeatures": {"title": "VIP功能", "message": "此功能仅限VIP会员使用，请先升级", "aiModelSettingTitle": "AI模型设置", "aiModelSettingMessage": "您可以选择不同的AI模型：DeepSeek-R1, GPT-4或者其他高级模型"}, "languageNames": {"zh-Hans": "简体中文", "zh-Hant": "繁體中文", "en": "English"}, "subscriptionPlans": {"monthly": "月度套餐", "yearly": "年度套餐", "vip": "VIP套餐"}, "authProviders": {"unknown": "未知方式登录"}, "validityInfo": {"notAvailable": "未获取到有效期信息"}, "models": {"nativeTranscribe": {"ios": "iOS原生转写", "android": "Android原生转写"}}, "prompts": {"upgradeVipTitle": "升级VIP会员", "upgradeVipMessage": "请先登录以升级VIP会员", "modelSettingsTitle": "修改模型设置", "modelSettingsMessage": "请先登录以修改模型设置"}, "periodUnit": {"month": "/月", "year": "/年"}, "productFeedback": "产品问题反馈", "feedbackEmailSubject": "知我AI输入法反馈 - {{platform}} v{{version}}", "feedbackEmailBodyLine1": "应用名称: 知我AI输入法", "feedbackEmailBodyLine2": "应用版本: {{version}}", "feedbackEmailBodyLine3": "系统类型: {{platform}}", "feedbackEmailBodyLine4": "系统版本: {{systemVersion}}", "feedbackEmailBodyLine5": "用户ID: {{userId}}", "feedbackEmailBodyLine6": "\n请在此描述您遇到的问题或建议：\n\n"}, "dataManagement": {"title": "数据管理", "appLock": "应用加密", "appLockDesc": "开启后进入历史页面需要生物识别验证", "deleteLocalData": "删除本地数据", "deleteLocalDataDesc": "清空历史记录和自定义模板", "deleteLocalDataTitle": "删除本地数据", "deleteLocalDataMessage": "确定要删除所有本地数据吗？这将清空历史记录和自定义模板", "deleteLocalDataSuccess": "本地数据已删除", "deleteLocalDataError": "删除本地数据失败", "deleteAccount": "注销账号", "deleteAccountDesc": "永久删除账号和所有相关数据", "deleteAccountTitle": "注销账号", "deleteAccountMessage": "注销后将删除您的账号和所有数据，此操作不可撤销", "deleteAccountConfirmTitle": "确认注销", "deleteAccountConfirmMessage": "您真的要注销账号吗？此操作无法撤销", "deleteAccountSuccess": "账号已注销", "deleteAccountError": "注销账号失败", "userNotLoggedIn": "用户未登录", "warningMessage": "删除操作不可撤销，请谨慎操作。删除本地数据不会影响账号，但注销账号将永久删除您的所有信息。"}, "tabs": {"input": "输入", "history": "历史", "settings": "设置"}, "templates": {"editPrompt": "编辑提示词", "vipTemplateMessage": "此模板为VIP专属，请升级后使用", "updateSuccess": "模板更新成功", "loginToCopyTemplate": "登录以复制模板", "createTemplate": "创建模板", "loginToCreateTemplate": "登录以创建模板", "copySuffix": "副本", "editButton": "编辑", "deleteButton": "删除", "gotIt": "知道了", "setDefaultTitle": "设为默认", "setDefault": "设为默认", "setDefaultMessage": "设为默认模板", "templateManagement": "风格模板管理", "systemTemplate": "预设模板", "customTemplate": "自定义模板", "noCustomTemplate": "暂无自定义模板", "addNewTemplate": "添加新模板", "longPressToCopy": "长按可创建副本", "systemLabel": "预设", "customLabel": "自定义", "defaultLabel": "默认", "syncedLabel": "已同步", "defaultTemplate": "默认模板", "setAsDefault": "设为默认", "editTemplate": "编辑模板", "newTemplate": "新建模板", "copyButton": "复制", "copyTemplate": "复制模板", "copyTemplateDesc": "创建此模板的副本", "deleteTemplate": "删除模板", "deleteConfirm": "确定要删除此模板吗？", "deleteSuccess": "模板删除成功", "setDefaultSuccess": "已设为默认模板", "copySuccess": "复制成功", "copySuccessMessage": "模板复制成功，你可以二次编辑生成自定义模板", "templateName": "模板名称", "templateDescription": "模板描述", "templatePrompt": "提示词", "templateNamePlaceholder": "输入模板名称", "descriptionPlaceholder": "输入模板描述", "promptPlaceholder": "输入提示词，指导AI如何优化文本", "saveTemplate": "保存模板", "saveSuccess": "保存成功", "saveFailed": "保存失败", "invalidInput": "输入无效，请检查后重试", "systemTemplateCannotEdit": "系统预设模板不能修改", "basicInfo": "基本信息", "usageInstructions": "使用说明", "usageInstructionsTitle": "如何写好的提示词？", "usageInstructionsText": "1. 提示词应该清晰描述文本预期的风格和内容特征。\n2. 你可以指定特定的语气、情感、格式或专业术语。\n3. 添加具体示例可以帮助AI更好地理解你的需求。\n4. 提示词越详细，越符合你的预期。", "usageInstructionsExampleTitle": "示例提示词", "usageInstructionsExampleText": "请将文本组织成正式的电子邮件格式，使用礼貌和专业的语言，确保语法正确，并保留所有重要信息。在开头添加适当的问候语，在结尾添加礼貌的结束语。", "vipNoteText": "免费用户只能创建和编辑1个自定义模板。升级为VIP以解锁无限创建。", "description": "描述", "setDefaultTemplate": "设为默认模板", "templatePromptHint": "输入模板提示词", "templateNameHint": "输入模板名称", "templateDescriptionHint": "输入模板描述", "templatePromptPlaceholder": "输入模板提示词", "templateDescriptionPlaceholder": "输入模板描述", "templateNameRequired": "模板名称不能为空", "deleteConfirmButton": "删除此模板", "deleteConfirmText": "确定要删除此模板吗？", "deleteFailed": "删除失败", "confirmDeleteTitle": "确认删除", "confirmDeleteMessage": "确定要删除此模板吗？", "vipLimitTitle": "达到模板数量上限", "vipLimitMessage": "升级VIP解锁无限创建", "vipTemplateTitle": "VIP模板访问", "vipTemplateAccessMessage": "此模板为VIP专享，请先登录并升级到VIP会员", "vipTemplateFallback": "升级VIP以使用VIP专用模板", "vipUpgradeModal": {"title": "达到模板数量上限", "description": "免费用户只能创建1个自定义模板，升级VIP解锁无限创建", "upgradeButton": "立即升级VIP", "priceHint": "随时可取消"}, "vipRequired": "需要VIP会员", "vipRequiredMessage": "该模板为VIP专享，请升级到VIP会员后使用", "vipRequiredForOptimization": "使用VIP专享模板需要VIP会员权限", "deleteTemplateDesc": "确定要删除此模板吗？", "cannotDeleteSystemTemplate": "不能删除系统预设模板", "setDefaultFailed": "设置默认模板失败"}, "errors": {"networkError": "网络错误，请检查您的网络连接", "serverError": "服务器错误，请稍后重试", "timeout": "请求超时，请检查您的网络连接", "unknownError": "未知错误，请稍后重试", "invalidInput": "输入无效，请检查后重试", "permissionDenied": "权限被拒绝，请检查应用权限设置"}, "auth": {"login": "登录", "signup": "注册", "email": "邮箱", "password": "密码", "forgotPassword": "忘记密码？", "noAccount": "还没有账号？", "hasAccount": "已有账号？", "loginSuccess": "登录成功", "signupSuccess": "注册成功", "logoutSuccess": "已退出登录"}, "vipDetail": {"title": "知我AI输入法VIP权益", "vipStatus": "VIP会员 ({{planName}})", "validUntil": "有效期至 {{date}}", "featuresTitle": "会员权益", "changePlan": "更换订阅套餐", "validityNotAvailable": "未获取到有效期信息", "plans": {"monthly": "月度套餐", "yearly": "年度套餐", "default": "VIP套餐"}, "features": {"unlimitedTemplates": {"title": "无限自定义风格模板", "description": "创建并保存无限数量的自定义AI风格模板，快速一键应用到任何文本"}, "advancedModel": {"title": "高级AI模型", "description": "使用最先进的AI模型进行文本优化，生成更精准、更自然的内容"}, "priorityProcessing": {"title": "优先处理请求", "description": "您的所有请求都将优先处理，大幅减少等待时间"}, "cloudSync": {"title": "模板云端同步", "description": "云端同步自定义模板，换个设备随时使用你喜欢的风格模板"}}}, "payment": {"currentPackage": "当前套餐", "title": "开通VIP会员", "priceHint": "随时可取消", "getVip": "升级知我AI输入法 VIP", "unlockPremium": "解锁高级AI表达能力 — 升级VIP享受更强大的功能！", "award2025": "2025年度最佳", "awardApp": "语音输入应用", "vipBenefits": "VIP权益", "userReviews": "用户评价", "subscriptionOptions": "订阅选项", "monthly": "月度", "yearly": "年度", "billedAs": "账单金额 {{price}}/{{period}}", "trialBilling": "，{{days}}天免费试用后收费", "subscribeNow": "立即开通{{type}}VIP", "startTrial": "开始{{days}}天免费试用", "restorePurchases": "恢复购买", "termsOfService": "服务条款", "privacyPolicy": "隐私政策", "subscriptionTerms": "订阅会自动续期，可随时在{{store}}账户设置中取消。", "subscriptionTermsIos": "App Store", "subscriptionTermsAndroid": "Google Play", "paymentInfoIos": "付款将在确认购买时通过iTunes账户收取。", "paymentInfoAndroid": "付款将在确认购买时通过Google Play账户收取。", "benefits": {"unlimitedTemplates": {"title": "无限自定义风格模板", "description": "创建并保存无限数量的自定义AI风格模板，快速一键应用到任何文本"}, "advancedModel": {"title": "高级AI模型", "description": "使用最先进的AI模型进行文本优化，生成更精准、更自然的内容"}, "priorityRequest": {"title": "优先处理请求", "description": "您的所有请求都将优先处理，大幅减少等待时间"}, "cloudSync": {"title": "模板云端同步", "description": "云端同步自定义模板，换个设备随时使用你喜欢的风格模板"}}, "reviews": {"review1": {"title": "再也不用纠结如何表达了！", "content": "AI自动优化文本，让我的表达更加精准流畅，节省了大量时间，效率提升了不止一倍！", "author": "张先生"}, "review2": {"title": "管理生图提示词太方便了", "content": "用knowmeType整理生图提示词，十分方便，一句话出图，效率超T！直也不用手动编写复杂的提示词了。", "author": "张艺，AI绘画师"}, "review3": {"title": "录音功能超好用", "content": "不仅是语音输入，还可以当录音机使用，希望增加更多录音编辑整理功能，会议记录变得超级简单！", "author": "王琳，会议小助手"}, "review4": {"title": "输入法替代品", "content": "如果能替代我的传统输入法就好了，希望快点完善！期待这款应用成为我手机上的默认输入法。", "author": "陈浩，科技爱好者"}, "review5": {"title": "省时又高效", "content": "好奇试了一下，的确很棒，相对传统的语音输入，最大的变化就是不需要二次编辑，节省了我大大量时间。", "author": "刘佳，效率控"}, "review6": {"title": "找表情功能太强了", "content": "再也不用费劲找emoji啦，说出情绪就能自动添加合适的表情，朋友们都羡慕我的聊天技能提升了！", "author": "杨花，社交达人"}, "review7": {"title": "写作灵感源泉", "content": "作为一名作家，这款应用帮我快速记录灵感，是创作的得力助手。再也不会因为打字慢而忘记创意了。", "author": "周星，网络作家"}, "review8": {"title": "语言学习者福音", "content": "语音输入加AI优化，让我的外语学习事半功倍。我现在可以轻松练习口语，看到正确的文本反馈！", "author": "赵国际，语言学习者"}, "review9": {"title": "大学生的笔记利器", "content": "和女朋友聊天再也不词穷了！AI自动让表达更加逻辑清晰，彻底摆脱了直男语言，表情推荐超准确，她说我变得更有情调，成功追到了无数次争吵！", "author": "孙光，大学生"}}, "errors": {"purchaseFailed": "购买失败", "purchaseError": "处理订阅时出现问题，请稍后重试", "restoreFailed": "恢复失败", "restoreError": "无法恢复您的购买，请稍后重试", "noSubscriptionFound": "未找到订阅", "noActiveSubscription": "未找到可恢复的活跃订阅", "loginRequired": "请先登录", "loginRequiredMessage": "请登录后再进行订阅", "subscriptionError": "订阅错误", "noSubscriptionAvailable": "未找到可用的订阅套餐，请稍后重试"}, "success": {"purchaseSuccess": "订阅成功", "purchaseSuccessMessage": "您已成功订阅VIP会员，感谢您的支持！", "restoreSuccess": "恢复成功", "restoreSuccessMessage": "您的VIP会员已恢复"}, "loading": {"loading": "加载中...", "processing": "处理中..."}, "periodUnit": {"month": "/月", "year": "/年"}}, "login": {"title": "欢迎使用知我AI输入法", "subtitle": "登录以解锁全部功能，使用高级语音模型。", "wechatLogin": "使用微信账号登录", "appleLogin": "使用Apple账号登录", "googleLogin": "使用Google账号登录", "emailLogin": "使用邮箱账号登录", "phoneLogin": "使用手机号登录", "devLoginTitle": "开发环境测试登录", "emailPlaceholder": "邮箱", "passwordPlaceholder": "密码", "devLoginButtonText": "账号登录", "skipLogin": "先不登录，继续使用", "footerText": "登录即表示您同意我们的服务条款和隐私政策", "serviceTerms": "服务条款", "privacyPolicy": "隐私政策", "and": "和", "login": "登录", "loadingText": "登录中..."}, "aiModels": {"qwen2.5-7b": {"name": "QWen 2.5-7B", "description": "阿里通义千问大语言模型，基础免费模型"}, "qwen3-8b": {"name": "QWen 3-8B", "description": "通义千问第三代中型模型，性能更优"}, "qwen3-14b": {"name": "QWen 3-14B", "description": "通义千问第三代高性能模型，适合复杂文本"}, "deepseek-v3": {"name": "DeepSeek V3", "description": "顶级大语言模型，适合复杂场景的理解和创作"}, "native": {"name": "原生转写", "description": "本地模型，实时转写，速度快，准确率较低，适合安静环境使用"}, "whisper-1": {"name": "Whisper", "description": "OpenAI Whisper 语音识别，准确度高，智能降噪，覆盖大部分日常场景"}, "gpt-4o-mini-transcribe": {"name": "GPT-4o Mini", "description": "高精度智能转写，支持复杂场景和多语言"}}, "template": {"email": {"name": "发邮件", "description": "一键生成专业得体的商务邮件，自动适配收件人身份和邮件场景。"}, "chat": {"name": "日常发信息", "description": "让闲聊消息更自然生动，自动匹配好友亲密度和对话场景。"}, "notes": {"name": "做notes记录", "description": "将零散信息转化为结构化的纯文本笔记，支持学习/会议/灵感记录场景。"}, "markdown_notes": {"name": "markdown格式的要点记录", "description": "使用markdown格式生成结构化要点记录，支持标题层级和格式化。"}, "text_optimize": {"name": "原文优化", "description": "保持原文基础上修正错误、删除重复和语气词，优化段落结构。"}, "emoji": {"name": "emoji表情", "description": "智能添加表情符号增强情绪表达，避免滥用尴尬场景。"}, "xhs": {"name": "小红书文案", "description": "打造爆款种草文案，自动生成分段标题+标签+emoji组合拳。"}, "moment": {"name": "社交平台文案", "description": "创作高互动率社交平台文案，自动平衡真实感和精致度。"}, "enTrans": {"name": "英语翻译", "description": "母语级英译优化，智能处理俚语和文化隐喻。"}, "jaTrans": {"name": "日语翻译", "description": "适配日本敬语体系，区分商务/动漫/日常场景。"}, "koTrans": {"name": "韩语翻译", "description": "精准转换韩语敬语与半语，自动区分商务与K-pop流行语境。"}, "idTrans": {"name": "印尼语翻译", "description": "适配东南亚多方言场景，智能处理宗教与文化敏感词。"}, "deTrans": {"name": "德语翻译", "description": "严谨处理德语格与词性，自动适配法律/工程/日常场景。"}, "frTrans": {"name": "法语翻译", "description": "优雅处理法语阴阳性与变位，强化文学性与商务正式感。"}, "esTrans": {"name": "西班牙语翻译", "description": "灵活转换拉丁美洲/西班牙变体，智能处理倒装句结构。"}, "zhCnTrans": {"name": "中文翻译（简体）", "description": "精准翻译各种语言为规范简体中文，保持原意并符合中文表达习惯。"}, "zhTwTrans": {"name": "中文翻译（繁体）", "description": "精准翻译各种语言为标准繁体中文，保持原意并符合繁体中文表达习惯。"}, "recraft": {"name": "Recraft生图", "description": "生成矢量艺术专用提示词，自动匹配平面设计规范。"}, "midjourney": {"name": "Midjourney V7", "description": "适配V7最新参数，强化细节控制与风格混合能力。"}, "stableDiffusion": {"name": "Stable Diffusion", "description": "专业控制SDXL模型，优化负面提示与LORA触发词。"}, "dreamAI": {"name": "即梦AI", "description": "专为中文图像生成优化，智能转换古诗文与成语意境。"}, "lovable": {"name": "Lovable提示词", "description": "生成高度可维护代码，聚焦可读性与防御性编程。"}, "cursor": {"name": "Cursor提示词", "description": "适配AI结对编程，生成上下文感知的智能补全提示。"}, "windsurf": {"name": "Windsurf提示词", "description": "生成云计算部署脚本，集成AWS/Azure/GCP最佳实践。"}, "sunoAI": {"name": "SUNO AI", "description": "生成带和弦进行的流行歌词，自动匹配Verse/Chorus结构。"}, "murekaAI": {"name": "Mureka AI", "description": "生成EDM电子舞曲元素，集成Build-up/Drop提示词。"}, "official": {"name": "正式公文", "description": "符合机关单位标准的公文写作规范，严肃准确。"}, "academic": {"name": "学术论文", "description": "严谨的学术写作风格，符合期刊投稿标准。"}, "news": {"name": "新闻报道", "description": "客观中立的新闻写作，倒金字塔结构。"}, "creative": {"name": "创意文案", "description": "充满创意和感染力的营销文案，吸引眼球。"}, "casual": {"name": "口语化表达", "description": "将正式语言转换为自然口语，亲切易懂。"}, "direct_transcription": {"name": "直接转录", "description": "仅转录语音内容，不进行AI优化"}}, "onboarding": {"languageSelection": {"title": "选择语言", "subtitle": "请选择您的首选语言", "titleBilingual": "选择语言 / Choose Language", "subtitleBilingual": "请选择您的首选语言 / Please select your preferred language"}, "welcome": {"title": "欢迎使用知我AI输入法", "subtitle": "用AI赋能你的文字表达，让沟通更高效、更精准", "changeLanguage": "切换语言/Change Language", "feature1": "语音转文字，解放双手", "feature2": "AI智能润色，提升表达", "feature3": "多种风格，适应各场景"}, "source": {"title": "您从哪里了解到知我AI输入法？", "options": {"app_store_search": "应用市场搜索", "app_store_ad": "应用市场广告", "douyin": "抖音", "youtube_ad": "YouTube广告", "xiaohongshu": "小红书", "instagram": "Instagram", "friend_recommend": "朋友推荐", "other": "其他渠道"}}, "useCases": {"title": "您希望在哪些场景中使用？", "subtitle": "可以选择多个选项", "options": {"daily_chat": "日常聊天", "work_communication": "工作沟通", "social_media": "发朋友圈", "xiaohongshu_post": "发小红书", "inspiration": "记录灵感", "ai_prompt": "大模型提示词", "other": "其他", "translation": "多语言翻译"}}, "templates": {"title": "选择您的默认模板", "subtitle": "选择一个模板作为您的默认风格，后续可以在设置中随时更改", "vipRequired": "该风格模板为VIP专享，请先登录并升级到VIP会员", "loading": "正在加载模板...", "loadError": "加载模板失败，请检查网络连接", "noTemplates": "暂无可用模板"}, "fastMode": {"title": "开启极速模式", "subtitle": "设置您的录音处理偏好", "enableLabel": "极速模式", "description": "根据您的需求选择不同的录音处理方式，以获得最佳的使用体验。", "benefits": {"efficiency": "提高录音转文字的处理效率", "consistency": "保持风格模板的一致性", "flexibility": "随时可在设置中调整"}, "note": "您可以随时在设置页面中更改此选项", "normalMode": {"title": "普通模式（当前）", "description": "每次转录成功后，需要选择使用的风格模板", "advantages": "更灵活，每次录音完可以根据需要选择需要的风格", "suitableFor": "更适合使用场景多变的用户，方便在不同的场景之间切换"}, "fastModeEnabled": {"title": "极速模式（当前）", "description": "每次转录成功后，系统直接使用默认风格模板进行处理", "advantages": "更快速，减少操作次数", "suitableFor": "更适合使用场景较为固定的用户"}}, "templateCategories": {"scenario": "场景化", "translation": "翻译", "ai_prompt": "AI提示词", "writing": "写作"}, "navigation": {"next": "下一步", "back": "上一步", "complete": "完成设置", "saving": "保存中...", "skip": "跳过问卷"}, "progress": "第 {{current}} 步，共 {{total}} 步"}, "vip": {"upgradeRequired": "需要VIP会员", "templateRequiresVip": "「{{templateName}}」需要VIP会员权限", "upgradeNow": "立即升级", "benefits": {"unlimitedTemplates": "无限制使用模板", "unlimitedTemplatesDesc": "解锁所有系统预置模板，包含专业写作、情感表达等多种风格", "premiumTemplates": "专享高级模板", "premiumTemplatesDesc": "获得独家VIP模板，让您的表达更加专业和精彩", "unlimitedUsage": "无限次数使用", "unlimitedUsageDesc": "无限制使用所有功能，没有次数限制和时间限制", "advancedAI": "高级AI模型", "advancedAIDesc": "优先使用最新最强的AI模型，获得更优质的文本优化效果"}}, "loginGuide": {"title": "使用高级语音模型", "desc": "登录后可使用高级AI语音模型，转录更准确！", "login": "立即登录", "skip": "暂不登录"}}