{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "retry": "Retry", "ok": "OK", "skip": "<PERSON><PERSON>", "done": "Done", "search": "Search", "noResults": "No results found", "close": "Close", "submit": "Submit", "continue": "Continue", "select": "Select", "languageChangeSuccess": "Language changed successfully. Please restart the app for the changes to take full effect.", "languageChangeFailed": "Failed to change language. Please try again later.", "upgradeNow": "Upgrade Now", "copySuccess": "Copied to clipboard", "basicModel": "(Basic Model)", "premiumModel": "(Premium Model)", "default": "<PERSON><PERSON><PERSON>", "sensitiveWord": {"filterTitle": "Optimization Failed", "filterMessage": "Optimization failed, please re-record", "filterMessageShort": "Optimization failed, please retry", "optimizationFailed": "Optimization failed, please retry"}, "permissions": {"microphone": {"title": "Microphone Permission Required", "description": "The app needs access to your microphone for voice input. Please enable microphone permission in settings.", "usage": "Microphone permission is used for voice recording"}, "speech": {"title": "Speech Recognition Permission Required", "description": "The app needs speech recognition permission to convert voice to text. Please enable speech recognition permission in settings.", "usage": "Speech recognition permission is used for voice-to-text conversion"}, "both": {"title": "Microphone and Speech Recognition Permissions Required", "description": "The app needs access to your microphone and speech recognition features to work properly. Please enable the required permissions in settings."}, "guide": {"openSettings": "Go to Settings", "skipForNow": "Skip for Now", "checkingPermissions": "Checking permissions..."}}}, "home": {"whisperModelEnabled": "Whisper Model is now enabled.", "title": "KnowmeType", "inputTitle": "Tap the button to start voice input", "inputSubtitle": "Supports multiple voice inputs, AI will automatically optimize the text", "startRecording": "Start Recording", "stopRecording": "Stop Recording", "processing": "Processing...", "optimize": "Optimize", "copy": "Copy", "share": "Share", "clear": "Clear", "selectTemplate": "Select Template", "noTemplate": "No Template", "optimizedText": "Optimized Text", "originalText": "Original Text", "editHint": "You can edit the text here", "recording": "Recording...", "transcribing": "Transcribing...", "selectStyle": "Select Style", "optimizationResult": "Optimization Result", "aiOptimizing": "AI is optimizing the text...", "vipExclusive": "VIP Exclusive Benefits Active", "vipHint": "VIP members can use faster premium models", "regenerate": "Regenerate", "selectTemplateHint": "Select Style Template", "moreStyles": "More Styles", "noVoiceDetected": "No voice detected", "recordingError": "Recording Failed", "recordingErrorMsg": "Unable to start voice recognition. Please check app permissions.", "recordingTimeout": "Recording Timeout", "recordingTimeoutMsg": "No voice input detected for a long time. Please choose to continue or stop the current recording.", "stopRecordingBtn": "Stop Recording", "continueRecordingBtn": "Continue Recording", "speakNow": "Speak Now", "tapToPause": "Tap the microphone to pause/resume recording", "clickToStartRecording": "Click the button below to start voice input", "recordingHint": "Supports multiple voice inputs, AI will optimize the text automatically", "inputOrEditText": "Input or edit text...", "newVoiceInput": "New Voice Input", "finish": "Finish", "pause": "Pause", "continue": "Continue", "newInput": "New Voice Input", "upgradeNow": "Upgrade Now", "upgradeToVIP": "Upgrade to VIP", "vipUpgradeDescription": "Upgrade to VIP for unlimited access to advanced models, custom templates, and priority processing", "optimizingText": "Optimizing...", "retryFailed": "Retry Failed", "audioFileCorrupted": "Audio file may be corrupted, please record again", "audioFileNotFound": "Audio file not found, please record again", "retryTranscriptionFailed": "Retry transcription call failed", "retryTranscriptionFailedMsg": "Retry transcription call failed, please try again later", "retryFailedWithError": "Retry failed", "featureTags": {"smartReworking": "Smart Polishing", "sceneAdaptation": "Scene Adaptation", "personalizedTemplates": "Personalized Templates"}, "textDisplay": {"emptyText": "Please tap the Regenerate button to try again", "speakNow": "Speak now...", "listening": "Listening...", "pausedRecording": "Recording paused", "clickTemplate": "Tap to select template", "waiting": "Waiting...", "recording": "Recording...", "transcribing": "Transcribing...", "transcribingWithModel": "Transcribing with {{model}} model...", "retryTranscribing": "Retrying transcription with {{model}} model...", "speechRecognitionFailed": "Speech recognition failed", "noVoiceContentDetected": "No voice content detected", "noVoiceContentDetectedRetry": "No voice content detected, please try again", "requestTimeout": "Request timeout, please retry", "networkError": "Network connection error, please retry", "processing": "Processing...", "retryTranscribingShort": "Retrying transcription...", "retryTranscriptionShort": "Retrying transcription...", "transcribingShort": "Transcribing...", "transcriptionShort": "Transcribing...", "recordingWithTime": "Recording...{{time}}s", "listeningWithTime": "Listening...recorded {{time}}s", "retryTranscribingWithModel": "Retrying transcription with {{model}} model...", "processingProgress": "Processing... {{percentage}}% (estimated {{seconds}} seconds remaining)", "uploadingAudio": "Uploading audio file ({{size}}MB)...", "processingAudio": "Processing audio: {{message}}", "retryFailed": "Retry Failed", "audioFileCorrupted": "Audio file may be corrupted, please record again", "audioFileNotFound": "Audio file not found, please record again", "retryTranscriptionFailed": "Retry transcription call failed", "retryTranscriptionFailedMsg": "Retry transcription call failed, please try again later", "retryFailedWithError": "Retry failed", "noResponse": "No response? Click to record again", "directTranscription": "Direct Transcription", "usingDefaultTemplate": "Using default template for style optimization", "usingDefaultModel": "Using default model for style optimization"}, "networkError": {"title": "Network Connection Failed", "message": "Network issue encountered during speech transcription, please check your connection and retry", "tip1": "• Check if network connection is normal", "tip2": "• Ensure WiFi or mobile data is connected", "tip3": "• Retry later or record again", "retryButton": "Retry Transcription", "rerecordButton": "Record Again", "retrySuccess": "Retry Successful", "retryFailed": "Retry failed, please ensure network connection is stable and try again", "audioCorrupted": "Multiple retries failed, please start a new recording", "retryFailedWithNetwork": "Retry failed, please ensure network connection is stable and try again. If multiple attempts fail, please start a new recording"}, "transcribingStatus": "Transcribing...", "transcribingNoResponse": "No response? Click to record again", "whisperLimit": {"loginPrompt": "Please login and upgrade to VIP to unlock unlimited voice transcription", "switchToNative": "Switched to Native Speech recognition.", "title": "Daily Usage Limit Reached", "message": "You have used up your daily advanced transcription quota.", "upgradeOption": "Upgrade to VIP for unlimited usage", "switchOption": "Switch to device speech recognition", "remainingCount": "{{count}} remaining today", "vipUnlimited": "VIP users have unlimited access", "usageStats": "Used {{used}}/{{limit}} today", "tip1": "• Upgrade to VIP for unlimited transcription", "tip2": "• Or switch to device speech recognition", "tip3": "• You will get {{limit}} free credits tomorrow"}}, "history": {"detail": {"jumpToResult": "Jump to Result", "title": "History Details", "backToList": "Back to List", "goToResultPage": "Go to Result Page", "originalContent": "Original Content", "optimizedContent": "AI Optimized Content", "optimizationSuccess": "Optimization Successful", "optimizationSuccessDesc": "New result generated with selected template, click to view details", "optimizationFailed": "Optimization Failed", "optimizationFailedDesc": "Please try again later", "reoptimize": "Re-optimize", "share": "Share", "shareOptions": {"title": "Choose Content to Share", "originalContent": "Original Content", "optimizedContent": "AI Optimized Content", "originalAudio": "Original Audio", "cancel": "Cancel"}, "noOriginalContent": "No original audio content", "noOptimizedContent": "No AI optimized content", "optimizationHistory": "All historical optimization records are preserved for you", "copy": "Copy", "reoptimizedInfo": {"title": "Re-optimized Record", "description": "This record is generated by re-optimization and has no original audio", "viewOriginal": "View Original Record", "titleSimple": "Re-optimized Record", "viewOriginalSimple": "Original"}, "sections": {"originalAudio": "Original Audio Content", "aiOptimized": "AI Optimized Result"}, "actions": {"copyText": "Copy Text", "reoptimize": "Re-optimize", "shareResult": "Share Result"}, "templateModal": {"title": "Select Optimization Template", "sourceText": "Source Text", "optimizedText": "Optimized Text", "originalText": "Original Text", "optimizeButton": "Start Optimizing", "cancelButton": "Cancel", "optimizing": "Optimizing...", "selectTemplate": "Select Template Style:", "startOptimize": "Start Optimizing"}, "toast": {"copySuccess": "Copied to clipboard", "selectSingleResult": "Please select a single optimization result to copy", "optimizationSuccess": "Optimization Successful", "optimizationSuccessMessage": "New optimization result generated, return to the list to view", "optimizationFailed": "Optimization Failed", "optimizationFailedMessage": "Please try again later", "viewHistoryList": "View History List"}, "errors": {"templateNotFound": "Selected template not found"}, "sourceRecordDeleted": "Source Record Deleted", "sourceRecordDeletedDesc": "The original record for this reoptimized content has been deleted and cannot be viewed"}, "today": "Today", "yesterday": "Yesterday", "dayBeforeYesterday": "Day Before Yesterday", "thisWeek": "This Week", "thisMonth": "This Month", "earlier": "Earlier", "expand": "Expand", "collapse": "Collapse", "delete": "Delete", "copy": "Copy", "all": "All", "workRelated": "Work Related", "email": "Email", "meetingNotes": "Meeting Notes", "chat": "Cha<PERSON>", "important": "Important", "personal": "Personal", "draft": "Draft", "archived": "Archived", "title": "History", "noRecords": "No records yet", "deleteConfirm": "Delete record is not recoverable, please confirm", "deleteConfirmMultiple": "Are you sure you want to delete {{count}} selected records?", "deleteConfirmTitle": "Are you sure you want to delete?", "deleteSuccess": "Deleted successfully", "deleteSuccessMultiple": "Successfully deleted {{count}} records", "copySuccess": "Copied to clipboard", "shareSuccess": "Shared successfully", "searchPlaceholder": "Search history...", "selectAll": "Select All", "deselectAll": "Deselect All", "select": "Select", "cancel": "Cancel", "vipAllRecords": "Showing {{count}} record", "vipAllRecords_plural": "Showing {{count}} records", "freeUserLimitedRecords": "Showing recent {{max}} record (Upgrade to VIP to check all history)", "freeUserLimitedRecords_plural": "Showing recent {{max}} records (Upgrade to VIP to check all history)", "freeUserAllRecords": "Showing {{count}} record", "freeUserAllRecords_plural": "Showing {{count}} records", "templateAllRecords": "Showing {{count}} record of \"{{template}}\"", "templateAllRecords_plural": "Showing {{count}} records of \"{{template}}\"", "templateLimitedRecords": "Showing {{count}} record of \"{{template}}\" (in first 50)", "templateLimitedRecords_plural": "Showing {{count}} records of \"{{template}}\" (in first 50)", "deleteSelected": "Delete Selected", "noRecordsFound": "No matching records found", "loadMore": "Load more...", "loading": "Loading...", "pullToRefresh": "Pull to refresh", "releaseToRefresh": "Release to refresh", "refreshing": "Refreshing...", "emptyStateTitle": "No history yet", "emptyStateSubtitle": "You don't have any history yet, tap the button below to start recording", "startRecording": "Start Recording", "selectMode": "Select Mode", "selectedCount": "{{count}} items selected", "share": "Share", "vipUpgrade": {"title": "View More History", "subtitle": "{{count}} more records require VIP access", "upgradeButton": "Upgrade VIP"}}, "settings": {"appName": "KnowmeType", "languageChangeFailed": "Failed to change language, please try again later", "wechatLogin": "<PERSON><PERSON><PERSON>", "appleLogin": "Apple Login", "googleLogin": "Google Login", "emailLogin": "<PERSON><PERSON>", "phoneLogin": "Phone Login", "title": "Settings", "account": "Account", "selectTranscribeModel": "Select Transcribe Model", "selectLanguageModel": "Select Language Model", "uiLanguage": "Interface Language", "selectLanguage": "Select Language", "themeStyle": "Theme Style", "selectThemeStyle": "Select Theme Style", "themeSystemStyle": "System Style", "themeSystemStyleDesc": "Automatically switch based on system settings", "themeDarkStyle": "Dark Style", "themeDarkStyleDesc": "Always use dark interface", "themeLightStyle": "Light Style", "themeLightStyleDesc": "Always use light interface", "fastMode": "Fast Mode", "fastModeDescription": "Enable to skip style template selection", "fastModeDisabledDescription": "Style Mode must be enabled first", "styleOptimization": "Style Mode", "styleOptimizationDescription": "When enabled, AI will optimize text with style templates", "appLock": {"title": "App Lock", "description": "Require biometric authentication to access history", "notSupported": "Not Supported", "notSupportedDesc": "Your device does not support biometric authentication", "testAuth": "Please verify your identity to enable app lock", "disableAuth": "Please verify your identity to disable app lock", "usePassword": "Use Password", "authFailed": "Authentication Failed", "authFailedDesc": "Biometric authentication failed, please try again", "enabled": "App lock enabled", "disabled": "App lock disabled", "toggleFailed": "Failed to toggle app lock, please try again", "authPage": {"checkingAccess": "Checking Access Permissions", "pleaseWait": "Please wait...", "appLocked": "App is Locked", "verifyIdentity": "Please verify your identity to access history", "authenticating": "Authenticating...", "retryAuth": "Retry Authentication"}}, "styleTemplate": "Style Template", "templatePersonalization": "Template Personalization", "templatePersonalizationSubtitle": "Customize template display and sorting", "templatePersonalizationPortal": "Manage", "templateManagerInstructions": "• Long press templates to drag and adjust display order\n• Click ⊗ in the top right to hide templates, click ⊕ to show templates\n• Default template always stays first and cannot be moved or hidden", "templateManagerLoading": "Loading template data...", "templateManagerSaving": "Saving settings...", "templateManagerLoadError": "Failed to load template data, please try again", "templateManagerSaveError": "Failed to save settings, please try again", "templateManagerSystemTag": "System", "templateManagerCustomTag": "Custom", "templateManagerDefaultTag": "<PERSON><PERSON><PERSON>", "templateManagerDeletedTemplate": "Deleted Template", "templateManagerDeletedTemplateDesc": "This template has been deleted but is still retained in preferences", "templateManagerOrderExplanation": "Template display order: 1-3 for first row on style selection page, 4-6 for second row, 7+ for \"More Styles\" section", "templateManagerDragHint": "💡 Long press template cards to drag and reorder", "templateManagerDefaultNotMovable": "Default template cannot be moved", "templateManagerDefaultNotMovableMessage": "The default template always stays in the first position and cannot be moved or reordered", "templateManagerCannotReplaceDefault": "Cannot replace default template position", "templateManagerCannotReplaceDefaultMessage": "The first position is reserved for the default template, other templates cannot be moved to this position", "transcribeModel": "Transcribe Model", "languageModel": "Language Model", "theme": "Theme", "about": "About", "feedback": {"modalTitle": "We Need Your Feedback❤️", "modalDescription": "Help us improve by sharing your thoughts", "rateButton": "Rate Us", "rateDescription": "Rate us on the App Store", "feedbackButton": "Send Feedback", "feedbackDescription": "Send us your suggestions or report issues", "cancel": "Cancel"}, "rateUs": "Rate Us", "productFeedback": "Product Feedback", "rateInAppStore": "Rate in App Store", "rateInGooglePlay": "Rate in Google Play", "thankYouForRating": "Thank you for rating!", "ratingNotAvailable": "Rating feature is not available", "openStoreError": "Failed to open app store, please try again later", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "logout": "Logout", "logoutConfirm": "Are you sure you want to logout?", "switchingLanguage": "Switching interface language...", "deleteAllData": "Delete All Data", "dataManagement": "Data Management", "deleteAllDataConfirm": "Are you sure you want to delete all data? This action cannot be undone.", "deleteAllDataSuccess": "All data deleted successfully", "deleteAllDataSuccessDesc": "All data has been deleted. You can start fresh.", "version": "Version {{version}}", "versionUpdate": "Check for Updates", "vipExclusiveFeature": "VIP Exclusive Feature", "onlyVIPUsersCanUseThisModel": "Only VIP users can use this model", "upgradeToUseVipModel": "This model is only available for VIP users. Would you like to upgrade to VIP membership to unlock advanced models?", "upgradeNow": "Upgrade Now", "vipBadgeText": "Knowme VIP", "vipCardTitle": "Unlock All Advanced Features", "vipCardSubtitle": "<PERSON>joy Unlimited AI Optimization and Advanced Models", "unlimitedCustomTemplate": "Unlimited Custom Templates", "advancedAIModel": "Advanced AI Models", "priorityRequest": "Priority Request", "fullHistoryRecord": "Full History Record", "vipSubscribed": "VIP Subscribed", "checkVipDetail": "Check VIP Privileges", "vipSubscribedCardTitle": "VIP Subscribed", "vipSubscribedCardSubtitle": "<PERSON>joy Unlimited AI Optimization and Advanced Models", "loginGuideTitle": "Unlock full features", "loginGuideSubtitle": "Custom templates, custom AI models", "loginGuideButtonText": "<PERSON><PERSON>", "toast": {"settingsSaved": "Setting<PERSON> saved", "settingsSaveSuccess": "Settings saved and synced", "settingsSaveFailed": "Failed to save settings, please try again later", "localSettingsSaveFailed": "Failed to save local settings", "cloudSyncFailed": "Cloud sync failed, but local settings saved", "settingsSavedWithIssue": "Settings saved, but sync may have issues", "languageChangeFailed": "Failed to change language, please try again later", "voiceModelSwitched": "Switched to Native Speech recognition."}, "vipFeatures": {"title": "VIP Feature", "message": "This feature is only available for VIP members, please upgrade first", "aiModelSettingTitle": "AI Model Settings", "aiModelSettingMessage": "You can choose different AI models: DeepSeek-R1, GPT-4 or other advanced models"}, "languageNames": {"zh-Hans": "简体中文", "zh-Hant": "繁體中文", "en": "English"}, "subscriptionPlans": {"monthly": "Monthly Plan", "yearly": "Yearly Plan", "vip": "VIP Plan"}, "authProviders": {"unknown": "Unknown login method"}, "validityInfo": {"notAvailable": "Validity information not available"}, "models": {"nativeTranscribe": {"ios": "iOS Native Transcribe", "android": "Android Native Transcribe"}}, "prompts": {"upgradeVipTitle": "Upgrade to VIP", "upgradeVipMessage": "Please login first to upgrade to VIP", "modelSettingsTitle": "Model Settings", "modelSettingsMessage": "Please login first to modify model settings"}, "periodUnit": {"month": "/mo", "year": "/yr"}, "feedbackEmailSubject": "KnowmeType Feedback - {{platform}} v{{version}}", "feedbackEmailBodyLine1": "App Name: KnowmeType", "feedbackEmailBodyLine2": "App Version: {{version}}", "feedbackEmailBodyLine3": "Platform: {{platform}}", "feedbackEmailBodyLine4": "System Version: {{systemVersion}}", "feedbackEmailBodyLine5": "User ID: {{userId}}", "feedbackEmailBodyLine6": "\nPlease describe the issue or suggestion you encountered below:\n\n"}, "dataManagement": {"title": "Data Management", "appLock": "App Lock", "appLockDesc": "Require biometric authentication to access history", "deleteLocalData": "Delete Local Data", "deleteLocalDataDesc": "Clear history records and custom templates", "deleteLocalDataTitle": "Delete Local Data", "deleteLocalDataMessage": "Are you sure you want to delete all local data? This will clear history records and custom templates.", "deleteLocalDataSuccess": "Local data deleted successfully", "deleteLocalDataError": "Failed to delete local data", "deleteAccount": "Delete Account", "deleteAccountDesc": "Permanently delete account and all related data", "deleteAccountTitle": "Delete Account", "deleteAccountMessage": "Deleting your account will permanently remove your account and all data. This action cannot be undone.", "deleteAccountConfirmTitle": "Confirm Deletion", "deleteAccountConfirmMessage": "Are you really sure you want to delete your account? This action cannot be undone.", "deleteAccountSuccess": "Account deleted successfully", "deleteAccountError": "Failed to delete account", "userNotLoggedIn": "User not logged in", "warningMessage": "Deletion operations cannot be undone, please proceed with caution. Deleting local data will not affect your account, but deleting your account will permanently remove all your information."}, "tabs": {"input": "Input", "history": "History", "settings": "Settings"}, "templates": {"editPrompt": "Edit Prompt", "vipTemplateMessage": "This template is VIP exclusive, please upgrade to use", "updateSuccess": "Template updated successfully", "loginToCopyTemplate": "Login to copy templates", "createTemplate": "Create Template", "loginToCreateTemplate": "Login to create templates", "copySuffix": "Duplicate", "editButton": "Edit", "deleteButton": "Delete", "gotIt": "Got It", "setDefaultTitle": "Set as <PERSON><PERSON><PERSON>", "setDefaultMessage": "Set as default template", "templateManagement": "Template Management", "systemTemplate": "Preset Templates", "customTemplate": "Custom Templates", "noCustomTemplate": "No custom templates yet", "addNewTemplate": "Add New Template", "longPressToCopy": "Long press to create a copy", "systemLabel": "Preset", "customLabel": "Custom", "defaultLabel": "<PERSON><PERSON><PERSON>", "syncedLabel": "Synced", "defaultTemplate": "<PERSON><PERSON><PERSON>", "setAsDefault": "Set as <PERSON><PERSON><PERSON>", "editTemplate": "Edit Template", "copyButton": "Copy", "copyTemplate": "Copy Template", "copyTemplateDesc": "Create a copy of this template", "deleteTemplate": "Delete Template", "deleteConfirm": "Are you sure you want to delete this template?", "deleteSuccess": "Template deleted successfully", "setDefaultSuccess": "Default template set successfully", "copySuccess": "Copy successfully", "copySuccessMessage": "Template copied successfully, you can edit the template name and description", "setDefault": "Set as <PERSON><PERSON><PERSON>", "newTemplate": "New Template", "templateName": "Template Name", "templateDescription": "Template Description", "templatePrompt": "Prompt", "templateNamePlaceholder": "Enter template name", "descriptionPlaceholder": "Enter template description", "promptPlaceholder": "Enter template prompt", "saveTemplate": "Save Template", "saveSuccess": "Template saved successfully", "saveFailed": "Failed to save template", "invalidInput": "Invalid input, please check and try again", "systemTemplateCannotEdit": "System preset template cannot be edited", "basicInfo": "Basic Info", "usageInstructions": "Usage Instructions", "usageInstructionsTitle": "How to Write Good Prompts?", "usageInstructionsText": "1. The prompt should clearly describe the expected style and content characteristics of the text.\n2. You can specify specific tones, emotions, formats, or professional terms.\n3. Adding specific examples can help the AI better understand your needs.\n4. The more detailed the prompt, the more符合 your expectations.", "usageInstructionsExampleTitle": "Example Prompt", "usageInstructionsExampleText": "Please organize the text into a formal email format, using polite and professional language, ensuring accurate grammar, and retaining all important information. Add an appropriate greeting at the beginning and a polite closing at the end.", "vipNoteText": "Free users can only create and edit 1 custom template. Upgrade to VIP to unlock unlimited creation.", "description": "Description", "setDefaultTemplate": "Set as <PERSON><PERSON><PERSON>", "templatePromptHint": "Enter template prompt", "templateNameHint": "Enter template name", "templateDescriptionHint": "Enter template description", "templatePromptPlaceholder": "Enter template prompt", "templateDescriptionPlaceholder": "Enter template description", "templateNameRequired": "Template name is required", "deleteConfirmButton": "Delete this template", "deleteConfirmText": "Are you sure you want to delete this template?", "deleteFailed": "Delete failed", "confirmDeleteTitle": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this template?", "vipLimitTitle": "Reached template limit", "vipLimitMessage": "Upgrade to VIP to unlock unlimited creation", "vipTemplateTitle": "VIP Template Access", "vipTemplateAccessMessage": "This template is exclusive to VIP members. Please login and upgrade to VIP", "vipTemplateFallback": "Upgrade to VIP to use VIP-exclusive templates", "vipRequired": "VIP Required", "vipRequiredMessage": "This template is exclusive to VIP members. Please upgrade to VIP to use", "vipRequiredForOptimization": "VIP membership required to use VIP-exclusive templates", "deleteTemplateDesc": "Are you sure you want to delete this template?", "title": "Select Your Style Templates", "subtitle": "You can select multiple templates and adjust them later in settings", "loading": "Loading templates...", "loadError": "Failed to load templates, please check your network connection", "noTemplates": "No templates available"}, "templateCategories": {"scenario": "Scenarios", "translation": "Translation", "ai_prompt": "AI Prompts", "writing": "Writing"}, "errors": {"networkError": "Network error, please check your connection", "serverError": "Server error, please try again later", "timeout": "Request timeout, please check your connection", "unknownError": "Unknown error occurred, please try again", "invalidInput": "Invalid input, please check and try again", "permissionDenied": "Permission denied, please check app settings"}, "auth": {"login": "<PERSON><PERSON>", "signup": "Sign Up", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account?", "hasAccount": "Already have an account?", "loginSuccess": "Login successful", "signupSuccess": "Registration successful", "logoutSuccess": "Logged out successfully"}, "vipDetail": {"title": "KnowmeType VIP Privileges", "vipStatus": "VIP Member ({{planName}})", "validUntil": "Valid Until {{date}}", "featuresTitle": "VIP Privileges", "changePlan": "Change Subscription Plan", "validityNotAvailable": "Validity information not available", "plans": {"monthly": "Monthly Plan", "yearly": "Yearly Plan", "default": "VIP Plan"}, "features": {"unlimitedTemplates": {"title": "Unlimited Custom Templates", "description": "Create and save unlimited custom AI style templates, quickly apply to any text"}, "advancedModel": {"title": "Advanced AI Model", "description": "Use the latest AI model for text optimization, generating more accurate and natural content"}, "priorityProcessing": {"title": "Priority Processing", "description": "All your requests will be processed with priority, significantly reducing waiting time"}, "cloudSync": {"title": "Template Cloud Sync", "description": "Sync custom templates to cloud, use your favorite style templates on any device"}}}, "payment": {"currentPackage": "Current Plan", "title": "Upgrade to VIP", "priceHint": "Cancel anytime", "getVip": "Get KnowmeType VIP", "unlockPremium": "Unlock Premium AI Expression — Upgrade to VIP for Enhanced Features!", "award2025": "Best of 2025", "awardApp": "Voice Input App", "vipBenefits": "VIP Benefits", "userReviews": "User Reviews", "subscriptionOptions": "Subscription Options", "monthly": "Monthly", "yearly": "Yearly", "billedAs": "Billed as {{price}}/{{period}}", "trialBilling": ", {{days}}-day free trial then {{price}}/{{period}}", "subscribeNow": "Subscribe to {{type}} VIP Now", "startTrial": "Start {{days}}-Day Free Trial", "restorePurchases": "Rest<PERSON>", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "subscriptionTerms": "Subscription automatically renews, cancel anytime in your {{store}} account settings.", "subscriptionTermsIos": "App Store", "subscriptionTermsAndroid": "Google Play", "paymentInfoIos": "Payment will be charged to your iTunes account at confirmation of purchase.", "paymentInfoAndroid": "Payment will be charged to your Google Play account at confirmation of purchase.", "benefits": {"unlimitedTemplates": {"title": "Unlimited Custom Templates", "description": "Create and save unlimited custom AI style templates, apply them to any text with one tap"}, "advancedModel": {"title": "Advanced AI Models", "description": "Use cutting-edge AI models for text optimization, generating more accurate and natural content"}, "priorityRequest": {"title": "Priority Request Processing", "description": "All your requests get priority processing, significantly reducing waiting times"}, "cloudSync": {"title": "Template Cloud Sync", "description": "Sync custom templates to cloud, use your favorite style templates on any device"}}, "reviews": {"review1": {"title": "No More Struggling with Expressions!", "content": "The AI automatically optimizes my text, making my expressions more precise and fluent. It saves me so much time and has more than doubled my efficiency!", "author": "Mr. <PERSON>"}, "review2": {"title": "Amazing for Managing Image Prompts", "content": "Using knowmeType to organize image generation prompts is incredibly convenient. It generates images with just one sentence, super efficient! No more manual prompt writing.", "author": "<PERSON>, AI Artist"}, "review3": {"title": "Excellent Recording Feature", "content": "It's not just for voice input - it works great as a voice recorder too! I hope to see more editing features for recordings. Meeting notes have never been easier!", "author": "<PERSON>, Meeting Assistant"}, "review4": {"title": "Potential Keyboard Replacement", "content": "I wish this could replace my traditional keyboard app. Looking forward to seeing it become my default input method on my phone!", "author": "<PERSON><PERSON>, Tech Enthusiast"}, "review5": {"title": "Saves Time and Boosts Efficiency", "content": "Tried it out of curiosity and it's amazing! The biggest difference from traditional voice input is that it eliminates the need for secondary editing, saving me tons of time.", "author": "<PERSON><PERSON>, Productivity Enthusiast"}, "review6": {"title": "Incredible Emoji Search", "content": "No more struggling to find the right emoji! Just say how you feel and it adds the perfect emoji. All my friends are jealous of my improved chat skills!", "author": "<PERSON><PERSON>, <PERSON> Butterfly"}, "review7": {"title": "A Writer's Dream", "content": "As a writer, this app helps me quickly capture inspiration. It's an invaluable creative assistant. I'll never lose an idea due to slow typing again!", "author": "<PERSON><PERSON>, Online Writer"}, "review8": {"title": "A Boon for Language Learners", "content": "Voice input plus AI optimization has doubled my language learning efficiency. I can practice speaking and get instant text feedback!", "author": "<PERSON><PERSON>, Language Learner"}, "review9": {"title": "A Must-Have for College Students", "content": "No more awkward conversations with my girlfriend! The AI makes my expressions more logical and natural. The emoji suggestions are spot on - she says I've become more romantic and it's saved us from countless arguments!", "author": "<PERSON><PERSON>, College Student"}}, "errors": {"purchaseFailed": "Purchase Failed", "purchaseError": "There was a problem processing your subscription. Please try again later.", "restoreFailed": "Restore Failed", "restoreError": "Unable to restore your purchase. Please try again later.", "noSubscriptionFound": "No Subscription Found", "noActiveSubscription": "No active subscription found to restore", "loginRequired": "<PERSON><PERSON> Required", "loginRequiredMessage": "Please log in to subscribe", "subscriptionError": "Subscription Error", "noSubscriptionAvailable": "No subscription packages available. Please try again later."}, "success": {"purchaseSuccess": "Subscription Successful", "purchaseSuccessMessage": "You've successfully subscribed to VIP membership. Thank you for your support!", "restoreSuccess": "Restore Successful", "restoreSuccessMessage": "Your VIP membership has been restored"}, "loading": {"loading": "Loading...", "processing": "Processing..."}, "periodUnit": {"month": "/mo", "year": "/yr"}}, "login": {"title": "Welcome to KnowmeType AI Input Method", "subtitle": "Login to unlock full features, including advanced AI voice transcription.", "wechatLogin": "Continue with WeChat", "appleLogin": "Continue with Apple", "googleLogin": "Continue with Google", "emailLogin": "Login via Email account", "phoneLogin": "Login via Phone number", "devLoginTitle": "Development Environment Test Login", "emailPlaceholder": "Email", "passwordPlaceholder": "Password", "devLoginButtonText": "<PERSON><PERSON>", "skipLogin": "<PERSON><PERSON>", "footerText": "Logging in indicates your agreement to our terms and privacy policy", "serviceTerms": "Service Terms", "privacyPolicy": "Privacy Policy", "and": "and", "login": "<PERSON><PERSON>", "loadingText": "Logging in..."}, "aiModels": {"qwen2.5-7b": {"name": "QWen 2.5-7B", "description": "Alibaba's <PERSON><PERSON> large language model, basic free model"}, "qwen3-8b": {"name": "QWen 3-8B", "description": "Third-generation Tongyi Qianwen medium model, better performance"}, "qwen3-14b": {"name": "QWen 3-14B", "description": "Third-generation Tongyi Qianwen high-performance model, suitable for complex text"}, "deepseek-v3": {"name": "DeepSeek V3", "description": "Top-tier large language model with strong understanding and creation capabilities"}, "native": {"name": "Native Transcription", "description": "Local model, real-time transcription, fast speed, low accuracy, suitable for quiet environments"}, "whisper-1": {"name": "Whisper", "description": "OpenAI Whisper speech recognition, high accuracy, intelligent noise reduction, covering most daily scenarios"}, "gpt-4o-mini-transcribe": {"name": "GPT-4o Mini", "description": "High-precision intelligent transcription, supports complex scenarios and multiple languages"}}, "template": {"email": {"name": "Send Email", "description": "Generate professional business emails with automatic adaptation to recipient and context."}, "chat": {"name": "Daily Messaging", "description": "Make casual messages more natural and dynamic, matching friend intimacy and conversation context."}, "notes": {"name": "Take Notes", "description": "Transform scattered information into structured plain text notes for learning/meetings/inspiration recording."}, "markdown_notes": {"name": "Markdown Notes", "description": "Generate structured notes in markdown format with title hierarchy and formatting."}, "text_optimize": {"name": "Text Optimize", "description": "Fix errors, remove duplicates and filler words, optimize paragraph structure while preserving original text."}, "emoji": {"name": "Emoji Expression", "description": "Intelligently add emoji symbols to enhance emotional expression while avoiding awkward overuse."}, "xhs": {"name": "rednote Content", "description": "Create viral marketing content with auto-generated segmented headlines, tags, and emoji combos."}, "moment": {"name": "Social Media Post", "description": "Create high-engagement social media posts that balance authenticity with polish."}, "enTrans": {"name": "English Translation", "description": "Native-level English translation optimization, intelligently handling slang and cultural metaphors."}, "jaTrans": {"name": "Japanese Translation", "description": "Adapt to Japanese honorific system, distinguishing business/anime/daily scenarios."}, "koTrans": {"name": "Korean Translation", "description": "Precisely convert Korean honorifics and casual speech, automatically distinguishing business and K-pop contexts."}, "idTrans": {"name": "Indonesian Translation", "description": "Adapt to Southeast Asian multi-dialect scenarios, intelligently handling religious and cultural sensitive words."}, "deTrans": {"name": "German Translation", "description": "Rigorously handle German cases and gender, automatically adapting to legal/engineering/daily scenarios."}, "frTrans": {"name": "French Translation", "description": "Elegantly handle French gender and conjugation, enhancing literary and business formality."}, "esTrans": {"name": "Spanish Translation", "description": "Flexibly convert Latin American/Spanish variants, intelligently handling inverted sentence structures."}, "zhCnTrans": {"name": "Chinese Translation (Simplified)", "description": "Accurately translate various languages into standard Simplified Chinese while preserving original meaning and conforming to Chinese expression habits."}, "zhTwTrans": {"name": "Chinese Translation (Traditional)", "description": "Accurately translate various languages into standard Traditional Chinese while preserving original meaning and conforming to Traditional Chinese expression habits."}, "recraft": {"name": "Recraft Image Generation", "description": "Generate vector art-specific prompts with automatic flat design specification matching."}, "midjourney": {"name": "Midjourney V7", "description": "Adapt to V7 latest parameters, enhancing detail control and style blending capabilities."}, "stableDiffusion": {"name": "Stable Diffusion", "description": "Professional SDXL model control, optimized negative prompts and LORA trigger words."}, "dreamAI": {"name": "DreamAI", "description": "Optimized for Chinese image generation, intelligently converting classical poetry and idiom imagery."}, "lovable": {"name": "Lovable Prompts", "description": "Generate highly maintainable code, focusing on readability and defensive programming."}, "cursor": {"name": "Cursor Prompts", "description": "Adapt to AI pair programming, generating context-aware intelligent completion prompts."}, "windsurf": {"name": "Windsurf Prompts", "description": "Generate cloud deployment scripts, integrating AWS/Azure/GCP best practices."}, "sunoAI": {"name": "SUNO AI", "description": "Generate pop lyrics with chord progressions, automatically matching Verse/Chorus structure."}, "murekaAI": {"name": "Mureka AI", "description": "Generate EDM electronic dance music elements, integrating Build-up/Drop prompts."}, "official": {"name": "Official Document", "description": "Standard official document writing format, serious and accurate."}, "academic": {"name": "Academic Paper", "description": "Rigorous academic writing style, suitable for journal submission."}, "news": {"name": "News Report", "description": "Objective and neutral news writing with inverted pyramid structure."}, "creative": {"name": "Creative Copy", "description": "Creative and compelling marketing copy that catches attention."}, "casual": {"name": "Casual Expression", "description": "Convert formal language to natural spoken language, friendly and easy to understand."}, "direct_transcription": {"name": "Direct Transcription", "description": "Only transcribe voice content without AI optimization"}}, "onboarding": {"languageSelection": {"title": "Choose Language", "subtitle": "Please select your preferred language", "titleBilingual": "选择语言 / Choose Language", "subtitleBilingual": "请选择您的首选语言 / Please select your preferred language"}, "welcome": {"title": "Welcome to KnowmeType AI Input Method", "subtitle": "AI-powered text enhancement for efficient and precise communication", "changeLanguage": "Change Language/切换语言", "feature1": "Voice to text, hands-free input", "feature2": "AI polishing, enhanced expression", "feature3": "Multiple styles for various scenarios"}, "source": {"title": "Where did you hear about KnowmeType?", "options": {"app_store_search": "App Store Search", "app_store_ad": "App Store Ad", "tiktok": "TikTok", "youtube_ad": "YouTube Ad", "xiaohongshu": "rednote", "instagram": "Instagram", "friend_recommend": "Friend Recommend", "other": "Other Channels", "douyin": "TikTok"}}, "useCases": {"title": "Where do you want to use it?", "subtitle": "You can select multiple options", "options": {"daily_chat": "Daily Chat", "work_communication": "Work Communication", "social_media": "Post on X(Twitter)", "xiaohongshu_post": "Post on rednote", "inspiration": "Record Inspiration", "ai_prompt": "AI Prompt", "other": "Other", "translation": "Multilingual Translation"}}, "templates": {"title": "Choose Your Default Template", "subtitle": "Select one template as your default style. You can change it anytime in settings", "vipRequired": "This template is VIP exclusive, please login and upgrade to VIP member first", "loading": "Loading templates...", "loadError": "Failed to load templates, please check your network connection", "noTemplates": "No templates available"}, "fastMode": {"title": "Enable Fast Mode", "subtitle": "Configure your recording processing preferences", "enableLabel": "Fast Mode", "description": "Choose different recording processing methods based on your needs for the best user experience.", "benefits": {"efficiency": "Improve recording-to-text processing efficiency", "consistency": "Maintain consistency of style templates", "flexibility": "Can be adjusted anytime in settings"}, "note": "You can change this option anytime in the settings page", "normalMode": {"title": "Normal Mode (Current)", "description": "After each successful transcription, you need to select the style template to use", "advantages": "More flexible, you can choose the desired style after each recording", "suitableFor": "More suitable for users with varied use cases, convenient for switching between different scenarios"}, "fastModeEnabled": {"title": "Fast Mode (Current)", "description": "After each successful transcription, the system directly uses the default style template for processing", "advantages": "Faster, reduces the number of operations", "suitableFor": "More suitable for users with relatively fixed use cases"}}, "templateCategories": {"scenario": "Scenarios", "translation": "Translation", "ai_prompt": "AI Prompts", "writing": "Writing"}, "navigation": {"next": "Next", "back": "Back", "complete": "Complete", "saving": "Saving...", "skip": "Skip Survey"}, "progress": "Step {{current}} of {{total}}"}, "vip": {"upgradeRequired": "VIP Required", "templateRequiresVip": "\"{{templateName}}\" requires VIP membership", "upgradeNow": "Upgrade Now", "benefits": {"unlimitedTemplates": "Unlimited Template Usage", "unlimitedTemplatesDesc": "Unlock all system templates, including professional writing, emotional expression, and more", "premiumTemplates": "Premium Templates", "premiumTemplatesDesc": "Get exclusive VIP templates for more professional and engaging expression", "unlimitedUsage": "Unlimited Usage", "unlimitedUsageDesc": "Use all features without limits on times or duration", "advancedAI": "Advanced AI Model", "advancedAIDesc": "Priority access to the latest and most powerful AI models for better text optimization"}}, "loginGuide": {"title": "Use advanced voice model", "desc": "Sign in to unlock advanced AI voice transcription for more accurate results and a better experience!", "login": "Sign In", "skip": "Skip for now"}}