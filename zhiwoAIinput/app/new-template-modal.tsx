import React, { useState, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, TextInput, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Text, View, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { router, Stack } from 'expo-router';
import { useAppTheme } from '@/hooks/useAppTheme';
import { saveUserTemplate } from '@/services/templateService';
import { generateUUID } from '@/utils/helpers';
import { withAuthGuard } from '@/components/AuthGuard';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useTranslation } from 'react-i18next';
import { log, error as logError } from '@/services/logService';
import { FullScreenEditor } from '@/components/ui/FullScreenEditor';
import Toast from 'react-native-toast-message';
import { getToastOptions } from '@/utils/toastConfig';
import { useTemplates } from '@/hooks/useTemplates';

/**
 * 新建风格模板Modal页面
 * 以modal形式展现的新建模板页面，支持从任何页面调用
 */
function NewTemplateModalScreen() {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [prompt, setPrompt] = useState('');
  const [showFullScreenEditor, setShowFullScreenEditor] = useState(false);
  const theme = useAppTheme();
  const isDark = theme === 'dark';

  // 获取VIP状态
  const { isVIP } = useSelector((state: RootState) => state.auth);

  // 使用翻译钩子
  const { t } = useTranslation();

  const { refreshTemplates } = useTemplates();

  // 导航回上一页面
  const navigateBack = () => {
    router.back();
  };

  // 保存模板
  const handleSaveTemplate = async () => {
    // 表单验证
    if (!name.trim()) {
      Toast.show(getToastOptions(t('templates.templateNameRequired', '请输入模板名称'), 'error'));
      return;
    }

    if (!prompt.trim()) {
      Toast.show(getToastOptions(t('templates.promptRequired', '请输入模板提示词'), 'error'));
      return;
    }

    try {
      // 创建模板对象
      const newTemplate = {
        id: generateUUID(),
        title: name.trim(),
        description: description.trim(),
        prompt: prompt.trim(),
        isSystem: false,
        color: "#3B82F6",
        borderColor: "#3B82F6",
        backgroundColor: "#EFF6FF",
        isSynced: false, // 新建模板初始状态为未同步
        icon: "👤" // 用户自定义模板的默认图标
      };
      
      // 保存模板到本地存储
      const success = await saveUserTemplate(newTemplate);
      
      if (success) {
        await refreshTemplates();
        Toast.show(getToastOptions(t('templates.createSuccess', '模板已成功创建'), 'success'));
        router.back();
      } else {
        Toast.show(getToastOptions(t('templates.reachLimit', '可能是由于非VIP用户自定义模板数量达到上限'), 'error'));
      }
    } catch (error) {
      logError("保存模板出错:", error);
      Toast.show(getToastOptions(t('templates.saveFailed') + '，' + t('common.retryLater', '请稍后再试'), 'error'));
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: t('templates.newTemplate'),
          headerShown: false,
          presentation: 'modal',
          animation: 'slide_from_bottom',
          animationDuration: 300,
          gestureEnabled: true,
          gestureDirection: 'vertical',
        }}
      />
      
      <SafeAreaView style={[
        styles.container,
        isDark && { backgroundColor: '#151718' }
      ]} edges={['top']}>
        <StatusBar style={isDark ? "light" : "dark"} />

        <KeyboardAvoidingView 
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
        >
          {/* 头部导航 */}
          <View style={[
            styles.header,
            isDark && styles.headerDark
          ]}>
            <View style={styles.headerLeft}>
              <TouchableOpacity style={styles.backButton} onPress={navigateBack}>
                <IconSymbol name="xmark" size={20} color={isDark ? "#9BA1A6" : "#6B7280"} />
              </TouchableOpacity>
              <Text style={[
                styles.headerTitle,
                isDark && styles.headerTitleDark
              ]}>{t('templates.newTemplate')}</Text>
            </View>
            <TouchableOpacity 
              style={[
                styles.saveButton, 
                (!name.trim() || !prompt.trim()) && styles.saveButtonDisabled,
                isDark && styles.saveButtonDark
              ]}
              onPress={handleSaveTemplate}
              disabled={!name.trim() || !prompt.trim()}
            >
              <Text style={[
                styles.saveButtonText, 
                (!name.trim() || !prompt.trim()) && styles.saveButtonTextDisabled
              ]}>{t('common.save')}</Text>
            </TouchableOpacity>
          </View>

          <ScrollView 
            style={[
              styles.scrollView,
              isDark && styles.scrollViewDark
            ]}
            contentContainerStyle={styles.scrollViewContent}
            keyboardShouldPersistTaps="handled"
          >
            {/* 表单区域 */}
            <View style={[
              styles.formSection,
              isDark && { 
                backgroundColor: '#1E2122',
                borderColor: '#2B2F31'
              }
            ]}>
              <Text style={[
                styles.formSectionTitle,
                isDark && { color: '#ECEDEE' }
              ]}>{t('templates.basicInfo')}</Text>

              <View style={styles.formGroup}>
                <Text style={[
                  styles.formLabel,
                  isDark && { color: '#9BA1A6' }
                ]}>{t('templates.templateName')}</Text>
                <TextInput
                  style={[
                    styles.formInput,
                    isDark && { 
                      backgroundColor: '#151718',
                      borderColor: '#2B2F31',
                      color: '#ECEDEE'
                    }
                  ]}
                  value={name}
                  onChangeText={setName}
                  placeholder={t('templates.templateNamePlaceholder')}
                  placeholderTextColor={isDark ? '#6B7280' : '#9CA3AF'}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={[
                  styles.formLabel,
                  isDark && { color: '#9BA1A6' }
                ]}>{t('templates.templateDescription')}</Text>
                <TextInput
                  style={[
                    styles.formInput,
                    isDark && { 
                      backgroundColor: '#151718',
                      borderColor: '#2B2F31',
                      color: '#ECEDEE'
                    }
                  ]}
                  value={description}
                  onChangeText={setDescription}
                  placeholder={t('templates.descriptionPlaceholder')}
                  placeholderTextColor={isDark ? '#6B7280' : '#9CA3AF'}
                />
              </View>

              <View style={styles.formGroup}>
                <View style={styles.promptHeader}>
                  <Text style={[
                    styles.formLabel,
                    isDark && { color: '#9BA1A6' }
                  ]}>{t('templates.templatePrompt')}</Text>
                  <TouchableOpacity
                    style={[
                      styles.expandButton,
                      isDark && { backgroundColor: '#2B2F31' }
                    ]}
                    onPress={() => setShowFullScreenEditor(true)}
                  >
                    <IconSymbol
                      name="arrow.up.left.and.arrow.down.right"
                      size={16}
                      color={isDark ? '#9BA1A6' : '#6B7280'}
                    />
                  </TouchableOpacity>
                </View>
                <TextInput
                  style={[
                    styles.formTextarea,
                    isDark && {
                      backgroundColor: '#151718',
                      borderColor: '#2B2F31',
                      color: '#ECEDEE'
                    }
                  ]}
                  value={prompt}
                  onChangeText={setPrompt}
                  placeholder={t('templates.promptPlaceholder')}
                  placeholderTextColor={isDark ? '#6B7280' : '#9CA3AF'}
                  multiline={true}
                  numberOfLines={6}
                  textAlignVertical="top"
                  scrollEnabled={true}
                />
              </View>
            </View>

            {/* 使用说明 */}
            <View style={[
              styles.formSection,
              isDark && { 
                backgroundColor: '#1E2122',
                borderColor: '#2B2F31'
              }
            ]}>
              <Text style={[
                styles.formSectionTitle,
                isDark && { color: '#ECEDEE' }
              ]}>{t('templates.usageInstructions')}</Text>
              
              <View style={[
                styles.infoBox,
                isDark && { backgroundColor: '#1A1D1E' }
              ]}>
                <Text style={[
                  styles.infoTitle,
                  isDark && { color: '#ECEDEE' }
                ]}>{t('templates.usageInstructionsTitle')}</Text>
                <Text style={[
                  styles.infoText,
                  isDark && { color: '#9BA1A6' }
                ]}>
                  {t('templates.usageInstructionsText')}
                </Text>
              </View>
              
              <View style={[
                styles.exampleBox,
                isDark && { backgroundColor: '#1A1D1E' }
              ]}>
                <Text style={[
                  styles.exampleTitle,
                  isDark && { color: '#ECEDEE' }
                ]}>{t('templates.usageInstructionsExampleTitle')}</Text>
                <Text style={[
                  styles.exampleText,
                  isDark && { color: '#9BA1A6' }
                ]}>
                  {t('templates.usageInstructionsExampleText')}
                </Text>
              </View>
            </View>

            {/* VIP提示区域 */}
            {!isVIP && (
              <View style={[
                styles.vipNote,
                isDark && { backgroundColor: '#1E2122' }
              ]}>
                <IconSymbol name="crown.fill" size={20} color="#FFD700" style={styles.vipIcon} />
                <Text style={[
                  styles.vipNoteText,
                  isDark && { color: '#9BA1A6' }
                ]}>
                  {t('templates.vipNoteText')}
                </Text>
              </View>
            )}
            
            {/* 底部空间 */}
            <View style={{ height: 40 }} />
          </ScrollView>
        </KeyboardAvoidingView>

        {/* 全屏编辑器 */}
        <FullScreenEditor
          visible={showFullScreenEditor}
          value={prompt}
          onChangeText={setPrompt}
          onClose={() => setShowFullScreenEditor(false)}
          title={t('templates.editPrompt')}
          placeholder={t('templates.promptPlaceholder')}
          maxLength={1000}
        />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerDark: {
    backgroundColor: '#151718',
    borderBottomColor: '#2B2F31',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 8,
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  headerTitleDark: {
    color: '#ECEDEE',
  },
  saveButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 8,
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  saveButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  saveButtonDark: {
    backgroundColor: '#3B82F6',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '500',
    fontSize: 16,
  },
  saveButtonTextDisabled: {
    color: '#FFFFFF80',
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  scrollViewDark: {
    backgroundColor: '#151718',
  },
  scrollViewContent: {
    padding: 20,
  },
  formSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  formSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: 'white',
  },
  formTextarea: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: 'white',
    minHeight: 120,
    maxHeight: 200, // 设置最大高度
    textAlignVertical: 'top',
  },
  promptHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
  },
  expandButtonText: {
    fontSize: 14,
    marginLeft: 4,
    color: '#6B7280',
  },
  infoBox: {
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  infoTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 8,
    color: '#111827',
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4b5563',
  },
  exampleBox: {
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    padding: 12,
  },
  exampleTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 8,
    color: '#111',
  },
  exampleText: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#4b5563',
    lineHeight: 20,
  },
  vipNote: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginTop: 4,
  },
  vipIcon: {
    marginRight: 8,
  },
  vipNoteText: {
    fontSize: 14,
    color: '#6B7280',
    flex: 1,
  },
});

// 将新建模板页面使用AuthGuard高阶组件包装，要求用户登录
export default withAuthGuard(NewTemplateModalScreen, {
  requireAuth: true, // 需要用户登录
  loginTitle: '创建自定义模板',
  loginMessage: '请先登录以创建自定义模板',
}); 