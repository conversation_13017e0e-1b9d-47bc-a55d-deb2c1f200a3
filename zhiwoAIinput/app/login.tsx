import React, { useState, useCallback, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, ActivityIndicator, Platform, Linking, Alert, Image, Animated, Dimensions, TextInput, ScrollView } from 'react-native';
import { View, Text } from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { authService, userService, IS_DEV_ENV } from '@/services/supabaseService';
import { supabase } from '@/services/supabaseService';
import { purchaseService } from '@/services/purchaseService';
import { AntDesign } from '@expo/vector-icons';
import { useAppTheme } from '@/hooks/useAppTheme';
import { Stack, router, useNavigation } from 'expo-router';
import { GestureDetector, Gesture } from 'react-native-gesture-handler';
import * as WebBrowser from 'expo-web-browser';
import Constants from 'expo-constants';
import * as AppleAuthentication from 'expo-apple-authentication';
import { checkSession, setUser, clearError } from '@/store/slices/authSlice';
import { useTranslation } from 'react-i18next';
import { log, error as logError } from '@/services/logService';

// 确保WebBrowser可以重定向回应用
WebBrowser.maybeCompleteAuthSession();

// 获取屏幕高度
const { height: SCREEN_HEIGHT } = Dimensions.get('window');

/**
 * 登录页面组件
 * 通过路由导航实现，以全屏模态形式展示
 */
export default function LoginScreen() {
  const { isLoading, isAuthenticated } = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch<AppDispatch>();
  const [loginInProgress, setLoginInProgress] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [errorVisible, setErrorVisible] = useState(false);
  const [appleAuthAvailable, setAppleAuthAvailable] = useState(false);
  const navigation = useNavigation();
  
  // 获取主题设置
  const currentTheme = useAppTheme();
  const isDark = currentTheme === 'dark';
  const { t } = useTranslation();
  
  log('LoginScreen: 渲染, 当前主题:', currentTheme, '是否深色:', isDark);
  
  // 检查Apple登录是否可用
  useEffect(() => {
    const checkAppleAuthAvailability = async () => {
      try {
        const isAvailable = await AppleAuthentication.isAvailableAsync();
        setAppleAuthAvailable(isAvailable);
        log('Apple认证可用性:', isAvailable);
      } catch (error) {
        log('检查Apple认证可用性出错:', error);
        setAppleAuthAvailable(false);
      }
    };
    
    if (Platform.OS === 'ios') {
      checkAppleAuthAvailability();
    }
  }, []);
  
  // 当显示错误消息时，自动设置计时器隐藏它
  useEffect(() => {
    if (errorMessage) {
      setErrorVisible(true);
      const timer = setTimeout(() => {
        setErrorVisible(false);
        setTimeout(() => {
          setErrorMessage(null);
        }, 300); // 等待消失动画完成后再清除消息
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage]);

  // 当组件卸载时清理所有浏览器会话
  useEffect(() => {
    return () => {
      // 组件卸载时确保关闭所有浏览器会话
      log('LoginScreen: 组件卸载，关闭所有浏览器会话');
      try {
        WebBrowser.dismissAuthSession();
      } catch (error) {
        log('关闭浏览器会话失败:', error);
      }
      WebBrowser.maybeCompleteAuthSession();
    };
  }, []);

  // 获取应用的URL scheme
  const getRedirectUrl = (): string => {
    // 使用完全硬编码的URL，避免类型问题
    const scheme = Constants.expoConfig?.scheme || 'knowmetype';
    return `${scheme}://`;
  };
  
  // 处理Google登录
  const handleGoogleSignIn = useCallback(async () => {
    try {
      // 如果已经在登录过程中，不再重复处理
      if (loginInProgress) return;
      
      setLoginInProgress(true);
      setErrorMessage(null);
      
      // 确保关闭任何已打开的浏览器会话
      WebBrowser.maybeCompleteAuthSession();
      
      // 准备重定向URL
      const redirectUrl = getRedirectUrl();
      log('Google登录开始，重定向URL:', redirectUrl);
      
      // 使用Supabase OAuth登录获取授权URL
      const { data, error } = await authService.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          skipBrowserRedirect: true // 我们将手动打开URL
        }
      });
      
      if (error) {
        logError('Google登录错误:', error);
        setErrorMessage(`登录失败：${error.message}`);
        setLoginInProgress(false);
        return;
      }
      
      // 判断返回数据中是否包含url属性
      if (data && 'url' in data && data.url) {
        log('正在打开Google授权URL:', data.url);
        
        try {
          // 使用WebBrowser在应用内打开授权页面
          const result = await WebBrowser.openAuthSessionAsync(
            data.url,
            redirectUrl,
            { showInRecents: true }
          );
          
          log('授权结果:', result);
          
          if (result.type === 'success') {
            log('授权成功，等待会话检查');
            try {
              // 处理OAuth回调URL，现在使用隐式流程
              log('解析返回URL中的认证信息...');
              const url = result.url;
              
              // 使用隐式流程，应该直接包含访问令牌
              const accessToken = extractToken(url, 'access_token');
              const refreshToken = extractToken(url, 'refresh_token');
              // 还检查授权码以防万一
              const authCode = extractToken(url, 'code');
              
              if (accessToken) {
                log('获取到access_token，手动设置会话...');
                
                // 手动设置会话
                const { data, error } = await supabase.auth.setSession({
                  access_token: accessToken,
                  refresh_token: refreshToken || '',
                });
                
                if (error) {
                  logError('设置会话错误:', error);
                  setErrorMessage(`登录失败：${error.message}`);
                  setLoginInProgress(false);
                  return;
                }
                
                if (data?.session?.user) {
                  // 获取到用户信息后，直接更新Redux状态
                  const user = data.session.user;
                  log('Google登录获取到用户信息:', user.id);
                  
                  // 获取用户资料
                  const profileResult = await userService.getUserProfile(user.id);
                  
                  // 更新用户状态
                  dispatch(setUser({
                    id: user.id,
                    email: user.email || '',
                    profile: profileResult.data,
                    // 设置为Google登录方式
                    providerType: user.app_metadata?.provider || 'google',
                    // 如果有头像，也设置头像URL
                    avatarUrl: user.user_metadata?.avatar_url || ''
                  }));
                  
                  // 打印相关信息进行调试
                  log('用户信息:', {
                    id: user.id,
                    email: user.email,
                    provider: user.app_metadata?.provider,
                    avatar: user.user_metadata?.avatar_url
                  });
                  
                  // 等待状态更新完成再返回
                  setTimeout(() => {
                    log('Google登录成功，准备处理页面关闭');
                    setLoginInProgress(false);
                    
                    // 使用全局方法设置标志，确保在登录成功后不切换标签页
                    global.setPreserveTabAfterLogin(true);
                    
                    // 如果有待处理的VIP升级，立即快速关闭登录页面
                    if (global.pendingVipUpgrade) {
                      log('Google登录: VIP升级场景，立即关闭登录页面');
                      // 在VIP升级场景下，立即关闭登录页面，不再等待
                      if (router.canGoBack()) {
                        router.back();
                      }
                      return;
                    }
                    
                    // 普通登录场景，关闭登录页面
                    if (router.canGoBack()) {
                      router.back();
                    }
                  }, global.pendingVipUpgrade ? 100 : 500);  // VIP升级时使用更短延迟
                } else {
                  throw new Error('会话中没有用户信息');
                }
                                } else if (authCode) {
                    // 如果仍然返回授权码，提示用户
                    log('仍然收到授权码，可能需要调整配置');
                    throw new Error('OAuth 配置问题：仍然使用 PKCE 流程');
                  } else {
                    throw new Error('未能从URL中提取认证信息（访问令牌）');
                  }
            } catch (sessionError) {
              logError('获取会话失败:', sessionError);
              setErrorMessage('登录状态获取失败，请重试');
              setLoginInProgress(false);
            }
          } else {
            // 用户取消了登录
            log('用户取消了登录');
            setErrorMessage('登录已取消');
          }
        } catch (browserError: any) {
          logError('打开授权浏览器错误:', browserError);
          
          // 特别处理"另一个浏览器已经打开"的错误
          if (browserError.message?.includes('Another web browser is already open')) {
            // 尝试关闭已有会话
            try {
              WebBrowser.dismissAuthSession();
            } catch (e) {
              log('关闭浏览器会话失败:', e);
            }
            setErrorMessage('授权页面已打开，请完成登录或关闭后再试');
          } else {
            setErrorMessage(`授权过程出错：${browserError.message || '未知错误'}`);
          }
        }
        
        setLoginInProgress(false);
      } else {
        setErrorMessage('未能获取授权URL，请重试');
        setLoginInProgress(false);
      }
    } catch (error: any) {
      logError('登录错误:', error);
      setErrorMessage(`登录过程发生错误：${error.message || '未知错误'}`);
      setLoginInProgress(false);
    }
  }, [loginInProgress, dispatch]);
  
  // 处理Apple登录
  const handleAppleSignIn = async () => {
    try {
      if (loginInProgress) return;
      
      setLoginInProgress(true);
      setErrorMessage(null);
      
      // 确保关闭任何已打开的浏览器会话
      WebBrowser.maybeCompleteAuthSession();
      
      // 使用原生Apple登录
      if (appleAuthAvailable) {
        try {
          // 生成随机nonce - 使用更随机的方法
          const rawNonce = Array.from(
            { length: 16 },
            () => Math.floor(Math.random() * 36).toString(36)
          ).join('');
          
          const credential = await AppleAuthentication.signInAsync({
            requestedScopes: [
              AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
              AppleAuthentication.AppleAuthenticationScope.EMAIL,
            ],
            // 将nonce作为state传递
            state: rawNonce
          });
          
          log('Apple登录成功, 凭证:', credential);
          
          // 确保identityToken不为null
          if (!credential.identityToken) {
            throw new Error('未获取到有效的Apple身份令牌');
          }
          
          // 通过Supabase的signInWithIdToken进行身份验证
          // 使用与之前相同的nonce
          const { data, error } = await authService.signInWithApple(
            credential.identityToken
          );
          
          if (error) {
            logError('Supabase Apple登录错误:', error);
            setErrorMessage(`Apple登录失败：${error.message}`);
            setLoginInProgress(false);
          } else {
            log('Supabase Apple登录成功:', data);
            
            try {
              // 先获取会话数据
              const sessionResult = await supabase.auth.getSession();
              
              if (!sessionResult.error && sessionResult.data?.session?.user) {
                // 获取到用户信息后，直接更新 Redux 状态，不使用异步 thunk
                const user = sessionResult.data.session.user;
                log('Apple登录获取到用户信息:', user.id);
                
                // 获取用户资料
                const profileResult = await userService.getUserProfile(user.id);
                
                // 直接使用 setUser 更新用户状态
                dispatch(setUser({
                  id: user.id,
                  email: user.email || '',
                  profile: profileResult.data,
                  // 设置为Apple登录方式
                  providerType: user.app_metadata?.provider || 'apple',
                  // 如果有头像，也设置头像URL
                  avatarUrl: user.user_metadata?.avatar_url || ''
                }));
                
                // 打印相关信息进行调试
                log('Apple登录用户信息:', {
                  id: user.id,
                  email: user.email,
                  provider: user.app_metadata?.provider,
                  avatar: user.user_metadata?.avatar_url,
                  metadata: user.user_metadata
                });
                
                // 等待状态更新完成再返回
                setTimeout(() => {
                  log('Apple登录成功，准备处理页面关闭');
                  setLoginInProgress(false);
                  
                  // 使用全局方法设置标志，确保在登录成功后不切换标签页
                  global.setPreserveTabAfterLogin(true);
                  
                  // 如果有待处理的VIP升级，立即快速关闭登录页面
                  if (global.pendingVipUpgrade) {
                    log('Apple登录: VIP升级场景，立即关闭登录页面');
                    // 在VIP升级场景下，立即关闭登录页面，不再等待
                    if (router.canGoBack()) {
                      router.back();
                    }
                    return;
                  }
                  
                  // 普通登录场景，关闭登录页面
                  if (router.canGoBack()) {
                    router.back();
                  }
                }, global.pendingVipUpgrade ? 100 : 500);  // VIP升级时使用更短延迟
              } else {
                throw new Error('没有获取到用户信息');
              }
            } catch (sessionError) {
              logError('获取会话失败:', sessionError);
              setErrorMessage('登录状态获取失败，请重试');
              setLoginInProgress(false);
            }
          }
        } catch (error: any) {
          // 用户取消或其他Apple登录错误
          if (error.code === 'ERR_CANCELED') {
            log('用户取消了Apple登录');
            setErrorMessage('登录已取消');
          } else {
            logError('Apple登录错误:', error);
            setErrorMessage(`Apple登录失败：${error.message || '未知错误'}`);
          }
        }
      } else {
        // 回退到旧方法，使用OAuth
        const redirectUrl = getRedirectUrl();
        log('Apple登录开始，重定向URL:', redirectUrl);
        
        const { data, error } = await authService.signInWithOAuth({
          provider: 'apple',
          options: {
            redirectTo: redirectUrl,
            skipBrowserRedirect: true
          }
        });
        
        if (error) {
          logError('Apple登录错误:', error);
          setErrorMessage(`登录失败：${error.message}`);
          setLoginInProgress(false);
          return;
        }
        
        if (data && 'url' in data && data.url) {
          log('正在打开Apple授权URL:', data.url);
          
          try {
            const result = await WebBrowser.openAuthSessionAsync(
              data.url,
              redirectUrl,
              { showInRecents: true }
            );
            
            log('授权结果:', result);
            
            if (result.type === 'success') {
              log('授权成功，等待会话检查');
              // 添加会话检查
              try {
                // 处理OAuth回调URL，现在使用隐式流程
                log('解析返回URL中的认证信息...');
                const url = result.url;
                
                // 使用隐式流程，应该直接包含访问令牌
                const accessToken = extractToken(url, 'access_token');
                const refreshToken = extractToken(url, 'refresh_token');
                // 还检查授权码以防万一
                const authCode = extractToken(url, 'code');
                
                if (accessToken) {
                  log('获取到access_token，手动设置会话...');
                  
                  // 手动设置会话
                  const { data, error } = await supabase.auth.setSession({
                    access_token: accessToken,
                    refresh_token: refreshToken || '',
                  });
                  
                  if (error) {
                    logError('设置会话错误:', error);
                    setErrorMessage(`登录失败：${error.message}`);
                    setLoginInProgress(false);
                    return;
                  }
                  
                  if (data?.session?.user) {
                    // 获取到用户信息后，直接更新Redux状态
                    const user = data.session.user;
                    log('Apple OAuth登录获取到用户信息:', user.id);
                    
                    // 获取用户资料
                    const profileResult = await userService.getUserProfile(user.id);
                    
                    // 更新用户状态
                    dispatch(setUser({
                      id: user.id,
                      email: user.email || '',
                      profile: profileResult.data,
                      // 设置为Apple登录方式
                      providerType: user.app_metadata?.provider || 'apple',
                      // 如果有头像，也设置头像URL
                      avatarUrl: user.user_metadata?.avatar_url || ''
                    }));
                    
                    // 打印相关信息进行调试
                    log('用户信息:', {
                      id: user.id,
                      email: user.email,
                      provider: user.app_metadata?.provider,
                      avatar: user.user_metadata?.avatar_url
                    });
                    
                    // 等待状态更新完成再返回
                    setTimeout(() => {
                      log('Apple OAuth登录成功，准备处理页面关闭');
                      setLoginInProgress(false);
                      
                      // 使用全局方法设置标志，确保在登录成功后不切换标签页
                      global.setPreserveTabAfterLogin(true);
                      
                      // 如果有待处理的VIP升级，立即快速关闭登录页面
                      if (global.pendingVipUpgrade) {
                        log('Apple OAuth登录: VIP升级场景，立即关闭登录页面');
                        // 在VIP升级场景下，立即关闭登录页面，不再等待
                        if (router.canGoBack()) {
                          router.back();
                        }
                        return;
                      }
                      
                      // 普通登录场景，关闭登录页面
                      if (router.canGoBack()) {
                        router.back();
                      }
                    }, global.pendingVipUpgrade ? 100 : 500);  // VIP升级时使用更短延迟
                  } else {
                    throw new Error('会话中没有用户信息');
                  }
                } else if (authCode) {
                  // 如果仍然返回授权码，提示用户
                  log('仍然收到授权码，可能需要调整配置');
                  throw new Error('OAuth 配置问题：仍然使用 PKCE 流程');
                } else {
                  throw new Error('未能从URL中提取认证信息（访问令牌）');
                }
              } catch (sessionError) {
                logError('获取会话失败:', sessionError);
                setErrorMessage('登录状态获取失败，请重试');
                setLoginInProgress(false);
              }
            } else {
              log('用户取消了登录');
              setErrorMessage('登录已取消');
            }
          } catch (browserError: any) {
            logError('打开授权浏览器错误:', browserError);
            
            // 特别处理"另一个浏览器已经打开"的错误
            if (browserError.message?.includes('Another web browser is already open')) {
              // 尝试关闭已有会话
              try {
                WebBrowser.dismissAuthSession();
              } catch (e) {
                log('关闭浏览器会话失败:', e);
              }
              setErrorMessage('授权页面已打开，请完成登录或关闭后再试');
            } else {
              setErrorMessage(`授权过程出错：${browserError.message || '未知错误'}`);
            }
          }
        } else {
          setErrorMessage('未能获取授权URL，请重试');
        }
      }
      
      setLoginInProgress(false);
    } catch (error: any) {
      logError('Apple登录过程错误:', error);
      setErrorMessage(`登录过程发生错误：${error.message || '未知错误'}`);
      setLoginInProgress(false);
    }
  };
  
  // 清除错误信息
  const clearError = () => {
    setErrorVisible(false);
    setTimeout(() => {
      setErrorMessage(null);
    }, 300);
  };
  
  // 跳过登录
  const handleSkip = useCallback(() => {
    log('LoginScreen: 用户跳过登录，直接关闭登录页面');
    
    // 直接返回到之前的页面，不进行任何额外操作
    if (router.canGoBack()) {
      router.back();
    } else {
      log('无法返回，尝试导航到主页');
      try {
        router.navigate("/(tabs)");
      } catch (error) {
        logError('导航错误:', error);
      }
    }
  }, []);
  
  // 获取颜色
  const getColor = (lightColor: string, darkColor: string) => {
    return isDark ? darkColor : lightColor;
  };
  
  // 处理隐私政策链接
  const handlePrivacyPolicy = () => {
    const privacyUrl = 'https://www.knowme-type.com/en/privacy-policy';
    Linking.openURL(privacyUrl).catch((err) => logError('打开隐私政策失败', err));
  };

  // 处理服务条款链接
  const handleTermsOfService = () => {
    const termsUrl = 'https://www.knowme-type.com/en/terms-of-service';
    Linking.openURL(termsUrl).catch((err) => logError('打开服务条款失败', err));
  };
  
  const [email, setEmail] = useState('<EMAIL>'); // 开发测试账号
  const [password, setPassword] = useState('password123'); // 开发测试密码
  const [showDevLogin, setShowDevLogin] = useState(IS_DEV_ENV); // 仅在开发环境显示

  
  // 处理邮箱密码登录
  const handleEmailSignIn = async () => {
    try {
      if (loginInProgress) return;
      
      setLoginInProgress(true);
      setErrorMessage(null);
      
      log('尝试邮箱登录:', email);
      log('使用密码:', password.replace(/./g, '*')); // 安全打印，不显示实际密码
      
      // 清除任何现有会话，确保从头开始
      try {
        log('清除现有会话...');
        await supabase.auth.signOut();
        log('会话已清除');
      } catch (clearError) {
        log('清除会话失败:', clearError);
        // 继续尝试登录
      }
      
      // 使用邮箱密码登录
      log('开始登录...');
      const { data, error } = await authService.signIn(email, password);
      
      log('登录响应:', error ? '失败' : '成功');
      
      if (error) {
        logError('邮箱登录错误:', error);
        logError('错误类型:', error.name);
        logError('错误消息:', error.message);
        logError('错误详情:', JSON.stringify(error, null, 2));
        setErrorMessage(`邮箱登录失败：${error.message}`);
        setLoginInProgress(false);
        return;
      }
      
      if (data?.session?.user) {
        // 获取到用户信息后，直接更新Redux状态
        const user = data.session.user;
        log('邮箱登录获取到用户信息:', user.id);
        
        // 获取用户资料
        const profileResult = await userService.getUserProfile(user.id);
        
        // 更新用户状态
        dispatch(setUser({
          id: user.id,
          email: user.email || '',
          profile: profileResult.data,
          // 设置为邮箱登录方式
          providerType: 'email'
        }));
        
        // 确保用户有资料记录
        if (!profileResult.data && user.id) {
          log('创建新用户资料');
          await userService.createUserProfile({
            id: user.id,
            email: user.email,
            full_name: user.email?.split('@')[0] || '用户',
            created_at: new Date(),
            updated_at: new Date()
          });
        }
        
        // RevenueCat用户标识设置 - 与Google/Apple登录保持一致
        try {
          log('邮箱登录: 开始设置RevenueCat用户标识:', user.id);
          await purchaseService.identifyUser(user.id);
          log('邮箱登录: RevenueCat用户标识设置成功');
        } catch (rcError) {
          logError('邮箱登录: RevenueCat用户标识失败:', rcError);
          
          // 添加重试机制，最多尝试3次
          let retries = 3;
          while (retries > 0) {
            try {
              log(`邮箱登录: 尝试重新连接RevenueCat (剩余${retries}次)...`);
              // 等待3秒后重试
              await new Promise(resolve => setTimeout(resolve, 3000));
              await purchaseService.identifyUser(user.id);
              
              log('邮箱登录: RevenueCat用户标识重试成功');
              break;
            } catch (retryError) {
              retries--;
              logError(`邮箱登录: RevenueCat重试失败 (剩余${retries}次):`, retryError);
              if (retries === 0) {
                log('邮箱登录: RevenueCat连接失败，但登录过程将继续');
              }
            }
          }
        }
        
        // 同步购买状态以更新VIP状态
        try {
          log('邮箱登录: 同步购买状态...');
          await purchaseService.syncPurchases();
          log('邮箱登录: 购买状态同步完成');
          
          // 确保从服务器获取最新的VIP状态
          await purchaseService.getServerVIPStatus();
        } catch (syncError) {
          logError('邮箱登录: 同步购买状态失败:', syncError);
          // 继续登录流程，不中断
        }
        
        log('邮箱登录成功，关闭登录页面');
        
        // 设置标志，确保不会切换标签页
        global.setPreserveTabAfterLogin(true);
        log('[全局] 设置preserveTabAfterLogin = true');
        
        // 添加VIP升级状态调试
        log('=== 邮箱登录成功 - VIP升级状态检查 ===');
        log('当前global.pendingVipUpgrade:', global.pendingVipUpgrade);
        log('即将关闭login页面，VIP升级流程应该接管');
        
        // 保存VIP升级状态，因为登录过程中状态可能变化
        const shouldNavigateToPayment = global.pendingVipUpgrade;
        
        // 等待状态更新完成再返回
        setTimeout(() => {
          log('邮箱登录处理完成，准备关闭登录页面');
          setLoginInProgress(false);
          
          // 先关闭登录页面
          if (router.canGoBack()) {
            log('关闭登录页面');
            router.back();
            
            // 如果有待处理的VIP升级，在关闭登录页面后导航到支付页面
            if (shouldNavigateToPayment) {
              log('VIP升级场景：登录页面关闭后，导航到支付页面');
              // 延迟导航到支付页面，确保login页面已经关闭
              setTimeout(() => {
                log('执行VIP升级导航到支付页面');
                // 重置VIP升级标志
                global.pendingVipUpgrade = false;
                router.push('/payment');
              }, 200);  // 给login页面足够时间关闭
            }
          }
        }, 100);  // 统一使用较短延迟
      }
    } catch (error: any) {
      logError('邮箱登录过程错误:', error);
      setErrorMessage(`登录过程发生错误：${error.message || '未知错误'}`);
      setLoginInProgress(false);
    }
  };
  
  // 渲染主内容
  return (
    <View style={{ 
      flex: 1, 
      backgroundColor: getColor('#f8f9fa', '#151718') 
    }}>
      <StatusBar style={isDark ? "light" : "dark"} />
      <SafeAreaView style={styles.container}>
        {/* 固定在顶部的关闭按钮 */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.closeButton} 
            onPress={handleSkip}
          >
            <IconSymbol 
              name="xmark"
              size={24} 
              color={getColor('#333', '#ECEDEE')}
            />
          </TouchableOpacity>
        </View>
        
        {/* 可滚动的内容区域 */}
        <ScrollView 
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.content}>
            <View style={styles.logoContainer}>
              <View style={[
                styles.logoWrapper, 
                { backgroundColor: isDark ? '#151718' : '#fff' }
              ]}>
                <Image
                  source={require('@/assets/images/icon.png')}
                  style={styles.logo}
                  resizeMode="contain"
                />
              </View>
            </View>
            
            <Text style={[styles.title, { color: getColor('#333', '#ECEDEE') }]}>
              {t('login.title' as any)}
            </Text>
            
            <Text style={[styles.subtitle, { color: getColor('#6B7280', '#9BA1A6') }]}>
              {t('login.subtitle' as any)}
            </Text>
          </View>
          
          <View style={styles.buttonContainer}>
            {/* Google登录按钮 */}
            <TouchableOpacity
              style={[
                styles.loginButton, 
                { 
                  backgroundColor: getColor('#ffffff', '#ffffff'),
                  borderColor: getColor('#e0e0e0', '#e0e0e0'),
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 1 },
                  shadowOpacity: isDark ? 0.2 : 0.1,
                  shadowRadius: 2,
                  elevation: 2,
                }
              ]}
              onPress={handleGoogleSignIn}
              disabled={loginInProgress}
            >
              <View style={styles.buttonIcon}>
                <Image
                  source={require('@/assets/images/auth-providers/google_logo.png')}
                  style={{ width: 20, height: 20 }}
                  resizeMode="contain"
                />
              </View>
              <Text style={[styles.loginButtonText, { color: '#333' }]}>
                {t('login.googleLogin' as any)}
              </Text>
            </TouchableOpacity>
            
            {/* 开发环境邮箱登录区域 - 仅在开发环境显示 */}
            {showDevLogin && (
              <View style={styles.devLoginContainer}>
                <Text style={[styles.devLoginTitle, isDark ? { color: '#e91e63' } : { color: '#d32f2f' }]}>
                  {t('login.devLoginTitle' as any)}
                </Text>
                
                <TextInput
                  style={[
                    styles.input,
                    isDark ? { backgroundColor: '#333', color: '#fff', borderColor: '#555' } : { backgroundColor: '#f5f5f5', color: '#333', borderColor: '#ddd' }
                  ]}
                  value={email}
                  onChangeText={setEmail}
                  placeholder={t('login.emailPlaceholder' as any)}
                  placeholderTextColor={isDark ? '#aaa' : '#999'}
                  autoCapitalize="none"
                  keyboardType="email-address"
                />
                
                <TextInput
                  style={[
                    styles.input,
                    isDark ? { backgroundColor: '#333', color: '#fff', borderColor: '#555' } : { backgroundColor: '#f5f5f5', color: '#333', borderColor: '#ddd' }
                  ]}
                  value={password}
                  onChangeText={setPassword}
                  placeholder={t('login.passwordPlaceholder' as any)}
                  placeholderTextColor={isDark ? '#aaa' : '#999'}
                  secureTextEntry
                />
                
                <TouchableOpacity
                  style={[styles.devLoginButton, loginInProgress ? { opacity: 0.7 } : {}]}
                  onPress={handleEmailSignIn}
                  disabled={loginInProgress}
                >
                  <Text style={styles.devLoginButtonText}>{t('login.devLoginButtonText' as any)}</Text>
                </TouchableOpacity>
              </View>
            )}
            
            {/* 只在iOS上显示Apple登录按钮 */}
            {Platform.OS === 'ios' && (
              <TouchableOpacity
                style={[
                  styles.loginButton, 
                  { 
                    backgroundColor: getColor('#000000', '#1a1a1a'), 
                    marginTop: 12,
                    borderColor: getColor('#000000', '#2a2a2a'),
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 1 },
                    shadowOpacity: isDark ? 0.4 : 0.2,
                    shadowRadius: 3,
                    elevation: 3,
                  }
                ]}
                onPress={handleAppleSignIn}
                disabled={loginInProgress}
              >
                <View style={styles.buttonIcon}>
                  <AntDesign name="apple1" size={20} color="#fff" />
                </View>
                <Text style={[
                  styles.loginButtonText, 
                  { color: '#fff' }
                ]}>
                  {t('login.appleLogin' as any)}
                </Text>
              </TouchableOpacity>
            )}
            
            {/* 跳过登录按钮 */}
            <TouchableOpacity
              style={[
                styles.skipButton, 
                { 
                  borderColor: getColor('#e0e0e0', '#2B2F31'),
                  backgroundColor: getColor('transparent', 'transparent'),
                  marginTop: 16
                }
              ]}
              onPress={handleSkip}
              disabled={loginInProgress}
            >
              <Text style={[styles.skipButtonText, { color: getColor('#6B7280', '#9BA1A6') }]}>
                {t('login.skipLogin' as any)}
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: getColor('#6B7280', '#9BA1A6') }]}>
              {t('login.footerText' as any)}
            </Text>
            <View style={styles.footerLinks}>
              <TouchableOpacity onPress={handleTermsOfService}>
                <Text style={[styles.footerLink, { color: getColor('#6a5ae1', '#8364e2') }]}>
                  {t('login.serviceTerms' as any)}
                </Text>
              </TouchableOpacity>
              <Text style={[styles.footerSeparator, { color: getColor('#6B7280', '#9BA1A6') }]}>{t('login.and' as any)}</Text>
              <TouchableOpacity onPress={handlePrivacyPolicy}>
                <Text style={[styles.footerLink, { color: getColor('#6a5ae1', '#8364e2') }]}>
                  {t('login.privacyPolicy' as any)}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
      
      {/* 全屏加载指示器 - 使用 loginInProgress 本地状态而不是全局 isLoading */}
      {loginInProgress && (
        <View style={styles.loadingOverlayContainer}>
          <View style={styles.loadingOverlayContent}>
            <ActivityIndicator size="large" color="#6a5ae1" />
            <Text style={styles.loadingText}>{t('login.loadingText' as any)}</Text>
          </View>
        </View>
      )}
      
      {/* Toast错误提示 */}
      {errorMessage && (
        <Animated.View 
          style={[
            styles.toastContainer, 
            { opacity: errorVisible ? 1 : 0 }
          ]}
        >
          <View style={styles.toastContent}>
            <View style={styles.toastIcon}>
              <IconSymbol name="exclamationmark.circle.fill" size={20} color="#fff" />
            </View>
            <Text style={styles.toastText}>{errorMessage}</Text>
            <TouchableOpacity style={styles.toastClose} onPress={clearError}>
              <IconSymbol name="xmark" size={16} color="#fff" />
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 12,
    position: 'relative',
    height: 60,
    justifyContent: 'center',
    alignItems: 'flex-end',
    zIndex: 10, // 确保关闭按钮在最上层
  },
  closeButton: {
    padding: 12,
    position: 'absolute',
    right: 8,
    top: 8,
  },
  // 新增：ScrollView容器样式
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20, // 底部留出空间
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingTop: 20, // 调整顶部间距
    minHeight: 300, // 确保有足够高度来居中显示
  },
  logoContainer: {
    marginBottom: 32,
  },
  logoWrapper: {
    borderRadius: 20,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 0,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
    paddingHorizontal: 16,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#ef4444',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    width: '100%',
  },
  errorText: {
    color: '#fff',
    fontSize: 14,
    flex: 1,
  },
  errorCloseButton: {
    marginLeft: 8,
  },
  buttonContainer: {
    paddingHorizontal: 24,
    width: '100%',
    marginTop: 20, // 添加顶部间距
  },
  loginButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    position: 'relative',
  },
  appleButton: {
    height: 50,
    width: '100%',
  },
  buttonIcon: {
    marginRight: 10,
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loginButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  skipButton: {
    marginTop: 12,
    paddingVertical: 16,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  footer: {
    alignItems: 'center',
    paddingBottom: 24,
    paddingTop: 24, // 添加顶部间距
  },
  footerText: {
    fontSize: 14,
    marginBottom: 4,
  },
  footerLinks: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerLink: {
    fontSize: 14,
    fontWeight: '500',
  },
  footerSeparator: {
    fontSize: 14,
    marginHorizontal: 4,
  },
  
  // 全屏加载浮层样式
  loadingOverlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
  loadingOverlayContent: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  
  // Toast样式
  toastContainer: {
    position: 'absolute',
    top: '50%', // 定位到屏幕垂直中间
    marginTop: 50, // 向下偏移50像素
    left: 20,
    right: 20,
    alignItems: 'center',
    zIndex: 9999,
  },
  toastContent: {
    flexDirection: 'row',
    backgroundColor: '#ef4444',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    maxWidth: '100%',
  },
  toastIcon: {
    marginRight: 10,
  },
  toastText: {
    flex: 1,
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  toastClose: {
    marginLeft: 10,
    padding: 4,
  },
  // 开发环境登录相关样式
  devLoginContainer: {
    width: '100%',
    marginTop: 16,
    marginBottom: 16,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#ff9800',
    borderRadius: 12,
    backgroundColor: 'rgba(255, 152, 0, 0.1)',
  },
  devLoginTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  input: {
    width: '100%',
    height: 44,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 12,
    paddingHorizontal: 12,
  },
  devLoginButton: {
    backgroundColor: '#ff9800',
    paddingVertical: 12,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  devLoginButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

// 从URL中提取指定参数值的函数 (添加在文件底部或者组件外部)
const extractToken = (url: string, tokenName: string): string => {
  // 处理 # 和 & 格式
  const hashMatch = new RegExp(`[#&]${tokenName}=([^&]*)`, 'i').exec(url);
  if (hashMatch && hashMatch[1]) {
    return hashMatch[1];
  }
  
  // 处理 ? 格式
  const queryMatch = new RegExp(`[?]${tokenName}=([^&]*)`, 'i').exec(url);
  if (queryMatch && queryMatch[1]) {
    return queryMatch[1];
  }
  
  return '';
}; 