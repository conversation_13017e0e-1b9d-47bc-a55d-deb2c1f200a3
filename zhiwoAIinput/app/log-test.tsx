/**
 * 日志测试页面
 * 用于开发环境下测试日志服务和RevenueCat配置
 */
import React from 'react';
import { ScrollView } from 'react-native';
import { Stack } from 'expo-router';
import LogTestComponent from '@/components/LogTestComponent';
import { useAppTheme } from '@/hooks/useAppTheme';

export default function LogTestScreen() {
  const theme = useAppTheme();
  const isDark = theme === 'dark';

  return (
    <ScrollView style={{ flex: 1 }}>
      <Stack.Screen
        options={{
          title: '日志测试工具',
          headerShown: true,
          presentation: 'card',
          headerTintColor: isDark ? '#ffffff' : '#000000',
          headerStyle: {
            backgroundColor: isDark ? '#1a1a1a' : '#ffffff',
          },
        }}
      />
      <LogTestComponent />
    </ScrollView>
  );
} 