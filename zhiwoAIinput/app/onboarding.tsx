import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Dimensions,
  Alert,
  SafeAreaView,
  Switch,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import * as Localization from 'expo-localization';
// import { systemTemplates } from '@/services/templateService'; // 已移除，现在使用云端同步
import { OnboardingSurvey, completeOnboarding, saveSelectedTemplate } from '@/services/onboardingService';
import { useAuth } from '@/hooks/useAuth';
import { LanguageCode, saveLanguagePreference } from '@/store/slices/languageSlice';
import { log, warn, error as logError } from '@/services/logService';
import { getTemplateDisplayName, getTemplateDisplayDescription, StyleTemplateFromDB } from '@/utils/templateI18nUtils';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// 问卷步骤枚举 - 新增语言选择作为第一步，极速模式作为第五步
enum OnboardingStep {
  LanguageSelection = 0, // 新增：语言选择
  Welcome = 1,
  UseCases = 2, 
  Source = 3,   
  Templates = 4,
  FastMode = 5, // 新增：极速模式设置
  Complete = 6,
}

// 语言选项配置 - 英语调整为第一位
const getLanguageOptions = () => [
  { 
    code: 'en' as LanguageCode, 
    name: 'English', 
    englishName: 'English'
  },
  { 
    code: 'zh-Hans' as LanguageCode, 
    name: '简体中文', 
    englishName: 'Simplified Chinese'
  },
  { 
    code: 'zh-Hant' as LanguageCode, 
    name: '繁體中文', 
    englishName: 'Traditional Chinese'
  },
];

// 检测系统语言并返回对应的LanguageCode
const getSystemLanguage = (): LanguageCode => {
  try {
    const systemLocale = Localization.getLocales()[0];
    if (!systemLocale) {
      return 'en'; // 默认英语
    }
    
    // 检查语言代码
    const languageCode = systemLocale.languageCode;
    const regionCode = systemLocale.regionCode;
    
    log('[Onboarding] 系统语言检测:', {
      languageCode,
      regionCode,
      fullLocale: systemLocale.languageTag
    });
    
    // 根据语言代码匹配
    if (languageCode === 'zh') {
      // 简体中文的地区代码：CN, SG
      if (regionCode === 'CN' || regionCode === 'SG') {
        return 'zh-Hans';
      }
      // 繁体中文的地区代码：TW, HK, MO
      else if (regionCode === 'TW' || regionCode === 'HK' || regionCode === 'MO') {
        return 'zh-Hant';
      }
      // 其他中文地区默认简体
      else {
        return 'zh-Hans';
      }
    } else if (languageCode === 'en') {
      return 'en';
    }
    
    // 如果不是支持的语言，默认返回英语
    return 'en';
  } catch (error) {
    warn('[Onboarding] 系统语言检测失败，使用默认语言:', error);
    return 'en';
  }
};

// 了解渠道选项 - 根据语言动态生成
const getSourceOptions = (t: any, currentLanguage: LanguageCode) => {
  const baseOptions = [
    { id: 'app_store_search', label: t('onboarding.source.options.app_store_search'), icon: 'magnifyingglass' },
    { id: 'app_store_ad', label: t('onboarding.source.options.app_store_ad'), icon: 'rectangle.and.hand.point.up.left' },
  ];
  
  // 根据语言添加不同的平台选项
  const platformOptions = [];
  
  if (currentLanguage === 'en') {
    // 英文版本显示 TikTok
    platformOptions.push(
      { id: 'tiktok', label: t('onboarding.source.options.tiktok'), icon: 'play.tv' }
    );
  } else {
    // 中文版本显示抖音
    platformOptions.push(
      { id: 'douyin', label: t('onboarding.source.options.douyin'), icon: 'play.tv' }
    );
  }
  
  // 公共选项
  const commonOptions = [
    { id: 'youtube_ad', label: t('onboarding.source.options.youtube_ad'), icon: 'play.rectangle' },
    { id: 'xiaohongshu', label: t('onboarding.source.options.xiaohongshu'), icon: 'book.closed' },
    { id: 'instagram', label: t('onboarding.source.options.instagram'), icon: 'camera' },
    { id: 'friend_recommend', label: t('onboarding.source.options.friend_recommend'), icon: 'person.2' },
    { id: 'other', label: t('onboarding.source.options.other'), icon: 'ellipsis' },
  ];
  
  return [...baseOptions, ...platformOptions, ...commonOptions];
};

// 使用场景选项
const getUseCaseOptions = (t: any) => [
  { id: 'daily_chat', label: t('onboarding.useCases.options.daily_chat'), icon: 'bubble.left.and.bubble.right' },
  // 新增多语言翻译选项
  { id: 'translation', label: t('onboarding.useCases.options.translation'), icon: 'translate' },
  { id: 'work_communication', label: t('onboarding.useCases.options.work_communication'), icon: 'briefcase' },
  { id: 'social_media', label: t('onboarding.useCases.options.social_media'), icon: 'heart.circle' },
  { id: 'xiaohongshu_post', label: t('onboarding.useCases.options.xiaohongshu_post'), icon: 'book.closed' },
  { id: 'inspiration', label: t('onboarding.useCases.options.inspiration'), icon: 'lightbulb' },
  { id: 'ai_prompt', label: t('onboarding.useCases.options.ai_prompt'), icon: 'brain.head.profile' },
  { id: 'other', label: t('onboarding.useCases.options.other'), icon: 'ellipsis' },
];

// 模板分类多语言映射
const getCategoryTranslation = (category: string, t: any): string => {
  const categoryMap: { [key: string]: string } = {
    'scenario': t('onboarding.templateCategories.scenario'),
    'translation': t('onboarding.templateCategories.translation'),
    'ai_prompt': t('onboarding.templateCategories.ai_prompt'),
    'writing': t('onboarding.templateCategories.writing'),
  };
  return categoryMap[category] || category;
};

// 新增：将云端模板转换为StyleTemplateFromDB格式的工具函数
const convertToStyleTemplateFromDB = (template: any): StyleTemplateFromDB => {
  return {
    id: template.id,
    name: template.title || template.name || '',
    description: template.description || '',
    name_key: template.name_key,
    description_key: template.description_key,
    prompt_text: template.prompt || template.prompt_text || '',
    is_system: template.isSystem || template.is_system || false,
    color: template.color,
    icon: template.icon,
    position: template.position,
    is_active: template.is_active,
    user_id: template.user_id,
    created_at: template.created_at,
    updated_at: template.updated_at
  };
};

export default function OnboardingScreen() {
  const [currentStep, setCurrentStep] = useState(OnboardingStep.LanguageSelection);
  const [selectedLanguage, setSelectedLanguage] = useState<LanguageCode>(getSystemLanguage());
  const [selectedSource, setSelectedSource] = useState<string>('');
  const [selectedUseCases, setSelectedUseCases] = useState<string[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>(''); // 改为单选
  const [fastModeEnabled, setFastModeEnabled] = useState<boolean>(false); // 新增：极速模式状态
  const [otherSourceText, setOtherSourceText] = useState('');
  const [otherUseCaseText, setOtherUseCaseText] = useState('');
  const [loading, setLoading] = useState(false);
  const [cloudTemplates, setCloudTemplates] = useState<any[]>([]);
  const [templatesLoading, setTemplatesLoading] = useState(true);
  const [languageChanging, setLanguageChanging] = useState(false);

  const dispatch = useDispatch();
  const theme = useAppTheme();
  const isDark = theme === 'dark';
  const { t, i18n } = useTranslation();
  const { isAuthenticated, isVIP } = useSelector((state: RootState) => state.auth);
  const currentLanguage = useSelector((state: RootState) => state.language.language);
  const { showLoginModal } = useAuth();

  // 初始化时设置当前语言 - 优先使用系统检测到的语言并立即切换界面语言
  useEffect(() => {
    const initializeLanguage = async () => {
      const systemLang = getSystemLanguage();
      log('[Onboarding] 初始化语言设置:', {
        currentLanguage,
        systemLanguage: systemLang,
        selectedLanguage,
        currentI18nLanguage: i18n.language
      });
      
      // 如果当前Redux中的语言与系统语言不同，则使用系统语言
      if (currentLanguage !== systemLang) {
        setSelectedLanguage(systemLang);
        // 立即切换i18n语言以更新界面
        try {
          await i18n.changeLanguage(systemLang);
          log('[Onboarding] 使用系统检测到的语言并切换界面:', systemLang);
        } catch (error) {
          logError('[Onboarding] 切换到系统语言失败:', error);
        }
      } else {
        setSelectedLanguage(currentLanguage);
        // 确保i18n语言与当前语言一致
        if (i18n.language !== currentLanguage) {
          try {
            await i18n.changeLanguage(currentLanguage);
            log('[Onboarding] 使用Redux中的当前语言并同步界面:', currentLanguage);
          } catch (error) {
            logError('[Onboarding] 同步i18n语言失败:', error);
          }
        }
      }
    };

    initializeLanguage();
  }, [currentLanguage, i18n]);

  // 组件加载时获取云端模板数据 - 改进加载逻辑
  useEffect(() => {
    const loadCloudTemplates = async () => {
      try {
        setTemplatesLoading(true);
        const { syncSystemTemplates } = await import('@/services/templateSyncService');
        const templates = await syncSystemTemplates();
        log('[Onboarding] 成功加载云端模板:', templates.length, '个');
        setCloudTemplates(templates);
      } catch (error) {
        warn('[Onboarding] 加载云端模板失败，使用空数组:', error);
        setCloudTemplates([]);
        // 显示友好的错误提示
        Alert.alert(
          t('common.error'),
          t('onboarding.templates.loadError'),
          [
            {
              text: t('common.retry'),
              onPress: () => {
                // 重试加载
                loadCloudTemplates();
              }
            },
            {
              text: t('common.skip'),
              style: 'cancel'
            }
          ]
        );
      } finally {
        setTemplatesLoading(false);
      }
    };

    // 只有在不是语言选择步骤时才加载模板
    if (currentStep !== OnboardingStep.LanguageSelection) {
      loadCloudTemplates();
    }
  }, [currentStep, t]);

  // 恢复已选择的模板（防止重新渲染时丢失）
  useEffect(() => {
    const restoreSelectedTemplate = async () => {
      try {
        const { getSelectedTemplates } = await import('@/services/onboardingService');
        const savedTemplates = await getSelectedTemplates();
        if (savedTemplates.length > 0 && !selectedTemplate) {
          log('[Onboarding] 从AsyncStorage恢复模板选择:', savedTemplates[0]);
          setSelectedTemplate(savedTemplates[0]); // 只取第一个作为默认模板
        }
      } catch (error) {
        warn('[Onboarding] 恢复模板选择失败:', error);
      }
    };

    // 只在模板选择步骤时恢复
    if (currentStep === OnboardingStep.Templates) {
      restoreSelectedTemplate();
    }
  }, [currentStep, selectedTemplate]);

  // 获取模板按分类分组 - 改进分类显示
  const getTemplatesByCategory = () => {
    const categories: { [key: string]: any[] } = {};
    const templatesToUse = cloudTemplates;
    
    templatesToUse.forEach((template: any) => {
      const category = template.category || '其他';
      const translatedCategory = getCategoryTranslation(category, t);
      if (!categories[translatedCategory]) {
        categories[translatedCategory] = [];
      }
      categories[translatedCategory].push(template);
    });
    return categories;
  };

  // 处理语言选择
  const handleLanguageSelect = async (language: LanguageCode) => {
    try {
      setLanguageChanging(true);
      setSelectedLanguage(language);
      
      // 即时切换i18n语言进行预览
      await i18n.changeLanguage(language);
      
      log(`[Onboarding] 语言已切换到: ${language} (预览模式)`);
      
      // 短暂延迟让用户看到选择效果
      setTimeout(() => {
        setLanguageChanging(false);
      }, 300);
    } catch (error) {
      logError('[Onboarding] 切换语言失败:', error);
      setLanguageChanging(false);
    }
  };

  // 确认语言选择
  const handleLanguageConfirm = async () => {
    try {
      setLoading(true);
      
      // 先保存语言选择到Redux和AsyncStorage
      await dispatch(saveLanguagePreference(selectedLanguage) as any);
      
      // 然后同步更新i18n语言
      await i18n.changeLanguage(selectedLanguage);
      
      log(`[Onboarding] 语言选择已确认并同步到i18n: ${selectedLanguage}`);
      
      // 强制等待一下确保语言切换完成
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 进入下一步
      setCurrentStep(OnboardingStep.Welcome);
    } catch (error) {
      logError('[Onboarding] 保存语言选择失败:', error);
      Alert.alert(
        t('common.error'), 
        'Failed to save language preference, please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  // 返回语言选择页面
  const handleBackToLanguageSelection = () => {
    setCurrentStep(OnboardingStep.LanguageSelection);
  };

  // 处理下一步
  const handleNext = () => {
    if (currentStep < OnboardingStep.Complete) {
      setCurrentStep(currentStep + 1);
    }
  };

  // 处理上一步
  const handleBack = () => {
    if (currentStep > OnboardingStep.LanguageSelection) {
      setCurrentStep(currentStep - 1);
    }
  };

  // 处理来源选择
  const handleSourceSelect = (sourceId: string) => {
    setSelectedSource(sourceId);
  };

  // 处理使用场景选择
  const handleUseCaseToggle = (useCaseId: string) => {
    setSelectedUseCases(prev => 
      prev.includes(useCaseId) 
        ? prev.filter(id => id !== useCaseId)
        : [...prev, useCaseId]
    );
  };

  // 处理模板选择（单选模式）
  const handleTemplateSelect = async (templateId: string) => {
    const templatesToUse = cloudTemplates;
    const template = templatesToUse.find((t: any) => t.id === templateId);
    
    log('[Onboarding] 模板选择事件:', {
      templateId,
      templateTitle: template?.title,
      currentSelected: selectedTemplate,
      isAlreadySelected: selectedTemplate === templateId
    });
    
    // 如果是VIP模板但用户不是VIP，引导登录
    if (template?.isVipOnly && !isVIP) {
      log('[Onboarding] VIP模板访问被拒绝:', template.title);
      if (!isAuthenticated) {
        // 设置VIP升级标志
        global.pendingVipUpgrade = true;
        showLoginModal();
        return;
      } else {
        // 已登录但不是VIP，跳转到支付页面
        router.push('/payment');
        return;
      }
    }

    // 单选模式：直接设置为选中的模板
    log('[Onboarding] 设置默认模板:', {
      previous: selectedTemplate,
      new: templateId,
      templateName: template?.title
    });
    
    // 立即保存到AsyncStorage作为备份
    try {
      await saveSelectedTemplate(templateId);
      log('[Onboarding] 已立即保存默认模板选择到AsyncStorage:', templateId);
    } catch (error) {
      warn('[Onboarding] 立即保存模板选择失败:', error);
    }
    
    setSelectedTemplate(templateId);
  };

  // 处理跳过问卷
  const handleSkip = async () => {
    try {
      setLoading(true);
      
      // 跳过问卷，直接跳转到模板选择步骤
      setCurrentStep(OnboardingStep.Templates);
      
    } catch (error) {
      logError('跳过引导失败:', error);
      Alert.alert('错误', '操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 完成引导
  const handleComplete = async () => {
    try {
      setLoading(true);
      
      // 获取最新的模板选择（防止状态丢失）
      let finalSelectedTemplate = selectedTemplate;
      try {
        const { getSelectedTemplates } = await import('@/services/onboardingService');
        const savedTemplates = await getSelectedTemplates();
        if (savedTemplates.length > 0) {
          finalSelectedTemplate = savedTemplates[0]; // 取第一个作为默认模板
          log('[Onboarding] 使用AsyncStorage中的默认模板:', savedTemplates[0]);
        }
      } catch (error) {
        warn('[Onboarding] 获取AsyncStorage模板选择失败，使用状态值:', error);
      }
      
      log('[Onboarding] 开始完成引导流程:', {
        selectedSource,
        selectedUseCasesCount: selectedUseCases.length,
        selectedTemplate: finalSelectedTemplate,
        fastModeEnabled,
        currentStep
      });

      // 检查是否是跳过问卷的情况（只选择了模板）
      if (selectedSource === '' && selectedUseCases.length === 0) {
        log('[Onboarding] 跳过问卷模式，只保存模板选择');
        // 只保存模板选择，不保存问卷数据
        if (finalSelectedTemplate) {
          await saveSelectedTemplate(finalSelectedTemplate);
          log('[Onboarding] 已保存选择的默认模板到AsyncStorage');
        }
        await completeOnboarding(); // 不传入survey参数
        log('[Onboarding] 已完成onboarding状态设置');
        
        // 保存极速模式设置
        try {
          const { store } = await import('@/store');
          const { toggleFastModeLocal } = await import('@/store/slices/fastModeSlice');
          await store.dispatch(toggleFastModeLocal(fastModeEnabled));
          log('[Onboarding] 跳过问卷后已保存极速模式设置:', fastModeEnabled);
        } catch (fastModeError) {
          warn('[Onboarding] 保存极速模式设置失败:', fastModeError);
        }
        
        // 初始化模板偏好设置
        if (finalSelectedTemplate) {
          log('[Onboarding] 开始初始化模板偏好设置，选中的默认模板:', finalSelectedTemplate);
          try {
            const { initializeTemplatePreferences } = await import('@/services/onboardingService');
            await initializeTemplatePreferences();
            log('[Onboarding] 跳过问卷后已初始化模板偏好设置');
            
            // 验证默认模板是否正确设置
            const { getDefaultTemplateId } = await import('@/services/templateService');
            const currentDefaultId = await getDefaultTemplateId();
            log('[Onboarding] 跳过问卷后当前默认模板ID:', currentDefaultId);
            log('[Onboarding] 期望的默认模板ID:', finalSelectedTemplate);
          
          // 重新加载模板数据，让偏好设置生效
          try {
            const { store } = await import('@/store');
            const { loadTemplates } = await import('@/store/slices/templateSlice');
            await store.dispatch(loadTemplates());
            log('[Onboarding] 跳过问卷后已重新加载模板数据');
          } catch (reduxError) {
            warn('[Onboarding] 跳过问卷后重新加载模板数据失败:', reduxError);
          }
        } catch (prefError) {
          warn('[Onboarding] 跳过问卷后初始化偏好设置失败:', prefError);
        }
      } else {
        log('[Onboarding] 跳过问卷时没有选择任何模板');
      }
        
        log('[Onboarding] 跳过问卷后引导流程已完成');
        router.replace('/(tabs)');
        return;
      }

      // 正常完成问卷流程
      const survey: OnboardingSurvey = {
        source: selectedSource,
        useCases: selectedUseCases,
        selectedTemplate: finalSelectedTemplate || '',
        fastModeEnabled: fastModeEnabled,
        completedAt: new Date().toISOString(),
      };

      await completeOnboarding(survey);
      
      // 保存极速模式设置
      try {
        const { store } = await import('@/store');
        const { toggleFastModeLocal } = await import('@/store/slices/fastModeSlice');
        await store.dispatch(toggleFastModeLocal(fastModeEnabled));
        log('[Onboarding] 正常流程已保存极速模式设置:', fastModeEnabled);
      } catch (fastModeError) {
        warn('[Onboarding] 保存极速模式设置失败:', fastModeError);
      }
      
      // 初始化模板偏好设置
      if (finalSelectedTemplate) {
        try {
          const { initializeTemplatePreferences } = await import('@/services/onboardingService');
          await initializeTemplatePreferences();
          log('[Onboarding] 已初始化模板偏好设置');
          
          // 重新加载模板数据，让偏好设置生效
          try {
            const { store } = await import('@/store');
            const { loadTemplates } = await import('@/store/slices/templateSlice');
            await store.dispatch(loadTemplates());
            log('[Onboarding] 已重新加载模板数据');
          } catch (reduxError) {
            warn('[Onboarding] 重新加载模板数据失败:', reduxError);
          }
        } catch (prefError) {
          warn('[Onboarding] 初始化偏好设置失败:', prefError);
        }
      }

      log('[Onboarding] 引导流程已完成');
      router.replace('/(tabs)');
    } catch (error) {
      logError('完成引导失败:', error);
      Alert.alert('错误', '完成设置失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 检查是否可以继续下一步
  const canProceed = () => {
    switch (currentStep) {
      case OnboardingStep.LanguageSelection:
        return true; // 语言选择总是可以继续的
      case OnboardingStep.Welcome:
        return true;
      case OnboardingStep.UseCases:
        return selectedUseCases.length > 0;
      case OnboardingStep.Source:
        return selectedSource !== '';
      case OnboardingStep.Templates:
        return !!selectedTemplate; // 必须选择一个默认模板
      case OnboardingStep.FastMode:
        return true; // 极速模式设置总是可以继续的
      default:
        return false;
    }
  };

    // 渲染语言选择页面
  const renderLanguageSelection = () => {
    const languageOptions = getLanguageOptions();
    
    return (
      <View style={styles.stepContainer}>
        <View style={styles.languageContent}>
          <IconSymbol name="globe" size={80} color="#6366F1" />
          <Text style={[styles.languageTitle, { color: isDark ? '#F0F6FC' : '#1F2937' }]}>
            {t('onboarding.languageSelection.title') as string}
          </Text>
          <Text style={[styles.languageSubtitle, { color: isDark ? '#8B949E' : '#6B7280' }]}>
            {t('onboarding.languageSelection.subtitle') as string}
          </Text>
          
          <View style={styles.languageOptionsContainer}>
            {languageOptions.map((option) => (
              <TouchableOpacity
                key={option.code}
                style={[
                  styles.languageOption,
                  selectedLanguage === option.code && styles.selectedLanguageOption,
                  {
                    backgroundColor: selectedLanguage === option.code 
                      ? '#6366F1' 
                      : (isDark ? '#21262D' : '#FFFFFF'),
                    borderColor: selectedLanguage === option.code 
                      ? '#6366F1' 
                      : (isDark ? '#30363D' : '#E5E7EB'),
                  }
                ]}
                onPress={() => handleLanguageSelect(option.code)}
                disabled={languageChanging}
              >
                <View style={styles.languageTextContainer}>
                  <Text style={[
                    styles.languageName,
                    { 
                      color: selectedLanguage === option.code 
                        ? '#FFFFFF' 
                        : (isDark ? '#F0F6FC' : '#1F2937') 
                    }
                  ]}>
                    {option.name}
                  </Text>
                  <Text style={[
                    styles.languageEnglishName,
                    { 
                      color: selectedLanguage === option.code 
                        ? '#E0E7FF' 
                        : (isDark ? '#8B949E' : '#6B7280') 
                    }
                  ]}>
                    {option.englishName}
                  </Text>
                </View>
                {/* 选中符号 - 始终显示，但只在选中时可见 */}
                <View style={styles.languageCheckContainer}>
                  {selectedLanguage === option.code && (
                    <Text style={styles.languageCheckmark}>✓</Text>
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* 语言选择确认按钮 */}
        <View style={styles.languageButtonContainer}>
          <TouchableOpacity
            style={[
              styles.languageConfirmButton,
              !selectedLanguage && styles.disabledButton,
            ]}
            onPress={handleLanguageConfirm}
            disabled={!selectedLanguage || loading || languageChanging}
          >
            <LinearGradient
              colors={selectedLanguage ? ['#6366F1', '#8B5CF6'] : ['#9CA3AF', '#9CA3AF']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.languageConfirmGradient}
            >
              <Text style={styles.languageConfirmText}>
                {loading ? t("common.loading") : t("onboarding.navigation.next")}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // 渲染欢迎页面 - 恢复原有内容和布局
  const renderWelcome = () => (
    <View style={styles.stepContainer}>
      <View style={styles.welcomeContent}>
        <IconSymbol name="sparkles" size={80} color="#6366F1" />
        <Text style={[styles.welcomeTitle, { color: isDark ? '#F0F6FC' : '#1F2937' }]}>
          {t('onboarding.welcome.title') as string}
        </Text>
        <Text style={[styles.welcomeSubtitle, { color: isDark ? '#8B949E' : '#6B7280' }]}>
          {t('onboarding.welcome.subtitle') as string}
        </Text>
        
        {/* 语言切换入口 - 移到页面中间更和谐的位置 */}
        <TouchableOpacity
          style={styles.languageSwitchEntry}
          onPress={handleBackToLanguageSelection}
        >
          <IconSymbol name="globe" size={16} color="#6366F1" />
          <Text style={[styles.languageSwitchText, { color: isDark ? '#8B949E' : '#6B7280' }]}>
            {t('onboarding.welcome.changeLanguage') as string}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // 渲染使用场景调查 - 移到第一个问题
  const renderUseCases = () => {
    const useCaseOptions = getUseCaseOptions(t);
    
    return (
      <View style={styles.stepContainer}>
        <Text style={[styles.stepTitle, { color: isDark ? '#F0F6FC' : '#1F2937' }]}>
          {t('onboarding.useCases.title') as string}
        </Text>
        <Text style={[styles.stepSubtitle, { color: isDark ? '#8B949E' : '#6B7280' }]}>
          {t('onboarding.useCases.subtitle') as string}
        </Text>
        <ScrollView style={styles.optionsContainer} showsVerticalScrollIndicator={false}>
          {useCaseOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.optionButton,
                selectedUseCases.includes(option.id) && styles.selectedOption,
                { 
                  backgroundColor: selectedUseCases.includes(option.id) 
                    ? '#6366F1' 
                    : (isDark ? '#21262D' : '#FFFFFF'),
                  borderColor: isDark ? '#30363D' : '#E5E7EB',
                }
              ]}
              onPress={() => handleUseCaseToggle(option.id)}
            >
              <IconSymbol 
                name={option.icon as any} 
                size={20} 
                color={selectedUseCases.includes(option.id) ? '#FFFFFF' : (isDark ? '#8B949E' : '#6B7280')} 
              />
              <Text style={[
                styles.optionText,
                { 
                  color: selectedUseCases.includes(option.id) 
                    ? '#FFFFFF' 
                    : (isDark ? '#F0F6FC' : '#1F2937') 
                }
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  // 渲染来源调查 - 移到第二个问题
  const renderSource = () => {
    const sourceOptions = getSourceOptions(t, selectedLanguage);
    
    return (
      <View style={styles.stepContainer}>
        <Text style={[styles.stepTitle, { color: isDark ? '#F0F6FC' : '#1F2937' }]}>
          {t('onboarding.source.title') as string}
        </Text>
        <ScrollView style={styles.optionsContainer} showsVerticalScrollIndicator={false}>
          {sourceOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.optionButton,
                selectedSource === option.id && styles.selectedOption,
                { 
                  backgroundColor: selectedSource === option.id 
                    ? '#6366F1' 
                    : (isDark ? '#21262D' : '#FFFFFF'),
                  borderColor: isDark ? '#30363D' : '#E5E7EB',
                }
              ]}
              onPress={() => handleSourceSelect(option.id)}
            >
              <IconSymbol 
                name={option.icon as any} 
                size={20} 
                color={selectedSource === option.id ? '#FFFFFF' : (isDark ? '#8B949E' : '#6B7280')} 
              />
              <Text style={[
                styles.optionText,
                { 
                  color: selectedSource === option.id 
                    ? '#FFFFFF' 
                    : (isDark ? '#F0F6FC' : '#1F2937') 
                }
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  // 渲染模板选择 - 改进加载状态
  const renderTemplates = () => {
    const templatesByCategory = getTemplatesByCategory();
    
    log('[Onboarding] 渲染模板选择页面:', {
      templatesLoading,
      cloudTemplatesCount: cloudTemplates.length,
      selectedTemplate,
      categoriesCount: Object.keys(templatesByCategory).length
    });
    
    if (templatesLoading) {
      return (
        <View style={styles.stepContainer}>
          <Text style={[styles.stepTitle, { color: isDark ? '#F0F6FC' : '#1F2937' }]}>
            {t('onboarding.templates.title') as string}
          </Text>
          <View style={styles.loadingContainer}>
            <Text style={[styles.loadingText, { color: isDark ? '#8B949E' : '#6B7280' }]}>
              {t('onboarding.templates.loading') as string}
            </Text>
          </View>
        </View>
      );
    }

    if (cloudTemplates.length === 0) {
      return (
        <View style={styles.stepContainer}>
          <Text style={[styles.stepTitle, { color: isDark ? '#F0F6FC' : '#1F2937' }]}>
            {t('onboarding.templates.title') as string}
          </Text>
          <View style={styles.loadingContainer}>
            <Text style={[styles.loadingText, { color: isDark ? '#8B949E' : '#6B7280' }]}>
              {t('onboarding.templates.noTemplates') as string}
            </Text>
          </View>
        </View>
      );
    }
    
    return (
      <View style={styles.stepContainer}>
        <Text style={[styles.stepTitle, { color: isDark ? '#F0F6FC' : '#1F2937' }]}>
          {t('onboarding.templates.title') as string}
        </Text>
        <Text style={[styles.stepSubtitle, { color: isDark ? '#8B949E' : '#6B7280' }]}>
          {t('onboarding.templates.subtitle') as string}
        </Text>
        <ScrollView style={styles.templatesContainer} showsVerticalScrollIndicator={false}>
          {Object.entries(templatesByCategory).map(([category, templates]) => (
            <View key={category} style={styles.categorySection}>
              <Text style={[styles.categoryTitle, { color: isDark ? '#8B949E' : '#6B7280' }]}>
                {category}
              </Text>
              <View style={styles.templateGrid}>
                {templates.map((template) => (
                  <TouchableOpacity
                    key={template.id}
                    style={[
                      styles.templateCard,
                      selectedTemplate === template.id && styles.selectedTemplate,
                      { 
                        backgroundColor: selectedTemplate === template.id
                          ? template.backgroundColor
                          : (isDark ? '#21262D' : '#FFFFFF'),
                        borderColor: selectedTemplate === template.id
                          ? template.borderColor
                          : (isDark ? '#30363D' : '#E5E7EB'),
                      }
                    ]}
                    onPress={() => handleTemplateSelect(template.id)}
                  >
                    <View style={styles.templateHeader}>
                      <Text style={[
                        styles.templateTitle,
                        { 
                          color: selectedTemplate === template.id
                            ? template.color
                            : (isDark ? '#F0F6FC' : '#1F2937')
                        }
                      ]}>
                        {getTemplateDisplayName(convertToStyleTemplateFromDB(template), t)}
                      </Text>
                      {template.isVipOnly && (
                        <View style={styles.vipBadge}>
                          <IconSymbol name="crown" size={12} color="#F59E0B" />
                          <Text style={styles.vipText}>VIP</Text>
                        </View>
                      )}
                      {/* 单选指示器 */}
                      {selectedTemplate === template.id && (
                        <View style={styles.selectedIndicator}>
                          <IconSymbol name="checkmark.circle.fill" size={20} color="#6366F1" />
                        </View>
                      )}
                    </View>
                    <Text style={[
                      styles.templateDescription,
                      { 
                        color: selectedTemplate === template.id
                          ? template.color
                          : (isDark ? '#8B949E' : '#6B7280')
                      }
                    ]}>
                      {getTemplateDisplayDescription(convertToStyleTemplateFromDB(template), t)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          ))}
        </ScrollView>
      </View>
    );
  };

  // 渲染极速模式设置页面
  const renderFastMode = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: isDark ? '#F0F6FC' : '#1F2937' }]}>
        {(t as any)('onboarding.fastMode.title')}
      </Text>
      <Text style={[styles.stepSubtitle, { color: isDark ? '#8B949E' : '#6B7280' }]}>
        {(t as any)('onboarding.fastMode.subtitle')}
      </Text>
      
      <View style={styles.fastModeContainer}>
        {/* 极速模式开关卡片 */}
        <View style={[
          styles.fastModeCard,
          { 
            backgroundColor: isDark ? '#21262D' : '#FFFFFF',
            borderColor: isDark ? '#30363D' : '#E5E7EB',
          }
        ]}>
          <View style={styles.fastModeHeader}>
            <View style={styles.fastModeIconAndTitle}>
              <View style={[
                styles.fastModeIconContainer,
                { backgroundColor: fastModeEnabled ? '#6366F1' : (isDark ? '#30363D' : '#F3F4F6') }
              ]}>
                <IconSymbol 
                  name={fastModeEnabled ? "bolt.fill" : "bolt"} 
                  size={20} 
                  color={fastModeEnabled ? '#FFFFFF' : (isDark ? '#8B949E' : '#6B7280')} 
                />
              </View>
              <Text style={[
                styles.fastModeCardTitle,
                { color: isDark ? '#F0F6FC' : '#1F2937' }
              ]}>
                {(t as any)('onboarding.fastMode.enableLabel')}
              </Text>
            </View>
            <Switch
              value={fastModeEnabled}
              onValueChange={setFastModeEnabled}
              trackColor={{ 
                false: isDark ? '#39393D' : '#E5E7EB', 
                true: isDark ? '#8364E2' : '#6A5AE1' 
              }}
              thumbColor={fastModeEnabled ? '#FFFFFF' : '#FFFFFF'}
              ios_backgroundColor={isDark ? '#39393D' : '#E5E7EB'}
            />
          </View>
        </View>

        {/* 动态显示当前模式说明 */}
        <View style={[
          styles.modeDescriptionCard,
          { 
            backgroundColor: fastModeEnabled 
              ? (isDark ? '#1E293B' : '#F0F9FF') 
              : (isDark ? '#1F2937' : '#F9FAFB'),
            borderColor: fastModeEnabled 
              ? (isDark ? '#334155' : '#BFDBFE') 
              : (isDark ? '#374151' : '#E5E7EB'),
          }
        ]}>
          <View style={styles.modeDescriptionHeader}>
            <IconSymbol 
              name={fastModeEnabled ? "bolt.fill" : "gear"} 
              size={16} 
              color={fastModeEnabled ? '#3B82F6' : (isDark ? '#9CA3AF' : '#6B7280')} 
            />
            <Text style={[
              styles.modeDescriptionTitle,
              { 
                color: fastModeEnabled 
                  ? '#3B82F6' 
                  : (isDark ? '#D1D5DB' : '#374151') 
              }
            ]}>
              {fastModeEnabled 
                ? (t as any)('onboarding.fastMode.fastModeEnabled.title')
                : (t as any)('onboarding.fastMode.normalMode.title')
              }
            </Text>
          </View>
          
          <Text style={[
            styles.modeDescriptionText,
            { color: isDark ? '#9CA3AF' : '#6B7280' }
          ]}>
            {fastModeEnabled 
              ? (t as any)('onboarding.fastMode.fastModeEnabled.description')
              : (t as any)('onboarding.fastMode.normalMode.description')
            }
          </Text>
          
          <View style={styles.modeAdvantages}>
            <View style={styles.advantageItem}>
              <IconSymbol name="checkmark.circle" size={14} color="#10B981" />
              <Text style={[
                styles.advantageText,
                { color: isDark ? '#9CA3AF' : '#6B7280' }
              ]}>
                {fastModeEnabled 
                  ? (t as any)('onboarding.fastMode.fastModeEnabled.advantages')
                  : (t as any)('onboarding.fastMode.normalMode.advantages')
                }
              </Text>
            </View>
            
            <View style={styles.advantageItem}>
              <IconSymbol name="checkmark.circle" size={14} color="#10B981" />
              <Text style={[
                styles.advantageText,
                { color: isDark ? '#9CA3AF' : '#6B7280' }
              ]}>
                {fastModeEnabled 
                  ? (t as any)('onboarding.fastMode.fastModeEnabled.suitableFor')
                  : (t as any)('onboarding.fastMode.normalMode.suitableFor')
                }
              </Text>
            </View>
          </View>
        </View>

        {/* 提示信息 */}
        <View style={[
          styles.fastModeNote,
          { 
            backgroundColor: isDark ? 'rgba(99, 102, 241, 0.1)' : 'rgba(99, 102, 241, 0.05)',
            borderColor: isDark ? 'rgba(99, 102, 241, 0.3)' : 'rgba(99, 102, 241, 0.2)',
          }
        ]}>
          <IconSymbol name="info.circle" size={16} color="#6366F1" />
          <Text style={[
            styles.noteText, 
            { color: isDark ? '#8B949E' : '#6B7280' }
          ]}>
            {(t as any)('onboarding.fastMode.note')}
          </Text>
        </View>
      </View>
    </View>
  );

  // 渲染当前步骤
  const renderCurrentStep = () => {
    switch (currentStep) {
      case OnboardingStep.LanguageSelection:
        return renderLanguageSelection();
      case OnboardingStep.Welcome:
        return renderWelcome();
      case OnboardingStep.UseCases:
        return renderUseCases();
      case OnboardingStep.Source:
        return renderSource();
      case OnboardingStep.Templates:
        return renderTemplates();
      case OnboardingStep.FastMode:
        return renderFastMode();
      default:
        return renderLanguageSelection();
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? '#0D1117' : '#F9FAFB' }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      
      {/* 进度指示器 - 语言选择不显示进度 */}
      {currentStep !== OnboardingStep.LanguageSelection && (
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <LinearGradient
              colors={['#6366F1', '#8B5CF6']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={[
                styles.progressFill,
                { width: `${(currentStep / 5) * 100}%` }
              ]}
            />
          </View>
          <Text style={[styles.progressText, { color: isDark ? '#8B949E' : '#6B7280' }]}>
            {currentStep} / 5
          </Text>
        </View>
      )}

      {/* 内容区域 */}
      <View style={styles.content}>
        {renderCurrentStep()}
      </View>

      {/* 底部按钮 - 语言选择步骤不显示导航按钮 */}
      {currentStep !== OnboardingStep.LanguageSelection && (
        <View style={styles.buttonContainer}>
          <View style={styles.mainButtonRow}>
            {currentStep > OnboardingStep.Welcome && (
              <TouchableOpacity
                style={[styles.backButton, { borderColor: isDark ? '#30363D' : '#E5E7EB' }]}
                onPress={handleBack}
              >
                <Text style={[styles.backButtonText, { color: isDark ? '#8B949E' : '#6B7280' }]}>
                  {t("common.back")}
                </Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity
              style={[
                styles.nextButton,
                !canProceed() && styles.disabledButton,
                { flex: currentStep === OnboardingStep.Welcome ? 1 : 1 }
              ]}
              onPress={currentStep === OnboardingStep.FastMode ? handleComplete : handleNext}
              disabled={!canProceed() || loading}
            >
              <LinearGradient
                colors={canProceed() ? ['#6366F1', '#8B5CF6'] : ['#9CA3AF', '#9CA3AF']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.nextButtonGradient}
              >
                <Text style={styles.nextButtonText}>
                  {loading ? t("common.loading") : (currentStep === OnboardingStep.FastMode ? t("onboarding.navigation.complete") : t("onboarding.navigation.next"))}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
          
          {/* 跳过按钮 - 只在欢迎页面显示，放在主按钮下方 */}
          {currentStep === OnboardingStep.Welcome && (
            <TouchableOpacity
              style={styles.skipButtonBottom}
              onPress={handleSkip}
              disabled={loading}
            >
              <Text style={[styles.skipButtonBottomText, { color: isDark ? '#6B7280' : '#9CA3AF' }]}>
                {t('onboarding.navigation.skip') as string}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContainer: {
    flex: 1,
    paddingTop: 20,
  },
  // 语言选择相关样式
  languageContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 0,
    paddingBottom: 0,
    paddingTop: 20,
  },
  languageTitle: {
    fontSize: 22,
    fontWeight: '700',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  languageSubtitle: {
    fontSize: 15,
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  languageOptionsContainer: {
    width: '100%',
    paddingHorizontal: 0,
    gap: 12,
    marginBottom: 20,
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    position: 'relative',
  },
  selectedLanguageOption: {
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
  },
  languageTextContainer: {
    flex: 1,
  },
  languageName: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 3,
  },
  languageEnglishName: {
    fontSize: 13,
    fontWeight: '400',
  },
  languageCheckContainer: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  languageCheckmark: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '700',
  },
  // 语言选择确认按钮样式
  languageButtonContainer: {
    paddingHorizontal: 0,
    paddingBottom: 20,
    paddingTop: 20,
  },
  languageConfirmButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  languageConfirmGradient: {
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    height: 54,
  },
  languageConfirmText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  // 语言切换入口样式
  languageSwitchEntry: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    gap: 6,
    marginTop: 20,
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(99, 102, 241, 0.2)',
  },
  languageSwitchText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // 原有样式
  welcomeContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 60,
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  featureList: {
    alignItems: 'flex-start',
    gap: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureText: {
    fontSize: 16,
    fontWeight: '500',
  },
  welcomeTitle: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 12,
  },
  welcomeSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 16,
  },
  stepSubtitle: {
    fontSize: 16,
    marginBottom: 32,
  },
  optionsContainer: {
    flex: 1,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  selectedOption: {
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  optionText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  templatesContainer: {
    flex: 1,
  },
  categorySection: {
    marginBottom: 24,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  templateGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  templateCard: {
    width: '48%',
    marginBottom: 12,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    minHeight: 100,
  },
  selectedTemplate: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  templateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  templateTitle: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  templateDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  vipBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  vipText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#F59E0B',
    marginLeft: 2,
  },
  buttonContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    paddingTop: 20,
  },
  mainButtonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  backButton: {
    flex: 0.35,
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    height: 54,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  nextButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  nextButtonGradient: {
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    height: 54,
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.5,
  },
  skipButtonBottom: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginTop: 8,
  },
  skipButtonBottomText: {
    fontSize: 14,
    fontWeight: '400',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
  },
  // 新增：模板选择指示器样式
  selectedIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
  },
  // 新增：极速模式设置相关样式
  fastModeContainer: {
    flex: 1,
    paddingTop: 20,
  },
  fastModeCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 2,
    marginBottom: 20,
  },
  fastModeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  fastModeIconAndTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  fastModeIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fastModeCardTitle: {
    fontSize: 17,
    fontWeight: '600',
  },
  modeDescriptionCard: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 2,
    marginBottom: 20,
  },
  modeDescriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  modeDescriptionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  modeDescriptionText: {
    fontSize: 14,
    lineHeight: 20,
  },
  modeAdvantages: {
    marginTop: 12,
    gap: 12,
  },
  advantageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  advantageText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  fastModeNote: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  noteText: {
    fontSize: 13,
    lineHeight: 18,
    flex: 1,
  },
}); 