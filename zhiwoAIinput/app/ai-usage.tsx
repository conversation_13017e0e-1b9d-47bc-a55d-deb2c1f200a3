import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
  Alert,
  TouchableOpacity,
  StyleSheet,
  useColorScheme,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';

import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { getUserAIUsageStats, getTodayUsageStats } from '@/services/secureAiService';
import { error as logError } from '@/services/logService';

// 使用记录接口
interface UsageRecord {
  id: string;
  model: string;
  prompt_tokens: number | null;
  completion_tokens: number | null;
  total_tokens: number | null;
  request_timestamp: string;
  is_vip: boolean;
  cost_estimate?: number | null;
}

// 接口
interface TodayStats {
  totalRequests: number;
  totalTokens: number;
  totalCost: number;
  records: UsageRecord[];
}

// 工具函数
const getColor = (lightColor: string, darkColor: string, isDark: boolean) => {
  return isDark ? darkColor : lightColor;
};

const formatTime = (timestamp: string) => {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatCost = (cost: number | null | undefined) => {
  return `$${(cost || 0).toFixed(4)}`;
};

const getModelDisplayName = (model: string) => {
  const modelNames: { [key: string]: string } = {
    'Qwen/Qwen2.5-7B-Instruct': 'Qwen2.5-7B',
    'deepseek-ai/DeepSeek-V3': 'DeepSeek-V3'
  };
  return modelNames[model] || model;
};

// 通用的主题文本组件
const ThemedStyledText = ({ style, color, children }: { 
  style?: any, 
  color?: [string, string], 
  children: React.ReactNode 
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  return (
    <Text style={[style, color && { color: getColor(color[0], color[1], isDark) }]}>
      {children}
    </Text>
  );
};

// 统计卡片组件
const StatCard = ({ title, value, subtitle, icon }: {
  title: string;
  value: string;
  subtitle?: string;
  icon: string;
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  return (
    <View style={[
      styles.statCard,
      { 
        backgroundColor: getColor('#F9FAFB', '#374151', isDark),
        borderColor: getColor('#E5E7EB', '#4B5563', isDark),
      }
    ]}>
      <View style={styles.statHeader}>
        <IconSymbol 
          name={icon as any} 
          size={24} 
          color={getColor('#3B82F6', '#60A5FA', isDark)} 
        />
        <ThemedStyledText 
          style={styles.statTitle} 
          color={['#6B7280', '#9CA3AF']}
        >
          {title}
        </ThemedStyledText>
      </View>
      <ThemedStyledText 
        style={styles.statValue} 
        color={['#1F2937', '#ECEDEE']}
      >
        {value}
      </ThemedStyledText>
      {subtitle && (
        <ThemedStyledText 
          style={styles.statSubtitle} 
          color={['#6B7280', '#9CA3AF']}
        >
          {subtitle}
        </ThemedStyledText>
      )}
    </View>
  );
};

// 使用记录项组件
const UsageItem = ({ record }: { record: UsageRecord }) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <View style={[
      styles.usageItem,
      { 
        backgroundColor: getColor('#FFFFFF', '#374151', isDark),
        borderColor: getColor('#E5E7EB', '#4B5563', isDark),
      }
    ]}>
      <View style={styles.usageHeader}>
        <View style={styles.usageInfo}>
          <ThemedStyledText 
            style={styles.modelName} 
            color={['#1F2937', '#ECEDEE']}
          >
            {getModelDisplayName(record.model)}
          </ThemedStyledText>
          {record.is_vip && (
            <View style={styles.vipTag}>
              <Text style={styles.vipText}>VIP</Text>
            </View>
          )}
        </View>
        <ThemedStyledText 
          style={styles.usageTime} 
          color={['#6B7280', '#9CA3AF']}
        >
          {formatTime(record.request_timestamp)}
        </ThemedStyledText>
      </View>
      
      <View style={styles.usageDetails}>
        <View style={styles.tokenInfo}>
          <View style={styles.tokenRow}>
            <ThemedStyledText 
              style={styles.tokenLabel} 
              color={['#6B7280', '#9CA3AF']}
            >
              Tokens:
            </ThemedStyledText>
            <ThemedStyledText 
              style={styles.tokenValue} 
              color={['#1F2937', '#ECEDEE']}
            >
              {(record.total_tokens || 0).toLocaleString()}
            </ThemedStyledText>
          </View>
        </View>
        {record.cost_estimate != null && (
          <View style={styles.costInfo}>
            <View style={styles.costRow}>
              <ThemedStyledText 
                style={styles.costLabel} 
                color={['#6B7280', '#9CA3AF']}
              >
                成本:
              </ThemedStyledText>
              <Text style={styles.costValue}>
                {formatCost(record.cost_estimate)}
              </Text>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

// 主屏幕组件
export default function AIUsageScreen() {
  const { t } = useTranslation();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [todayStats, setTodayStats] = useState<TodayStats | null>(null);
  const [recentUsage, setRecentUsage] = useState<UsageRecord[]>([]);

  // 加载使用统计
  const loadUsageStats = async () => {
    try {
      // 并行获取今日统计和最近使用记录
      const [todayData, recentData] = await Promise.all([
        getTodayUsageStats(),
        getUserAIUsageStats(50)
      ]);

      setTodayStats(todayData);
      setRecentUsage(recentData || []);
    } catch (error) {
      logError('加载使用统计失败:', error);
      Alert.alert('加载失败', '无法获取使用统计数据');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    setRefreshing(true);
    loadUsageStats();
  };

  useEffect(() => {
    loadUsageStats();
  }, []);

  if (loading) {
    return (
      <SafeAreaView style={[
        styles.container,
        { backgroundColor: getColor('#FFFFFF', '#151718', isDark) }
      ]}>
        <StatusBar style={isDark ? "light" : "dark"} />
        
        <View style={[
          styles.header,
          { 
            backgroundColor: getColor('#FFFFFF', '#151718', isDark),
            borderBottomColor: getColor('#E5E7EB', '#374151', isDark),
          }
        ]}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <IconSymbol name="chevron.left" size={24} color={getColor('#1F2937', '#ECEDEE', isDark)} />
          </TouchableOpacity>
          <ThemedStyledText 
            style={styles.headerTitle} 
            color={['#1F2937', '#ECEDEE']}
          >
            AI 使用统计
          </ThemedStyledText>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <ThemedStyledText 
            style={styles.loadingText} 
            color={['#6B7280', '#9CA3AF']}
          >
            加载中...
          </ThemedStyledText>
        </View>
      </SafeAreaView>
    );
  }

  // 限制显示的记录数量
  const displayedRecords = recentUsage.slice(0, 20);
  const hasMoreRecords = recentUsage.length > 20;

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: getColor('#FFFFFF', '#151718', isDark) }
    ]}>
      <StatusBar style={isDark ? "light" : "dark"} />
      
      <View style={[
        styles.header,
        { 
          backgroundColor: getColor('#FFFFFF', '#151718', isDark),
          borderBottomColor: getColor('#E5E7EB', '#374151', isDark),
        }
      ]}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <IconSymbol name="chevron.left" size={24} color={getColor('#1F2937', '#ECEDEE', isDark)} />
        </TouchableOpacity>
        <ThemedStyledText 
          style={styles.headerTitle} 
          color={['#1F2937', '#ECEDEE']}
        >
          AI 使用统计
        </ThemedStyledText>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* 今日统计 */}
        <View style={styles.section}>
          <ThemedStyledText 
            style={styles.sectionTitle} 
            color={['#1F2937', '#ECEDEE']}
          >
            今日统计
          </ThemedStyledText>
          
          <View style={styles.statsGrid}>
            <StatCard
              title="请求次数"
              value={todayStats?.totalRequests.toString() || '0'}
              icon="bolt.fill"
            />
            <StatCard
              title="总 Tokens"
              value={(todayStats?.totalTokens || 0).toLocaleString()}
              icon="doc.text.fill"
            />
          </View>
          
          <View style={styles.statsGrid}>
            <StatCard
              title="估算成本"
              value={todayStats ? formatCost(todayStats.totalCost) : '$0.0000'}
              subtitle="美元"
              icon="creditcard.fill"
            />
            <StatCard
              title="平均每次"
              value={todayStats && todayStats.totalRequests > 0 
                ? (todayStats.totalTokens / todayStats.totalRequests).toFixed(0)
                : '0'
              }
              subtitle="Tokens"
              icon="chart.bar.fill"
            />
          </View>
        </View>

        {/* 最近使用记录 */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <ThemedStyledText 
              style={styles.sectionTitle} 
              color={['#1F2937', '#ECEDEE']}
            >
              最近使用记录
            </ThemedStyledText>
            {hasMoreRecords && (
              <TouchableOpacity 
                style={styles.viewAllButton}
                onPress={() => router.push('/ai-usage-detail')}
              >
                <Text style={styles.viewAllText}>查看全部</Text>
                <IconSymbol name="chevron.right" size={16} color="#3B82F6" />
              </TouchableOpacity>
            )}
          </View>
          
          {displayedRecords.length === 0 ? (
            <View style={styles.emptyContainer}>
              <IconSymbol 
                name="doc.text" 
                size={48} 
                color={getColor('#9CA3AF', '#6B7280', isDark)} 
              />
              <ThemedStyledText 
                style={styles.emptyText} 
                color={['#6B7280', '#9CA3AF']}
              >
                暂无使用记录
              </ThemedStyledText>
            </View>
          ) : (
            <View style={styles.usageList}>
              {displayedRecords.map((record) => (
                <UsageItem key={record.id} record={record} />
              ))}
            </View>
          )}
        </View>

        {/* 使用提示 */}
        <View style={styles.tipsSection}>
          <Text style={styles.tipsTitle}>💡 使用提示</Text>
          <View style={styles.tipsContent}>
            <Text style={styles.tipsText}>• 免费用户每小时限制 10 次请求</Text>
            <Text style={styles.tipsText}>• VIP 用户每小时限制 100 次请求</Text>
            <Text style={styles.tipsText}>• 成本估算基于 SiliconFlow 定价</Text>
            <Text style={styles.tipsText}>• 所有请求都经过安全验证和加密</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500',
    marginRight: 4,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
    borderWidth: 1,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statTitle: {
    fontSize: 14,
    marginLeft: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
  },
  statSubtitle: {
    fontSize: 12,
    marginTop: 2,
  },
  usageList: {
    marginBottom: 8,
  },
  usageItem: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  usageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  usageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modelName: {
    fontSize: 16,
    fontWeight: '600',
  },
  vipTag: {
    backgroundColor: '#10B981',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  vipText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  usageTime: {
    fontSize: 12,
  },
  usageDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  tokenInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tokenRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tokenLabel: {
    fontSize: 14,
  },
  tokenValue: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  costInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  costRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  costLabel: {
    fontSize: 14,
  },
  costValue: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
    color: '#10B981',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 12,
  },
  tipsSection: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#FEF3C7',
    marginTop: 8,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#92400E',
    marginBottom: 8,
  },
  tipsContent: {
    marginTop: 4,
  },
  tipsText: {
    fontSize: 14,
    color: '#92400E',
    lineHeight: 20,
    marginBottom: 4,
  },
}); 