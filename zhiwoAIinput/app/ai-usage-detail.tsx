import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
  Alert,
  TouchableOpacity,
  StyleSheet,
  useColorScheme,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';

import { IconSymbol } from '@/components/ui/IconSymbol';
import { getUserAIUsageStats } from '@/services/secureAiService';
import { error as logError } from '@/services/logService';

// 使用记录接口
interface UsageRecord {
  id: string;
  model: string;
  prompt_tokens: number | null;
  completion_tokens: number | null;
  total_tokens: number | null;
  request_timestamp: string;
  is_vip: boolean;
  cost_estimate?: number | null;
}

export default function AIUsageDetailScreen() {
  const { t } = useTranslation();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [allUsage, setAllUsage] = useState<UsageRecord[]>([]);

  // 获取主题颜色
  const getColor = (lightColor: string, darkColor: string) => {
    return isDark ? darkColor : lightColor;
  };

  // 加载所有使用记录
  const loadAllUsageStats = async () => {
    try {
      const data = await getUserAIUsageStats(1000); // 获取更多记录
      setAllUsage(data || []);
    } catch (error) {
      logError('加载使用记录失败:', error);
      Alert.alert('加载失败', '无法获取使用记录数据');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    setRefreshing(true);
    loadAllUsageStats();
  };

  useEffect(() => {
    loadAllUsageStats();
  }, []);

  // 格式化时间
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 格式化成本
  const formatCost = (cost: number | null | undefined) => {
    return `$${(cost || 0).toFixed(4)}`;
  };

  // 获取模型显示名称
  const getModelDisplayName = (model: string) => {
    const modelNames: { [key: string]: string } = {
      'Qwen/Qwen2.5-7B-Instruct': 'Qwen2.5-7B',
      'deepseek-ai/DeepSeek-V3': 'DeepSeek-V3'
    };
    return modelNames[model] || model;
  };

  // 抽取通用的文本样式组件
  const ThemedText = ({ style, color, children }: { 
    style?: any, 
    color?: [string, string], // 修改为固定长度的元组类型
    children: React.ReactNode 
  }) => (
    <Text style={[style, color && { color: getColor(color[0], color[1]) }]}>
      {children}
    </Text>
  );

  // 使用记录项组件
  const UsageItem = ({ record }: { record: UsageRecord }) => {
    const TokenRow = ({ label, value }: { label: string, value: number | null }) => (
      <View style={styles.tokenRow}>
        <ThemedText style={styles.tokenLabel} color={['#6B7280', '#9CA3AF']}>
          {label}
        </ThemedText>
        <ThemedText style={styles.tokenValue} color={['#1F2937', '#ECEDEE']}>
          {(value || 0).toLocaleString()}
        </ThemedText>
      </View>
    );

    return (
      <View style={[
        styles.usageItem,
        { 
          backgroundColor: getColor('#FFFFFF', '#374151'),
          borderColor: getColor('#E5E7EB', '#4B5563'),
        }
      ]}>
        {/* 头部信息 */}
        <View style={styles.usageHeader}>
          <View style={styles.usageInfo}>
            <ThemedText style={styles.modelName} color={['#1F2937', '#ECEDEE']}>
              {getModelDisplayName(record.model)}
            </ThemedText>
            {record.is_vip && (
              <View style={styles.vipTag}>
                <Text style={styles.vipText}>VIP</Text>
              </View>
            )}
          </View>
          <ThemedText style={styles.usageTime} color={['#6B7280', '#9CA3AF']}>
            {formatTime(record.request_timestamp)}
          </ThemedText>
        </View>
        
        {/* Token 使用详情 */}
        <View style={styles.usageDetails}>
          <View style={styles.tokenInfo}>
            <TokenRow label="输入:" value={record.prompt_tokens} />
            <TokenRow label="输出:" value={record.completion_tokens} />
            <TokenRow label="总计:" value={record.total_tokens} />
          </View>

          {/* 成本信息 */}
          {record.cost_estimate != null && (
            <View style={styles.costInfo}>
              <View style={styles.costRow}>
                <ThemedText style={styles.costLabel} color={['#6B7280', '#9CA3AF']}>
                  成本:
                </ThemedText>
                <Text style={styles.costValue}>
                  {formatCost(record.cost_estimate)}
                </Text>
              </View>
            </View>
          )}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[
        styles.container,
        { backgroundColor: getColor('#FFFFFF', '#151718') }
      ]}>
        <StatusBar style={isDark ? "light" : "dark"} />
        
        {/* 导航栏 */}
        <View style={[
          styles.header,
          { 
            backgroundColor: getColor('#FFFFFF', '#151718'),
            borderBottomColor: getColor('#E5E7EB', '#374151'),
          }
        ]}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <IconSymbol name="chevron.left" size={24} color={getColor('#1F2937', '#ECEDEE')} />
          </TouchableOpacity>
          <Text style={[
            styles.headerTitle,
            { color: getColor('#1F2937', '#ECEDEE') }
          ]}>请求明细</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text style={[
            styles.loadingText,
            { color: getColor('#6B7280', '#9CA3AF') }
          ]}>加载中...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[
      styles.container,
      { backgroundColor: getColor('#FFFFFF', '#151718') }
    ]}>
      <StatusBar style={isDark ? "light" : "dark"} />
      
      {/* 导航栏 */}
      <View style={[
        styles.header,
        { 
          backgroundColor: getColor('#FFFFFF', '#151718'),
          borderBottomColor: getColor('#E5E7EB', '#374151'),
        }
      ]}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <IconSymbol name="chevron.left" size={24} color={getColor('#1F2937', '#ECEDEE')} />
        </TouchableOpacity>
        <Text style={[
          styles.headerTitle,
          { color: getColor('#1F2937', '#ECEDEE') }
        ]}>请求明细</Text>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* 统计信息 */}
        <View style={styles.statsSection}>
          <Text style={[
            styles.statsText,
            { color: getColor('#6B7280', '#9CA3AF') }
          ]}>
            共 {allUsage.length} 条记录
          </Text>
        </View>

        {/* 所有使用记录 */}
        <View style={styles.section}>
          {allUsage.length === 0 ? (
            <View style={styles.emptyContainer}>
              <IconSymbol 
                name="doc.text" 
                size={48} 
                color={getColor('#9CA3AF', '#6B7280')} 
              />
              <Text style={[
                styles.emptyText,
                { color: getColor('#6B7280', '#9CA3AF') }
              ]}>暂无使用记录</Text>
            </View>
          ) : (
            <View style={styles.usageList}>
              {allUsage.map((record) => (
                <UsageItem key={record.id} record={record} />
              ))}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  statsSection: {
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  statsText: {
    fontSize: 14,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  usageList: {
    marginBottom: 8,
  },
  usageItem: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  usageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  usageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modelName: {
    fontSize: 16,
    fontWeight: '600',
  },
  vipTag: {
    backgroundColor: '#10B981',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  vipText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  usageTime: {
    fontSize: 12,
  },
  usageDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  tokenInfo: {
    flex: 1,
    marginBottom: 4,
  },
  tokenRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  tokenLabel: {
    fontSize: 12,
    marginRight: 4,
  },
  tokenValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  costInfo: {
    alignItems: 'flex-end',
  },
  costRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  costLabel: {
    fontSize: 12,
    marginRight: 4,
  },
  costValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#10B981',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 12,
  },
}); 