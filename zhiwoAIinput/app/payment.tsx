/**
 * 支付墙页面
 * 以Modal形式展示支付订阅选项
 */
import React, { useEffect } from 'react';
import { View, StatusBar } from 'react-native';
import PaymentWall from '@/components/PaymentWall';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { useAppTheme } from '@/hooks/useAppTheme';
import { SUBSCRIPTION_TYPE } from '@/services/purchaseService';
import { log } from '@/services/logService';

export default function PaymentScreen() {
  const isDark = useAppTheme() === 'dark';
  const { onSuccess, switchPlan, planType } = useLocalSearchParams<{ 
    onSuccess: string, 
    switchPlan: string,
    planType: string
  }>();
  
  // 确定默认选择的套餐类型
  const defaultSubscriptionType = planType === 'yearly' 
    ? SUBSCRIPTION_TYPE.YEARLY 
    : planType === 'monthly' 
      ? SUBSCRIPTION_TYPE.MONTHLY 
      : undefined;

  // 记录参数信息
  useEffect(() => {
    log('[PaymentScreen] 接收到参数:', { 
      onSuccess, 
      switchPlan, 
      planType,
      defaultSubscriptionType
    });
  }, [onSuccess, switchPlan, planType, defaultSubscriptionType]);

  // 处理成功订阅回调
  const handleSuccess = () => {
    if (onSuccess === 'true') {
      // 如果需要触发成功回调，可以在这里处理
      // 例如更新全局状态等
    }
    router.back();
  };
  
  // 关闭页面
  const handleClose = () => {
    router.back();
  };
  
  return (
    <View style={{ flex: 1 }}>
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
      
      <Stack.Screen
        options={{
          title: '会员订阅',
          headerShown: false,
          presentation: 'modal',
          animation: 'slide_from_bottom',
          animationDuration: 300,
          gestureEnabled: true,
          gestureDirection: 'vertical',
        }}
      />
      
      <PaymentWall
        visible={true}
        onClose={handleClose}
        onSuccess={handleSuccess}
        initialSubscriptionType={defaultSubscriptionType}
        switchPlan={switchPlan === 'true'}
      />
    </View>
  );
} 