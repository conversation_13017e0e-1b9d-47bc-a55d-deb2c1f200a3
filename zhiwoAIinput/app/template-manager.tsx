/**
 * 模板管理页面
 * 用户可以自定义模板的显示/隐藏和排序
 * 采用矩阵化排列，所见即所得的设计
 */

import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions,
  Animated,
  ScrollView,
} from "react-native";
import { PanGestureHandler, State } from "react-native-gesture-handler";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useFocusEffect } from "@react-navigation/native";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { loadTemplates } from "@/store/slices/templateSlice";

// 服务导入
import { getAllTemplates, Template } from "@/services/templateService";
import {
  getUserTemplatePreferences,
  updateTemplateDisplayOrder,
  hideTemplate,
  showTemplate,
  LocalTemplatePreference,
} from "@/services/templatePreferenceService";
import {
  getTemplateDisplayName,
  getTemplateDisplayDescription,
} from "@/utils/templateI18nUtils";

// 主题相关导入
import { useAppTheme } from "@/hooks/useAppTheme";
import { log, warn, error as logError } from "@/services/logService";
import { ThemedSafeAreaView } from "@/components/ThemedSafeAreaView";
import { IconSymbol } from "@/components/ui/IconSymbol";

// 扩展模板接口，包含偏好设置
interface TemplateWithPreference extends Template {
  isVisible: boolean;
  displayOrder: number;
  canHide: boolean; // 某些模板可能不允许隐藏（如默认模板）
}

const { width: screenWidth } = Dimensions.get("window");
const PADDING = 20;
const ITEM_MARGIN = 12;
const ITEMS_PER_ROW = 3;
const ITEM_WIDTH =
  (screenWidth - PADDING * 2 - ITEM_MARGIN * (ITEMS_PER_ROW - 1)) /
  ITEMS_PER_ROW;
const ITEM_HEIGHT = 140;

export default function TemplateManagerScreen() {
  // React Hooks必须在组件函数的顶层调用
  const { t } = useTranslation() as any;
  const currentTheme = useAppTheme();
  const isDark = currentTheme === "dark";
  const dispatch = useDispatch<AppDispatch>();
  const [templates, setTemplates] = useState<TemplateWithPreference[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  // ScrollView引用
  const scrollViewRef = useRef<ScrollView>(null);

  // 拖拽相关的动画值
  const dragX = useRef(new Animated.Value(0)).current;
  const dragY = useRef(new Animated.Value(0)).current;
  const dragScale = useRef(new Animated.Value(1)).current;
  const dragOpacity = useRef(new Animated.Value(1)).current;

  // 获取完整的模板列表（包括隐藏的）
  const getAllTemplatesWithHidden = async (): Promise<
    TemplateWithPreference[]
  > => {
    try {
      // 强制刷新模板数据，确保获取到最新的云端模板
      // 注意：不使用forceRefreshTemplates，因为它会重新初始化偏好设置，可能覆盖用户设置
      try {
        const { syncSystemTemplates } = await import(
          "@/services/templateSyncService"
        );
        await syncSystemTemplates(true); // 强制同步系统模板
        log("[TemplateManager] 强制同步系统模板完成");
      } catch (refreshError) {
        warn(
          "[TemplateManager] 强制同步模板失败，继续使用现有数据:",
          refreshError
        );
      }

      // 获取用户模板
      const userTemplates = await import("@/services/templateService").then(
        (m) => m.getUserTemplates()
      );

      // 获取系统模板
      let syncedSystemTemplates: Template[] = [];
      try {
        const { syncSystemTemplates } = await import(
          "@/services/templateSyncService"
        );
        const cloudTemplates = await syncSystemTemplates(true); // 强制同步
        syncedSystemTemplates = cloudTemplates.map((ct) => ({
          id: ct.id,
          title: ct.title,
          description: ct.description,
          name_key: ct.name_key,
          description_key: ct.description_key,
          prompt: ct.prompt,
          isSystem: ct.isSystem,
          color: ct.color,
          borderColor: ct.borderColor,
          backgroundColor: ct.backgroundColor,
          category: ct.category,
          isVipOnly: ct.isVipOnly,
        }));
      } catch (syncError) {
        warn("[TemplateManager] 同步系统模板失败，使用空数组:", syncError);
        syncedSystemTemplates = [];
      }

      // 合并所有模板
      const allTemplates = [...syncedSystemTemplates, ...userTemplates];

      // 获取默认模板ID
      const defaultTemplateId = await import("@/services/templateService").then(
        (m) => m.getDefaultTemplateId()
      );

      // 获取用户偏好设置
      const preferences = await getUserTemplatePreferences();
      const preferenceMap = new Map(preferences.map((p) => [p.templateId, p]));

      // 构建带偏好设置的模板列表
      const templatesWithPrefs: TemplateWithPreference[] = allTemplates.map(
        (template, index) => {
          const preference = preferenceMap.get(template.id);
          const isDefault = template.id === defaultTemplateId;
          return {
            ...template,
            isDefault,
            isVisible: !preference?.isHidden, // 默认可见，除非偏好设置中隐藏
            displayOrder: isDefault ? 0 : preference?.displayOrder ?? index, // 默认模板强制设为0，其他使用偏好设置或默认索引
            canHide: !isDefault, // 非默认模板可以隐藏
          };
        }
      );

      // 添加仅有偏好记录的已删除模板（显示为占位符）
      preferences.forEach((pref) => {
        if (pref.isHidden) {
          const existsInTemplates = allTemplates.some(
            (t) => t.id === pref.templateId
          );
          if (!existsInTemplates) {
            // 这是一个已删除但有偏好记录的模板，创建占位符
            templatesWithPrefs.push({
              id: pref.templateId,
              title: t("settings.templateManagerDeletedTemplate"),
              description: t("settings.templateManagerDeletedTemplateDesc"),
              prompt: "",
              isSystem: pref.templateType === "system",
              color: isDark ? "#6B7280" : "#9CA3AF",
              borderColor: isDark ? "#6B7280" : "#9CA3AF",
              backgroundColor: isDark ? "#374151" : "#F3F4F6",
              isVisible: false,
              displayOrder: pref.displayOrder,
              canHide: true,
            });
          }
        }
      });

      // 按显示顺序排序，但确保默认模板始终在第一位
      templatesWithPrefs.sort((a, b) => {
        // 默认模板始终排在第一位
        if (a.isDefault && !b.isDefault) return -1;
        if (!a.isDefault && b.isDefault) return 1;

        // 如果都是默认模板或都不是默认模板，按displayOrder排序
        return a.displayOrder - b.displayOrder;
      });

      return templatesWithPrefs;
    } catch (error) {
      logError("[TemplateManager] 获取完整模板列表失败:", error);
      throw error;
    }
  };

  // 加载模板数据
  const loadTemplateData = useCallback(async () => {
    try {
      setLoading(true);
      const templatesWithPrefs = await getAllTemplatesWithHidden();
      setTemplates(templatesWithPrefs);
    } catch (error) {
      logError("[TemplateManager] 加载模板数据失败:", error);
      Alert.alert(t("common.error"), t("settings.templateManagerLoadError"));
    } finally {
      setLoading(false);
    }
  }, [t, isDark]);

  // 页面聚焦时重新加载数据
  useFocusEffect(
    useCallback(() => {
      loadTemplateData();
    }, [loadTemplateData])
  );

  // 切换模板可见性
  const toggleTemplateVisibility = async (
    templateId: string,
    templateType: "system" | "user",
    currentlyVisible: boolean
  ) => {
    try {
      setSaving(true);

      if (currentlyVisible) {
        // 当前可见，隐藏它
        await hideTemplate(templateId, templateType);
      } else {
        // 当前隐藏，显示它
        await showTemplate(templateId, templateType);
      }

      // 更新本地状态
      setTemplates((prev) =>
        prev.map((template) =>
          template.id === templateId
            ? { ...template, isVisible: !currentlyVisible }
            : template
        )
      );

      // 刷新Redux中的模板数据，确保input页面同步更新
      log("[TemplateManager] 隐藏/显示模板操作完成，刷新Redux数据");
      dispatch(loadTemplates());
    } catch (error) {
      logError("[TemplateManager] 切换模板可见性失败:", error);
      Alert.alert(t("common.error"), t("settings.templateManagerSaveError"));
    } finally {
      setSaving(false);
    }
  };

  // 重置拖拽状态的辅助函数
  const resetDragState = useCallback(() => {
    log("[拖拽] 重置拖拽状态");
    setIsDragging(false);
    setDraggedIndex(null);

    // 重置所有动画值
    Animated.parallel([
      Animated.timing(dragX, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(dragY, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(dragScale, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(dragOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  }, [dragX, dragY, dragScale, dragOpacity]);

  // 长按开始拖拽
  const handleLongPress = (index: number) => {
    if (saving || isDragging) return;

    log("[拖拽] 长按开始拖拽，索引:", index);
    setDraggedIndex(index);
    setIsDragging(true);

    // 开始拖拽动画
    Animated.parallel([
      Animated.timing(dragScale, {
        toValue: 1.1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(dragOpacity, {
        toValue: 0.8,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // 拖拽手势处理
  const onGestureEvent = (event: any) => {
    const { translationX, translationY } = event.nativeEvent;

    // 添加调试日志
    if (isDragging && draggedIndex !== null) {
      log("[拖拽] 手势事件:", { translationX, translationY, draggedIndex });
      // 直接设置动画值，让卡片跟随手指移动
      dragX.setValue(translationX);
      dragY.setValue(translationY);
    } else {
      log(
        "[拖拽] 忽略手势事件，isDragging:",
        isDragging,
        "draggedIndex:",
        draggedIndex
      );
    }
  };

  // 计算拖拽目标位置
  const calculateTargetIndex = (
    translationX: number,
    translationY: number,
    currentIndex: number
  ): number => {
    // 计算移动了多少行和列
    const rowHeight = ITEM_HEIGHT + ITEM_MARGIN;
    const colWidth = ITEM_WIDTH + ITEM_MARGIN;

    const rowOffset = Math.round(translationY / rowHeight);
    const colOffset = Math.round(translationX / colWidth);

    // 计算当前位置
    const currentRow = Math.floor(currentIndex / ITEMS_PER_ROW);
    const currentCol = currentIndex % ITEMS_PER_ROW;

    // 计算目标位置
    const targetRow = Math.max(0, currentRow + rowOffset);
    const targetCol = Math.max(
      0,
      Math.min(ITEMS_PER_ROW - 1, currentCol + colOffset)
    );
    const targetIndex = targetRow * ITEMS_PER_ROW + targetCol;

    // 确保目标索引在有效范围内
    return Math.max(0, Math.min(templates.length - 1, targetIndex));
  };

  // 拖拽结束
  const handleDragEnd = async (event: any) => {
    if (!isDragging || draggedIndex === null) return;

    const { translationX = 0, translationY = 0, state } = event.nativeEvent;
    log("[拖拽] 拖拽结束:", {
      translationX,
      translationY,
      state,
      draggedIndex,
    });

    // 保存当前拖拽索引
    const currentDraggedIndex = draggedIndex;

    // 立即重置拖拽状态，避免状态残留
    resetDragState();

    // 只有在确实移动了位置且手势正常结束时才执行重排序
    if (
      state === State.END &&
      (Math.abs(translationX) > 10 || Math.abs(translationY) > 10)
    ) {
      const targetIndex = calculateTargetIndex(
        translationX,
        translationY,
        currentDraggedIndex
      );
      log("[拖拽] 目标索引:", targetIndex, "当前索引:", currentDraggedIndex);

      if (targetIndex !== currentDraggedIndex) {
        await handleReorder(currentDraggedIndex, targetIndex);
      }
    } else {
      log("[拖拽] 取消或未移动，不执行重排序");
    }
  };

  // 处理拖拽排序
  const handleReorder = async (fromIndex: number, toIndex: number) => {
    if (fromIndex === toIndex) return;

    // 检查是否尝试移动默认模板
    const movedTemplate = templates[fromIndex];
    if (movedTemplate.isDefault) {
      Alert.alert(
        t("settings.templateManagerDefaultNotMovable"),
        t("settings.templateManagerDefaultNotMovableMessage")
      );
      return;
    }

    // 检查是否尝试移动到默认模板的位置（索引0）
    if (toIndex === 0) {
      Alert.alert(
        t("settings.templateManagerCannotReplaceDefault"),
        t("settings.templateManagerCannotReplaceDefaultMessage")
      );
      return;
    }

    try {
      setSaving(true);

      // 克隆数组并移动元素
      const newTemplates = [...templates];
      const [movedItem] = newTemplates.splice(fromIndex, 1);
      newTemplates.splice(toIndex, 0, movedItem);

      // 更新本地状态
      setTemplates(newTemplates);

      // 批量更新所有模板的显示顺序
      const templateOrder = newTemplates.map((template) => ({
        templateId: template.id,
        templateType: template.isSystem
          ? ("system" as const)
          : ("user" as const),
      }));

      await updateTemplateDisplayOrder(templateOrder);

      // 刷新Redux中的模板数据，确保input页面同步更新
      log("[TemplateManager] 排序操作完成，刷新Redux数据");
      dispatch(loadTemplates());
    } catch (error) {
      logError("[TemplateManager] 保存排序失败:", error);
      Alert.alert(t("common.error"), t("settings.templateManagerSaveError"));
      // 失败时重新加载数据
      loadTemplateData();
    } finally {
      setSaving(false);
    }
  };

  // 模拟拖拽移动（上移/下移）
  const moveTemplate = async (index: number, direction: "up" | "down") => {
    const template = templates[index];

    // 检查是否尝试移动默认模板
    if (template.isDefault) {
      Alert.alert(
        t("settings.templateManagerDefaultNotMovable"),
        t("settings.templateManagerDefaultNotMovableMessage")
      );
      return;
    }

    const newIndex = direction === "up" ? index - 1 : index + 1;

    // 检查是否尝试移动到默认模板的位置（索引0）
    if (newIndex === 0) {
      Alert.alert(
        t("settings.templateManagerCannotReplaceDefault"),
        t("settings.templateManagerCannotReplaceDefaultMessage")
      );
      return;
    }

    if (newIndex >= 0 && newIndex < templates.length) {
      await handleReorder(index, newIndex);
    }
  };

  // 渲染单个模板项
  const renderTemplateItem = (item: TemplateWithPreference, index: number) => {
    const isBeingDragged = draggedIndex === index;
    const isLastInRow = (index + 1) % ITEMS_PER_ROW === 0;

    return (
      <PanGestureHandler
        key={item.id}
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={(event) => {
          const { state } = event.nativeEvent;
          if (state === State.BEGAN && !isDragging) {
            // 如果手势开始但还没有进入拖拽状态，忽略
            return;
          }
          // 处理所有可能的结束状态：END, CANCELLED, FAILED
          if (
            state === State.END ||
            state === State.CANCELLED ||
            state === State.FAILED
          ) {
            handleDragEnd(event);
          }
        }}
        enabled={!saving}
        shouldCancelWhenOutside={false}
        activeOffsetX={
          isDragging && draggedIndex === index ? [-5, 5] : [-30, 30]
        }
        activeOffsetY={
          isDragging && draggedIndex === index ? [-5, 5] : [-30, 30]
        }
      >
        <Animated.View
          style={[
            styles.templateGridItem,
            { width: ITEM_WIDTH },
            !isLastInRow && { marginRight: ITEM_MARGIN },
            isBeingDragged && {
              transform: [
                { translateX: dragX },
                { translateY: dragY },
                { scale: dragScale },
              ],
              opacity: dragOpacity,
              zIndex: 1000,
              elevation: 10,
            },
          ]}
        >
          <TouchableOpacity
            onLongPress={() => handleLongPress(index)}
            disabled={saving}
            activeOpacity={isDragging && draggedIndex === index ? 1 : 0.8}
            delayLongPress={300}
            style={[
              styles.templateCard,
              {
                backgroundColor:
                  item.backgroundColor || (isDark ? "#21262D" : "#F3F4F6"),
                borderColor:
                  item.borderColor || (isDark ? "#30363D" : "#E5E7EB"),
                opacity: item.isVisible ? 1 : 0.5,
              },
            ]}
          >
            {/* 模板内容 */}
            <View style={styles.templateContent}>
              {/* 模板标题 */}
              <Text
                style={[
                  styles.templateTitle,
                  {
                    color: item.isVisible
                      ? item.color || (isDark ? "#F0F6FC" : "#374151")
                      : isDark
                      ? "#7D8590"
                      : "#9CA3AF",
                  },
                ]}
                numberOfLines={2}
              >
                {(() => {
                  // 将Template转换为StyleTemplateFromDB格式以使用多语言工具函数
                  const templateForI18n = {
                    id: item.id,
                    name: item.title,
                    description: item.description,
                    name_key: item.name_key,
                    description_key: item.description_key,
                    prompt_text: item.prompt,
                    is_system: item.isSystem,
                  };
                  return getTemplateDisplayName(templateForI18n, t);
                })()}
              </Text>

              {/* 模板描述 */}
              <Text
                style={[
                  styles.templateDescription,
                  { color: isDark ? "#8B949E" : "#6B7280" },
                ]}
                numberOfLines={2}
              >
                {(() => {
                  // 将Template转换为StyleTemplateFromDB格式以使用多语言工具函数
                  const templateForI18n = {
                    id: item.id,
                    name: item.title,
                    description: item.description,
                    name_key: item.name_key,
                    description_key: item.description_key,
                    prompt_text: item.prompt,
                    is_system: item.isSystem,
                  };
                  return getTemplateDisplayDescription(templateForI18n, t);
                })()}
              </Text>

              {/* 模板标签 - 只显示默认标签 */}
              <View style={styles.templateTags}>
                {item.isDefault && (
                  <View
                    style={[
                      styles.defaultTag,
                      { backgroundColor: isDark ? "#2EA04320" : "#D1FAE5" },
                    ]}
                  >
                    <Text
                      style={[
                        styles.defaultTagText,
                        { color: isDark ? "#56D364" : "#065F46" },
                      ]}
                    >
                      {t("settings.templateManagerDefaultTag")}
                    </Text>
                  </View>
                )}
              </View>
            </View>

            {/* 右上角关闭按钮 */}
            {item.canHide && (
              <TouchableOpacity
                style={[
                  styles.hideButton,
                  {
                    backgroundColor: "transparent",
                    borderWidth: 1,
                    borderColor: item.isVisible
                      ? isDark
                        ? "#F85149"
                        : "#EF4444"
                      : isDark
                      ? "#56D364"
                      : "#10B981",
                    width: 16,
                    height: 16,
                    borderRadius: 12,
                    alignItems: "center",
                    justifyContent: "center",
                  },
                  item.isVisible
                    ? styles.hideButtonVisible
                    : styles.hideButtonHidden,
                ]}
                onPress={() =>
                  toggleTemplateVisibility(
                    item.id,
                    item.isSystem ? "system" : "user",
                    item.isVisible
                  )
                }
                disabled={saving}
                hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
              >
                <Ionicons
                  name={item.isVisible ? "close" : "add"}
                  size={12}
                  color={
                    item.isVisible
                      ? isDark
                        ? "#F85149"
                        : "#EF4444"
                      : isDark
                      ? "#56D364"
                      : "#10B981"
                  }
                />
              </TouchableOpacity>
            )}

            {/* 拖拽控制按钮 */}
            <View style={styles.dragControls}>
              {/* 上移按钮 - 默认模板不显示 */}
              {index > 0 && !item.isDefault && (
                <TouchableOpacity
                  style={[
                    styles.dragControlButton,
                    {
                      borderWidth: 1,
                      borderColor: isDark ? "#6C7293" : "#9CA3AF",
                      backgroundColor: "transparent",
                    },
                  ]}
                  onPress={() => moveTemplate(index, "up")}
                  disabled={saving || isDragging}
                  hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
                >
                  <Ionicons
                    name="chevron-up"
                    size={14}
                    color={isDark ? "#6C7293" : "#9CA3AF"}
                  />
                </TouchableOpacity>
              )}

              {/* 下移按钮 - 默认模板不显示 */}
              {index < templates.length - 1 && !item.isDefault && (
                <TouchableOpacity
                  style={[
                    styles.dragControlButton,
                    {
                      borderWidth: 1,
                      borderColor: isDark ? "#6C7293" : "#9CA3AF",
                      backgroundColor: "transparent",
                    },
                  ]}
                  onPress={() => moveTemplate(index, "down")}
                  disabled={saving || isDragging}
                  hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
                >
                  <Ionicons
                    name="chevron-down"
                    size={14}
                    color={isDark ? "#6C7293" : "#9CA3AF"}
                  />
                </TouchableOpacity>
              )}
            </View>

            {/* 排序号显示 */}
            <View
              style={[
                styles.orderIndicator,
                { backgroundColor: isDark ? "#1F6FEB" : "#3B82F6" },
              ]}
            >
              <Text style={styles.orderText}>{index + 1}</Text>
            </View>
          </TouchableOpacity>
        </Animated.View>
      </PanGestureHandler>
    );
  };

  // 将模板分组为行
  const renderRows = () => {
    const rows = [];
    for (let i = 0; i < templates.length; i += ITEMS_PER_ROW) {
      const rowItems = templates.slice(i, i + ITEMS_PER_ROW);
      rows.push(
        <View key={`row-${i}`} style={styles.templateRow}>
          {rowItems.map((item, index) => renderTemplateItem(item, i + index))}
        </View>
      );
    }
    return rows;
  };

  return (
    <ThemedSafeAreaView
      style={[
        styles.container,
        { backgroundColor: isDark ? "#151718" : "white" },
      ]}
      edges={["top"]}
    >
      {/* 标题栏 */}
      <View
        style={[
          styles.header,
          {
            backgroundColor: isDark ? "#151718" : "white",
            borderBottomColor: isDark ? "#2B2F31" : "#f0f0f0",
          },
        ]}
      >
        <View style={styles.headerLeft}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol
              name="chevron.left"
              size={20}
              color={isDark ? "#9BA1A6" : "#6B7280"}
            />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, isDark && styles.headerTitleDark]}>
            {t("settings.templatePersonalization")}
          </Text>
        </View>
        <View style={styles.placeholder} />
      </View>

      {/* 说明文字 */}
      <View
        style={[
          styles.instructions,
          {
            backgroundColor: isDark ? "#0D419D20" : "#EBF8FF",
            borderLeftColor: isDark ? "#1F6FEB" : "#3B82F6",
          },
        ]}
      >
        <Text
          style={[
            styles.instructionText,
            { color: isDark ? "#79C0FF" : "#1E40AF" },
          ]}
        >
          {t("settings.templateManagerInstructions")}
        </Text>
        <Text
          style={[
            styles.orderExplanation,
            { color: isDark ? "#79C0FF" : "#1E40AF" },
          ]}
        >
          • {t("settings.templateManagerOrderExplanation")}
        </Text>
        {/* <Text style={[styles.dragHint, { color: isDark ? '#79C0FF' : '#1E40AF' }]}>
          {t('settings.templateManagerDragHint')}
        </Text> */}
      </View>

      {/* 模板网格 */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={isDark ? "#1F6FEB" : "#6366F1"}
          />
          <Text
            style={[
              styles.loadingText,
              { color: isDark ? "#8B949E" : "#6B7280" },
            ]}
          >
            {t("settings.templateManagerLoading")}
          </Text>
        </View>
      ) : (
        <ScrollView
          style={styles.gridContainer}
          contentContainerStyle={styles.gridContent}
          scrollEnabled={!isDragging}
          showsVerticalScrollIndicator={false}
          ref={scrollViewRef}
        >
          <View style={styles.gridWrapper}>{renderRows()}</View>
        </ScrollView>
      )}

      {/* 保存状态指示器 */}
      {saving && (
        <View
          style={[
            styles.savingIndicator,
            { backgroundColor: isDark ? "#21262D" : "#FEF3C7" },
          ]}
        >
          <Text
            style={[
              styles.savingText,
              { color: isDark ? "#F7CC48" : "#92400E" },
            ]}
          >
            {t("settings.templateManagerSaving")}
          </Text>
        </View>
      )}
    </ThemedSafeAreaView>
  );
}

const styles = {
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row" as const,
    justifyContent: "space-between" as const,
    alignItems: "center" as const,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    minHeight: 58,
  },
  headerDark: {
    backgroundColor: "#151718",
    borderBottomColor: "#2B2F31",
  },
  headerLeft: {
    flexDirection: "row" as const,
    alignItems: "center" as const,
  },
  backButton: {
    marginRight: 8,
    padding: 0,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold" as const,
    color: "#111827",
  },
  headerTitleDark: {
    color: "#ECEDEE",
  },
  placeholder: {
    width: 40,
  },
  instructions: {
    marginHorizontal: 16,
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
  },
  instructionText: {
    fontSize: 13,
    lineHeight: 18,
    marginBottom: 8,
  },
  orderExplanation: {
    fontSize: 12,
    lineHeight: 16,
    fontStyle: "italic" as const,
    marginBottom: 4,
  },
  dragHint: {
    fontSize: 12,
    lineHeight: 16,
    fontStyle: "italic" as const,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center" as const,
    alignItems: "center" as const,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  gridContainer: {
    flex: 1,
  },
  gridContent: {
    paddingTop: 16,
    paddingBottom: 40,
  },
  gridWrapper: {
    paddingHorizontal: PADDING,
  },
  templateRow: {
    flexDirection: "row" as const,
    marginBottom: ITEM_MARGIN,
  },
  templateGridItem: {
    // width 在渲染时动态设置
  },
  templateCard: {
    height: ITEM_HEIGHT,
    borderRadius: 12,
    borderWidth: 1,
    padding: 12,
    position: "relative" as const,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  templateContent: {
    flex: 1,
    paddingBottom: 20, // 为底部控制按钮留空间
    paddingRight: 4, // 为右上角按钮留出足够空间
  },
  templateTitle: {
    fontSize: 14,
    fontWeight: "600" as const,
    marginBottom: 4,
    paddingRight: 8, // 额外的右边距确保不与按钮重合
  },
  templateDescription: {
    fontSize: 11,
    lineHeight: 14,
    marginBottom: 6, // 减少下边距为标签留出空间
    flex: 1,
    paddingRight: 4, // 描述文字也给一点右边距
  },
  templateTags: {
    flexDirection: "row" as const,
    gap: 4,
    marginTop: 4, // 增加一点上边距
  },
  defaultTag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  defaultTagText: {
    fontSize: 9,
    fontWeight: "500" as const,
  },
  hideButton: {
    position: "absolute" as const,
    top: 4,
    right: 4,
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  hideButtonVisible: {
    opacity: 1,
  },
  hideButtonHidden: {
    opacity: 0.7,
  },
  dragControls: {
    position: "absolute" as const,
    bottom: 8,
    right: 8,
    flexDirection: "column" as const,
    gap: 2,
  },
  dragControlButton: {
    borderRadius: 6,
    padding: 4,
    alignItems: "center" as const,
    justifyContent: "center" as const,
  },
  orderIndicator: {
    position: "absolute" as const,
    bottom: 8,
    left: 8,
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: "center" as const,
    justifyContent: "center" as const,
  },
  orderText: {
    color: "#FFFFFF",
    fontSize: 10,
    fontWeight: "600" as const,
  },
  savingIndicator: {
    position: "absolute" as const,
    top: 80,
    left: 0,
    right: 0,
    paddingVertical: 8,
    alignItems: "center" as const,
  },
  savingText: {
    fontSize: 13,
    fontWeight: "500" as const,
  },
};
