import * as React from 'react';
import { Tabs } from 'expo-router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Platform, StyleSheet, View, TouchableOpacity, Text, DeviceEventEmitter } from 'react-native';
import { usePathname, useRouter } from 'expo-router';
import { BottomTabBarButtonProps } from '@react-navigation/bottom-tabs';
import { useFocusEffect } from '@react-navigation/native';
import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAppTheme } from '@/hooks/useAppTheme';
import { AUDIO_EVENTS } from '@/hooks/useAudioPlayer';
import GlobalCopyBadge from '@/components/ui/GlobalCopyBadge';
import { useTranslation } from 'react-i18next';
import { isOnboardingCompleted } from '@/services/onboardingService';
import { log, warn, debug, error as logError, info } from "@/services/logService";


// 为全局添加类型声明
declare global {
  var shouldResetInputScreen: boolean;
  var preserveTabAfterLogin: boolean;
}

// 创建一个全局的标志，用于控制重置
global.shouldResetInputScreen = false;
// 不再在这里设置preserveTabAfterLogin，避免覆盖根_layout.tsx中的设置

/**
 * 知我AI输入法标签页导航
 * 包含三个主要标签:
 * 1. 输入 - 语音输入和AI优化功能
 * 2. 历史 - 历史记录管理
 * 3. 设置 - 应用设置和VIP管理
 */
export default function TabLayout() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const pathname = usePathname();
  const theme = useAppTheme();
  const t = (key: string, options?: any): string => {
    // @ts-ignore
    return useTranslation().t(key, options);
  };
  
  // 引导状态
  const [onboardingChecked, setOnboardingChecked] = useState(false);
  const [needsOnboarding, setNeedsOnboarding] = useState(false);
  
  // 获取标签页标题
  const getTabTitle = (key: 'input' | 'history' | 'settings'): string => {
    return t(`tabs.${key}`) as string;
  };
  const DOUBLE_PRESS_DELAY = 300; // 双击判定的时间间隔（毫秒）
  
  // 使用本地状态而不是Redux
  const [lastPressTime, setLastPressTime] = useState(0);
  const [pressCount, setPressCount] = useState(0);
  const [hideTabBar, setHideTabBar] = useState(false);
  
  // 判断是否在子页面中
  const isInNestedScreen = pathname.split('/').length > 2;
  
  // 跟踪组件挂载和路径变化，调试标签切换行为
  useEffect(() => {
    log('[TabLayout] 当前路径:', pathname);
    log('[TabLayout] 嵌套页面状态:', isInNestedScreen);
    log('[TabLayout] preserveTabAfterLogin =', global.preserveTabAfterLogin);
    
    // 如果应用刚启动且preserveTabAfterLogin为true，强制设置为true
    // 这是为了防止任何自动导航到第一个标签页的行为
    if (pathname === "/" || pathname === "/(tabs)") {
      log('[TabLayout] 根路径加载，设置preserveTabAfterLogin = true');
      global.setPreserveTabAfterLogin(true);
    }
  }, [pathname, isInNestedScreen]);
  
  // 使用 ref 存储上次点击时间
  const lastTapRef = useRef<number>(0);
  
  // 处理输入标签页的双击
  const handleTabDoublePress = useCallback(() => {
    log('检测到双击输入标签页，重置输入页面状态');
    // 触发重置事件
    DeviceEventEmitter.emit('resetInputScreen');
  }, []);
  
  // 处理标签页点击
  const handleTabPress = useCallback((e: any) => {
    const now = Date.now();
    log('标签页点击，当前时间:', now);
    
    // 检查是否是双击（300ms内）
    if (now - lastTapRef.current < 300) {
      log('检测到双击标签页');
      lastTapRef.current = 0;
      // 触发重置
      handleTabDoublePress();
      // 阻止默认的标签页切换
      e.preventDefault();
    } else {
      // 记录点击时间
      lastTapRef.current = now;
      // 设置超时后重置
      setTimeout(() => {
        lastTapRef.current = 0;
      }, 350);
    }
  }, [handleTabDoublePress]);
  
  // 获取主题对应的颜色
  const activeTintColor = theme === 'dark' ? '#61dafb' : '#6a5ae1';
  const inactiveTintColor = theme === 'dark' ? '#9BA1A6' : '#687076';

  // 处理标签页切换事件，触发APP_TAB_CHANGE通知
  const handleTabChange = useCallback(() => {
    // 获取全局变量进行明确的日志记录
    log('[标签切换检查] preserveTabAfterLogin =', global.preserveTabAfterLogin);
    
    // 如果设置了保持标签状态（登录后不切换标签），则不触发切换事件
    if (global.preserveTabAfterLogin) {
      log('[标签切换检查] 标签页状态保持，不自动切换至首页');
      // 重置标志，允许下一次正常切换
      global.preserveTabAfterLogin = false;
      log('[标签切换检查] 重置标志 preserveTabAfterLogin = false');
      return;
    }
    
    log('[标签切换检查] 标签页切换，发送事件通知停止音频播放');
    DeviceEventEmitter.emit(AUDIO_EVENTS.APP_TAB_CHANGE);
  }, []);

  useEffect(() => {
    const sub = DeviceEventEmitter.addListener('HIDE_TAB_BAR', (hide) => {
      setHideTabBar(!!hide);
    });
    return () => sub.remove();
  }, []);

  // 检查引导状态
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        const completed = await isOnboardingCompleted();
        setNeedsOnboarding(!completed);
        setOnboardingChecked(true);
        
        // 如果未完成引导，跳转到引导页面
        if (!completed) {
          log('[TabLayout] 用户未完成引导，跳转到引导页面');
          router.push('/onboarding');
        }
      } catch (error) {
        logError('[TabLayout] 检查引导状态失败:', error);
        setOnboardingChecked(true);
      }
    };

    checkOnboardingStatus();
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <Tabs
        // 如果不想默认显示第一个标签页，可以使用initialRouteName设置初始路由
        // 但这里我们不指定，让它按照用户当前所在页面或保持最后的状态
        // 这样做会防止在登录后强制重定向到第一个标签页
        initialRouteName={undefined}
        screenOptions={{
          tabBarActiveTintColor: activeTintColor,
          tabBarInactiveTintColor: inactiveTintColor,
          headerShown: false,
          tabBarButton: (props) => {
            // 检查是否是输入标签页
            const isInputTab = React.Children.toArray(props.children).some(child => {
              if (React.isValidElement(child) && child.type === 'Label') {
                return child.props.children === t('tabs.input');
              }
              return false;
            });
            
            return (
              <HapticTab
                {...props}
                onPress={(e) => {
                  // 记录当前标志状态供调试
                  log('[Tab按钮点击] 当前preserveTabAfterLogin =', global.preserveTabAfterLogin);
                  
                  // 如果点击的不是当前选中的标签页，则发送切换事件
                  if (!props.accessibilityState?.selected) {
                    // 再次检查标志，确保登录后返回时不会触发标签切换
                    if (global.preserveTabAfterLogin) {
                      log('[Tab按钮点击] 标签页状态保持，不触发切换事件');
                      // 手动设置标志为false，确保下次点击能正常切换
                      global.preserveTabAfterLogin = false;
                      log('[Tab按钮点击] 重置标志 preserveTabAfterLogin = false');
                    } else {
                      log('[Tab按钮点击] 标志未设置，执行正常标签切换');
                      handleTabChange();
                    }
                  }
                  
                  // 保留原始点击行为
                  props.onPress?.(e);
                  
                  // 检查是否是输入标签页
                  const isInputTab = React.Children.toArray(props.children).some(child => {
                    if (React.isValidElement(child) && child.type === 'Label') {
                      return child.props.children === getTabTitle('input');
                    }
                    return false;
                  });
                  
                  if (isInputTab) {
                    const now = Date.now();
                    log('输入标签页点击，当前时间:', now);
                    
                    // 检查是否是双击（300ms内）
                    if (now - lastTapRef.current < 300) {
                      log('检测到双击输入标签页');
                      lastTapRef.current = 0;
                      // 触发重置
                      handleTabDoublePress();
                      // 阻止标签页切换
                      return;
                    } else {
                      // 记录点击时间
                      lastTapRef.current = now;
                      // 设置超时后重置
                      setTimeout(() => {
                        lastTapRef.current = 0;
                      }, 350);
                    }
                  }
                }}
              />
            );
          },
          tabBarBackground: TabBarBackground,
          // 在子页面中隐藏底部标签栏
          tabBarStyle: hideTabBar || isInNestedScreen ? { display: 'none' } : (Platform.OS === 'ios' ? { position: 'absolute' } : undefined),
        }}>
        <Tabs.Screen
          name="index"
          options={{
            title: getTabTitle('input'),
            tabBarIcon: ({ color }) => <IconSymbol name="mic" size={24} color={color} />,
          }}
          listeners={{
            tabPress: (e) => {
              handleTabPress(e);
            },
          }}
        />
        <Tabs.Screen
          name="history"
          options={{
            title: getTabTitle('history'),
            tabBarIcon: ({ color }) => <IconSymbol size={28} name="clock.fill" color={color} />,
          }}
        />
        <Tabs.Screen
          name="settings"
          options={{
            title: getTabTitle('settings'),
            tabBarIcon: ({ color }) => <IconSymbol size={28} name="gear" color={color} />,
          }}
        />
      </Tabs>
      
      {/* 全局CopyBadge组件，确保在所有页面都能显示 */}
      <GlobalCopyBadge />
    </View>
  );
}
