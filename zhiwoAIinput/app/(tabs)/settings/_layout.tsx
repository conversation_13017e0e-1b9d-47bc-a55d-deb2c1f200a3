import { Stack } from 'expo-router';
import { Platform } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';

/**
 * 设置页面内嵌的Stack导航
 * 用于在设置页面中打开子页面时隐藏底部标签栏
 */
export default function SettingsLayout() {
  const theme = useAppTheme();
  const backgroundColor = theme === 'dark' ? '#151718' : '#f9fafb';
  
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        // 自定义页面过渡效果
        animation: 'slide_from_right',
        // 非index页面隐藏底部标签栏
        presentation: 'card',
        contentStyle: { backgroundColor },
      }}>
      <Stack.Screen 
        name="index" 
      />
      <Stack.Screen 
        name="templates" 
        options={{
          // templates页面隐藏底部标签栏，使用card属性
          presentation: 'card',
          animationDuration: 200,
        }} 
      />
      <Stack.Screen 
        name="new-template" 
        options={{
          // new-template页面以modal形式展示
          presentation: 'modal',
          animation: 'slide_from_bottom',
          animationDuration: 300,
          gestureEnabled: true,
          gestureDirection: 'vertical',
        }} 
      />
      <Stack.Screen 
        name="edit-template" 
        options={{
          // edit-template页面隐藏底部标签栏，使用card属性
          presentation: 'card',
          animationDuration: 200,
        }} 
      />
      <Stack.Screen 
        name="vip-detail" 
        options={{
          // vip-detail页面以modal形式展示
          presentation: 'modal',
          animationDuration: 200,
        }} 
      />
    </Stack>
  );
} 