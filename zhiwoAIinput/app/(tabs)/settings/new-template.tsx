import { StyleSheet, TouchableOpacity, TextInput, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Text, View, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { FullScreenEditor } from '@/components/ui/FullScreenEditor';
import { router } from 'expo-router';
import { useState } from 'react';
import { useAppTheme } from '@/hooks/useAppTheme';
import { saveUserTemplate } from '@/services/templateService';
import { generateUUID } from '@/utils/helpers';
import { withAuthGuard } from '@/components/AuthGuard';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useTranslation } from 'react-i18next';
import { error as logError } from '@/services/logService';

/**
 * 新建风格模板页面
 * 允许用户创建自定义风格模板
 */
function NewTemplateScreen() {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [prompt, setPrompt] = useState('');
  const [showFullScreenEditor, setShowFullScreenEditor] = useState(false);
  const theme = useAppTheme();
  const isDark = theme === 'dark';

  // 获取VIP状态
  const { isVIP } = useSelector((state: RootState) => state.auth);

  // 使用翻译钩子
  const { t } = useTranslation();

  // 导航回模板管理页面
  const navigateBack = () => {
    router.back();
  };

  // 保存模板
  const handleSaveTemplate = async () => {
    // 表单验证
    if (!name.trim()) {
      Alert.alert(
        t('common.tip'), 
        t('templates.templateNameRequired', '请输入模板名称')
      );
      return;
    }

    if (!prompt.trim()) {
      Alert.alert(
        t('common.tip'), 
        t('templates.promptRequired', '请输入模板提示词')
      );
      return;
    }

    try {
      // 创建模板对象
      const newTemplate = {
        id: generateUUID(),
        title: name.trim(),
        description: description.trim(),
        prompt: prompt.trim(),
        isSystem: false,
        color: "#3B82F6",
        borderColor: "#3B82F6",
        backgroundColor: "#EFF6FF",
        isSynced: false, // 新建模板初始状态为未同步
        icon: "👤" // 用户自定义模板的默认图标
      };
      
      // 保存模板到本地存储
      const success = await saveUserTemplate(newTemplate);
      
      if (success) {
        Alert.alert(
          t('templates.saveSuccess'),
          t('templates.createSuccess', '模板已成功创建'),
          [
            {
              text: t('common.ok'),
              onPress: () => router.back()
            }
          ]
        );
      } else {
        Alert.alert(
          t('templates.saveFailed'), 
          t('templates.reachLimit', '可能是由于非VIP用户自定义模板数量达到上限')
        );
      }
    } catch (error) {
      logError("保存模板出错:", error);
      Alert.alert(
        t('templates.saveFailed'), 
        t('common.retryLater', '请稍后再试')
      );
    }
  };

  return (
    <SafeAreaView style={[
      styles.container,
      isDark && { backgroundColor: '#151718' }
    ]} edges={['top']}>
      <StatusBar style={isDark ? "light" : "dark"} />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 88 : 0}
      >
        {/* 头部导航 */}
        <View style={[
          styles.header,
          isDark && { 
            backgroundColor: '#151718',
            borderBottomColor: '#2B2F31'
          }
        ]}>
          <View style={styles.headerLeft}>
            <TouchableOpacity style={styles.backButton} onPress={navigateBack}>
              <IconSymbol name="chevron.left" size={20} color={isDark ? "#9BA1A6" : "#6B7280"} />
            </TouchableOpacity>
            <Text style={[
              styles.headerTitle,
              isDark && { color: '#ECEDEE' }
            ]}>{t('templates.newTemplate')}</Text>
          </View>
          <TouchableOpacity 
            style={[
              styles.saveButton, 
              (!name.trim() || !prompt.trim()) && styles.saveButtonDisabled,
              isDark && styles.saveButtonDark
            ]}
            onPress={handleSaveTemplate}
            disabled={!name.trim() || !prompt.trim()}
          >
            <Text style={[
              styles.saveButtonText, 
              (!name.trim() || !prompt.trim()) && styles.saveButtonTextDisabled
            ]}>{t('common.save')}</Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          automaticallyAdjustKeyboardInsets={Platform.OS === 'ios'}
        >
          {/* 表单区域 */}
          <View style={[
            styles.formSection,
            isDark && { 
              backgroundColor: '#1E2122',
              borderColor: '#2B2F31'
            }
          ]}>
            <Text style={[
              styles.formSectionTitle,
              isDark && { color: '#ECEDEE' }
            ]}>{t('templates.basicInfo')}</Text>

            <View style={styles.formGroup}>
              <Text style={[
                styles.formLabel,
                isDark && { color: '#9BA1A6' }
              ]}>{t('templates.templateName')}</Text>
              <TextInput
                style={[
                  styles.formInput,
                  isDark && { 
                    backgroundColor: '#151718',
                    borderColor: '#2B2F31',
                    color: '#ECEDEE'
                  }
                ]}
                value={name}
                onChangeText={setName}
                placeholder={t('templates.templateNamePlaceholder')}
                placeholderTextColor={isDark ? '#6B7280' : '#9CA3AF'}
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={[
                styles.formLabel,
                isDark && { color: '#9BA1A6' }
              ]}>{t('templates.templateDescription')}</Text>
              <TextInput
                style={[
                  styles.formInput,
                  isDark && { 
                    backgroundColor: '#151718',
                    borderColor: '#2B2F31',
                    color: '#ECEDEE'
                  }
                ]}
                value={description}
                onChangeText={setDescription}
                placeholder={t('templates.descriptionPlaceholder')}
                placeholderTextColor={isDark ? '#6B7280' : '#9CA3AF'}
              />
            </View>

            <View style={styles.formGroup}>
              <View style={styles.promptHeader}>
                <Text style={[
                  styles.formLabel,
                  isDark && { color: '#9BA1A6' }
                ]}>{t('templates.templatePrompt')}</Text>
                <TouchableOpacity
                  style={[
                    styles.expandButton,
                    isDark && { backgroundColor: '#2B2F31' }
                  ]}
                  onPress={() => setShowFullScreenEditor(true)}
                >
                  <IconSymbol
                    name="arrow.up.left.and.arrow.down.right"
                    size={16}
                    color={isDark ? '#9BA1A6' : '#6B7280'}
                  />
                </TouchableOpacity>
              </View>
              <TextInput
                style={[
                  styles.formTextarea,
                  isDark && {
                    backgroundColor: '#151718',
                    borderColor: '#2B2F31',
                    color: '#ECEDEE'
                  }
                ]}
                value={prompt}
                onChangeText={setPrompt}
                placeholder={t('templates.promptPlaceholder')}
                placeholderTextColor={isDark ? '#6B7280' : '#9CA3AF'}
                multiline={true}
                numberOfLines={6}
                textAlignVertical="top"
                onFocus={() => {
                  // 当聚焦时，可以选择性地显示一些提示
                }}
              />
              <View style={styles.promptFooter}>
                <Text style={[
                  styles.characterCount,
                  isDark && { color: '#6B7280' }
                ]}>
                  {prompt.length}/1000
                </Text>
              </View>
            </View>
          </View>

          {/* 使用说明 */}
          <View style={[
            styles.formSection,
            isDark && { 
              backgroundColor: '#1E2122',
              borderColor: '#2B2F31'
            }
          ]}>
            <Text style={[
              styles.formSectionTitle,
              isDark && { color: '#ECEDEE' }
            ]}>{t('templates.usageInstructions')}</Text>
            
            <View style={[
              styles.infoBox,
              isDark && { backgroundColor: '#1A1D1E' }
            ]}>
              <Text style={[
                styles.infoTitle,
                isDark && { color: '#ECEDEE' }
              ]}>{t('templates.usageInstructionsTitle')}</Text>
              <Text style={[
                styles.infoText,
                isDark && { color: '#9BA1A6' }
              ]}>
                {t('templates.usageInstructionsText')}
              </Text>
            </View>
            
            <View style={[
              styles.exampleBox,
              isDark && { backgroundColor: '#1A1D1E' }
            ]}>
              <Text style={[
                styles.exampleTitle,
                isDark && { color: '#ECEDEE' }
              ]}>{t('templates.usageInstructionsExampleTitle')}</Text>
              <Text style={[
                styles.exampleText,
                isDark && { color: '#9BA1A6' }
              ]}>
                {t('templates.usageInstructionsExampleText')}
              </Text>
            </View>
          </View>

          {/* VIP提示区域 */}
          {!isVIP && (
            <View style={[
              styles.vipNote,
              isDark && { backgroundColor: '#1E2122' }
            ]}>
              <IconSymbol 
                name="crown.fill" 
                size={18} 
                color="#F59E0B" 
                style={styles.vipIcon} 
              />
              <Text style={[
                styles.vipNoteText,
                isDark && { color: '#9BA1A6' }
              ]}>{t('templates.vipNoteText')}</Text>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>

      {/* 全屏编辑器 */}
      <FullScreenEditor
        visible={showFullScreenEditor}
        value={prompt}
        onChangeText={setPrompt}
        onClose={() => setShowFullScreenEditor(false)}
        title={t('templates.editPrompt')}
        placeholder={t('templates.promptPlaceholder')}
        maxLength={1000}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 44,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    padding: 8,
    marginRight: 4,
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#111',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 8,
    backgroundColor: '#6a5ae0',
  },
  saveButtonDisabled: {
    backgroundColor: '#d1d5db',
  },
  saveButtonDark: {
    backgroundColor: '#6a5ae0',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  saveButtonTextDisabled: {
    color: '#9ca3af',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 20,
    paddingBottom: 40,
  },
  formSection: {
    marginBottom: 20,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: '#f0f0f0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  formSectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 20,
    color: '#111',
    letterSpacing: -0.5,
  },
  formGroup: {
    marginBottom: 20,
  },
  formLabel: {
    fontSize: 16,
    marginBottom: 12,
    color: '#374151',
    fontWeight: '500',
  },
  formInput: {
    height: 48,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: '#f9fafb',
    lineHeight: 20,
  },
  formTextarea: {
    minHeight: 140,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    fontSize: 16,
    lineHeight: 24,
    backgroundColor: '#f9fafb',
  },
  promptHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
  },
  expandButtonText: {
    fontSize: 14,
    marginLeft: 4,
    color: '#6B7280',
  },
  promptFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  characterCount: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  infoBox: {
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#111',
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#4b5563',
  },
  exampleBox: {
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    padding: 12,
  },
  exampleTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 8,
    color: '#111',
  },
  exampleText: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#4b5563',
    lineHeight: 20,
  },
  vipNote: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginTop: 4,
  },
  vipIcon: {
    marginRight: 8,
  },
  vipNoteText: {
    fontSize: 14,
    color: '#6B7280',
    flex: 1,
  },
});

// 将新建模板页面使用AuthGuard高阶组件包装，要求用户登录
export default withAuthGuard(NewTemplateScreen, {
  requireAuth: true, // 需要用户登录
  loginTitle: '创建自定义模板',
  loginMessage: '请先登录以创建自定义模板',
}); 