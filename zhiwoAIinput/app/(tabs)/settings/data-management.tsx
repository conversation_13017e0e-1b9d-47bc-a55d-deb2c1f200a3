import React from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  DeviceEventEmitter,
  Switch,
} from "react-native";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { useAppTheme } from "@/hooks/useAppTheme";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import { logout } from "@/store/slices/authSlice";
import { clearAllHistoryRecords } from "@/store/slices/historySlice";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { supabase } from "@/services/supabaseService";
import { resetOnboardingStatus } from "@/services/onboardingService";
import { resetRecording } from "@/store/slices/recordingSlice";
import { store } from "@/store";
import { log, error as logError } from "@/services/logService";
import { saveUserSettings, getUserSettings } from "@/services/storageService";
import { showCenteredToast } from "@/utils/toastConfig";
import { ThemedSafeAreaView } from "@/components/ThemedSafeAreaView";
import {
  checkBiometricCapability,
  authenticateWithBiometrics,
  showBiometricSetupGuide,
  BiometricCapability,
  clearAuthenticationSession
} from '@/services/biometricService';

// 存储键名
const STORAGE_KEYS = {
  HISTORY_RECORDS: "knowme_history_records",
  USER_TEMPLATES: "knowme_user_templates",
  SELECTED_TRANSCRIBE_MODEL: "knowme_selected_transcribe_model",
  SELECTED_TEXT_MODEL: "knowme_selected_text_model",
  FAST_MODE_ENABLED: "knowme_fast_mode_enabled",
};

const LOGIN_GUIDE_KEY = "knowme_login_guide_shown";

export default function DataManagementScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const theme = useAppTheme();
  const isDark = theme === "dark";
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);

  // 应用加密相关状态
  const [appLockEnabled, setAppLockEnabled] = React.useState(false);
  const [biometricCapability, setBiometricCapability] = React.useState<BiometricCapability | null>(null);

  // 初始化应用加密设置
  React.useEffect(() => {
    const initAppLockSettings = async () => {
      try {
        // 获取用户设置
        const settings = await getUserSettings();
        setAppLockEnabled(settings.appLockEnabled || false);

        // 检查设备生物识别能力
        const capability = await checkBiometricCapability();
        setBiometricCapability(capability);

        log('[DataManagement] 应用加密设置初始化完成', {
          appLockEnabled: settings.appLockEnabled,
          biometricAvailable: capability.isAvailable,
          biometricEnrolled: capability.isEnrolled,
          supportedTypes: capability.supportedTypes
        });
      } catch (error) {
        logError('[DataManagement] 初始化应用加密设置失败:', error);
      }
    };

    initAppLockSettings();
  }, []);

  // 处理应用加密开关切换
  const handleToggleAppLock = async () => {
    try {
      if (!appLockEnabled) {
        // 开启应用加密前，先检查设备能力
        if (!biometricCapability?.isAvailable) {
          Alert.alert(
            t('settings.appLock.notSupported' as any),
            t('settings.appLock.notSupportedDesc' as any),
            [{ text: t('common.ok' as any), style: 'default' }]
          );
          return;
        }

        if (!biometricCapability?.isEnrolled) {
          // 设备支持但未注册，显示设置引导
          showBiometricSetupGuide(() => {
            // 用户设置完成后重新检查能力
            checkBiometricCapability().then(setBiometricCapability);
          });
          return;
        }

        // 测试生物识别认证
        const authResult = await authenticateWithBiometrics(
          t('settings.appLock.testAuth' as any),
          t('settings.appLock.usePassword' as any)
        );

        if (!authResult.success) {
          Alert.alert(
            t('settings.appLock.authFailed' as any),
            authResult.error || t('settings.appLock.authFailedDesc' as any),
            [{ text: t('common.ok' as any), style: 'default' }]
          );
          return;
        }

        // 认证成功，启用应用加密
        const newValue = true;
        setAppLockEnabled(newValue);
        await saveUserSettings({ appLockEnabled: newValue });

        showCenteredToast(t('settings.appLock.enabled' as any), 'success');
      } else {
        // 关闭应用加密，需要先验证身份
        const authResult = await authenticateWithBiometrics(
          t('settings.appLock.disableAuth' as any),
          t('settings.appLock.usePassword' as any)
        );

        if (!authResult.success) {
          Alert.alert(
            t('settings.appLock.authFailed' as any),
            authResult.error || t('settings.appLock.authFailedDesc' as any),
            [{ text: t('common.ok' as any), style: 'default' }]
          );
          return;
        }

        // 认证成功，关闭应用加密
        const newValue = false;
        setAppLockEnabled(newValue);
        await saveUserSettings({ appLockEnabled: newValue });

        // 清除认证会话
        await clearAuthenticationSession();

        showCenteredToast(t('settings.appLock.disabled' as any), 'success');
      }
    } catch (error) {
      logError('[DataManagement] 切换应用加密失败:', error);
      showCenteredToast(t('settings.appLock.toggleFailed' as any), 'error');
    }
  };

  // 删除所有本地数据
  const handleDeleteLocalData = () => {
    Alert.alert(
      t("dataManagement.deleteLocalDataTitle" as any),
      t("dataManagement.deleteLocalDataMessage" as any),
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            try {
              // 清空历史记录
              await dispatch(clearAllHistoryRecords()).unwrap();

              // 删除自定义模板
              await AsyncStorage.removeItem(STORAGE_KEYS.USER_TEMPLATES);

              // 删除其他本地数据（但保留用户登录状态）
              const keysToRemove = [
                STORAGE_KEYS.HISTORY_RECORDS,
                STORAGE_KEYS.USER_TEMPLATES,
                STORAGE_KEYS.SELECTED_TRANSCRIBE_MODEL,
                STORAGE_KEYS.SELECTED_TEXT_MODEL,
                STORAGE_KEYS.FAST_MODE_ENABLED,
              ];

              await AsyncStorage.multiRemove(keysToRemove);

              Alert.alert(
                t("common.success"),
                t("dataManagement.deleteLocalDataSuccess" as any),
                [{ text: t("common.ok") }]
              );
            } catch (error) {
              logError("删除本地数据失败:", error);
              Alert.alert(
                t("common.error"),
                t("dataManagement.deleteLocalDataError" as any)
              );
            }
          },
        },
      ]
    );
  };

  // 注销账号
  const handleDeleteAccount = () => {
    Alert.alert(
      t("dataManagement.deleteAccountTitle" as any),
      t("dataManagement.deleteAccountMessage" as any),
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("dataManagement.deleteAccount" as any),
          style: "destructive",
          onPress: () => {
            // 二次确认
            Alert.alert(
              t("dataManagement.deleteAccountConfirmTitle" as any),
              t("dataManagement.deleteAccountConfirmMessage" as any),
              [
                {
                  text: t("common.cancel"),
                  style: "cancel",
                },
                {
                  text: t("dataManagement.deleteAccount" as any),
                  style: "destructive",
                  onPress: executeDeleteAccount,
                },
              ]
            );
          },
        },
      ]
    );
  };

  // 执行注销账号
  const executeDeleteAccount = async () => {
    if (!user?.id) {
      Alert.alert(
        t("common.error"),
        t("dataManagement.userNotLoggedIn" as any)
      );
      return;
    }

    try {
      // 1. 在数据库中软删除用户信息，使用数据库函数
      const { error: deleteError } = await supabase.rpc("soft_delete_user", {
        target_user_id: user.id,
      });

      if (deleteError) {
        logError("软删除用户信息失败:", deleteError);
        Alert.alert(
          t("common.error"),
          t("dataManagement.deleteAccountError" as any)
        );
        return;
      }

      // 2. 清空所有本地数据
      await AsyncStorage.clear();

      // 3. 登出用户
      await dispatch(logout()).unwrap();

      Alert.alert(
        t("common.success"),
        t("dataManagement.deleteAccountSuccess" as any),
        [
          {
            text: t("common.ok"),
            onPress: () => {
              // 首先尝试关闭所有模态框和弹出页面
              while (router.canGoBack()) {
                router.back();
              }
              // 然后导航到根路由
              router.replace("/(tabs)");
            },
          },
        ]
      );
    } catch (error) {
      logError("注销账号失败:", error);
      Alert.alert(
        t("common.error"),
        t("dataManagement.deleteAccountError" as any)
      );
    }
  };

  // 重置引导状态
  const handleResetOnboarding = () => {
    Alert.alert(
      "重置引导状态",
      "重置后下次启动应用时会重新显示引导流程，确定要继续吗？",
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: "重置",
          style: "default",
          onPress: async () => {
            try {
              // 首先重置录音相关的Redux状态，确保AbortController被正确清理
              log("[Debug] 重置引导状态前先清理录音状态");
              store.dispatch(resetRecording());

              // 重置引导状态
              await resetOnboardingStatus();

              // 发送引导状态重置事件，通知InputScreen重置ref状态
              DeviceEventEmitter.emit("onboardingReset");

              log("[Debug] 引导状态重置完成，所有相关状态已清理");

              Alert.alert(
                t("common.success"),
                "引导状态已重置，下次启动应用时会重新显示引导流程",
                [{ text: t("common.ok") }]
              );
            } catch (error) {
              logError("重置引导状态失败:", error);
              Alert.alert(t("common.error"), "重置引导状态失败");
            }
          },
        },
      ]
    );
  };

  // 修复系统提示词缓存
  const handleFixSystemPrompt = async () => {
    Alert.alert(
      "修复系统提示词缓存",
      "这将清除本地缓存并从云端重新获取最新的系统提示词，确定要继续吗？",
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: "修复",
          style: "default",
          onPress: async () => {
            try {
              const { fixSystemPromptCache } = await import(
                "../../../utils/fixSystemPrompt"
              );
              const result = await fixSystemPromptCache();

              Alert.alert(
                result.success ? "修复成功" : "修复失败",
                result.message,
                [{ text: t("common.ok") }]
              );
            } catch (error) {
              logError("修复系统提示词失败:", error);
              Alert.alert(
                "修复失败",
                "修复系统提示词失败: " + (error as Error).message
              );
            }
          },
        },
      ]
    );
  };

  // 检查系统提示词内容
  const handleCheckSystemPrompt = async () => {
    try {
      const { checkSystemPromptContent } = await import(
        "../../../utils/fixSystemPrompt"
      );
      const result = await checkSystemPromptContent();

      Alert.alert(
        "系统提示词检查结果",
        `版本: ${result.version}\n状态: ${result.message}`,
        [{ text: t("common.ok") }]
      );
    } catch (error) {
      logError("检查系统提示词失败:", error);
      Alert.alert(
        "检查失败",
        "检查系统提示词失败: " + (error as Error).message
      );
    }
  };

  // 清理所有缓存
  const handleClearAllCache = async () => {
    Alert.alert(
      "清理所有缓存",
      "这将清理系统提示词、模板偏好设置等所有缓存数据，确定要继续吗？",
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: "清理",
          style: "destructive",
          onPress: async () => {
            try {
              const { DebugUtils } = await import("../../../utils/debugUtils");
              await DebugUtils.clearAllCache();

              Alert.alert("清理成功", "所有缓存已清理完成", [
                { text: t("common.ok") },
              ]);
            } catch (error) {
              logError("清理缓存失败:", error);
              Alert.alert(
                "清理失败",
                "清理缓存失败: " + (error as Error).message
              );
            }
          },
        },
      ]
    );
  };

  // 检查语音提示词
  const handleCheckSpeechPrompt = async () => {
    try {
      const { SpeechPromptManager } = await import(
        "../../../utils/speechPromptManager"
      );
      const result = await SpeechPromptManager.checkForUpdates();

      Alert.alert(
        "语音提示词检查结果",
        `缓存状态: ${result.cacheExpired ? "已过期" : "有效"}\n需要更新: ${
          result.needsUpdate ? "是" : "否"
        }`,
        [{ text: t("common.ok") }]
      );
    } catch (error) {
      logError("检查语音提示词失败:", error);
      Alert.alert(
        "检查失败",
        "检查语音提示词失败: " + (error as Error).message
      );
    }
  };

  // 修复语音提示词缓存
  const handleFixSpeechPrompt = async () => {
    Alert.alert(
      "修复语音提示词缓存",
      "这将清除本地缓存并从云端重新获取最新的语音提示词，确定要继续吗？",
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: "修复",
          style: "default",
          onPress: async () => {
            try {
              const { SpeechPromptManager } = await import(
                "../../../utils/speechPromptManager"
              );
              await SpeechPromptManager.clearCache();
              const success = await SpeechPromptManager.refreshCache();

              Alert.alert(
                success ? "修复成功" : "修复失败",
                success ? "语音提示词缓存已修复" : "无法从云端获取语音提示词",
                [{ text: t("common.ok") }]
              );
            } catch (error) {
              logError("修复语音提示词失败:", error);
              Alert.alert(
                "修复失败",
                "修复语音提示词失败: " + (error as Error).message
              );
            }
          },
        },
      ]
    );
  };

  // 开发者工具：重置首次登录引导弹窗
  const handleResetLoginGuide = async () => {
    const before = await AsyncStorage.getItem(LOGIN_GUIDE_KEY);
    log("[LoginGuide] 重置前AsyncStorage.getItem:", before);
    await AsyncStorage.removeItem(LOGIN_GUIDE_KEY);
    const after = await AsyncStorage.getItem(LOGIN_GUIDE_KEY);
    log("[LoginGuide] 重置后AsyncStorage.getItem:", after);
    showCenteredToast(
      "已重置首次登录引导弹窗，下次进入主界面会再次弹出",
      "success"
    );
  };

  // 修正getColor调用方式，直接返回字符串
  const getColor = (lightColor: string, darkColor: string) => {
    return isDark ? darkColor : lightColor;
  };

  return (
    <ThemedSafeAreaView
      style={[
        styles.container,
        { backgroundColor: isDark ? "#151718" : "white" },
      ]}
    >
      {/* 页面标题 */}
      <View style={[styles.header, isDark && styles.headerDark]}>
        <View style={styles.headerLeft}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol
              name="chevron.left"
              size={20}
              color={isDark ? "#9BA1A6" : "#6B7280"}
            />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, isDark && styles.headerTitleDark]}>
            {t("settings.dataManagement")}
          </Text>
        </View>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* 应用加密 */}
        <View
          style={[
            styles.sectionContainer,
            {
              backgroundColor: getColor("#fff", "#1E2122"),
              borderColor: getColor("#f0f0f0", "#2B2F31"),
            },
          ]}
        >
          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleToggleAppLock}
            disabled={!biometricCapability?.isAvailable}
          >
            <View style={styles.settingItemLeft}>
              <View
                style={[
                  styles.iconContainer,
                  { backgroundColor: getColor("#FEF3C7", "#7C2D12") },
                ]}
              >
                <IconSymbol
                  name="lock.shield"
                  size={20}
                  color={getColor("#D97706", "#FB923C")}
                />
              </View>
              <View style={styles.textContainer}>
                <Text
                  style={[
                    styles.itemTitle,
                    {
                      color: biometricCapability?.isAvailable
                        ? getColor("#1D1D1F", "#fbfbfd")
                        : getColor("#9CA3AF", "#6B7280")
                    },
                  ]}
                >
                  {t("dataManagement.appLock" as any)}
                </Text>
                <Text
                  style={[
                    styles.itemSubtitle,
                    { color: getColor("#6B7280", "#9CA3AF") },
                  ]}
                >
                  {t("dataManagement.appLockDesc" as any)}
                </Text>
              </View>
            </View>
            <Switch
              value={appLockEnabled}
              onValueChange={!biometricCapability?.isAvailable ? undefined : handleToggleAppLock}
              disabled={!biometricCapability?.isAvailable}
              trackColor={{
                false: !biometricCapability?.isAvailable ? (isDark ? '#2B2F31' : '#F3F4F6') : (isDark ? '#39393D' : '#E5E7EB'),
                true: !biometricCapability?.isAvailable ? (isDark ? '#4B5563' : '#D1D5DB') : (isDark ? '#8364E2' : '#6A5AE1')
              }}
              thumbColor={!biometricCapability?.isAvailable ? (isDark ? '#6B7280' : '#9CA3AF') : '#FFFFFF'}
              ios_backgroundColor={!biometricCapability?.isAvailable ? (isDark ? '#2B2F31' : '#F3F4F6') : (isDark ? '#39393D' : '#E5E7EB')}
            />
          </TouchableOpacity>
        </View>

        {/* 删除本地数据 */}
        <View
          style={[
            styles.sectionContainer,
            {
              backgroundColor: getColor("#fff", "#1E2122"),
              borderColor: getColor("#f0f0f0", "#2B2F31"),
            },
          ]}
        >
          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleDeleteLocalData}
          >
            <View style={styles.settingItemLeft}>
              <View
                style={[
                  styles.iconContainer,
                  { backgroundColor: getColor("#FEF3C7", "#451A03") },
                ]}
              >
                <IconSymbol
                  name="trash"
                  size={20}
                  color={getColor("#F59E0B", "#FDE047")}
                />
              </View>
              <View style={styles.textContainer}>
                <Text
                  style={[
                    styles.itemTitle,
                    { color: getColor("#1D1D1F", "#fbfbfd") },
                  ]}
                >
                  {t("dataManagement.deleteLocalData" as any)}
                </Text>
                <Text
                  style={[
                    styles.itemSubtitle,
                    { color: getColor("#6B7280", "#9CA3AF") },
                  ]}
                >
                  {t("dataManagement.deleteLocalDataDesc" as any)}
                </Text>
              </View>
            </View>
            <IconSymbol
              name="chevron.right"
              size={16}
              color={getColor("#6B7280", "#9CA3AF")}
            />
          </TouchableOpacity>
        </View>

        {/* 注销账号 */}
        <View
          style={[
            styles.sectionContainer,
            {
              backgroundColor: getColor("#fff", "#1E2122"),
              borderColor: getColor("#f0f0f0", "#2B2F31"),
            },
          ]}
        >
          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleDeleteAccount}
          >
            <View style={styles.settingItemLeft}>
              <View
                style={[
                  styles.iconContainer,
                  { backgroundColor: getColor("#FEE2E2", "#7F1D1D") },
                ]}
              >
                <IconSymbol
                  name="person.badge.minus"
                  size={20}
                  color={getColor("#EF4444", "#F87171")}
                />
              </View>
              <View style={styles.textContainer}>
                <Text
                  style={[
                    styles.itemTitle,
                    { color: getColor("#1D1D1F", "#fbfbfd") },
                  ]}
                >
                  {t("dataManagement.deleteAccount" as any)}
                </Text>
                <Text
                  style={[
                    styles.itemSubtitle,
                    { color: getColor("#6B7280", "#9CA3AF") },
                  ]}
                >
                  {t("dataManagement.deleteAccountDesc" as any)}
                </Text>
              </View>
            </View>
            <IconSymbol
              name="chevron.right"
              size={16}
              color={getColor("#6B7280", "#9CA3AF")}
            />
          </TouchableOpacity>
        </View>

        {/* 开发调试功能 */}
        {__DEV__ && (
          <View
            style={[
              styles.sectionContainer,
              {
                backgroundColor: getColor("#fff", "#1E2122"),
                borderColor: getColor("#f0f0f0", "#2B2F31"),
                marginTop: 20,
              },
            ]}
          >
            <View style={styles.sectionHeader}>
              <Text
                style={[
                  styles.sectionTitle,
                  { color: getColor("#6B7280", "#9CA3AF") },
                ]}
              >
                开发调试
              </Text>
            </View>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => router.push("/ai-usage")}
            >
              <View style={styles.settingItemLeft}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: getColor("#DBEAFE", "#1E3A8A") },
                  ]}
                >
                  <IconSymbol
                    name="chart.bar"
                    size={20}
                    color={getColor("#3B82F6", "#60A5FA")}
                  />
                </View>
                <View style={styles.textContainer}>
                  <Text
                    style={[
                      styles.itemTitle,
                      { color: getColor("#1D1D1F", "#fbfbfd") },
                    ]}
                  >
                    AI 使用统计
                  </Text>
                  <Text
                    style={[
                      styles.itemSubtitle,
                      { color: getColor("#6B7280", "#9CA3AF") },
                    ]}
                  >
                    查看 AI 调用记录和成本统计
                  </Text>
                </View>
              </View>
              <IconSymbol
                name="chevron.right"
                size={16}
                color={getColor("#6B7280", "#9CA3AF")}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={handleResetOnboarding}
            >
              <View style={styles.settingItemLeft}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: getColor("#E5E7EB", "#374151") },
                  ]}
                >
                  <IconSymbol
                    name="arrow.clockwise"
                    size={20}
                    color={getColor("#6B7280", "#9CA3AF")}
                  />
                </View>
                <View style={styles.textContainer}>
                  <Text
                    style={[
                      styles.itemTitle,
                      { color: getColor("#1D1D1F", "#fbfbfd") },
                    ]}
                  >
                    重置引导状态
                  </Text>
                  <Text
                    style={[
                      styles.itemSubtitle,
                      { color: getColor("#6B7280", "#9CA3AF") },
                    ]}
                  >
                    下次启动时重新显示引导流程
                  </Text>
                </View>
              </View>
              <IconSymbol
                name="chevron.right"
                size={16}
                color={getColor("#6B7280", "#9CA3AF")}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={handleFixSystemPrompt}
            >
              <View style={styles.settingItemLeft}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: getColor("#E5E7EB", "#374151") },
                  ]}
                >
                  <IconSymbol
                    name="wrench.and.screwdriver"
                    size={20}
                    color={getColor("#6B7280", "#9CA3AF")}
                  />
                </View>
                <View style={styles.textContainer}>
                  <Text
                    style={[
                      styles.itemTitle,
                      { color: getColor("#1D1D1F", "#fbfbfd") },
                    ]}
                  >
                    修复系统提示词缓存
                  </Text>
                  <Text
                    style={[
                      styles.itemSubtitle,
                      { color: getColor("#6B7280", "#9CA3AF") },
                    ]}
                  >
                    清除缓存并重新获取最新提示词
                  </Text>
                </View>
              </View>
              <IconSymbol
                name="chevron.right"
                size={16}
                color={getColor("#6B7280", "#9CA3AF")}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={handleCheckSystemPrompt}
            >
              <View style={styles.settingItemLeft}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: getColor("#E5E7EB", "#374151") },
                  ]}
                >
                  <IconSymbol
                    name="checkmark.circle"
                    size={20}
                    color={getColor("#6B7280", "#9CA3AF")}
                  />
                </View>
                <View style={styles.textContainer}>
                  <Text
                    style={[
                      styles.itemTitle,
                      { color: getColor("#1D1D1F", "#fbfbfd") },
                    ]}
                  >
                    检查系统提示词
                  </Text>
                  <Text
                    style={[
                      styles.itemSubtitle,
                      { color: getColor("#6B7280", "#9CA3AF") },
                    ]}
                  >
                    检查提示词版本和内容状态
                  </Text>
                </View>
              </View>
              <IconSymbol
                name="chevron.right"
                size={16}
                color={getColor("#6B7280", "#9CA3AF")}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => {
                try {
                  router.push({
                    pathname: "/log-test" as any,
                  });
                } catch (error) {
                  logError("导航到日志测试页面失败:", error);
                  Alert.alert("导航错误", "无法打开日志测试页面");
                }
              }}
            >
              <View style={styles.settingItemLeft}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: getColor("#FECACA", "#7F1D1D") },
                  ]}
                >
                  <IconSymbol
                    name="text.alignleft"
                    size={20}
                    color={getColor("#DC2626", "#F87171")}
                  />
                </View>
                <View style={styles.textContainer}>
                  <Text
                    style={[
                      styles.itemTitle,
                      { color: getColor("#1D1D1F", "#fbfbfd") },
                    ]}
                  >
                    日志测试工具
                  </Text>
                  <Text
                    style={[
                      styles.itemSubtitle,
                      { color: getColor("#6B7280", "#9CA3AF") },
                    ]}
                  >
                    测试日志服务和RevenueCat配置
                  </Text>
                </View>
              </View>
              <IconSymbol
                name="chevron.right"
                size={16}
                color={getColor("#6B7280", "#9CA3AF")}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={handleClearAllCache}
            >
              <View style={styles.settingItemLeft}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: getColor("#E5E7EB", "#374151") },
                  ]}
                >
                  <IconSymbol
                    name="trash"
                    size={20}
                    color={getColor("#F59E0B", "#FDE047")}
                  />
                </View>
                <View style={styles.textContainer}>
                  <Text
                    style={[
                      styles.itemTitle,
                      { color: getColor("#1D1D1F", "#fbfbfd") },
                    ]}
                  >
                    清理所有缓存
                  </Text>
                  <Text
                    style={[
                      styles.itemSubtitle,
                      { color: getColor("#6B7280", "#9CA3AF") },
                    ]}
                  >
                    清理系统提示词和模板缓存
                  </Text>
                </View>
              </View>
              <IconSymbol
                name="chevron.right"
                size={16}
                color={getColor("#6B7280", "#9CA3AF")}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={handleCheckSpeechPrompt}
            >
              <View style={styles.settingItemLeft}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: getColor("#E5E7EB", "#374151") },
                  ]}
                >
                  <IconSymbol
                    name="checkmark.circle"
                    size={20}
                    color={getColor("#6B7280", "#9CA3AF")}
                  />
                </View>
                <View style={styles.textContainer}>
                  <Text
                    style={[
                      styles.itemTitle,
                      { color: getColor("#1D1D1F", "#fbfbfd") },
                    ]}
                  >
                    检查语音提示词
                  </Text>
                  <Text
                    style={[
                      styles.itemSubtitle,
                      { color: getColor("#6B7280", "#9CA3AF") },
                    ]}
                  >
                    检查语音提示词缓存状态
                  </Text>
                </View>
              </View>
              <IconSymbol
                name="chevron.right"
                size={16}
                color={getColor("#6B7280", "#9CA3AF")}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={handleFixSpeechPrompt}
            >
              <View style={styles.settingItemLeft}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: getColor("#E5E7EB", "#374151") },
                  ]}
                >
                  <IconSymbol
                    name="wrench.and.screwdriver"
                    size={20}
                    color={getColor("#6B7280", "#9CA3AF")}
                  />
                </View>
                <View style={styles.textContainer}>
                  <Text
                    style={[
                      styles.itemTitle,
                      { color: getColor("#1D1D1F", "#fbfbfd") },
                    ]}
                  >
                    修复语音提示词缓存
                  </Text>
                  <Text
                    style={[
                      styles.itemSubtitle,
                      { color: getColor("#6B7280", "#9CA3AF") },
                    ]}
                  >
                    清除本地缓存并重新获取最新语音提示词
                  </Text>
                </View>
              </View>
              <IconSymbol
                name="chevron.right"
                size={16}
                color={getColor("#6B7280", "#9CA3AF")}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={handleResetLoginGuide}
            >
              <View style={styles.settingItemLeft}>
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: getColor("#E0E7FF", "#312E81") },
                  ]}
                >
                  <IconSymbol
                    name="arrow.uturn.backward"
                    size={20}
                    color={getColor("#6366F1", "#A5B4FC")}
                  />
                </View>
                <View style={styles.textContainer}>
                  <Text
                    style={[
                      styles.itemTitle,
                      { color: getColor("#1D1D1F", "#fbfbfd") },
                    ]}
                  >
                    重置首次登录引导弹窗
                  </Text>
                  <Text
                    style={[
                      styles.itemSubtitle,
                      { color: getColor("#6B7280", "#9CA3AF") },
                    ]}
                  >
                    下次进入主界面会再次弹出首次登录引导
                  </Text>
                </View>
              </View>
              <IconSymbol
                name="chevron.right"
                size={16}
                color={getColor("#6B7280", "#9CA3AF")}
              />
            </TouchableOpacity>
          </View>
        )}

        {/* 警告说明 */}
        <View style={styles.warningContainer}>
          <View style={styles.warningHeader}>
            <IconSymbol
              name="exclamationmark.triangle"
              size={20}
              color={getColor("#F59E0B", "#FDE047")}
            />
            <Text
              style={[
                styles.warningTitle,
                { color: getColor("#F59E0B", "#FDE047") },
              ]}
            >
              {t("common.warning")}
            </Text>
          </View>
          <Text
            style={[
              styles.warningText,
              { color: getColor("#6B7280", "#9CA3AF") },
            ]}
          >
            {t("dataManagement.warningMessage" as any)}
          </Text>
        </View>
      </ScrollView>
    </ThemedSafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 100,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    minHeight: 58,
  },
  headerDark: {
    backgroundColor: "#151718",
    borderBottomColor: "#2B2F31",
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 8,
    padding: 0,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#111827",
  },
  headerTitleDark: {
    color: "#ECEDEE",
  },
  headerSpacer: {
    width: 40,
  },
  sectionContainer: {
    marginHorizontal: 16,
    marginTop: 20,
    borderRadius: 12,
    borderWidth: 0.5,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  settingItemLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 2,
  },
  itemSubtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  warningContainer: {
    marginHorizontal: 16,
    marginTop: 30,
    padding: 16,
    borderRadius: 12,
    backgroundColor: "rgba(245, 158, 11, 0.1)",
  },
  warningHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  warningText: {
    fontSize: 14,
    lineHeight: 20,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
  },
});
