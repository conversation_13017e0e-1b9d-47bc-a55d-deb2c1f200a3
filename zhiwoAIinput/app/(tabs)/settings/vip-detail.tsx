import React, { useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../../store';
import { 
  ThemedView, 
  ThemedText, 
  ThemedSafeAreaView
} from '../../../components/withTheme';
import { IconSymbol, IconSymbolName } from '../../../components/ui/IconSymbol';
import { SUBSCRIPTION_PACKAGES, SUBSCRIPTION_TYPE } from '../../../services/purchaseService';
import { useAppTheme } from '../../../hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { log, error as logError } from '@/services/logService';

// VIP功能项组件
interface VipFeatureItemProps {
  icon: IconSymbolName;
  iconBgColor: string;
  iconColor: string;
  title: string;
  description: string;
  isLast?: boolean;
  isDark?: boolean;
}

function VipFeatureItem({
  icon,
  iconBgColor,
  iconColor,
  title,
  description,
  isLast = false,
  isDark = false
}: VipFeatureItemProps) {
  return (
    <View style={[
      styles.vipFeatureItem,
      !isLast && styles.vipFeatureItemWithBorder,
      !isLast && { borderBottomColor: isDark ? '#2B2F31' : '#e5e7eb' }
    ]}>
      <View style={[
        styles.vipFeatureIconContainer, 
        { 
          backgroundColor: iconBgColor,
          shadowColor: iconColor,
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: isDark ? 0.3 : 0.1,
          shadowRadius: 3
        }
      ]}>
        <IconSymbol name={icon} size={22} color={iconColor} />
      </View>
      <View style={styles.vipFeatureContent}>
        <Text style={[
          styles.vipFeatureTitle,
          { color: isDark ? '#ECEDEE' : '#1F2937' }
        ]}>{title}</Text>
        <Text style={[
          styles.vipFeatureDescription,
          { color: isDark ? '#9BA1A6' : '#6B7280' }
        ]}>{description}</Text>
      </View>
    </View>
  );
}

// 用户评论项组件
interface UserReviewItemProps {
  title: string;
  content: string;
  author: string;
  rating: number;
  isDark: boolean;
}

function UserReviewItem({
  title,
  content,
  author,
  rating,
  isDark
}: UserReviewItemProps) {
  return (
    <View style={[
      styles.reviewCard,
      { backgroundColor: isDark ? '#1E2122' : '#F9FAFB' }
    ]}>
      <View style={styles.reviewHeader}>
        <Text style={[
          styles.reviewTitle,
          { color: isDark ? '#E5E7EB' : '#1F2937' }
        ]}>{title}</Text>
        <View style={styles.starsContainer}>
          {[1, 2, 3, 4, 5].map((_, index) => (
            <IconSymbol
              key={index}
              name={index < rating ? "star.fill" : "star"}
              size={12}
              color="#FBBF24"
            />
          ))}
        </View>
      </View>
      <Text style={[
        styles.reviewContent,
        { color: isDark ? '#9CA3AF' : '#6B7280' }
      ]}>{content}</Text>
      <Text style={[
        styles.reviewAuthor,
        { color: isDark ? '#9CA3AF' : '#6B7280' }
      ]}>{author}</Text>
    </View>
  );
}

export default function VipDetailScreen() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  
  // 用户评论横向滚动的引用
  const reviewsScrollViewRef = useRef<ScrollView>(null);
  const autoScrollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const hasUserInteractedRef = useRef(false); // 记录用户是否已经手动滑动过
  
  // 从Redux获取状态
  const currentTheme = useAppTheme();
  const isDark = currentTheme === 'dark';
  const userProfile = useSelector((state: RootState) => state.auth.user?.profile);
  const currentSubscription = useSelector((state: RootState) => state.subscription.currentSubscription);
  
  // 获取深色模式下的颜色
  const getColor = (lightColor: string, darkColor: string) => {
    return isDark ? darkColor : lightColor;
  };
  
  // 获取当前订阅的类型（月度/年度）
  const getCurrentPlanType = () => {
    log('检查套餐类型:', {
      userProfile: userProfile?.vip_product_id,
      subscriptionPlanId: currentSubscription?.planId
    });
    
    // 优先从用户资料中获取产品ID
    if (userProfile?.vip_product_id) {
      const productId = userProfile.vip_product_id.toLowerCase();
      
      // 检查产品ID是否包含月度/年度关键词
      if (productId.includes('month') || 
          productId.includes('monthly') || 
          productId === SUBSCRIPTION_PACKAGES.MONTHLY.toLowerCase()) {
        return SUBSCRIPTION_TYPE.MONTHLY;
      } 
      
      if (productId.includes('year') || 
          productId.includes('yearly') || 
          productId.includes('annual') || 
          productId === SUBSCRIPTION_PACKAGES.YEARLY.toLowerCase()) {
        return SUBSCRIPTION_TYPE.YEARLY;
      }
    }
    
    // 其次从Redux获取订阅信息
    if (currentSubscription?.planId) {
      const planId = currentSubscription.planId.toLowerCase();
      
      if (planId.includes('month') || 
          planId.includes('monthly') || 
          planId === SUBSCRIPTION_PACKAGES.MONTHLY.toLowerCase()) {
        return SUBSCRIPTION_TYPE.MONTHLY;
      }
      
      if (planId.includes('year') || 
          planId.includes('yearly') ||
          planId.includes('annual') ||
          planId === SUBSCRIPTION_PACKAGES.YEARLY.toLowerCase()) {
        return SUBSCRIPTION_TYPE.YEARLY;
      }
    }
    
    // 默认返回月度套餐类型
    return SUBSCRIPTION_TYPE.MONTHLY;
  };
  
  // 获取当前套餐名称的显示文本
  const getCurrentPlanName = (): string => {
    const planType = getCurrentPlanType();
    
    switch (planType) {
      case SUBSCRIPTION_TYPE.MONTHLY: return t('vipDetail.plans.monthly' as any) as string;
      case SUBSCRIPTION_TYPE.YEARLY: return t('vipDetail.plans.yearly' as any) as string;
      default: return t('vipDetail.plans.default' as any) as string;
    }
  };
  
  // 获取订阅到期日期的格式化字符串
  const getSubscriptionEndDate = (): string => {
    // 获取用户VIP资料
    const user = useSelector((state: RootState) => state.auth.user);
    
    // 如果用户未登录或没有用户资料，返回空字符串
    if (!user) return t('vipDetail.validityNotAvailable' as any) as string;
    
    const formatDate = (date: Date) => {
      return t('vipDetail.validUntil' as any, {
        date: `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`,
        defaultValue: ''
      }) as string;
    };
    
    // 优先从用户资料中获取VIP过期时间
    if (userProfile?.vip_expires_at) {
      const expiryDate = new Date(userProfile.vip_expires_at);
      return formatDate(expiryDate);
    }
    
    // 其次从Redux状态获取订阅信息
    if (currentSubscription?.currentPeriodEnd) {
      const endDate = new Date(currentSubscription.currentPeriodEnd);
      return formatDate(endDate);
    }
    
    // 当前没有有效的订阅信息
    return t('vipDetail.validityNotAvailable' as any) as string;
  };
  
  // 处理套餐切换
  const handleChangePlan = () => {
    try {
      log('[VipDetail] handleChangePlan 被调用');
      
      // 获取当前套餐类型
      const currentPlan = getCurrentPlanType();
      
      // 如果当前是月套餐，直接跳转到年套餐支付页面
      // 如果当前是年套餐，直接跳转到月套餐支付页面
      const targetPlan = currentPlan === SUBSCRIPTION_TYPE.MONTHLY ? SUBSCRIPTION_TYPE.YEARLY : SUBSCRIPTION_TYPE.MONTHLY;
      
      log('[VipDetail] 切换套餐，当前:', currentPlan, '目标:', targetPlan);
      
      // 构造路由参数
      const routeParams = {
        pathname: '/payment' as const,
        params: { 
          onSuccess: 'true',
          switchPlan: 'true',
          planType: targetPlan
        }
      };
      
      log('[VipDetail] 准备跳转到支付页面，参数:', routeParams);
      
      // 跳转到支付页面
      router.push(routeParams);
      
      log('[VipDetail] router.push 已调用');
    } catch (error) {
      logError('[VipDetail] handleChangePlan 出错:', error);
    }
  };
  
  // 返回设置页面
  const goBack = () => {
    router.back();
  };

  // 自动滚动用户评论 - 只在首次进入时自动滚动
  useEffect(() => {
    let scrollPosition = 0;
    const cardWidth = 276; // 每个评论卡片的宽度（260 + 16margin）
    const totalCards = 9; // 更新为9个评论卡片
    const maxScrollWidth = totalCards * cardWidth;
    let direction = 1; // 1为向右，-1为向左
    
    const scrollInterval = setInterval(() => {
      // 只有在用户还没有手动滑动过的情况下才自动滚动
      if (reviewsScrollViewRef.current && !hasUserInteractedRef.current) {
        scrollPosition += direction * 1; // 每次移动1px
        
        // 到达边界时改变方向
        if (scrollPosition >= maxScrollWidth - 300) {
          direction = -1;
        } else if (scrollPosition <= 0) {
          direction = 1;
        }
        
        // 确保不超出边界
        scrollPosition = Math.max(0, Math.min(scrollPosition, maxScrollWidth - 300));
        
        reviewsScrollViewRef.current.scrollTo({
          x: scrollPosition,
          animated: false
        });
      }
    }, 30); // 每30毫秒移动一次

    autoScrollIntervalRef.current = scrollInterval;
    return () => {
      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
      }
    };
  }, []);

  // 处理用户手动滚动 - 一旦用户滑动就永久停止自动滚动
  const handleUserScrollBegin = () => {
    hasUserInteractedRef.current = true;
    // 立即停止自动滚动
    if (autoScrollIntervalRef.current) {
      clearInterval(autoScrollIntervalRef.current);
      autoScrollIntervalRef.current = null;
    }
  };

  // 获取随机渐变颜色
  const getIconGradientColors = (baseColor: string, isDark: boolean): [string, string] => {
    if (isDark) {
      switch (baseColor) {
        case 'purple': return ['#3B0764', '#6B21A8'];
        case 'blue': return ['#082F49', '#0C4A6E'];
        case 'amber': return ['#451A03', '#78350F'];
        case 'green': return ['#052E16', '#14532D'];
        default: return ['#3B0764', '#6B21A8'];
      }
    } else {
      switch (baseColor) {
        case 'purple': return ['#F3E8FF', '#E9D5FF'];
        case 'blue': return ['#DBEAFE', '#BFDBFE'];
        case 'amber': return ['#FEF3C7', '#FDE68A'];
        case 'green': return ['#DCFCE7', '#BBF7D0'];
        default: return ['#F3E8FF', '#E9D5FF'];
      }
    }
  };

  return (
    <ThemedSafeAreaView 
      style={[
        styles.container, 
        { backgroundColor: 'transparent' }
      ]} 
      edges={['top']}
    >
      <StatusBar style={isDark ? "light" : "dark"} />
      
      <View style={[
        styles.modalContainer,
        { 
          backgroundColor: getColor('#fff', '#151718'),
          shadowColor: "#000",
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: isDark ? 0.3 : 0.1,
          shadowRadius: 8,
          elevation: 10
        }
      ]}>
        <ThemedView style={[
          styles.header, 
          { 
            backgroundColor: getColor('#fff', '#151718'),
            borderBottomColor: getColor('#e5e7eb', '#2B2F31') 
          }
        ]}>
          <ThemedText style={[
            styles.headerTitle,
            { color: getColor('#1F2937', '#ECEDEE') }
          ]}>{t('vipDetail.title' as any) as string}</ThemedText>
          
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={goBack}
          >
            <IconSymbol name="xmark" size={20} color={getColor('#6B7280', '#9BA1A6')} />
          </TouchableOpacity>
        </ThemedView>
        
        <ScrollView 
          style={[
            styles.scrollView,
            { backgroundColor: getColor('#fff', '#151718') }
          ]}
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
          bounces={true}
        >
            <View style={[
              styles.membershipStatus,
              { 
                backgroundColor: getColor('#f3f4f6', '#26292B'),
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: isDark ? 0.3 : 0.05,
                shadowRadius: 3,
                elevation: isDark ? 4 : 2
              }
            ]}>
              <View style={styles.membershipStatusRow}>
                <View style={styles.membershipIconContainer}>
                  <IconSymbol name="checkmark.circle.fill" size={18} color={getColor('#10B981', '#22C55E')} />
                </View>
                <Text style={[
                  styles.membershipStatusText,
                  { color: getColor('#1F2937', '#ECEDEE') }
                ]}>
                  {t('vipDetail.vipStatus', { 
                    planName: getCurrentPlanName(),
                    defaultValue: ''
                  })}
                </Text>
              </View>
              <View style={styles.membershipExpiryRow}>
                <Text style={[
                  styles.membershipExpiry,
                  { color: getColor('#6B7280', '#9BA1A6') }
                ]}>{getSubscriptionEndDate()}</Text>
              </View>
            </View>
            
            <View style={styles.vipFeaturesContainer}>
              <Text style={[
                styles.vipFeaturesTitle,
                { color: getColor('#1F2937', '#ECEDEE') }
              ]}>{t('vipDetail.featuresTitle' as any) as string}</Text>
              
              <VipFeatureItem 
                icon="doc.text" 
                iconBgColor={getIconGradientColors('purple', isDark)[0]} 
                iconColor={isDark ? '#C084FC' : '#9333EA'}
                title={t('vipDetail.features.unlimitedTemplates.title' as any) as string}
                description={t('vipDetail.features.unlimitedTemplates.description' as any) as string} 
                isDark={isDark}
              />
              
              <VipFeatureItem 
                icon="cpu" 
                iconBgColor={getIconGradientColors('blue', isDark)[0]} 
                iconColor={isDark ? '#60A5FA' : '#3B82F6'}
                title={t('vipDetail.features.advancedModel.title' as any) as string}
                description={t('vipDetail.features.advancedModel.description' as any) as string} 
                isDark={isDark}
              />
              
              <VipFeatureItem 
                icon="bolt" 
                iconBgColor={getIconGradientColors('amber', isDark)[0]} 
                iconColor={isDark ? '#FBBF24' : '#D97706'}
                title={t('vipDetail.features.priorityProcessing.title' as any) as string}
                description={t('vipDetail.features.priorityProcessing.description' as any) as string} 
                isDark={isDark}
              />
              
              <VipFeatureItem 
                icon="cloud.fill" 
                iconBgColor={getIconGradientColors('green', isDark)[0]} 
                iconColor={isDark ? '#34D399' : '#10B981'}
                title={t('vipDetail.features.cloudSync.title' as any) as string}
                description={t('vipDetail.features.cloudSync.description' as any) as string} 
                isLast={true}
                isDark={isDark}
              />
            </View>
            
            {/* 用户评价横向滚动区域 */}
            <View style={styles.reviewsSection}>
              <Text style={[
                styles.reviewsSectionTitle,
                { color: getColor('#1F2937', '#ECEDEE') }
              ]}>{t('payment.userReviews' as any) as string}</Text>
              
              <ScrollView 
                ref={reviewsScrollViewRef}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.reviewsScrollContainer}
                style={styles.reviewsScrollView}
                bounces={true}
                decelerationRate="fast"
                snapToInterval={280}
                snapToAlignment="start"
                onScrollBeginDrag={handleUserScrollBegin}
              >
                <UserReviewItem
                  title={t('payment.reviews.review1.title' as any) as string}
                  content={t('payment.reviews.review1.content' as any) as string}
                  author={t('payment.reviews.review1.author' as any) as string}
                  rating={5}
                  isDark={isDark}
                />
                <UserReviewItem
                  title={t('payment.reviews.review2.title' as any) as string}
                  content={t('payment.reviews.review2.content' as any) as string}
                  author={t('payment.reviews.review2.author' as any) as string}
                  rating={5}
                  isDark={isDark}
                />
                <UserReviewItem
                  title={t('payment.reviews.review3.title' as any) as string}
                  content={t('payment.reviews.review3.content' as any) as string}
                  author={t('payment.reviews.review3.author' as any) as string}
                  rating={4}
                  isDark={isDark}
                />
                <UserReviewItem
                  title={t('payment.reviews.review5.title' as any) as string}
                  content={t('payment.reviews.review5.content' as any) as string}
                  author={t('payment.reviews.review5.author' as any) as string}
                  rating={5}
                  isDark={isDark}
                />
                <UserReviewItem
                  title={t('payment.reviews.review6.title' as any) as string}
                  content={t('payment.reviews.review6.content' as any) as string}
                  author={t('payment.reviews.review6.author' as any) as string}
                  rating={5}
                  isDark={isDark}
                />
                <UserReviewItem
                  title={t('payment.reviews.review4.title' as any) as string}
                  content={t('payment.reviews.review4.content' as any) as string}
                  author={t('payment.reviews.review4.author' as any) as string}
                  rating={4}
                  isDark={isDark}
                />
                <UserReviewItem
                  title={t('payment.reviews.review7.title' as any) as string}
                  content={t('payment.reviews.review7.content' as any) as string}
                  author={t('payment.reviews.review7.author' as any) as string}
                  rating={5}
                  isDark={isDark}
                />
                <UserReviewItem
                  title={t('payment.reviews.review8.title' as any) as string}
                  content={t('payment.reviews.review8.content' as any) as string}
                  author={t('payment.reviews.review8.author' as any) as string}
                  rating={5}
                  isDark={isDark}
                />
                <UserReviewItem
                  title={t('payment.reviews.review9.title' as any) as string}
                  content={t('payment.reviews.review9.content' as any) as string}
                  author={t('payment.reviews.review9.author' as any) as string}
                  rating={5}
                  isDark={isDark}
                />
              </ScrollView>
            </View>
          </ScrollView>
        </View>
        
        {/* 更换订阅套餐按钮 - 固定在底部 */}
        <View style={[
          styles.bottomButtonContainer,
          { 
            backgroundColor: getColor('#fff', '#151718'),
            borderTopColor: getColor('#e5e7eb', '#2B2F31'),
          }
        ]}>
          <TouchableOpacity 
            style={[
              styles.changePlanButton,
              { 
                backgroundColor: getColor('#f8fafc', '#1f2937'),
                borderColor: getColor('#e2e8f0', '#374151'),
              }
            ]}
            activeOpacity={0.7}
            onPress={() => {
              log('[VipDetail] onPress 被调用，即将调用 handleChangePlan');
              handleChangePlan();
            }}
            onPressIn={() => log('[VipDetail] 按钮被按下 (onPressIn)')}
            onPressOut={() => log('[VipDetail] 按钮松开 (onPressOut)')}
            onLongPress={() => log('[VipDetail] 按钮长按 (onLongPress)')}
            hitSlop={{ top: 10, bottom: 10, left: 20, right: 20 }}
          >
            <View style={styles.changePlanButtonContent}>
              <IconSymbol name="repeat" size={16} color={getColor('#3B82F6', '#60A5FA')} />
              <Text style={[
                styles.changePlanButtonText,
                { color: getColor('#3B82F6', '#60A5FA') }
              ]}>{t('vipDetail.changePlan' as any) as string}</Text>
            </View>
          </TouchableOpacity>
        </View>
    </ThemedSafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  modalContainer: {
    flex: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
    marginTop: 0
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    position: 'relative'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  closeButton: {
    position: 'absolute',
    right: 16,
    justifyContent: 'center',
    alignItems: 'center',
    width: 40,
    height: 40,
    zIndex: 10
  },
  backButton: {
    padding: 8
  },
  scrollView: {
    flex: 1
  },
  scrollViewContent: {
    padding: 20,
    paddingBottom: 120 // 为底部固定按钮留出空间
  },
  membershipStatus: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 28,
  },
  membershipStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10
  },
  membershipIconContainer: {
    width: 26,
    height: 26,
    justifyContent: 'center',
    alignItems: 'center',
  },
  membershipStatusText: {
    fontSize: 17,
    fontWeight: '600',
    marginLeft: 10
  },
  membershipExpiryRow: {
    flexDirection: 'row',
    paddingLeft: 26
  },
  membershipExpiry: {
    fontSize: 15,
  },
  vipFeaturesContainer: {
    marginBottom: 30
  },
  vipFeaturesTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 20
  },
  vipFeatureItem: {
    flexDirection: 'row',
    paddingVertical: 20
  },
  vipFeatureItemWithBorder: {
    borderBottomWidth: 1
  },
  vipFeatureIconContainer: {
    width: 46,
    height: 46,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 18
  },
  vipFeatureContent: {
    flex: 1,
    justifyContent: 'center'
  },
  vipFeatureTitle: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 6
  },
  vipFeatureDescription: {
    fontSize: 14,
    lineHeight: 20
  },
  changePlanTextButton: {
    alignItems: 'center',
    paddingTop: 15,
    paddingBottom: 30,
    paddingHorizontal: 20,
    minHeight: 44, // 确保有足够的触摸高度
    justifyContent: 'center',
  },
  changePlanTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  changePlanText: {
    marginLeft: 5,
    fontSize: 15,
    fontWeight: '500'
  },
  // 底部按钮容器
  bottomButtonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingBottom: 34, // 为底部安全区域留出空间
    borderTopWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  changePlanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 1,
    minHeight: 48,
  },
  changePlanButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changePlanButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600'
  },
  // 用户评价区域样式
  reviewsSection: {
    marginBottom: 25
  },
  reviewsSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    paddingHorizontal: 4
  },
  reviewsScrollView: {
    flexGrow: 0
  },
  reviewsScrollContainer: {
    paddingLeft: 4,
    paddingRight: 20
  },
  reviewCard: {
    width: 260,
    marginRight: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)'
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8
  },
  reviewTitle: {
    fontSize: 15,
    fontWeight: '600',
    flex: 1,
    marginRight: 8
  },
  starsContainer: {
    flexDirection: 'row',
    gap: 2
  },
  reviewContent: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 10
  },
  reviewAuthor: {
    fontSize: 12,
    fontWeight: '500'
  }
}); 