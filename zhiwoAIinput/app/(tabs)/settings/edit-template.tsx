import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Text, View, ScrollView } from "react-native";
import { StatusBar } from "expo-status-bar";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { FullScreenEditor } from "@/components/ui/FullScreenEditor";
import { router, useLocalSearchParams } from "expo-router";
import { useAppTheme } from "@/hooks/useAppTheme";
import { useTemplates } from "@/hooks/useTemplates";
import { Template } from "@/store/slices/templateSlice";
import { useTranslation } from "react-i18next";
import { log, error as logError } from "@/services/logService";
import Toast from 'react-native-toast-message';
import { getToastOptions } from '@/utils/toastConfig';

/**
 * 编辑风格模板页面
 * 允许用户编辑已有的自定义风格模板
 */
export default function EditTemplateScreen() {
  const params = useLocalSearchParams();
  const templateId = params.id as string;
  const theme = useAppTheme();
  const isDark = theme === "dark";

  // 使用模板钩子获取模板数据
  const {
    templates,
    saveTemplate: saveTemplateHandler,
    deleteTemplate: deleteTemplateHandler,
    loading: templatesLoading,
    refreshTemplates,
  } = useTemplates();

  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [prompt, setPrompt] = useState("");
  const [loading, setLoading] = useState(true);
  const [isSystem, setIsSystem] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isDefault, setIsDefault] = useState(false);
  const [titleError, setTitleError] = useState("");
  const [descriptionError, setDescriptionError] = useState("");
  const [promptError, setPromptError] = useState("");
  const [showFullScreenEditor, setShowFullScreenEditor] = useState(false);

  // 使用翻译钩子
  const { t } = useTranslation();

  // 加载模板数据
  useEffect(() => {
    const loadTemplateData = async () => {
      try {
        log("加载模板ID:", templateId); // 调试日志

        // 当模板数据加载完成后，查找对应ID的模板
        if (!templatesLoading && templates.length > 0) {
          const template = templates.find((t) => t.id === templateId);

          if (template) {
            // 找到模板，设置表单数据
            setName(template.title);
            setDescription(template.description);
            setPrompt(template.prompt);
            setIsSystem(template.isSystem);
            setIsDefault(!!template.isDefault);
            log("模板数据加载成功");
          } else {
            // 没有找到对应的模板，toast提示并返回上一页
            Toast.show(getToastOptions(t('templates.templateNotFound', '未找到指定的模板'), 'error'));
            router.back();
            return;
          }

          setLoading(false);
        }
      } catch (error) {
        logError("加载模板数据失败:", error);
        Toast.show(getToastOptions(t('templates.loadTemplateFailed', '加载模板数据失败，请重试'), 'error'));
        router.back();
        setLoading(false);
      }
    };

    loadTemplateData();
  }, [templateId, templates, templatesLoading]);

  // 导航回模板管理页面
  const navigateBack = () => {
    router.back();
  };

  // 保存模板
  const handleSaveTemplate = async () => {
    setIsSaving(true);
    try {
      // 系统模板不允许编辑
      if (isSystem) {
        Alert.alert(t("common.tip"), t("templates.systemTemplateCannotEdit"));
        setIsSaving(false);
        return;
      }

      // 表单验证
      let hasError = false;

      if (!name.trim()) {
        setTitleError(t("templates.templateNameRequired", "请输入模板名称"));
        hasError = true;
      } else {
        setTitleError("");
      }

      if (!prompt.trim()) {
        setPromptError(t("templates.promptRequired", "请输入模板提示词"));
        hasError = true;
      } else {
        setPromptError("");
      }

      if (hasError) {
        setIsSaving(false);
        return;
      }

      // 获取原始模板以保留颜色属性
      const originalTemplate = templates.find((t) => t.id === templateId);
      if (!originalTemplate) {
        Toast.show(getToastOptions(t('templates.templateNotFound', '未找到指定的模板'), 'error'));
        router.back();
        setIsSaving(false);
        return;
      }

      // 创建更新后的模板对象
      const updatedTemplate: Template = {
        id: templateId,
        title: name.trim(),
        description: description.trim(),
        prompt: prompt.trim(),
        isSystem: false,
        isDefault: isDefault,
        color: originalTemplate.color,
        borderColor: originalTemplate.borderColor,
        backgroundColor: originalTemplate.backgroundColor,
      };

      // 使用Redux保存模板
      const success = await saveTemplateHandler(updatedTemplate);

      if (success) {
        Toast.show(getToastOptions(t('templates.updateSuccess'), 'success'));
        await refreshTemplates?.();
        router.back();
      } else {
        Toast.show(getToastOptions(t('templates.saveFailed'), 'error'));
        await refreshTemplates?.();
        router.back();
      }
    } catch (error) {
      logError("保存模板失败:", error);
      Toast.show(getToastOptions(t('templates.saveFailed') + ', ' + t('common.retry'), 'error'));
      await refreshTemplates?.();
      router.back();
    } finally {
      setIsSaving(false);
    }
  };

  const toggleIsDefault = () => {
    setIsDefault(!isDefault);
  };

  // 如果模板数据正在加载中，显示加载界面
  if (loading || templatesLoading) {
    return (
      <SafeAreaView
        style={[styles.container, isDark && { backgroundColor: "#151718" }]}
        edges={["top"]}
      >
        <StatusBar style={isDark ? "light" : "dark"} />
        <View
          style={[
            styles.header,
            isDark && {
              backgroundColor: "#151718",
              borderBottomColor: "#2B2F31",
            },
          ]}
        >
          <View style={styles.headerLeft}>
            <TouchableOpacity style={styles.backButton} onPress={navigateBack}>
              <IconSymbol
                name="chevron.left"
                size={20}
                color={isDark ? "#9BA1A6" : "#6B7280"}
              />
            </TouchableOpacity>
            <Text style={[styles.headerTitle, isDark && { color: "#ECEDEE" }]}>
              {t("templates.editTemplate")}
            </Text>
          </View>
          <TouchableOpacity
            style={[
              styles.headerSaveButton,
              isDark && styles.headerSaveButtonDark,
              { opacity: 0.5 },
            ]}
            disabled={true}
          >
            <Text style={styles.headerSaveButtonText}>{t("common.save")}</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={isDark ? "#3B82F6" : "#2563EB"}
          />
          <Text style={[styles.loadingText, isDark && { color: "#9BA1A6" }]}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, isDark && styles.containerDark]}
      edges={["top"]}
    >
      <StatusBar style={isDark ? "light" : "dark"} />

      {/* 头部导航 */}
      <View style={[styles.header, isDark && styles.headerDark]}>
        <View style={styles.headerLeft}>
          <TouchableOpacity style={styles.backButton} onPress={navigateBack}>
            <IconSymbol
              name="chevron.left"
              size={20}
              color={isDark ? "#9BA1A6" : "#6B7280"}
            />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, isDark && styles.headerTitleDark]}>
            {t("templates.editTemplate")}
          </Text>
        </View>
        <TouchableOpacity
          style={[
            styles.headerSaveButton,
            isDark && styles.headerSaveButtonDark,
          ]}
          onPress={handleSaveTemplate}
          disabled={isSaving}
        >
          {isSaving ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text style={styles.headerSaveButtonText}>{t("common.save")}</Text>
          )}
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 88 : 0}
      >
        <ScrollView
          style={[styles.scrollView, isDark && styles.scrollViewDark]}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          automaticallyAdjustKeyboardInsets={Platform.OS === 'ios'}
        >
          <View style={styles.formContainer}>
            {isSystem && (
              <View
                style={[
                  styles.systemWarning,
                  isDark && styles.systemWarningDark,
                ]}
              >
                <IconSymbol
                  name="exclamationmark.triangle"
                  size={18}
                  color={isDark ? "#F59E0B" : "#D97706"}
                />
                <Text
                  style={[
                    styles.systemWarningText,
                    isDark && styles.systemWarningTextDark,
                  ]}
                >
                  {t("templates.systemTemplateWarning")}
                </Text>
              </View>
            )}

            {/* 标题输入 */}
            <View style={styles.formGroup}>
              <Text style={[styles.label, isDark && { color: "#9BA1A6" }]}>
                {t("templates.templateName")}
              </Text>
              <TextInput
                style={[
                  styles.input,
                  isDark && styles.inputDark,
                  titleError && styles.inputError,
                  isSystem && styles.inputDisabled,
                ]}
                value={name}
                onChangeText={setName}
                placeholder={t("templates.templateNamePlaceholder")}
                placeholderTextColor={isDark ? "#6B7280" : "#9CA3AF"}
                maxLength={50}
                editable={!isSystem}
              />
              {titleError && <Text style={styles.errorText}>{titleError}</Text>}
            </View>

            {/* 描述输入 */}
            <View style={styles.formGroup}>
              <Text style={[styles.label, isDark && { color: "#9BA1A6" }]}>
                {t("templates.description")}
              </Text>
              <TextInput
                style={[
                  styles.input,
                  styles.textArea,
                  isDark && styles.inputDark,
                  descriptionError && styles.inputError,
                  isSystem && styles.inputDisabled,
                ]}
                value={description}
                onChangeText={setDescription}
                placeholder={t("templates.descriptionPlaceholder")}
                placeholderTextColor={isDark ? "#6B7280" : "#9CA3AF"}
                multiline
                maxLength={200}
                editable={!isSystem}
              />
              {descriptionError && (
                <Text style={styles.errorText}>{descriptionError}</Text>
              )}
              <Text
                style={[
                  styles.characterCount,
                  isDark && styles.characterCountDark,
                ]}
              >
                {description.length}/200
              </Text>
            </View>

            {/* 提示词输入 */}
            <View style={styles.formGroup}>
              <View style={styles.formHeader}>
                <Text style={[styles.label, isDark && styles.labelDark]}>
                  {t("templates.templatePrompt")}
                </Text>
                <TouchableOpacity
                  style={[
                    styles.expandButton,
                    isDark && { backgroundColor: '#2B2F31' },
                    isSystem && { opacity: 0.5 }
                  ]}
                  onPress={() => setShowFullScreenEditor(true)}
                  disabled={isSystem}
                >
                  <IconSymbol
                    name="arrow.up.left.and.arrow.down.right"
                    size={16}
                    color={isDark ? '#9BA1A6' : '#6B7280'}
                  />
                </TouchableOpacity>
              </View>

              <TextInput
                style={[
                  styles.input,
                  styles.textArea,
                  styles.promptInput,
                  isDark && styles.inputDark,
                  promptError && styles.inputError,
                  isSystem && styles.inputDisabled,
                ]}
                value={prompt}
                onChangeText={setPrompt}
                placeholder={t("templates.templatePromptPlaceholder")}
                placeholderTextColor={isDark ? "#6B7280" : "#9CA3AF"}
                multiline
                maxLength={1000}
                editable={!isSystem}
                scrollEnabled={true}
              />
              {promptError && (
                <Text style={styles.errorText}>{promptError}</Text>
              )}
              <Text
                style={[
                  styles.characterCount,
                  isDark && styles.characterCountDark,
                ]}
              >
                {prompt.length}/1000
              </Text>
            </View>

            {/* 设为默认选项 */}
            <View style={styles.formGroup}>
              <TouchableOpacity
                style={styles.setDefaultButton}
                onPress={toggleIsDefault}
                disabled={isSystem}
              >
                <IconSymbol
                  name={isDefault ? "checkmark.circle.fill" : "circle"}
                  size={20}
                  color={isDefault ? "#3B82F6" : isDark ? "#9BA1A6" : "#9CA3AF"}
                />
                <Text
                  style={[
                    styles.setDefaultText,
                    isDefault && { color: "#3B82F6" },
                    isDark && !isDefault && styles.setDefaultTextDark,
                  ]}
                >
                  {t("templates.setDefaultTemplate")}
                </Text>
              </TouchableOpacity>
            </View>

            {/* 保存按钮 */}
            <TouchableOpacity
              style={[
                styles.saveButton,
                isSaving && styles.saveButtonDisabled,
                isDark && styles.saveButtonDark,
              ]}
              onPress={handleSaveTemplate}
              disabled={isSaving}
            >
              {isSaving ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text style={styles.saveButtonText}>{t("common.save")}</Text>
              )}
            </TouchableOpacity>

            {/* 删除按钮 */}
            <TouchableOpacity
              style={[styles.deleteButton, isDark && styles.deleteButtonDark]}
              onPress={() => {
                Alert.alert(
                  t("templates.confirmDeleteTitle"),
                  t("templates.confirmDeleteMessage"),
                  [
                    {
                      text: t("common.cancel"),
                      style: "cancel",
                    },
                    {
                      text: t("common.delete"),
                      style: "destructive",
                      onPress: async () => {
                        // 系统模板不允许删除
                        if (isSystem) {
                          Alert.alert(
                            t("common.tip"),
                            t("templates.systemTemplateCannotDelete")
                          );
                          return;
                        }
                        // 使用Redux删除模板
                        const success = await deleteTemplateHandler(templateId);
                        if (success) {
                          Toast.show(getToastOptions(t('templates.deleteSuccess'), 'success'));
                          await refreshTemplates?.();
                          // router.replace('/settings/templates');
                        } else {
                          Toast.show(getToastOptions(t('templates.deleteFailed'), 'error'));
                          await refreshTemplates?.();
                          // router.replace('/settings/templates');
                        }
                      },
                    },
                  ]
                );
              }}
              disabled={isSaving || isSystem}
            >
              <Text style={styles.deleteButtonText}>
                {" "}
                {t("templates.deleteConfirmButton")}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* 全屏编辑器 */}
      <FullScreenEditor
        visible={showFullScreenEditor}
        value={prompt}
        onChangeText={setPrompt}
        onClose={() => setShowFullScreenEditor(false)}
        title={t('templates.editPrompt')}
        placeholder={t('templates.promptPlaceholder')}
        maxLength={1000}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  containerDark: {
    backgroundColor: "#151718",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  headerDark: {
    backgroundColor: "#151718",
    borderBottomColor: "#2B2F31",
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 8,
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#111827",
  },
  headerTitleDark: {
    color: "#ECEDEE",
  },
  saveText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#3B82F6",
  },
  headerSaveButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    backgroundColor: "#3B82F6",
  },
  headerSaveButtonDark: {
    backgroundColor: "#2563EB",
  },
  headerSaveButtonText: {
    color: "white",
    fontWeight: "500",
    fontSize: 15,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewDark: {
    backgroundColor: "#151718",
  },
  formContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#111827",
    marginBottom: 12,
  },
  labelDark: {
    color: "#ECEDEE",
  },
  labelHint: {
    fontSize: 12,
    fontWeight: "normal",
    color: "#6B7280",
    marginLeft: 12,
  },
  input: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#E5E7EB",
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    lineHeight: 24,
    color: "#111827",
  },
  inputDark: {
    backgroundColor: "#1E2122",
    borderColor: "#2B2F31",
    color: "#ECEDEE",
  },
  textArea: {
    minHeight: 120,
    textAlignVertical: "top",
  },
  promptInput: {
    minHeight: 160,
    maxHeight: 200, // 设置最大高度
  },
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    marginBottom: 12, // 与输入框保持间距
  },
  expandButtonText: {
    fontSize: 14,
    marginLeft: 4,
    color: '#6B7280',
  },
  inputError: {
    borderColor: "#EF4444",
  },
  errorText: {
    color: "#EF4444",
    fontSize: 14,
    marginTop: 4,
  },
  characterCount: {
    fontSize: 12,
    color: "#6B7280",
    textAlign: "right",
    marginTop: 4,
  },
  characterCountDark: {
    color: "#9BA1A6",
  },
  setDefaultButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
  },
  setDefaultText: {
    fontSize: 16,
    color: "#6B7280",
    marginLeft: 10,
  },
  setDefaultTextDark: {
    color: "#9BA1A6",
  },
  saveButton: {
    backgroundColor: "#3B82F6",
    borderRadius: 8,
    padding: 14,
    alignItems: "center",
    marginTop: 20,
  },
  saveButtonDark: {
    backgroundColor: "#2563EB",
  },
  saveButtonDisabled: {
    backgroundColor: "#93C5FD",
  },
  saveButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  deleteButton: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#EF4444",
    borderRadius: 8,
    padding: 14,
    alignItems: "center",
    marginTop: 12,
  },
  deleteButtonDark: {
    backgroundColor: "#1E2122",
  },
  deleteButtonText: {
    color: "#EF4444",
    fontSize: 16,
    fontWeight: "600",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: "#6B7280",
  },
  inputDisabled: {
    opacity: 0.6,
  },
  systemWarning: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FEF3C7",
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  systemWarningDark: {
    backgroundColor: "#433619",
  },
  systemWarningText: {
    fontSize: 14,
    color: "#92400E",
    marginLeft: 8,
    flex: 1,
  },
  systemWarningTextDark: {
    color: "#F59E0B",
  },
  formHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
});
