import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, Alert, Modal, LayoutChangeEvent, TouchableWithoutFeedback, ActivityIndicator, View, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ScrollView, Pressable } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { router, useFocusEffect } from 'expo-router';
import { Swipeable, GestureHandlerRootView } from 'react-native-gesture-handler';
import { Animated as RNAnimated } from "react-native";
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTemplates } from '@/hooks/useTemplates';
import { Template } from '@/store/slices/templateSlice';
import { useAuth } from '@/hooks/useAuth';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { useTranslation } from 'react-i18next';
import VipUpgradeModal from '@/components/VipUpgradeModal';
import { generateUUID } from '@/utils/helpers';
import { loadTemplates } from '@/store/slices/templateSlice';
import { useCallback } from 'react';
import { getTemplateDisplayName, getTemplateDisplayDescription } from '@/utils/templateI18nUtils';
import { log } from '@/services/logService';
import Toast from 'react-native-toast-message';
import { getToastOptions } from '@/utils/toastConfig';
import { getSystemTemplatesFromLocal } from '@/services/templateSyncService';
import { getUserTemplates as getUserTemplatesLocal } from '@/services/templateService';

/**
 * 风格模板管理页面
 * 显示系统模板和用户自定义模板列表
 */
export default function TemplatesScreen() {
  const { t } = useTranslation();
  const theme = useAppTheme();
  const isDark = theme === 'dark';
  const dispatch = useDispatch<AppDispatch>();
  
  // 使用模板钩子获取模板数据
  const { 
    templates, 
    systemTemplates, 
    userTemplates, 
    defaultTemplate,
    setDefaultTemplate: setDefaultTemplateHandler,
    deleteTemplate: deleteTemplateHandler,
    saveTemplate: saveTemplateHandler,
    refreshTemplates,
    loading,
    error 
  } = useTemplates();

  // 本地兜底模板state
  const [localTemplates, setLocalTemplates] = useState<Template[]>([]);
  const [localLoading, setLocalLoading] = useState(true);

  // 首次挂载时直接读取本地缓存
  useEffect(() => {
    (async () => {
      try {
        setLocalLoading(true);
        const system = await getSystemTemplatesFromLocal();
        const user = await getUserTemplatesLocal();
        setLocalTemplates([...(system || []), ...(user || [])]);
      } catch (e) {
        setLocalTemplates([]);
      } finally {
        setLocalLoading(false);
      }
    })();
  }, []);

  // 首次挂载时加载模板（本地优先）
  useEffect(() => {
    log('[TemplatesScreen] 首次挂载，加载模板数据');
    dispatch(loadTemplates());
  }, [dispatch]);

  // 优先用redux templates，有则用redux，否则用本地localTemplates
  const displayTemplates = templates.length > 0 ? templates : localTemplates;
  const displayLoading = loading && displayTemplates.length === 0 && localLoading;

  // 使用Auth钩子获取登录和VIP检查功能
  const { 
    checkIsLoggedIn, 
    loginPromptVisible, 
    loginPromptTitle, 
    loginPromptMessage, 
    hideLoginPrompt, 
    LoginPromptComponent,
    showLoginModal
  } = useAuth();
  
  // 获取VIP状态
  const { isVIP } = useSelector((state: RootState) => state.auth);

  // 快捷操作菜单状态
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [actionItemId, setActionItemId] = useState<string | null>(null);
  const [actionButtonPosition, setActionButtonPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);

  // 添加打开状态跟踪
  const [openItems, setOpenItems] = useState<{
    [key: string]: boolean;
  }>({});

  // 创建引用来跟踪当前打开的Swipeable和所有Swipeable实例
  const openedSwipeableRef = useRef<string | null>(null);
  const swipeableRefs = useRef<{ [key: string]: any }>({});

  // VIP升级场景枚举
  enum VipUpgradeScene {
    TEMPLATE_LIMIT = 'template_limit', // 复制模板超出上限
    VIP_TEMPLATE_DEFAULT = 'vip_template_default', // 设置VIP模板为默认
    VIP_TEMPLATE_ACCESS = 'vip_template_access' // 访问VIP模板
  }

  // VIP升级Modal状态
  const [vipUpgradeModalVisible, setVipUpgradeModalVisible] = useState(false);
  const [vipUpgradeScene, setVipUpgradeScene] = useState<VipUpgradeScene | null>(null);
  const [selectedVipTemplate, setSelectedVipTemplate] = useState<Template | null>(null);

  // 关闭所有打开的滑动条目
  const closeSwipeable = () => {
    const openedItemId = openedSwipeableRef.current;
    if (openedItemId && swipeableRefs.current[openedItemId]) {
      swipeableRefs.current[openedItemId].close();
      openedSwipeableRef.current = null;
    }
  };

  // 导航回设置页面
  const navigateBack = () => {
    router.back();
  };

  // 导航到新建模板页面，添加登录和VIP检查
  const navigateToNewTemplate = () => {
    // 检查用户是否已登录
    if (!checkIsLoggedIn({
      showPrompt: true,
      title: t('templates.createTemplate'),
      message: t('templates.loginToCreateTemplate')
    })) {
      return;
    }
    
    // 检查非VIP用户是否已达到自定义模板上限
    if (!isVIP && userTemplates.length >= 1) {
      setVipUpgradeScene(VipUpgradeScene.TEMPLATE_LIMIT);
      setVipUpgradeModalVisible(true);
      return;
    }
    
    // 如果检查通过，则导航到新建模板页面
    router.push('/new-template-modal');
  };

  // 导航到编辑模板页面
  const handleEditTemplate = (template: Template) => {
    // 隐藏快速操作菜单
    setShowQuickActions(false);
    // 关闭可能打开的swipeable
    if (openedSwipeableRef.current) {
      closeSwipeable();
    }
    // 导航到编辑模板页面，使用字符串格式的模板ID
    router.push(`/settings/edit-template?id=${encodeURIComponent(template.id)}`);
  };

  // 设置默认模板
  const handleSetDefaultTemplate = async (template: Template) => {
    // 隐藏快速操作菜单
    setShowQuickActions(false);
    
    // 检查VIP模板的权限
    if (template.isVipOnly && !isVIP) {
      // 设置选中的VIP模板信息和场景
      setSelectedVipTemplate(template);
      setVipUpgradeScene(VipUpgradeScene.VIP_TEMPLATE_DEFAULT);
      // 显示VIP升级Modal
      setVipUpgradeModalVisible(true);
      return;
    }
    
    // 获取模板显示名称
    const templateForI18n = {
      id: template.id,
      name: template.title,
      description: template.description,
      name_key: template.name_key,
      description_key: template.description_key,
      prompt_text: template.prompt,
      is_system: template.isSystem
    };
    const displayName = getTemplateDisplayName(templateForI18n, t);
    
    // 直接设置默认模板
    const success = await setDefaultTemplateHandler(template.id);
    
    if (success) {
      // 关闭快捷操作菜单
      hideQuickActions();
      // 显示成功提示
      Toast.show(getToastOptions(t('templates.setDefaultSuccess', { title: displayName }), 'success'));
    } else {
      Toast.show(getToastOptions(t('templates.setDefaultFailed'), 'error'));
    }
  };

  // 删除模板
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const handleDeleteTemplate = async (template: Template) => {
    // 隐藏快速操作菜单
    setShowQuickActions(false);
    // 确认删除
    Alert.alert(
      t('templates.deleteTemplate'),
      t('templates.deleteTemplateDesc'),
      [
        {
          text: t('common.cancel'),
          style: "cancel"
        },
        {
          text: t('common.confirm'),
          onPress: async () => {
            setDeletingId(template.id);
            if (template.isDefault) {
              const firstSystemTemplate = systemTemplates[0];
              if (firstSystemTemplate) {
                await setDefaultTemplateHandler(firstSystemTemplate.id);
              }
            }
            const success = await deleteTemplateHandler(template.id);
            await refreshTemplates();
            setDeletingId(null);
            if (success) {
              hideQuickActions();
              Toast.show(getToastOptions(t('templates.deleteSuccess'), 'success'));
            } else {
              Toast.show(getToastOptions(t('templates.deleteFailed'), 'error'));
            }
          }
        }
      ]
    );
  };

  // 显示快捷操作
  const handleShowQuickActions = (templateId: string, x?: number, y?: number) => {
    setActionItemId(templateId);
    if (x && y) {
      setActionButtonPosition({ x, y });
    }
    setShowQuickActions(true);
  };

  // 隐藏快捷操作
  const hideQuickActions = () => {
    setShowQuickActions(false);
    setActionButtonPosition(null);
  };

  // 获取VIP升级Modal的标题和描述
  const getVipUpgradeContent = () => {
    switch (vipUpgradeScene) {
      case VipUpgradeScene.TEMPLATE_LIMIT:
        return {
          title: t('templates.vipLimitTitle'),
          description: t('templates.vipLimitMessage')
        };
      case VipUpgradeScene.VIP_TEMPLATE_DEFAULT:
        if (selectedVipTemplate) {
          const templateName = getTemplateDisplayName({
            id: selectedVipTemplate.id,
            name: selectedVipTemplate.title,
            description: selectedVipTemplate.description,
            name_key: selectedVipTemplate.name_key,
            description_key: selectedVipTemplate.description_key,
            prompt_text: selectedVipTemplate.prompt,
            is_system: selectedVipTemplate.isSystem
          }, t);
          return {
            title: t('templates.vipTemplateTitle'),
            description: t('templates.vipTemplateMessage', { templateName })
          };
        }
        return {
          title: t('templates.vipTemplateTitle'),
          description: t('templates.vipTemplateFallback')
        };
      case VipUpgradeScene.VIP_TEMPLATE_ACCESS:
        return {
          title: t('templates.vipTemplateTitle'),
          description: t('templates.vipTemplateAccessMessage')
        };
      default:
        return {
          title: t('templates.vipLimitTitle'),
          description: t('templates.vipLimitMessage')
        };
    }
  };

  // 处理VIP升级Modal的升级按钮
  const handleVipUpgrade = () => {
    setVipUpgradeModalVisible(false);
    setVipUpgradeScene(null);
    setSelectedVipTemplate(null);
    
    // 检查是否已登录
    if (!checkIsLoggedIn({
      showPrompt: true,
      title: t('settings.prompts.upgradeVipTitle'),
      message: t('settings.prompts.upgradeVipMessage')
    })) {
      return;
    }
    
    // 已登录，直接导航到支付页面
    router.push('/payment');
  };

  // 渲染右侧操作菜单
  const renderRightActions = (template: Template, progress: any, dragX: any) => {
    // 动画转换更平滑
    const trans = dragX.interpolate({
      inputRange: [-210, 0],
      outputRange: [0, 210],
      extrapolate: 'clamp',
    });
    
    // 添加背景色渐变效果
    const editBackgroundColor = progress.interpolate({
      inputRange: [0, 1],
      outputRange: ['#60a5fa', '#3b82f6']
    });
    
    const copyBackgroundColor = progress.interpolate({
      inputRange: [0, 1],
      outputRange: ['#34d399', '#10b981']
    });
    
    const deleteBackgroundColor = progress.interpolate({
      inputRange: [0, 1],
      outputRange: isDark ? ['#f87171', '#ef4444'] : ['#f87171', '#ef4444']
    });
    
    return (
      <RNAnimated.View
        style={[
          styles.rightActionsContainer,
          {
            transform: [{ translateX: trans }],
          }
        ]}>
        <RNAnimated.View 
          style={[
            styles.editButton, 
            { backgroundColor: editBackgroundColor }
          ]}
        >
          <TouchableOpacity
            style={{ width: '100%', height: '100%', justifyContent: 'center', alignItems: 'center' }}
            onPress={() => handleEditTemplate(template)}
          >
            <IconSymbol name="pencil" size={20} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>{t('templates.editButton')}</Text>
          </TouchableOpacity>
        </RNAnimated.View>
        
        <RNAnimated.View 
          style={[
            styles.copyButton, 
            { backgroundColor: copyBackgroundColor }
          ]}
        >
          <TouchableOpacity
            style={{ width: '100%', height: '100%', justifyContent: 'center', alignItems: 'center' }}
            onPress={() => handleCopySystemTemplate(template)}
          >
            <IconSymbol name="doc.on.doc" size={20} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>{t('templates.copyButton')}</Text>
          </TouchableOpacity>
        </RNAnimated.View>
        
        <RNAnimated.View 
          style={[
            styles.deleteButton, 
            { backgroundColor: deleteBackgroundColor }
          ]}
        >
          <TouchableOpacity
            style={{ width: '100%', height: '100%', justifyContent: 'center', alignItems: 'center' }}
            onPress={() => handleDeleteTemplate(template)}
            disabled={deletingId === template.id}
          >
            <IconSymbol name="trash" size={20} color="#FFFFFF" />
            <Text style={styles.actionButtonText}>{t('templates.deleteButton')}</Text>
          </TouchableOpacity>
        </RNAnimated.View>
      </RNAnimated.View>
    );
  };

  // 复制系统模板
  const handleCopySystemTemplate = async (template: Template) => {
    // 检查用户是否已登录
    const isLoggedIn = checkIsLoggedIn({
      showPrompt: true,
      title: t('templates.copyTemplate'),
      message: t('templates.loginToCopyTemplate')
    });
    
    // 如果未登录，则不继续操作
    if (!isLoggedIn) {
      return;
    }
    
    // 检查是否为VIP模板
    if (template.isVipOnly && !isVIP) {
      setVipUpgradeScene(VipUpgradeScene.VIP_TEMPLATE_ACCESS);
      setVipUpgradeModalVisible(true);
      return;
    }

    // 检查非VIP用户是否已达到自定义模板上限
    if (!isVIP && userTemplates.length >= 1) {
      setVipUpgradeScene(VipUpgradeScene.TEMPLATE_LIMIT);
      setVipUpgradeModalVisible(true);
      return;
    }
    
    // 确认复制
    Alert.alert(
      t('templates.copyTemplate'),
      t('templates.copyTemplateDesc'),
      [
        {
          text: t('common.cancel'),
          style: "cancel"
        },
        {
          text: t('common.confirm'),
          onPress: async () => {
            // 生成新的模板ID
            const newTemplateId = generateUUID();
            
            // 创建新模板对象
            // 先构造用于i18n的对象
            const templateForI18n = {
              id: template.id,
              name: template.title,
              description: template.description,
              name_key: template.name_key,
              description_key: template.description_key,
              prompt_text: template.prompt,
              is_system: template.isSystem
            };
            const newTemplate: Template = {
              ...template,
              id: newTemplateId,
              isSystem: false,
              isDefault: false, // 确保复制的模板不会自动成为默认模板
              // 关键：title/description用当前语言翻译，去除多语言key
              title: getTemplateDisplayName(templateForI18n, t),
              description: getTemplateDisplayDescription(templateForI18n, t),
              name_key: undefined,
              description_key: undefined
            };
            
            // 保存新模板
            const success = await saveTemplateHandler(newTemplate);
            if (success) {
              // 关闭快捷操作菜单
              hideQuickActions();
              // Toast 成功提示
              Toast.show(getToastOptions(t('templates.copySuccess'), 'success'));
              // 立即刷新模板列表，确保新模板可见
              await refreshTemplates();
              // 不再跳转到编辑页面，停留在模板管理页
            } else {
              // Toast 失败提示
              Toast.show(getToastOptions(t('templates.copyFailed'), 'error'));
            }
          }
        }
      ]
    );
  };

  // 渲染系统模板项
  const renderSystemTemplateItem = (template: Template) => (
    <TouchableOpacity 
      style={[
        styles.templateItem,
        isDark && styles.templateItemDark
      ]} 
      key={template.id}
      onLongPress={() => handleCopySystemTemplate(template)}
      delayLongPress={500}
    >
      <View style={styles.templateHeader}>
        <View style={styles.templateTitleContainer}>
          <View style={styles.systemBadge}>
            <Text style={styles.systemBadgeText}>{t('templates.systemLabel')}</Text>
          </View>
          {template.isVipOnly && (
            <View style={styles.vipBadge}>
              <IconSymbol name="crown" size={12} color="#F59E0B" />
              <Text style={styles.vipBadgeText}>VIP</Text>
            </View>
          )}
          {template.isDefault && (
            <View style={styles.defaultBadge}>
              <Text style={styles.defaultBadgeText}>{t('templates.defaultLabel')}</Text>
            </View>
          )}
          <Text style={[
            styles.templateTitle, 
            { marginLeft: 8 },
            isDark && styles.templateTitleDark
          ]}>{(() => {
            // 将Template转换为StyleTemplateFromDB格式以使用多语言工具函数
            const templateForI18n = {
              id: template.id,
              name: template.title,
              description: template.description,
              name_key: template.name_key,
              description_key: template.description_key,
              prompt_text: template.prompt,
              is_system: template.isSystem
            };
            return getTemplateDisplayName(templateForI18n, t);
          })()}</Text>
        </View>
      </View>
      <Text style={[
        styles.templateDescription,
        isDark && styles.templateDescriptionDark
      ]}>
        {(() => {
          // 将Template转换为StyleTemplateFromDB格式以使用多语言工具函数
          const templateForI18n = {
            id: template.id,
            name: template.title,
            description: template.description,
            name_key: template.name_key,
            description_key: template.description_key,
            prompt_text: template.prompt,
            is_system: template.isSystem
          };
          return getTemplateDisplayDescription(templateForI18n, t);
        })()}
      </Text>
      
      <View style={styles.templateFooter}>
        <TouchableOpacity 
          style={styles.setDefaultButton}
          onPress={() => handleSetDefaultTemplate(template)}
          disabled={template.isDefault}
        >
          <IconSymbol name={template.isDefault ? "checkmark.circle.fill" : "circle"} size={16} color={template.isDefault ? "#3B82F6" : isDark ? "#9BA1A6" : "#9CA3AF"} />
          <Text style={[
            styles.setDefaultButtonText, 
            template.isDefault && styles.setDefaultButtonTextActive,
            isDark && styles.setDefaultButtonTextDark
          ]}>
            {template.isDefault ? t('templates.defaultTemplate') : t('templates.setDefault')}
          </Text>
        </TouchableOpacity>
        <Text style={[
          styles.templateFooterHint,
          isDark && styles.templateFooterHintDark
        ]}>{t('templates.longPressToCopy')}</Text>
      </View>
    </TouchableOpacity>
  );

  // 渲染自定义模板项
  const renderCustomTemplateItem = (template: Template) => {
    const isOpen = openItems[template.id] || false;
    
    return (
      <View key={template.id} style={styles.swipeableContainer}>
        <Swipeable
          ref={(ref) => {
            if (ref) {
              swipeableRefs.current[template.id] = ref;
            }
          }}
          renderRightActions={(progress, dragX) => renderRightActions(template, progress, dragX)}
          onSwipeableOpen={() => {
            // 关闭之前打开的Swipeable
            if (openedSwipeableRef.current && openedSwipeableRef.current !== template.id) {
              closeSwipeable();
            }
            // 设置当前打开的Swipeable
            openedSwipeableRef.current = template.id;
            setOpenItems(prev => ({ ...prev, [template.id]: true }));
          }}
          onSwipeableClose={() => {
            setOpenItems(prev => ({ ...prev, [template.id]: false }));
            if (openedSwipeableRef.current === template.id) {
              openedSwipeableRef.current = null;
            }
          }}
        >
          <View 
            style={[
              styles.templateItem, 
              isOpen && styles.templateItemOpen,
              { borderWidth: 1, borderColor: isDark ? '#2B2F31' : '#f0f0f0', marginBottom: 0 },
              isDark && styles.templateItemDark
            ]}
          >
            <View style={styles.templateHeader}>
              <View style={styles.templateTitleContainer}>
                <View style={styles.customBadge}>
                  <Text style={styles.customBadgeText}>{t('templates.customLabel')}</Text>
                </View>
                {template.isSynced && isVIP && (
                  <View style={styles.syncedBadge}>
                    <IconSymbol name="checkmark.circle.fill" size={12} color="#10B981" />
                    <Text style={styles.syncedBadgeText}>{t('templates.syncedLabel', '已同步')}</Text>
                  </View>
                )}
                {template.isDefault && (
                  <View style={styles.defaultBadge}>
                    <Text style={styles.defaultBadgeText}>{t('templates.defaultLabel')}</Text>
                  </View>
                )}
                <Text style={[
                  styles.templateTitle, 
                  { marginLeft: 8 },
                  isDark && styles.templateTitleDark
                ]}>{(() => {
                  // 将Template转换为StyleTemplateFromDB格式以使用多语言工具函数
                  const templateForI18n = {
                    id: template.id,
                    name: template.title,
                    description: template.description,
                    name_key: template.name_key,
                    description_key: template.description_key,
                    prompt_text: template.prompt,
                    is_system: template.isSystem
                  };
                  return getTemplateDisplayName(templateForI18n, t);
                })()}</Text>
              </View>
              <View style={styles.templateActions}>
                <TouchableOpacity 
                  style={styles.templateActionButton} 
                  onPress={() => handleEditTemplate(template)}
                >
                  <IconSymbol name="pencil" size={16} color={isDark ? "#9BA1A6" : "#9CA3AF"} />
                </TouchableOpacity>
                <TouchableOpacity 
                  style={styles.templateActionButton} 
                  onPress={(event) => {
                    // 获取操作按钮的位置
                    const { pageX, pageY } = event.nativeEvent;
                    handleShowQuickActions(template.id, pageX, pageY);
                  }}
                >
                  <IconSymbol name="ellipsis" size={16} color={isDark ? "#9BA1A6" : "#9CA3AF"} />
                </TouchableOpacity>
              </View>
            </View>
            <Text style={[
              styles.templateDescription,
              isDark && styles.templateDescriptionDark
            ]}>
              {(() => {
                // 将Template转换为StyleTemplateFromDB格式以使用多语言工具函数
                const templateForI18n = {
                  id: template.id,
                  name: template.title,
                  description: template.description,
                  name_key: template.name_key,
                  description_key: template.description_key,
                  prompt_text: template.prompt,
                  is_system: template.isSystem
                };
                return getTemplateDisplayDescription(templateForI18n, t);
              })()}
            </Text>
            <View style={[
              styles.promptContainer,
              isDark && styles.promptContainerDark
            ]}>
              <Text style={[
                styles.promptText, 
                isDark && styles.promptTextDark
              ]} numberOfLines={2}>
                {template.prompt}
              </Text>
            </View>
            
            <View style={styles.templateFooter}>
              <TouchableOpacity 
                style={styles.setDefaultButton}
                onPress={() => handleSetDefaultTemplate(template)}
                disabled={template.isDefault}
              >
                <IconSymbol name={template.isDefault ? "checkmark.circle.fill" : "circle"} size={16} color={template.isDefault ? "#3B82F6" : isDark ? "#9BA1A6" : "#9CA3AF"} />
                <Text style={[
                  styles.setDefaultButtonText, 
                  template.isDefault && styles.setDefaultButtonTextActive,
                  isDark && styles.setDefaultButtonTextDark
                ]}>
                  {template.isDefault ? t('templates.defaultTemplate') : t('templates.setAsDefault')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </Swipeable>
      </View>
    );
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaView style={[
        styles.container,
        isDark && styles.containerDark
      ]} edges={['top']}>
        <StatusBar style={isDark ? "light" : "dark"} />

        {/* 登录提示组件 */}
        <LoginPromptComponent
          visible={loginPromptVisible}
          onClose={hideLoginPrompt}
          title={loginPromptTitle}
          message={loginPromptMessage}
          onLogin={showLoginModal}
        />

        {/* 头部导航（始终显示） */}
        <View style={[
          styles.header,
          isDark && styles.headerDark
        ]}>
          <View style={styles.headerLeft}>
            <TouchableOpacity style={styles.backButton} onPress={navigateBack}>
              <IconSymbol name="chevron.left" size={20} color={isDark ? "#9BA1A6" : "#6B7280"} />
            </TouchableOpacity>
            <Text style={[
              styles.headerTitle,
              isDark && styles.headerTitleDark
            ]}>{t('templates.templateManagement')}</Text>
          </View>
          <TouchableOpacity 
            style={styles.addButton}
            onPress={navigateToNewTemplate}
          >
            <IconSymbol name="plus" size={20} color={isDark ? "#ECEDEE" : "#111827"} />
          </TouchableOpacity>
        </View>

        {/* 内容区：loading时显示加载指示器，否则显示模板内容 */}
        {displayLoading ? (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <ActivityIndicator size="large" color={isDark ? '#1F6FEB' : '#6366F1'} />
            <Text style={{ marginTop: 12, color: isDark ? '#8B949E' : '#6B7280', fontSize: 14 }}>
              {t('settings.templateManagerLoading', '加载中...')}
            </Text>
          </View>
        ) : error && displayTemplates.length === 0 ? (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <Text style={{ color: isDark ? '#F87171' : '#EF4444', fontSize: 16, marginBottom: 8 }}>
              {t('settings.templateManagerLoadError', '加载失败，请检查网络或重试')}
            </Text>
            <TouchableOpacity onPress={refreshTemplates} style={{ padding: 10, backgroundColor: isDark ? '#26292B' : '#F3F4F6', borderRadius: 8 }}>
              <Text style={{ color: isDark ? '#3B82F6' : '#2563EB', fontWeight: 'bold' }}>{t('common.retry', '重试')}</Text>
            </TouchableOpacity>
          </View>
        ) : displayTemplates.length === 0 ? (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <Text style={{ color: isDark ? '#8B949E' : '#6B7280', fontSize: 16 }}>
              {t('settings.templateManagerNoData', '暂无模板数据')}
            </Text>
          </View>
        ) : (
          <>
            {/* 原有模板内容区（ScrollView等） */}
            <ScrollView 
              style={[
                styles.scrollView,
                isDark && styles.scrollViewDark
              ]} 
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.scrollViewContent}
              onScrollBeginDrag={() => closeSwipeable()}
            >
              {/* 自定义模板区域 */}
              <View style={styles.sectionContainer}>
                <Text style={[
                  styles.sectionTitle,
                  isDark && styles.sectionTitleDark
                ]}>{t('templates.customTemplate')}</Text>
                {displayTemplates.filter(t => !t.isSystem).length > 0 ? (
                  displayTemplates.filter(t => !t.isSystem).map(renderCustomTemplateItem)
                ) : (
                  <View style={[
                    styles.emptyState,
                    isDark && styles.emptyStateDark
                  ]}>
                    <Text style={[
                      styles.emptyStateText,
                      isDark && styles.emptyStateTextDark
                    ]}>{t('templates.noCustomTemplate')}</Text>
                  </View>
                )}
              </View>

              {/* 系统模板区域 */}
              <View style={styles.sectionContainer}>
                <Text style={[
                  styles.sectionTitle,
                  isDark && styles.sectionTitleDark
                ]}>{t('templates.systemTemplate')}</Text>
                {displayTemplates.filter(t => t.isSystem).map(renderSystemTemplateItem)}
              </View>

              {/* 添加模板按钮 */}
              <TouchableOpacity 
                style={[
                  styles.addTemplateButton,
                  isDark && styles.addTemplateButtonDark
                ]}
                onPress={navigateToNewTemplate}
              >
                <IconSymbol name="plus" size={16} color="#3B82F6" />
                <Text style={styles.addTemplateText}>{t('templates.addNewTemplate')}</Text>
              </TouchableOpacity>

              {/* 底部空间 */}
              <View style={styles.bottomSpace} />
            </ScrollView>

            {/* 快捷操作菜单 */}
            {showQuickActions && actionItemId && (
              <>
                {/* 添加背景遮罩层 */}
                <TouchableOpacity
                  style={styles.menuBackdrop}
                  activeOpacity={1}
                  onPress={hideQuickActions}
                />

                <View
                  style={[
                    styles.quickActionsOverlay,
                    isDark && styles.quickActionsOverlayDark,
                    actionButtonPosition
                      ? {
                          position: "absolute",
                          top: actionButtonPosition.y - 40,
                          left: actionButtonPosition.x - 100, // 向左移动使菜单居中显示
                          zIndex: 1002,
                        }
                      : {},
                  ]}
                >
                  <TouchableOpacity
                    style={styles.quickActionsButton}
                    onPress={() => handleEditTemplate(templates.find(t => t.id === actionItemId)!)}
                  >
                    <IconSymbol name="pencil" size={18} color="#3B82F6" />
                    <Text style={styles.quickActionsButtonText}>{t('templates.editButton')}</Text>
                  </TouchableOpacity>
                  <View style={[
                    styles.menuDivider,
                    isDark && styles.menuDividerDark
                  ]} />
                  
                  {/* 设为默认按钮 */}
                  <TouchableOpacity
                    style={styles.quickActionsButton}
                    onPress={() => {
                      setShowQuickActions(false);
                      const template = templates.find(t => t.id === actionItemId);
                      if (template && !template.isDefault) {
                        handleSetDefaultTemplate(template);
                      }
                    }}
                    disabled={templates.find(t => t.id === actionItemId)?.isDefault}
                  >
                    <IconSymbol 
                      name={templates.find(t => t.id === actionItemId)?.isDefault 
                        ? "checkmark.circle.fill" 
                        : "circle"} 
                      size={18} 
                      color={templates.find(t => t.id === actionItemId)?.isDefault 
                        ? "#3B82F6" 
                        : "#3B82F6"} 
                    />
                    <Text style={styles.quickActionsButtonText}>
                      {templates.find(t => t.id === actionItemId)?.isDefault 
                        ? t('templates.defaultTemplate') 
                        : t('templates.setAsDefault')}
                    </Text>
                  </TouchableOpacity>
                  <View style={[
                    styles.menuDivider,
                    isDark && styles.menuDividerDark
                  ]} />
                  
                  <TouchableOpacity
                    style={styles.quickActionsButton}
                    onPress={() => handleCopySystemTemplate(templates.find(t => t.id === actionItemId)!)}
                  >
                    <IconSymbol name="doc.on.doc" size={18} color="#3B82F6" />
                    <Text style={styles.quickActionsButtonText}>{t('templates.copyButton')}</Text>
                  </TouchableOpacity>
                  <View style={[
                    styles.menuDivider,
                    isDark && styles.menuDividerDark
                  ]} />
                  <TouchableOpacity
                    style={styles.quickActionsButton}
                    onPress={() => handleDeleteTemplate(templates.find(t => t.id === actionItemId)!)}
                    disabled={deletingId === actionItemId}
                  >
                    <IconSymbol name="trash" size={18} color="#EF4444" />
                    <Text style={styles.quickActionsButtonTextDelete}>{t('templates.deleteButton')}</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}

            {/* VIP升级Modal */}
            <VipUpgradeModal
              visible={vipUpgradeModalVisible}
              onClose={() => {
                setVipUpgradeModalVisible(false);
                setVipUpgradeScene(null);
                setSelectedVipTemplate(null);
              }}
              onUpgrade={handleVipUpgrade}
              title={getVipUpgradeContent().title}
              description={getVipUpgradeContent().description}
            />
          </>
        )}
      </SafeAreaView>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  containerDark: {
    backgroundColor: '#151718',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    minHeight: 58,
  },
  headerDark: {
    backgroundColor: '#151718',
    borderBottomColor: '#2B2F31',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 8,
    padding: 0,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  headerTitleDark: {
    color: '#ECEDEE',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewDark: {
    backgroundColor: '#151718',
  },
  scrollViewContent: {
    paddingBottom: 100,
  },
  sectionContainer: {
    paddingHorizontal: 16,
    marginTop: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
  },
  sectionTitleDark: {
    color: '#9BA1A6',
  },
  swipeableContainer: {
    marginBottom: 12,
    borderRadius: 12,
    overflow: 'hidden',
  },
  templateItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#f0f0f0',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  templateItemDark: {
    backgroundColor: '#1E2122',
    borderColor: '#2B2F31',
    shadowColor: "#000",
    shadowOpacity: 0.2,
  },
  templateItemOpen: {
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    borderRightWidth: 0,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  templateTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    flexShrink: 1,
    flexWrap: 'wrap',
  },
  templateTitleDark: {
    color: '#ECEDEE',
  },
  systemBadge: {
    backgroundColor: '#DBEAFE',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  systemBadgeText: {
    fontSize: 12,
    color: '#3B82F6',
    fontWeight: '500',
  },
  customBadge: {
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  customBadgeText: {
    fontSize: 12,
    color: '#D97706',
    fontWeight: '500',
  },
  vipBadge: {
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  vipBadgeText: {
    fontSize: 10,
    color: '#F59E0B',
    fontWeight: '600',
    marginLeft: 2,
  },
  templateActions: {
    flexDirection: 'row',
    gap: 8,
    flexShrink: 0,
    alignItems: 'center',
  },
  templateActionButton: {
    padding: 6,
    minWidth: 28,
    minHeight: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  templateDescription: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 12,
    lineHeight: 20,
  },
  templateDescriptionDark: {
    color: '#9BA1A6',
  },
  promptContainer: {
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 8,
  },
  promptContainerDark: {
    backgroundColor: '#26292B',
  },
  promptText: {
    fontSize: 12,
    color: '#6B7280',
    fontStyle: 'italic',
    lineHeight: 18,
  },
  promptTextDark: {
    color: '#9BA1A6',
  },
  addTemplateButton: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 16,
    padding: 12,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#3B82F6',
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addTemplateButtonDark: {
    backgroundColor: '#1E2122',
    borderColor: '#3B82F6',
  },
  addTemplateText: {
    color: '#3B82F6',
    fontWeight: '500',
    marginLeft: 8,
  },
  bottomSpace: {
    height: 80,
  },
  emptyState: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    marginBottom: 12,
  },
  emptyStateDark: {
    backgroundColor: '#26292B',
  },
  emptyStateText: {
    color: '#6B7280',
    fontSize: 14,
  },
  emptyStateTextDark: {
    color: '#9BA1A6',
  },
  // 快捷操作菜单样式
  quickActionsOverlay: {
    backgroundColor: "white",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1001,
    width: 120,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  quickActionsOverlayDark: {
    backgroundColor: "#26292B",
    borderColor: "#3B3F41",
  },
  quickActionsButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  quickActionsButtonText: {
    fontSize: 15,
    color: "#3B82F6",
    marginLeft: 10,
    fontWeight: "500",
  },
  quickActionsButtonTextDelete: {
    fontSize: 15,
    color: "#EF4444",
    marginLeft: 10,
    fontWeight: "500",
  },
  menuBackdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "transparent",
    zIndex: 1000,
  },
  menuDivider: {
    height: 1,
    backgroundColor: "#E5E7EB",
    width: "100%",
  },
  menuDividerDark: {
    backgroundColor: "#3B3F41",
  },
  swipeRightAction: {
    width: 80,
    flexDirection: 'row',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  rightActionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    height: '100%',
    width: 210, // 3个按钮的总宽度
  },
  editButton: {
    width: 70,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
  copyButton: {
    width: 70,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
  deleteButton: {
    width: 70,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 13,
    fontWeight: '500',
    marginTop: 5,
    textAlign: 'center',
  },
  templateTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
  },
  templateFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 8,
  },
  templateFooterHint: {
    fontSize: 12,
    color: '#6B7280',
    fontStyle: 'italic',
  },
  templateFooterHintDark: {
    color: '#9BA1A6',
  },
  defaultBadge: {
    backgroundColor: '#EBF5FF',
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
  },
  defaultBadgeText: {
    color: '#3B82F6',
    fontSize: 10,
    fontWeight: '500',
  },
  syncedBadge: {
    backgroundColor: '#ECFDF5',
    borderRadius: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  syncedBadgeText: {
    color: '#10B981',
    fontSize: 10,
    fontWeight: '500',
    marginLeft: 3,
  },
  setDefaultButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  setDefaultButtonText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  setDefaultButtonTextDark: {
    color: '#9BA1A6',
  },
  setDefaultButtonTextActive: {
    color: '#3B82F6',
    fontWeight: '500',
  },
  addButton: {
    marginRight: 8,
    padding: 0,
  },
}); 