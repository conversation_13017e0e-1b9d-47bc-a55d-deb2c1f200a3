import { 
  StyleSheet, 
  Modal, 
  Alert, 
  Image, 
  Pressable, 
  Platform, 
  Dimensions,
  Text, 
  View, 
  ScrollView, 
  TouchableOpacity, 
  AppState, 
  AppStateStatus,
  Switch
} from 'react-native';
import { useEffect, useRef, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { router } from 'expo-router';
import { IconSymbol, IconSymbolName } from '@/components/ui/IconSymbol';
import { LanguageIcon } from '@/components/LanguageIcon';
import { useAppTheme } from '@/hooks/useAppTheme';
import { AppDispatch, RootState } from '@/store';
import { saveThemeMode, selectEffectiveTheme, selectThemeMode, ThemeMode } from '@/store/slices/themeSlice';
import { loadLanguage, saveLanguagePreference, selectCurrentLanguage, LanguageCode } from '@/store/slices/languageSlice';
import { logout } from '@/store/slices/authSlice';
import { toggleFastMode } from '@/store/slices/fastModeSlice';
import { toggleStyleOptimization } from '@/store/slices/styleOptimizationSlice';
import { subscriptionService, paymentService, userService, modelService } from '@/services/supabaseService';
import { purchaseService, SUBSCRIPTION_PACKAGES, SUBSCRIPTION_TYPE } from '@/services/purchaseService';
import { fetchUserSubscription } from '@/store/slices/subscriptionSlice';
import { useAuth } from '@/hooks/useAuth';
import { LoginPrompt } from '@/components/LoginPrompt';
import { useAuthModal } from '@/components/AuthProvider';
import { 
  ThemedView, 
  ThemedText, 
  ThemedScrollView, 
  ThemedTouchableOpacity, 
  ThemedSafeAreaView,
  getThemedColor
} from '@/components/withTheme';
import { toggleAdvancedTranscriptionThunk } from '@/store/slices/recordingSlice';
import Toast from 'react-native-toast-message';
import { showCenteredToast } from '@/utils/toastConfig';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getModelDisplayName } from '@/utils/modelUtils';
import { saveUserSettings, getUserSettings } from '@/services/storageService';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { RatingService } from '@/services/ratingService';
import {
  checkBiometricCapability,
  authenticateWithBiometrics,
  showBiometricSetupGuide,
  getBiometricTypeName,
  BiometricCapability,
  clearAuthenticationSession
} from '@/services/biometricService';
import VipUpgradeModal from '@/components/VipUpgradeModal';
import { 
  AIModelFromDB, 
  transformModelsWithI18n, 
  getModelDisplayName as getI18nModelDisplayName,
  getModelDisplayDescription 
} from '@/utils/modelI18nUtils';
import React from 'react';
import { log, error as logError, warn } from '@/services/logService';
import { VipDowngradeService } from '@/services/vipDowngradeService';

// 定义语音转写模型类型
type TranscribeModel = 'native' | 'whisper-1' | 'gpt-4o-mini-transcribe';

// 定义语言模型类型
type LanguageModel = 'qwen2.5-7b' | 'qwen3-8b' | 'qwen3-14b' | 'deepseek-v3';

// 使用新的AI模型接口
type AIModel = AIModelFromDB;

// 新增小型Card样式组件
interface SwitchCardProps {
  icon: IconSymbolName;
  iconBgColor: string;
  iconColor: string;
  title: string;
  subtitle: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
  disabled?: boolean;
  isDark?: boolean;
}
function SwitchCard({
  icon, // 保留参数但不渲染
  iconBgColor, // 保留参数但不渲染
  iconColor, // 保留参数但不渲染
  title,
  subtitle,
  value,
  onValueChange,
  disabled = false,
  isDark = false,
}: SwitchCardProps) {

  const screenWidth = Dimensions.get('window').width;
  const isSmallScreen = screenWidth <= 400; // 你可以根据需要调整阈值
  const switchScale = isSmallScreen ? 0.85 : 1;

  return (
    <LinearGradient
      colors={isDark ? ['#23272A', '#2B2F31'] : ['#f5f7fa', '#e8ecf3']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={[
        styles.switchCardGradient,
        isDark && styles.switchCardGradientDark,
        disabled && styles.switchCardDisabled,
      ]}
    >
      <View style={styles.switchCardContent}>
        <View style={styles.switchCardHeader}>
          <Text 
            style={[
              styles.switchCardTitle,
              { color: isDark ? '#ECEDEE' : '#333' },
              disabled && { color: isDark ? '#6B7280' : '#9CA3AF' }
            ]}
            numberOfLines={1}
          >{title}</Text>
          <Switch
            value={value}
            onValueChange={disabled ? undefined : onValueChange}
            disabled={disabled}
            trackColor={{ 
              false: disabled ? (isDark ? '#2B2F31' : '#F3F4F6') : (isDark ? '#39393D' : '#E5E7EB'), 
              true: disabled ? (isDark ? '#4B5563' : '#D1D5DB') : (isDark ? '#8364E2' : '#6A5AE1')
            }}
            thumbColor={disabled ? (isDark ? '#6B7280' : '#9CA3AF') : '#FFFFFF'}
            ios_backgroundColor={disabled ? (isDark ? '#2B2F31' : '#F3F4F6') : (isDark ? '#39393D' : '#E5E7EB')}
            style={{ transform: [{ scale: switchScale }] }}
          />
        </View>
        <Text 
          style={[
            styles.switchCardSubtitle,
            { color: isDark ? '#9BA1A6' : '#6B7280' },
            disabled && { color: isDark ? '#4B5563' : '#D1D5DB' }
          ]}
          numberOfLines={3}
        >{subtitle}</Text>
      </View>
    </LinearGradient>
  );
}

/**
 * 设置页面
 * 包含应用设置选项和VIP会员信息
 */
export default function SettingsScreen() {
  // 从Redux获取用户状态
  const { user, isAuthenticated, isVIP } = useSelector((state: RootState) => state.auth);
  const { currentSubscription } = useSelector((state: RootState) => state.subscription);
  
  // 获取极速模式状态
  const { isFastModeOn } = useSelector((state: RootState) => state.fastMode);
  
  // 获取风格模板优化状态
  const { isStyleOptimizationOn } = useSelector((state: RootState) => state.styleOptimization);
  
  // 使用Auth Hook
  const { 
    checkIsLoggedIn, 
    loginPromptVisible, 
    loginPromptTitle, 
    loginPromptMessage, 
    hideLoginPrompt
  } = useAuth();
  

  
  // 使用 AuthModal
  const { showLoginModal } = useAuthModal();
  
  // 主题选择弹窗可见性
  const [themeModalVisible, setThemeModalVisible] = useState(false);
  // 语言选择弹窗可见性
  const [languageSelectModalVisible, setLanguageSelectModalVisible] = useState(false);
  // 语音转写模型弹窗可见性
  const [transcribeModalVisible, setTranscribeModalVisible] = useState(false);
  // 语言模型弹窗可见性
  const [languageModalVisible, setLanguageModalVisible] = useState(false);
  // VIP升级Modal状态
  const [vipUpgradeModalVisible, setVipUpgradeModalVisible] = useState(false);
  // 从 Redux 获取当前语言
  const currentLanguage = useSelector(selectCurrentLanguage);
  const appState = useRef(AppState.currentState);

  // Redux
  const dispatch = useDispatch<AppDispatch>();
  const themeMode = useSelector(selectThemeMode);
  const currentTheme = useAppTheme();
  const isDark = currentTheme === 'dark';

  // 用于刷新订阅信息
  const [refreshing, setRefreshing] = useState(false);
  // 用于存储用户资料（包含VIP信息）
  const [userProfile, setUserProfile] = useState<any>(null);
  
  // 模型选择状态
  const [selectedTranscribeModel, setSelectedTranscribeModel] = useState<TranscribeModel>('native');
  const [selectedLanguageModel, setSelectedLanguageModel] = useState<LanguageModel>('qwen2.5-7b');
  const { t } = useTranslation();
  
  // 添加模型列表状态
  const [textModels, setTextModels] = useState<AIModel[]>([]);
  const [voiceModels, setVoiceModels] = useState<AIModel[]>([]);
  const [loadingModels, setLoadingModels] = useState(false);
  
  // VIP状态监听器
  const [vipStatusListener, setVipStatusListener] = useState<any>(null);

  // 应用加密相关状态
  const [appLockEnabled, setAppLockEnabled] = useState(false);
  const [biometricCapability, setBiometricCapability] = useState<BiometricCapability | null>(null);

  // 获取Redux状态 - 保留但不使用
  const useAdvancedTranscription = useSelector((state: RootState) => state.recording.useAdvancedTranscription);
  
  // 获取平台
  const platform = Platform.OS;
  
  // 从服务器获取完整的用户资料，包括VIP信息
  const getUserVIPProfile = async () => {
    if (!user?.id) return;
    
    try {
      // 显示刷新状态
      setRefreshing(true);
      
      // 从服务器获取用户资料
      const { data: profileData, error } = await userService.getUserProfile(user.id);
      
      if (error) {
        logError('获取用户VIP资料失败:', error);
        return;
      }
      
      // 保存用户资料
      setUserProfile(profileData);
      
      log('获取到用户VIP资料:', {
        isVIP: profileData.is_vip,
        vipProductId: profileData.vip_product_id,
        vipExpiresAt: profileData.vip_expires_at,
      });
      
    } catch (error) {
      logError('获取用户VIP资料出错:', error);
    } finally {
      setRefreshing(false);
    }
  };
  
  // 刷新订阅信息
  const refreshSubscriptionInfo = async () => {
    if (user && user.id) {
      try {
        setRefreshing(true);
        // 先获取用户VIP资料
        await getUserVIPProfile();
        // 再尝试从服务器同步购买信息
        await purchaseService.syncPurchases();
        // 从服务器获取订阅信息
        await dispatch(fetchUserSubscription(user.id));
        // 最后检查服务器VIP状态
        await purchaseService.getServerVIPStatus();
      } catch (error) {
        logError('刷新订阅信息失败:', error);
      } finally {
        setRefreshing(false);
      }
    }
  };

  // 自动刷新订阅信息
  useEffect(() => {
    if (user?.id && !refreshing) {
      refreshSubscriptionInfo();
    }
  }, [user?.id]);

  // 组件挂载时获取用户资料
  useEffect(() => {
    if (user?.id && isVIP) {
      getUserVIPProfile();
    }
  }, [user?.id, isVIP]);

  // 监控用户资料变化
  useEffect(() => {
    if (userProfile) {
      log('用户资料已更新:', {
        vip产品ID: userProfile.vip_product_id,
        vip到期时间: userProfile.vip_expires_at,
        vip环境: userProfile.vip_environment,
        vip是否试用: userProfile.vip_is_trial,
      });
    }
  }, [userProfile]);

  // 初始化应用加密设置
  useEffect(() => {
    const initAppLockSettings = async () => {
      try {
        // 获取用户设置
        const settings = await getUserSettings();
        setAppLockEnabled(settings.appLockEnabled || false);

        // 检查设备生物识别能力
        const capability = await checkBiometricCapability();
        setBiometricCapability(capability);

        log('[Settings] 应用加密设置初始化完成', {
          appLockEnabled: settings.appLockEnabled,
          biometricAvailable: capability.isAvailable,
          biometricEnrolled: capability.isEnrolled,
          supportedTypes: capability.supportedTypes
        });
      } catch (error) {
        logError('[Settings] 初始化应用加密设置失败:', error);
      }
    };

    initAppLockSettings();
  }, []);

  // 打印用户信息用于调试
  useEffect(() => {
    if (user) {
      log('设置页面用户信息:', {
        id: user.id,
        email: user.email,
        providerType: user.providerType,
        avatarUrl: user.avatarUrl
      });
    }
  }, [user]);

  // 监控登录状态变化，仅在完全退出登录时重置模型
  useEffect(() => {
    // 仅当用户完全退出登录时才重置模型设置
    if (!isAuthenticated) {
      log('用户退出登录，重置为基础模型');
      
      // 设置默认的基础转写模型
      setSelectedTranscribeModel('native');
      
      // 设置默认的基础语言模型
      setSelectedLanguageModel('qwen2.5-7b');
      
      // 注意：这里不需要更新数据库，因为用户已经退出登录
    }
    // 移除对isVIP的依赖，避免VIP状态变化时强制重置用户已选择的模型
  }, [isAuthenticated]); // 只监听登录状态，不监听VIP状态

  // 显示VIP详情弹窗
  const showVipDetail = () => {
    router.push('/settings/vip-detail' as any);
  };

  // 关闭VIP详情弹窗 - 不再需要，但保留函数避免其他地方调用时出错
  const hideVipDetail = () => {
    // 空函数，不再需要设置Modal状态
  };



  // 处理登录
  const handleLogin = () => {
    log('SettingsScreen: 用户点击登录按钮');
    showLoginModal();
  };

  // 登出
  const handleLogout = () => {
    log('SettingsScreen: 用户点击登出按钮');
    Alert.alert(
      t("settings.logout"),
      t("settings.logoutConfirm"),
      [
        {
          text: t("common.cancel"),
          style: "cancel"
        },
        {
          text: t("settings.logout"),
          onPress: async () => {
            try {
              await dispatch(logout()).unwrap();
              // 直接重置到input页面（从settings主页，无需特殊处理）
              router.replace('/(tabs)');
            } catch (error) {
              logError('登出失败:', error);
            }
          },
          style: "destructive"
        }
      ]
    );
  };

  // 处理VIP升级点击事件
  const upgradeToVIP = () => {
    log('=== 设置页面VIP升级按钮点击 ===');
    log('当前登录状态:', { isAuthenticated, user: user?.id });
    
    // 检查是否已登录
    if (!isAuthenticated) {
      log('用户未登录，设置VIP升级标志并显示登录提示');
      // 设置全局标志，表示登录后需要导航到支付页面
      global.pendingVipUpgrade = true;
      
      // 使用checkIsLoggedIn来显示登录提示
      checkIsLoggedIn({
        showPrompt: true,
        title: t('settings.prompts.upgradeVipTitle'),
        message: t('settings.prompts.upgradeVipMessage')
      });
    } else {
      log('用户已登录，直接导航到支付页面');
      // 已登录，直接导航到支付页面
      router.push('/payment');
    }
    
    log('=== 设置页面VIP升级按钮点击处理完成 ===');
  };

  // 模拟AI模型设置点击
  const handleAIModelSettingClick = () => {
    if (isVIP) {
      Alert.alert(t('settings.vipFeatures.aiModelSettingTitle'), t('settings.vipFeatures.aiModelSettingMessage'));
    } else {
      Alert.alert(t('settings.vipFeatures.title'), t('settings.vipFeatures.message'));
    }
  };
  
  // 导航到风格模板管理页面
  const navigateToTemplates = () => {
    // 修改为使用嵌套的Stack导航
    router.push("/settings/templates" as any);
  };
  
  // 导航到模板个性化管理页面
  const navigateToTemplateManager = () => {
    router.push("/template-manager" as any);
  };



  // 处理评价我们功能
  const handleRateUs = async () => {
    try {
      await RatingService.smartRatingRequest();
    } catch (error) {
      logError('评价功能出错:', error);
      showCenteredToast(t('settings.ratingNotAvailable'), 'error');
    }
  };

  // 显示主题选择弹窗
  const showThemeModal = () => {
    setThemeModalVisible(true);
  };

  // 关闭主题选择弹窗
  const hideThemeModal = () => {
    setThemeModalVisible(false);
  };

  // 设置主题模式
  const handleSetThemeMode = (mode: ThemeMode) => {
    dispatch(saveThemeMode(mode));
    hideThemeModal();
  };

  // 处理极速模式切换（使用统一的切换操作）
  const handleToggleFastMode = () => {
    // 使用统一的切换操作，避免状态不一致问题
    dispatch(toggleFastMode());
  };

  // 处理风格模板优化切换（使用统一的切换操作）
  const handleToggleStyleOptimization = () => {
    // 如果即将关闭风格模板优化，同时关闭极速模式
    if (isStyleOptimizationOn && isFastModeOn) {
      // 先关闭极速模式
      dispatch(toggleFastMode());
    }

    // 使用统一的切换操作，避免状态不一致问题
    dispatch(toggleStyleOptimization());
  };

  // 处理应用加密开关切换
  const handleToggleAppLock = async () => {
    try {
      if (!appLockEnabled) {
        // 开启应用加密前，先检查设备能力
        if (!biometricCapability?.isAvailable) {
          Alert.alert(
            '设备不支持',
            '您的设备不支持生物识别认证功能',
            [{ text: '确定', style: 'default' }]
          );
          return;
        }

        if (!biometricCapability?.isEnrolled) {
          // 设备支持但未注册，显示设置引导
          showBiometricSetupGuide(() => {
            // 用户设置完成后重新检查能力
            checkBiometricCapability().then(setBiometricCapability);
          });
          return;
        }

        // 测试生物识别认证
        const authResult = await authenticateWithBiometrics(
          '请验证您的身份以启用应用加密',
          '使用密码'
        );

        if (!authResult.success) {
          Alert.alert(
            '认证失败',
            authResult.error || '生物识别认证失败，请重试',
            [{ text: '确定', style: 'default' }]
          );
          return;
        }

        // 认证成功，启用应用加密
        const newValue = true;
        setAppLockEnabled(newValue);
        await saveUserSettings({ appLockEnabled: newValue });

        showCenteredToast('应用加密已启用', 'success');
      } else {
        // 关闭应用加密，需要先验证身份
        const authResult = await authenticateWithBiometrics(
          '请验证您的身份以关闭应用加密',
          '使用密码'
        );

        if (!authResult.success) {
          Alert.alert(
            '认证失败',
            authResult.error || '生物识别认证失败，请重试',
            [{ text: '确定', style: 'default' }]
          );
          return;
        }

        // 认证成功，关闭应用加密
        const newValue = false;
        setAppLockEnabled(newValue);
        await saveUserSettings({ appLockEnabled: newValue });

        // 清除认证会话
        await clearAuthenticationSession();

        showCenteredToast('应用加密已关闭', 'success');
      }
    } catch (error) {
      logError('[Settings] 切换应用加密失败:', error);
      showCenteredToast('切换应用加密失败，请重试', 'error');
    }
  };

  // 语言切换状态
  const [isSwitchingLanguage, setIsSwitchingLanguage] = useState(false);
  const [targetLanguage, setTargetLanguage] = useState<LanguageCode | null>(null);
  const { i18n } = useTranslation();

  // 设置应用语言
  const handleSetLanguage = async (language: LanguageCode) => {
    try {
      setTargetLanguage(language);
      setIsSwitchingLanguage(true);
      
      // 先保存到 Redux
      await dispatch(saveLanguagePreference(language)).unwrap();
      // 然后更改 i18n 的语言
      await i18n.changeLanguage(language);
      
      // 强制触发组件的重新渲染
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 关闭语言选择弹窗
      hideLanguageSelectModal();
    } catch (error) {
      logError('切换语言失败:', error);
      setIsSwitchingLanguage(false);
      setTargetLanguage(null);
      Alert.alert(
        t('common.error'),
        t('settings.languageChangeFailed'),
        [
          { text: t('common.ok'), style: 'cancel' }
        ]
      );
    }
  };

  // 语言切换完成后的回调
  const handleLanguageSwitchComplete = () => {
    setIsSwitchingLanguage(false);
    setTargetLanguage(null);
  };

  // 应用启动时加载保存的语言设置
  useEffect(() => {
    // 初始加载语言设置
    dispatch(loadLanguage());
    
    // 监听应用状态变化
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        // 应用从后台返回前台时，重新加载语言设置
        dispatch(loadLanguage());
      }
      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, [dispatch]);

  // 显示语言选择弹窗
  const showLanguageSelectModal = () => {
    setLanguageSelectModalVisible(true);
  };

  // 隐藏语言选择弹窗
  const hideLanguageSelectModal = () => {
    setLanguageSelectModalVisible(false);
  };

  // 获取语言的显示名称
  const getLanguageName = (code: LanguageCode) => {
    return t(`settings.languageNames.${code}`);
  };

  // 获取当前语言的显示名称
  const getCurrentLanguageName = () => {
    return getLanguageName(currentLanguage);
  };

  // 语言选择弹窗
  const renderLanguageSelectModal = () => {
    const languages = [
      { code: 'en' as const, name: t('settings.languageNames.en'), displayCode: 'EN' },
      { code: 'zh-Hans' as const, name: t('settings.languageNames.zh-Hans'), displayCode: '中' },
      { code: 'zh-Hant' as const, name: t('settings.languageNames.zh-Hant'), displayCode: '繁' },
    ];
    
    const getLanguageColor = (code: string) => {
      switch (code) {
        case 'zh-Hans':
          return '#1890ff';
        case 'zh-Hant':
          return '#722ed1';
        case 'en':
          return '#13c2c2';
        default:
          return '#8c8c8c';
      }
    };

    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={languageSelectModalVisible}
        onRequestClose={hideLanguageSelectModal}
      >
        <Pressable
          style={[styles.themeModalOverlay, { backgroundColor: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(0,0,0,0.5)' }]}
          onPress={hideLanguageSelectModal}
        >
          <View 
            style={[
              styles.modalContainer, 
              isDark && styles.modalContainerDark,
              { maxHeight: '50%' }
            ]}
            onStartShouldSetResponder={() => true}
          >
            <View style={[styles.themeModalHeader, isDark && styles.modalHeaderDark]}>
              <Text style={[styles.modalTitle, isDark && { color: '#ECEDEE' }]}>{t('settings.selectLanguage')}</Text>
              <TouchableOpacity 
                style={[styles.themeCloseButton, isDark && styles.closeButtonDark]} 
                onPress={hideLanguageSelectModal}
              >
                <IconSymbol name="xmark" size={16} color={isDark ? '#9BA1A6' : '#687076'} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.themeOptions}>
              {languages.map((lang, index) => (
                <TouchableOpacity 
                  key={lang.code}
                  style={[
                    styles.themeOption, 
                    currentLanguage === lang.code && styles.selectedThemeOption,
                    isDark && styles.themeOptionDark,
                    currentLanguage === lang.code && isDark && styles.selectedThemeOptionDark,
                    index === languages.length - 1 && { borderBottomWidth: 0 }
                  ]} 
                  onPress={() => handleSetLanguage(lang.code)}
                >
                  <View style={styles.themeOptionIconContainer}>
                    <LanguageIcon 
                      code={lang.code}
                      size={24}
                      style={{
                        marginRight: 8,
                      }}
                    />
                  </View>
                  <View style={styles.themeOptionContent}>
                    <Text style={[
                      styles.themeOptionText, 
                      currentLanguage === lang.code && styles.selectedThemeOptionText,
                      isDark && styles.themeOptionTextDark,
                      currentLanguage === lang.code && isDark && styles.selectedThemeOptionTextDark
                    ]}>{lang.name}</Text>
                  </View>
                  {currentLanguage === lang.code && (
                    <IconSymbol name="checkmark" size={20} color={isDark ? '#61dafb' : '#0a7ea4'} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </Pressable>
      </Modal>
    );
  };

  // 获取当前主题模式的显示文本
  const getThemeModeText = (mode: ThemeMode): string => {
    switch (mode) {
      case 'light': return t("settings.themeLightStyle");
      case 'dark': return t("settings.themeDarkStyle");
      case 'system': return t("settings.themeSystemStyle");
    }
  };

  // 获取深色模式下的颜色
  const getColor = (lightColor: string, darkColor: string) => {
    return isDark ? darkColor : lightColor;
  };

  // 获取订阅到期日期的格式化字符串
  const getSubscriptionEndDate = () => {
    // 优先从用户资料中获取VIP过期时间
    if (userProfile?.vip_expires_at) {
      const expiryDate = new Date(userProfile.vip_expires_at);
      return `${expiryDate.getFullYear()}年${expiryDate.getMonth() + 1}月${expiryDate.getDate()}日`;
    }
    
    // 其次从Redux状态获取订阅信息
    if (currentSubscription?.currentPeriodEnd) {
      const endDate = new Date(currentSubscription.currentPeriodEnd);
      return `${endDate.getFullYear()}年${endDate.getMonth() + 1}月${endDate.getDate()}日`;
    }
    
    return t('settings.validityInfo.notAvailable');
  };

  // 获取认证提供商文本
  const getAuthProviderText = (): string => {
    if (!user?.providerType) {
      return t('settings.authProviders.unknown');
    }
    
    switch (user.providerType) {
      case 'wechat':
        return t('settings.wechatLogin');
      case 'apple':
        return t('settings.appleLogin');
      case 'google':
        return t('settings.googleLogin');
      case 'email':
        return t('settings.emailLogin');
      case 'phone':
        return t('settings.phoneLogin');
      default:
        return t('settings.authProviders.unknown');
    }
  };

  // 获取默认头像 - 为Apple登录用户生成带有初始字母的头像
  const getAvatarContent = () => {
    if (!user) return null;
    
    // 调试信息
    log('尝试获取头像:', user.avatarUrl);
    
    // 如果有头像URL，直接返回Image组件
    if (user.avatarUrl && user.avatarUrl.trim() !== '') {
      log('使用用户头像URL:', user.avatarUrl);
      return (
        <Image 
          source={{ uri: user.avatarUrl }}
          style={styles.userAvatar}
          onLoad={() => log('头像加载成功')}
          onError={(error) => log('头像加载失败:', error)}
        />
      );
    }
    
    // 获取用户标识符（邮箱或ID）
    const identifier = user.email || user.id || '';
    log('使用字母头像:', identifier);
    
    // 获取初始字母（取第一个字符和第二个字符，如果有的话）
    let initials = '';
    if (identifier) {
      initials = identifier.substring(0, 2).toUpperCase();
    }
    
    // 根据initials生成一个一致的颜色（简单的哈希映射到颜色）
    const getColorFromInitials = (initials: string) => {
      const colorOptions = isDark ? 
        ['#8B5CF6', '#60A5FA', '#34D399', '#F87171', '#FBBF24'] : 
        ['#7C3AED', '#3B82F6', '#10B981', '#EF4444', '#F59E0B'];
      
      let hash = 0;
      for (let i = 0; i < initials.length; i++) {
        hash = initials.charCodeAt(i) + ((hash << 5) - hash);
      }
      
      return colorOptions[Math.abs(hash) % colorOptions.length];
    };
    
    const bgColor = getColorFromInitials(initials);
    
    // 返回带有初始字母的View
    return (
      <View style={[styles.userAvatar, { backgroundColor: bgColor, justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ color: 'white', fontSize: 24, fontWeight: 'bold' }}>{initials}</Text>
      </View>
    );
  };

  // 获取当前订阅的类型（月度/年度）
  const getCurrentPlanType = () => {
    log('检查套餐类型:', {
      userProfile: userProfile?.vip_product_id,
      subscriptionPlanId: currentSubscription?.planId
    });
    
    // 优先从用户资料中获取产品ID
    if (userProfile?.vip_product_id) {
      const productId = userProfile.vip_product_id.toLowerCase();
      
      // 检查产品ID是否包含月度/年度关键词
      if (productId.includes('month') || 
          productId.includes('monthly') || 
          productId === SUBSCRIPTION_PACKAGES.MONTHLY.toLowerCase()) {
        return SUBSCRIPTION_TYPE.MONTHLY;
      } 
      
      if (productId.includes('year') || 
          productId.includes('yearly') || 
          productId.includes('annual') || 
          productId === SUBSCRIPTION_PACKAGES.YEARLY.toLowerCase()) {
        return SUBSCRIPTION_TYPE.YEARLY;
      }
    }
    
    // 其次从Redux获取订阅信息
    if (currentSubscription?.planId) {
      const planId = currentSubscription.planId.toLowerCase();
      
      if (planId.includes('month') || 
          planId.includes('monthly') || 
          planId === SUBSCRIPTION_PACKAGES.MONTHLY.toLowerCase()) {
        return SUBSCRIPTION_TYPE.MONTHLY;
      }
      
      if (planId.includes('year') || 
          planId.includes('yearly') ||
          planId.includes('annual') ||
          planId === SUBSCRIPTION_PACKAGES.YEARLY.toLowerCase()) {
        return SUBSCRIPTION_TYPE.YEARLY;
      }
    }
    
    // 默认返回月度套餐类型
    return SUBSCRIPTION_TYPE.MONTHLY;
  };
  
  // 获取当前套餐名称的显示文本
  const getCurrentPlanName = () => {
    switch (getCurrentPlanType()) {
      case SUBSCRIPTION_TYPE.MONTHLY: return t('settings.subscriptionPlans.monthly');
      case SUBSCRIPTION_TYPE.YEARLY: return t('settings.subscriptionPlans.yearly');
      default: return t('settings.subscriptionPlans.vip');
    }
  };
  
  // 处理套餐切换
  const handleChangePlan = () => {
    // 获取当前套餐类型
    const currentPlan = getCurrentPlanType();
    
    // 如果当前是月套餐，直接跳转到年套餐支付页面
    // 如果当前是年套餐，直接跳转到月套餐支付页面
    const targetPlan = currentPlan === SUBSCRIPTION_TYPE.MONTHLY ? SUBSCRIPTION_TYPE.YEARLY : SUBSCRIPTION_TYPE.MONTHLY;
    
    log('切换套餐，当前:', currentPlan, '目标:', targetPlan);
    
    // 跳转到支付页面
    router.push({
      pathname: '/payment',
      params: { 
        onSuccess: 'true',
        switchPlan: 'true',
        planType: targetPlan
      }
    });
  };

  // 显示语音转写模型选择弹窗
  const showTranscribeModelModal = () => {
    setTranscribeModalVisible(true);
  };

  // 关闭语音转写模型选择弹窗
  const hideTranscribeModelModal = () => {
    setTranscribeModalVisible(false);
  };

  // 选择语音转写模型
  const handleSelectTranscribeModel = async (model: TranscribeModel) => {
    // 再次检查VIP状态，确保用户有权限使用此模型
    const selectedModel = voiceModels.find(m => m.model_id === model);
    
    // 使用当前Redux状态中的VIP状态
    if (selectedModel?.is_vip_only && !isVIP) {
      // 使用checkIsLoggedIn来显示登录提示Modal而不是Alert
      checkIsLoggedIn({
        showPrompt: true,
        title: t('settings.vipExclusiveFeature'),
        message: t('settings.onlyVIPUsersCanUseThisModel')
      });
      return;
    }
    
    // 先保存到本地状态
    setSelectedTranscribeModel(model);
    setTranscribeModalVisible(false);
    
    // 保存到本地存储
    try {
      await saveUserSettings({
        transcribeModel: model
      });
      log('语音转写模型设置已保存到本地存储:', model);
    } catch (storageError) {
      logError('保存语音转写模型设置到本地存储失败:', storageError);
    }
    
    // 保存用户设置到服务器
    if (user?.id) {
      try {
        log(`正在保存用户语音转写模型设置: ${model}`);
        // 保存到Supabase
        const { data, error } = await modelService.saveUserModelSettings(user.id, {
          default_voice_model: model
        });
        
        if (error) {
          logError('保存语音转写模型设置失败:', error);
          showCenteredToast(t('settings.toast.settingsSaveFailed'), 'error');
          return;
        }
        
        log('语音转写模型设置已保存，服务器返回:', data);
        showCenteredToast(t('settings.toast.settingsSaveSuccess'), 'success');
      } catch (error) {
        logError('保存语音转写模型设置错误:', error);
        showCenteredToast(t('settings.toast.settingsSaveFailed'), 'error');
      }
    }
  };

  // 显示语言模型选择弹窗
  const showLanguageModelModal = () => {
    setLanguageModalVisible(true);
  };

  // 关闭语言模型选择弹窗
  const hideLanguageModelModal = () => {
    setLanguageModalVisible(false);
  };

  // 选择语言模型
  const handleSelectLanguageModel = async (model: LanguageModel) => {
    // 再次检查VIP状态，确保用户有权限使用此模型
    const selectedModel = textModels.find(m => m.model_id === model);
    
    // 使用当前Redux状态中的VIP状态
    if (selectedModel?.is_vip_only && !isVIP) {
      // 使用checkIsLoggedIn来显示登录提示Modal而不是Alert
      checkIsLoggedIn({
        showPrompt: true,
        title: t('settings.vipExclusiveFeature'),
        message: t('settings.onlyVIPUsersCanUseThisModel')
      });
      return;
    }
    
    // 先保存到本地状态
    setSelectedLanguageModel(model);
    setLanguageModalVisible(false);
    
    // 立即保存到本地存储，确保AI服务能立即获取到最新设置
    try {
      await saveUserSettings({
        languageModel: model
      });
      log('语言模型设置已立即保存到本地存储:', model);
    } catch (storageError) {
      logError('保存语言模型设置到本地存储失败:', storageError);
      showCenteredToast(t('settings.toast.localSettingsSaveFailed'), 'error');
      return;
    }
    
    // 保存用户设置到服务器（异步执行，不阻塞用户体验）
    if (user?.id) {
      try {
        log(`正在保存用户语言模型设置到数据库: ${model}`);
        // 保存到Supabase
        const { data, error } = await modelService.saveUserModelSettings(user.id, {
          default_text_model: model
        });
        
        if (error) {
          logError('保存语言模型设置到数据库失败:', error);
          showCenteredToast(t('settings.toast.cloudSyncFailed'), 'info');
          return;
        }
        
        log('语言模型设置已保存到数据库，服务器返回:', data);
        
        // 确认模型ID已正确设置
        if (data?.default_text_model === model) {
          log('模型ID已成功保存到数据库');
          showCenteredToast(t('settings.toast.settingsSaveSuccess'), 'success');
        } else {
          warn('数据库返回的模型ID与请求不一致:', {
            请求: model,
            返回: data?.default_text_model
          });
          showCenteredToast(t('settings.toast.settingsSavedWithIssue'), 'info');
        }
      } catch (error) {
        logError('保存语言模型设置到数据库错误:', error);
        showCenteredToast(t('settings.toast.cloudSyncFailed'), 'info');
      }
    } else {
      // 用户未登录，只显示本地保存成功
      showCenteredToast(t('settings.toast.settingsSaved'), 'success');
    }
  };

  // 获取语音转写模型名称（使用多语言支持）
  const getTranscribeModelName = (model: TranscribeModel): string => {
    const voiceModel = voiceModels.find(m => m.model_id === model);
    if (voiceModel) {
      return getI18nModelDisplayName(voiceModel, t);
    }
    
    // 回退到硬编码名称
    switch (model) {
      case 'native': return platform === 'ios' ? t('settings.models.nativeTranscribe.ios') : t('settings.models.nativeTranscribe.android');
      case 'whisper-1': return t('aiModels.whisper-1.name', 'Whisper') as unknown as string;
      case 'gpt-4o-mini-transcribe': return t('aiModels.gpt-4o-mini-transcribe.name', 'GPT-4o Mini') as unknown as string;
    }
  };

  // 获取语言模型名称（使用多语言支持）
  const getLanguageModelName = (model: LanguageModel): string => {
    const textModel = textModels.find(m => m.model_id === model);
    if (textModel) {
      return getI18nModelDisplayName(textModel, t);
    }
    
    // 回退到原有逻辑
    return getModelDisplayName(model);
  };

  // 获取用户模型设置
  const getUserModelSettings = async () => {
    if (!user?.id) return;
    
    try {
      log('正在获取用户模型设置...');
      const { data: settings, error } = await modelService.getUserModelSettings(user.id);
      if (error) {
        logError('获取用户模型设置失败:', error);
        return;
      }
      
      if (settings) {
        log('获取到用户模型设置:', settings);
        
        // 设置默认语言模型
        if (settings.default_text_model) {
          // 检查语言模型是否需要VIP权限
          const textModel = textModels.find(m => m.model_id === settings.default_text_model);
          
          if (textModel?.is_vip_only && !isVIP) {
            // 如果模型需要VIP但用户不是VIP，使用默认基础模型
            log('语言模型需要VIP权限，但用户不是VIP，使用默认基础模型');
            setSelectedLanguageModel('qwen2.5-7b');
            
            // 更新数据库中的设置
            if (user?.id) {
              try {
                const { error } = await modelService.saveUserModelSettings(user.id, {
                  default_text_model: 'qwen2.5-7b'
                });
                if (error) {
                  logError('更新语言模型设置失败:', error);
                } else {
                  log('已将用户语言模型设置更新为基础模型');
                }
              } catch (updateError) {
                logError('更新模型设置失败:', updateError);
              }
            }
          } else {
            log(`设置默认语言模型: ${settings.default_text_model}`);
            setSelectedLanguageModel(settings.default_text_model as LanguageModel);
            
            // 同步保存到本地存储
            try {
              await saveUserSettings({
                languageModel: settings.default_text_model as LanguageModel
              });
              log('语言模型设置已同步到本地存储');
            } catch (storageError) {
              logError('保存语言模型设置到本地存储失败:', storageError);
            }
          }
        } else {
          log('未找到默认语言模型设置，使用默认值');
          setSelectedLanguageModel('qwen2.5-7b');
          
          // 确保数据库中有默认设置
          if (user?.id) {
            try {
              await modelService.saveUserModelSettings(user.id, {
                default_text_model: 'qwen2.5-7b'
              });
            } catch (updateError) {
              logError('初始化默认模型设置失败:', updateError);
            }
          }
        }
        
        // 设置默认语音转写模型
        if (settings.default_voice_model) {
          log(`设置默认语音转写模型: ${settings.default_voice_model}`);
          setSelectedTranscribeModel(settings.default_voice_model as TranscribeModel);
          
          // 同步保存到本地存储
          try {
            await saveUserSettings({
              transcribeModel: settings.default_voice_model as TranscribeModel
            });
            log('语音转写模型设置已同步到本地存储');
          } catch (storageError) {
            logError('保存语音转写模型设置到本地存储失败:', storageError);
          }
        } else {
          log('未找到默认语音转写模型设置，使用默认值');
          setSelectedTranscribeModel('native');
          
          // 确保数据库中有默认设置
          if (user?.id) {
            try {
              await modelService.saveUserModelSettings(user.id, {
                default_voice_model: 'native'
              });
            } catch (updateError) {
              logError('初始化默认模型设置失败:', updateError);
            }
          }
        }
      } else {
        log('未找到用户模型设置，使用默认值');
        setSelectedLanguageModel('qwen2.5-7b');
        setSelectedTranscribeModel('native');
        
        // 确保数据库中有默认设置
        if (user?.id) {
          try {
            await modelService.saveUserModelSettings(user.id, {
              default_text_model: 'qwen2.5-7b',
              default_voice_model: 'native'
            });
          } catch (updateError) {
            logError('初始化默认模型设置失败:', updateError);
          }
        }
      }
    } catch (error) {
      logError('获取用户模型设置出错:', error);
      
      // 出错时使用默认值
      setSelectedLanguageModel('qwen2.5-7b');
      setSelectedTranscribeModel('native');
    }
  };
  
  // 获取AI模型列表
  const fetchModels = async () => {
    try {
      setLoadingModels(true);
      
      // 获取文本模型
      const { data: textModelData } = await modelService.getModelsByType('text');
      if (textModelData) {
        setTextModels(textModelData);
      }
      
      // 获取语音模型
      const { data: voiceModelData } = await modelService.getModelsByType('voice');
      if (voiceModelData) {
        setVoiceModels(voiceModelData);
      }
      
      log('已加载AI模型列表:', { 
        textModels: textModelData?.length, 
        voiceModels: voiceModelData?.length 
      });
    } catch (error) {
      logError('获取AI模型列表失败:', error);
    } finally {
      setLoadingModels(false);
    }
  };
  
  // 设置VIP状态监听
  const setupVipStatusMonitoring = () => {
    if (!user?.id) return;
    
    // 清理旧的监听器
    cleanupVipStatusMonitoring();
    
    log('[设置页面] 设置VIP状态监听器');
    
    const listener = VipDowngradeService.setupVipStatusListener(
      user.id,
      (newSettings) => {
        log('[设置页面] 收到模型设置更新:', newSettings);
        
        // 更新本地状态
        if (newSettings.default_text_model) {
          setSelectedLanguageModel(newSettings.default_text_model as LanguageModel);
        }
        if (newSettings.default_voice_model) {
          setSelectedTranscribeModel(newSettings.default_voice_model as TranscribeModel);
        }

      }
    );
    
    setVipStatusListener(listener);
  };
  
  // 清理VIP状态监听
  const cleanupVipStatusMonitoring = () => {
    if (vipStatusListener) {
      log('[设置页面] 清理VIP状态监听器');
      vipStatusListener.unsubscribe();
      setVipStatusListener(null);
    }
  };
  
  // 手动同步VIP状态和模型设置
  const manualSyncVipStatus = async () => {
    if (!user?.id) return;
    
    log('[设置页面] 手动同步VIP状态和模型设置');
    
    try {
      const hasDowngraded = await VipDowngradeService.manualSync(user.id);
      
      if (hasDowngraded) {
        // 重新获取用户设置
        await getUserModelSettings();
        
        log('[设置页面] 模型设置已同步完成');
      }
    } catch (error) {
      logError('[设置页面] 手动同步VIP状态失败:', error);
    }
  };

  // 组件挂载时获取模型列表和用户设置
  useEffect(() => {
    // 先获取模型列表
    fetchModels();
    
    // 如果用户已登录，则获取用户设置
    if (user?.id) {
      log('用户已登录，获取用户模型设置');
      getUserModelSettings();
      
      // 设置VIP状态监听器
      setupVipStatusMonitoring();
    } else {
      // 用户退出登录时清理监听器
      cleanupVipStatusMonitoring();
    }
    
    // 组件卸载时清理监听器
    return () => {
      cleanupVipStatusMonitoring();
    };
  }, [user?.id]);

  // 页面获得焦点时重新获取用户模型设置，确保显示最新状态
  useFocusEffect(
    React.useCallback(() => {
      const syncSettings = async () => {
        try {
          log('设置页面获得焦点，开始同步模型设置');
          
          // 1. 先获取最新的模型列表
          await fetchModels();
          
          // 2. 如果用户已登录，获取服务器端的设置
          if (user?.id) {
            await getUserModelSettings();
          } else {
            // 3. 如果用户未登录，从本地存储获取设置
                         const settings = await getUserSettings();
             if (settings.transcribeModel) {
               setSelectedTranscribeModel(settings.transcribeModel as TranscribeModel);
             }
             if (settings.languageModel) {
               setSelectedLanguageModel(settings.languageModel as LanguageModel);
             }
          }
          
          log('模型设置同步完成');
        } catch (error) {
          logError('同步模型设置失败:', error);
        }
      };
      
      syncSettings();
    }, [user?.id])
  );

  // 在修改模型设置前检查用户是否已登录
  const handleModelSettingClick = (type: 'transcribe' | 'language') => {
    if (!user) {
      // 使用checkIsLoggedIn来显示登录提示Modal而不是Alert
      checkIsLoggedIn({
        showPrompt: true,
        title: t('settings.prompts.modelSettingsTitle'),
        message: t('settings.prompts.modelSettingsMessage')
      });
      return;
    }
    
    if (type === 'transcribe') {
      showTranscribeModelModal();
    } else {
      showLanguageModelModal();
    }
  };
  
  // 模型设置项处理：
  const getModelSettingItems = () => {
    return (
      <>
        {/* 语音转写模型设置 */}
        <SettingsItem
          icon="mic"
          iconBgColor={isDark ? '#1E293B' : '#EBF5FF'}
          iconColor={isDark ? '#94A3B8' : '#3B82F6'}
          title={t('settings.transcribeModel')}
          subtitle={getTranscribeModelName(selectedTranscribeModel)}
          hasArrow={true}
          isVipLocked={false}
          isDark={isDark}
          onPress={() => handleModelSettingClick('transcribe')}
        />
        
        {/* AI语言模型设置 */}
        <SettingsItem
          icon="wand.and.stars"
          iconBgColor={isDark ? '#082F49' : '#F0F9FF'}
          iconColor={isDark ? '#60A5FA' : '#0EA5E9'}
          title={t('settings.languageModel')}
          subtitle={getLanguageModelName(selectedLanguageModel)}
          hasArrow={true}
          isVipLocked={false}
          isDark={isDark}
          onPress={() => handleModelSettingClick('language')}
          isLast={true}
        />
      </>
    );
  };
  
  // 语音转写模型Modal的JSX
  // 在这里添加语音转写模型和语言模型Modal的JSX
  // 需要替换原有的Modal内容
  // 语音转写模型Modal
  // 在JSX返回之前添加
  
  // 准备模型选择对话框的渲染函数
  const renderTranscribeModelModal = () => {
    return (
      <Modal
        visible={transcribeModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setTranscribeModalVisible(false)}
      >
        <Pressable
          style={[styles.themeModalOverlay, { backgroundColor: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(0,0,0,0.5)' }]}
          onPress={() => setTranscribeModalVisible(false)}
        >
          <View 
            style={[
              styles.modalContainer, 
              isDark && styles.modalContainerDark,
              { maxHeight: '70%' }
            ]}
            onStartShouldSetResponder={() => true}
          >
            <View style={[styles.themeModalHeader, isDark && styles.modalHeaderDark]}>
              <Text style={[styles.modalTitle, isDark && { color: '#ECEDEE' }]}>{t('settings.selectTranscribeModel')}</Text>
              <TouchableOpacity 
                style={[styles.themeCloseButton, isDark && styles.closeButtonDark]} 
                onPress={() => setTranscribeModalVisible(false)}
              >
                <IconSymbol name="xmark" size={16} color={isDark ? '#9BA1A6' : '#687076'} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.themeOptions}>
              {loadingModels ? (
                <View style={{
                  padding: 20,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <Text style={{
                    fontSize: 16,
                    color: isDark ? '#9BA1A6' : '#6B7280',
                  }}>{t('common.loading')}</Text>
                </View>
              ) : (
                voiceModels.map((model) => (
                  <TouchableOpacity 
                    key={model.model_id}
                    style={[
                      styles.themeOption, 
                      selectedTranscribeModel === model.model_id && styles.selectedThemeOption,
                      isDark && styles.themeOptionDark,
                      selectedTranscribeModel === model.model_id && isDark && styles.selectedThemeOptionDark,
                      model.is_vip_only && !isVIP && styles.vipLockedOption,
                      model === voiceModels[voiceModels.length - 1] && { borderBottomWidth: 0 }
                    ]} 
                    onPress={() => {
                      // 检查是否为VIP模型但用户不是VIP
                      if (model.is_vip_only && !isVIP) {
                        // 关闭模态框
                        setTranscribeModalVisible(false);
                        // 如果已登录，显示VIP升级Modal
                        if (user) {
                          setVipUpgradeModalVisible(true);
                        } else {
                          // 未登录，显示登录提示
                          checkIsLoggedIn({
                            showPrompt: true,
                            title: t('settings.vipExclusiveFeature'),
                            message: t('settings.onlyVIPUsersCanUseThisModel')
                          });
                        }
                        return;
                      }
                      handleSelectTranscribeModel(model.model_id as TranscribeModel);
                    }}
                  >
                    <View style={styles.themeOptionIconContainer}>
                      <IconSymbol 
                        name={model.model_id === 'native' ? 'mic' : model.model_id === 'whisper-1' ? 'ear' : 'sparkles'}
                        size={20} 
                        color={selectedTranscribeModel === model.model_id ? (isDark ? '#61dafb' : '#0a7ea4') : isDark ? '#9BA1A6' : '#687076'} 
                      />
                    </View>
                    <View style={styles.themeOptionContent}>
                      <Text style={[
                        styles.themeOptionText, 
                        selectedTranscribeModel === model.model_id && styles.selectedThemeOptionText,
                        isDark && styles.themeOptionTextDark,
                        selectedTranscribeModel === model.model_id && isDark && styles.selectedThemeOptionTextDark,
                        model.is_vip_only && !isVIP && styles.vipLockedText
                      ]}>{getI18nModelDisplayName(model, t)}</Text>
                      <Text style={[
                        styles.themeOptionSubtext,
                        isDark && styles.themeOptionSubtextDark,
                        model.is_vip_only && !isVIP && styles.vipLockedSubtext
                      ]}>{getModelDisplayDescription(model, t)}</Text>
                    </View>
                    {selectedTranscribeModel === model.model_id && (
                      <IconSymbol name="checkmark" size={20} color={isDark ? '#61dafb' : '#0a7ea4'} />
                    )}
                    {model.is_vip_only && (
                      <View style={[
                        styles.vipTagBadge,
                        !isVIP && styles.vipLockedBadge
                      ]}>
                        <Text style={styles.vipTagText}>VIP</Text>
                      </View>
                    )}
                  </TouchableOpacity>
                ))
              )}
            </View>
          </View>
        </Pressable>
      </Modal>
    );
  };
  
  const renderLanguageModelModal = () => {
    return (
      <Modal
        visible={languageModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setLanguageModalVisible(false)}
      >
        <Pressable
          style={[styles.themeModalOverlay, { backgroundColor: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(0,0,0,0.5)' }]}
          onPress={() => setLanguageModalVisible(false)}
        >
          <View 
            style={[
              styles.modalContainer, 
              isDark && styles.modalContainerDark,
              { maxHeight: '70%' }
            ]}
            onStartShouldSetResponder={() => true}
          >
            <View style={[styles.themeModalHeader, isDark && styles.modalHeaderDark]}>
              <Text style={[styles.modalTitle, isDark && { color: '#ECEDEE' }]}>{t('settings.selectLanguageModel')}</Text>
              <TouchableOpacity 
                style={[styles.themeCloseButton, isDark && styles.closeButtonDark]} 
                onPress={() => setLanguageModalVisible(false)}
              >
                <IconSymbol name="xmark" size={16} color={isDark ? '#9BA1A6' : '#687076'} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.themeOptions}>
              {loadingModels ? (
                <View style={{
                  padding: 20,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <Text style={{
                    fontSize: 16,
                    color: isDark ? '#9BA1A6' : '#6B7280',
                  }}>{t('common.loading')}</Text>
                </View>
              ) : (
                textModels.map((model) => (
                  <TouchableOpacity 
                    key={model.model_id}
                    style={[
                      styles.themeOption, 
                      selectedLanguageModel === model.model_id && styles.selectedThemeOption,
                      isDark && styles.themeOptionDark,
                      selectedLanguageModel === model.model_id && isDark && styles.selectedThemeOptionDark,
                      model.is_vip_only && !isVIP && styles.vipLockedOption,
                      model === textModels[textModels.length - 1] && { borderBottomWidth: 0 }
                    ]} 
                    onPress={() => {
                      // 检查是否为VIP模型但用户不是VIP
                      if (model.is_vip_only && !isVIP) {
                        // 关闭模态框
                        setLanguageModalVisible(false);
                        // 如果已登录，显示VIP升级Modal
                        if (user) {
                          setVipUpgradeModalVisible(true);
                        } else {
                          // 未登录，显示登录提示
                          checkIsLoggedIn({
                            showPrompt: true,
                            title: t('settings.vipExclusiveFeature'),
                            message: t('settings.onlyVIPUsersCanUseThisModel')
                          });
                        }
                        return;
                      }
                      handleSelectLanguageModel(model.model_id as LanguageModel);
                    }}
                  >
                    <View style={styles.themeOptionIconContainer}>
                      <IconSymbol 
                        name={
                          model.model_id.includes('qwen') ? 'brain.head.profile' : 
                          model.model_id.includes('deepseek') ? 'cpu' :
                          'sparkles'
                        }
                        size={20} 
                        color={selectedLanguageModel === model.model_id ? (isDark ? '#61dafb' : '#0a7ea4') : isDark ? '#9BA1A6' : '#687076'} 
                      />
                    </View>
                    <View style={styles.themeOptionContent}>
                      <Text style={[
                        styles.themeOptionText, 
                        selectedLanguageModel === model.model_id && styles.selectedThemeOptionText,
                        isDark && styles.themeOptionTextDark,
                        selectedLanguageModel === model.model_id && isDark && styles.selectedThemeOptionTextDark,
                        model.is_vip_only && !isVIP && styles.vipLockedText
                      ]}>{getI18nModelDisplayName(model, t)}</Text>
                      <Text style={[
                        styles.themeOptionSubtext,
                        isDark && styles.themeOptionSubtextDark,
                        model.is_vip_only && !isVIP && styles.vipLockedSubtext
                      ]}>{getModelDisplayDescription(model, t)}</Text>
                    </View>
                    {selectedLanguageModel === model.model_id && (
                      <IconSymbol name="checkmark" size={20} color={isDark ? '#61dafb' : '#0a7ea4'} />
                    )}
                    {model.is_vip_only && (
                      <View style={[
                        styles.vipTagBadge,
                        !isVIP && styles.vipLockedBadge
                      ]}>
                        <Text style={styles.vipTagText}>VIP</Text>
                      </View>
                    )}
                  </TouchableOpacity>
                ))
              )}
            </View>
          </View>
        </Pressable>
      </Modal>
    );
  };
  
  // 在返回的JSX结构中，添加两个Modal组件
  return (
    <ThemedSafeAreaView 
      style={[
        styles.container, 
        { backgroundColor: getColor('#fff', '#151718') }
      ]} 
      edges={['top']}
    >
      <StatusBar style={isDark ? "light" : "dark"} />
      
      {/* 登录提示弹窗 */}
      <LoginPrompt
        visible={loginPromptVisible}
        onClose={hideLoginPrompt}
        title={loginPromptTitle}
        message={loginPromptMessage}
        onLogin={showLoginModal}
      />
      
      <ThemedView style={[
        styles.header, 
        { 
          backgroundColor: getColor('#fff', '#151718'),
          borderBottomColor: getColor('#f0f0f0', '#2B2F31') 
        }
      ]}>
        <ThemedText style={[
          styles.headerTitle,
          { color: getColor('#333', '#ECEDEE') }
        ]}>{t('settings.title')}</ThemedText>
      </ThemedView>
      
      <ThemedScrollView 
        style={styles.scrollView} 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
        bounces={true}
        alwaysBounceVertical={false}
      >
        {/* VIP卡片放在最上方 */}
        {isVIP ? (
          // VIP用户卡片
          <LinearGradient
            colors={isDark ? ['#4338ca', '#3730a3'] : ['#6366f1', '#4f46e5']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.vipCard}>
            <View style={styles.vipBadgeContainer}>
              <LinearGradient
                colors={isDark ? ['#fbbf24', '#d97706'] : ['#fde047', '#ffc107']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.vipBadge}>
                <IconSymbol name="crown.fill" size={12} color={isDark ? "#fff" : "#713f12"} />
                <Text style={styles.vipBadgeText}>{t('settings.vipBadgeText')}</Text>
              </LinearGradient>
            </View>
            <Text style={styles.vipCardTitle}>{t('settings.vipSubscribedCardTitle')}</Text>
            <Text style={styles.vipCardSubtitle}>{t('settings.vipSubscribedCardSubtitle')}</Text>
            
            <TouchableOpacity style={styles.detailButton} onPress={showVipDetail}>
              <Text style={styles.detailButtonText}>{t('settings.checkVipDetail')}</Text>
              <IconSymbol name="chevron.right" size={12} color="white" />
            </TouchableOpacity>
          </LinearGradient>
        ) : (
          // 免费用户卡片
          <LinearGradient
            colors={isDark ? ['#6d28d9', '#5b21b6'] : ['#8364e2', '#6a5ae1']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.vipCard}>
            <View style={styles.vipBadgeContainer}>
              <LinearGradient
                colors={isDark ? ['#f97316', '#ea580c'] : ['#ffb655', '#ff8c37']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.vipBadge}>
                <IconSymbol name="crown.fill" size={12} color="white" />
                <Text style={styles.vipBadgeText}>{t('settings.vipBadgeText')}</Text>
              </LinearGradient>
            </View>
            <Text style={styles.vipCardTitle}>{t('settings.vipCardTitle')}</Text>
            <Text style={styles.vipCardSubtitle}>{t('settings.vipCardSubtitle')}</Text>
            
            <View style={styles.privilegeTagsContainer}>
              <View style={styles.privilegeTag}>
                <IconSymbol name="doc.text" size={10} color="white" />
                <Text style={styles.privilegeTagText}>{t('settings.unlimitedCustomTemplate')}</Text>
              </View>
              <View style={styles.privilegeTag}>
                <IconSymbol name="cpu" size={10} color="white" />
                <Text style={styles.privilegeTagText}>{t('settings.advancedAIModel')}</Text>
              </View>
              <View style={styles.privilegeTag}>
                <IconSymbol name="bolt" size={10} color="white" />
                <Text style={styles.privilegeTagText}>{t('settings.priorityRequest')}</Text>
              </View>
              <View style={styles.privilegeTag}>
                <IconSymbol name="cloud" size={10} color="white" />
                <Text style={styles.privilegeTagText}>{t('vipDetail.features.cloudSync.title')}</Text>
              </View>
            </View>
            
            <TouchableOpacity style={styles.upgradeButton} onPress={upgradeToVIP}>
              <Text style={styles.upgradeButtonText}>
                {t('settings.upgradeNow')}
              </Text>
            </TouchableOpacity>
          </LinearGradient>
        )}
        
        {/* 用户信息卡片 - 根据登录状态显示不同内容 */}
        {isAuthenticated && user ? (
          // 已登录用户显示信息卡片
          <ThemedView 
            style={[
              styles.userCardContainer,
              { 
                backgroundColor: getColor('#fff', '#1E2122'),
                borderColor: getColor('#f0f0f0', '#2B2F31'),
              }
            ]}
          >
            <View style={styles.userCardContent}>
              {/* 用户头像 - 可能是图片或初始字母 */}
              <View style={styles.userAvatarContainer}>
                {getAvatarContent()}
                {isVIP && (
                  <View style={[
                    styles.vipBadgeSmall,
                    { 
                      backgroundColor: isDark ? '#fbbf24' : '#fde047', 
                      borderColor: isDark ? '#1E2122' : '#fff' 
                    }
                  ]}>
                    <IconSymbol name="crown.fill" size={10} color={isDark ? "#000" : "#713f12"} />
                  </View>
                )}
              </View>
              
              <View style={styles.userInfoContainer}>
                {/* 登录方式 - 直接显示文字 */}
                <ThemedText style={[
                  styles.authProviderText,
                  { 
                    color: getColor('#4B5563', '#9BA1A6'), 
                    fontWeight: '500',
                    marginBottom: 4
                  }
                ]}>
                  {/* 确保能获取到正确的provider信息，并添加调试标识 */}
                  {(() => {
                    log('显示登录方式:', user.providerType);
                    switch(user.providerType) {
                      case 'apple': return t("settings.appleLogin");
                      case 'google': return t("settings.googleLogin");
                      case 'wechat': return t("settings.wechatLogin");
                      case 'email': return t("settings.emailLogin");
                      case 'phone': return t("settings.phoneLogin");
                      default: return `${user.providerType || '未知'}方式登录`;
                    }
                  })()}
                </ThemedText>
                
                {/* 用户邮箱 */}
                <ThemedText style={[
                  styles.userEmail,
                  { color: getColor('#1F2937', '#ECEDEE') }
                ]}>
                  {user.email || ''}
                </ThemedText>
              </View>
              
              {/* 退出登录按钮 */}
              <TouchableOpacity
                style={styles.logoutButton}
                onPress={handleLogout}
              >
                <IconSymbol
                  name="rectangle.portrait.and.arrow.right"
                  size={20}
                  color={getColor('#EF4444', '#F87171')}
                />
              </TouchableOpacity>
            </View>
          </ThemedView>
        ) : (
          // 未登录用户显示登录引导卡片
          <ThemedView 
            style={[
              styles.userCardContainer,
              { 
                backgroundColor: getColor('#fff', '#1E2122'),
                borderColor: getColor('#f0f0f0', '#2B2F31'),
              }
            ]}
          >
            <View style={styles.loginGuideContent}>
              <View style={[
                styles.loginGuideIconContainer,
                { backgroundColor: getColor('rgba(106, 90, 225, 0.1)', 'rgba(139, 92, 246, 0.15)') }
              ]}>
                <IconSymbol 
                  name="person.circle" 
                  size={28} 
                  color={getColor('#6a5ae1', '#8364e2')} 
                />
              </View>
              
              <View style={styles.loginGuideTextContainer}>
                <ThemedText style={[
                  styles.loginGuideTitle,
                  { color: getColor('#1F2937', '#ECEDEE') }
                ]}>
                  {t('settings.loginGuideTitle')}
                </ThemedText>
                
                <ThemedText style={[
                  styles.loginGuideSubtitle,
                  { color: getColor('#6B7280', '#9BA1A6') }
                ]}>
                  {t('settings.loginGuideSubtitle')}
                </ThemedText>
              </View>
              
              <TouchableOpacity 
                style={[
                  styles.loginGuideButton,
                  { backgroundColor: getColor('#6a5ae1', '#7c3aed') }
                ]}
                onPress={handleLogin}
              >
                <ThemedText style={styles.loginGuideButtonText}>
                  {t('settings.loginGuideButtonText')}
                </ThemedText>
              </TouchableOpacity>
            </View>
          </ThemedView>
        )}
        
        {/* 设置选项组 - 风格模板相关（第一组） */}
        <ThemedView style={[
          styles.settingsGroupContainer,
          { 
            backgroundColor: getColor('#fff', '#1E2122'),
            borderColor: getColor('#f0f0f0', '#2B2F31'),
          }
        ]}>
          {/* 新增：风格模板优化和极速模式并列一行Card样式 */}
          <View style={styles.switchCardRow}>
            <SwitchCard
              icon="wand.and.stars"
              iconBgColor={isDark ? '#1E40AF' : '#DBEAFE'}
              iconColor={isDark ? '#60A5FA' : '#2563EB'}
              title={t('settings.styleOptimization')}
              subtitle={t('settings.styleOptimizationDescription')}
              value={isStyleOptimizationOn}
              onValueChange={handleToggleStyleOptimization}
              isDark={isDark}
            />
            <View style={{ width: 12 }} />
            <SwitchCard
              icon="bolt.fill"
              iconBgColor={isDark ? '#7C2D12' : '#FEF3C7'}
              iconColor={isDark ? '#FBBF24' : '#D97706'}
              title={t('settings.fastMode')}
              subtitle={isStyleOptimizationOn ? t('settings.fastModeDescription') : t('settings.fastModeDisabledDescription')}
              value={isFastModeOn}
              onValueChange={handleToggleFastMode}
              disabled={!isStyleOptimizationOn}
              isDark={isDark}
            />
          </View>
          {/* 其余设置项保持原有竖排 */}
          <SettingsItem 
            icon="paintpalette" 
            iconBgColor={isDark ? '#3B0764' : '#F3E8FF'} 
            iconColor={isDark ? '#C084FC' : '#9333EA'}
            title={t('settings.styleTemplate')} 
            hasArrow={true} 
            onPress={navigateToTemplates}
            isDark={isDark}
          />
          <SettingsItem 
            icon="slider.horizontal.3" 
            iconBgColor={isDark ? '#451A03' : '#FEF3C7'} 
            iconColor={isDark ? '#FBBF24' : '#D97706'}
            title={t('settings.templatePersonalization')} 
            hasArrow={true} 
            onPress={navigateToTemplateManager}
            isLast={true}
            isDark={isDark}
          />
        </ThemedView>
        
        {/* 设置选项组 - 界面风格和语言（第二组） */}
        <ThemedView style={[
          styles.settingsGroupContainer,
          { 
            backgroundColor: getColor('#fff', '#1E2122'),
            borderColor: getColor('#f0f0f0', '#2B2F31'),
          }
        ]}>
          <SettingsItem 
            icon="paintpalette" 
            iconBgColor={isDark ? '#052E16' : '#DCFCE7'} 
            iconColor={isDark ? '#4ADE80' : '#16A34A'}
            title={t('settings.themeStyle')} 
            subtitle={getThemeModeText(themeMode)} 
            hasArrow={true} 
            onPress={showThemeModal}
            isDark={isDark}
          />
          <SettingsItem 
            icon="globe" 
            iconBgColor={isDark ? '#082F49' : '#DBEAFE'}
            iconColor={isDark ? '#60A5FA' : '#2563EB'}
            title={t('settings.uiLanguage')} 
            subtitle={getCurrentLanguageName()} 
            hasArrow={true}
            onPress={showLanguageSelectModal}
            isLast={true}
            isDark={isDark}
          />
        </ThemedView>
        
        {/* 设置选项组 - AI模型设置 */}
        <ThemedView style={[
          styles.settingsGroupContainer,
          { 
            backgroundColor: getColor('#fff', '#1E2122'),
            borderColor: getColor('#f0f0f0', '#2B2F31'),
          }
        ]}>
          {getModelSettingItems()}
        </ThemedView>
        

        {/* 设置选项组 - 关于和数据管理 */}
        <ThemedView style={[
          styles.settingsGroupContainer,
          {
            backgroundColor: getColor('#fff', '#1E2122'),
            borderColor: getColor('#f0f0f0', '#2B2F31'),
          }
        ]}>
          <SettingsItem
            icon="externaldrive"
            iconBgColor={isDark ? '#065F46' : '#D1FAE5'}
            iconColor={isDark ? '#34D399' : '#10B981'}
            title={t('settings.dataManagement')}
            hasArrow={true}
            isLast={false}
            onPress={() => router.push('/settings/data-management' as any)}
            isDark={isDark}
          />
          <SettingsItem
            icon="info.circle"
            iconBgColor={isDark ? '#082F49' : '#DBEAFE'}
            iconColor={isDark ? '#60A5FA' : '#3B82F6'}
            title={t('settings.about')}
            subtitle="v1.0.0"
            hasArrow={true}
            isLast={true}
            onPress={() => router.push('/settings/about' as any)}
            isDark={isDark}
          />
        </ThemedView>
        
        {/* 底部空间 */}
        <View style={styles.bottomSpace} />
      </ThemedScrollView>

      {/* 主题选择弹窗 */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={themeModalVisible}
        onRequestClose={hideThemeModal}
      >
        <Pressable
          style={styles.themeModalOverlay}
          onPress={hideThemeModal}
        >
          <View 
            style={[styles.modalContainer, isDark && styles.modalContainerDark]}
            onStartShouldSetResponder={() => true}
            onTouchEnd={(e) => {
              e.stopPropagation();
            }}
          >
            <View style={[styles.themeModalHeader, isDark && styles.modalHeaderDark]}>
              <Text style={[styles.modalTitle, isDark && { color: '#ECEDEE' }]}>{t('settings.selectThemeStyle')}</Text>
              <TouchableOpacity 
                style={[styles.themeCloseButton, isDark && styles.closeButtonDark]} 
                onPress={hideThemeModal}
              >
                <IconSymbol name="xmark" size={16} color={isDark ? '#9BA1A6' : '#687076'} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.themeOptions}>
              <TouchableOpacity 
                style={[
                  styles.themeOption, 
                  themeMode === 'system' && styles.selectedThemeOption,
                  isDark && styles.themeOptionDark,
                  themeMode === 'system' && isDark && styles.selectedThemeOptionDark
                ]} 
                onPress={() => handleSetThemeMode('system')}
              >
                <View style={styles.themeOptionIconContainer}>
                  <IconSymbol 
                    name="gear" 
                    size={20} 
                    color={themeMode === 'system' ? (isDark ? '#61dafb' : '#0a7ea4') : isDark ? '#9BA1A6' : '#687076'} 
                  />
                </View>
                <View style={styles.themeOptionContent}>
                  <Text style={[
                    styles.themeOptionText, 
                    themeMode === 'system' && styles.selectedThemeOptionText,
                    isDark && styles.themeOptionTextDark,
                    themeMode === 'system' && isDark && styles.selectedThemeOptionTextDark
                  ]}>{t('settings.themeSystemStyle')}</Text>
                  <Text style={[
                    styles.themeOptionSubtext,
                    isDark && styles.themeOptionSubtextDark
                  ]}>{t('settings.themeSystemStyleDesc')}</Text>
                </View>
                {themeMode === 'system' && (
                  <IconSymbol name="checkmark" size={20} color={isDark ? '#61dafb' : '#0a7ea4'} />
                )}
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[
                  styles.themeOption, 
                  themeMode === 'light' && styles.selectedThemeOption,
                  isDark && styles.themeOptionDark,
                  themeMode === 'light' && isDark && styles.selectedThemeOptionDark
                ]} 
                onPress={() => handleSetThemeMode('light')}
              >
                <View style={styles.themeOptionIconContainer}>
                  <IconSymbol 
                    name="sun.max" 
                    size={20} 
                    color={themeMode === 'light' ? (isDark ? '#61dafb' : '#0a7ea4') : isDark ? '#9BA1A6' : '#687076'} 
                  />
                </View>
                <View style={styles.themeOptionContent}>
                  <Text style={[
                    styles.themeOptionText, 
                    themeMode === 'light' && styles.selectedThemeOptionText,
                    isDark && styles.themeOptionTextDark,
                    themeMode === 'light' && isDark && styles.selectedThemeOptionTextDark
                  ]}>{t('settings.themeLightStyle')}</Text>
                  <Text style={[
                    styles.themeOptionSubtext,
                    isDark && styles.themeOptionSubtextDark
                  ]}>{t('settings.themeLightStyleDesc')}</Text>
                </View>
                {themeMode === 'light' && (
                  <IconSymbol name="checkmark" size={20} color={isDark ? '#61dafb' : '#0a7ea4'} />
                )}
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[
                  styles.themeOption, 
                  themeMode === 'dark' && styles.selectedThemeOption,
                  isDark && styles.themeOptionDark,
                  themeMode === 'dark' && isDark && styles.selectedThemeOptionDark,
                  { borderBottomWidth: 0 }
                ]} 
                onPress={() => handleSetThemeMode('dark')}
              >
                <View style={styles.themeOptionIconContainer}>
                  <IconSymbol 
                    name="moon" 
                    size={20} 
                    color={themeMode === 'dark' ? (isDark ? '#61dafb' : '#0a7ea4') : isDark ? '#9BA1A6' : '#687076'} 
                  />
                </View>
                <View style={styles.themeOptionContent}>
                  <Text style={[
                    styles.themeOptionText, 
                    themeMode === 'dark' && styles.selectedThemeOptionText,
                    isDark && styles.themeOptionTextDark,
                    themeMode === 'dark' && isDark && styles.selectedThemeOptionTextDark
                  ]}>{t('settings.themeDarkStyle')}</Text>
                  <Text style={[
                    styles.themeOptionSubtext,
                    isDark && styles.themeOptionSubtextDark
                  ]}>{t('settings.themeDarkStyleDesc')}</Text>
                </View>
                {themeMode === 'dark' && (
                  <IconSymbol name="checkmark" size={20} color={isDark ? '#61dafb' : '#0a7ea4'} />
                )}
              </TouchableOpacity>
            </View>
          </View>
        </Pressable>
      </Modal>

      {/* 语音转写模型选择弹窗 */}
      {renderTranscribeModelModal()}

      {/* 语言选择弹窗 */}
      {renderLanguageSelectModal()}
      <LanguageSwitcher 
        visible={isSwitchingLanguage} 
        onComplete={handleLanguageSwitchComplete} 
      />
      {renderLanguageModelModal()}

      {/* VIP升级Modal */}
      <VipUpgradeModal
        visible={vipUpgradeModalVisible}
        onClose={() => setVipUpgradeModalVisible(false)}
        title={t('settings.vipExclusiveFeature')}
        description={t('settings.upgradeToUseVipModel')}
      />
    </ThemedSafeAreaView>
  );
}

/**
 * 设置项组件属性类型
 */
interface SettingsItemProps {
  icon: IconSymbolName;
  iconBgColor: string;
  iconColor: string;
  title: string;
  subtitle?: string;
  subtitleColor?: string;
  hasArrow?: boolean;
  isLast?: boolean;
  isVipLocked?: boolean;
  onPress?: () => void;
  isDark?: boolean;
  hasSwitch?: boolean;
  switchValue?: boolean;
  onSwitchChange?: (value: boolean) => void;
  disabled?: boolean;
}

/**
 * 设置项组件
 */
function SettingsItem({ 
  icon, 
  iconBgColor,
  iconColor,
  title, 
  subtitle, 
  subtitleColor = '#6B7280',
  hasArrow = false, 
  isLast = false, 
  isVipLocked = false,
  onPress,
  isDark = false,
  hasSwitch = false,
  switchValue = false,
  onSwitchChange,
  disabled = false
}: SettingsItemProps) {
  return (
    <TouchableOpacity 
      style={[
        styles.settingsItem, 
        isLast ? null : styles.settingsItemBorder,
        !isLast && { borderBottomColor: isDark ? '#2B2F31' : '#f3f4f6' }
      ]}
      onPress={hasSwitch ? undefined : onPress}
      disabled={hasSwitch || disabled}
    >
      <View style={styles.settingsItemContent}>
        <View style={[styles.iconBackground, { backgroundColor: disabled ? (isDark ? '#39393D' : '#E5E7EB') : iconBgColor }]}>
          <IconSymbol name={icon} size={16} color={disabled ? (isDark ? '#6B7280' : '#9CA3AF') : iconColor} />
        </View>
        <View style={styles.settingsItemTextContainer}>
          <Text style={[
            styles.settingsItemTitle,
            { color: disabled ? (isDark ? '#6B7280' : '#9CA3AF') : (isDark ? '#ECEDEE' : '#333') }
          ]}>{title}</Text>
          {subtitle && hasSwitch && (
            <Text style={[
              styles.settingsItemSubtitle,
              { 
                color: disabled ? (isDark ? '#4B5563' : '#D1D5DB') : subtitleColor, 
                fontSize: 13, 
                marginTop: 2 
              }
            ]}>{subtitle}</Text>
          )}
        </View>
      </View>
      
      <View style={styles.settingsItemRight}>
        {isVipLocked && (
          <View style={styles.vipLockContainer}>
            <View style={styles.lockIcon}>
              <IconSymbol name="lock.fill" size={10} color="#6a5ae1" />
            </View>
          </View>
        )}
        
        {hasSwitch ? (
          <Switch
            value={switchValue}
            onValueChange={disabled ? undefined : onSwitchChange}
            disabled={disabled}
            trackColor={{ 
              false: disabled ? (isDark ? '#2B2F31' : '#F3F4F6') : (isDark ? '#39393D' : '#E5E7EB'), 
              true: disabled ? (isDark ? '#4B5563' : '#D1D5DB') : (isDark ? '#8364E2' : '#6A5AE1')
            }}
            thumbColor={disabled ? (isDark ? '#6B7280' : '#9CA3AF') : '#FFFFFF'}
            ios_backgroundColor={disabled ? (isDark ? '#2B2F31' : '#F3F4F6') : (isDark ? '#39393D' : '#E5E7EB')}
          />
        ) : (
          <>
            {subtitle && !hasSwitch && (
              <Text style={[styles.settingsItemSubtitle, { color: subtitleColor }]}>{subtitle}</Text>
            )}
            
            {hasArrow && <IconSymbol size={16} name="chevron.right" color={isDark ? '#9BA1A6' : '#9CA3AF'} />}
          </>
        )}
      </View>
    </TouchableOpacity>
  );
}

/**
 * VIP特权项组件属性类型
 */
interface VipFeatureItemProps {
  icon: IconSymbolName;
  iconBgColor: string;
  iconColor: string;
  title: string;
  description: string;
  isLast?: boolean;
  isDark?: boolean;
}

/**
 * VIP特权项组件
 */
function VipFeatureItem({
  icon,
  iconBgColor,
  iconColor,
  title,
  description,
  isLast = false,
  isDark = false
}: VipFeatureItemProps) {
  return (
    <View style={[
      styles.vipFeatureRow, 
      !isLast && styles.vipFeatureBorder,
      !isLast && { borderBottomColor: isDark ? '#2B2F31' : '#f3f4f6' }
    ]}>
      <View style={[styles.vipFeatureIcon, { backgroundColor: iconBgColor }]}>
        <IconSymbol name={icon} size={20} color={iconColor} />
      </View>
      <View style={styles.vipFeatureContent}>
        <Text style={[
          styles.vipFeatureTitle,
          { color: isDark ? '#ECEDEE' : '#1F2937' }
        ]}>{title}</Text>
        <Text style={[
          styles.vipFeatureDescription,
          { color: isDark ? '#9BA1A6' : '#6B7280' }
        ]}>{description}</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    minHeight: 58, 
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  scrollView: {
    flex: 1,
  },
  vipCard: {
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    borderRadius: 16,
    overflow: 'hidden',
    padding: 20,
    position: 'relative',
    elevation: 3,
    shadowColor: "#6a5ae1",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
  },
  vipBadgeContainer: {
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  vipBadge: {
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    width: 'auto',
  },
  vipBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: 'white',
    marginLeft: 4,
  },
  vipCardTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  vipCardSubtitle: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
    marginBottom: 16,
  },
  privilegeTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  privilegeTag: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  privilegeTagText: {
    color: 'white',
    fontSize: 12,
    marginLeft: 4,
  },
  upgradeButton: {
    backgroundColor: 'white',
    borderRadius: 24,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignSelf: 'flex-start',
  },
  upgradeButtonText: {
    color: '#6a5ae1',
    fontWeight: 'bold',
    fontSize: 14,
  },
  priceText: {
    fontWeight: 'normal',
  },
  detailButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 24,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignSelf: 'flex-start',
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
    marginRight: 4,
  },
  settingsGroupContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    elevation: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  settingsItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  iconBackground: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingsItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 12,
  },
  settingsItemTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  settingsItemTitle: {
    fontSize: 16,
    color: '#333',
  },
  settingsItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingsItemSubtitle: {
    fontSize: 14,
    marginRight: 8,
  },
  vipLockContainer: {
    marginRight: 8,
  },
  lockIcon: {
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: 'rgba(106, 90, 225, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteAllDataButton: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 32,
    paddingVertical: 14,
    borderRadius: 16,
    backgroundColor: 'white',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  deleteAllDataText: {
    color: '#EF4444',
    fontWeight: '500',
  },
  bottomSpace: {
    height: 32,
    paddingBottom: 50,
  },
  scrollViewContent: {
    paddingBottom: 50,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 16,
    backgroundColor: '#fff',
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F5',
    position: 'relative',
  },
  modalTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  closeButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBody: {
    paddingVertical: 16,
    maxHeight: 360,
  },
  membershipStatus: {
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  membershipStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  membershipStatusText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginLeft: 8,
  },
  membershipExpiry: {
    fontSize: 12,
    color: '#6B7280',
  },
  vipFeaturesContainer: {
    marginBottom: 16,
  },
  vipFeaturesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  vipFeatureRow: {
    flexDirection: 'row',
    paddingVertical: 16,
  },
  vipFeatureBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  vipFeatureIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  vipFeatureContent: {
    flex: 1,
  },
  vipFeatureTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 4,
  },
  vipFeatureDescription: {
    fontSize: 13,
    color: '#6B7280',
    lineHeight: 18,
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  changePlanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 16,
    marginTop: 12,
    marginBottom: 16,
  },
  changePlanButtonText: {
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 6,
  },
  accountButton: {
    marginHorizontal: 16,
    marginTop: 16,
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: 'white',
    alignItems: 'center',
  },
  userCardContainer: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    elevation: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  userCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 14,
  },
  userAvatarContainer: {
    position: 'relative',
    marginRight: 14,
  },
  userAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#f3f4f6',
  },
  vipBadgeSmall: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: '#fde047',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  userInfoContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  authProviderText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  userEmail: {
    fontSize: 15,
    color: '#1F2937',
    fontWeight: '500',
  },
  logoutButton: {
    padding: 10,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginGuideContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 14,
  },
  loginGuideIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  loginGuideTextContainer: {
    flex: 1,
  },
  loginGuideTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  loginGuideSubtitle: {
    fontSize: 14,
    lineHeight: 18,
  },
  loginGuideButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginGuideButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  planOptionsContainer: {
    marginHorizontal: 16,
    marginTop: 12,
    marginBottom: 16,
  },
  planOption: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  planOptionText: {
    fontWeight: '600',
    fontSize: 14,
  },
  cancelButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 4,
  },
  cancelButtonText: {
    fontWeight: '500',
    fontSize: 14,
  },
  themeModalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 16,
    backgroundColor: '#fff',
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalContainerDark: {
    backgroundColor: '#1E2122',
  },
  themeModalHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F5',
    position: 'relative',
  },
  modalHeaderDark: {
    borderBottomColor: '#2B2F31',
  },
  modalTitleDark: {
    color: '#ECEDEE',
  },
  themeCloseButton: {
    position: 'absolute',
    right: 12,
    top: 12,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F1F3F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonDark: {
    backgroundColor: '#26292B',
  },
  themeOptions: {
    paddingBottom: 12,
  },
  themeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F3F5',
  },
  themeOptionDark: {
    borderBottomColor: '#2B2F31',
  },
  selectedThemeOption: {
    backgroundColor: 'rgba(10, 126, 164, 0.05)',
  },
  selectedThemeOptionDark: {
    backgroundColor: 'rgba(97, 218, 251, 0.05)',
  },
  themeOptionIconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  themeOptionContent: {
    flex: 1,
  },
  themeOptionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#11181C',
    marginBottom: 2,
  },
  themeOptionTextDark: {
    color: '#ECEDEE',
  },
  selectedThemeOptionText: {
    color: '#0a7ea4',
    fontWeight: '600',
  },
  selectedThemeOptionTextDark: {
    color: '#61dafb',
  },
  themeOptionSubtext: {
    fontSize: 14,
    color: '#687076',
  },
  themeOptionSubtextDark: {
    color: '#9BA1A6',
  },
  switch: {
    width: 36,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#ccc',
    justifyContent: 'center',
    padding: 2,
  },
  switchActive: {
    backgroundColor: '#6366f1',
  },
  switchThumb: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'white',
  },
  switchThumbActive: {
    transform: [{ translateX: 16 }],
  },
  modalBodyContent: {
    height: '100%',
    paddingBottom: 8,
  },
  settingItemContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#FFFFFF',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIcon: {
    marginRight: 12,
  },
  settingTitle: {
    fontSize: 16,
    color: 'white',
    fontWeight: '600',
  },
  vipTagBadge: {
    backgroundColor: '#7c3aed',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 6,
  },
  vipTagText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  vipLockedBadge: {
    backgroundColor: '#9ca3af',
    opacity: 0.8,
  },
  vipLockedOption: {
    opacity: 0.7,
  },
  vipLockedText: {
    color: '#9ca3af',
  },
  vipLockedSubtext: {
    color: '#9ca3af',
    opacity: 0.8
  },
  switchCardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'stretch',
    marginHorizontal: 8,
    marginTop: 12,
    marginBottom: 8,
  },
  switchCard: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 14,
    marginBottom: 0,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    minWidth: 0,
  },
  switchCardDark: {
    backgroundColor: '#23272A',
    borderColor: '#2B2F31',
  },
  switchCardDisabled: {
    opacity: 0.6,
  },
  switchCardTopRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    minWidth: 0,
  },
  switchCardIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  switchCardTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 0,
    maxWidth: 120,
  },
  switchCardTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  switchCardSubtitle: {
    fontSize: 12,
    lineHeight: 16,
    marginTop: 2,
    flexShrink: 1,
    textAlign: 'left',
  },
  switchCardTopRowNoIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    minWidth: 0,
    paddingLeft: 2,
  },
  switchCardGradient: {
    flex: 1,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    minWidth: 0,
    paddingVertical: 16,
    paddingHorizontal: 14,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
  },
  switchCardGradientDark: {
    borderColor: '#2B2F31',
  },
  switchCardContent: {
    flex: 1,
  },
  switchCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
}); 