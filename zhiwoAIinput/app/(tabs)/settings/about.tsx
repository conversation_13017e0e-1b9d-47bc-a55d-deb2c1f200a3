import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  StyleSheet,
  SafeAreaView,
  Platform,
  StatusBar,
  Linking,
} from "react-native";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { IconSymbol } from "@/components/ui/IconSymbol";
import * as Application from "expo-application";
import { RatingService } from "@/services/ratingService";
import { ThemedView } from "@/components/ThemedView";
import { useAppTheme } from "@/hooks/useAppTheme";
import { error as logError } from "@/services/logService";
import { FeedbackModal } from "@/components/ui/FeedbackModal";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { ThemedSafeAreaView } from "@/components/ThemedSafeAreaView";

export default function AboutScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const currentTheme = useAppTheme();
  const isDark = currentTheme === "dark";
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const { user } = useSelector((state: RootState) => state.auth);

  // 获取应用信息
  const appVersion = Application.nativeApplicationVersion || "1.0.0";
  const appName = t("settings.appName" as any) as string;
  const platform = Platform.OS;

  // 处理评价功能
  const handleRateUs = () => {
    setShowFeedbackModal(true);
  };

  // 处理实际评分
  const handleActualRating = async () => {
    try {
      await RatingService.smartRatingRequest();
    } catch (error) {
      logError("评价功能出错:", error);
    }
  };

  // 处理产品反馈
  const handleProductFeedback = async () => {
    const systemVersion = Platform.Version || "unknown";
    const subject = encodeURIComponent(
      t("settings.feedbackEmailSubject", {
        platform,
        version: appVersion,
        defaultValue: "知我AI输入法反馈",
      })
    );
    const body = encodeURIComponent(
      [
        t("settings.feedbackEmailBodyLine6", {
          defaultValue: "\n请在此描述您遇到的问题或建议：\n\n",
        }),
        t("settings.feedbackEmailBodyLine1", {
          defaultValue: "应用名称: 知我AI输入法",
        }),
        t("settings.feedbackEmailBodyLine2", {
          version: appVersion,
          defaultValue: `应用版本: ${appVersion}`,
        }),
        t("settings.feedbackEmailBodyLine3", {
          platform,
          defaultValue: `系统类型: ${platform}`,
        }),
        t("settings.feedbackEmailBodyLine4", {
          systemVersion,
          defaultValue: `系统版本: ${systemVersion}`,
        }),
        t("settings.feedbackEmailBodyLine5", {
          userId:
            user?.id || t("common.notLoggedIn", { defaultValue: "未登录" }),
          defaultValue: `用户ID: ${user?.id || "未登录"}`,
        }),
      ].join("\n")
    );
    const mailtoUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    try {
      await Linking.openURL(mailtoUrl);
    } catch (error) {
      logError("打开邮件客户端失败:", error);
    }
  };

  // 处理版本更新
  const handleVersionUpdate = () => {
    const storeUrl =
      Platform.OS === "ios"
        ? "https://apps.apple.com/app/id6744967748"
        : "https://play.google.com/store/apps/details?id=com.mindpowerhk.knowmetype";
    Linking.openURL(storeUrl).catch((err) => logError("打开应用商店失败", err));
  };

  // 处理隐私政策
  const handlePrivacyPolicy = () => {
    const privacyUrl = "https://www.knowme-type.com/en/privacy-policy";
    Linking.openURL(privacyUrl).catch((err) =>
      logError("打开隐私政策失败", err)
    );
  };

  // 处理使用协议
  const handleTermsOfService = () => {
    const termsUrl = "https://www.knowme-type.com/en/terms-of-service";
    Linking.openURL(termsUrl).catch((err) => logError("打开使用协议失败", err));
  };

  return (
    <ThemedSafeAreaView
      style={[
        styles.container,
        { backgroundColor: isDark ? "#151718" : "white" },
      ]}
      edges={["top"]}
    >
      {/* 顶部导航栏 */}
      <ThemedView style={[styles.header, isDark && styles.headerDark]}>
        <View style={styles.headerLeft}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol
              name="chevron.left"
              size={20}
              color={isDark ? "#9BA1A6" : "#6B7280"}
            />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, isDark && styles.headerTitleDark]}>
            {t("settings.about")}
          </Text>
        </View>
        <View style={styles.placeholder} />
      </ThemedView>

      <ScrollView
        style={[
          styles.scrollView,
          { backgroundColor: isDark ? "#151718" : "white"},
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* 应用Logo和信息 */}
        <View style={styles.appInfoContainer}>
          <Image
            source={require("@/assets/images/icon.png")}
            style={styles.appIcon}
          />
          <Text
            style={[styles.appName, { color: isDark ? "#FBFBFD" : "#1D1D1F" }]}
          >
            {appName}
          </Text>
          <Text
            style={[
              styles.appVersion,
              { color: isDark ? "#9CA3AF" : "#6B7280" },
            ]}
          >
            {t("settings.version", { version: appVersion })}
          </Text>
        </View>

        {/* 功能选项 */}
        <View style={styles.optionsContainer}>
          {/* 评价应用 */}
          <TouchableOpacity
            style={[
              styles.optionItem,
              {
                backgroundColor: isDark ? "#1E2122" : "#FFFFFF",
                borderBottomColor: isDark ? "#2B2F31" : "#E5E7EB",
              },
            ]}
            onPress={handleRateUs}
          >
            <View style={styles.optionLeft}>
              <View
                style={[
                  styles.optionIcon,
                  { backgroundColor: isDark ? "#451A03" : "#FEF3C7" },
                ]}
              >
                <IconSymbol
                  name="star.fill"
                  size={20}
                  color={isDark ? "#FDE047" : "#EABE41"}
                />
              </View>
              <Text
                style={[
                  styles.optionTitle,
                  { color: isDark ? "#FBFBFD" : "#1D1D1F" },
                ]}
              >
                {t("settings.rateUs")}
              </Text>
            </View>
            <IconSymbol
              name="chevron.right"
              size={16}
              color={isDark ? "#9CA3AF" : "#6B7280"}
            />
          </TouchableOpacity>

          {/* 产品问题反馈 */}
          <TouchableOpacity
            style={[
              styles.optionItem,
              {
                backgroundColor: isDark ? "#1E2122" : "#FFFFFF",
                borderBottomColor: isDark ? "#2B2F31" : "#E5E7EB",
              },
            ]}
            onPress={handleProductFeedback}
          >
            <View style={styles.optionLeft}>
              <View
                style={[
                  styles.optionIcon,
                  { backgroundColor: isDark ? "#082F49" : "#E0F2FE" },
                ]}
              >
                <IconSymbol
                  name="envelope.fill"
                  size={20}
                  color={isDark ? "#38BDF8" : "#0EA5E9"}
                />
              </View>
              <Text
                style={[
                  styles.optionTitle,
                  { color: isDark ? "#FBFBFD" : "#1D1D1F" },
                ]}
              >
                {t("settings.productFeedback" as any)}
              </Text>
            </View>
            <IconSymbol
              name="chevron.right"
              size={16}
              color={isDark ? "#9CA3AF" : "#6B7280"}
            />
          </TouchableOpacity>

          {/* 使用协议 */}
          <TouchableOpacity
            style={[
              styles.optionItem,
              {
                backgroundColor: isDark ? "#1E2122" : "#FFFFFF",
                borderBottomColor: isDark ? "#2B2F31" : "#E5E7EB",
              },
            ]}
            onPress={handleTermsOfService}
          >
            <View style={styles.optionLeft}>
              <View
                style={[
                  styles.optionIcon,
                  { backgroundColor: isDark ? "#082F49" : "#E0F2FE" },
                ]}
              >
                <IconSymbol
                  name="doc.text"
                  size={20}
                  color={isDark ? "#38BDF8" : "#0EA5E9"}
                />
              </View>
              <Text
                style={[
                  styles.optionTitle,
                  { color: isDark ? "#FBFBFD" : "#1D1D1F" },
                ]}
              >
                {t("settings.termsOfService")}
              </Text>
            </View>
            <IconSymbol
              name="chevron.right"
              size={16}
              color={isDark ? "#9CA3AF" : "#6B7280"}
            />
          </TouchableOpacity>

          {/* 隐私政策 */}
          <TouchableOpacity
            style={[
              styles.optionItem,
              {
                backgroundColor: isDark ? "#1E2122" : "#FFFFFF",
                borderBottomColor: isDark ? "#2B2F31" : "#E5E7EB",
              },
            ]}
            onPress={handlePrivacyPolicy}
          >
            <View style={styles.optionLeft}>
              <View
                style={[
                  styles.optionIcon,
                  { backgroundColor: isDark ? "#450A0A" : "#FEE2E2" },
                ]}
              >
                <IconSymbol
                  name="lock.shield"
                  size={20}
                  color={isDark ? "#F87171" : "#EF4444"}
                />
              </View>
              <Text
                style={[
                  styles.optionTitle,
                  { color: isDark ? "#FBFBFD" : "#1D1D1F" },
                ]}
              >
                {t("settings.privacyPolicy")}
              </Text>
            </View>
            <IconSymbol
              name="chevron.right"
              size={16}
              color={isDark ? "#9CA3AF" : "#6B7280"}
            />
          </TouchableOpacity>

          {/* 版本更新 */}
          <TouchableOpacity
            style={[
              styles.optionItem,
              styles.lastOption,
              {
                backgroundColor: isDark ? "#1E2122" : "#FFFFFF",
              },
            ]}
            onPress={handleVersionUpdate}
          >
            <View style={styles.optionLeft}>
              <View
                style={[
                  styles.optionIcon,
                  { backgroundColor: isDark ? "#065F46" : "#D1FAE5" },
                ]}
              >
                <IconSymbol
                  name="arrow.down.circle"
                  size={20}
                  color={isDark ? "#34D399" : "#10B981"}
                />
              </View>
              <Text
                style={[
                  styles.optionTitle,
                  { color: isDark ? "#FBFBFD" : "#1D1D1F" },
                ]}
              >
                {t("settings.versionUpdate")}
              </Text>
            </View>
            <IconSymbol
              name="chevron.right"
              size={16}
              color={isDark ? "#9CA3AF" : "#6B7280"}
            />
          </TouchableOpacity>
        </View>

        {/* 底部空间 */}
        <View style={styles.bottomSpace} />
      </ScrollView>

      {/* 底部开发商信息 */}
      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: "#9CA3AF" }]}>
          盈思動力（香港） 有限公司版權所有
        </Text>
        <Text style={[styles.footerText, { color: "#9CA3AF" }]}>
          Copyright © 2025・MindPower(HongKong) Limited・All rights reserved.
        </Text>
      </View>

      {/* 评分反馈弹窗 */}
      <FeedbackModal
        visible={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        onRate={handleActualRating}
      />
    </ThemedSafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: Platform.OS === "android" ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    minHeight: 58,
  },
  headerDark: {
    backgroundColor: "#151718",
    borderBottomColor: "#2B2F31",
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 8,
    padding: 0,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#111827",
  },
  headerTitleDark: {
    color: "#ECEDEE",
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  appInfoContainer: {
    alignItems: "center",
    paddingTop: 40,
    paddingBottom: 40,
    paddingHorizontal: 20,
  },
  appIcon: {
    width: 80,
    height: 80,
    borderRadius: 16,
    marginBottom: 16,
  },
  appName: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
  },
  appVersion: {
    fontSize: 16,
    fontWeight: "400",
  },
  optionsContainer: {
    marginHorizontal: 16,
    borderRadius: 16,
    overflow: "hidden",
    elevation: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  optionItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  lastOption: {
    borderBottomWidth: 0,
  },
  optionLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  optionIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: "500",
  },
  bottomSpace: {
    height: 80,
  },
  footer: {
    alignItems: "center",
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  footerText: {
    fontSize: 12,
    lineHeight: 16,
    textAlign: "center",
  },
});
