import {
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Platform,
  Dimensions,
  Easing,
  Share,
  Linking,
  View as RNView,
  Text as RNText,
  TextInput,
  Keyboard,
  KeyboardAvoidingView,
  ActivityIndicator,
  Pressable,
  AppState,
} from "react-native";
import {
  SafeAreaView,
  useSafeAreaInsets,
} from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import { IconSymbol } from "@/components/ui/IconSymbol";
import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo,
} from "react";
import { Animated as RNAnimated } from "react-native";
import { useNavigationState } from "@react-navigation/native";
import { DeviceEventEmitter } from "react-native";
import { useFocusEffect, useIsFocused } from "@react-navigation/native";
import { useLocalSearchParams, router, useNavigation } from "expo-router";
import * as AIService from "@/services/aiService";
import { processTextWithAI, saveInputSession } from "@/utils/inputUtils";
import * as Clipboard from "expo-clipboard";
import * as Haptics from "expo-haptics";
import { useAppTheme } from "@/hooks/useAppTheme";
import { Text } from "@/components/Text";
import { ThemedView as View } from "@/components/ThemedView";
import { ThemedSafeAreaView } from "@/components/ThemedSafeAreaView";
import { useSpeechRecording } from "@/hooks/useSpeechRecording";
import RecognizedTextDisplay from "@/components/input/RecognizedTextDisplay";
import Toast from "react-native-toast-message";
import { showCenteredToast, showCopyBadge } from "@/utils/toastConfig";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  startRecording as startSpeechRecording,
  pauseRecording as pauseSpeechRecording,
  resumeRecording as resumeSpeechRecording,
  stopRecording as stopSpeechRecording,
  optimizeText,
  resetRecording,
  updateRecognizedText as updateRecognizedTextAction,
  setNetworkError,
  clearNetworkError,
  setWhisperLimitError,
  clearWhisperLimitError,
  hideWhisperLimitModal,
} from "@/store/slices/recordingSlice";
import { RootState, AppDispatch } from "@/store";
import { loadGroupedHistoryRecords } from "@/store/slices/historySlice";
import { useTemplates } from "@/hooks/useTemplates";
import { loadTemplates } from "@/store/slices/templateSlice";
import { getTemplateDisplayName } from "@/utils/templateI18nUtils";
import { getModelDisplayName, isModelPremium } from "@/utils/modelUtils";
import { LoginPrompt } from "@/components/LoginPrompt";
import { useAuthModal } from "@/components/AuthProvider";
import VipUpgradeModal from "@/components/VipUpgradeModal";
import { useAuth } from "@/hooks/useAuth";
import LottieView from "lottie-react-native";
import PermissionGuideModal from "@/components/PermissionGuideModal";
import { usePermissions } from "@/hooks/usePermissions";

// 导入自定义组件
import VoiceWave from "../../components/input/VoiceWave";
import AnimatedVoiceWave from "../../components/input/AnimatedVoiceWave";
import FeatureTags from "../../components/input/FeatureTags";
import TemplateCard, {
  Template as TemplateCardProps,
} from "../../components/input/TemplateCard";
import TemplateDropdownMenu from "../../components/input/TemplateDropdownMenu";

// 扩展Template接口以确保包含icon字段
type ExtendedTemplate = TemplateCardProps & {
  icon?: string;
};
import * as storageService from "@/services/storageService";
import {
  createTranscriptionTask,
  cancelCurrentTranscriptionTask,
} from "@/services/expoSpeechService";
import * as ExpoSpeechService from "@/services/expoSpeechService";

// 首先导入auth相关的Redux状态
import { useSelector } from "react-redux";

// 导入极速模式相关的Redux状态
import { toggleFastMode } from "@/store/slices/fastModeSlice";

// 导入分享服务
import { ShareService } from "@/services/shareService";
import { modelService } from "@/services/supabaseService";
import {
  checkUserDailyWhisperLimit,
  getDailyWhisperLimit,
} from "@/services/dailyWhisperUsageService";
import {
  log,
  warn,
  debug,
  error as logError,
  info,
} from "@/services/logService";
import { generateId } from "@/utils/helpers";
import * as HistoryService from "@/services/storageService";
import LoginGuideModal from "@/components/LoginGuideModal";
import { userService, getCurrentUser } from "@/services/supabaseService";
import AsyncStorage from "@react-native-async-storage/async-storage";

/**
 * 安全地调用触觉反馈，在web平台上会自动跳过
 */
const safeHaptics = {
  impactAsync: async (style: Haptics.ImpactFeedbackStyle) => {
    if (Platform.OS !== "web") {
      try {
        await Haptics.impactAsync(style);
      } catch (error) {
        log("触觉反馈失败，但不影响功能", error);
      }
    }
  },
  notificationAsync: async (type: Haptics.NotificationFeedbackType) => {
    if (Platform.OS !== "web") {
      try {
        await Haptics.notificationAsync(type);
      } catch (error) {
        log("触觉反馈失败，但不影响功能", error);
      }
    }
  },
};

// 创建圆形图标组件，使用Lottie动画
const EmptyStateIcon = () => {
  // 获取屏幕宽度
  const screenWidth = Dimensions.get("window").width;

  // 计算动画尺寸：
  // - 在iPhone SE (320dp宽度) 上保持150的尺寸
  // - 随着屏幕变大，尺寸按比例增加，但不超过屏幕宽度的50%
  const baseSize = 150;
  const scaleFactor = screenWidth / 320;
  const size = Math.min(baseSize * scaleFactor, screenWidth * 0.5);

  return (
    <RNView style={emptyIconStyles.container}>
      <RNView
        style={[
          emptyIconStyles.imageWrapper,
          { width: size, height: size, borderRadius: size / 2 },
        ]}
      >
        <LottieView
          source={require("@/assets/animations/initial.json")}
          style={emptyIconStyles.lottieAnimation}
          autoPlay
          loop
        />
      </RNView>
    </RNView>
  );
};

const emptyIconStyles = StyleSheet.create({
  container: {
    marginBottom: 5,
  },
  imageWrapper: {
    // 基础样式，具体尺寸通过props传入
  },
  lottieAnimation: {
    width: "100%",
    height: "100%",
  },
});

/**
 * 知我AI输入法主页面
 * 包含语音输入功能、AI文本优化和风格模板选择
 */
export default function InputScreen() {
  // 获取翻译函数
  const { t, i18n } = useTranslation();
  // 获取安全区域尺寸
  const insets = useSafeAreaInsets();
  const screenHeight = Dimensions.get("window").height;
  const theme = useAppTheme();
  const isDark = theme === "dark";
  const navigation = useNavigation();

  // 计算底部标签栏高度 (估计值，可能需要调整)
  const tabBarHeight = 80;

  // 状态管理
  const [inputState, setInputState] = useState<
    "initial" | "recording" | "transcribing" | "template" | "result" | "error"
  >("initial");
  const [recognizedText, setRecognizedText] = useState("");
  const [optimizedText, setOptimizedText] = useState("");
  const [recordingTime, setRecordingTime] = useState(0);
  const [lastRecordingTime, setLastRecordingTime] = useState(0); // 保存最后一次录音的时长
  const [currentHistoryRecordId, setCurrentHistoryRecordId] = useState<string | null>(null); // 跟踪当前历史记录ID
  const [selectedTemplate, setSelectedTemplate] =
    useState<TemplateCardProps | null>(null);
  // 添加标识AI处理状态的变量
  const [isOptimizing, setIsOptimizing] = useState(false);
  // 添加任务标志位，用于跟踪当前转录任务是否有效
  const [currentTranscriptionTaskId, setCurrentTranscriptionTaskId] = useState<
    string | null
  >(null);
  // 添加当前使用的AI模型信息
  const [currentModel, setCurrentModel] = useState<{
    name: string;
    isPremium: boolean;
  }>({
    name: "Qwen2.5-7B",
    isPremium: false,
  });
  // 添加语音转写模型信息
  const [transcriptionModel, setTranscriptionModel] = useState<{
    name: string;
    isPremium: boolean;
  }>({
    name: "whisper",
    isPremium: false,
  });
  // 添加用户VIP状态
  const [isVip, setIsVip] = useState(false);
  // 添加transcribing状态的按钮控制
  const [showTranscribingHint, setShowTranscribingHint] = useState(false);
  // 从Redux获取用户状态
  const {
    user,
    isAuthenticated,
    isVIP: reduxIsVIP,
  } = useSelector((state: RootState) => state.auth);

  // 获取极速模式状态
  const { isFastModeOn } = useSelector((state: RootState) => state.fastMode);

  // 获取风格模板优化状态
  const { isStyleOptimizationOn } = useSelector(
    (state: RootState) => state.styleOptimization
  );

  // 获取AuthModal相关方法
  const { showLoginModal } = useAuthModal();

  // 添加VIP升级待处理状态
  const [pendingVipUpgrade, setPendingVipUpgrade] = useState(false);

  // 添加登录提示相关状态
  const [loginPromptVisible, setLoginPromptVisible] = useState(false);
  const [loginPromptTitle, setLoginPromptTitle] = useState("");
  const [loginPromptMessage, setLoginPromptMessage] = useState("");

  // VIP升级Modal状态
  // VIP升级场景枚举
  enum VipUpgradeScene {
    TEMPLATE_USAGE = "template_usage", // 使用VIP模板
    TEMPLATE_CREATION = "template_creation", // 创建模板超出上限
  }

  const [vipUpgradeModalVisible, setVipUpgradeModalVisible] = useState(false);
  const [vipUpgradeScene, setVipUpgradeScene] =
    useState<VipUpgradeScene | null>(null);
  const [selectedVipTemplate, setSelectedVipTemplate] =
    useState<TemplateCardProps | null>(null);

  // 获取auth钩子用于登录检查
  const { checkIsLoggedIn } = useAuth();

  // 使用权限Hook
  const {
    permissionStatus,
    isCheckingPermissions,
    showPermissionGuide,
    permissionGuideType,
    ensurePermissionsAsync,
    hidePermissionModal,
    onPermissionGranted,
  } = usePermissions();

  // 脉动动画
  const pulseAnim = React.useRef(new RNAnimated.Value(1)).current;

  // 获取当前路由名称
  const currentRoute = useNavigationState(
    (state) => state?.routes?.[state.index]?.name
  );

  // 获取路由参数
  const params = useLocalSearchParams();

  // 使用语音录制钩子
  const {
    recordingStatus,
    recognizedText: speechRecognizedText,
    optimizedText: speechOptimizedText,
    audioLevel,
    audioUri,
    processingAI,
    hasActiveOptimization,
    startRecording: startSpeechRecording,
    pauseRecording: pauseSpeechRecording,
    resumeRecording: resumeSpeechRecording,
    stopRecording: stopSpeechRecording,
    optimizeText: optimizeSpeechText,
    resetRecording: resetSpeechRecording,
    updateRecognizedText: updateRecognizedTextThunk,
    getLatestOptimizedText,
    cancelOptimization,
  } = useSpeechRecording();

  // 添加一个状态变量，用于控制是否应该从Redux更新文本
  const [shouldSyncFromRedux, setShouldSyncFromRedux] = useState(true);

  // 网络错误状态 - 从Redux获取
  const {
    networkError,
    showNetworkError,
    whisperLimitError,
    showWhisperLimitModal,
  } = useSelector((state: RootState) => state.recording);
  const [isRetrying, setIsRetrying] = useState(false);

  // 添加实时whisper限制状态
  const [realtimeWhisperLimitStatus, setRealtimeWhisperLimitStatus] = useState<{
    canUse: boolean;
    isVip: boolean;
    currentUsage: number;
    dailyLimit: number;
    remaining: number;
    message: string;
  } | null>(null);

  // 添加每日限制配置状态
  const [dailyWhisperLimit, setDailyWhisperLimit] = useState<number>(5);

  // 增加防抖锁，防止多次切换到template
  const hasEnteredTemplateRef = useRef(false);

  // 添加极速模式执行锁，防止重复执行
  const fastModeExecutingRef = useRef(false);

  // 添加API请求保护锁
  const apiRequestLockRef = useRef(false);

  // 添加状态跳转处理锁，防止重复触发
  const stateTransitionLockRef = useRef(false);

  // 添加极速模式专用锁，确保只执行一次
  const fastModeProcessedRef = useRef(false);

  // 添加上次处理的状态快照，用于去重
  const lastProcessedStateRef = useRef<{
    recordingStatus: string;
    speechRecognizedText: string;
    inputState: string;
    currentTranscriptionTaskId: string | null;
  } | null>(null);

  // 添加组件初始化状态追踪
  const componentInitializedRef = useRef(false);

  // 添加状态处理频率限制
  const lastStateProcessTime = useRef(0);

  // 添加第一次录音标记
  const isFirstRecordingRef = useRef(true);

  // 添加模型信息缓存
  const modelInfoCacheRef = useRef<{
    modelId: string;
    modelName: string;
    isPremium: boolean;
    timestamp: number;
  } | null>(null);

  // 缓存有效期（5分钟）
  const MODEL_CACHE_DURATION = 5 * 60 * 1000;

  // 录音会话标识，用于过滤过期异步回调
  const sessionIdRef = useRef(0);

  // whisper限制本地缓存及刷新机制
  const whisperLimitCacheRef = useRef<{
    status: {
      canUse: boolean;
      isVip: boolean;
      currentUsage: number;
      dailyLimit: number;
      remaining: number;
      message: string;
    } | null;
    lastUpdate: number;
  } | null>(null);
  const WHISPER_LIMIT_CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存，减少不必要的调用

  // 智能检查whisper限制的函数 - 只在必要时调用
  const checkWhisperLimitIfNeeded = useCallback(async (reason: string = "按需检查") => {
    // 检查缓存是否有效
    const now = Date.now();
    if (whisperLimitCacheRef.current &&
        (now - whisperLimitCacheRef.current.lastUpdate) < WHISPER_LIMIT_CACHE_DURATION) {
      log(`[WhisperUsage] ${reason} - 使用缓存数据，跳过检查`);
      return whisperLimitCacheRef.current.status;
    }

    if (user?.id) {
      try {
        log(`[WhisperUsage] ${reason} - 执行检查:`, user.id);

        const [result, limitConfig] = await Promise.all([
          checkUserDailyWhisperLimit(user.id),
          getDailyWhisperLimit(),
        ]);

        whisperLimitCacheRef.current = {
          status: result,
          lastUpdate: now,
        };
        setRealtimeWhisperLimitStatus(result);
        setDailyWhisperLimit(limitConfig);

        log(`[WhisperUsage] ${reason} - 检查完成:`, {
          canUse: result.canUse,
          currentUsage: result.currentUsage,
          dailyLimit: result.dailyLimit,
          isVip: result.isVip
        });

        return result;
      } catch (error) {
        logError(`[WhisperUsage] ${reason} - 检查失败:`, error);
        return null;
      }
    }
    return null;
  }, [user?.id]);

  // 状态引用，避免异步回调引起的 UI 覆盖
  const inputStateRef = useRef(inputState);
  useEffect(() => {
    inputStateRef.current = inputState;
  }, [inputState]);

  // 监听processingAI状态变化并同步到isOptimizing
  useEffect(() => {
    setIsOptimizing(processingAI);
  }, [processingAI]);

  // 组件初始化效果
  useEffect(() => {
    // 确保组件完全初始化
    const initializeComponent = () => {
      componentInitializedRef.current = true;

      // 重置所有ref状态
      fastModeExecutingRef.current = false;
      apiRequestLockRef.current = false;
      stateTransitionLockRef.current = false;
      fastModeProcessedRef.current = false;
      lastProcessedStateRef.current = null;

      log("[组件初始化] InputScreen组件初始化完成");
    };

    // 延迟初始化，确保所有状态都已设置
    const initTimer = setTimeout(initializeComponent, 100);

    return () => {
      clearTimeout(initTimer);
      componentInitializedRef.current = false;
    };
  }, []);

  // 重置录音会话状态的函数
  const resetRecordingSession = useCallback(() => {
    log("[录音会话] 重置录音会话状态");

    // 重置处理锁和标记
    fastModeExecutingRef.current = false;
    apiRequestLockRef.current = false;
    stateTransitionLockRef.current = false;
    fastModeProcessedRef.current = false;
    lastProcessedStateRef.current = null;
    lastStateProcessTime.current = 0;
    isFirstRecordingRef.current = true;

    log("[录音会话] 录音会话状态重置完成");
  }, []);

  // 添加一个调试函数来帮助追踪文本变化
  const logTextDebug = (source: string, text: string) => {
    log(
      `文本更新[${source}]:`,
      text
        ? text.length > 30
          ? text.substring(0, 30) + "..."
          : text
        : "(无文本)",
      `长度: ${text?.length || 0}`
    );
  };

  // 添加dispatch以便触发历史记录加载 - 修复类型
  const dispatch = useDispatch<AppDispatch>();

  // 防抖的模板刷新函数
  const debouncedTemplateRefresh = useRef<NodeJS.Timeout | null>(null);
  const lastOnboardingCheck = useRef<number>(0);

  const refreshTemplatesWithDebounce = useCallback(() => {
    if (debouncedTemplateRefresh.current) {
      clearTimeout(debouncedTemplateRefresh.current);
    }

    debouncedTemplateRefresh.current = setTimeout(() => {
      dispatch(loadTemplates());
    }, 100); // 100ms防抖
  }, [dispatch]);

  // 使用页面焦点来刷新模板列表（当从新建模板页面返回时）
  useFocusEffect(
    useCallback(() => {
      // 防抖的模板刷新
      refreshTemplatesWithDebounce();

      // 【优化】预加载模型信息到缓存，提升录音启动速度
      (async () => {
        try {
          log("[HomePage] 页面焦点时预加载模型信息");
          await updateCurrentModelInfoLightweight();
        } catch (error) {
          warn("[HomePage] 预加载模型信息失败:", error);
        }
      })();

      // 优化的onboarding检查 - 只在必要时检查
      const checkOnboardingIfNeeded = async () => {
        try {
          const now = Date.now();
          // 5分钟内只检查一次onboarding状态
          if (now - lastOnboardingCheck.current < 5 * 60 * 1000) {
            return;
          }

          lastOnboardingCheck.current = now;

          const { getDefaultTemplateId, getAllTemplates } = await import(
            "../../services/templateService"
          );
          const defaultId = await getDefaultTemplateId();
          log("[HomePage] 页面焦点时检查默认模板ID:", defaultId);

          // 获取所有模板并查找默认模板
          const allTemplates = await getAllTemplates();
          const defaultTemplate = allTemplates.find((t) => t.id === defaultId);
          if (defaultTemplate) {
            log("[HomePage] 找到默认模板:", defaultTemplate.title);
          } else {
            log("[HomePage] 未找到对应的默认模板，模板ID:", defaultId);
          }

          // 只在没有默认模板时检查onboarding选择
          if (!defaultTemplate) {
            const { getSelectedTemplates } = await import(
              "../../services/onboardingService"
            );
            const selectedTemplates = await getSelectedTemplates();
            if (selectedTemplates.length > 0) {
              log("[HomePage] Onboarding选择的模板:", selectedTemplates);
              const firstSelectedTemplate = allTemplates.find(
                (t) => t.id === selectedTemplates[0]
              );
              if (firstSelectedTemplate) {
                log("[HomePage] 第一个选择的模板:", firstSelectedTemplate.title);
              }
            } else {
              log("[HomePage] 没有找到onboarding选择的模板");
            }
          }
        } catch (error) {
          logError("[HomePage] 检查默认模板失败:", error);
        }
      };

      checkOnboardingIfNeeded();

      // 异步检查系统提示词更新
      (async () => {
        try {
          const { SystemPromptManager } = await import(
            "../../utils/systemPromptManager"
          );
          const updateCheck = await SystemPromptManager.checkForUpdates();

          if (updateCheck.needsUpdate) {
            log("[HomePage] 检测到系统提示词需要更新");
            await SystemPromptManager.autoUpdate();
          }
        } catch (error) {
          warn("[HomePage] 检查系统提示词更新失败:", error);
        }
      })();

      // 异步检查语音提示词更新
      (async () => {
        try {
          const { SpeechPromptManager } = await import(
            "../../utils/speechPromptManager"
          );
          const updateCheck = await SpeechPromptManager.checkForUpdates();

          if (updateCheck.needsUpdate) {
            log("[HomePage] 检测到语音提示词需要更新");
            await SpeechPromptManager.autoUpdate();
          }
        } catch (error) {
          warn("[HomePage] 检查语音提示词更新失败:", error);
        }
      })();
    }, [dispatch])
  );

  // 从Redux获取语音识别状态
  const { error: speechError } = useSelector(
    (state: RootState) => state.recording
  );

  // 跟踪转写错误状态
  const [transcriptionError, setTranscriptionError] = useState<string | null>(
    null
  );

  // 添加重试状态
  const [showRetryButton, setShowRetryButton] = useState(false);
  const [retryAudioUri, setRetryAudioUri] = useState<string | null>(null);

  // 添加进度文本状态，用于显示详细的进度信息
  const [progressText, setProgressText] = useState<string | null>(null);

  // 模板下拉菜单状态
  const [templateDropdownVisible, setTemplateDropdownVisible] = useState(false);
  const [templateButtonPosition, setTemplateButtonPosition] = useState({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });

  // 监听语音识别错误
  useEffect(() => {
    if (speechError) {
      log("检测到语音识别错误:", speechError);

      // 简化错误信息显示
      let displayError = speechError;
      if (speechError.includes("超时") || speechError.includes("timeout")) {
        displayError = t("home.textDisplay.requestTimeout");
      } else if (
        speechError.includes("网络") ||
        speechError.includes("network") ||
        speechError.includes("连接") ||
        speechError.includes("请求失败")
      ) {
        displayError = t("home.textDisplay.networkError");
      }

      setTranscriptionError(displayError);

      // 检查是否为网络相关错误，如果是则显示重试按钮
      const isNetworkError =
        speechError.includes("网络") ||
        speechError.includes("超时") ||
        speechError.includes("timeout") ||
        speechError.includes("network") ||
        speechError.includes("连接") ||
        speechError.includes("请求失败");

      if (isNetworkError && inputState === "transcribing") {
        log("检测到网络错误，显示重试按钮");
        log("当前audioUri:", audioUri);
        setShowRetryButton(true);

        // 保存当前音频URI用于重试，如果当前audioUri为空，尝试获取最新的录音URI
        if (audioUri) {
          setRetryAudioUri(audioUri);
          log("保存音频URI用于重试:", audioUri);
        } else {
          // 尝试从服务中获取当前录音URI
          const currentUri = ExpoSpeechService.getCurrentRecordingUri();
          if (currentUri) {
            setRetryAudioUri(currentUri);
            log("从服务获取音频URI用于重试:", currentUri);
          } else {
            warn("无法获取音频URI，重试功能可能无法使用");
          }
        }
      }

      // 如果当前是转写中状态，更新UI显示错误信息
      if (inputState === "transcribing") {
        setRecognizedText(displayError);
        setProgressText(null); // 清除进度文本，错误状态不应显示进度
      }
    } else {
      // 重置错误状态
      setTranscriptionError(null);
      setShowRetryButton(false);
      setRetryAudioUri(null);
      setProgressText(null); // 重置时也清除进度文本
    }
  }, [speechError, inputState, audioUri]);

  // 使用useTemplates钩子获取所有模板
  const {
    templates: allTemplates,
    systemTemplates,
    userTemplates,
    selectedTemplate: selectedReduxTemplate,
    defaultTemplate,
    refreshTemplates, // 添加强制刷新功能
  } = useTemplates();

  // 将Redux模板转换为UI组件需要的格式
  const templates: TemplateCardProps[] = allTemplates.map((template) => ({
    id: template.id,
    title: template.title,
    description: template.description,
    name_key: template.name_key,
    description_key: template.description_key,
    color: template.color || "#6a5ae1",
    borderColor: template.borderColor || "#6a5ae1",
    backgroundColor: template.backgroundColor || "#f5f3ff",
    isDefault: template.isDefault,
    category: template.category,
    isVipOnly: template.isVipOnly,
    icon: (template as any).icon || (template.isSystem ? "📝" : "👤"),
  }));

  // 优化的模板刷新逻辑 - 避免与HomePage重复调用
  const lastInputScreenRefresh = useRef<number>(0);

  useFocusEffect(
    useCallback(() => {
      const now = Date.now();
      // 如果距离上次刷新不到2秒，跳过刷新（避免与HomePage重复）
      if (now - lastInputScreenRefresh.current < 2000) {
        log("[InputScreen] 跳过重复的模板刷新，距离上次刷新:", now - lastInputScreenRefresh.current, "ms");
        return;
      }

      lastInputScreenRefresh.current = now;
      log("[InputScreen] 页面获得焦点，刷新模板数据...");

      refreshTemplates()
        .then((success) => {
          if (success) {
            log("[InputScreen] 模板数据刷新成功");
          } else {
            warn("[InputScreen] 模板数据刷新失败");
          }
        })
        .catch((error) => {
          warn("[InputScreen] 模板数据刷新异常:", error);
        });
    }, [refreshTemplates])
  );

  // 监听模板数据变化，更新selectedTemplate以确保多语言显示正确
  useEffect(() => {
    if (selectedTemplate && templates.length > 0) {
      // 查找与当前selectedTemplate相同ID的最新模板数据
      const updatedTemplate = templates.find(
        (t) => t.id === selectedTemplate.id
      );
      if (updatedTemplate) {
        // 检查模板数据是否有变化（比较关键字段）
        const hasChanged =
          updatedTemplate.title !== selectedTemplate.title ||
          updatedTemplate.description !== selectedTemplate.description ||
          updatedTemplate.name_key !== selectedTemplate.name_key ||
          updatedTemplate.description_key !== selectedTemplate.description_key;

        if (hasChanged) {
          log(
            "[InputScreen] 检测到模板数据变化，更新selectedTemplate以确保多语言正确显示"
          );
          setSelectedTemplate(updatedTemplate);
        }
      }
    }
  }, [templates, selectedTemplate]);

  // 统一的状态跳转处理逻辑 - 监听录音状态和识别文本变化
  useEffect(() => {
    const stateSnapshot = {
      recordingStatus,
      speechRecognizedText: speechRecognizedText || "",
      inputState,
      currentTranscriptionTaskId,
    };

    log("=== 状态跳转检查 ===", {
      inputState,
      recordingStatus,
      speechRecognizedText: speechRecognizedText?.substring(0, 50) + "...",
      speechRecognizedTextLength: speechRecognizedText?.length || 0,
      isFastModeOn,
      currentTranscriptionTaskId,
      transitionLocked: stateTransitionLockRef.current,
      fastModeExecuting: fastModeExecutingRef.current,
      fastModeProcessed: fastModeProcessedRef.current,
      apiRequestLocked: apiRequestLockRef.current,
      componentInitialized: componentInitializedRef.current,
    });

    // 组件初始化检查
    if (!componentInitializedRef.current) {
      log("组件尚未完全初始化，跳过状态处理");
      return;
    }

    // 状态处理频率限制（至少间隔100ms）
    const now = Date.now();
    if (now - lastStateProcessTime.current < 100) {
      log("状态处理频率限制，跳过过于频繁的处理");
      return;
    }
    lastStateProcessTime.current = now;

    // 检查是否已经处理过相同的状态组合
    const lastProcessed = lastProcessedStateRef.current;
    if (
      lastProcessed &&
      lastProcessed.recordingStatus === stateSnapshot.recordingStatus &&
      lastProcessed.speechRecognizedText ===
        stateSnapshot.speechRecognizedText &&
      lastProcessed.inputState === stateSnapshot.inputState &&
      lastProcessed.currentTranscriptionTaskId ===
        stateSnapshot.currentTranscriptionTaskId
    ) {
      log("状态未变化，跳过重复处理");
      return;
    }

    // 如果状态跳转锁定中，避免重复处理
    if (stateTransitionLockRef.current) {
      log("状态跳转锁定中，跳过处理");
      return;
    }

    // 更新最后处理的状态快照
    lastProcessedStateRef.current = stateSnapshot;

    // 映射recordingStatus到inputState，但不覆盖transcribing状态
    if (
      (recordingStatus === "recording" || recordingStatus === "paused") &&
      inputState !== "transcribing"
    ) {
      setInputState("recording");
    }

    // 检查语音识别超时信号
    if (speechRecognizedText === "__recognition_timeout__") {
      log("检测到语音识别超时信号");

      // 避免显示特殊标记文本
      setRecognizedText("");

      // 显示超时提示Toast
      Toast.show({
        type: "error",
        text1: t("home.recordingTimeout"),
        text2: t("home.recordingTimeoutMsg"),
        position: "top",
        visibilityTime: 4000,
      });

      // 自动停止录音
      setInputState("initial");
      setRecognizedText("");
      setOptimizedText("");
      setSelectedTemplate(null);
      setRecordingTime(0);
      resetSpeechRecording();

      return; // 避免后续处理
    }

    // 只处理有效的转写结果
    const isStatusUpdate =
      /^正在录音中\.{3}\s*\d+秒$/.test(speechRecognizedText || "") ||
      /^正在聆听中\.{3}\s*已录制\d+秒$/.test(speechRecognizedText || "");

    const isErrorMessage =
      (speechRecognizedText || "").includes("语音转写失败") ||
      (speechRecognizedText || "").includes("没有检测到语音内容") ||
      (speechRecognizedText || "").includes("超时") ||
      (speechRecognizedText || "").includes("网络");

    const isActualTranscription =
      speechRecognizedText &&
      speechRecognizedText.trim() !== "" &&
      !isStatusUpdate &&
      !isErrorMessage &&
      speechRecognizedText !== "__recognition_timeout__";

    log("文本分析结果:", {
      isActualTranscription,
      isStatusUpdate,
      isErrorMessage,
    });

    // 处理录音停止后的状态跳转
    if (recordingStatus === "stopped" && isActualTranscription) {
      log("检测到录音停止且有有效转写文本，开始状态跳转处理");

      // 检查当前转录任务是否已被取消
      if (currentTranscriptionTaskId && inputState === "initial") {
        log("转录任务已被取消，忽略API返回结果");
        return;
      }

      // 防止重复进入template状态
      if (inputState === "template" || inputState === "result") {
        log("已在目标状态，跳过处理");
        return;
      }

      // 设置状态跳转锁
      stateTransitionLockRef.current = true;

      // 更新UI文本
      if (speechRecognizedText !== recognizedText) {
        log("更新UI显示文本");
        setRecognizedText(speechRecognizedText);
      }

      // 自动选择默认模板
      const defaultTemplate = templates.find((t) => t.isDefault);
      if (
        defaultTemplate &&
        (!selectedTemplate || selectedTemplate.id !== defaultTemplate.id)
      ) {
        log("设置默认模板:", defaultTemplate.title);
        setSelectedTemplate(defaultTemplate);
      }

      // 检查风格模板优化开关
      if (!isStyleOptimizationOn) {
        log("风格模板优化已关闭，直接显示转录内容");

        // 直接跳转到结果页面，显示转录内容而不进行AI优化
        setInputState("result");
        setShouldSyncFromRedux(false);
        setOptimizedText(speechRecognizedText); // 直接显示转录文本

        // 保存到历史记录
        const defaultTemplate = templates.find((t) => t.isDefault);
        if (defaultTemplate) {
          // 保存到历史记录前，强制获取最新的audioUri
          let finalAudioUri = audioUri;
          if (!finalAudioUri) {
            // 尝试从服务获取
            finalAudioUri =
              ExpoSpeechService.getCurrentRecordingUri?.() || null;
          }
          if (!finalAudioUri) {
            // 兜底提示
            logError(
              "直接转录保存历史时未能获取到有效的音频文件，历史记录将无法播放"
            );
            Toast.show({
              type: "error",
              text1: t("home.recordingError"),
              text2: t("home.audioFileNotFound") || "录音文件生成失败，请重试",
              position: "top",
              visibilityTime: 3000,
            });
          } else {
            const historyRecord = {
              id: generateId(),
              timestamp: Date.now(),
              originalText: speechRecognizedText,
              optimizedText: speechRecognizedText, // 直接转录，原文即为结果
              templateId: String(defaultTemplate.id), // 确保 templateId 是字符串类型
              templateName: "direct_transcription", // 直接保存语义ID，便于多语言显示
              audioUri: finalAudioUri, // 确保类型是 string | undefined
              optimizedResults: [
                {
                  text: speechRecognizedText,
                  timestamp: Date.now(),
                  templateId: String(defaultTemplate.id), // 确保 templateId 是字符串类型
                  templateName: "direct_transcription", // 直接保存语义ID，便于多语言显示
                },
              ],
            };

            // 使用Promise处理异步操作
            HistoryService.saveHistoryRecord(historyRecord)
              .then(() => {
                log("已保存直接转录历史记录");
                // 发送历史记录新增事件
                DeviceEventEmitter.emit("HISTORY_RECORD_ADDED");
              })
              .catch((error) => {
                logError("保存直接转录历史记录失败:", error);
              });
          }
        }

        // 自动复制到剪贴板
        Clipboard.setStringAsync(speechRecognizedText).then(() => {
          showCopyBadge();
        });

        // 触发触觉反馈
        safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        // 释放状态跳转锁
        stateTransitionLockRef.current = false;

        log("已直接显示转录内容，跳过AI优化");
        return;
      }

      // 检查是否开启极速模式
      if (isFastModeOn && defaultTemplate) {
        log("极速模式已开启，全面检查是否需要处理");

        // 全面检查各种状态，确保不重复执行
        const shouldSkip =
          fastModeExecutingRef.current || // 极速模式正在执行
          fastModeProcessedRef.current || // 极速模式已处理过
          apiRequestLockRef.current || // API请求锁定中
          hasActiveOptimization || // 有活跃的优化请求
          isOptimizing; // 正在优化中

        if (shouldSkip) {
          log("极速模式跳过重复处理:", {
            fastModeExecuting: fastModeExecutingRef.current,
            fastModeProcessed: fastModeProcessedRef.current,
            apiRequestLocked: apiRequestLockRef.current,
            hasActiveOptimization,
            currentInputState: inputState,
            isOptimizing,
          });
          stateTransitionLockRef.current = false; // 释放锁
          return;
        }

        log("极速模式开始处理，设置处理标记");

        // 先设置所有锁，防止重复执行
        fastModeExecutingRef.current = true;
        apiRequestLockRef.current = true;
        fastModeProcessedRef.current = true;

        // 直接跳转到结果页面
        setInputState("result");
        setShouldSyncFromRedux(false);
        setIsOptimizing(true);

        // 触发触觉反馈
        safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        // 开始AI优化
        setTimeout(async () => {
          try {
            // 查找对应的Redux模板以获取完整的prompt
            const reduxTemplate = allTemplates.find(
              (t) => t.id.toString() === defaultTemplate.id.toString()
            );
            const templatePrompt = reduxTemplate ? reduxTemplate.prompt : "";

            log("极速模式开始AI优化:", {
              templateId: defaultTemplate.id,
              templateName: defaultTemplate.title,
              textLength: speechRecognizedText.length,
              hasActiveOptimization: hasActiveOptimization,
              requestId: `极速模式_${Date.now()}`,
              isFirstRecording: isFirstRecordingRef.current,
            });

            // 第一次录音时增加额外的重复执行检查
            if (isFirstRecordingRef.current && hasActiveOptimization) {
              log(
                "[第一次录音] 检测到已有活跃优化请求，等待完成而不是重复执行"
              );
              // 保持加载状态，等待已有请求完成
              return;
            }

            // 使用默认模板进行优化
            const success = await optimizeSpeechText({
              text: speechRecognizedText,
              templateId: defaultTemplate.id.toString(),
              templateName: defaultTemplate.title,
              templatePrompt:
                templatePrompt ||
                `按照${defaultTemplate.title}风格优化文本: ${defaultTemplate.description}`,
              isFromFastMode: true, // 标记来自极速模式
            });

            if (success) {
              log("极速模式优化调用成功，等待结果...");

              // 标记第一次录音已完成
              if (isFirstRecordingRef.current) {
                isFirstRecordingRef.current = false;
                log("[第一次录音] 已完成，标记为false");
              }

              // 使用轮询机制等待优化结果，最多等待10秒
              let attempts = 0;
              const maxAttempts = 20; // 每次500ms，总共10秒
              const pollForResult = async (): Promise<void> => {
                attempts++;
                log(`极速模式轮询优化结果，第${attempts}次尝试`);

                // 获取最新结果
                const latestResult = await getLatestOptimizedText();
                log("[极速模式轮询] 获取最新优化文本结果:", {
                  hasResult: !!latestResult,
                  resultLength: latestResult?.length || 0,
                  resultPreview: latestResult?.substring(0, 50) || "",
                  currentOptimizedTextLength: optimizedText?.length || 0,
                });

                if (latestResult && latestResult.trim()) {
                  const trimmedResult = latestResult.trim();
                  log(
                    "[极速模式轮询] 极速模式获取到优化文本:",
                    trimmedResult.substring(0, 50) + "..."
                  );
                  log("[极速模式轮询] 强制设置UI文本");

                  // 强制设置UI文本
                  setOptimizedText(trimmedResult);

                  // 创建一个引用来持续保护这个文本值
                  const protectedText = trimmedResult;

                  // 多重保护机制：确保文本设置成功
                  const protectionInterval = setInterval(() => {
                    // 检查UI文本是否被意外清空
                    if (
                      !optimizedText ||
                      optimizedText.trim() !== protectedText
                    ) {
                      log(
                        "[文本保护] 检测到UI文本被清空，强制恢复:",
                        protectedText.substring(0, 30) + "..."
                      );
                      setOptimizedText(protectedText);
                    }
                  }, 10); // 每10ms检查一次

                  // 延迟停止保护和结束加载状态
                  setTimeout(() => {
                    log("[极速模式轮询] 延迟确认，停止文本保护");
                    clearInterval(protectionInterval);

                    // 最后一次确认文本设置
                    if (
                      !optimizedText ||
                      optimizedText.trim() !== protectedText
                    ) {
                      log("[极速模式轮询] 最后确认，重新设置文本");
                      setOptimizedText(protectedText);
                    }

                    log("[极速模式轮询] 设置isOptimizing为false");
                    setIsOptimizing(false);
                  }, 200); // 延长保护时间到200ms

                  // 自动复制到剪贴板
                  await Clipboard.setStringAsync(trimmedResult);
                  showCopyBadge();

                  // 刷新历史记录列表
                  dispatch(loadGroupedHistoryRecords());

                  log("极速模式完成，优化文本长度:", trimmedResult.length);
                  return;
                }

                // 如果还没有结果且还有尝试次数，继续轮询
                if (attempts < maxAttempts) {
                  setTimeout(pollForResult, 500);
                } else {
                  logError("极速模式轮询超时，仍未获取到优化文本");
                  setIsOptimizing(false);
                }
              };

              // 开始轮询
              await pollForResult();
            } else {
              log("[极速模式失败处理] 优化调用失败");
              // 对于第一次录音的特殊处理：不立即设置为false，而是延迟等待可能的重试
              if (isFirstRecordingRef.current) {
                log("[第一次录音] 优化失败，延迟500ms后再决定是否关闭加载状态");
                setTimeout(() => {
                  // 再次检查是否有优化结果
                  if (!optimizedText || optimizedText.trim() === "") {
                    log("[第一次录音] 延迟检查后仍无结果，保持加载状态等待");
                    // 保持加载状态，不设置setIsOptimizing(false)
                  }
                }, 500);
              } else {
                // 非第一次录音，正常处理
                setIsOptimizing(false);
              }
              // 保持在result状态
            }
          } catch (optimizeError) {
            logError("极速模式优化失败:", optimizeError);
            setIsOptimizing(false);
            // 不要设置为template，保持在result状态
          } finally {
            // 重置执行锁
            fastModeExecutingRef.current = false;
            apiRequestLockRef.current = false;
            stateTransitionLockRef.current = false;
          }
        }, 100);
      } else if (!isFastModeOn) {
        // 非极速模式，切换到模板选择状态
        log("非极速模式，切换到模板选择状态");
        setInputState("template");
        setShouldSyncFromRedux(false);

        // 触发触觉反馈
        safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        // 释放状态跳转锁
        stateTransitionLockRef.current = false;
      } else {
        log("极速模式已在执行中，跳过重复处理");
        stateTransitionLockRef.current = false; // 释放锁
      }

      return; // 处理完成，退出
    }

    // 处理转写中状态到结果状态的跳转
    if (inputState === "transcribing" && isActualTranscription) {
      log("转写完成，开始状态跳转处理");

      // 检查是否已经有极速模式在处理
      if (fastModeExecutingRef.current || fastModeProcessedRef.current) {
        log("转写完成，极速模式已在处理中，跳过重复处理");
        return;
      }

      // 设置状态跳转锁
      stateTransitionLockRef.current = true;

      // 更新UI文本
      if (speechRecognizedText !== recognizedText) {
        log("更新转写完成后的UI显示文本");
        setRecognizedText(speechRecognizedText);
      }

      // 自动选择默认模板
      const defaultTemplate = templates.find((t) => t.isDefault);
      if (
        defaultTemplate &&
        (!selectedTemplate || selectedTemplate.id !== defaultTemplate.id)
      ) {
        log("转写完成，设置默认模板:", defaultTemplate.title);
        setSelectedTemplate(defaultTemplate);
      }

      // 对于转写完成的情况，不再执行极速模式逻辑，只做状态跳转
      if (isFastModeOn && defaultTemplate) {
        log("转写完成，由于极速模式已由录音停止分支处理，这里只做状态跳转");
        // 直接跳转到结果页面等待极速模式完成
        setInputState("result");
        setShouldSyncFromRedux(false);

        // 触发触觉反馈
        safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else if (!isFastModeOn) {
        // 非极速模式，切换到模板选择状态
        log("转写完成，非极速模式，切换到模板选择状态");
        setInputState("template");
        setShouldSyncFromRedux(false);

        // 触发触觉反馈
        safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        log("转写完成，极速模式已在执行中，跳过重复处理");
      }

      // 释放状态跳转锁
      stateTransitionLockRef.current = false;
      return; // 处理完成，退出
    }

    // 其他原有的文本同步逻辑保持不变（但不处理状态跳转）
    if (speechRecognizedText) {
      const isStatusUpdate =
        /^正在录音中\.{3}\s*\d+秒$/.test(speechRecognizedText) ||
        /^正在聆听中\.{3}\s*已录制\d+秒$/.test(speechRecognizedText);

      const isErrorMessage =
        speechRecognizedText.includes("语音转写失败") ||
        speechRecognizedText.includes("没有检测到语音内容");

      const isActualTranscription =
        !isStatusUpdate &&
        speechRecognizedText.trim() !== "" &&
        !isErrorMessage;

      // 确定是否需要更新UI文本
      let shouldUpdateUI = false;

      if (recordingStatus === "recording" || recordingStatus === "paused") {
        // 录音中：总是显示最新文本
        shouldUpdateUI = true;
      } else if (recordingStatus === "stopped") {
        if (isActualTranscription) {
          shouldUpdateUI = true;
        } else if (isErrorMessage) {
          shouldUpdateUI = true;
        } else if (
          !recognizedText ||
          recognizedText.trim() === "" ||
          /^正在录音中\.{3}\s*\d+秒$/.test(recognizedText) ||
          /^正在聆听中\.{3}\s*已录制\d+秒$/.test(recognizedText)
        ) {
          shouldUpdateUI = true;
        }
      } else if (inputState === "initial") {
        shouldUpdateUI = true;
      } else if (
        (shouldSyncFromRedux ||
          !recognizedText ||
          recognizedText.trim() === "") &&
        inputState !== "template" &&
        inputState !== "result"
      ) {
        shouldUpdateUI = true;
      }

      // 如果需要更新UI文本（但不处理状态跳转）
      if (
        shouldUpdateUI &&
        inputState !== "template" &&
        inputState !== "result"
      ) {
        setRecognizedText(speechRecognizedText);
      }
    }

    // 同步优化后的文本 - 强化UI文本保护机制
    if (speechOptimizedText && speechOptimizedText.trim()) {
      const trimmedSpeechText = speechOptimizedText.trim();
      log(
        "[Redux同步] Redux有优化文本:",
        trimmedSpeechText.substring(0, 50) + "...",
        "长度:",
        trimmedSpeechText.length
      );
      log(
        "[Redux同步] 当前UI优化文本:",
        optimizedText
          ? `长度:${optimizedText.length}, 内容:${optimizedText.substring(
              0,
              30
            )}...`
          : "空"
      );
      log("[Redux同步] 当前输入状态:", inputState);
      log("[Redux同步] 文本比较结果:", {
        reduxText: trimmedSpeechText.substring(0, 30) + "...",
        uiText: optimizedText?.trim()?.substring(0, 30) + "..." || "空",
        相等: trimmedSpeechText === optimizedText?.trim(),
      });

      // 只在Redux文本与UI文本不同时才更新，避免不必要的重置
      if (trimmedSpeechText !== optimizedText?.trim()) {
        log("[Redux同步] Redux文本与UI不同，执行同步到UI");
        setOptimizedText(trimmedSpeechText);
        log(
          "[Redux同步] setOptimizedText调用完成，设置的内容:",
          trimmedSpeechText.substring(0, 50) + "..."
        );

        // 如果当前在结果页面，确保不在加载状态
        if (inputState === "result" && isOptimizing) {
          log("[Redux同步] 结果页面已有优化文本，停止加载状态");
          setIsOptimizing(false);
        }
      } else {
        log("[Redux同步] Redux文本与UI相同，跳过重复设置");
      }
    } else {
      log("[Redux同步] Redux优化文本为空或无效:", speechOptimizedText);

      // 强化结果页面UI文本保护：在结果页面有UI文本时，绝对不允许被重置
      if (inputState === "result") {
        if (optimizedText && optimizedText.trim()) {
          log("[强化UI保护] 结果页面有UI优化文本，绝对保护不被清空");
          log(
            "[强化UI保护] UI文本长度:",
            optimizedText.length,
            "内容:",
            optimizedText.substring(0, 30) + "..."
          );
          log(
            "[强化UI保护] Redux为空但UI有内容，这是正常的时序差异，保持UI显示"
          );
          // 绝对不执行任何重置操作，UI文本必须保持不变
          return;
        } else {
          log("[强化UI保护] 结果页面但UI文本为空，这可能有问题");
        }
      }

      // 其他页面的保护：如果Redux为空但UI有文本，不要清空UI文本
      if (optimizedText && optimizedText.trim()) {
        log(
          "[Redux同步] 其他页面，UI有文本但Redux为空，可能是时序问题，保持UI文本不变"
        );
        log(
          "[Redux同步] UI文本长度:",
          optimizedText.length,
          "内容:",
          optimizedText.substring(0, 20) + "..."
        );
      }
    }

    log("语音状态更新:", {
      recordingStatus,
      inputState,
      hasRecognizedText: !!speechRecognizedText,
      textLength: speechRecognizedText?.length || 0,
      uiTextLength: recognizedText?.length || 0,
      hasOptimizedText: !!speechOptimizedText,
      shouldSyncFromRedux,
    });
  }, [
    // 只监听关键的状态变化，避免循环触发
    recordingStatus,
    speechRecognizedText,
    speechOptimizedText,
    inputState,
    isFastModeOn,
    currentTranscriptionTaskId,
    optimizedText, // 添加optimizedText依赖，确保比较逻辑使用最新值
    // 移除动态依赖数组，因为React不允许依赖数组大小变化
    // 移除 recognizedText, selectedTemplate, templates, shouldSyncFromRedux 等会导致循环的依赖
  ]);

  // 监听状态变化
  useEffect(() => {
    log("InputScreen - 状态变化:", {
      inputState,
      hasRecognizedText: !!recognizedText,
      recognizedTextLength: recognizedText?.length || 0,
      textPreview: recognizedText?.substring(0, 30),
      hasOptimizedText: !!optimizedText,
      optimizedTextLength: optimizedText?.length || 0,
      optimizedTextPreview: optimizedText?.substring(0, 30) || "",
      hasSelectedTemplate: !!selectedTemplate,
      recordingTime,
      isOptimizing,
      hasActiveOptimization,
    });
  }, [
    inputState,
    recognizedText,
    optimizedText,
    selectedTemplate,
    recordingTime,
    isOptimizing,
    hasActiveOptimization,
  ]);

  // 添加语音识别状态检查机制
  useEffect(() => {
    // 只在录音状态下执行检查
    if (inputState !== "recording" || recordingStatus !== "recording") {
      return;
    }

    // 创建定期检查计时器，每15秒检查一次语音识别状态，避免过于频繁的检查
    const statusCheckInterval = setInterval(async () => {
      try {
        // 检查是否需要重新激活录音状态
        if (recordingStatus === "recording" && !processingAI) {
          log("定期检查语音识别状态");

          // 如果当前没有正在显示的文本，可能是语音识别服务停止了
          if (!recognizedText || recognizedText.trim() === "") {
            const timeInRecordingState = recordingTime;

            // 如果已经在录音状态超过20秒但没有任何文本，给用户温和提示
            if (timeInRecordingState > 20) {
              // 使用Toast提示用户，而不是立即重启录音
              Toast.show({
                type: "info",
                text1: "请说话",
                text2: "轻触麦克风可以暂停/继续录音",
                position: "bottom",
                visibilityTime: 3000,
              });

              // 只有在录音状态超过30秒且仍无文本时，才尝试重启
              if (timeInRecordingState > 30) {
                log("检测到录音状态异常：已录音超过30秒但无文本，尝试重启录音");

                // 先暂停再继续，相当于重启录音
                try {
                  await pauseSpeechRecording();
                  // 短暂延迟后继续
                  setTimeout(async () => {
                    if (inputState === "recording") {
                      await resumeSpeechRecording();
                    }
                  }, 800);
                } catch (e) {
                  warn("尝试重启录音失败:", e);
                }
              }
            }
          }
        }
      } catch (error) {
        logError("检查语音识别状态失败:", error);
      }
    }, 15000); // 每15秒检查一次，避免频繁干扰

    return () => {
      clearInterval(statusCheckInterval);
    };
  }, [
    inputState,
    recordingStatus,
    recognizedText,
    recordingTime,
    processingAI,
  ]);

  // 使用ref来跟踪当前是否正在录音
  const isRecordingRef = useRef(false);

  // 更新录音状态
  useEffect(() => {
    isRecordingRef.current = inputState === "recording";
  }, [inputState]);

  // 重置到初始状态的函数 - 增强内容保护
  const resetToInitialState = useCallback(
    async (forceReset: boolean = false) => {
      log("InputScreen - 执行重置函数, 强制重置:", forceReset);

      // 智能重置保护：在结果页面有内容时，除非强制重置否则不清空内容
      if (
        !forceReset &&
        inputState === "result" &&
        optimizedText &&
        optimizedText.trim()
      ) {
        log("[智能重置保护] 结果页面有内容，只重置交互状态，保留内容");
        // 只重置交互相关状态，保留用户的内容
        setInputState("initial");
        setRecognizedText("");
        setSelectedTemplate(null);
        setRecordingTime(0);
        setCurrentTranscriptionTaskId(null);

        // 重置执行锁
        fastModeExecutingRef.current = false;
        apiRequestLockRef.current = false;
        stateTransitionLockRef.current = false;

        log("[智能重置保护] 保护性重置完成，优化文本已保留");
        return;
      }

      try {
        // 停止任何正在进行的动画
        if (pulseAnim) {
          pulseAnim.stopAnimation();
          pulseAnim.setValue(1);
        }

        // 停止当前录音（如果正在录音）
        if (isRecordingRef.current) {
          log("正在停止录音...");
          try {
            await stopSpeechRecording();
          } catch (e) {
            warn("停止录音时出错:", e);
          }
        }

        // 取消当前转写任务（如果存在）
        if (currentTranscriptionTaskId) {
          log("正在取消转写任务...");
          try {
            await cancelCurrentTranscriptionTask();
            setCurrentTranscriptionTaskId(null);
          } catch (e) {
            warn("取消转写任务时出错:", e);
          }
        }

        // 完全重置所有状态
        log("执行完全重置，清空所有内容");
        setInputState("initial");
        setRecognizedText("");
        setOptimizedText("");
        setSelectedTemplate(null);
        setRecordingTime(0);
        setCurrentTranscriptionTaskId(null);
        setCurrentHistoryRecordId(null); // 清除历史记录ID

        // 重置执行锁
        fastModeExecutingRef.current = false;
        apiRequestLockRef.current = false;
        stateTransitionLockRef.current = false;
        // 注意：不重置fastModeProcessedRef，让它在整个录音会话期间保持状态
        // fastModeProcessedRef.current = false; // 这里不重置，避免极速模式重复执行

        // 重置语音识别状态
        resetSpeechRecording();

        // 彻底清除所有错误状态
        dispatch(clearNetworkError());
        setTranscriptionError(null);
        setShowRetryButton(false);
        setProgressText(null);
        setIsOptimizing(false);

        // 重置重试状态
        setIsRetrying(false);

        // 清理音频相关状态
        setRetryAudioUri(null);

        // 启动新的脉冲动画
        setTimeout(() => {
          if (pulseAnim) {
            RNAnimated.loop(
              RNAnimated.sequence([
                RNAnimated.timing(pulseAnim, {
                  toValue: 1.1,
                  duration: 1500,
                  useNativeDriver: true,
                  easing: Easing.bezier(0.4, 0, 0.2, 1),
                }),
                RNAnimated.timing(pulseAnim, {
                  toValue: 1,
                  duration: 1500,
                  useNativeDriver: true,
                  easing: Easing.bezier(0.4, 0, 0.2, 1),
                }),
              ])
            ).start();
          }
          log("InputScreen - 脉冲动画已启动");
        }, 100);

        log("InputScreen - 重置完成");
      } catch (error) {
        logError("重置过程出错:", error);
      }
    },
    [pulseAnim, currentTranscriptionTaskId, cancelCurrentTranscriptionTask]
  );

  // 监听标签页点击事件
  const lastTapRef = React.useRef<number | null>(null);
  const isFocused = useIsFocused();

  // 监听路由变化和标签页点击
  useEffect(() => {
    log("当前路由:", currentRoute, "是否获得焦点:", isFocused);

    // 当路由是输入页时，检查是否是双击
    if (currentRoute === "index" && isFocused) {
      const now = Date.now();
      const DOUBLE_TAP_DELAY = 300; // 300毫秒内点击两次视为双击

      log("输入标签页被点击，上次点击时间:", lastTapRef.current);

      if (lastTapRef.current && now - lastTapRef.current < DOUBLE_TAP_DELAY) {
        log("检测到双击标签页，重置状态");
        resetToInitialState();
        // 重置点击时间，避免连续多次触发
        lastTapRef.current = null;
      } else {
        // 记录第一次点击时间
        lastTapRef.current = now;
        // 设置一个定时器，超过DOUBLE_TAP_DELAY后清除点击时间
        const timer = setTimeout(() => {
          lastTapRef.current = null;
        }, DOUBLE_TAP_DELAY + 100);

        return () => clearTimeout(timer);
      }
    }
  }, [currentRoute, isFocused, resetToInitialState]);

  // 添加全局标签页点击监听器
  useEffect(() => {
    // 监听自定义事件，当标签页被点击时触发
    const handleTabPress = () => {
      log("检测到标签页点击事件");
      // 手动触发一次路由变化检查
      if (currentRoute === "index") {
        const now = Date.now();
        const DOUBLE_TAP_DELAY = 300;

        log("手动触发标签页点击检查，上次点击时间:", lastTapRef.current);

        if (lastTapRef.current && now - lastTapRef.current < DOUBLE_TAP_DELAY) {
          log("手动检测到双击标签页，重置状态");
          resetToInitialState();
          lastTapRef.current = null;
        } else {
          lastTapRef.current = now;
        }
      }
    };

    // 添加全局事件监听器
    const subscription = DeviceEventEmitter.addListener(
      "tabPress",
      handleTabPress
    );

    return () => {
      subscription.remove();
    };
  }, [currentRoute, resetToInitialState]);

  // 将重置函数暴露给全局对象
  useEffect(() => {
    (global as any).resetInputScreen = resetToInitialState;
    return () => {
      delete (global as any).resetInputScreen;
    };
  }, [resetToInitialState]);

  // 监听重置事件
  useEffect(() => {
    const handleResetEvent = async () => {
      log("InputScreen - 收到重置事件");
      await resetToInitialState();
    };

    const resetSubscription = DeviceEventEmitter.addListener(
      "resetInputScreen",
      handleResetEvent
    );

    return () => {
      resetSubscription.remove();
    };
  }, [resetToInitialState]);

  // 监听引导状态重置事件，确保同步重置组件状态
  useEffect(() => {
    const handleOnboardingReset = () => {
      log("InputScreen - 收到引导状态重置事件，检查是否可以重置ref状态");

      // 检查是否有进行中的极速模式处理，如果有则不重置
      if (fastModeExecutingRef.current || apiRequestLockRef.current) {
        log("InputScreen - 检测到进行中的极速模式或API请求，跳过ref状态重置");
        return;
      }

      log("InputScreen - 安全重置所有ref状态");
      // 重置所有ref状态
      fastModeExecutingRef.current = false;
      apiRequestLockRef.current = false;
      stateTransitionLockRef.current = false;
      fastModeProcessedRef.current = false;
      lastProcessedStateRef.current = null;

      log("InputScreen - 引导状态重置后，所有ref状态已重置");
    };

    const onboardingResetSubscription = DeviceEventEmitter.addListener(
      "onboardingReset",
      handleOnboardingReset
    );

    return () => {
      onboardingResetSubscription.remove();
    };
  }, []);

  // 初始化脉动动画
  useEffect(() => {
    if (inputState === "initial") {
      // 按钮呼吸效果
      RNAnimated.loop(
        RNAnimated.sequence([
          RNAnimated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1500,
            useNativeDriver: true,
            easing: Easing.bezier(0.4, 0, 0.2, 1),
          }),
          RNAnimated.timing(pulseAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
            easing: Easing.bezier(0.4, 0, 0.2, 1),
          }),
        ])
      ).start();
    } else {
      pulseAnim.setValue(1);
    }

    return () => {
      pulseAnim.setValue(1);
    };
  }, [inputState, pulseAnim]);

  // 计时器
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (inputState === "recording" && recordingStatus === "recording") {
      interval = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    }
    // 当状态改变为非录音状态或应用退出录音状态时重置计时器
    else if (inputState !== "recording") {
      setRecordingTime(0);
    }
    // 注意：在暂停状态时不设置interval，保留当前计时

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [inputState, recordingStatus]);

  // 格式化时间
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // 通用的更新AI模型信息函数
  const updateCurrentModelInfo = async () => {
    try {
      const modelId = await AIService.getCurrentLanguageModel();
      log("更新模型信息，获取的模型ID:", modelId);

      // 更新VIP状态（始终与Redux保持同步）
      setIsVip(isAuthenticated && reduxIsVIP);

      // 首先尝试从数据库获取模型信息以支持多语言
      let modelName = "";
      let isPremium = false;

      try {
        const { data: textModels } = await modelService.getModelsByType("text");
        if (textModels && textModels.length > 0) {
          const dbModel = textModels.find((m) => m.model_id === modelId);
          if (dbModel) {
            // 使用多语言工具获取显示名称
            const { getModelByIdWithI18n } = await import(
              "@/utils/modelI18nUtils"
            );
            const modelWithI18n = getModelByIdWithI18n(textModels, modelId, t);

            if (modelWithI18n) {
              modelName = modelWithI18n.displayName;
              isPremium = modelWithI18n.is_vip_only;

              log("使用数据库多语言模型信息:", {
                modelId,
                modelName,
                isPremium,
              });
            }
          }
        }
      } catch (dbError) {
        warn("从数据库获取模型信息失败，使用fallback:", dbError);
      }

      // 如果数据库获取失败，使用modelUtils的fallback逻辑
      if (!modelName) {
        modelName = getModelDisplayName(modelId);
        isPremium = isModelPremium(modelId);
        log("使用fallback模型信息:", {
          modelId,
          modelName,
          isPremium,
        });
      }

      // 设置当前模型信息
      setCurrentModel({
        name: modelName,
        isPremium: isPremium,
      });

      return { modelId, modelName, isPremium };
    } catch (modelError) {
      logError("更新AI模型信息失败:", modelError);
      // 使用默认值
      const defaultModel = {
        name: "Qwen2.5-7B",
        isPremium: false,
      };
      setCurrentModel(defaultModel);
      return { modelId: "qwen2.5-7b", ...defaultModel };
    }
  };

  // 新增：轻量级模型信息更新函数，优先使用缓存
  const updateCurrentModelInfoLightweight = async (
    forceRefresh: boolean = false
  ) => {
    try {
      log("开始轻量级模型信息更新, 强制刷新:", forceRefresh);

      // 更新VIP状态（始终与Redux保持同步）
      setIsVip(isAuthenticated && reduxIsVIP);

      // 直接从本地存储获取模型ID，避免数据库连接
      const modelId = await AIService.getCurrentLanguageModel();
      log("从本地存储获取的模型ID:", modelId);

      // 检查缓存是否有效
      const now = Date.now();
      const cache = modelInfoCacheRef.current;

      if (
        !forceRefresh &&
        cache &&
        cache.modelId === modelId &&
        now - cache.timestamp < MODEL_CACHE_DURATION
      ) {
        log("使用缓存的模型信息:", {
          modelId: cache.modelId,
          modelName: cache.modelName,
          isPremium: cache.isPremium,
          cacheAge: Math.round((now - cache.timestamp) / 1000) + "秒",
        });

        // 设置当前模型信息
        setCurrentModel({
          name: cache.modelName,
          isPremium: cache.isPremium,
        });

        return {
          modelId: cache.modelId,
          modelName: cache.modelName,
          isPremium: cache.isPremium,
        };
      }

      // 缓存失效或强制刷新，重新获取模型信息
      log("缓存失效或强制刷新，重新获取模型信息");

      // 使用modelUtils的本地逻辑获取模型信息，避免数据库查询
      const modelName = getModelDisplayName(modelId);
      const isPremium = isModelPremium(modelId);

      log("轻量级更新模型信息:", {
        modelId,
        modelName,
        isPremium,
      });

      // 更新缓存
      modelInfoCacheRef.current = {
        modelId,
        modelName,
        isPremium,
        timestamp: now,
      };

      // 设置当前模型信息
      setCurrentModel({
        name: modelName,
        isPremium: isPremium,
      });

      return { modelId, modelName, isPremium };
    } catch (modelError) {
      logError("轻量级更新AI模型信息失败:", modelError);
      // 使用默认值
      const defaultModel = {
        name: "Qwen2.5-7B",
        isPremium: false,
      };
      setCurrentModel(defaultModel);
      return { modelId: "qwen2.5-7b", ...defaultModel };
    }
  };

  // 开始录音
  const startRecording = async () => {
    log("尝试开始录音，当前状态:", inputState);
    sessionIdRef.current += 1;
    if (inputState !== "initial" && inputState !== "error") {
      log("当前不是初始状态或错误状态，无法开始录音");
      return;
    }
    // 权限检查
    log("[权限检查] 开始检查录音权限");
    const hasPermissions = await ensurePermissionsAsync("both");
    if (!hasPermissions) {
      log("[权限检查] 权限未授予，无法开始录音");
      return;
    }
    log("[权限检查] 权限检查通过，开始录音");

    // 智能检查限制状态（使用缓存避免频繁调用）
    checkWhisperLimitIfNeeded("录音开始前检查");

    try {
      // 开始新的录音会话时，重置所有状态
      log("开始新录音会话，重置所有状态");
      fastModeProcessedRef.current = false;
      dispatch(clearNetworkError());
      setTranscriptionError(null);
      setShowRetryButton(false);
      setProgressText(null);
      setIsOptimizing(false);
      setIsRetrying(false);
      setRetryAudioUri(null);

      // 设置状态为recording
      setInputState("recording");

      // 开始新录音会话，完全清空所有状态
      log("[新录音会话] 完全清空所有状态，开始全新的录音会话");
      setRecognizedText("");
      setOptimizedText("");
      setTranscriptionModel({ name: "", isPremium: false });
      setCurrentHistoryRecordId(null); // 完全清除历史记录ID，开始新的录音会话

      log("[新录音会话] 所有状态已清空，准备开始新的录音");

      // 清除当前转写任务ID
      setCurrentTranscriptionTaskId(null);

      // 重置Redux录音状态
      dispatch(resetRecording());

      // 允许从Redux同步文本
      setShouldSyncFromRedux(true);

      // 【优化】使用轻量级模型信息更新，避免数据库连接延时
      await updateCurrentModelInfoLightweight();

      // 触发触觉反馈
      safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // 使用钩子开始录音
      const success = await startSpeechRecording();

      if (!success) {
        logError("录音启动失败");
        setInputState("initial");
        Toast.show({
          type: "error",
          text1: t("home.recordingError"),
          text2: t("home.recordingErrorMsg"),
          position: "bottom",
          visibilityTime: 3000,
        });
      }
    } catch (error) {
      logError("开始录音失败:", error);
      Toast.show({
        type: "error",
        text1: t("home.recordingError"),
        text2: t("home.recordingErrorMsg"),
        position: "bottom",
        visibilityTime: 3000,
      });
      setInputState("initial");
    }
  };

  // 暂停录音 - 优化Native模式下的处理
  const pauseRecording = async () => {
    try {
      log("暂停录音，当前UI文本:", recognizedText);

      // 【优化】检查Native模式下是否有识别文字
      const userSettings = await storageService.getUserSettings();
      const currentTranscribeModel = userSettings.transcribeModel || "native";
      const isNativeMode = currentTranscribeModel === "native";

      if (
        isNativeMode &&
        (!recognizedText ||
          recognizedText.trim() === "" ||
          recognizedText.includes("正在录音") ||
          recognizedText.includes("正在聆听"))
      ) {
        log("Native模式下无有效识别文字，直接返回初始状态");

        // 停止录音
        await stopSpeechRecording();

        // 重置到初始状态
        await resetToInitialState();

        // 触发触觉反馈
        safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

        return;
      }

      // 触发触觉反馈
      safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // 使用钩子暂停录音 - 语音服务会保存当前段落的文本
      await pauseSpeechRecording();
    } catch (error) {
      logError("暂停录音失败:", error);
    }
  };

  // 继续录音
  const resumeRecording = async () => {
    try {
      log("继续录音，当前UI文本:", recognizedText);

      // 触发触觉反馈
      safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // 使用钩子继续录音 - 语音服务内部会处理文本的合并
      await resumeSpeechRecording();

      // 确保录音状态正确设置为recording
      if (inputState !== "recording") {
        setInputState("recording");
      }

      // 短暂延迟后检查文本同步情况
      setTimeout(() => {
        if (inputState === "recording" && !recognizedText) {
          log("恢复录音后无文本，确保显示正在聆听状态");
          // 强制刷新状态以显示"正在聆听..."
          setRecognizedText("");
        }
      }, 300);
    } catch (error) {
      logError("继续录音失败:", error);
    }
  };

  // 完成录音，进入模板选择状态 - 优化Native模式下的处理
  const finishRecording = async () => {
    try {
      log(
        "完成录音，UI文本长度:",
        recognizedText?.length || 0,
        "当前状态:",
        inputState
      );

      // 【优化】检查Native模式下是否有识别文字
      const userSettings = await storageService.getUserSettings();
      const currentTranscribeModel = userSettings.transcribeModel || "native";
      const isNativeMode = currentTranscribeModel === "native";

      if (
        isNativeMode &&
        (!recognizedText ||
          recognizedText.trim() === "" ||
          recognizedText.includes("正在录音") ||
          recognizedText.includes("正在聆听"))
      ) {
        log("Native模式下无有效识别文字，直接返回初始状态");

        // 停止录音
        await stopSpeechRecording();

        // 重置到初始状态
        await resetToInitialState();

        // 触发触觉反馈表示操作完成
        safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        return;
      }

      // 强制将UI中显示的完整文本同步回Redux，确保包含所有段落
      if (recognizedText && recognizedText.trim()) {
        log("完成录音，同步UI文本回Redux，长度:", recognizedText.length);
        // 首先更新Redux状态
        await updateRecognizedTextThunk(recognizedText);
        // 等待状态同步
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      // 获取当前使用的语音识别模型
      log("当前使用的语音识别模型:", currentTranscribeModel);

      // 设置转写模型信息
      setTranscriptionModel({
        name: currentTranscribeModel,
        isPremium: false,
      });

      // 判断是否为native模式
      if (!isNativeMode) {
        // 非native模式（如whisper）需要进入转录状态
        const taskId = `task_${Date.now()}_${Math.random()
          .toString(36)
          .substring(2, 9)}`;
        createTranscriptionTask(taskId);
        setCurrentTranscriptionTaskId(taskId);
        log(`创建新的转录任务: ${taskId}`);

        // 保存当前的录音时长
        setLastRecordingTime(recordingTime);
        // 切换到转写中状态
        setInputState("transcribing");
        // 设置提示文本
        setRecognizedText(
          t("home.textDisplay.transcribingWithModel", {
            model: currentTranscribeModel,
          })
        );
        log(`切换到${currentTranscribeModel}转写中状态`);

        // 转录前智能检查限制状态（关键时刻）
        checkWhisperLimitIfNeeded("转录前检查");

        // 提前保存音频URI用于可能的重试
        if (audioUri) {
          setRetryAudioUri(audioUri);
          log("提前保存音频URI用于重试:", audioUri);
        }
      }

      // 停止录音并获取结果
      log("停止录音处理开始...");

      // 清除之前的网络错误状态
      dispatch(clearNetworkError());

      const success = await stopSpeechRecording(recognizedText, false);
      log("停止录音处理完成，返回状态:", success);

      // 检查是否返回了错误状态，切换到error状态
      if (success === "error" && !isNativeMode) {
        log("检测到停止录音错误，切换到error状态");
        dispatch(setNetworkError("网络连接错误，语音转写失败"));
        setInputState("error"); // 使用新的error状态
        return;
      }

      // 停止录音后，再次尝试保存音频URI（以防之前没有成功）
      if (!isNativeMode && audioUri && !retryAudioUri) {
        setRetryAudioUri(audioUri);
        log("停止录音后补充保存音频URI:", audioUri);
      }

      // 录音结束后异步刷新限制状态
      (async () => {
        if (user?.id) {
          try {
            const [result, limitConfig] = await Promise.all([
              checkUserDailyWhisperLimit(user.id),
              getDailyWhisperLimit(),
            ]);
            whisperLimitCacheRef.current = {
              status: result,
              lastUpdate: Date.now(),
            };
            setRealtimeWhisperLimitStatus(result);
            setDailyWhisperLimit(limitConfig);
          } catch (error) {
            logError("[录音结束异步刷新] whisper限制检查失败:", error);
          }
        }
      })();

      // 注意：不再在这里处理状态跳转，所有跳转逻辑统一到useEffect中处理
    } catch (error) {
      logError("完成录音失败:", error);
      // 出错时重置到初始状态
      setInputState("initial");
      setInputState("error"); // 使用新的error状态
      dispatch(setNetworkError("网络连接错误，语音转写失败"));
    }
  };

  // 开始AI优化文本处理
  const startOptimizing = async () => {
    if (!recognizedText || !selectedTemplate) {
      Toast.show({
        type: "info",
        text1: t("common.info"),
        text2: t("home.selectTemplateHint"),
        position: "bottom",
        visibilityTime: 3000,
      });
      return;
    }

    // 查找对应的Redux模板以验证VIP权限
    const reduxTemplate = allTemplates.find(
      (t) => t.id.toString() === selectedTemplate.id.toString()
    );

    // 检查VIP模板的权限
    if (reduxTemplate?.isVipOnly && !reduxIsVIP) {
      // 设置选中的VIP模板信息和场景
      setSelectedVipTemplate({
        id: Number(reduxTemplate.id),
        title: reduxTemplate.title,
        description: reduxTemplate.description,
        color: reduxTemplate.color || "#6a5ae1",
        borderColor: reduxTemplate.borderColor || "#6a5ae1",
        backgroundColor: reduxTemplate.backgroundColor || "#f5f3ff",
        isDefault: reduxTemplate.isDefault,
        category: reduxTemplate.category,
        isVipOnly: reduxTemplate.isVipOnly,
      });
      setVipUpgradeScene(VipUpgradeScene.TEMPLATE_USAGE);
      // 显示VIP升级Modal
      setVipUpgradeModalVisible(true);
      return;
    }

    setInputState("result");
    setShouldSyncFromRedux(false); // 停止从Redux同步
    // 设置优化状态为true，显示加载指示器
    setIsOptimizing(true);

    try {
      // 触发触觉反馈
      safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const templatePrompt = reduxTemplate ? reduxTemplate.prompt : "";

      log(
        `[主界面startOptimizing] 模板ID: ${selectedTemplate.id}, 模板名称: ${selectedTemplate.title}`
      );
      log(`[主界面startOptimizing] 找到Redux模板:`, !!reduxTemplate);
      log(
        `[主界面startOptimizing] Redux模板详情:`,
        reduxTemplate
          ? {
              id: reduxTemplate.id,
              title: reduxTemplate.title,
              promptLength: reduxTemplate.prompt.length,
              promptPreview: reduxTemplate.prompt.substring(0, 100),
            }
          : "null"
      );
      log(`[主界面startOptimizing] 最终提示词长度: ${templatePrompt.length}`);

      // 获取当前使用的AI模型信息和VIP状态
      await updateCurrentModelInfo();

      // 使用主方法优化文本 - 使用钩子方法，它内部会处理取消和事务处理
      if (selectedTemplate) {
        log("开始优化文本，使用钩子调用，模板:", selectedTemplate.title);

        // 直接调用Redux的optimizeText来获取记录ID
        const result = await dispatch(
          optimizeText({
            text: recognizedText, // 使用UI中显示的文本
            templateId: selectedTemplate.id.toString(),
            templateName: selectedTemplate.title,
            templatePrompt:
              templatePrompt ||
              `按照${selectedTemplate.title}风格优化文本: ${selectedTemplate.description}`,
          })
        );

        if (optimizeText.fulfilled.match(result)) {
          log("优化调用成功");
          log("首次优化返回的完整payload:", JSON.stringify(result.payload, null, 2));

          // 保存历史记录ID以便后续重新生成时使用
          if (result.payload && result.payload.historyRecordId) {
            setCurrentHistoryRecordId(result.payload.historyRecordId);
            log("首次优化成功，保存当前历史记录ID:", result.payload.historyRecordId);
          } else {
            log("警告：首次优化成功但未获取到历史记录ID", result.payload);
          }

          // 获取最新结果
          const latestResult = await getLatestOptimizedText();
          if (latestResult) {
            log("获取结果成功，长度:", latestResult.length);
            const trimmedResult = latestResult.trim();
            setOptimizedText(trimmedResult);

            // 自动复制到剪贴板
            await Clipboard.setStringAsync(trimmedResult);
            showCopyBadge();

            // 刷新历史记录列表
            dispatch(loadGroupedHistoryRecords());

            // 成功反馈
            safeHaptics.notificationAsync(
              Haptics.NotificationFeedbackType.Success
            );
          } else {
            setOptimizedText("获取优化结果失败，请重试");
            setIsOptimizing(false);
          }
        } else {
          log("优化调用失败");
          // 如果请求被取消，不显示错误
          // 可以根据实际情况判断是否是被用户取消的
          const state = await dispatch((dispatch, getState) => {
            return getState().recording;
          });

          if (!state.error) {
            // 可能是被用户取消的，不显示错误
            setIsOptimizing(false);
          } else {
            Toast.show({
              type: "error",
              text1: t("common.sensitiveWord.filterTitle"),
              text2: t("common.sensitiveWord.filterMessageShort"),
              position: "bottom",
              visibilityTime: 3000,
            });
            setOptimizedText(t("common.sensitiveWord.optimizationFailed"));
            setIsOptimizing(false);
          }
        }
      }
    } catch (error) {
      logError("优化文本流程出错:", error);

      // 判断是否是因请求取消导致的错误
      if (
        error instanceof Error &&
        (error.name === "AbortError" || error.message.includes("abort"))
      ) {
        log("请求被取消");
        setIsOptimizing(false);
      } else {
        // 显示具体的API错误信息
        const errorMessage =
          error instanceof Error ? error.message : t("home.retryFailed");

        Toast.show({
          type: "error",
          text1: "API请求失败",
          text2: errorMessage,
          position: "bottom",
          visibilityTime: 3000,
        });
        setOptimizedText(`请求失败: ${errorMessage}`);
        setIsOptimizing(false);
      }
    }
  };

  // 选择模板
  const selectTemplate = (template: TemplateCardProps) => {
    // 检查是否为VIP模板
    if (template.isVipOnly && !reduxIsVIP) {
      // 设置选中的VIP模板信息和场景
      setSelectedVipTemplate(template);
      setVipUpgradeScene(VipUpgradeScene.TEMPLATE_USAGE);
      // 显示VIP升级Modal
      setVipUpgradeModalVisible(true);
      return;
    }

    setSelectedTemplate(template);
  };

  // 处理模板下拉菜单的显示/隐藏
  const templateButtonRef = useRef<any>(null);

  const handleTemplateButtonPress = () => {
    if (templateButtonRef.current) {
      templateButtonRef.current.measure((x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
        setTemplateButtonPosition({
          x: pageX,
          y: pageY,
          width,
          height,
        });
        setTemplateDropdownVisible(true);
      });
    }
  };

  // 处理从下拉菜单选择模板并重新转录
  const handleTemplateDropdownSelect = async (template: any) => {
    try {
      // 检查是否为VIP模板
      if (template.isVipOnly && !reduxIsVIP) {
        // 设置选中的VIP模板信息和场景
        setSelectedVipTemplate({
          id: template.id,
          title: template.title,
          description: template.description,
          color: template.color || '#6a5ae1',
          borderColor: template.borderColor || '#6a5ae1',
          backgroundColor: template.backgroundColor || '#f5f3ff',
          isDefault: template.isDefault,
          isVipOnly: template.isVipOnly,
          icon: template.icon,
        });
        setVipUpgradeScene(VipUpgradeScene.TEMPLATE_USAGE);
        // 显示VIP升级Modal
        setVipUpgradeModalVisible(true);
        return;
      }

      // 如果有原始文本，使用新模板重新转录
      if (recognizedText) {
        log('使用新模板重新转录:', template.title);
        log('当前选中的模板:', selectedTemplate?.title);
        log('新选择的模板:', template.title);

        // 判断是否为相同模板
        const isSameTemplate = selectedTemplate?.id === template.id;
        log('是否为相同模板:', isSameTemplate);

        // 更新选中的模板
        setSelectedTemplate({
          id: template.id,
          title: template.title,
          description: template.description,
          color: template.color || '#6a5ae1',
          borderColor: template.borderColor || '#6a5ae1',
          backgroundColor: template.backgroundColor || '#f5f3ff',
          isDefault: template.isDefault,
          isVipOnly: template.isVipOnly,
          icon: template.icon,
        });

        // 调用optimizeText进行重新转录
        const result = await dispatch(
          optimizeText({
            text: recognizedText,
            templateId: template.id,
            templatePrompt: template.prompt,
            templateName: template.title,
            isRegenerate: true, // 标记为重新生成
            regenerateForSameAudio: true, // 同一音频的重新生成
            isSameTemplate, // 新增：标记是否为相同模板
          })
        );

        if (optimizeText.fulfilled.match(result)) {
          log('模板切换重新转录成功');

          // 保存历史记录ID以便后续重新生成时使用
          if (result.payload && result.payload.historyRecordId) {
            setCurrentHistoryRecordId(result.payload.historyRecordId);
            log("模板切换后保存历史记录ID:", result.payload.historyRecordId);
          }

          // 获取最新结果
          const latestResult = await getLatestOptimizedText();
          if (latestResult) {
            const trimmedResult = latestResult.trim();
            setOptimizedText(trimmedResult);

            // 自动复制到剪贴板
            await Clipboard.setStringAsync(trimmedResult);
            showCopyBadge();

            // 刷新历史记录列表
            dispatch(loadGroupedHistoryRecords());

            // 触发成功反馈
            safeHaptics.notificationAsync(
              Haptics.NotificationFeedbackType.Success
            );
          }
        } else {
          logError('模板切换重新转录失败:', result.error);
        }
      }
    } catch (error) {
      logError('处理模板下拉菜单选择失败:', error);
    }
  };

  // 导航到模板管理页面
  const navigateToTemplateManager = () => {
    router.push("/template-manager" as any);
  };

  // 处理新建模板点击 - 导航到新建模板页面，添加登录和VIP检查
  const handleCreateNewTemplate = () => {
    // 检查用户是否已登录
    if (!isAuthenticated) {
      // 未登录时显示登录提示
      setLoginPromptTitle(t("templates.createTemplate"));
      setLoginPromptMessage(t("templates.loginToCreateTemplate"));
      setLoginPromptVisible(true);
      return;
    }

    // 检查非VIP用户是否已达到自定义模板上限
    if (!reduxIsVIP && userTemplates.length >= 1) {
      setVipUpgradeScene(VipUpgradeScene.TEMPLATE_CREATION);
      setVipUpgradeModalVisible(true);
      return;
    }

    // 如果检查通过，则导航到新建模板Modal页面
    router.push("/new-template-modal");
  };

  // 修改复制优化后的文字函数
  const copyOptimizedText = async () => {
    try {
      if (!optimizedText) return;

      // 使用expo-clipboard API复制文本
      await Clipboard.setStringAsync(optimizedText);

      // 触发触觉反馈
      safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // 显示Badge
      showCopyBadge();
    } catch (error) {
      logError("复制文本失败:", error);
      showCenteredToast("复制失败，请重试");
    }
  };

  // 分享优化后的文字
  const shareOptimizedText = async () => {
    try {
      if (!optimizedText) return;

      await ShareService.shareToWeChat({
        message: optimizedText,
      });

      // 触发触觉反馈
      safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      logError("分享文本失败:", error);
    }
  };

  // 创建新的输入会话
  const createNewInput = () => {
    log("创建新的输入会话 - 开始强制重置状态");
    // 强制重置，清空所有内容包括优化文本
    resetToInitialState(true);
    // 触发触觉反馈
    safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    log("创建新的输入会话 - 强制重置完成");
  };

  // 在转录状态下创建新的输入会话，并取消当前转录任务
  const createNewInputDuringTranscribing = () => {
    log("在转录状态下创建新的输入会话 - 开始重置状态");
    // 在服务中取消当前转录任务
    cancelCurrentTranscriptionTask();
    // 在组件中设置当前转录任务为无效
    setCurrentTranscriptionTaskId(null);

    log("在转录状态下取消当前转录任务");

    // 重置到初始状态
    setInputState("initial");
    setRecognizedText("");
    setOptimizedText("");
    setSelectedTemplate(null);
    setCurrentHistoryRecordId(null); // 清除历史记录ID
    setRecordingTime(0);
    // 重置语音识别状态
    resetSpeechRecording();
    // 触发触觉反馈
    safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    log("在转录状态下创建新的输入会话 - 状态重置完成");
  };

  // 处理VIP升级点击事件
  const handleUpgradeVIP = () => {
    log("=== VIP升级按钮点击 ===");
    log("当前登录状态:", { isAuthenticated, user: user?.id });
    log("当前pendingVipUpgrade状态:", pendingVipUpgrade);
    log("当前global.pendingVipUpgrade状态:", global.pendingVipUpgrade);

    // 检查是否已登录
    if (!isAuthenticated) {
      log("用户未登录，显示登录提示并设置pendingVipUpgrade=true");
      // 未登录，显示登录提示
      setLoginPromptTitle(t("settings.prompts.upgradeVipTitle"));
      setLoginPromptMessage(t("settings.prompts.upgradeVipMessage"));
      setLoginPromptVisible(true);
      // 设置待处理的VIP升级状态
      setPendingVipUpgrade(true);
      // 设置全局标志，阻止登录模态框自动返回
      global.pendingVipUpgrade = true;

      // 确认标志设置
      log("VIP升级标志设置完成:", {
        localFlag: true, // setPendingVipUpgrade(true)是异步的
        globalFlag: global.pendingVipUpgrade,
      });
    } else {
      log("用户已登录，直接导航到支付页面");
      // 已登录，直接导航到支付页面
      router.push("/payment");
    }

    // 触发触觉反馈
    safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    log("=== VIP升级按钮点击处理完成 ===");
  };

  // 隐藏登录提示
  const hideLoginPrompt = () => {
    setLoginPromptVisible(false);
  };

  // 处理登录成功后的回调
  const handleLoginSuccess = () => {
    log("处理登录回调 - pendingVipUpgrade状态:", pendingVipUpgrade);
    // 关闭登录提示
    hideLoginPrompt();
    // 显示登录模态框
    showLoginModal();
  };

  // 获取VIP升级Modal的标题和描述
  const getVipUpgradeContent = () => {
    switch (vipUpgradeScene) {
      case VipUpgradeScene.TEMPLATE_CREATION:
        return {
          title: t("templates.vipLimitTitle"),
          description: t("templates.vipLimitMessage"),
        };
      case VipUpgradeScene.TEMPLATE_USAGE:
        if (selectedVipTemplate) {
          // 转换为getTemplateDisplayName需要的格式
          const templateForI18n = {
            id: selectedVipTemplate.id.toString(),
            name: selectedVipTemplate.title,
            description: selectedVipTemplate.description || "",
            name_key: (selectedVipTemplate as any).name_key,
            description_key: (selectedVipTemplate as any).description_key,
            prompt_text: "",
            is_system: true,
          };
          const templateName = getTemplateDisplayName(templateForI18n, t);

          return {
            title: t("templates.vipTemplateTitle"),
            description: t("templates.vipTemplateMessage", { templateName }),
          };
        }
        return {
          title: t("templates.vipTemplateTitle"),
          description: t("templates.vipTemplateFallback"),
        };
      default:
        return {
          title: t("templates.vipLimitTitle"),
          description: t("templates.vipLimitMessage"),
        };
    }
  };

  // 处理VIP升级Modal的升级按钮
  const handleVipUpgrade = () => {
    setVipUpgradeModalVisible(false);
    setVipUpgradeScene(null);
    setSelectedVipTemplate(null);

    // 检查是否已登录
    if (!isAuthenticated) {
      // 未登录，显示登录提示并设置VIP升级标志
      setLoginPromptTitle(t("settings.prompts.upgradeVipTitle"));
      setLoginPromptMessage(
        vipUpgradeScene === VipUpgradeScene.TEMPLATE_USAGE
          ? "该风格模板为VIP专享，请先登录并升级到VIP会员"
          : t("settings.prompts.upgradeVipMessage")
      );
      setLoginPromptVisible(true);
      setPendingVipUpgrade(true);
      global.pendingVipUpgrade = true;
    } else {
      // 已登录，直接跳转到支付页面
      router.push("/payment");
    }
  };

  // 记录是否为直接转录后第一次重新生成
  const [showDirectTranscriptionToast, setShowDirectTranscriptionToast] =
    useState(false);

  // 在直接转录后进入优化结果页时，设置标志
  useEffect(() => {
    if (
      inputState === "result" &&
      selectedTemplate &&
      selectedTemplate.isDefault &&
      optimizedText === recognizedText
    ) {
      setShowDirectTranscriptionToast(true);
    }
  }, [inputState, selectedTemplate, optimizedText, recognizedText]);

  // 处理重新生成按钮点击
  const handleRegenerate = async () => {
    // 只在风格模式关闭且直接转录后第一次重新生成时弹toast
    if (!isStyleOptimizationOn && showDirectTranscriptionToast) {
      showCenteredToast(t("home.textDisplay.usingDefaultTemplate"), "info");
      setShowDirectTranscriptionToast(false);
    }

    log("=== 开始重新生成 ===");
    log("重新生成时的状态:", {
      currentHistoryRecordId,
      selectedTemplateId: selectedTemplate?.id,
      recognizedTextLength: recognizedText?.length,
      hasOptimizedText: !!optimizedText
    });

    // 设置加载状态
    setIsOptimizing(true);

    try {
      // 触发触觉反馈
      safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // 检查是否有模板和文本
      if (!recognizedText || !selectedTemplate) {
        Toast.show({
          type: "warning",
          text1: t("common.info"),
          text2: "无法重新生成，缺少原始文本或模板",
          position: "bottom",
          visibilityTime: 3000,
        });
        setIsOptimizing(false);
        return;
      }

      // 直接使用Redux状态，确保与登录状态一致
      setIsVip(isAuthenticated && reduxIsVIP);
      log("重新生成时VIP状态检查:", {
        isAuthenticated,
        reduxIsVIP,
        isVip: isAuthenticated && reduxIsVIP,
      });

      // 获取当前用户设置的语言模型ID
      try {
        const modelId = await AIService.getCurrentLanguageModel();
        log("重新生成时获取的模型ID:", modelId);
        log("重新生成时模型ID类型:", typeof modelId);

        // 检查模型ID格式
        if (modelId) {
          log(
            "重新生成时模型ID是否包含特殊字符:",
            modelId.includes("/"),
            modelId.includes("-"),
            modelId.includes(".")
          );
        }

        // 添加详细日志
        log("重新生成时准备调用getModelDisplayName，传入modelId:", modelId);
        const modelName = getModelDisplayName(modelId);
        log("重新生成时getModelDisplayName返回结果:", modelName);

        log("重新生成时准备调用isModelPremium，传入modelId:", modelId);
        const isPremium = isModelPremium(modelId);
        log("重新生成时isModelPremium返回结果:", isPremium);

        log("重新生成使用AI模型详细信息:", {
          modelId,
          modelIdType: typeof modelId,
          modelName,
          modelNameType: typeof modelName,
          isPremium,
          isPremiumType: typeof isPremium,
        });

        // 更新当前模型信息
        setCurrentModel({
          name: modelName,
          isPremium: isPremium,
        });
      } catch (modelError) {
        logError("重新生成时获取AI模型信息失败:", modelError);
        logError("重新生成时错误详情:", JSON.stringify(modelError, null, 2));
      }

      // 查找对应的Redux模板以获取完整的prompt
      const reduxTemplate = allTemplates.find(
        (t) => t.id.toString() === selectedTemplate.id.toString()
      );
      const templatePrompt = reduxTemplate ? reduxTemplate.prompt : "";

      // 使用Redux的optimizeText直接调用，传递当前历史记录ID
      const result = await dispatch(
        optimizeText({
          text: recognizedText,
          templateId: selectedTemplate.id.toString(),
          templateName: selectedTemplate.title,
          templatePrompt:
            templatePrompt ||
            `按照${selectedTemplate.title}风格优化文本: ${selectedTemplate.description}`,
          isRegenerate: true, // 标记这是重新生成操作
          regenerateForSameAudio: true, // 标记这是同一音频的重新生成
          isSameTemplate: true, // 重新生成按钮总是使用相同模板
          targetRecordId: currentHistoryRecordId || undefined, // 传递要更新的记录ID
        })
      );

      if (optimizeText.fulfilled.match(result)) {
        log("重新生成成功");
        log("重新生成返回的完整payload:", JSON.stringify(result.payload, null, 2));

        // 获取最新结果
        const latestResult = await getLatestOptimizedText();
        if (latestResult) {
          const trimmedResult = latestResult.trim();
          setOptimizedText(trimmedResult);

          // 自动复制到剪贴板
          await Clipboard.setStringAsync(trimmedResult);
          showCopyBadge();

          // 刷新历史记录列表
          dispatch(loadGroupedHistoryRecords());

          // 触发成功反馈
          safeHaptics.notificationAsync(
            Haptics.NotificationFeedbackType.Success
          );
        }

        // 重置加载状态
        setIsOptimizing(false);
      } else {
        log("重新生成失败");
        Toast.show({
          type: "error",
          text1: t("common.sensitiveWord.filterTitle"),
          text2: "无法重新生成结果，请稍后重试",
          position: "bottom",
          visibilityTime: 3000,
        });
        setIsOptimizing(false);
      }
    } catch (error) {
      logError("重新生成失败:", error);
      setIsOptimizing(false);
    }
  };

  // VIP标识闪烁动画
  const vipGlowAnim = React.useRef(new RNAnimated.Value(0.6)).current;

  // 初始化VIP标识闪烁动画
  useEffect(() => {
    if (isVip && inputState === "result") {
      // 创建闪烁效果动画
      RNAnimated.loop(
        RNAnimated.sequence([
          RNAnimated.timing(vipGlowAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
            easing: Easing.bezier(0.4, 0, 0.2, 1),
          }),
          RNAnimated.timing(vipGlowAnim, {
            toValue: 0.6,
            duration: 1500,
            useNativeDriver: true,
            easing: Easing.bezier(0.4, 0, 0.2, 1),
          }),
        ])
      ).start();
    } else {
      // 重置动画
      vipGlowAnim.setValue(0.6);
    }

    return () => {
      vipGlowAnim.stopAnimation();
    };
  }, [isVip, inputState, vipGlowAnim]);

  // 处理whisper使用限制的VIP升级
  const handleWhisperLimitUpgrade = () => {
    // 关闭whisper限制模态框
    dispatch(hideWhisperLimitModal());

    // 检查是否已登录
    if (!isAuthenticated) {
      // 未登录，显示登录模态框
      setLoginPromptVisible(true);
      setLoginPromptTitle(t("auth.loginRequired"));
      setLoginPromptMessage(t("home.whisperLimit.loginPrompt") as string);
      // 设置标志，表示登录后需要导航到支付页面
      global.pendingVipUpgrade = true;
    } else {
      // 已登录，直接导航到支付页面
      router.push("/payment");
    }
  };

  // 关闭whisper限制模态框
  const handleCloseWhisperLimitModal = () => {
    dispatch(hideWhisperLimitModal());
  };

  // 切换到设备本地语音识别模型
  const handleSwitchToNativeModel = async () => {
    try {
      log("[SwitchToNative] 开始切换到设备本地语音识别模型");

      // 1. 清除whisper限制错误状态
      dispatch(clearWhisperLimitError());

      // 2. 立即更新本地转录模型状态
      setTranscriptionModel({
        name: "native",
        isPremium: false,
      });
      log("[SwitchToNative] 已更新本地转录模型状态");

      // 3. 保存到本地存储
      await storageService.saveUserSettings({
        transcribeModel: "native",
      });
      log("[SwitchToNative] 已保存模型设置到本地存储");

      // 4. 如果用户已登录，同时保存到服务器
      if (user?.id) {
        try {
          const { error } = await modelService.saveUserModelSettings(user.id, {
            default_voice_model: "native",
          });

          if (error) {
            logError("[SwitchToNative] 保存模型设置到服务器失败:", error);
            showCenteredToast(
              t("settings.toast.cloudSyncFailed") as string,
              "info"
            );
          } else {
            log("[SwitchToNative] 已保存模型设置到服务器");
          }
        } catch (serverError) {
          logError("[SwitchToNative] 保存到服务器时出错:", serverError);
          showCenteredToast(
            t("settings.toast.cloudSyncFailed") as string,
            "info"
          );
        }
      }

      // 5. 重置录音状态到初始状态
      setInputState("initial");
      setRecognizedText("");
      setOptimizedText("");
      setSelectedTemplate(null);
      setRecordingTime(0);
      setTranscriptionError(null);
      setShowRetryButton(false);
      setProgressText(null);
      setIsOptimizing(false);
      setIsRetrying(false);
      setRetryAudioUri(null);

      // 6. 重置Redux状态
      dispatch(resetRecording());

      // 7. 强制刷新模型信息，确保设置页面能够同步显示
      try {
        await updateCurrentModelInfoLightweight(true);
        log("[SwitchToNative] 已强制刷新模型信息缓存");
      } catch (refreshError) {
        logError("[SwitchToNative] 刷新模型信息失败:", refreshError);
      }

      // 8. 显示多语言成功提示
      showCenteredToast(
        t("home.whisperLimit.switchToNative") as string,
        "success"
      );

      // 9. 触发触觉反馈
      safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      log("[SwitchToNative] 成功切换到设备本地语音识别模型");
    } catch (error) {
      logError("[SwitchToNative] 切换模型失败:", error);
      showCenteredToast(
        t("settings.toast.settingsSaveFailed") as string,
        "error"
      );
    }
  };

  // 监听语言变化，重新更新模型信息和模板数据以确保多语言内容及时更新
  useEffect(() => {
    const handleLanguageChange = () => {
      log("语言已切换，强制刷新模型信息缓存和模板数据");
      // 【优化】使用轻量级更新并强制刷新缓存
      updateCurrentModelInfoLightweight(true);

      // 强制刷新模板数据，确保模板名称能够及时更新为新语言
      refreshTemplates()
        .then((success) => {
          if (success) {
            log("[语言切换] 模板数据刷新成功");
          } else {
            warn("[语言切换] 模板数据刷新失败");
          }
        })
        .catch((error) => {
          warn("[语言切换] 模板数据刷新异常:", error);
        });
    };

    // 添加语言变化监听器
    if (i18n) {
      i18n.on("languageChanged", handleLanguageChange);
    }

    // 清理监听器
    return () => {
      if (i18n) {
        i18n.off("languageChanged", handleLanguageChange);
      }
    };
  }, [i18n, refreshTemplates]);

  // transcribing状态的3秒倒计时逻辑
  useEffect(() => {
    if (inputState === "transcribing") {
      // 重置状态
      setShowTranscribingHint(false);

      // 3秒后显示提示
      const timer = setTimeout(() => {
        if (inputState === "transcribing") {
          setShowTranscribingHint(true);
        }
      }, 5000);

      return () => clearTimeout(timer);
    } else {
      setShowTranscribingHint(false);
    }
  }, [inputState]);

  // 在组件挂载时同步Redux的VIP状态
  useEffect(() => {
    // 同步Redux状态，确保用户未登录时不显示VIP
    setIsVip(isAuthenticated && reduxIsVIP);

    // 添加事件监听器，当从历史页面切换过来时自动开始录音
    const subscription = DeviceEventEmitter.addListener(
      "START_RECORDING",
      () => {
        log("收到开始录音事件");
        // 确保在UI线程上执行
        requestAnimationFrame(() => {
          startRecording().catch((error) => {
            logError("自动开始录音失败:", error);
          });
        });
      }
    );

    // 添加焦点监听器，当页面重新获得焦点时更新VIP状态
    const unsubscribeFocus = navigation.addListener("focus", () => {
      log("页面重新获得焦点，更新VIP状态");
      setIsVip(isAuthenticated && reduxIsVIP);
    });

    // 组件卸载时移除事件监听器
    return () => {
      subscription.remove();
      unsubscribeFocus();
    };
  }, [isAuthenticated, reduxIsVIP, navigation]);

  // 监听VIP状态变化
  useEffect(() => {
    log("VIP状态发生变化:", {
      isAuthenticated,
      reduxIsVIP,
      currentIsVip: isVip,
    });
    setIsVip(isAuthenticated && reduxIsVIP);
  }, [isAuthenticated, reduxIsVIP]);

  // VIP升级监听器：当用户登录后自动导航到支付页面
  useEffect(() => {
    log("VIP升级监听器 - 状态变化:", {
      isAuthenticated,
      pendingVipUpgrade,
      userId: user?.id,
      globalFlag: global.pendingVipUpgrade,
    });

    // 注释掉自动导航逻辑，改为由login页面处理
    // 这样可以确保login页面先关闭，然后再导航到payment页面

    // 如果用户刚刚登录且有待处理的VIP升级
    // if (isAuthenticated && pendingVipUpgrade && user?.id) {
    //   log('用户登录成功，自动导航到支付页面');
    //
    //   // 使用较短延迟，但强制保持modal形式
    //   setTimeout(() => {
    //     log('执行延迟导航到支付页面');
    //
    //     // 只有在成功导航后才重置标志
    //     router.push('/payment');
    //
    //     // 延迟重置标志，确保导航已经完成
    //     setTimeout(() => {
    //       log('导航完成，重置VIP升级标志');
    //       setPendingVipUpgrade(false);
    //       global.pendingVipUpgrade = false;
    //     }, 100);
    //   }, 50);  // 进一步减少延迟到50ms
    // }
  }, [isAuthenticated, pendingVipUpgrade, user]);

  // 添加额外的监听机制，用于处理可能的状态更新延迟
  useEffect(() => {
    log("VIP升级额外检查 - 状态:", {
      pendingVipUpgrade,
      isAuthenticated,
      userId: user?.id,
      globalFlag: global.pendingVipUpgrade,
    });

    // 简化额外检查逻辑，主要用于调试状态变化
    // 实际的VIP升级导航已经移到login页面处理

    // if (pendingVipUpgrade && isAuthenticated && user?.id) {
    //   log('额外检查 - 检测到登录状态和pendingVipUpgrade都为true');
    //
    //   // 设置一个间隔检查，确保捕获到状态变化
    //   const checkInterval = setInterval(() => {
    //     log('间隔检查 - 当前状态:', {
    //       isAuthenticated,
    //       userId: user?.id,
    //       pendingVipUpgrade,
    //       globalFlag: global.pendingVipUpgrade
    //     });
    //
    //     if (isAuthenticated && user?.id && pendingVipUpgrade) {
    //       log('间隔检查 - 触发VIP升级导航');
    //       clearInterval(checkInterval);
    //       router.push('/payment');
    //
    //       // 延迟重置标志
    //       setTimeout(() => {
    //         log('间隔检查导航完成，重置VIP升级标志');
    //         setPendingVipUpgrade(false);
    //         global.pendingVipUpgrade = false;
    //       }, 100);
    //     }
    //   }, 50);  // 减少间隔检查时间到50ms
    //
    //   // 清理间隔，防止内存泄漏
    //   const timeoutId = setTimeout(() => {
    //     clearInterval(checkInterval);
    //     log('额外检查超时，清理间隔检查');
    //   }, 3000);
    //
    //   return () => {
    //     clearInterval(checkInterval);
    //     clearTimeout(timeoutId);
    //   };
    // }
  }, [pendingVipUpgrade, isAuthenticated, user]);

  // 防护机制：监听路由变化，确保VIP升级流程不被干扰
  useEffect(() => {
    log("VIP升级防护机制 - 状态:", {
      globalFlag: global.pendingVipUpgrade,
      isAuthenticated,
      userId: user?.id,
    });

    // 简化防护机制，主要用于调试
    // 实际的VIP升级导航已经移到login页面处理

    // if (global.pendingVipUpgrade && isAuthenticated && user?.id) {
    //   log('路由防护：检测到待处理的VIP升级，确保导航到payment页面');
    //   // 强制导航到payment页面
    //   const timeoutId = setTimeout(() => {
    //     log('路由防护：执行强制导航到payment页面');
    //     router.push('/payment');
    //
    //     // 延迟重置标志
    //     setTimeout(() => {
    //       log('路由防护导航完成，重置VIP升级标志');
    //       global.pendingVipUpgrade = false;
    //       setPendingVipUpgrade(false);
    //     }, 100);
    //   }, 50);  // 减少防护机制延迟到50ms
    //
    //   return () => clearTimeout(timeoutId);
    // }
  }, [isAuthenticated, user]);

  // useEffect中，切换到template后立即return，保证只切换一次
  useEffect(() => {
    // 判断是否为有效的转写结果
    const isStatusUpdate =
      /^正在录音中\.{3}\s*\d+秒$/.test(speechRecognizedText || "") ||
      /^正在聆听中\.{3}\s*已录制\d+秒$/.test(speechRecognizedText || "");
    const isErrorMessage =
      (speechRecognizedText || "").includes("语音转写失败") ||
      (speechRecognizedText || "").includes("没有检测到语音内容");
    const isActualTranscription =
      !isStatusUpdate &&
      !!(speechRecognizedText && speechRecognizedText.trim() !== "") &&
      !isErrorMessage;

    // 检查当前转录任务是否已被取消
    if (
      isActualTranscription &&
      inputState === "initial" &&
      !currentTranscriptionTaskId
    ) {
      log("转录任务已被取消，忽略API返回结果");
      return; // 如果任务已被取消，忽略结果
    }

    // 移除这里的状态跳转逻辑，统一到上面的useEffect中处理
    // if (
    //   isActualTranscription &&
    //   inputState !== "template" &&
    //   !hasEnteredTemplateRef.current
    // ) {
    //   setInputState("template");
    //   setShouldSyncFromRedux(false);
    //   hasEnteredTemplateRef.current = true;
    //   return;
    // }
    // ... existing code ...
  }, [
    recordingStatus,
    speechRecognizedText,
    inputState,
    shouldSyncFromRedux,
    currentTranscriptionTaskId,
  ]);

  // 网络错误重试转写功能
  const handleNetworkErrorRetry = async () => {
    if (isRetrying) {
      log("已在重试中，跳过重复请求");
      return;
    }

    setIsRetrying(true);
    dispatch(clearNetworkError());

    try {
      log("开始网络错误重试转写");

      // 触发触觉反馈
      safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // 更新UI显示重试状态
      setRecognizedText(
        t("home.textDisplay.retryTranscribing", {
          model: transcriptionModel.name,
        })
      );

      // 使用现有的retryTranscription功能
      await retryTranscription();

      // 重试成功后，额外确保所有状态已清理
      log("网络错误重试完成，确保状态已清理");
    } catch (error) {
      logError("网络错误重试失败:", error);

      // 判断错误类型，决定是否继续显示重试选项
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      // 扩展网络错误识别范围，包含更多错误类型
      const isNetworkError =
        errorMessage.includes("网络") ||
        errorMessage.includes("超时") ||
        errorMessage.includes("timeout") ||
        errorMessage.includes("network") ||
        errorMessage.includes("连接") ||
        errorMessage.includes("请求失败") ||
        errorMessage.includes("Failed to fetch") ||
        errorMessage.includes("Network request failed") ||
        errorMessage.includes("No network") ||
        errorMessage.includes("Connection") ||
        errorMessage.includes("ENOTFOUND") ||
        errorMessage.includes("ENETUNREACH") ||
        errorMessage.includes("ECONNREFUSED") ||
        errorMessage.includes("语音转写失败"); // 很多情况下这也是网络问题

      // === 错误归类优化 ===
      // 先检测与音频相关的关键词，但若同时包含网络关键词，则视为网络问题
      const audioKeywords =
        /(音频|audio).*(损坏|corrupted|invalid|格式不受支持)/i;
      const audioNotFound = /(文件不存在|file not found)/i;
      const isAudioError =
        audioKeywords.test(errorMessage) || audioNotFound.test(errorMessage);

      const networkKeywords =
        /(网络|network|连接|timeout|超时|请求失败|Failed to fetch|Network request failed|No network|ENOTFOUND|ENETUNREACH|ECONNREFUSED)/i;
      const isAlsoNetwork = networkKeywords.test(errorMessage);

      if (isAudioError && !isAlsoNetwork) {
        // 只有在"明确是音频且排除网络问题"时提示损坏
        dispatch(setNetworkError("音频文件可能已损坏，建议重新录音"));
      } else {
        // 其余一律按网络问题处理，用户可在网络恢复后继续重试
        dispatch(setNetworkError("重试失败，请检查网络连接后再试"));
      }
    } finally {
      setIsRetrying(false);
    }
  };

  // 网络错误时重新录音
  const handleNetworkErrorRerecord = async () => {
    log("用户选择重新录音");

    // 清除网络错误状态
    dispatch(clearNetworkError());

    // 重置到初始状态
    await resetToInitialState();
  };

  // 重试转写功能
  const retryTranscription = async () => {
    let currentUri: string | null = null;
    try {
      // 重置所有状态
      setInputState("transcribing");
      setTranscriptionError(null);
      setShowRetryButton(false);
      setProgressText(null);
      setIsRetrying(true);
      dispatch(clearNetworkError());

      // 获取可用的音频URI
      currentUri =
        retryAudioUri ||
        (await ExpoSpeechService.getCurrentRecordingUri()) ||
        audioUri;

      if (!currentUri) {
        logError("没有可重试的音频文件，所有URI都为空");
        setInputState("error");
        Toast.show({
          type: "error",
          text1: t("home.retryFailed"),
          text2: t("home.audioFileNotFound"),
          position: "bottom",
          visibilityTime: 3000,
        });
        return;
      }

      log("使用音频URI重试:", currentUri);

      log("开始重试转写，音频URI:", currentUri);

      // 触发触觉反馈
      safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // 获取当前使用的语音识别模型
      const userSettings = await storageService.getUserSettings();
      const currentModel = userSettings.transcribeModel || "native";

      // 使用retryTranscription进行重试
      const thisSession = sessionIdRef.current;
      const success = await ExpoSpeechService.retryTranscription(
        currentUri,
        currentModel,
        {
          onRecognizing: (text: string) => {
            // 只有当前录音会话的回调才更新 UI
            if (
              thisSession === sessionIdRef.current &&
              inputStateRef.current === "transcribing"
            ) {
              setRecognizedText(text);
              setProgressText(null); // 清除进度文本
            }
          },
          onResult: (text: string) => {
            if (thisSession !== sessionIdRef.current) {
              return; // 过期回调，忽略
            }
            log("重试转写成功，文本长度:", text.length);

            // 重试成功后，彻底清理所有错误状态
            dispatch(clearNetworkError());
            setTranscriptionError(null);
            setShowRetryButton(false);
            setProgressText(null);
            setIsRetrying(false);

            // 设置转写结果
            setRecognizedText(text);

            // 自动选择默认模板
            const defaultTemplate = templates.find((t) => t.isDefault);
            if (defaultTemplate) {
              setSelectedTemplate(defaultTemplate);
            }

            // 检查风格模板优化开关
            if (!isStyleOptimizationOn) {
              log("风格模板优化已关闭（重试转写成功），直接显示转录内容");

              // 直接跳转到结果页面，显示转录内容而不进行AI优化
              setInputState("result");
              setShouldSyncFromRedux(false);
              setOptimizedText(text); // 直接显示转录文本

              // 自动复制到剪贴板
              Clipboard.setStringAsync(text).then(() => {
                showCopyBadge();
              });

              // 触发触觉反馈
              safeHaptics.notificationAsync(
                Haptics.NotificationFeedbackType.Success
              );

              log("已直接显示转录内容（重试转写成功），跳过AI优化");
              return;
            }

            // 检查是否开启极速模式
            if (isFastModeOn && defaultTemplate) {
              log("极速模式已开启（重试转写成功），直接使用默认模板进行优化");

              // 直接跳转到结果页面并开始优化
              setInputState("result");
              setShouldSyncFromRedux(false);
              setIsOptimizing(true);

              // 触发触觉反馈
              safeHaptics.notificationAsync(
                Haptics.NotificationFeedbackType.Success
              );

              // 开始AI优化
              setTimeout(async () => {
                try {
                  // 查找对应的Redux模板以获取完整的prompt
                  const reduxTemplate = allTemplates.find(
                    (t) => t.id.toString() === defaultTemplate.id.toString()
                  );
                  const templatePrompt = reduxTemplate
                    ? reduxTemplate.prompt
                    : "";

                  // 使用默认模板进行优化
                  const success = await optimizeSpeechText({
                    text: text,
                    templateId: defaultTemplate.id.toString(),
                    templateName: defaultTemplate.title,
                    templatePrompt:
                      templatePrompt ||
                      `按照${defaultTemplate.title}风格优化文本: ${defaultTemplate.description}`,
                  });

                  if (success) {
                    log("极速模式优化调用成功（重试转写成功）");
                    // 获取最新结果
                    const latestResult = await getLatestOptimizedText();
                    if (latestResult) {
                      const trimmedResult = latestResult.trim();
                      setOptimizedText(trimmedResult);

                      // 自动复制到剪贴板
                      await Clipboard.setStringAsync(trimmedResult);
                      showCopyBadge();

                      // 刷新历史记录列表
                      dispatch(loadGroupedHistoryRecords());

                      // 触发成功反馈
                      safeHaptics.notificationAsync(
                        Haptics.NotificationFeedbackType.Success
                      );
                    }
                  }
                } catch (optimizeError) {
                  logError("极速模式优化失败（重试转写成功）:", optimizeError);
                  setIsOptimizing(false);
                  // 如果优化失败，回退到模板选择状态
                  setInputState("template");
                }
              }, 100);
            } else {
              // 非极速模式，切换到模板选择状态
              log("非极速模式（重试转写成功），切换到模板选择状态");
              setInputState("template");
              setShouldSyncFromRedux(false);

              // 触发触觉反馈
              safeHaptics.notificationAsync(
                Haptics.NotificationFeedbackType.Success
              );
            }
          },
          onError: (error: Error) => {
            if (thisSession !== sessionIdRef.current) {
              return;
            }
            logError("重试转写失败:", error);

            // 仅在仍处于 transcribing 状态时处理错误 UI，
            // 避免新一轮录音后被旧回调覆盖
            if (inputStateRef.current !== "transcribing") {
              return;
            }

            // 简化错误信息显示
            let displayMessage = error.message;
            if (
              error.message.includes("超时") ||
              error.message.includes("timeout")
            ) {
              displayMessage = t("home.textDisplay.requestTimeout");
            } else if (
              error.message.includes("网络") ||
              error.message.includes("network") ||
              error.message.includes("连接") ||
              error.message.includes("请求失败")
            ) {
              displayMessage = t("home.textDisplay.networkError");
            }

            setTranscriptionError(displayMessage);
            setRecognizedText(displayMessage);
            setProgressText(null); // 清除进度文本

            // 如果仍然是网络错误，继续显示重试按钮
            const isNetworkError =
              error.message.includes("网络") ||
              error.message.includes("超时") ||
              error.message.includes("timeout") ||
              error.message.includes("network") ||
              error.message.includes("连接") ||
              error.message.includes("请求失败");

            if (isNetworkError) {
              setShowRetryButton(true);
              // 确保音频URI仍然可用
              if (currentUri) {
                setRetryAudioUri(currentUri);
              }
            } else {
              // 如果不是网络错误，可能是音频文件问题，建议重新录音
              Toast.show({
                type: "error",
                text1: t("home.retryFailed"),
                text2: t("home.networkError.retryFailed"),
                position: "bottom",
                visibilityTime: 3000,
              });
            }
          },
        }
      );

      if (!success) {
        logError("重试转写调用失败");
        setInputState("error");
        setTranscriptionError(t("home.retryTranscriptionFailed"));
        setRecognizedText(t("home.retryTranscriptionFailedMsg"));
        setProgressText(null); // 清除进度文本
        setShowRetryButton(true);
        // 保持音频URI可用
        if (currentUri) {
          setRetryAudioUri(currentUri);
        }
      }
    } catch (error) {
      logError("重试转写过程出错:", error);
      setInputState("error");
      const errorMessage =
        error instanceof Error ? error.message : t("home.retryFailed");
      setTranscriptionError(errorMessage);
      setRecognizedText(`${t("home.retryFailedWithError")}: ${errorMessage}`);
      setProgressText(null); // 清除进度文本
      setShowRetryButton(true);
      // 保持音频URI可用
      if (currentUri) {
        setRetryAudioUri(currentUri);
      }
    }
  };

  // 添加极速模式状态检查函数，处理状态同步延迟问题
  const checkFastModeWithDelay = useCallback(
    async (defaultTemplate: TemplateCardProps | null): Promise<boolean> => {
      // 首次检查
      if (isFastModeOn && defaultTemplate) {
        return true;
      }

      // 如果首次检查失败，等待一小段时间后再检查
      // 这样可以处理状态同步的时序问题
      await new Promise((resolve) => setTimeout(resolve, 50));

      // 重新获取最新状态
      const currentState = await dispatch((dispatch, getState) => {
        return getState().fastMode.isFastModeOn;
      });

      log("延时检查极速模式状态:", {
        firstCheck: isFastModeOn,
        delayedCheck: currentState,
        hasDefaultTemplate: !!defaultTemplate,
      });

      return currentState && defaultTemplate !== null;
    },
    [isFastModeOn, dispatch]
  );

  // 以下代码已在顶部声明并实现，重复部分注释处理，避免重复声明错误
  // 录音会话标识，用于过滤过期的异步回调 (duplicate)
  // const sessionIdRef = useRef(0);

  // 在开始新录音时递增 sessionId (duplicate stub)
  /*
  const startRecording = async () => {
    sessionIdRef.current += 1; // 标记新会话
  };
  */

  // === 兜底检测：transcribing状态下有有效转录文本但未跳转 ===
  useEffect(() => {
    if (
      inputState === "transcribing" &&
      speechRecognizedText &&
      speechRecognizedText.trim() !== "" &&
      !/^正在录音中/.test(speechRecognizedText) &&
      !/^正在聆听中/.test(speechRecognizedText) &&
      !speechRecognizedText.includes("语音转写失败") &&
      !speechRecognizedText.includes("没有检测到语音内容") &&
      !speechRecognizedText.includes("超时") &&
      !speechRecognizedText.includes("网络")
    ) {
      // 兜底：3秒后还未跳转，强制跳转
      const timer = setTimeout(() => {
        if (inputState === "transcribing") {
          log("[兜底] 检测到转录文本但未跳转，强制切换到模板选择页");
          setInputState("template");
          setShouldSyncFromRedux(false);
          stateTransitionLockRef.current = false; // 释放锁
        }
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [inputState, speechRecognizedText]);

  // 智能检查whisper使用限制 - 只在关键时刻调用
  useEffect(() => {
    const checkWhisperLimit = async (reason: string = "初始化") => {
      if (user?.id) {
        try {
          log(`[WhisperUsage] ${reason} - 检查用户使用限制:`, user.id);

          // 并行获取限制状态和每日限制配置
          const [result, limitConfig] = await Promise.all([
            checkUserDailyWhisperLimit(user.id),
            getDailyWhisperLimit(),
          ]);
          whisperLimitCacheRef.current = {
            status: result,
            lastUpdate: Date.now(),
          };
          setRealtimeWhisperLimitStatus(result);
          setDailyWhisperLimit(limitConfig);

          log(`[WhisperUsage] ${reason} - 检查完成:`, {
            canUse: result.canUse,
            currentUsage: result.currentUsage,
            dailyLimit: result.dailyLimit,
            isVip: result.isVip
          });
        } catch (error) {
          logError(`[WhisperUsage] ${reason} - 检查失败:`, error);
        }
      } else {
        whisperLimitCacheRef.current = {
          status: null,
          lastUpdate: Date.now(),
        };
        setRealtimeWhisperLimitStatus(null);
        // 未登录用户也需要获取默认限制配置
        try {
          const limitConfig = await getDailyWhisperLimit();
          setDailyWhisperLimit(limitConfig);
        } catch (error) {
          logError("[WhisperUsage] 获取默认限制配置失败:", error);
        }
      }
    };

    // 只在用户登录状态变化时检查一次
    checkWhisperLimit("用户状态变化");
  }, [user?.id]);

  // 计算是否应该显示whisper限制UI
  const shouldShowWhisperLimitUI = useMemo(() => {
    // 在转录状态下，如果实时状态显示可以使用，绝对不显示限制UI
    if (inputState === "transcribing" && realtimeWhisperLimitStatus?.canUse) {
      log("[UI判断] 转录状态下且实时状态显示可以使用，不显示限制UI");
      return false;
    }

    // 优先检查实时状态：如果实时状态显示可以使用，绝对不显示限制UI
    if (realtimeWhisperLimitStatus) {
      // 如果是VIP用户或者可以使用，不显示限制UI
      if (
        realtimeWhisperLimitStatus.isVip ||
        realtimeWhisperLimitStatus.canUse
      ) {
        log("[UI判断] 实时状态显示VIP或可以使用，不显示限制UI", {
          isVip: realtimeWhisperLimitStatus.isVip,
          canUse: realtimeWhisperLimitStatus.canUse,
          currentUsage: realtimeWhisperLimitStatus.currentUsage,
          dailyLimit: realtimeWhisperLimitStatus.dailyLimit,
        });
        return false;
      }

      // 只有当明确达到限制且无法使用时才显示UI
      if (
        !realtimeWhisperLimitStatus.canUse &&
        realtimeWhisperLimitStatus.message === "FREE_LIMIT_REACHED"
      ) {
        log("[UI判断] 实时状态显示已达限制，显示限制UI", {
          canUse: realtimeWhisperLimitStatus.canUse,
          message: realtimeWhisperLimitStatus.message,
        });
        return true;
      }
    }

    // 如果有Redux中的whisperLimitError，但实时状态显示可以使用，不显示限制UI
    if (
      whisperLimitError &&
      (!realtimeWhisperLimitStatus || realtimeWhisperLimitStatus.canUse)
    ) {
      log("[UI判断] Redux中有错误但实时状态显示可以使用，不显示限制UI");
      return false;
    }

    // 如果有Redux中的whisperLimitError且没有实时状态覆盖，显示限制UI
    if (whisperLimitError) {
      log("[UI判断] Redux中有错误且无实时状态覆盖，显示限制UI");
      return true;
    }

    log("[UI判断] 默认不显示限制UI");
    return false;
  }, [whisperLimitError, realtimeWhisperLimitStatus, inputState]);

  // 处理whisper限制状态码到提示文案的转换
  const getWhisperLimitMessage = (status: any) => {
    if (!status) return "";

    switch (status.message) {
      case "VIP":
        return t("home.whisperLimit.vipUnlimited", {
          defaultValue: "VIP用户无使用限制",
        });
      case "FREE_REMAINING":
        return t("home.whisperLimit.remainingCount", {
          count: status.remaining,
          defaultValue: "今日剩余{{count}}次",
        });
      case "FREE_LIMIT_REACHED":
        return t("home.whisperLimit.message", {
          defaultValue: "您今日的高级语音转写次数已用完",
        });
      default:
        return status.message; // 兜底返回原始消息
    }
  };

  // 新增：首次登录引导弹窗状态
  const [showLoginGuide, setShowLoginGuide] = useState(false);
  const [loginGuideChecked, setLoginGuideChecked] = useState(false);
  const LOGIN_GUIDE_KEY = "knowme_login_guide_shown";

  // 检查是否需要弹出首次登录引导弹窗
  useEffect(() => {
    log(
      "[LoginGuide] useEffect isAuthenticated变化，重置loginGuideChecked为false，当前isAuthenticated:",
      isAuthenticated
    );
    setLoginGuideChecked(false); // 每次登录状态变化时重置
  }, [isAuthenticated]);

  useEffect(() => {
    async function checkLoginGuide() {
      log(
        "[LoginGuide] checkLoginGuide执行，isAuthenticated:",
        isAuthenticated,
        "loginGuideChecked:",
        loginGuideChecked
      );
      if (isAuthenticated) {
        setShowLoginGuide(false);
        setLoginGuideChecked(true);
        log("[LoginGuide] 已登录，强制关闭弹窗");
        return;
      }
      if (!isAuthenticated && !loginGuideChecked) {
        const flag = await AsyncStorage.getItem(LOGIN_GUIDE_KEY);
        log("[LoginGuide] AsyncStorage.getItem结果:", flag);
        if (flag !== "true") {
          log("[LoginGuide] 满足弹窗条件，setShowLoginGuide(true)");
          setShowLoginGuide(true);
        } else {
          log("[LoginGuide] 已展示过弹窗，不再弹出");
        }
        setLoginGuideChecked(true);
        log("[LoginGuide] setLoginGuideChecked(true)");
      } else {
        log("[LoginGuide] 不满足弹窗条件");
      }
    }
    checkLoginGuide();
  }, [isAuthenticated, loginGuideChecked]);

  // 处理弹窗登录
  const handleLoginGuideLogin = async () => {
    setShowLoginGuide(false);
    await AsyncStorage.setItem(LOGIN_GUIDE_KEY, "true");
    if (showLoginModal) showLoginModal();
    // 登录成功后设置whisper模型
    const checkAndSetModel = async () => {
      let userId = null;
      for (let i = 0; i < 20; i++) {
        const user = await getCurrentUser();
        if (user && user.data && user.data.user && user.data.user.id) {
          userId = user.data.user.id;
          break;
        }
        await new Promise((r) => setTimeout(r, 300));
      }
      if (userId) {
        await modelService.saveUserModelSettings(userId, {
          default_voice_model: "whisper-1",
        });
        await storageService.saveUserSettings({
          ...(await storageService.getUserSettings()),
          transcribeModel: "whisper-1",
        });
      } else {
        const old = await storageService.getUserSettings();
        await storageService.saveUserSettings({
          ...(old || {}),
          transcribeModel: "whisper-1",
        });
      }
      // 切换模型后允许弹toast
      hasShownWhisperToastRef.current = false;
      // 移除主动弹toast逻辑，toast 统一在主页 focus/useFocusEffect 里处理
    };
    setTimeout(checkAndSetModel, 1000); // 延迟执行，等待登录完成
  };

  // 处理弹窗跳过
  const handleLoginGuideSkip = async () => {
    setShowLoginGuide(false);
    await AsyncStorage.setItem(LOGIN_GUIDE_KEY, "true");
  };

  // 新增：记录本次会话是否已弹出whisper模型toast
  const hasShownWhisperToastRef = useRef(false);

  // 登录后强制刷新模型信息并弹toast（仅弹一次）
  useEffect(() => {
    if (isAuthenticated) {
      updateCurrentModelInfoLightweight(true).then((info) => {
        if (info.modelId === "whisper-1" && !hasShownWhisperToastRef.current) {
          showCenteredToast(t('home.whisperModelEnabled') as string, "success");
          hasShownWhisperToastRef.current = true;
        }
      });
    } else {
      // 退出登录时重置，允许下次登录后再次弹toast
      hasShownWhisperToastRef.current = false;
    }
  }, [isAuthenticated]);

  // 新增：页面focus时自动补偿模型设置和toast
  useFocusEffect(
    useCallback(() => {
      console.log("【TOAST_DEBUG】主页 focus 检查 toast");
      let cancelled = false;
      const checkAndSetModelOnFocus = async () => {
        if (!isAuthenticated) {
          hasShownWhisperToastRef.current = false;
          return;
        }
        // 关键：读取语音转写模型
        const userSettings = await storageService.getUserSettings();
        const transcribeModel = userSettings?.transcribeModel || "native";
        console.log(
          "【TOAST_DEBUG】当前转写模型",
          transcribeModel,
          "hasShownWhisperToastRef",
          hasShownWhisperToastRef.current
        );
        if (
          transcribeModel === "whisper-1" &&
          !hasShownWhisperToastRef.current
        ) {
          setTimeout(() => {
            if (!cancelled) {
              console.log("【TOAST_DEBUG】即将弹出 toast");
              showCenteredToast(t('home.whisperModelEnabled'), "success");
              hasShownWhisperToastRef.current = true;
            }
          }, 300);
        }
      };
      checkAndSetModelOnFocus();
      return () => {
        cancelled = true;
      };
    }, [isAuthenticated])
  );

  // 监听模板列表和默认模板变化，自动同步selectedTemplate
  useEffect(() => {
    if (inputState === 'template') {
      // 进入模板选择页面时，如未手动选中模板，自动选中最新默认模板
      if (!selectedTemplate && templates.length > 0) {
        const defaultTpl = templates.find(t => t.isDefault);
        if (defaultTpl) setSelectedTemplate(defaultTpl);
      }
    } else if (inputState === 'initial') {
      // 回到初始状态时，清空手动选中，保持下次进入自动同步
      if (selectedTemplate && templates.length > 0) {
        setSelectedTemplate(null);
      }
    }
    // 注意：在result状态时保持selectedTemplate，以便显示模板名称和支持重新生成功能
  }, [inputState, templates]);

  return (
    <ThemedSafeAreaView
      style={[
        styles.container,
        { backgroundColor: isDark ? "#151718" : "white" },
      ]}
      edges={["top"]}
    >
      <StatusBar style={isDark ? "light" : "dark"} />

      {/* 页面标题 */}
      <View
        style={[
          styles.header,
          {
            backgroundColor: isDark ? "#151718" : "white",
            borderBottomColor: isDark ? "#2B2F31" : "#f0f0f0",
          },
        ]}
      >
        <Text
          style={[styles.headerTitle, { color: isDark ? "#ECEDEE" : "#333" }]}
        >
          {inputState === "initial"
            ? t("home.title")
            : inputState === "recording"
            ? t("home.recording")
            : inputState === "transcribing"
            ? t("home.transcribing")
            : inputState === "template"
            ? t("home.selectStyle")
            : inputState === "error"
            ? t("home.networkError.title")
            : t("home.optimizationResult")}
        </Text>
        {(inputState === "recording" || inputState === "transcribing") && (
          <View
            style={[styles.recordingStatus, { backgroundColor: "transparent" }]}
          >
            {inputState === "recording" && (
              <View
                style={[styles.recordingDot, { backgroundColor: "#ff3b30" }]}
              />
            )}
            {inputState === "transcribing" && (
              <View
                style={[styles.recordingDot, { backgroundColor: "#34c759" }]}
              />
            )}
            <Text
              style={[styles.recordingTime, isDark && styles.darkRecordingTime]}
            >
              {formatTime(
                inputState === "transcribing"
                  ? lastRecordingTime
                  : recordingTime
              )}
            </Text>
          </View>
        )}
        {inputState === "template" && (
          <TouchableOpacity
            style={styles.addTemplateButton}
            onPress={handleCreateNewTemplate}
          >
            <IconSymbol
              name="plus"
              size={20}
              color={isDark ? "#9BA1A6" : "#666"}
            />
          </TouchableOpacity>
        )}
        {inputState === "result" && selectedTemplate && (
          <TouchableOpacity
            ref={templateButtonRef}
            style={styles.templateDropdownButton}
            onPress={handleTemplateButtonPress}
            activeOpacity={0.7}
          >
            <Text
              style={[
                styles.templateIndicator,
                { color: isDark ? "#ECEDEE" : "#333" },
              ]}
              numberOfLines={1}
              ellipsizeMode="middle"
            >
              {(() => {
                // 从allTemplates中找到完整的模板信息
                const fullTemplate = allTemplates.find(t => t.id === selectedTemplate.id);
                if (!fullTemplate) {
                  return selectedTemplate.title; // 降级到原始标题
                }

                // 转换为多语言翻译工具函数期望的格式
                const templateForI18n = {
                  id: fullTemplate.id.toString(),
                  name: fullTemplate.title,
                  description: fullTemplate.description || "",
                  name_key: fullTemplate.name_key,
                  description_key: fullTemplate.description_key,
                  prompt_text: fullTemplate.prompt || "",
                  is_system: fullTemplate.isSystem,
                };
                return getTemplateDisplayName(templateForI18n, t);
              })()}
            </Text>
            <IconSymbol
              name="chevron.down"
              size={14}
              color={isDark ? "#9BA1A6" : "#666"}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* 主要内容区域布局容器 */}
      <View
        style={styles.bodyContainer}
        lightColor="transparent"
        darkColor="transparent"
      >
        {/* 灰色背景内容区 - 使用flex布局占据70% */}
        <View
          style={[
            styles.mainContentArea,
            // 根据不同状态调整布局
            inputState === "initial" && styles.mainContentAreaInitial,
            inputState !== "initial" && styles.transparentContentArea,
            (inputState === "template" ||
              inputState === "result" ||
              inputState === "error") &&
              styles.fullPageContentArea,
            isDark && styles.mainContentAreaDark,
          ]}
          lightColor={inputState === "initial" ? "#f5f5f5" : "transparent"}
          darkColor={inputState === "initial" ? "#1E2122" : "transparent"}
        >
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={[
              styles.scrollViewContent,
              inputState === "result" && { paddingBottom: 120 },
              inputState === "template" && {
                flexGrow: 1,
                paddingBottom: 120,
              },
            ]}
          >
            {/* 初始状态 */}
            {inputState === "initial" && (
              <View
                style={styles.emptyStateContainer}
                lightColor="transparent"
                darkColor="transparent"
              >
                <EmptyStateIcon />

                <Text
                  style={[
                    styles.emptyStateTitle,
                    isDark && styles.darkEmptyStateTitle,
                  ]}
                >
                  {t("home.inputTitle")}
                </Text>
                <Text
                  style={[
                    styles.emptyStateSubtitle,
                    isDark && styles.darkEmptyStateSubtitle,
                  ]}
                >
                  {t("home.inputSubtitle")}
                </Text>
                <FeatureTags />
              </View>
            )}

            {/* 录音状态 */}
            {inputState === "recording" && (
              <View
                style={styles.recordingContainer}
                lightColor="transparent"
                darkColor="transparent"
              >
                {/* 使用RecognizedTextDisplay显示识别文本和波形 */}
                <RecognizedTextDisplay
                  text={recognizedText}
                  recordingStatus={recordingStatus}
                  audioLevel={audioLevel}
                  isDark={isDark} // 传递isDark参数给子组件
                  isStatusText={
                    recognizedText.includes("正在录音") ||
                    recognizedText.includes("正在聆听") ||
                    recognizedText.includes("已录制") ||
                    (recognizedText.length < 30 &&
                      (recognizedText.includes("录音中") ||
                        recognizedText.includes("聆听中")))
                  }
                />
              </View>
            )}

            {/* 转录中状态或错误状态 - 使用与录音状态相同的布局结构 */}
            {(inputState === "transcribing" || inputState === "error") && (
              <View
                style={styles.recordingContainer}
                lightColor="transparent"
                darkColor="transparent"
              >
                {/* 优先检查whisper限制错误，然后是网络错误，最后是一般错误 */}
                {/* 在转录状态下，如果实时状态显示可以使用，不显示限制UI */}
                {shouldShowWhisperLimitUI &&
                !(
                  inputState === "transcribing" &&
                  realtimeWhisperLimitStatus?.canUse
                ) ? (
                  /* 显示whisper限制专用UI */
                  <View
                    style={[
                      styles.networkErrorContainer,
                      isDark && styles.networkErrorContainerDark,
                    ]}
                    lightColor="#FEF2F2"
                    darkColor="#2B1F1F"
                  >
                    <View
                      style={styles.networkErrorContent}
                      lightColor="transparent"
                      darkColor="transparent"
                    >
                      {/* whisper限制图标 */}
                      <IconSymbol
                        name="mic.circle.fill"
                        size={32}
                        color={isDark ? "#F59E0B" : "#D97706"}
                        style={styles.networkErrorIcon}
                      />

                      {/* whisper限制标题 */}
                      <Text
                        style={[
                          styles.networkErrorTitle,
                          isDark && styles.networkErrorTitleDark,
                        ]}
                      >
                        {t("home.whisperLimit.title")}
                      </Text>

                      {/* whisper限制信息 */}
                      <Text
                        style={[
                          styles.networkErrorMessage,
                          isDark && styles.networkErrorMessageDark,
                        ]}
                      >
                        {(() => {
                          // 优先使用数据库状态码
                          if (realtimeWhisperLimitStatus?.message) {
                            return getWhisperLimitMessage(
                              realtimeWhisperLimitStatus
                            );
                          }
                          // fallback到whisperLimitError
                          if (whisperLimitError?.message) {
                            return whisperLimitError.message;
                          }
                          // 最后使用默认消息
                          return t("home.whisperLimit.message", {
                            defaultValue: "您今日的高级语音转写次数已用完",
                          });
                        })()}
                      </Text>

                      {/* whisper限制提示信息 */}
                      <View
                        style={styles.networkErrorTips}
                        lightColor="transparent"
                        darkColor="transparent"
                      >
                        <Text
                          style={[
                            styles.networkErrorTip,
                            isDark && styles.networkErrorTipDark,
                          ]}
                        >
                          {t("home.whisperLimit.tip1", {
                            defaultValue: "• 升级VIP享受无限制语音转录",
                          })}
                        </Text>
                        <Text
                          style={[
                            styles.networkErrorTip,
                            isDark && styles.networkErrorTipDark,
                          ]}
                        >
                          {t("home.whisperLimit.tip2", {
                            defaultValue: "• 或改用设备语音识别继续使用",
                          })}
                        </Text>
                        <Text
                          style={[
                            styles.networkErrorTip,
                            isDark && styles.networkErrorTipDark,
                          ]}
                        >
                          {t("home.whisperLimit.tip3", {
                            limit: dailyWhisperLimit,
                            defaultValue: `• 明日将重新获得${dailyWhisperLimit}次免费额度`,
                          })}
                        </Text>
                      </View>

                      {/* whisper限制操作按钮 */}
                      <View
                        style={[
                          styles.networkErrorButtons,
                          styles.networkErrorButtonsVertical,
                        ]}
                        lightColor="transparent"
                        darkColor="transparent"
                      >
                        <TouchableOpacity
                          style={[
                            styles.retryButton,
                            styles.retryButtonVertical,
                            { backgroundColor: isDark ? "#F59E0B" : "#D97706" },
                          ]}
                          onPress={handleWhisperLimitUpgrade}
                          activeOpacity={0.7}
                        >
                          <Text
                            style={[styles.retryButtonText, { color: "white" }]}
                          >
                            {t("home.whisperLimit.upgradeOption")}
                          </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[
                            styles.rerecordButton,
                            isDark && styles.rerecordButtonDark,
                            styles.rerecordButtonVertical,
                          ]}
                          onPress={handleSwitchToNativeModel}
                          activeOpacity={0.7}
                        >
                          <Text
                            style={[
                              styles.rerecordButtonText,
                              isDark && styles.rerecordButtonTextDark,
                            ]}
                          >
                            {t("home.whisperLimit.switchOption")}
                          </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[
                            styles.rerecordButton,
                            isDark && styles.rerecordButtonDark,
                            styles.rerecordButtonVertical,
                            {
                              marginTop: 8,
                              borderWidth: 0,
                              backgroundColor: "transparent",
                            },
                          ]}
                          onPress={handleNetworkErrorRerecord}
                          activeOpacity={0.7}
                        >
                          <Text
                            style={[
                              styles.rerecordButtonText,
                              isDark && styles.rerecordButtonTextDark,
                              { fontSize: 14, opacity: 0.7 },
                            ]}
                          >
                            {t("home.networkError.rerecordButton")}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                ) : showNetworkError || inputState === "error" ? (
                  <View
                    style={[
                      styles.networkErrorContainer,
                      isDark && styles.networkErrorContainerDark,
                    ]}
                    lightColor="#FEF2F2"
                    darkColor="#2B1F1F"
                  >
                    <View
                      style={styles.networkErrorContent}
                      lightColor="transparent"
                      darkColor="transparent"
                    >
                      {/* 错误图标 */}
                      <IconSymbol
                        name="wifi.exclamationmark"
                        size={32}
                        color={isDark ? "#F87171" : "#DC2626"}
                        style={styles.networkErrorIcon}
                      />

                      {/* 错误标题 */}
                      <Text
                        style={[
                          styles.networkErrorTitle,
                          isDark && styles.networkErrorTitleDark,
                        ]}
                      >
                        {t("home.networkError.title")}
                      </Text>

                      {/* 错误信息 */}
                      <Text
                        style={[
                          styles.networkErrorMessage,
                          isDark && styles.networkErrorMessageDark,
                        ]}
                      >
                        {t("home.networkError.message")}
                      </Text>

                      {/* 提示信息 */}
                      <View
                        style={styles.networkErrorTips}
                        lightColor="transparent"
                        darkColor="transparent"
                      >
                        <Text
                          style={[
                            styles.networkErrorTip,
                            isDark && styles.networkErrorTipDark,
                          ]}
                        >
                          {t("home.networkError.tip1")}
                        </Text>
                        <Text
                          style={[
                            styles.networkErrorTip,
                            isDark && styles.networkErrorTipDark,
                          ]}
                        >
                          {t("home.networkError.tip2")}
                        </Text>
                        <Text
                          style={[
                            styles.networkErrorTip,
                            isDark && styles.networkErrorTipDark,
                          ]}
                        >
                          {t("home.networkError.tip3")}
                        </Text>
                      </View>

                      {/* 操作按钮 - 根据语言自适应布局 */}
                      <View
                        style={[
                          styles.networkErrorButtons,
                          // 英文按钮文案较长，使用竖直布局；中文使用水平布局
                          i18n.language === "en" &&
                            styles.networkErrorButtonsVertical,
                        ]}
                        lightColor="transparent"
                        darkColor="transparent"
                      >
                        <TouchableOpacity
                          style={[
                            styles.retryButton,
                            // 英文模式下调整按钮样式
                            i18n.language === "en" &&
                              styles.retryButtonVertical,
                          ]}
                          onPress={handleNetworkErrorRetry}
                          activeOpacity={0.7}
                          disabled={isRetrying}
                        >
                          <Text style={styles.retryButtonText}>
                            {isRetrying
                              ? t("common.loading")
                              : t("home.networkError.retryButton")}
                          </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[
                            styles.rerecordButton,
                            isDark && styles.rerecordButtonDark,
                            // 英文模式下调整按钮样式
                            i18n.language === "en" &&
                              styles.rerecordButtonVertical,
                          ]}
                          onPress={handleNetworkErrorRerecord}
                          activeOpacity={0.7}
                        >
                          <Text
                            style={[
                              styles.rerecordButtonText,
                              isDark && styles.rerecordButtonTextDark,
                            ]}
                          >
                            {t("home.networkError.rerecordButton")}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                ) : (
                  /* 使用RecognizedTextDisplay显示转录文本和波形 */
                  <RecognizedTextDisplay
                    text={
                      transcriptionError
                        ? `${t(
                            "home.textDisplay.speechRecognitionFailed"
                          )}: ${transcriptionError}`
                        : recognizedText
                    }
                    recordingStatus={
                      transcriptionError ? "error" : "transcribing"
                    }
                    audioLevel={0.5}
                    isDark={isDark}
                    placeholderText={
                      transcriptionError
                        ? ""
                        : t("home.textDisplay.transcribingWithModel", {
                            model: transcriptionModel.name,
                          })
                    }
                    showError={!!transcriptionError}
                    isStatusText={
                      !transcriptionError &&
                      (recognizedText.includes("正在重试") ||
                        recognizedText.includes("正在使用") ||
                        recognizedText.includes("正在转录") ||
                        recognizedText.includes("正在转写"))
                    }
                    progressText={progressText || undefined}
                  />
                )}
              </View>
            )}

            {/* 模板选择状态 */}
            {inputState === "template" && (
              <KeyboardAvoidingView
                style={{ flex: 1 }}
                behavior={Platform.OS === "ios" ? "padding" : "height"}
                keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
              >
                <TouchableOpacity
                  style={[
                    styles.templatePageContainer,
                    { backgroundColor: "transparent" },
                  ]}
                  activeOpacity={1}
                  onPress={Keyboard.dismiss}
                >
                  {/* 文本显示区域 - 替换为可编辑的TextInput */}
                  <View
                    style={styles.templateTextContainer}
                    lightColor="transparent"
                    darkColor="transparent"
                  >
                    <View
                      style={[
                        styles.recognizedTextContainer,
                        isDark && { borderWidth: 1, borderColor: "#2B2F31" },
                      ]}
                      lightColor="#f0f0f0"
                      darkColor="#1E2122"
                    >
                      <TextInput
                        style={[
                          styles.recognizedText,
                          styles.editableText,
                          isDark && { color: "#ECEDEE" },
                        ]}
                        value={recognizedText}
                        onChangeText={setRecognizedText}
                        multiline={true}
                        autoCapitalize="none"
                        autoCorrect={true}
                        placeholder={t("home.inputOrEditText")}
                        placeholderTextColor={isDark ? "#6B7280" : "#9CA3AF"}
                      />
                    </View>
                    {/* 添加编辑提示文本 */}
                    <Text
                      tKey="home.editHint"
                      style={[
                        styles.editingHint,
                        isDark && { color: "#6B7280" },
                      ]}
                    />
                  </View>

                  {/* 模板选择区域 */}
                  <View
                    style={styles.templatesSection}
                    lightColor="transparent"
                    darkColor="transparent"
                  >
                    <View
                      style={styles.templatesSectionHeader}
                      lightColor="transparent"
                      darkColor="transparent"
                    >
                      <Text
                        style={[
                          styles.templatesSectionTitle,
                          isDark && { color: "#9BA1A6" },
                        ]}
                      >
                        {t("home.selectTemplateHint")}
                      </Text>

                      {/* 模板管理按钮 */}
                      <TouchableOpacity
                        style={[
                          styles.templateManageButton,
                          isDark && styles.templateManageButtonDark,
                        ]}
                        onPress={navigateToTemplateManager}
                        activeOpacity={0.7}
                      >
                        <IconSymbol
                          name="slider.horizontal.3"
                          size={16}
                          color={isDark ? "#9BA1A6" : "#6B7280"}
                        />
                        <Text
                          style={[
                            styles.templateManageButtonText,
                            isDark && { color: "#9BA1A6" },
                          ]}
                        >
                          {t("settings.templatePersonalizationPortal")}
                        </Text>
                      </TouchableOpacity>
                    </View>

                    {/* 网格布局的模板列表，两行三列 */}
                    <View
                      style={styles.templatesGrid}
                      lightColor="transparent"
                      darkColor="transparent"
                    >
                      {templates.slice(0, 6).map((template, index) => {
                        // 如果总共有5个模板并且当前是第5个之后的位置，则留空
                        if (templates.length === 5 && index === 5) {
                          return (
                            <View
                              key="empty-slot"
                              style={styles.templateGridItem}
                            />
                          );
                        }

                        // 确定是否是每行的最后一个
                        const isLastInRow = (index + 1) % 3 === 0;

                        return (
                          <View
                            key={template.id}
                            style={[
                              isLastInRow
                                ? styles.templateGridItemNoMargin
                                : styles.templateGridItem,
                            ]}
                            lightColor="transparent"
                            darkColor="transparent"
                          >
                            <TemplateCard
                              template={template}
                              isActive={selectedTemplate?.id === template.id}
                              onPress={selectTemplate}
                              style={styles.templateCardFixed}
                              layoutType="iconTitle"
                            />
                          </View>
                        );
                      })}
                    </View>

                    {/* 更多风格标题 - 只有当模板数量超过6个时才显示 */}
                    {templates.length > 6 && (
                      <>
                        <Text
                          style={[
                            styles.templatesSectionTitle,
                            isDark && { color: "#9BA1A6" },
                            { marginBottom: 15 },
                          ]}
                        >
                          {t("home.moreStyles")}
                        </Text>

                        {/* 只显示未在网格中显示的模板 */}
                        <ScrollView
                          horizontal
                          showsHorizontalScrollIndicator={true}
                          contentContainerStyle={styles.allTemplatesList}
                        >
                          {templates.slice(6).map((template) => (
                            <View
                              key={template.id}
                              style={styles.scrollTemplateItem}
                              lightColor="transparent"
                              darkColor="transparent"
                            >
                              <TemplateCard
                                template={template}
                                isActive={selectedTemplate?.id === template.id}
                                onPress={selectTemplate}
                                style={styles.templateCardFixed}
                              />
                            </View>
                          ))}
                        </ScrollView>
                      </>
                    )}
                  </View>
                </TouchableOpacity>
              </KeyboardAvoidingView>
            )}

            {/* 结果展示状态 */}
            {inputState === "result" && (
              <View
                style={styles.resultContainer}
                lightColor="transparent"
                darkColor="transparent"
              >
                {/* 优化后的文本内容区域 */}
                {isOptimizing ? (
                  // 加载中状态
                  <View
                    style={[
                      styles.optimizedTextContainer,
                      styles.loadingContainer,
                      isDark && styles.optimizedTextContainerDark,
                    ]}
                    lightColor="white"
                    darkColor="#1E2122"
                  >
                    {/* 加载动画 */}
                    <View
                      style={[
                        styles.aiModelInfoContainer,
                        { alignItems: "center", justifyContent: "center" },
                        isDark && { borderBottomColor: "#2B2F31" },
                      ]}
                      lightColor="white"
                      darkColor="#1E2122"
                    >
                      <View
                        style={[
                          styles.modelBadge,
                          {
                            backgroundColor: isDark
                              ? "rgba(150, 150, 150, 0.1)"
                              : "rgba(230, 230, 230, 0.5)",
                          },
                          {
                            borderColor: isDark
                              ? "rgba(150, 150, 150, 0.2)"
                              : "rgba(230, 230, 230, 0.8)",
                          },
                        ]}
                      >
                        <IconSymbol
                          name="cpu"
                          size={14}
                          color={isDark ? "#9BA1A6" : "#6B7280"}
                        />
                        <Text
                          style={[
                            styles.modelName,
                            isDark && { color: "#9BA1A6" },
                          ]}
                        >
                          {currentModel.name}{" "}
                          {currentModel.isPremium
                            ? t("common.premiumModel")
                            : t("common.basicModel")}
                        </Text>
                      </View>
                    </View>

                    <View
                      style={styles.loadingAnimationContainer}
                      lightColor="white"
                      darkColor="#1E2122"
                    >
                      <View
                        style={styles.loadingAnimationWrapper}
                        lightColor="white"
                        darkColor="#1E2122"
                      >
                        {/* <ActivityIndicator
                          size="large"
                          color={isDark ? "#8364e2" : "#6a5ae1"}
                        /> */}
                        <LottieView
                          source={require("@/assets/animations/loading.json")}
                          style={styles.loadingAnimationContainer}
                          autoPlay
                          loop
                        />
                        <Text
                          style={[
                            styles.loadingText,
                            isDark && { color: "#9BA1A6" },
                          ]}
                        >
                          {t("home.optimizingText")}
                        </Text>
                      </View>
                    </View>

                    {/* VIP升级提示 */}
                    <View
                      style={styles.vipUpgradeContainer}
                      lightColor="white"
                      darkColor="#1E2122"
                    >
                      {isVip ? (
                        // VIP用户显示VIP服务标识
                        <View
                          style={[
                            styles.vipServiceBadge,
                            {
                              backgroundColor: isDark
                                ? "rgba(255, 193, 7, 0.15)"
                                : "rgba(255, 193, 7, 0.1)",
                            },
                            {
                              borderWidth: 1,
                              borderColor: isDark
                                ? "rgba(255, 193, 7, 0.3)"
                                : "rgba(255, 193, 7, 0.2)",
                            },
                          ]}
                        >
                          <RNAnimated.View
                            style={{
                              width: 28,
                              height: 28,
                              borderRadius: 14,
                              alignItems: "center",
                              justifyContent: "center",
                              opacity: vipGlowAnim,
                              transform: [
                                {
                                  scale: vipGlowAnim,
                                },
                              ],
                            }}
                          >
                            <IconSymbol
                              name="crown.fill"
                              size={20}
                              color="#ffc107"
                            />
                          </RNAnimated.View>
                          <Text
                            style={[
                              styles.vipServiceText,
                              { color: isDark ? "#ffc107" : "#cc8800" },
                            ]}
                          >
                            {t("home.vipExclusive")}
                          </Text>
                        </View>
                      ) : (
                        // 非VIP用户显示升级提示 - 在加载状态下只显示提示文本，不显示按钮
                        <View
                          style={[
                            styles.loadingVipHintContainer,
                            {
                              backgroundColor: isDark
                                ? "rgba(75, 85, 99, 0.15)"
                                : "rgba(229, 231, 235, 0.5)",
                              borderColor: isDark
                                ? "rgba(107, 114, 128, 0.2)"
                                : "rgba(209, 213, 219, 0.8)",
                            },
                          ]}
                        >
                          <IconSymbol
                            name="bolt.badge.clock"
                            size={18}
                            color={isDark ? "#9BA1A6" : "#6B7280"}
                          />
                          <Text
                            style={[
                              styles.loadingVipHintText,
                              isDark && { color: "#9BA1A6" },
                            ]}
                          >
                            {t("home.vipHint")}
                          </Text>
                        </View>
                      )}
                    </View>
                  </View>
                ) : (
                  // 结果展示
                  <ScrollView style={styles.optimizedTextScroll}>
                    <View
                      style={[
                        styles.optimizedTextContainer,
                        isDark && styles.optimizedTextContainerDark,
                      ]}
                      lightColor="white"
                      darkColor="#1E2122"
                    >
                      {/* 显示AI模型信息 */}
                      <View
                        style={[
                          styles.aiModelInfoContainer,
                          isDark && { borderBottomColor: "#2B2F31" },
                        ]}
                        lightColor="white"
                        darkColor="#1E2122"
                      >
                        <View
                          style={[
                            styles.modelBadge,
                            {
                              backgroundColor: isDark
                                ? "rgba(150, 150, 150, 0.1)"
                                : "rgba(230, 230, 230, 0.5)",
                            },
                            {
                              borderColor: isDark
                                ? "rgba(150, 150, 150, 0.2)"
                                : "rgba(230, 230, 230, 0.8)",
                            },
                          ]}
                        >
                          <IconSymbol
                            name="cpu"
                            size={14}
                            color={isDark ? "#9BA1A6" : "#6B7280"}
                          />
                          <Text
                            style={[
                              styles.modelName,
                              isDark && { color: "#9BA1A6" },
                            ]}
                          >
                            {currentModel.name}{" "}
                            {currentModel.isPremium
                              ? t("common.premiumModel")
                              : t("common.basicModel")}
                          </Text>
                        </View>

                        {/* 重新生成按钮 */}
                        <TouchableOpacity
                          style={[
                            styles.regenerateButton,
                            isDark && styles.regenerateButtonDark,
                          ]}
                          onPress={handleRegenerate}
                        >
                          <IconSymbol
                            name="arrow.clockwise"
                            size={12}
                            color={isDark ? "#8364e2" : "#6a5ae1"}
                          />
                          <Text
                            style={[
                              styles.regenerateButtonText,
                              isDark && { color: "#8364e2" },
                            ]}
                          >
                            {t("home.regenerate")}
                          </Text>
                        </TouchableOpacity>
                      </View>

                      {/* 优化结果显示 */}
                      <Text
                        style={[
                          styles.optimizedText,
                          isDark && styles.darkOptimizedText,
                        ]}
                      >
                        {(() => {
                          log("[UI渲染] 优化文本显示检查:", {
                            hasOptimizedText: !!optimizedText,
                            optimizedTextLength: optimizedText?.length || 0,
                            optimizedTextPreview:
                              optimizedText?.substring(0, 50) || "",
                            optimizedTextType: typeof optimizedText,
                            isOptimizing,
                            inputState,
                            speechOptimizedTextPreview:
                              speechOptimizedText?.substring(0, 50) || "",
                            speechOptimizedTextLength:
                              speechOptimizedText?.length || 0,
                          });

                          // 在结果页面，优先使用Redux状态，fallback到UI本地状态
                          const displayText =
                            speechOptimizedText || optimizedText;

                          if (!displayText || displayText.trim() === "") {
                            log("[UI渲染] 优化文本为空，显示占位符");
                            return t("home.textDisplay.emptyText") as string;
                          }

                          const finalText = displayText.trim();
                          log("[UI渲染] 将显示的优化文本:", finalText);
                          return finalText;
                        })()}
                      </Text>

                      {/* VIP升级提示放在底部 */}
                      <View
                        style={[
                          styles.resultVipUpgradeContainer,
                          isDark && { borderTopColor: "#2B2F31" },
                        ]}
                        lightColor="white"
                        darkColor="#1E2122"
                      >
                        {isVip ? (
                          // VIP用户显示VIP服务标识
                          <View
                            style={[
                              styles.vipServiceBadge,
                              {
                                backgroundColor: isDark
                                  ? "rgba(255, 193, 7, 0.15)"
                                  : "rgba(255, 193, 7, 0.1)",
                              },
                              {
                                borderWidth: 1,
                                borderColor: isDark
                                  ? "rgba(255, 193, 7, 0.3)"
                                  : "rgba(255, 193, 7, 0.2)",
                              },
                            ]}
                          >
                            <RNAnimated.View
                              style={{
                                width: 28,
                                height: 28,
                                borderRadius: 14,
                                alignItems: "center",
                                justifyContent: "center",
                                opacity: vipGlowAnim,
                                transform: [
                                  {
                                    scale: vipGlowAnim,
                                  },
                                ],
                              }}
                            >
                              <IconSymbol
                                name="crown.fill"
                                size={20}
                                color="#ffc107"
                              />
                            </RNAnimated.View>
                            <Text
                              style={[
                                styles.vipServiceText,
                                { color: isDark ? "#ffc107" : "#cc8800" },
                              ]}
                            >
                              {t("home.vipExclusive")}
                            </Text>
                          </View>
                        ) : (
                          // 非VIP用户显示升级提示
                          <>
                            <Text
                              style={[
                                styles.vipUpgradeText,
                                isDark && { color: "#9BA1A6" },
                              ]}
                            >
                              {t("home.vipUpgradeDescription")}
                            </Text>
                            <TouchableOpacity
                              style={[
                                styles.vipUpgradeButton,
                                isDark && { backgroundColor: "#6a5ae1" },
                              ]}
                              onPress={handleUpgradeVIP}
                            >
                              <IconSymbol name="bolt" size={16} color="white" />
                              <Text style={styles.vipUpgradeButtonText}>
                                {t("home.upgradeNow")}
                              </Text>
                            </TouchableOpacity>
                          </>
                        )}
                      </View>
                    </View>
                  </ScrollView>
                )}
              </View>
            )}
          </ScrollView>
        </View>

        {/* 操作区域 - 使用flex布局占据30% - 只在需要的状态下显示 */}
        {(inputState === "initial" ||
          inputState === "recording" ||
          inputState === "transcribing" ||
          inputState === "error") && (
          <View
            style={[
              inputState === "initial"
                ? styles.actionAreaInitial
                : styles.actionArea,
              // 为不同状态调整内边距
              {
                paddingBottom:
                  inputState === "transcribing"
                    ? Math.max(40, insets.bottom + 20) // transcribing状态增加更多底部间距
                    : Math.max(20, insets.bottom),
              },
            ]}
            lightColor="transparent"
            darkColor="transparent"
          >
            {inputState === "initial" ? (
              // 语音输入按钮
              <RNAnimated.View
                style={{
                  transform: [{ scale: pulseAnim }],
                }}
              >
                <TouchableOpacity
                  style={styles.voiceBtn}
                  onPress={startRecording}
                  activeOpacity={0.7}
                >
                  <IconSymbol name="mic.fill" size={32} color="white" />
                </TouchableOpacity>
              </RNAnimated.View>
            ) : inputState === "transcribing" || inputState === "error" ? (
              // 转录状态或错误状态下的按钮组合
              <View
                style={styles.recordingControlsContainer}
                lightColor="transparent"
                darkColor="transparent"
              >
                {showRetryButton || inputState === "error" ? (
                  // 显示重试按钮和新录音按钮
                  <View
                    style={styles.recordingControls}
                    lightColor="transparent"
                    darkColor="transparent"
                  >
                    <TouchableOpacity
                      style={styles.pauseButton}
                      onPress={retryTranscription}
                      activeOpacity={0.7}
                    >
                      <View
                        style={styles.secondaryButton}
                        lightColor="#6a5ae1"
                        darkColor="#6a5ae1"
                      >
                        <IconSymbol
                          name="arrow.clockwise"
                          size={18}
                          color="white"
                        />
                        <Text style={styles.controlButtonText}>
                          {t("common.retry")}
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.finishButton}
                      onPress={createNewInputDuringTranscribing}
                      activeOpacity={0.7}
                    >
                      <View
                        style={styles.primaryButton}
                        lightColor="#6a5ae1"
                        darkColor="#6a5ae1"
                      >
                        <IconSymbol name="mic.fill" size={18} color="white" />
                        <Text style={styles.controlButtonText}>
                          {t("home.newInput")}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                ) : (
                  // 只显示新录音按钮 - 根据时间显示不同状态
                  <View
                    style={styles.recordingControlsContainer}
                    lightColor="transparent"
                    darkColor="transparent"
                  >
                    {showTranscribingHint && (
                      <Text
                        style={[
                          styles.transcribingHintText,
                          isDark && styles.transcribingHintTextDark,
                        ]}
                      >
                        {t("home.textDisplay.noResponse")}
                      </Text>
                    )}
                    <View
                      style={styles.recordingControls}
                      lightColor="transparent"
                      darkColor="transparent"
                    >
                      {!showTranscribingHint ? (
                        // 转录状态指示器
                        <View style={styles.transcribingIndicator}>
                          <View style={styles.transcribingIconContainer}>
                            <IconSymbol
                              name="waveform"
                              size={20}
                              color={isDark ? "#D1D5DB" : "#6B7280"}
                            />
                          </View>
                          <Text
                            style={[
                              styles.transcribingStatusText,
                              isDark && styles.transcribingStatusTextDark,
                            ]}
                          >
                            {t("home.textDisplay.transcribingShort")}
                          </Text>
                        </View>
                      ) : (
                        // 新录音按钮
                        <TouchableOpacity
                          style={styles.singleButton}
                          onPress={createNewInputDuringTranscribing}
                          activeOpacity={0.7}
                        >
                          <View
                            style={[styles.primaryButton]}
                            lightColor="#6a5ae1"
                            darkColor="#6a5ae1"
                          >
                            <IconSymbol
                              name="mic.fill"
                              size={18}
                              color="white"
                            />
                            <Text style={styles.controlButtonText}>
                              {t("home.newInput")}
                            </Text>
                          </View>
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                )}
              </View>
            ) : (
              // 录音控制区域
              <View
                style={styles.recordingControlsContainer}
                lightColor="transparent"
                darkColor="transparent"
              >
                {/* 录音控制按钮 */}
                <View
                  style={styles.recordingControls}
                  lightColor="transparent"
                  darkColor="transparent"
                >
                  {/* 根据录音状态显示暂停/继续按钮 */}
                  <TouchableOpacity
                    style={styles.pauseButton}
                    onPress={
                      recordingStatus === "paused"
                        ? resumeRecording
                        : pauseRecording
                    }
                    activeOpacity={0.7}
                  >
                    <View
                      style={styles.secondaryButton}
                      lightColor="#6a5ae1"
                      darkColor="#6a5ae1"
                    >
                      <IconSymbol
                        name={
                          recordingStatus === "paused"
                            ? "play.fill"
                            : "pause.fill"
                        }
                        size={18}
                        color="white"
                      />
                      <Text style={styles.controlButtonText}>
                        {recordingStatus === "paused"
                          ? t("home.continue")
                          : t("home.pause")}
                      </Text>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.finishButton}
                    onPress={finishRecording}
                    activeOpacity={0.7}
                  >
                    <View
                      style={styles.primaryButton}
                      lightColor="#6a5ae1"
                      darkColor="#6a5ae1"
                    >
                      <IconSymbol name="checkmark" size={18} color="white" />
                      <Text style={styles.controlButtonText}>
                        {t("home.finish")}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        )}

        {/* 底部固定的按钮区域（常驻） */}
        {inputState === "template" && (
          <View
            style={[
              styles.fixedTemplateFooter,
              { bottom: (Platform.OS === "ios" ? insets.bottom : 0) + 40 },
              isDark && styles.fixedTemplateFooterDark,
            ]}
            lightColor="white"
            darkColor="#1E2122"
          >
            <TouchableOpacity
              style={styles.optimizeButton}
              onPress={startOptimizing}
            >
              <Text style={styles.optimizeButtonText}>
                {t("home.optimize")}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {inputState === "result" && (
          <View
            style={[
              styles.fixedResultFooter,
              { bottom: (Platform.OS === "ios" ? insets.bottom : 0) + 40 },
              isDark && styles.fixedResultFooterDark,
            ]}
            lightColor="white"
            darkColor="#1E2122"
          >
            <View
              style={styles.resultActions}
              lightColor="transparent"
              darkColor="transparent"
            >
              {(() => {
                // 计算是否有有效的优化文本
                const displayText = speechOptimizedText || optimizedText;
                const hasValidText = displayText && displayText.trim() !== "";
                // 在重新生成期间或没有有效文本时，按钮应该被禁用
                const isButtonEnabled = hasValidText && !isOptimizing;

                return (
                  <>
                    <TouchableOpacity
                      style={[
                        styles.resultActionButton,
                        styles.secondaryActionButton,
                        isDark && styles.secondaryActionButtonDark,
                        !isButtonEnabled && styles.disabledActionButton,
                      ]}
                      onPress={() => {
                        if (!isButtonEnabled) return;
                        // 复制文本并显示Badge
                        Clipboard.setStringAsync(displayText);
                        safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                        showCopyBadge();
                      }}
                      disabled={!isButtonEnabled}
                    >
                      <IconSymbol
                        name="doc.on.doc"
                        size={18}
                        color={!isButtonEnabled ? (isDark ? "#4B5563" : "#D1D5DB") : (isDark ? "#9CA3AF" : "#6B7280")}
                      />
                      <Text
                        style={[
                          styles.resultActionText,
                          styles.secondaryActionText,
                          isDark && styles.secondaryActionTextDark,
                          !isButtonEnabled && styles.disabledActionText,
                        ]}
                      >
                        {t("home.copy")}
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        styles.resultActionButton,
                        styles.secondaryActionButton,
                        isDark && styles.secondaryActionButtonDark,
                        !isButtonEnabled && styles.disabledActionButton,
                      ]}
                      onPress={() => {
                        if (!isButtonEnabled) return;
                        shareOptimizedText();
                      }}
                      disabled={!isButtonEnabled}
                    >
                      <IconSymbol
                        name="square.and.arrow.up"
                        size={18}
                        color={!isButtonEnabled ? (isDark ? "#4B5563" : "#D1D5DB") : (isDark ? "#9CA3AF" : "#6B7280")}
                      />
                      <Text
                        style={[
                          styles.resultActionText,
                          styles.secondaryActionText,
                          isDark && styles.secondaryActionTextDark,
                          !isButtonEnabled && styles.disabledActionText,
                        ]}
                      >
                        {t("home.share")}
                      </Text>
                    </TouchableOpacity>
                  </>
                );
              })()}
            </View>

            <TouchableOpacity
              style={[
                styles.newInputButton,
                styles.primaryNewInputButton,
                isDark && styles.primaryNewInputButtonDark,
              ]}
              onPress={createNewInput}
            >
              <IconSymbol name="mic.fill" size={18} color="white" />
              <Text
                style={[
                  styles.newInputButtonText,
                  styles.primaryNewInputButtonText,
                ]}
              >
                {t("home.newInput")}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* VIP升级Modal */}
      <VipUpgradeModal
        visible={vipUpgradeModalVisible}
        onClose={() => {
          setVipUpgradeModalVisible(false);
          setVipUpgradeScene(null);
          setSelectedVipTemplate(null);
        }}
        onUpgrade={handleVipUpgrade}
        title={getVipUpgradeContent().title}
        description={getVipUpgradeContent().description}
      />

      {/* Whisper使用限制Modal */}
      <VipUpgradeModal
        visible={showWhisperLimitModal}
        onClose={handleCloseWhisperLimitModal}
        onUpgrade={handleWhisperLimitUpgrade}
        title={t("home.whisperLimit.title")}
        description={(() => {
          let desc = t("home.whisperLimit.message", {
            defaultValue: "您今日的高级语音转写次数已用完",
          });

          // 优先使用实时状态
          if (realtimeWhisperLimitStatus) {
            const { currentUsage, dailyLimit } = realtimeWhisperLimitStatus;
            desc +=
              "\n\n" +
              t("home.whisperLimit.usageStats", {
                used: currentUsage,
                limit: dailyLimit,
                defaultValue: `今日已使用 ${currentUsage}/${dailyLimit} 次`,
              });
          } else if (whisperLimitError?.usageInfo) {
            // fallback到whisperLimitError
            const usageInfo = whisperLimitError.usageInfo;
            const used = usageInfo.todayUsage || 0;
            const limit = dailyWhisperLimit;
            desc +=
              "\n\n" +
              t("home.whisperLimit.usageStats", {
                used,
                limit,
                defaultValue: `今日已使用 ${used}/${limit} 次`,
              });
          }

          return desc;
        })()}
      />

      {/* 权限引导Modal */}
      <PermissionGuideModal
        visible={showPermissionGuide}
        onClose={hidePermissionModal}
        onPermissionGranted={onPermissionGranted}
        permissionType={permissionGuideType}
      />

      {/* 登录提示弹窗 */}
      <LoginPrompt
        visible={loginPromptVisible}
        onClose={hideLoginPrompt}
        title={loginPromptTitle}
        message={loginPromptMessage}
        onLogin={handleLoginSuccess}
      />
      {/* 首次登录引导弹窗 */}
      <LoginGuideModal
        visible={showLoginGuide}
        onLogin={handleLoginGuideLogin}
        onSkip={handleLoginGuideSkip}
        animationSource={require("@/assets/animations/transcribing.json")}
      />

      {/* 模板下拉菜单 */}
      <TemplateDropdownMenu
        selectedTemplate={selectedTemplate ? (() => {
          // 从allTemplates中找到完整的模板信息
          const fullTemplate = allTemplates.find(t => t.id === selectedTemplate.id);
          return fullTemplate || null;
        })() : null}
        templates={allTemplates}
        onSelectTemplate={handleTemplateDropdownSelect}
        visible={templateDropdownVisible}
        onClose={() => setTemplateDropdownVisible(false)}
        isDark={isDark}
        buttonPosition={templateButtonPosition}
      />
    </ThemedSafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    minHeight: 58,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  recordingStatus: {
    flexDirection: "row",
    alignItems: "center",
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  recordingTime: {
    fontSize: 14,
    color: "#666666", // 默认文字颜色
  },
  darkRecordingTime: {
    color: "#BBBBBB", // 暗色模式文字颜色
  },
  addTemplateButton: {
    padding: 5,
  },
  templateIndicator: {
    fontSize: 14,
    maxWidth: 120, // 设置最大宽度避免文字过长
    textAlign: "right", // 右对齐显示
  },
  templateDropdownButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 6,
    backgroundColor: "transparent",
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  emptyStateContainer: {
    flex: 1,
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
    paddingTop: "5%", // 向下移动一些，留出更多底部空间
    paddingBottom: "5%",
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: "500",
    marginBottom: 10,
    color: "#000000", // 默认浅色模式颜色
  },
  emptyStateSubtitle: {
    fontSize: 14,
    marginBottom: 25,
    textAlign: "center",
    lineHeight: 20,
    maxWidth: "80%", // 限制文本宽度，提高可读性
    color: "#666666", // 默认浅色模式颜色
  },
  // 深色模式下的文本颜色
  darkEmptyStateTitle: {
    color: "#FFFFFF", // 深色模式下的文字颜色
  },
  darkEmptyStateSubtitle: {
    color: "#9BA1A6", // 深色模式下的副标题颜色
  },
  // 录音状态样式
  recordingContainer: {
    flex: 1,
  },
  // 可滚动的文本区域
  recognizedTextScroll: {
    flex: 1,
    maxHeight: "100%",
  },
  recognizedTextContainer: {
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 20,
    marginTop: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  recognizedText: {
    fontSize: 16,
    lineHeight: 24,
  },
  // 录音控制区整体容器
  recordingControlsContainer: {
    width: "100%",
    alignItems: "center",
  },
  // 波形动画容器 - 调整位置到底部按钮上方
  voiceWaveContainer: {
    marginBottom: 20,
    height: 60,
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
  // 选择风格页面整体布局
  templatePageContainer: {
    flexDirection: "column",
    justifyContent: "flex-start",
    paddingBottom: 20,
    minHeight: "100%", // 确保最小高度占满整个屏幕
  },
  // 文本显示区域固定高度，不用过高
  templateTextContainer: {
    minHeight: 200, // 最小高度改为200
    maxHeight: 300, // 最大高度限制
    paddingVertical: 10,
  },
  // 模板选择样式
  templatesSection: {
    padding: 20,
    paddingTop: 10,
    paddingBottom: 0,
  },
  templatesSectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
    marginTop: 5,
  },
  templatesSectionTitle: {
    fontSize: 14,
    fontWeight: "500",
    flex: 1,
  },
  templateManageButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: "#F3F4F6",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  templateManageButtonDark: {
    backgroundColor: "#374151",
    borderColor: "#4B5563",
  },
  templateManageButtonText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#6B7280",
    marginLeft: 4,
  },
  // 网格布局 - 两行三列
  templatesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "flex-start", // 改为从左开始排列
    marginBottom: 20,
  },
  // 新增：所有模板的水平滚动列表
  allTemplatesList: {
    paddingTop: 6, // 为角标留出向上突出的空间
    paddingBottom: 20,
    paddingRight: 20,
  },
  scrollTemplateItem: {
    width: 130,
    marginRight: 10,
    marginTop: 6, // 为角标留出向上突出的空间
  },
  templateGridItem: {
    width: "32%",
    marginBottom: 12, // 增加底部间距
    height: 80, // 调整为新的卡片高度
    marginRight: "2%", // 添加右边距
    marginTop: 6, // 为角标留出向上突出的空间
  },
  // 特殊处理第3、6个元素
  templateGridItemNoMargin: {
    width: "32%",
    marginBottom: 12,
    height: 80, // 调整为新的卡片高度
    marginRight: 0, // 第3、6个元素没有右边距
    marginTop: 6, // 为角标留出向上突出的空间
  },
  // 固定尺寸的模板卡片
  templateCardFixed: {
    height: 80, // 调整为新的卡片高度
    width: "100%", // 宽度占满容器
  },
  // 结果展示样式
  resultContainer: {
    flex: 1,
    padding: 20,
    paddingBottom: 150, // 为底部固定按钮留出空间
  },
  optimizedTextContainer: {
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: "#eee",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    flex: 1, // 使用flex: 1替代minHeight
    backgroundColor: "#ffffff", // 确保背景色为白色
  },
  optimizedTextContainerDark: {
    borderColor: "#2B2F31",
    shadowOpacity: 0.2,
    backgroundColor: "#1E2122", // 统一为1E2122，与主体深色模式保持一致
  },
  optimizedTextScroll: {
    flex: 1,
  },
  optimizedText: {
    fontSize: 16,
    lineHeight: 24,
    color: "#333333", // 默认文字颜色
  },
  darkOptimizedText: {
    color: "#E0E0E0", // 暗色模式文字颜色
  },
  // 底部固定的结果页面按钮区域
  fixedResultFooter: {
    position: "absolute",
    bottom: 30, // 增加底部距离，让按钮不会贴边
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingTop: 15,
    paddingBottom: 15, // 减少底部内边距
    zIndex: 100,
  },
  fixedResultFooterDark: {
    borderTopColor: "#2B2F31",
    borderTopWidth: 1,
  },
  resultActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 15,
    paddingHorizontal: 0,
    gap: 12, // 添加按钮之间的间距
  },
  resultActionButton: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#6a5ae1",
    borderRadius: 12,
    paddingVertical: 14, // 统一为14，与optimizeButton保持一致
  },
  resultActionText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 8,
  },
  newInputButton: {
    backgroundColor: "#f5f5f5",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 12,
    paddingVertical: 14, // 统一为14，与optimizeButton保持一致
    alignItems: "center",
  },
  newInputButtonDark: {
    backgroundColor: "#1E2122", // 统一深色背景色
    borderColor: "#2B2F31",
  },
  newInputButtonText: {
    fontWeight: "500",
  },
  newInputButtonTextDark: {
    color: "#ECEDEE",
  },
  // 操作区样式
  actionArea: {
    flex: 0.3,
    paddingHorizontal: 20,
    paddingTop: 15,
    paddingBottom: 20, // 增加底部内边距
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "transparent",
  },
  // 初始状态的操作区域 - 修改为上下居中
  actionAreaInitial: {
    flex: 0.35,
    paddingHorizontal: 20,
    alignItems: "center",
    justifyContent: "center", // 改为center，让录音按钮上下居中
    backgroundColor: "transparent",
  },
  // 语音按钮 - 更接近原型
  voiceBtn: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: "#6a5ae1",
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#6a5ae1",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  voiceBtnInner: {
    width: "100%",
    height: "100%",
    borderRadius: 35,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#6a5ae1",
  },
  recordingControls: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  pauseButton: {
    flex: 1,
    marginRight: 10,
  },
  finishButton: {
    flex: 1,
    marginLeft: 10,
  },
  secondaryButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#6a5ae1",
    borderRadius: 20,
    paddingVertical: 14, // 增加垂直内边距，确保按钮高度足够
    paddingHorizontal: 20,
    minHeight: 48, // 添加最小高度，确保按钮不会太扁
  },
  primaryButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#6a5ae1",
    borderRadius: 20,
    paddingVertical: 14, // 增加垂直内边距，确保按钮高度足够
    paddingHorizontal: 20,
    minHeight: 48, // 添加最小高度，确保按钮不会太扁
  },
  controlButtonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 6,
    fontSize: 14, // 确保文字大小合适
  },
  bodyContainer: {
    flex: 1,
    flexDirection: "column",
  },
  mainContentArea: {
    flex: 1, // 占据70%高度
    margin: 15,
    marginBottom: 8,
    borderRadius: 16,
    overflow: "hidden",
  },
  mainContentAreaDark: {
    backgroundColor: "#1E2122",
  },
  // 初始状态的内容区域，需要预留更多空间给操作区
  mainContentAreaInitial: {
    flex: 0.7, // 初始状态稍微小一点，给操作区更多空间
  },
  // 其他状态下的透明背景区域
  transparentContentArea: {
    backgroundColor: "transparent", // 透明背景
  },
  // 处理全屏模式下的滚动区域
  fullPageContentArea: {
    flex: 1, // 在模板和结果页面占据更多空间，因为它们有固定底部按钮
    margin: 0,
    borderRadius: 0,
  },
  // 底部固定的按钮区域（常驻）
  fixedTemplateFooter: {
    position: "absolute",
    bottom: 0, // 这个值会被上面的动态设置覆盖
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingBottom: 20, // 减小底部内边距，因为已经通过bottom属性上移
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
    zIndex: 100,
  },
  fixedTemplateFooterDark: {
    borderTopColor: "#2B2F31",
  },
  optimizeButton: {
    backgroundColor: "#6a5ae1",
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: "center",
  },
  optimizeButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 16,
  },
  editableText: {
    minHeight: 150,
    maxHeight: 400,
    textAlignVertical: "top",
    padding: 5,
  },
  editingHint: {
    fontSize: 12,
    color: "#9CA3AF",
    textAlign: "center",
    marginTop: 6,
  },
  // 加载中状态样式
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  aiModelInfoContainer: {
    marginBottom: 16,
    alignItems: "flex-start",
    width: "100%",
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  modelBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  modelBadgeDark: {
    backgroundColor: "#1E2122", // 统一深色背景色
    borderColor: "#2B2F31",
  },
  modelName: {
    fontSize: 13,
    fontWeight: "500",
    marginLeft: 5,
    color: "#6B7280",
  },
  loadingAnimationContainer: {
    width: "60%",
    aspectRatio: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  vipUpgradeContainer: {
    marginTop: 30,
    alignItems: "center",
    justifyContent: "center",
    padding: 10,
  },
  vipUpgradeText: {
    fontSize: 14,
    marginBottom: 12,
    color: "#6B7280",
    textAlign: "center",
  },
  vipUpgradeButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#6a5ae1",
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    width: "80%",
  },
  vipUpgradeButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 14,
    marginLeft: 6,
  },
  smallVipButton: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 6,
  },
  smallVipButtonDark: {
    // 暗色模式下的样式
  },
  smallVipButtonText: {
    fontSize: 12,
    color: "#6a5ae1",
    marginLeft: 4,
    fontWeight: "500",
  },
  // 重新生成按钮样式
  regenerateButton: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 6,
    backgroundColor: "#f5f5f5",
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  regenerateButtonDark: {
    backgroundColor: "#1E2122", // 统一深色背景色
  },
  regenerateButtonText: {
    fontSize: 12,
    color: "#6a5ae1",
    marginLeft: 4,
    fontWeight: "500",
  },
  // 在结果页中的VIP升级容器
  resultVipUpgradeContainer: {
    marginTop: 24,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
    paddingTop: 16,
    alignItems: "center",
  },
  resultVipUpgradeButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#6a5ae1",
    borderRadius: 16,
    paddingVertical: 14, // 与其他按钮保持一致的高度
    paddingHorizontal: 16,
    width: "80%",
  },
  loadingText: {
    fontSize: 14,
    color: "#6B7280",
    marginTop: 16,
    fontWeight: "500",
    textAlign: "center",
  },
  loadingAnimationWrapper: {
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
  },
  vipServiceBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginVertical: 10,
  },
  vipServiceText: {
    marginLeft: 8,
    fontSize: 15,
    fontWeight: "600",
  },
  vipServiceBadgeInResult: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
  },
  vipServiceTextInResult: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: "600",
  },
  badgeContainer: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginVertical: 10,
  },
  loadingVipHintContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
  },
  loadingVipHintText: {
    fontSize: 13,
    color: "#6B7280",
    marginLeft: 8,
    fontWeight: "500",
  },
  // 转写中状态的文本容器
  transcribingTextContainer: {
    width: "100%",
    padding: 20,
    borderRadius: 12,
    marginVertical: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  // 转写中状态的文本样式
  transcribingText: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    fontWeight: "500",
  },
  // // 语音波形图容器
  // voiceWaveContainer: {
  //   width: "100%",
  //   alignItems: "center",
  //   justifyContent: "center",
  //   marginTop: 20,
  // },
  // 转写进度条容器
  progressBarContainer: {
    width: "100%",
    height: 4,
    backgroundColor: "#E5E7EB",
    borderRadius: 2,
    overflow: "hidden",
    marginVertical: 12,
  },
  // 转写进度条
  progressBar: {
    height: "100%",
    backgroundColor: "#6a5ae1",
    borderRadius: 2,
  },
  // 新的语音输入按钮
  newRecordingButton: {
    width: "100%",
    height: 50,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    backgroundColor: "white",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  // 新的语音输入按钮深色模式
  newRecordingButtonDark: {
    backgroundColor: "#1E2122", // 统一深色背景色
    borderColor: "#2B2F31",
  },
  // 新的语音输入按钮文本
  newRecordingButtonText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
  },
  singleButton: {
    flex: 1,
  },
  // 网络错误相关样式
  networkErrorContainer: {
    backgroundColor: "#FEF2F2",
    borderWidth: 1,
    borderColor: "#FECACA",
    borderRadius: 12,
    padding: 16,
    margin: 20,
  },
  networkErrorContainerDark: {
    backgroundColor: "#2B1F1F",
    borderColor: "#451A1A",
  },
  networkErrorContent: {
    alignItems: "center",
    justifyContent: "center",
  },
  networkErrorContentDark: {
    // 暗色模式样式已在容器中定义
  },
  networkErrorIcon: {
    marginBottom: 12,
  },
  networkErrorTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#DC2626",
    textAlign: "center",
    marginBottom: 8,
  },
  networkErrorTitleDark: {
    color: "#F87171",
  },
  networkErrorMessage: {
    fontSize: 14,
    color: "#6B7280",
    textAlign: "center",
    marginBottom: 16,
    lineHeight: 20,
  },
  networkErrorMessageDark: {
    color: "#9CA3AF",
  },
  networkErrorTips: {
    alignSelf: "stretch",
    marginBottom: 20,
  },
  networkErrorTip: {
    fontSize: 13,
    color: "#6B7280",
    lineHeight: 18,
    marginBottom: 4,
  },
  networkErrorTipDark: {
    color: "#9CA3AF",
  },
  retryButton: {
    backgroundColor: "#6a5ae1",
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginHorizontal: 5,
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
  },
  retryButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "600",
  },
  rerecordButton: {
    backgroundColor: "#E5E7EB",
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginHorizontal: 5,
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
  },
  rerecordButtonDark: {
    backgroundColor: "#374151",
  },
  rerecordButtonText: {
    color: "#374151",
    fontSize: 14,
    fontWeight: "600",
  },
  rerecordButtonTextDark: {
    color: "#D1D5DB",
  },
  networkErrorButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  // 英文模式下的垂直按钮布局
  networkErrorButtonsVertical: {
    flexDirection: "column",
    gap: 12,
    alignItems: "stretch",
  },
  // 英文模式下的重试按钮样式
  retryButtonVertical: {
    marginHorizontal: 0,
    marginBottom: 0,
    flex: 0,
  },
  // 英文模式下的重新录音按钮样式
  rerecordButtonVertical: {
    marginHorizontal: 0,
    marginTop: 0,
    flex: 0,
  },
  // transcribing状态的按钮样式
  transcribingButton: {
    // 继承primaryButton的所有样式，只是颜色不同
  },
  transcribingButtonText: {
    color: "#FFFFFF",
  },
  transcribingButtonTextDark: {
    color: "#D1D5DB",
  },
  transcribingHintText: {
    fontSize: 12,
    color: "#6B7280",
    textAlign: "center",
    marginBottom: 12,
  },
  transcribingHintTextDark: {
    color: "#9CA3AF",
  },
  // 次要操作按钮样式
  secondaryActionButton: {
    backgroundColor: "#F3F4F6",
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  secondaryActionButtonDark: {
    backgroundColor: "#374151",
    borderColor: "#4B5563",
  },
  secondaryActionText: {
    color: "#6B7280",
  },
  secondaryActionTextDark: {
    color: "#9CA3AF",
  },
  // 禁用状态的按钮样式
  disabledActionButton: {
    backgroundColor: "#F9FAFB",
    borderColor: "#F3F4F6",
    opacity: 0.6,
  },
  disabledActionText: {
    color: "#D1D5DB",
  },
  // 主要新输入按钮样式
  primaryNewInputButton: {
    backgroundColor: "#6a5ae1",
    borderWidth: 0,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 14, // 添加垂直内边距，与其他按钮保持一致
  },
  primaryNewInputButtonDark: {
    backgroundColor: "#6a5ae1",
  },
  primaryNewInputButtonText: {
    color: "white",
    marginLeft: 8,
  },
  // 转录状态指示器样式
  transcribingIndicator: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "transparent",
    width: "100%",
  },
  transcribingIconContainer: {
    marginRight: 8,
  },
  transcribingStatusText: {
    fontSize: 16,
    color: "#6B7280",
    fontWeight: "500",
  },
  transcribingStatusTextDark: {
    color: "#D1D5DB",
  },
});
