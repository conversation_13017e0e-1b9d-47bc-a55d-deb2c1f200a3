import { Stack } from 'expo-router';
import { Platform, DeviceEventEmitter } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { AUDIO_EVENTS } from '@/hooks/useAudioPlayer';
import { useEffect } from 'react';
import { useNavigation, useIsFocused } from '@react-navigation/native';
import { log } from '@/services/logService';

/**
 * 历史页面内嵌的Stack导航
 * 用于在历史记录列表和详情页面之间导航
 */
export default function HistoryLayout() {
  const theme = useAppTheme();
  const backgroundColor = theme === 'dark' ? '#151718' : '#f9fafb';
  const navigation = useNavigation();
  
  // 监听页面导航，在页面切换时停止音频播放
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      log('历史记录页面导航切换，发送停止音频播放事件');
      DeviceEventEmitter.emit(AUDIO_EVENTS.APP_TAB_CHANGE);
    });
    
    return unsubscribe;
  }, [navigation]);
  
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        // 自定义页面过渡效果
        animation: 'slide_from_right',
        // 所有页面使用card导航样式
        presentation: 'card',
        contentStyle: { backgroundColor },
      }}>
      <Stack.Screen 
        name="index" 
      />
      <Stack.Screen 
        name="detail" 
        options={{
          // 详情页面设置
          presentation: 'card',
          animationDuration: 200,
        }} 
      />
    </Stack>
  );
} 