import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  StyleSheet,
  TouchableOpacity,
  Pressable,
  Share,
  Alert,
  Clipboard,
  Modal,
  PanResponder,
  GestureResponderEvent,
  PanResponderGestureState,
  Dimensions,
  ActivityIndicator,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Text, View, FlatList } from "react-native";
import { StatusBar } from "expo-status-bar";
import { Animated as RNAnimated, Easing } from "react-native";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { router } from "expo-router";
import {
  Swipeable,
  GestureHandlerRootView,
  ScrollView,
} from "react-native-gesture-handler";
import { useAppTheme } from "@/hooks/useAppTheme";
import {
  ThemedView,
  ThemedText,
  ThemedScrollView,
  ThemedSafeAreaView,
  getThemedColor,
} from "@/components/withTheme";
import { useHistory } from "@/hooks/useHistory";
import { useAudioPlayer } from "@/hooks/useAudioPlayer";
import {
  HistoryRecord,
  HistoryRecordWithUI,
  HistoryGroup,
} from "@/services/storageService";
import * as HistoryService from "@/services/storageService";
import { formatDate } from "@/utils/helpers";
import { useFocusEffect } from "@react-navigation/native";
import { getVipStatus, getUserSettings } from "@/services/storageService";
import { RefreshControl } from "react-native";
import {
  authenticateWithSession,
  shouldRequireAuthentication,
  initializeAppStateListener,
  cleanupAppStateListener,
  clearAuthenticationSession
} from "@/services/biometricService";
import Toast from "react-native-toast-message";
import {
  showCenteredToast,
  registerBadgeShowFunction,
  showCopyBadge,
} from "@/utils/toastConfig";
import CopyBadge from "@/components/ui/CopyBadge";
import VipUpgradeCard from "@/components/ui/VipUpgradeCard";
import { DeviceEventEmitter } from "react-native";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import * as Haptics from 'expo-haptics';
import { RootState } from "@/store";
import { ShareService } from '@/services/shareService';
import { LoginPrompt } from '@/components/LoginPrompt';
import { useAuthModal } from '@/components/AuthProvider';
import { log, error as logError , warn, debug, info } from "@/services/logService";
import { getTemplateSemanticId, semanticIdToI18nKey } from "@/utils/templateI18nUtils";
import { getAudioFileAbsolutePath } from '@/utils/inputUtils';

/**
 * 安全地调用触觉反馈，在web平台上会自动跳过
 */
const safeHaptics = {
  impactAsync: async (style: Haptics.ImpactFeedbackStyle) => {
    if (Platform.OS !== "web") {
      try {
        await Haptics.impactAsync(style);
      } catch (error) {
        console.log("触觉反馈失败，但不影响功能", error);
      }
    }
  },
  notificationAsync: async (type: Haptics.NotificationFeedbackType) => {
    if (Platform.OS !== "web") {
      try {
        await Haptics.notificationAsync(type);
      } catch (error) {
        console.log("触觉反馈失败，但不影响功能", error);
      }
    }
  },
};

interface HistoryItem {
  id: string;
  title: string;
  time: string;
  date: string;
  tags: string[];
  audioProgress: number;
  originalText: string;
  optimizedText: string;
  dateGroup: string;
  expanded?: boolean;
  audioUri?: string; // 音频文件URI
  isSecondaryOptimization?: boolean; // 是否为二次优化记录
  sourceRecordId?: string; // 源记录ID，仅二次优化记录有此字段
}

interface TimeStampedText {
  text: string;
  timestamp: string;
}

// 定义AudioPlayer组件属性类型
interface AudioPlayerProps {
  progress: number;
  itemId?: string;
  small: boolean;
  currentItem: HistoryItem | null | undefined;
  isCurrentlyPlaying: boolean;
  isPlaying: boolean;
  onTogglePlay: (item: HistoryItem) => Promise<void>;
  onSeek: (percent: number, itemId: string) => Promise<void>;
  getColor: (lightColor: string, darkColor: string) => string;
  disabled?: boolean;
}

// 将音频播放器提取为独立组件
const AudioPlayer = React.memo(
  ({
    progress,
    itemId,
    small = false,
    currentItem,
    isCurrentlyPlaying,
    isPlaying,
    onTogglePlay,
    onSeek,
    getColor,
    disabled = false,
  }: AudioPlayerProps) => {
    // 没有音频的项目显示禁用状态
    const hasAudio = true; // 修改为总是显示可播放状态，便于测试
    const shouldShowPlaying = isCurrentlyPlaying && isPlaying;

    // 为播放器创建拖动状态
    const [isDragging, setIsDragging] = useState(false);
    const [sliderWidth, setSliderWidth] = useState(0);
    const [dragProgress, setDragProgress] = useState(
      isCurrentlyPlaying ? progress : 0
    );
    const [sliderPosition, setSliderPosition] = useState(0);

    // 使用ref来跟踪之前的值，避免不必要的更新
    const lastProgressRef = useRef(progress);
    const lastUpdateTimeRef = useRef(0);

    // 当进度更新且不在拖动状态时，同步UI
    useEffect(() => {
      if (!isDragging && isCurrentlyPlaying) {
        const now = Date.now();
        // 限制更新频率，最多每1000ms更新一次UI
        if (now - lastUpdateTimeRef.current < 1000) {
          return;
        }
        
        // 只有当progress变化超过2%时才更新，避免频繁的微小更新
        if (Math.abs(progress - lastProgressRef.current) > 2) {
          setDragProgress(progress);
          lastProgressRef.current = progress;
          lastUpdateTimeRef.current = now;
          
          // 进一步减少console.log的频率
          if (progress % 20 < 2) { // 只在20%的倍数附近输出日志
            log(
              `AudioPlayer progress updated: ${progress}%, item: ${itemId}`
            );
          }
        }
      }
    }, [progress, isDragging, isCurrentlyPlaying]); // 移除dragProgress依赖项避免循环

    // 创建手势响应器
    const panResponder = React.useMemo(
      () =>
        PanResponder.create({
          onStartShouldSetPanResponder: () => true, // 允许所有项目响应手势
          onMoveShouldSetPanResponder: () => true, // 允许所有项目响应手势
          onPanResponderGrant: (e) => {
            setIsDragging(true);
            // 更新初始位置
            const touchX = e.nativeEvent.locationX;
            const progressPos = Math.max(
              0,
              Math.min(100, (touchX / sliderWidth) * 100)
            );
            setDragProgress(progressPos);
          },
          onPanResponderMove: (e, gestureState) => {
            if (sliderWidth === 0) return;

            // 计算手指相对于滑块容器的位置
            const touchX = gestureState.moveX - sliderPosition;
            // 限制在0-100%范围内
            const newProgress = Math.max(
              0,
              Math.min(100, (touchX / sliderWidth) * 100)
            );
            setDragProgress(newProgress);
          },
          onPanResponderRelease: (e, gestureState) => {
            if (sliderWidth === 0 || !itemId) return;

            // 计算最终位置
            const touchX = gestureState.moveX - sliderPosition;
            const newProgress = Math.max(
              0,
              Math.min(100, (touchX / sliderWidth) * 100)
            );

            // 更新音频位置
            if (isCurrentlyPlaying) {
              onSeek(newProgress, itemId);
            } else if (currentItem) {
              // 如果不是当前播放的，先加载并播放，然后设置位置
              onTogglePlay(currentItem).then(() => {
                setTimeout(() => onSeek(newProgress, itemId), 300);
              });
            }

            setIsDragging(false);
          },
          onPanResponderTerminate: () => {
            setIsDragging(false);
          },
        }),
      [
        sliderWidth,
        sliderPosition,
        isCurrentlyPlaying,
        itemId,
        currentItem,
        onSeek,
        onTogglePlay,
      ]
    );

    return (
      <View style={small ? styles.audioPlayerSmall : styles.audioPlayer}>
        <TouchableOpacity
          style={styles.playButton}
          onPress={() => {
            if (disabled) return;
            if (!itemId || !currentItem) return;
            onTogglePlay(currentItem);
          }}
          activeOpacity={0.7}
        >
          <IconSymbol
            name={shouldShowPlaying ? "pause.fill" : "play.fill"}
            size={small ? 8 : 10}
            color="white"
          />
        </TouchableOpacity>
        <View
          style={styles.progressBarContainer}
          onLayout={(e) => {
            setSliderWidth(e.nativeEvent.layout.width);
            // 保存滑块容器在屏幕上的绝对位置
            e.target.measure((x, y, width, height, pageX, pageY) => {
              setSliderPosition(pageX);
            });
          }}
        >
          <View
            style={styles.sliderContainer}
            {...(disabled ? {} : panResponder.panHandlers)}
          >
            <View
              style={[
                styles.progressBar,
                { backgroundColor: getColor("#E5E7EB", "#26292B") },
              ]}
            >
              <View
                style={[
                  styles.progress,
                  {
                    width: `${
                      isDragging
                        ? dragProgress
                        : isCurrentlyPlaying
                        ? progress
                        : 0
                    }%`,
                    backgroundColor: "#6a5ae1",
                  },
                ]}
              />
              <View
                style={[
                  styles.progressThumb,
                  {
                    left: `${
                      isDragging
                        ? dragProgress
                        : isCurrentlyPlaying
                        ? progress
                        : 0
                    }%`,
                    // 只有当前播放的项目或拖动时才显示滑块
                    opacity: isDragging || isCurrentlyPlaying ? 1 : 0,
                  },
                ]}
              />
            </View>
          </View>
        </View>
      </View>
    );
  }
);

/**
 * 历史记录页面
 * 显示用户过去的语音输入及AI优化后的文本记录
 */
export default function HistoryScreen() {
  // 获取底部安全区域高度
  const insets = useSafeAreaInsets();

  // 获取当前主题
  const theme = useAppTheme();
  const isDark = theme === "dark";

  // 获取翻译函数
  const { t } = useTranslation();

  // 获取模板显示名称
  const getTemplateDisplayName = useCallback((templateName: string): string => {
    if (templateName === 'direct_transcription') {
      const translation = t('template.direct_transcription.name');
      return typeof translation === 'string' ? translation : (translation as any)?.name || 'Direct Transcription';
    }
    if (templateName === "全部") {
      return t("history.all" as const);
    }
    const semanticId = getTemplateSemanticId(templateName);
    if (semanticId) {
      const i18nKey = semanticIdToI18nKey[semanticId];
      if (i18nKey) {
        const nameKey = `template.${i18nKey}.name` as const;
        const translatedValue = t(nameKey);
        if (typeof translatedValue === 'string' && translatedValue !== nameKey) {
          return translatedValue;
        }
      }
    }
    return templateName;
  }, [t]);

  // 从Redux获取用户状态
  const { user, isAuthenticated, isVIP } = useSelector((state: RootState) => state.auth);

  // 获取AuthModal相关方法
  const { showLoginModal } = useAuthModal();

  // 添加VIP升级待处理状态
  const [pendingVipUpgrade, setPendingVipUpgrade] = useState(false);
  
  // 添加登录提示相关状态
  const [loginPromptVisible, setLoginPromptVisible] = useState(false);
  const [loginPromptTitle, setLoginPromptTitle] = useState("");
  const [loginPromptMessage, setLoginPromptMessage] = useState("");

  // 使用历史记录钩子获取真实数据
  const {
    groupedRecords,
    loading,
    error,
    loadGroupedRecords,
    getAllTemplateNames,
    loadGroupedRecordsByTemplate,
    getVisibleRecordsCountByTemplate,
    getRealRecordsCountByTemplate,
    deleteRecord,
    getLastUpdatedTimestamp,
    getHiddenRecordsCountByTemplate,
  } = useHistory();

  // 增加一个专门用于UI展示的数据数组
  const [displayGroupedRecords, setDisplayGroupedRecords] = useState<
    HistoryGroup[]
  >([]);

  // 增加分页加载相关的状态
  const [loadedCount, setLoadedCount] = useState(15); // 初始加载15条
  const [hasMore, setHasMore] = useState(true); // 是否还有更多数据
  const [isLoadingMore, setIsLoadingMore] = useState(false); // 是否正在加载更多
  const [initialLoadDone, setInitialLoadDone] = useState(false); // 是否完成初始加载
  const [lastDataTimestamp, setLastDataTimestamp] = useState<number>(0); // 上次数据时间戳

  // 免费用户最大记录数限制
  const freeUserMaxRecords = 50;

  // 标签筛选相关状态
  const [availableTemplateNames, setAvailableTemplateNames] = useState<string[]>([]);
  const [templateNamesLoaded, setTemplateNamesLoaded] = useState(false);
  const [currentSelectedTemplate, setCurrentSelectedTemplate] = useState<string>("全部");

  // 增加一个用于存储数据库中总记录数的状态
  const [totalRecordsCount, setTotalRecordsCount] = useState<number>(0);
  
  // 存储真实记录总数（不受VIP限制）
  const [realTotalRecordsCount, setRealTotalRecordsCount] = useState<number>(0);

  // 当前筛选模板的记录统计
  const [currentTemplateVisibleCount, setCurrentTemplateVisibleCount] = useState<number>(0);
  const [currentTemplateRealCount, setCurrentTemplateRealCount] = useState<number>(0);
  const [currentTemplateHiddenCount, setCurrentTemplateHiddenCount] = useState<number>(0);

  // 音频播放钩子
  const {
    isPlaying,
    progress,
    loadAudio,
    togglePlay,
    seekTo,
    audioUri,
    duration,
    position,
    formatTime,
    error: audioPlayerError,
    sound,
    setIsPlaying,
    stopPlayback,
  } = useAudioPlayer();

  // 当前播放记录ID引用
  const playingItemIdRef = useRef<string | null>(null);

  // 存储每个记录的音频信息
  const [audioInfoMap, setAudioInfoMap] = useState<{
    [key: string]: { duration: number; displayDuration: string };
  }>({});

  // 增加一个记忆化处理过的进度对象，避免直接使用原始progress造成无限更新
  const memoizedProgress = useRef(0);

  // 修改聚焦效果处理，使用UI专用数组
  const [refreshing, setRefreshing] = useState(false);

  // 生物识别认证相关状态
  const [isBiometricAuthenticating, setIsBiometricAuthenticating] = useState(false);
  const [isBiometricAuthenticated, setIsBiometricAuthenticated] = useState(false);
  const [biometricAuthRequired, setBiometricAuthRequired] = useState(false);
  const [authCheckCompleted, setAuthCheckCompleted] = useState(false);
  const [hasSuccessfulAuth, setHasSuccessfulAuth] = useState(false); // 标记是否已经成功认证过

  // 状态管理
  const [activeTab, setActiveTab] = useState("全部");
  const [expandedItems, setExpandedItems] = useState<{
    [key: string]: boolean;
  }>({});

  // 更新activeTab状态与currentSelectedTemplate保持同步
  useEffect(() => {
    setActiveTab(currentSelectedTemplate);
  }, [currentSelectedTemplate]);

  const [showQuickActions, setShowQuickActions] = useState(false);
  const [actionItemId, setActionItemId] = useState<string | null>(null);
  const [actionButtonPosition, setActionButtonPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);

  // 批量删除相关状态
  const [isSelectMode, setIsSelectMode] = useState(false);
  const [selectedItems, setSelectedItems] = useState<{
    [key: string]: boolean;
  }>({});
  const [selectedCount, setSelectedCount] = useState(0);

  // 添加打开状态跟踪
  const [openItems, setOpenItems] = useState<{
    [key: string]: boolean;
  }>({});

  // 创建引用来跟踪当前打开的Swipeable和所有Swipeable实例
  const openedSwipeableRef = React.useRef<string | null>(null);
  const swipeableRefs = React.useRef<{ [key: string]: any }>({});
  // 添加滚动节流控制
  const scrollThrottleTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
  const lastScrollTimeRef = React.useRef<number>(0);

  // 添加badge显示状态
  // 删除本地Badge状态和注册逻辑
  // 删除如下代码:
  // const [showCopyBadgeState, setShowCopyBadgeState] = useState(false);
  // useEffect(() => {
  //   registerBadgeShowFunction(setShowCopyBadgeState);
  //   return () => {
  //     registerBadgeShowFunction(() => {});
  //   };
  // }, []);

  // 删除页面布局中的CopyBadge组件
  // 删除如下代码:
  // {/* CopyBadge 组件 - 调整位置更加明显 */}
  // <CopyBadge
  //   visible={showCopyBadgeState}
  //   onHide={() => setShowCopyBadgeState(false)}
  // />

  // 初始化应用生命周期监听器
  useEffect(() => {
    initializeAppStateListener();

    return () => {
      cleanupAppStateListener();
    };
  }, []);

  // 在组件加载时获取历史记录数据，只加载初始数据
  useEffect(() => {
    if (!initialLoadDone) {
      loadInitialData();
    }
  }, []);

  // 初始化时检查认证状态
  useEffect(() => {
    const initAuthCheck = async () => {
      const needsAuth = await shouldRequireAuthentication();
      if (!needsAuth) {
        // 不需要认证，直接标记为已认证和检查完成
        setIsBiometricAuthenticated(true);
        setBiometricAuthRequired(false);
        setAuthCheckCompleted(true);
        setHasSuccessfulAuth(true); // 标记已成功认证
      } else {
        // 需要认证，设置相应状态，但不显示加载界面
        setIsBiometricAuthenticated(false);
        setBiometricAuthRequired(true);
        setAuthCheckCompleted(true);
        setHasSuccessfulAuth(false); // 还未认证
      }
    };

    initAuthCheck();
  }, []);

  // 检查并执行生物识别认证（使用智能会话管理）
  const checkAndPerformBiometricAuth = useCallback(async (forceAuth: boolean = false): Promise<boolean> => {
    let authTimeout: NodeJS.Timeout | null = null;

    try {
      // 检查是否需要认证
      const needsAuth = await shouldRequireAuthentication(forceAuth);
      if (!needsAuth) {
        log('[History] 无需认证，使用现有会话');
        setIsBiometricAuthenticated(true);
        setBiometricAuthRequired(false);
        return true;
      }

      log('[History] 需要进行生物识别认证');
      setBiometricAuthRequired(true);
      setIsBiometricAuthenticating(true);

      // 设置认证超时（30秒）
      const authPromise = new Promise<boolean>((resolve, reject) => {
        authTimeout = setTimeout(() => {
          reject(new Error('认证超时，请重试'));
        }, 30000);

        // 执行生物识别认证（带会话管理）
        authenticateWithSession(
          '请验证您的身份以访问历史记录',
          '使用密码',
          forceAuth
        ).then((authResult) => {
          if (authTimeout) {
            clearTimeout(authTimeout);
            authTimeout = null;
          }

          if (authResult.success) {
            resolve(true);
          } else {
            reject(new Error(authResult.error || '认证失败'));
          }
        }).catch((error) => {
          if (authTimeout) {
            clearTimeout(authTimeout);
            authTimeout = null;
          }
          reject(error);
        });
      });

      const success = await authPromise;

      if (success) {
        log('[History] 生物识别认证成功');
        setIsBiometricAuthenticated(true);
        setBiometricAuthRequired(false);
        setHasSuccessfulAuth(true); // 标记已成功认证

        // 认证成功后立即刷新数据
        setTimeout(() => {
          refreshAllHistoryData();
        }, 100);

        return true;
      }

      return false;
    } catch (error) {
      // 清除超时定时器
      if (authTimeout) {
        clearTimeout(authTimeout);
      }

      logError('[History] 生物识别认证异常:', error);
      setIsBiometricAuthenticated(false);
      setBiometricAuthRequired(true);

      // 检查是否是超时错误
      const isTimeout = error instanceof Error && error.message.includes('超时');
      const errorMessage = isTimeout ? '认证超时，可能是生物验证卡住了' : '认证过程中发生错误';

      Alert.alert(
        '认证错误',
        errorMessage,
        [
          {
            text: '重试',
            onPress: () => {
              checkAndPerformBiometricAuth(true);
            }
          },
          ...(isTimeout ? [{
            text: '紧急重置',
            onPress: async () => {
              try {
                const { clearAuthenticationSession } = await import('@/services/biometricService');
                await clearAuthenticationSession();

                // 重置本地状态
                setIsBiometricAuthenticated(false);
                setBiometricAuthRequired(false);
                setIsBiometricAuthenticating(false);
                setHasSuccessfulAuth(false);

                Alert.alert('重置完成', '生物验证状态已重置，请重新尝试');
              } catch (resetError) {
                logError('[History] 紧急重置失败:', resetError);
                Alert.alert('重置失败', '无法重置验证状态，请重启应用');
              }
            }
          }] : []),
          {
            text: '返回',
            style: 'cancel',
            onPress: () => {
              router.back();
            }
          }
        ]
      );
      return false;
    } finally {
      setIsBiometricAuthenticating(false);
    }
  }, []);

  // 加载可用的模板名称
  const loadAvailableTemplateNames = useCallback(async () => {
    try {
      const templateNames = await getAllTemplateNames();
      log('加载到的所有模板名称:', templateNames);
      setAvailableTemplateNames(templateNames);
      setTemplateNamesLoaded(true);
    } catch (error) {
      logError('加载模板名称失败:', error);
      setTemplateNamesLoaded(true);
    }
  }, [getAllTemplateNames]);

  // 加载初始数据时同时获取数据库记录总数和模板名称
  const loadInitialData = useCallback(async () => {
    log("加载初始历史记录数据 (最近", loadedCount, "条)");
    try {
      // 先加载可用的模板名称
      await loadAvailableTemplateNames();

      // 获取当前选择模板的真实记录总数和可见记录总数
      const realTotalCount = await getRealRecordsCountByTemplate(currentSelectedTemplate === t("history.all") ? undefined : currentSelectedTemplate);
      const visibleTotalCount = await getVisibleRecordsCountByTemplate(currentSelectedTemplate === t("history.all") ? undefined : currentSelectedTemplate);
      const hiddenCount = await getHiddenRecordsCountByTemplate(currentSelectedTemplate === t("history.all") ? undefined : currentSelectedTemplate);
      
      setCurrentTemplateRealCount(realTotalCount);
      setCurrentTemplateVisibleCount(visibleTotalCount);
      setCurrentTemplateHiddenCount(hiddenCount);
      log("当前模板", currentSelectedTemplate, "- 真实记录总数：", realTotalCount, "，可见记录总数：", visibleTotalCount, "，隐藏记录数：", hiddenCount, "，VIP状态：", isVIP);
      
      if (realTotalCount > visibleTotalCount) {
        log("用户有", realTotalCount - visibleTotalCount, "条记录因非VIP状态而不可见");
      }

      // 根据VIP状态决定实际加载数量
      let actualLoadCount = loadedCount;
      if (!isVIP) {
        actualLoadCount = Math.min(loadedCount, 50);
        log("非VIP用户，限制加载", actualLoadCount, "条记录");
      } else {
        log("VIP用户，加载", actualLoadCount, "条记录");
      }
      
      // 根据当前选择的模板加载记录
      const records = await loadGroupedRecordsByTemplate(
        currentSelectedTemplate === t("history.all") ? undefined : currentSelectedTemplate,
        actualLoadCount,
        t
      );
      
      if (records && Array.isArray(records)) {
        setDisplayGroupedRecords(records as HistoryGroup[]);

        // 计算当前已加载的记录数量
        const loadedRecordsCount = records.reduce(
          (acc: number, group: HistoryGroup) => acc + group.data.length,
          0
        );

        // 判断是否还有更多数据（基于可见的总记录数）
        setHasMore(loadedRecordsCount < visibleTotalCount);
        log("已加载记录数：", loadedRecordsCount, "，可见总数：", visibleTotalCount, "，还有更多：", loadedRecordsCount < visibleTotalCount);
      }
      setInitialLoadDone(true);
    } catch (error) {
      logError("加载初始历史记录失败:", error);
    }
      }, [loadGroupedRecordsByTemplate, loadedCount, isVIP, currentSelectedTemplate, getRealRecordsCountByTemplate, getVisibleRecordsCountByTemplate, getHiddenRecordsCountByTemplate, loadAvailableTemplateNames, t]);

  // 加载更多数据
  const loadMoreData = useCallback(async () => {
    if (isLoadingMore || !hasMore) return;

    try {
      setIsLoadingMore(true);

      // 计算当前已加载的记录数
      const currentLoadedRecords = Array.isArray(displayGroupedRecords)
        ? displayGroupedRecords.reduce(
            (acc: number, group: HistoryGroup) => acc + group.data.length,
            0
          )
        : 0;

      log(
        `加载更多历史记录, 当前已加载 ${currentLoadedRecords}/${currentTemplateVisibleCount} 条，VIP状态：${isVIP}，当前模板：${currentSelectedTemplate}`
      );

      // 非VIP用户检查是否已达到50条限制
      if (!isVIP && currentLoadedRecords >= 50) {
        log("非VIP用户已达到50条记录限制，停止加载更多");
        setHasMore(false);
        setIsLoadingMore(false);
        return;
      }

      // 增加加载条数，每次增加15条
      let newLoadedCount = loadedCount + 15;
      
      // 非VIP用户限制最大请求数量为50
      if (!isVIP) {
        newLoadedCount = Math.min(newLoadedCount, 50);
        log("非VIP用户，限制最大加载数量为50条");
      }
      
      setLoadedCount(newLoadedCount);

      // 加载更多数据
      const result = await loadGroupedRecordsByTemplate(
        currentSelectedTemplate === t("history.all") ? undefined : currentSelectedTemplate,
        newLoadedCount,
        t
      );

      if (result && Array.isArray(result)) {
        // 更新UI展示数组
        setDisplayGroupedRecords(result as HistoryGroup[]);

        // 计算新加载后的记录数
        const newLoadedRecords = result.reduce(
          (acc: number, group: HistoryGroup) => acc + group.data.length,
          0
        );

        // 判断是否已经加载了所有可见数据
        const allDataLoaded = newLoadedRecords >= currentTemplateVisibleCount;
        setHasMore(!allDataLoaded);

        log(
          `新加载记录数: ${newLoadedRecords}/${totalRecordsCount}, VIP状态: ${isVIP}, 是否全部加载: ${allDataLoaded}`
        );
      } else {
        setHasMore(false);
      }
    } catch (error) {
      logError("加载更多历史记录失败:", error);
    } finally {
      setIsLoadingMore(false);
    }
      }, [
    isLoadingMore,
    hasMore,
    loadedCount,
    loadGroupedRecords,
    displayGroupedRecords,
    totalRecordsCount,
    isVIP,
    currentSelectedTemplate,
    currentTemplateVisibleCount,
    loadGroupedRecordsByTemplate,
    t,
  ]);

  // 修复 useFocusEffect 中的刷新逻辑，优化加载最新记录
  useFocusEffect(
    useCallback(() => {
      log("历史记录页面聚焦");

      // 进入页面时先进行生物识别认证，然后刷新数据
      const handlePageFocus = async () => {
        // 如果已经成功认证过，直接刷新数据，避免重复认证
        if (hasSuccessfulAuth && isBiometricAuthenticated) {
          log("已认证，直接刷新数据");
          await refreshAllHistoryData();
          return;
        }

        // 静默检查是否需要认证
        const needsAuth = await shouldRequireAuthentication();
        if (!needsAuth) {
          // 不需要认证，直接刷新数据
          log("无需认证，直接刷新数据");
          setIsBiometricAuthenticated(true);
          setBiometricAuthRequired(false);
          setHasSuccessfulAuth(true);
          await refreshAllHistoryData();
          return;
        }

        // 需要认证但还未认证成功，设置状态等待用户认证
        if (!isBiometricAuthenticated) {
          log("需要用户认证");
          setBiometricAuthRequired(true);
          setHasSuccessfulAuth(false);
          // 不在这里刷新数据，等待用户认证成功后再刷新
          return;
        }

        // 认证成功后刷新数据
        await refreshAllHistoryData();
      };

      // 进入页面时强制刷新数据，确保获取最新记录
      const refreshHistoryData = async () => {
        try {
          setRefreshing(true);

          // 重新加载模板名称
          const newTemplateNames = await getAllTemplateNames();
          log("页面聚焦时重新加载的所有模板名称:", newTemplateNames);
          setAvailableTemplateNames(newTemplateNames);
          setTemplateNamesLoaded(true);

          // 检查当前选择的模板是否仍然存在
          let templateToUse = currentSelectedTemplate;
          if (currentSelectedTemplate !== t("history.all") as string) {
            if (!newTemplateNames.includes(currentSelectedTemplate)) {
              log("当前选择的模板", currentSelectedTemplate, "不再存在，切换到全部");
              templateToUse = t("history.all") as string;
              setCurrentSelectedTemplate(t("history.all") as string);
            }
          }

          // 重新获取当前模板的记录总数和可见记录总数
          const realTotalCount = await getRealRecordsCountByTemplate(templateToUse === t("history.all") ? undefined : templateToUse);
          const visibleTotalCount = await getVisibleRecordsCountByTemplate(templateToUse === t("history.all") ? undefined : templateToUse);
          const hiddenCount = await getHiddenRecordsCountByTemplate(templateToUse === t("history.all") ? undefined : templateToUse);
          
          setCurrentTemplateRealCount(realTotalCount);
          setCurrentTemplateVisibleCount(visibleTotalCount);
          setCurrentTemplateHiddenCount(hiddenCount);
          log("刷新数据 - 当前模板", templateToUse, "真实记录总数：", realTotalCount, "，可见记录总数：", visibleTotalCount, "，隐藏记录数：", hiddenCount, "，VIP状态：", isVIP);

          // 根据VIP状态决定加载数量
          let actualLoadCount = loadedCount;
          if (!isVIP) {
            actualLoadCount = Math.min(loadedCount, 50);
          }
          
          // 直接加载最新数据，不进行时间戳比较
          const refreshedRecords = await loadGroupedRecordsByTemplate(
            templateToUse === t("history.all") ? undefined : templateToUse,
            actualLoadCount,
            t
          );

          // 更新UI展示数组
          if (refreshedRecords && Array.isArray(refreshedRecords)) {
            setDisplayGroupedRecords(refreshedRecords as HistoryGroup[]);

            // 计算当前加载的记录数
            const loadedRecordsCount = refreshedRecords.reduce(
              (acc: number, group: HistoryGroup) => acc + group.data.length,
              0
            );

            // 更新是否还有更多数据的状态
            setHasMore(loadedRecordsCount < visibleTotalCount);
          }

          log("历史记录页面聚焦，已刷新数据");
        } catch (error) {
          logError("刷新历史记录失败:", error);
        } finally {
          setRefreshing(false);
        }
      };

      if (initialLoadDone) {
        handlePageFocus();
      }

      return () => {
        // 离开页面时的清理工作，停止音频播放
        if (isPlaying && sound) {
          stopPlayback();
        }
      };
    }, [
      initialLoadDone,
      loadGroupedRecordsByTemplate,
      loadedCount,
      isPlaying,
      sound,
      stopPlayback,
      isVIP,
      currentSelectedTemplate,
      getRealRecordsCountByTemplate,
      getVisibleRecordsCountByTemplate,
      getHiddenRecordsCountByTemplate,
      getAllTemplateNames,
      t,
      checkAndPerformBiometricAuth,
      hasSuccessfulAuth,
      isBiometricAuthenticated,
      authCheckCompleted,
      biometricAuthRequired,
    ])
  );

  // 切换标签时的处理函数
  const handleTabChange = useCallback(async (tabObj: FilterTab) => {
    let templateToUse = t("history.all");
    
    // 修复模板选择逻辑
    if (tabObj.rawName) {
      // 自定义模板：直接使用rawName
      templateToUse = tabObj.rawName;
    } else if (tabObj.semanticId) {
      // 系统预设模板：通过semanticId找到原始模板名
      const found = availableTemplateNames.find(name => getTemplateSemanticId(name) === tabObj.semanticId);
      if (found) templateToUse = found;
    }
    // 如果既没有rawName也没有semanticId，则使用默认的"全部"
    
    if (templateToUse === currentSelectedTemplate) return;
    log("切换标签到:", templateToUse);
    setCurrentSelectedTemplate(templateToUse);
    setLoadedCount(15);
    setHasMore(true);
    setIsLoadingMore(false);
    try {
      setRefreshing(true);
      
      // 重新获取当前模板的记录总数和可见记录总数
      const realTotalCount = await getRealRecordsCountByTemplate(templateToUse === t("history.all") ? undefined : templateToUse);
      const visibleTotalCount = await getVisibleRecordsCountByTemplate(templateToUse === t("history.all") ? undefined : templateToUse);
      const hiddenCount = await getHiddenRecordsCountByTemplate(templateToUse === t("history.all") ? undefined : templateToUse);
      
      setCurrentTemplateRealCount(realTotalCount);
      setCurrentTemplateVisibleCount(visibleTotalCount);
      setCurrentTemplateHiddenCount(hiddenCount);
      
      // 根据VIP状态决定加载数量
      let actualLoadCount = 15;
      if (!isVIP) {
        actualLoadCount = Math.min(15, 50);
      }
      
      // 加载新模板的数据
      const refreshedRecords = await loadGroupedRecordsByTemplate(
        templateToUse === t("history.all") ? undefined : templateToUse,
        actualLoadCount,
        t
      );

      // 更新UI展示数组
      if (refreshedRecords && Array.isArray(refreshedRecords)) {
        setDisplayGroupedRecords(refreshedRecords as HistoryGroup[]);

        // 计算当前加载的记录数
        const loadedRecordsCount = refreshedRecords.reduce(
          (acc: number, group: HistoryGroup) => acc + group.data.length,
          0
        );

        // 更新是否还有更多数据的状态
        setHasMore(loadedRecordsCount < visibleTotalCount);
      }
    } catch (e) {
      logError("切换标签失败:", e);
    }
    setRefreshing(false);
  }, [availableTemplateNames, currentSelectedTemplate, log, setCurrentSelectedTemplate, t, getRealRecordsCountByTemplate, getVisibleRecordsCountByTemplate, getHiddenRecordsCountByTemplate, loadGroupedRecordsByTemplate, isVIP]);

  // 优化刷新数据的函数，确保获取最新数据
  const refreshData = useCallback(async () => {
    try {
      setRefreshing(true);

      // 重置为初始加载数量
      const initialCount = 15;
      setLoadedCount(initialCount);

      // 重新加载模板名称
      const newTemplateNames = await getAllTemplateNames();
      log("手动刷新时重新加载的所有模板名称:", newTemplateNames);
      setAvailableTemplateNames(newTemplateNames);
      setTemplateNamesLoaded(true);

      // 检查当前选择的模板是否仍然存在
      let templateToUse = currentSelectedTemplate;
      if (currentSelectedTemplate !== t("history.all") as string) {
        if (!newTemplateNames.includes(currentSelectedTemplate)) {
          log("手动刷新时发现当前选择的模板", currentSelectedTemplate, "不再存在，切换到全部");
          templateToUse = t("history.all") as string;
          setCurrentSelectedTemplate(t("history.all") as string);
        }
      }

      // 重新获取当前模板的记录总数和可见记录总数
      const realTotalCount = await getRealRecordsCountByTemplate(
        templateToUse === t("history.all") ? undefined : templateToUse
      );
      const visibleTotalCount = await getVisibleRecordsCountByTemplate(
        templateToUse === t("history.all") ? undefined : templateToUse
      );
      const hiddenCount = await getHiddenRecordsCountByTemplate(
        templateToUse === t("history.all") ? undefined : templateToUse
      );
      
      setCurrentTemplateRealCount(realTotalCount);
      setCurrentTemplateVisibleCount(visibleTotalCount);
      setCurrentTemplateHiddenCount(hiddenCount);
      log("手动刷新 - 当前模板", templateToUse, "真实记录总数：", realTotalCount, "，可见记录总数：", visibleTotalCount, "，隐藏记录数：", hiddenCount, "，VIP状态：", isVIP);

      // 根据VIP状态决定加载数量
      let actualInitialCount = initialCount;
      if (!isVIP) {
        actualInitialCount = Math.min(initialCount, 50);
      }
      
      // 加载初始数据
      const refreshedRecords = await loadGroupedRecordsByTemplate(
        templateToUse === t("history.all") ? undefined : templateToUse,
        actualInitialCount,
        t
      );

      // 更新UI展示数组
      if (refreshedRecords && Array.isArray(refreshedRecords)) {
        setDisplayGroupedRecords(refreshedRecords as HistoryGroup[]);

        // 计算当前加载的记录数
        const loadedRecordsCount = refreshedRecords.reduce(
          (acc: number, group: HistoryGroup) => acc + group.data.length,
          0
        );

        // 更新是否还有更多数据的状态
        setHasMore(loadedRecordsCount < visibleTotalCount);
      }

      log("历史记录已手动刷新，重置为初始", initialCount, "条");
    } catch (error) {
      logError("刷新历史记录失败:", error);
    } finally {
      setRefreshing(false);
    }
  }, [loadGroupedRecordsByTemplate, isVIP, currentSelectedTemplate, getRealRecordsCountByTemplate, getVisibleRecordsCountByTemplate, getHiddenRecordsCountByTemplate, getAllTemplateNames, t]);

  // 添加下拉刷新功能，确保用户可以主动刷新列表
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = useCallback(async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    await refreshData();
    setIsRefreshing(false);
  }, [refreshData, isRefreshing]);

  // 监听列表滚动到底部，加载更多数据
  const handleEndReached = useCallback(() => {
    if (!isLoadingMore && hasMore && !isRefreshing) {
      // 计算当前已加载的记录数
      const currentLoadedRecords = Array.isArray(displayGroupedRecords)
        ? displayGroupedRecords.reduce(
            (acc: number, group: HistoryGroup) => acc + group.data.length,
            0
          )
        : 0;

      // 根据VIP状态判断是否继续加载
      let shouldLoadMore = false;
      // 无论VIP还是非VIP用户，都是基于当前模板的可见记录数来判断
      shouldLoadMore = currentLoadedRecords < currentTemplateVisibleCount;
      
      log(
        `加载更多判断: 已加载${currentLoadedRecords}条，当前模板可见${currentTemplateVisibleCount}条，VIP: ${isVIP}，模板: ${currentSelectedTemplate}，应该加载更多: ${shouldLoadMore}`
      );

      if (shouldLoadMore) {
        log(
          `列表滚动到底部，加载更多数据 (${currentLoadedRecords}/${currentTemplateVisibleCount}，VIP: ${isVIP}，模板: ${currentSelectedTemplate})`
        );
        loadMoreData();
      } else {
        // 已加载所有可见记录，设置hasMore为false
        if (hasMore) {
          setHasMore(false);
          const reason = !isVIP ? "已加载当前模板在前50条记录中的所有数据" : "当前模板的所有记录已加载完毕";
          log(`${reason}，不再尝试加载更多`);
        }
      }
    }
  }, [
    isLoadingMore,
    hasMore,
    loadMoreData,
    isRefreshing,
    displayGroupedRecords,
    currentTemplateVisibleCount,
    isVIP,
    currentSelectedTemplate,
  ]);

  // 修复关闭Swipeable功能
  const closeSwipeable = useCallback(() => {
    const openedItemId = openedSwipeableRef.current;
    if (openedItemId && swipeableRefs.current[openedItemId]) {
      swipeableRefs.current[openedItemId].close();
      openedSwipeableRef.current = null;
    }
  }, []);

  // 处理滚动时关闭滑动项的节流函数
  const handleScrollThrottled = useCallback(() => {
    const now = Date.now();
    // 如果距离上次滚动处理不到300ms，设置一个延迟处理
    if (now - lastScrollTimeRef.current < 300) {
      if (scrollThrottleTimeoutRef.current) {
        clearTimeout(scrollThrottleTimeoutRef.current);
      }
      scrollThrottleTimeoutRef.current = setTimeout(() => {
        if (openedSwipeableRef.current) {
          closeSwipeable();
        }
        lastScrollTimeRef.current = Date.now();
      }, 300);
    } else {
      // 超过300ms，立即处理
      if (openedSwipeableRef.current) {
        closeSwipeable();
      }
      lastScrollTimeRef.current = now;
    }
  }, [closeSwipeable]);

  // 筛选标签列表
  const filterTabs = [
    t("history.all"),
    t("history.workRelated"),
    t("history.email"),
    t("history.meetingNotes"),
    t("history.chat"),
    t("history.important"),
    t("history.personal"),
    t("history.draft"),
    t("history.archived"),
  ];

  // 定义Tab对象类型
  interface FilterTab {
    semanticId?: string;
    displayName: string;
    rawName?: string;
  }

  // 当前选中的 semanticId
  const currentSelectedSemanticId: string | undefined = getTemplateSemanticId(currentSelectedTemplate) || undefined;

  // 动态生成筛选标签列表，维护 semanticId
  const dynamicFilterTabs = React.useMemo(() => {
    const tabs: FilterTab[] = [{ semanticId: undefined, displayName: t("history.all" as const) }];
    if (templateNamesLoaded && availableTemplateNames.length > 0) {
      availableTemplateNames.forEach(name => {
        const semanticId = getTemplateSemanticId(name);
        tabs.push({ semanticId, displayName: getTemplateDisplayName(name), rawName: name });
      });
    }
    return tabs;
  }, [templateNamesLoaded, availableTemplateNames, t, getTemplateDisplayName]);

  // 将HistoryGroup类型转换为本地使用的HistoryItem类型
  const convertToHistoryItems = useCallback(
    (groups: HistoryGroup[]) => {
      const result: { [key: string]: HistoryItem[] } = {};

      groups.forEach((group) => {
        const items = group.data.map((record) => {
          // 从templateName提取标签
          const tags = extractTagsFromTemplate(record.templateName);

          // 为所有项目添加示例音频文件，确保可以测试播放功能
          const defaultAudioUri = "file:///assets/sounds/silence.mp3";
          // 使用记录自带的音频URI，如果没有则使用默认音频
          const audioUri = record.audioUri || defaultAudioUri;

          // 检查是否为二次优化记录
          const isSecondaryOptimization = !!record.sourceRecordId;

          // 获取最新的优化结果文本
          let latestOptimizedText = record.optimizedText;
          if (record.optimizedResults && record.optimizedResults.length > 0) {
            // 如果有优化结果数组，找到最新的优化结果
            const latestResult = record.optimizedResults.reduce((latest, current) => {
              return current.timestamp > latest.timestamp ? current : latest;
            });
            latestOptimizedText = latestResult.text;
          }

          return {
            id: record.id,
            title: getTemplateDisplayName(record.templateName),
            time: formatTimeFromTimestamp(record.timestamp), // 只包含时间部分
            date: formatDateFromTimestamp(record.timestamp), // 日期部分（今天/昨天/前天/具体日期）
            tags,
            audioProgress: 0,
            originalText: record.originalText,
            optimizedText: latestOptimizedText, // 使用最新的优化结果
            dateGroup: group.title,
            audioUri,
            isSecondaryOptimization, // 添加二次优化标记
            sourceRecordId: record.sourceRecordId, // 添加源记录ID
          };
        });

        result[group.title] = items;
      });

      return result;
    },
    [getTemplateDisplayName]
  );

  // 从模板名称提取标签
  const extractTagsFromTemplate = useCallback((templateName: string): string[] => {
    // 检查 templateName 是否存在
    if (!templateName) {
      return [];
    }

    // 获取semantic_id
    const semanticId = getTemplateSemanticId(templateName);
    if (!semanticId) {
      return [];
    }

    // 根据semantic_id返回相应的标签
    switch (semanticId) {
      case 'email':
        return [t('history.workRelated' as const), t('history.email' as const)];
      case 'chat':
      case 'emoji':
        return [t('history.chat' as const)];
      case 'notes':
      case 'markdown_notes':
        return [t('history.workRelated' as const), t('history.meetingNotes' as const)];
      case 'text_optimize':
        return [t('history.important' as const)];
      case 'xhs':
      case 'moment':
        return [t('history.personal' as const)];
      case 'enTrans':
      case 'jaTrans':
      case 'koTrans':
      case 'idTrans':
      case 'deTrans':
      case 'frTrans':
      case 'esTrans':
        return [t('history.important' as const)];
      case 'recraft':
      case 'midjourney':
      case 'stable_diffusion':
      case 'dreamAI':
        return [t('history.personal' as const)];
      case 'official':
        return [t('history.workRelated' as const), t('history.important' as const)];
      case 'academic':
        return [t('history.workRelated' as const), t('history.important' as const)];
      case 'news':
        return [t('history.workRelated' as const)];
      case 'creative':
        return [t('history.personal' as const)];
      case 'casual':
        return [t('history.chat' as const), t('history.personal' as const)];
      default:
        return [];
    }
  }, [t]);

  // 从时间戳格式化时间
  const formatTimeFromTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp);
    return `${date.getHours().toString().padStart(2, "0")}:${date
      .getMinutes()
      .toString()
      .padStart(2, "0")}`;
  };

  // 从时间戳格式化日期
  const formatDateFromTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp);
    return formatDate(timestamp, t);
  };

  // 转换历史记录数据
  const groupedData = React.useMemo(() => {
    return convertToHistoryItems(displayGroupedRecords);
  }, [displayGroupedRecords, audioInfoMap, getTemplateDisplayName]);

  // 示例数据 - 保留用于模拟不存在的功能，如音频分段文本
  const originalTimestampedText: { [key: string]: TimeStampedText[] } = {
    "1": [
      {
        timestamp: "0:05",
        text: "今天和张总讨论了新项目的进展情况，他对我们的工作表示满意，但是有几点需要注意的地方。",
      },
      {
        timestamp: "0:20",
        text: "第一，项目进度需要加快，目前的进度比计划慢了大约一周时间。",
      },
      { timestamp: "0:35", text: "第二，用户反馈需要更加及时地收集和分析。" },
      {
        timestamp: "0:45",
        text: "第三，下周二之前需要提交一份详细的项目报告，包括目前的进展、遇到的问题以及解决方案。",
      },
    ],
    "2": [
      {
        timestamp: "0:08",
        text: "今天早上的产品讨论很有收获。",
      },
      {
        timestamp: "0:15",
        text: "我们确定了三个关键功能：第一，用户可以自定义模板。",
      },
      {
        timestamp: "0:30",
        text: "第二，支持语音和文字输入。",
      },
      {
        timestamp: "0:42",
        text: "第三，历史记录可以按标签筛选和搜索。",
      },
      {
        timestamp: "1:00",
        text: "下一步我们需要确定开发时间表和资源分配。",
      },
    ],
    "3": [
      {
        timestamp: "0:05",
        text: "最近终于搞定了那个项目，",
      },
      {
        timestamp: "0:12",
        text: "这周末有空出来聚一聚吗？",
      },
      {
        timestamp: "0:20",
        text: "我们可以去尝试那家新开的餐厅，听说评价很好。",
      },
    ],
    "4": [
      {
        timestamp: "0:10",
        text: "本季度销售额同比增长15%，超出预期目标3个百分点。",
      },
      {
        timestamp: "0:25",
        text: "新产品线贡献了30%的增长，",
      },
      {
        timestamp: "0:35",
        text: "客户满意度提升了8%。",
      },
    ],
    "5": [
      {
        timestamp: "0:05",
        text: "这几天出差，每天都会想起你。",
      },
      {
        timestamp: "0:15",
        text: "期待周末回家。",
      },
    ],
  };

  // 详情视图中的高亮差异文本 - 保留用于展示功能
  const highlightedText: { [key: string]: string[] } = {
    "1": [
      "尊敬的<span class='highlight'>张总</span>：",
      "<span class='highlight'>您好！</span>",
      "<span class='highlight'>感谢您今天抽出宝贵时间</span>讨论新项目的进展情况。<span class='highlight'>非常高兴</span>您对我们的工作表示满意<span class='highlight'>。</span>",
      "<span class='highlight'>根据我们的讨论，以下是</span>需要注意的<span class='highlight'>几点：</span>",
      "<span class='highlight'>1. 项目进度：</span>需要加快<span class='highlight'>当前</span>进度，目前<span class='highlight'>比</span>计划<span class='highlight'>滞后</span>约一周时间<span class='highlight'>；</span>",
      "<span class='highlight'>2. 用户反馈：</span>需更加及时<span class='highlight'>地</span>收集和分析<span class='highlight'>用户反馈数据；</span>",
      "<span class='highlight'>3. 项目报告：</span>需<span class='highlight'>在</span>下周二<span class='highlight'>前</span>提交<span class='highlight'>详细的</span>项目报告，包括目前<span class='highlight'>进展</span>、遇到的问题<span class='highlight'>及</span>解决方案<span class='highlight'>。</span>",
      "<span class='highlight'>我们团队将立即着手解决这些问题，确保项目顺利推进。</span>",
      "<span class='highlight'>如有任何疑问，随时欢迎您的指导。</span>",
      "<span class='highlight'>此致</span>",
      "<span class='highlight'>敬礼</span>",
    ],
  };

  // 处理选择历史记录项
  const handleSelectHistoryItem = (item: HistoryItem) => {
    // 关闭任何已打开的滑动条目
    closeSwipeable();

    // 使用router导航到详情页面
    router.push({
      pathname: "/(tabs)/history/detail",
      params: { id: item.id },
    });
  };

  // 更新切换播放函数为异步回调
  const handleTogglePlay = useCallback(
    async (item: HistoryItem) => {
      try {
        // 处理是否正在播放相同的音频 - 如果是，直接调用togglePlay进行暂停/播放切换
        const isSameAudio = audioUri === item.audioUri;
        const isCurrentlyPlaying =
          playingItemIdRef.current === item.id && isPlaying;

        if (isSameAudio && isCurrentlyPlaying) {
          // 当前音频正在播放，只需暂停它
          log("暂停当前正在播放的音频:", item.id);
          await togglePlay();
          return;
        }

        if (isSameAudio && !isCurrentlyPlaying) {
          // 当前音频已加载但未播放，直接播放
          log("继续播放已加载的音频:", item.id);
          playingItemIdRef.current = item.id; // 更新播放ID
          await togglePlay();
          return;
        }

        // 到这里说明是切换到新的音频

        // 先停止所有播放中的音频并给系统一些缓冲时间
        if (isPlaying && sound) {
          try {
            log("检测到正在播放其他音频，准备暂停");

            // 先通过togglePlay暂停当前播放，它内部已经包含了错误处理和状态管理
            await togglePlay();
            log("成功暂停之前正在播放的音频");

            // 添加短暂延迟让音频系统缓冲
            await new Promise((resolve) => setTimeout(resolve, 100));
          } catch (error) {
            warn("暂停当前音频时出错，继续处理:", error);
            // 给系统更多恢复时间
            await new Promise((resolve) => setTimeout(resolve, 200));
          }
        }

        // 更新当前播放项目ID (UI会立即反映)
        playingItemIdRef.current = item.id;

        // 准备加载新音频
        log("准备加载新音频:", item.audioUri);

        try {
          // 确保路径正确
          const audioUriToLoad = getAudioFileAbsolutePath(item.audioUri || '');

          log("加载音频:", audioUriToLoad);

          // 加载新音频，将直接播放设置为false
          const loadResult = await loadAudio(audioUriToLoad);

          if (loadResult) {
            log("音频加载成功，准备播放");

            // 获取并更新音频时长
            try {
              const status = await loadResult.getStatusAsync();
              if (status.isLoaded && status.durationMillis) {
                const durationMs = status.durationMillis;
                const displayDuration = formatTime(durationMs);

                setAudioInfoMap((prev) => ({
                  ...prev,
                  [item.id]: {
                    duration: durationMs,
                    displayDuration,
                  },
                }));
              }
            } catch (err) {
              warn("获取音频时长失败，使用默认值");
            }

            // 添加短暂延迟确保音频完全准备好
            await new Promise((resolve) => setTimeout(resolve, 150));

            // 在播放前再次检查音频状态
            try {
              const finalStatus = await loadResult.getStatusAsync();
              if (finalStatus.isLoaded) {
                // 直接播放音频，而不是使用togglePlay
                await loadResult.playAsync();
                // 手动更新播放状态
                setIsPlaying(true);
                log("新加载的音频开始自动播放成功");
              } else {
                warn("音频似乎已加载但状态检查失败，尝试重载");
                throw new Error("音频加载状态异常");
              }
            } catch (playError) {
              warn("播放前检测到异常，尝试重新加载:", playError);

              // 给系统一些恢复时间
              await new Promise((resolve) => setTimeout(resolve, 200));

              // 重新加载并尝试播放
              const retryResult = await loadAudio(audioUriToLoad);
              if (retryResult) {
                await new Promise((resolve) => setTimeout(resolve, 200));
                // 直接播放而不是使用togglePlay
                await retryResult.playAsync();
                setIsPlaying(true);
                log("重试自动播放成功");
              } else {
                throw new Error("重试加载音频失败");
              }
            }
          } else {
            throw new Error("音频加载失败");
          }
        } catch (loadError) {
          logError("加载或播放音频失败:", loadError);

          // 尝试使用默认音频
          try {
            const defaultSound = await loadAudio(
              "file:///assets/sounds/silence.mp3"
            );
            log("已切换到默认音频，尝试播放");
            await new Promise((resolve) => setTimeout(resolve, 150));
            if (defaultSound) {
              // 直接播放默认音频
              await defaultSound.playAsync();
              setIsPlaying(true);
              log("默认音频自动播放成功");
            }
          } catch (fallbackError) {
            logError("默认音频也无法加载:", fallbackError);
            Alert.alert("播放提示", "无法加载音频文件", [{ text: "确定" }]);
          }
        }
      } catch (error) {
        logError("处理音频播放时出错:", error);
        Alert.alert("播放提示", "无法播放此音频，请稍后重试", [
          { text: "确定" },
        ]);
      }
    },
    [
      loadAudio,
      togglePlay,
      audioUri,
      isPlaying,
      formatTime,
      sound,
      setIsPlaying,
    ]
  );

  // 处理音频进度条点击
  const handleAudioProgressClick = useCallback(
    async (percent: number, itemId: string) => {
      try {
        // 确保是当前播放的音频
        if (playingItemIdRef.current === itemId) {
          seekTo(percent);
        }
      } catch (error) {
        logError("调整音频进度失败:", error);
      }
    },
    [seekTo]
  );

  // 切换展开/收起状态
  const toggleExpand = (itemId: string, event: any) => {
    event.stopPropagation(); // 阻止事件冒泡，避免触发项目点击
    setExpandedItems((prev) => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  // 更新音频进度
  const updateAudioProgress = (progress: number, itemId: string) => {
    // 在实际应用中，这里会更新音频播放位置
    log(`更新音频进度到 ${progress}%, 项目ID: ${itemId}`);
  };

  // 渲染筛选标签
  const renderFilterTab = useCallback((tabObj: FilterTab) => {
    const { semanticId, displayName, rawName } = tabObj;
    
    // 修复选中状态判断逻辑
    let isSelected = false;
    
    if (!semanticId && !rawName) {
      // "全部" 选项：只有当前选择是"全部"时才选中
      isSelected = currentSelectedTemplate === t("history.all");
    } else if (semanticId) {
      // 系统预设模板：通过semanticId匹配
      isSelected = semanticId === currentSelectedSemanticId;
    } else if (rawName) {
      // 自定义模板：通过rawName直接匹配原始模板名
      isSelected = currentSelectedTemplate === rawName;
    }
    
    return (
      <TouchableOpacity
        key={rawName || semanticId || "all"}
        style={[
          styles.filterTab,
          isSelected && styles.filterTabSelected,
          theme === 'dark' && styles.filterTabDark,
          isSelected && theme === 'dark' && styles.filterTabSelectedDark
        ]}
        onPress={() => handleTabChange(tabObj)}
      >
        <Text style={[
          styles.filterTabText,
          isSelected && styles.filterTabTextSelected,
          theme === 'dark' && styles.filterTabTextDark,
          isSelected && theme === 'dark' && styles.filterTabTextSelectedDark
        ]}>
          {displayName}
        </Text>
      </TouchableOpacity>
    );
  }, [currentSelectedTemplate, currentSelectedSemanticId, theme, handleTabChange, t]);

  // 渲染标签
  const renderTag = (tag: string, index: number) => {
    let bgColor = getColor("#F3F4F6", "#2B2F31");
    let textColor = getColor("#6B7280", "#9BA1A6");

    // 检查 tag 是否存在
    if (!tag) {
      return null;
    }

    if (isDark) {
      // 深色模式下的标签颜色
      switch (tag.toLowerCase()) {
        case "工作":
          bgColor = "#103366";
          textColor = "#60A5FA";
          break;
        case "会议":
          bgColor = "#064E3B";
          textColor = "#4ADE80";
          break;
        case "聊天":
          bgColor = "#3B0764";
          textColor = "#C084FC";
          break;
        case "个人":
          bgColor = "#450A0A";
          textColor = "#F87171";
          break;
        case "邮件":
          bgColor = "#451A03";
          textColor = "#FBBF24";
          break;
      }
    } else {
      // 浅色模式下的标签颜色
      switch (tag.toLowerCase()) {
        case "工作":
          bgColor = "#EBF5FF";
          textColor = "#3B82F6";
          break;
        case "会议":
          bgColor = "#ECFDF5";
          textColor = "#10B981";
          break;
        case "聊天":
          bgColor = "#F3E8FF";
          textColor = "#8B5CF6";
          break;
        case "个人":
          bgColor = "#FEE2E2";
          textColor = "#EF4444";
          break;
        case "邮件":
          bgColor = "#FEF3C7";
          textColor = "#F59E0B";
          break;
      }
    }

    return (
      <View
        key={`${tag}-${index}`}
        style={[styles.tag, { backgroundColor: bgColor }]}
      >
        <Text style={[styles.tagText, { color: textColor }]}>{tag}</Text>
      </View>
    );
  };

  // 渲染音频播放控件
  const renderAudioPlayer = (
    audioProgress: number,
    itemId?: string,
    small: boolean = false
  ) => {
    // 检查这个项目是否是当前正在播放的项目
    const isCurrentlyPlaying = itemId
      ? playingItemIdRef.current === itemId
      : false;

    // 对于当前播放的项目，使用来自useAudioPlayer的实时进度
    // 否则使用传入的进度（通常是0或保存的进度）
    const displayProgress = isCurrentlyPlaying
      ? memoizedProgress.current
      : audioProgress;

    // 查找当前项目，获取其音频URI
    const currentItem = itemId
      ? Object.values(groupedData)
          .flat()
          .find((item: HistoryItem) => item.id === itemId)
      : null;

    const safeOnTogglePlay = isSelectMode ? async () => {} : handleTogglePlay;
    const safeOnSeek = isSelectMode ? async () => {} : handleAudioProgressClick;
    return (
      <AudioPlayer
        progress={displayProgress}
        itemId={itemId}
        small={small}
        currentItem={currentItem}
        isCurrentlyPlaying={isCurrentlyPlaying}
        isPlaying={isPlaying}
        onTogglePlay={safeOnTogglePlay}
        onSeek={safeOnSeek}
        getColor={getColor}
        disabled={isSelectMode}
      />
    );
  };

  // 渲染历史记录项
  const renderHistoryItem = (item: HistoryItem) => {
    const isExpanded = expandedItems[item.id] || false;
    const isOpen = openItems[item.id] || false;
    const isSecondaryOptimization = item.isSecondaryOptimization || false;
    const isSelected = selectedItems[item.id] || false;

    // 右侧滑动动作
    const renderRightActions = (progress: any, dragX: any) => {
      const trans = dragX.interpolate({
        inputRange: [-80, 0],
        outputRange: [0, 80],
        extrapolate: "clamp",
      });

      return (
        <RNAnimated.View
          style={[
            styles.swipeRightAction,
            {
              transform: [{ translateX: trans }],
            },
          ]}
        >
          <RNAnimated.View style={[styles.deleteButton]}>
            <TouchableOpacity
              style={{
                width: "100%",
                height: "100%",
                justifyContent: "center",
                alignItems: "center",
              }}
              onPress={() => handleDeleteHistoryItem(item.id)}
            >
              <IconSymbol name="trash" size={20} color="white" />
              <Text style={styles.deleteButtonText}>{t("history.delete")}</Text>
            </TouchableOpacity>
          </RNAnimated.View>
        </RNAnimated.View>
      );
    };

    return (
      <View style={styles.swipeableContainer} key={item.id}>
        <Swipeable
          renderRightActions={renderRightActions}
          friction={2}
          rightThreshold={100}
          overshootRight={false}
          containerStyle={{ backgroundColor: "transparent" }}
          childrenContainerStyle={{
            backgroundColor: getColor("white", "#1E2122"),
            borderRadius: 12,
          }}
          onSwipeableOpen={(direction) => {
            // 关闭其他所有打开的滑动条目
            if (
              openedSwipeableRef.current &&
              openedSwipeableRef.current !== item.id
            ) {
              closeSwipeable();
            }

            // 记录当前打开的Swipeable
            openedSwipeableRef.current = item.id;

            // 更新打开状态
            setOpenItems((prev) => ({
              ...prev,
              [item.id]: true,
            }));
          }}
          onSwipeableClose={() => {
            // 更新关闭状态
            setOpenItems((prev) => ({
              ...prev,
              [item.id]: false,
            }));
          }}
          ref={(ref) => {
            // 保存对当前item的Swipeable引用
            if (ref) {
              swipeableRefs.current[item.id] = ref;
            }
          }}
        >
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            {isSelectMode && (
              <TouchableOpacity
                style={[
                  styles.checkbox,
                  isSelected && styles.checkboxSelected,
                  styles.checkboxAbsolute,
                ]}
                onPress={() => toggleSelectItem(item.id)}
                activeOpacity={0.7}
              >
                {isSelected && (
                  <IconSymbol name="checkmark" size={12} color="white" />
                )}
              </TouchableOpacity>
            )}
            <RNAnimated.View style={{ flex: 1, marginLeft: cardShiftAnim }}>
              <TouchableOpacity
                style={[
                  styles.historyItem,
                  {
                    backgroundColor: getColor("white", "#1E2122"),
                    borderColor: getColor("#f0f0f0", "#2B2F31"),
                    shadowColor: getColor("#000", "#000"),
                  },
                  isOpen && styles.historyItemOpen,
                  isSecondaryOptimization && styles.secondaryOptimizationItem,
                  isSelected && styles.selectedHistoryItem,
                ]}
                onPress={() =>
                  isSelectMode
                    ? toggleSelectItem(item.id)
                    : handleSelectHistoryItem(item)
                }
                onLongPress={() => {
                  if (!isSelectMode) {
                    enterSelectMode();
                    toggleSelectItem(item.id);
                  }
                }}
                activeOpacity={0.8}
              >
                <View style={styles.historyItemHeader}>
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    {/* 二次优化记录的标记 */}
                    {isSecondaryOptimization && (
                      <View style={styles.secondaryOptimizationBadge}>
                        <Text style={styles.secondaryOptimizationBadgeText}>
                          {t("history.detail.reoptimize")}
                        </Text>
                      </View>
                    )}
                    <Text
                      style={[
                        styles.historyItemTitle,
                        { color: getColor("#1F2937", "#ECEDEE") },
                        isSecondaryOptimization && { marginLeft: 6 },
                        isSelectMode && { marginLeft: 8 },
                      ]}
                    >
                      {item.title}
                    </Text>
                  </View>
                  <Text
                    style={[
                      styles.historyItemTime,
                      { color: getColor("#9CA3AF", "#9BA1A6") },
                    ]}
                  >
                    {item.time}
                  </Text>
                </View>
                {!isSecondaryOptimization &&
                  renderAudioPlayer(
                    item.audioProgress,
                    item.id
                  )}
                <View style={styles.contentSummary}>
                  <Text
                    style={[
                      styles.contentSummaryText,
                      { color: getColor("#4B5563", "#9BA1A6") },
                      !isExpanded && styles.truncatedText,
                    ]}
                    numberOfLines={isExpanded ? undefined : 3}
                  >
                    {item.optimizedText || item.originalText}
                  </Text>
                  {!isExpanded && (
                    <TouchableOpacity
                      style={[
                        styles.expandIndicator,
                        {
                          backgroundColor: getColor(
                            "rgba(255, 255, 255, 0.9)",
                            "rgba(30, 33, 34, 0.9)"
                          ),
                        },
                      ]}
                      onPress={(e) => toggleExpand(item.id, e)}
                    >
                      <Text
                        style={[
                          styles.expandIndicatorText,
                          { color: getColor("#6a5ae1", "#61dafb") },
                        ]}
                      >
                        {t("history.expand")}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
                {isExpanded && (
                  <View style={styles.expandBtnWrapper}>
                    <TouchableOpacity
                      style={[
                        styles.expandButtonWithBorder,
                        {
                          backgroundColor: getColor("white", "#1E2122"),
                          borderColor: getColor("#E5E7EB", "#2B2F31"),
                        },
                      ]}
                      onPress={(e) => toggleExpand(item.id, e)}
                    >
                      <Text
                        style={{
                          color: getColor("#6a5ae1", "#61dafb"),
                          fontSize: 13,
                          fontWeight: "500",
                        }}
                      >
                        {t("history.collapse")}
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}
                <View style={styles.historyItemFooter}>
                  <View style={styles.tagContainer}>
                    {item.tags.map((tag, index) => renderTag(tag, index))}
                  </View>
                  <View style={styles.actionButtons}>
                    {/* 复制按钮 */}
                    <TouchableOpacity
                      style={[
                        styles.actionButton,
                        {
                          backgroundColor: getColor(
                            "rgba(237, 242, 247, 0.3)",
                            "rgba(26, 32, 44, 0.3)"
                          ),
                        },
                      ]}
                      onPress={() => copyOptimizedText(item.optimizedText)}
                      activeOpacity={0.7}
                    >
                      <IconSymbol
                        name="doc.on.doc"
                        size={18}
                        color={getColor("#3B82F6", "#60A5FA")}
                      />
                    </TouchableOpacity>
                    {/* 分享按钮 */}
                    <TouchableOpacity
                      style={[
                        styles.actionButton,
                        {
                          backgroundColor: getColor(
                            "rgba(237, 242, 247, 0.3)",
                            "rgba(26, 32, 44, 0.3)"
                          ),
                        },
                      ]}
                      onPress={() => handleShareHistoryItem(item)}
                      activeOpacity={0.7}
                    >
                      <IconSymbol
                        name="square.and.arrow.up"
                        size={18}
                        color={getColor("#10B981", "#34D399")}
                      />
                    </TouchableOpacity>
                    {/* 更多操作按钮 */}
                    <TouchableOpacity
                      style={[
                        styles.actionButton,
                        {
                          backgroundColor: getColor(
                            "rgba(237, 242, 247, 0.3)",
                            "rgba(26, 32, 44, 0.3)"
                          ),
                        },
                      ]}
                      onPress={(event) => {
                        // 获取操作按钮的位置
                        const { pageX, pageY } = event.nativeEvent;
                        handleShowQuickActions(item.id, pageX, pageY);
                      }}
                      activeOpacity={0.7}
                    >
                      <IconSymbol
                        name="ellipsis"
                        size={18}
                        color={getColor("#6B7280", "#9BA1A6")}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
              </TouchableOpacity>
            </RNAnimated.View>
          </View>
        </Swipeable>
      </View>
    );
  };

  // 渲染详情视图中的原始文本
  const renderOriginalText = (itemId: string) => {
    // 这个函数已移至detail.tsx
    warn("这个函数已移至detail.tsx");
    return null;
  };

  // 渲染详情视图中的优化文本（带差异高亮）
  const renderOptimizedTextWithHighlights = (text: string) => {
    // 这个函数已移至detail.tsx
    warn("这个函数已移至detail.tsx");
    return null;
  };

  // 复制优化后的文本
  const copyOptimizedText = (text: string) => {
    Clipboard.setString(text);
    // 触发震动反馈
    safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    showCopyBadge();
  };

  // 分享历史记录
  const handleShareHistoryItem = async (item: HistoryItem) => {
    try {
      await ShareService.shareToWeChat({
        message: item.optimizedText,
      });
    } catch (error) {
      logError("分享失败:", error);
    }
  };

  // 显示快捷操作菜单
  const handleShowQuickActions = (itemId: string, x?: number, y?: number) => {
    if (x && y) {
      // 获取设备屏幕宽度
      const { width: screenWidth } = Dimensions.get("window");

      // 计算最佳位置，考虑屏幕边界
      // 菜单宽度为120，加上边距和安全区域，约为130
      const menuWidth = 130;

      // 确保菜单不超出屏幕右侧
      // 如果点击位置过于靠近屏幕右侧，则将菜单向左偏移更多
      let adjustedX = x;
      if (x + menuWidth > screenWidth - 20) {
        // 如果靠近右侧，将菜单向左移动，确保完全可见
        adjustedX = screenWidth - menuWidth - 20;
      } else if (x - menuWidth < 20) {
        // 如果靠近左侧，确保不会超出左边界
        adjustedX = 40;
      }

      setActionButtonPosition({ x: adjustedX, y });
    } else {
      setActionButtonPosition(null);
    }

    setActionItemId(itemId);
    setShowQuickActions(true);
  };

  // 隐藏快捷操作菜单
  const hideQuickActions = () => {
    setShowQuickActions(false);
    setActionItemId(null);
    setActionButtonPosition(null);
  };

  // 完整刷新历史记录数据和相关状态的函数
  const refreshAllHistoryData = useCallback(async () => {
    try {
      log("开始完整刷新历史记录数据");
      
      // 重新加载模板名称（可能有新的模板或模板被删除）
      const newTemplateNames = await getAllTemplateNames();
      log("重新加载的所有模板名称:", newTemplateNames);
      setAvailableTemplateNames(newTemplateNames);
      setTemplateNamesLoaded(true);

      // 检查当前选择的模板是否仍然存在
      let templateToUse = currentSelectedTemplate;
      if (currentSelectedTemplate !== t("history.all") as string) {
        if (!newTemplateNames.includes(currentSelectedTemplate)) {
          log("当前选择的模板", currentSelectedTemplate, "不再存在，切换到全部");
          templateToUse = t("history.all") as string;
          setCurrentSelectedTemplate(t("history.all") as string);
        }
      }

      // 重新获取当前模板的记录总数和可见记录总数
      const realTotalCount = await getRealRecordsCountByTemplate(
        templateToUse === t("history.all") ? undefined : templateToUse
      );
      const visibleTotalCount = await getVisibleRecordsCountByTemplate(
        templateToUse === t("history.all") ? undefined : templateToUse
      );
      const hiddenCount = await getHiddenRecordsCountByTemplate(
        templateToUse === t("history.all") ? undefined : templateToUse
      );
      
      setCurrentTemplateRealCount(realTotalCount);
      setCurrentTemplateVisibleCount(visibleTotalCount);
      setCurrentTemplateHiddenCount(hiddenCount);
      log("刷新统计数据 - 当前模板", templateToUse, "真实记录总数：", realTotalCount, "，可见记录总数：", visibleTotalCount, "，隐藏记录数：", hiddenCount);

      // 根据VIP状态决定加载数量
      let actualLoadCount = loadedCount;
      if (!isVIP) {
        actualLoadCount = Math.min(loadedCount, 50);
      }
      
      // 重新加载当前模板的记录
      const refreshedRecords = await loadGroupedRecordsByTemplate(
        templateToUse === t("history.all") ? undefined : templateToUse,
        actualLoadCount,
        t
      );

      // 更新UI展示数组
      if (refreshedRecords && Array.isArray(refreshedRecords)) {
        setDisplayGroupedRecords(refreshedRecords as HistoryGroup[]);

        // 计算当前加载的记录数
        const loadedRecordsCount = refreshedRecords.reduce(
          (acc: number, group: HistoryGroup) => acc + group.data.length,
          0
        );

        // 更新是否还有更多数据的状态
        setHasMore(loadedRecordsCount < visibleTotalCount);
        log("刷新完成 - 已加载记录数：", loadedRecordsCount, "，可见总数：", visibleTotalCount);
      }
    } catch (error) {
      logError("完整刷新历史记录数据失败:", error);
    }
  }, [
    getAllTemplateNames,
    getRealRecordsCountByTemplate,
    getVisibleRecordsCountByTemplate,
    getHiddenRecordsCountByTemplate,
    currentSelectedTemplate,
    loadedCount,
    isVIP,
    loadGroupedRecordsByTemplate,
    t,
  ]);

  // 处理删除历史记录项
  const handleDeleteHistoryItem = (itemId: string) => {
    Alert.alert(t("history.deleteConfirmTitle"), t("history.deleteConfirm"), [
      {
        text: t("common.cancel"),
        style: "cancel",
      },
      {
        text: t("common.delete"),
        style: "destructive",
        onPress: async () => {
          try {
            // 显示加载状态
            setRefreshing(true);
            
            // 调用真实的删除函数
            await deleteRecord(itemId);

            // 完整刷新所有相关数据
            await refreshAllHistoryData();

            // 关闭任何打开的swipeable
            closeSwipeable();
            // 隐藏快捷操作菜单
            hideQuickActions();
          } catch (error) {
            logError("删除历史记录失败:", error);
          } finally {
            setRefreshing(false);
          }
        },
      },
    ]);
  };

  // 批量删除历史记录
  const handleBatchDelete = () => {
    const selectedIds = Object.keys(selectedItems).filter(
      (id) => selectedItems[id]
    );
    if (selectedIds.length === 0) return;

    Alert.alert(
      t("history.deleteConfirmTitle"),
      t("history.deleteConfirmMultiple", { count: selectedIds.length }),
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            try {
              // 显示加载指示器
              setRefreshing(true);

              // 依次删除所有选中的记录
              for (const id of selectedIds) {
                await deleteRecord(id);
              }

              // 完整刷新所有相关数据
              await refreshAllHistoryData();

              // 退出选择模式
              exitSelectMode();
            } catch (error) {
              logError("批量删除历史记录失败:", error);
            } finally {
              setRefreshing(false);
            }
          },
        },
      ]
    );
  };

  // 进入选择模式
  const enterSelectMode = () => {
    setIsSelectMode(true);
    setSelectedItems({});
    setSelectedCount(0);
    // 关闭任何打开的swipeable
    closeSwipeable();
    // 隐藏快捷操作菜单
    hideQuickActions();
  };

  // 退出选择模式
  const exitSelectMode = () => {
    setIsSelectMode(false);
    setSelectedItems({});
    setSelectedCount(0);
  };

  // 选择/取消选择项目
  const toggleSelectItem = (itemId: string) => {
    setSelectedItems((prev) => {
      const newSelectedItems = { ...prev };
      newSelectedItems[itemId] = !prev[itemId];

      // 更新选中计数
      let count = 0;
      Object.values(newSelectedItems).forEach((value) => {
        if (value) count++;
      });
      setSelectedCount(count);

      return newSelectedItems;
    });
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    // 获取当前所有可见的记录ID
    const allVisibleIds: string[] = [];
    Object.values(groupedData).forEach((items) => {
      items.forEach((item) => {
        allVisibleIds.push(item.id);
      });
    });

    // 检查是否已经全选（所有可见记录都被选中）
    const isAllSelected = selectedCount === allVisibleIds.length;

    // 如果已全选，则取消全选；否则全选
    const newSelectedItems: { [key: string]: boolean } = {};
    allVisibleIds.forEach((id) => {
      newSelectedItems[id] = !isAllSelected;
    });

    setSelectedItems(newSelectedItems);
    setSelectedCount(isAllSelected ? 0 : allVisibleIds.length);
  };

  // 分享优化后的文本
  const shareOptimizedText = async (item: HistoryItem) => {
    try {
      if (!item) return;

      await ShareService.shareToWeChat({
        message: item.optimizedText,
      });

      // 隐藏快捷操作菜单
      hideQuickActions();
    } catch (error) {
      logError("分享失败:", error);
    }
  };

  // 处理VIP升级点击事件
  const handleUpgradeVIP = () => {
    log('=== VIP升级按钮点击 ===');
    log('当前登录状态:', { isAuthenticated, user: user?.id });
    log('当前pendingVipUpgrade状态:', pendingVipUpgrade);
    log('当前global.pendingVipUpgrade状态:', global.pendingVipUpgrade);
    
    // 检查是否已登录
    if (!isAuthenticated) {
      log('用户未登录，显示登录提示并设置pendingVipUpgrade=true');
      // 未登录，显示登录提示
      setLoginPromptTitle(t('settings.prompts.upgradeVipTitle'));
      setLoginPromptMessage(t('settings.prompts.upgradeVipMessage'));
      setLoginPromptVisible(true);
      // 设置待处理的VIP升级状态
      setPendingVipUpgrade(true);
      // 设置全局标志，阻止登录模态框自动返回
      global.pendingVipUpgrade = true;
      
      // 确认标志设置
      log('VIP升级标志设置完成:', {
        localFlag: true, // setPendingVipUpgrade(true)是异步的
        globalFlag: global.pendingVipUpgrade
      });
    } else {
      log('用户已登录，直接导航到支付页面');
      // 已登录，直接导航到支付页面
      router.push('/payment');
    }

    log('=== VIP升级按钮点击处理完成 ===');
  };

  // 隐藏登录提示
  const hideLoginPrompt = () => {
    setLoginPromptVisible(false);
  };

  // 处理登录成功后的回调
  const handleLoginSuccess = () => {
    log('处理登录回调 - pendingVipUpgrade状态:', pendingVipUpgrade);
    // 关闭登录提示
    hideLoginPrompt();
    // 显示登录模态框
    showLoginModal();
  };

  // 组件卸载时停止播放
  useEffect(() => {
    return () => {
      // 组件卸载时停止音频播放
      if (sound) {
        stopPlayback();
      }

      // 清理滚动节流定时器
      if (scrollThrottleTimeoutRef.current) {
        clearTimeout(scrollThrottleTimeoutRef.current);
      }
    };
  }, [sound, stopPlayback]);

  // 监听历史记录变更事件
  useEffect(() => {
    const handleHistoryRecordAdded = () => {
      log("监听到历史记录新增事件，刷新列表");
      // 当有新的历史记录添加时，刷新当前页面数据
      if (initialLoadDone) {
        refreshAllHistoryData();
      }
    };

    const handleHistoryRecordUpdated = () => {
      log("监听到历史记录更新事件，刷新列表");
      // 当历史记录被更新时，刷新当前页面数据
      if (initialLoadDone) {
        refreshAllHistoryData();
      }
    };

    // 注册事件监听器
    const addedListener = DeviceEventEmitter.addListener('HISTORY_RECORD_ADDED', handleHistoryRecordAdded);
    const updatedListener = DeviceEventEmitter.addListener('HISTORY_RECORD_UPDATED', handleHistoryRecordUpdated);

    return () => {
      // 清理事件监听器
      addedListener.remove();
      updatedListener.remove();
    };
  }, [initialLoadDone, refreshAllHistoryData]);

  // VIP升级监听器：当用户登录后自动导航到支付页面
  useEffect(() => {
    log('VIP升级监听器 - 状态变化:', { 
      isAuthenticated, 
      pendingVipUpgrade, 
      userId: user?.id,
      globalFlag: global.pendingVipUpgrade 
    });
    
    // 注释掉自动导航逻辑，改为由login页面处理
    // 这样可以确保login页面先关闭，然后再导航到payment页面
  }, [isAuthenticated, pendingVipUpgrade, user]);

  // 添加额外的监听机制，用于处理可能的状态更新延迟
  useEffect(() => {
    log('VIP升级额外检查 - 状态:', { 
      pendingVipUpgrade, 
      isAuthenticated, 
      userId: user?.id,
      globalFlag: global.pendingVipUpgrade
    });
  }, [pendingVipUpgrade, isAuthenticated, user]);

  // 防护机制：监听路由变化，确保VIP升级流程不被干扰
  useEffect(() => {
    log('VIP升级防护机制 - 状态:', { 
      globalFlag: global.pendingVipUpgrade, 
      isAuthenticated, 
      userId: user?.id 
    });
  }, [isAuthenticated, user]);

  // 在FlatList底部渲染加载状态或结束提示
  const renderFooter = () => {
    if (!initialLoadDone) return null;

    if (isLoadingMore) {
      // 加载中显示加载指示器
      return (
        <View style={styles.footerContainer}>
          <ActivityIndicator size="small" color="#6a5ae1" />
          <Text
            style={[
              styles.footerText,
              { color: getColor("#6B7280", "#9BA1A6") },
            ]}
          >
            {t('history.loadMore')}
          </Text>
        </View>
      );
    }

    if (!hasMore && displayGroupedRecords && displayGroupedRecords.length > 0) {
      // 所有数据已加载完毕
      const loadedRecordsCount = Array.isArray(displayGroupedRecords)
        ? displayGroupedRecords.reduce(
            (acc: number, group: HistoryGroup) => acc + group.data.length,
            0
          )
        : 0;

      // 根据VIP状态显示不同的提示文案
      let footerText = "";
      
      // 计算隐藏记录数的新逻辑：
      // 对于非VIP用户，需要考虑全局50条限制和当前模板筛选的关系
      let hiddenRecordsCount = 0;
      if (!isVIP) {
        if (currentSelectedTemplate === "全部") {
          // 查看全部记录时：如果真实总记录数超过50条，则隐藏记录数 = 真实总记录数 - 50
          if (currentTemplateRealCount > 50) {
            hiddenRecordsCount = currentTemplateRealCount - 50;
          }
        } else {
          // 查看特定模板时：隐藏记录数 = 该模板的真实记录数 - 该模板在前50条中的可见记录数
          hiddenRecordsCount = currentTemplateRealCount - currentTemplateVisibleCount;
        }
      }
      // VIP用户没有隐藏记录
      
      log("Footer逻辑判断:", {
        isVIP,
        currentSelectedTemplate,
        currentTemplateVisibleCount,
        loadedRecordsCount,
        currentTemplateRealCount,
        hiddenRecordsCount
      });
      
      if (isVIP) {
        // VIP用户显示全部记录
        if (currentSelectedTemplate === "全部") {
          footerText = t("history.vipAllRecords", {
            count: loadedRecordsCount,
          });
        } else {
          // 针对特定模板的文案 - 使用多语言支持
          footerText = (t as any)("history.templateAllRecords", {
            template: getTemplateDisplayName(currentSelectedTemplate),
            count: loadedRecordsCount,
          });
        }
        log("Footer文案: VIP用户全部记录");
      } else {
        // 非VIP用户
        if (hiddenRecordsCount > 0) {
          // 有隐藏记录，显示限制提示
          if (currentSelectedTemplate === "全部") {
            footerText = t("history.freeUserLimitedRecords", {
              max: 50,
            });
          } else {
            footerText = (t as any)("history.templateLimitedRecords", {
              template: getTemplateDisplayName(currentSelectedTemplate),
              count: loadedRecordsCount,
            });
          }
          log("Footer文案: 免费用户限制记录");
        } else {
          // 没有隐藏记录
          if (currentSelectedTemplate === "全部") {
            footerText = t("history.freeUserAllRecords", {
              count: loadedRecordsCount,
            });
          } else {
            footerText = (t as any)("history.templateAllRecords", {
              template: getTemplateDisplayName(currentSelectedTemplate),
              count: loadedRecordsCount,
            });
          }
          log("Footer文案: 免费用户全部记录");
        }
      }

      return (
        <View>
          <View style={styles.footerContainer}>
            <Text
              style={[
                styles.footerText,
                { color: getColor("#6B7280", "#9BA1A6") },
              ]}
            >
              {footerText}
            </Text>
          </View>
          
          {/* 非VIP用户且有隐藏记录时显示升级引导 */}
          {!isVIP && hiddenRecordsCount > 0 && (
            <VipUpgradeCard 
              hiddenRecordsCount={hiddenRecordsCount}
              onUpgrade={() => {
                router.push('/payment');
              }}
            />
          )}
        </View>
      );
    }

    return null;
  };

  // 1. 新增：选择模式下隐藏Tab栏
  useEffect(() => {
    if (isSelectMode) {
      // 通过事件或全局状态通知_tab布局隐藏TabBar
      DeviceEventEmitter.emit("HIDE_TAB_BAR", true);
    } else {
      DeviceEventEmitter.emit("HIDE_TAB_BAR", false);
    }
  }, [isSelectMode]);

  // 在HistoryScreen组件内顶部（useState等之后）添加：
  const cardShiftAnim = useRef(new RNAnimated.Value(0)).current;
  useEffect(() => {
    RNAnimated.timing(cardShiftAnim, {
      toValue: isSelectMode ? 44 : 0,
      duration: 50,
      easing: Easing.inOut(Easing.ease),
      useNativeDriver: false,
    }).start();
  }, [isSelectMode]);

  // 使用useEffect来更新记忆值，只在progress真正变化时才会触发
  useEffect(() => {
    // 只有当progress值真正发生变化时才更新
    if (Math.abs(progress - memoizedProgress.current) > 2) { // 防止微小变化导致频繁更新，阈值改为2%
      memoizedProgress.current = progress;
    }
  }, [progress]);

  // 获取主题相关颜色
  const getColor = (lightColor: string, darkColor: string) => {
    return isDark ? darkColor : lightColor;
  };

  // 确保CopyBadge在页面最上层
  // 只有在明确需要生物识别认证但尚未认证成功时，才显示认证界面
  if (biometricAuthRequired && !isBiometricAuthenticated) {
    return (
      <GestureHandlerRootView style={{ flex: 1 }}>
        <ThemedSafeAreaView
          style={[
            styles.container,
            { backgroundColor: getColor("white", "#151718") },
          ]}
          edges={["top"]}
        >
          <StatusBar style={isDark ? "light" : "dark"} />

          <View style={styles.authContainer}>
            <View style={styles.authContent}>
              <IconSymbol
                name="lock.shield"
                size={64}
                color={getColor("#6a5ae1", "#8364e2")}
              />
              <ThemedText style={[styles.authTitle, { color: getColor("#333", "#ECEDEE") }]}>
                {t('settings.appLock.authPage.appLocked' as any)}
              </ThemedText>
              <ThemedText style={[styles.authSubtitle, { color: getColor("#666", "#9BA1A6") }]}>
                {t('settings.appLock.authPage.verifyIdentity' as any)}
              </ThemedText>

              {isBiometricAuthenticating && (
                <View style={styles.authLoadingContainer}>
                  <ActivityIndicator size="large" color={getColor("#6a5ae1", "#8364e2")} />
                  <ThemedText style={[styles.authLoadingText, { color: getColor("#666", "#9BA1A6") }]}>
                    {t('settings.appLock.authPage.authenticating' as any)}
                  </ThemedText>
                </View>
              )}

              {!isBiometricAuthenticating && (
                <TouchableOpacity
                  style={[styles.authButton, { backgroundColor: getColor("#6a5ae1", "#8364e2") }]}
                  onPress={() => checkAndPerformBiometricAuth(true)}
                >
                  <ThemedText style={styles.authButtonText}>
                    {t('settings.appLock.authPage.retryAuth' as any)}
                  </ThemedText>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </ThemedSafeAreaView>
      </GestureHandlerRootView>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ThemedSafeAreaView
        style={[
          styles.container,
          { backgroundColor: getColor("white", "#151718") },
        ]}
        edges={["top"]}
      >
        <StatusBar style={isDark ? "light" : "dark"} />

        {/* 列表视图 */}
        <ThemedView style={styles.listViewContainer}>
          {/* 页面标题和操作按钮 */}
          <ThemedView
            style={[
              styles.headerWrapper,
              {
                backgroundColor: getColor("white", "#151718"),
                borderBottomColor: getColor("#f0f0f0", "#2B2F31"),
              },
            ]}
          >
            <View style={styles.headerTitleContainer}>
              <ThemedText
                style={[styles.title, { color: getColor("#333", "#ECEDEE") }]}
              >
                {t("history.title")}
              </ThemedText>

              {/* 批量操作按钮 */}
              {isSelectMode ? (
                <View style={styles.batchActionContainer}>
                  <TouchableOpacity
                    style={styles.headerActionButton}
                    onPress={toggleSelectAll}
                  >
                    <ThemedText
                      style={[
                        styles.headerActionText,
                        { color: getColor("#6a5ae1", "#61dafb") },
                      ]}
                    >
                      {selectedCount > 0 &&
                      selectedCount === Object.values(groupedData).flat().length
                        ? t("history.deselectAll")
                        : t("history.selectAll")}
                    </ThemedText>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.headerActionButton}
                    onPress={exitSelectMode}
                  >
                    <ThemedText
                      style={[
                        styles.headerActionText,
                        { color: getColor("#6B7280", "#9BA1A6") },
                      ]}
                    >
                      {t("history.cancel")}
                    </ThemedText>
                  </TouchableOpacity>
                </View>
              ) : Object.keys(groupedData).length > 0 ? (
                <TouchableOpacity
                  style={styles.headerActionButton}
                  onPress={enterSelectMode}
                >
                  <IconSymbol
                    name="checkmark.circle"
                    size={20}
                    color={getColor("#6a5ae1", "#61dafb")}
                  />
                  <ThemedText
                    style={[
                      styles.headerActionText,
                      { color: getColor("#6a5ae1", "#61dafb") },
                    ]}
                  >
                    {t("history.select")}
                  </ThemedText>
                </TouchableOpacity>
              ) : (
                <View style={styles.headerActionButton} />
              )}
            </View>
          </ThemedView>

          {/* 标签筛选区域 - 仅在有记录时显示 */}
          {(currentTemplateRealCount > 0 || loading) && (
            <ThemedView
              style={[
                styles.filterTabsOuterWrapper,
                {
                  backgroundColor: getColor("white", "#151718"),
                  borderBottomColor: getColor("#f0f0f0", "#2B2F31"),
                },
              ]}
            >
              <ScrollView
                horizontal={true}
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.filterTabsContent}
                bounces={false}
                style={styles.filterTabsScroll}
                alwaysBounceHorizontal={false}
                alwaysBounceVertical={false}
                scrollEnabled={true}
                scrollEventThrottle={16}
              >
                {dynamicFilterTabs.map((tab) => renderFilterTab(tab))}
              </ScrollView>
            </ThemedView>
          )}

          {/* 空状态或VIP升级引导 */}
          {!loading &&
          initialLoadDone &&
          Object.keys(groupedData).length === 0 ? (
            // 检查是否有真实记录但因VIP限制而不可见
            !isVIP && currentTemplateRealCount > 0 ? (
              <View style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'stretch',
                paddingHorizontal: 16,
                paddingVertical: 20,
                marginBottom: 200,
              }}>
                <VipUpgradeCard 
                  hiddenRecordsCount={currentTemplateRealCount}
                  onUpgrade={handleUpgradeVIP}
                />
              </View>
            ) : (
              // 真正的空状态
              <View style={styles.emptyContainer}>
                <View
                  style={[
                    styles.emptyIconContainer,
                    { backgroundColor: getColor("#F3F4F6", "#2B2F31") },
                  ]}
                >
                  <IconSymbol
                    name="text.bubble"
                    size={48}
                    color={getColor("#9CA3AF", "#6B7280")}
                  />
                </View>
                <Text
                  style={[
                    styles.emptyStateText,
                    { color: getColor("#4B5563", "#9BA1A6") },
                  ]}
                >
                  {t("history.emptyStateTitle")}
                </Text>
                <Text
                  style={[
                    styles.emptyStateSubtext,
                    { color: getColor("#6B7280", "#868E96") },
                  ]}
                >
                  {t("history.emptyStateSubtitle")}
                </Text>
                <TouchableOpacity
                  style={[
                    styles.startRecordingButton,
                    { backgroundColor: getColor("#6a5ae1", "#7C4DFF") },
                  ]}
                  onPress={() => {
                    // 切换到输入标签页
                    router.replace("/(tabs)");

                    // 使用setTimeout确保页面切换完成后再开始录音
                    setTimeout(() => {
                      // 发布自定义事件，通知输入页面开始录音
                      DeviceEventEmitter.emit("START_RECORDING");
                    }, 500);
                  }}
                >
                  <IconSymbol name="mic.fill" size={16} color="white" />
                  <Text style={styles.startRecordingButtonText}>
                    {t("history.startRecording")}
                  </Text>
                </TouchableOpacity>
              </View>
            )
          ) : loading && !isLoadingMore && !initialLoadDone ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#6a5ae1" />
              <Text
                style={[
                  styles.loadingText,
                  { color: getColor("#6B7280", "#9BA1A6") },
                ]}
              >
                {t("history.loading")}
              </Text>
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <Text
                style={[
                  styles.errorText,
                  { color: getColor("#EF4444", "#F87171") },
                ]}
              >
                {t("common.error")}: {error}
              </Text>
              <TouchableOpacity
                style={styles.retryButton}
                onPress={refreshData}
              >
                <Text style={styles.retryButtonText}>{t("common.retry")}</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <FlatList
              data={Object.entries(groupedData).sort(([a], [b]) => {
                // 按日期排序，"今天" > "昨天" > "前天" > 具体日期
                const sortOrder: Record<string, number> = {
                  [t("history.today")]: 3,
                  [t("history.yesterday")]: 2,
                  [t("history.dayBeforeYesterday")]: 1,
                };
                return (sortOrder[b] || 0) - (sortOrder[a] || 0);
              })}
              keyExtractor={([date]) => date}
              renderItem={({ item: [date, items] }) => (
                <View style={styles.dateGroupContainer}>
                  <Text
                    style={[
                      styles.sectionHeader,
                      { color: getColor("#4B5563", "#9BA1A6") },
                    ]}
                  >
                    {date}
                  </Text>
                  {(items as HistoryItem[]).map(renderHistoryItem)}
                </View>
              )}
              contentContainerStyle={styles.flatListContentContainer}
              style={styles.flatListStyle}
              showsVerticalScrollIndicator={true}
              onEndReached={handleEndReached}
              onEndReachedThreshold={0.3} // 当距离底部还有30%时触发加载更多
              ListFooterComponent={renderFooter}
              bounces={true}
              scrollEnabled={true}
              scrollEventThrottle={16}
              onScroll={handleScrollThrottled}
              refreshControl={
                <RefreshControl
                  refreshing={isRefreshing}
                  onRefresh={handleRefresh}
                  colors={["#6a5ae1"]}
                  tintColor={isDark ? "#61dafb" : "#6a5ae1"}
                />
              }
            />
          )}
        </ThemedView>

        {/* 批量删除按钮 */}
        {isSelectMode && selectedCount > 0 && (
          <View
            style={[
              styles.batchDeleteContainer,
              { bottom: insets.bottom > 0 ? insets.bottom : 16 },
            ]}
          >
            <TouchableOpacity
              style={styles.batchDeleteButton}
              onPress={handleBatchDelete}
              activeOpacity={0.8}
            >
              <IconSymbol name="trash" size={20} color="white" />
              <Text style={styles.batchDeleteText}>
                {t("history.deleteSelected")} ({selectedCount})
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* 快捷操作弹出菜单 */}
        {showQuickActions && actionButtonPosition && (
          <Pressable
            style={[styles.quickActionsOverlay]}
            onPress={hideQuickActions}
          >
            <View
              style={[
                styles.quickActionsMenu,
                {
                  backgroundColor: getColor("#fff", "#1E2122"),
                  shadowColor: getColor("#000", "#000"),
                  borderColor: getColor("#e5e7eb", "#2B2F31"),
                  position: "absolute",
                  left: actionButtonPosition.x,
                  top: actionButtonPosition.y - 110,
                },
              ]}
            >
              <TouchableOpacity
                style={styles.quickActionItem}
                onPress={() => {
                  if (actionItemId) {
                    // 查找对应的历史记录项
                    let targetItem: HistoryItem | undefined;
                    for (const group of Object.values(groupedData)) {
                      const found = (group as HistoryItem[]).find(
                        (item: HistoryItem) => item.id === actionItemId
                      );
                      if (found) {
                        targetItem = found;
                        break;
                      }
                    }

                    if (targetItem) {
                      shareOptimizedText(targetItem);
                    }
                  }
                }}
              >
                <IconSymbol
                  name="square.and.arrow.up"
                  size={16}
                  color={getColor("#3B82F6", "#60A5FA")}
                />
                <Text
                  style={[
                    styles.quickActionText,
                    { color: getColor("#333", "#ECEDEE") },
                  ]}
                >
                  {t("history.share")}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.quickActionItem}
                onPress={() => {
                  if (actionItemId) {
                    // 查找对应的历史记录项
                    let targetText = "";
                    for (const group of Object.values(groupedData)) {
                      const found = (group as HistoryItem[]).find(
                        (item: HistoryItem) => item.id === actionItemId
                      );
                      if (found) {
                        targetText = found.optimizedText;
                        break;
                      }
                    }

                    if (targetText) {
                      copyOptimizedText(targetText);
                    }
                  }
                  hideQuickActions();
                }}
              >
                <IconSymbol
                  name="doc.on.doc"
                  size={16}
                  color={getColor("#10B981", "#4ADE80")}
                />
                <Text
                  style={[
                    styles.quickActionText,
                    { color: getColor("#333", "#ECEDEE") },
                  ]}
                >
                  {t("history.copy")}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.quickActionItem}
                onPress={() => {
                  if (actionItemId) {
                    handleDeleteHistoryItem(actionItemId);
                  }
                }}
              >
                <IconSymbol
                  name="trash"
                  size={16}
                  color={getColor("#EF4444", "#F87171")}
                />
                <Text
                  style={[
                    styles.quickActionText,
                    { color: getColor("#333", "#ECEDEE") },
                  ]}
                >
                  {t("history.delete")}
                </Text>
              </TouchableOpacity>
            </View>
          </Pressable>
        )}

        {/* 登录提示对话框 */}
        <LoginPrompt
          visible={loginPromptVisible}
          title={loginPromptTitle}
          message={loginPromptMessage}
          onLogin={handleLoginSuccess}
          onClose={hideLoginPrompt}
        />
      </ThemedSafeAreaView>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listViewContainer: {
    flex: 1,
    flexDirection: "column",
  },
  headerWrapper: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    minHeight: 58,
  },
  headerTitleContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
  },
  headerActionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: 'center',
    height: 16, // 固定高度
    paddingHorizontal: 6,
    borderRadius: 6,
  },
  headerActionText: {
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 4,
  },
  batchActionContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12, // 减少按钮之间的间距
    height: 16, // 固定高度
  },
  filterTabsOuterWrapper: {
    height: 52,
    borderBottomWidth: 1,
    overflow: "hidden", // 确保内容不会溢出
    width: "100%",
  },
  filterTabsScroll: {
    maxHeight: 52, // 限制最大高度
    height: 52, // 固定高度
  },
  filterTabsContent: {
    paddingHorizontal: 16,
    paddingRight: 24,
    flexDirection: "row",
    alignItems: "center",
    height: 52,
    gap: 12,
    flexWrap: "nowrap", // 确保不换行
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  filterTabDark: {
    backgroundColor: '#26292B',
    borderColor: '#2B2F31',
  },
  filterTabSelected: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6a5ae1',
  },
  filterTabSelectedDark: {
    backgroundColor: '#312e81',
    borderColor: '#6a5ae1',
  },
  filterTabText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  filterTabTextDark: {
    color: '#9BA1A6',
  },
  filterTabTextSelected: {
    color: '#6a5ae1',
    fontWeight: '600',
  },
  filterTabTextSelectedDark: {
    color: '#c7d2fe',
  },
  historyListContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  dateGroupContainer: {
    marginBottom: 20,
    marginTop: 16, // 增加日期标题与上方的间距
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
    marginTop: 8, // 增加日期标题内部上边距
  },
  swipeableContainer: {
    marginBottom: 16, // 设置滑动条目之间的间距
    borderRadius: 12,
    overflow: "hidden", // 确保内容不会溢出圆角
  },
  // 滑动操作相关样式已在下方定义
  historyItem: {
    borderRadius: 12,
    padding: 16,
    paddingVertical: 14,
    elevation: 2,
    borderWidth: 1,
  },
  historyItemOpen: {
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
  },
  historyItemHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  historyItemTitle: {
    fontSize: 16,
    fontWeight: "500",
  },
  historyItemTime: {
    fontSize: 12,
    marginTop: 2,
  },
  audioPlayer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
    marginTop: 2,
  },
  audioPlayerSmall: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 4,
  },
  playButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#6a5ae1",
    justifyContent: "center",
    alignItems: "center",
  },
  progressBarContainer: {
    flex: 1,
    marginHorizontal: 8,
    position: "relative",
  },
  sliderContainer: {
    width: "100%",
    height: 24,
    justifyContent: "center",
    paddingVertical: 10,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: "visible",
    position: "relative",
  },
  progress: {
    height: "100%",
    borderRadius: 2,
  },
  progressThumb: {
    position: "absolute",
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: "#6a5ae1",
    top: -4,
    marginLeft: -6,
    borderWidth: 2,
    borderColor: "#fff",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  contentSummary: {
    marginBottom: 8,
    position: "relative",
  },
  contentSummaryText: {
    fontSize: 14,
    lineHeight: 20,
  },
  truncatedText: {
    overflow: "hidden",
    maxHeight: 60, // 约3行文字的高度
  },
  expandBtnWrapper: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 6,
    marginBottom: 2,
  },
  expandButtonWithBorder: {
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 12,
  },
  expandIndicator: {
    position: "absolute",
    right: 0,
    bottom: 0,
    paddingLeft: 30,
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderTopLeftRadius: 12,
  },
  expandIndicatorText: {
    fontSize: 13,
    fontWeight: "500",
  },
  historyItemFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 10,
  },
  tagContainer: {
    flexDirection: "row",
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 6,
  },
  tagDefault: {
    backgroundColor: "#F3F4F6",
    color: "#6B7280",
  },
  tagText: {
    fontSize: 12,
    fontWeight: "500",
  },
  actionButtons: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButton: {
    padding: 6,
    marginLeft: 6,
    borderRadius: 6,
    backgroundColor: "transparent",
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingBottom: 80, // 添加底部内边距以适应底部标签栏
  },
  emptyStateText: {
    fontSize: 14,
    marginTop: 20, // 增加与图标的间距
  },
  emptyStateSubtext: {
    fontSize: 12,
    marginTop: 12, // 增加与标题的间距
    textAlign: "center",
    paddingHorizontal: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  emptyIconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
  },
  startRecordingButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 24,
    marginTop: 32,
  },
  startRecordingButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },

  // 快捷操作菜单样式
  quickActionsOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "transparent",
    zIndex: 1000,
  },
  quickActionsMenu: {
    borderRadius: 12,
    elevation: 5,
    zIndex: 1001,
    width: 120,
    borderWidth: 1,
  },
  quickActionItem: {
    padding: 12,
    flexDirection: "row",
    alignItems: "center",
  },
  quickActionText: {
    marginLeft: 10,
    fontWeight: "500",
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 32,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 32,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 16,
  },
  retryButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: "#6a5ae1",
    borderRadius: 8,
  },
  retryButtonText: {
    color: "white",
    fontWeight: "500",
  },
  footerContainer: {
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  footerText: {
    fontSize: 14,
    marginLeft: 8,
    textAlign: "center",
  },
  swipeRightAction: {
    backgroundColor: "#FF5F57",
    justifyContent: "center",
    alignItems: "center",
    width: 80,
    height: "100%",
  },
  deleteButton: {
    justifyContent: "center",
    alignItems: "center",
    flex: 1,
  },
  deleteButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "600",
  },
  contentContainer: {
    paddingBottom: 100,
  },
  contentContainerStyle: {
    paddingBottom: 20,
    paddingRight: 16,
  },
  dateHeader: {
    fontSize: 12,
    marginBottom: 10,
    marginLeft: 2,
  },
  flatListStyle: {
    width: "100%",
    flex: 1,
  },
  flatListContentContainer: {
    paddingLeft: 16,
    paddingRight: 16,
    flexGrow: 1,
    paddingBottom: 120, // 增加底部空间确保所有内容都能滑动查看
    paddingTop: 8, // 为整个列表增加顶部内边距
  },
  secondaryOptimizationItem: {
    // 二次优化记录的卡片边框样式
    borderLeftWidth: 3,
    borderLeftColor: "#6a5ae1", // 紫色边框标记
  },
  secondaryOptimizationBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    backgroundColor: "#6a5ae1",
    borderRadius: 4,
    justifyContent: "center",
    alignItems: "center",
  },
  secondaryOptimizationBadgeText: {
    color: "white",
    fontSize: 10,
    fontWeight: "600",
  },
  checkbox: {
    width: 22,
    height: 22,
    borderRadius: 11,
    borderWidth: 2,
    borderColor: "#6a5ae1",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: undefined, // 透明
    shadowColor: "#000",
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
    marginLeft: 8,
  },
  checkboxSelected: {
    backgroundColor: "#6a5ae1",
    borderColor: "#6a5ae1",
  },
  selectedHistoryItem: {
    borderWidth: 2,
    borderColor: "#6a5ae1",
  },
  batchDeleteContainer: {
    position: "absolute",
    left: 0,
    right: 0,
    alignItems: "center",
    paddingHorizontal: 16,
    zIndex: 1000,
  },
  batchDeleteButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FF5F57",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
    width: "100%",
    maxWidth: 500,
  },
  batchDeleteText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  checkboxAbsolute: {
    position: "absolute",
    left: 0,
    top: "50%",
    marginTop: -16,
    zIndex: 2,
  },
  // 生物识别认证相关样式
  authContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  authContent: {
    alignItems: 'center',
    maxWidth: 300,
  },
  authTitle: {
    fontSize: 24,
    fontWeight: '600',
    marginTop: 24,
    marginBottom: 8,
    textAlign: 'center',
  },
  authSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  authLoadingContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  authLoadingText: {
    fontSize: 14,
    marginTop: 12,
  },
  authButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  authButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
