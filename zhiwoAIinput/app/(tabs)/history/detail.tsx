import React, { useState, useEffect, useCallback } from "react";
import {
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Share,
  Alert,
  Clipboard,
  PanResponder,
  GestureResponderEvent,
  PanResponderGestureState,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  ActivityIndicator,
  Dimensions
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { useLocalSearchParams, router } from "expo-router";
import { useTranslation } from 'react-i18next';
import * as Haptics from 'expo-haptics';
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useAppTheme } from "@/hooks/useAppTheme";
import { ThemedText as Text } from "@/components/ThemedText";
import { ThemedView as View } from "@/components/ThemedView";
import { useHistory } from '@/hooks/useHistory';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';
import { useTemplates } from '@/hooks/useTemplates';
import { useState as useReactState } from 'react';
import { Modal, TextInput } from "react-native";
import { useSpeechRecording } from '@/hooks/useSpeechRecording';
import { useDispatch } from 'react-redux';
import TemplateSelector from '@/components/input/TemplateSelector';
import Animated, { 
  useAnimatedStyle, 
  useSharedValue, 
  withTiming, 
  runOnJS, 
  Easing,
  interpolate,
  withSpring,
  useAnimatedGestureHandler,
  Extrapolate
} from 'react-native-reanimated';
import { PanGestureHandler, GestureHandlerRootView } from 'react-native-gesture-handler';
import Toast from 'react-native-toast-message';
import { showCenteredToast, registerBadgeShowFunction, showCopyBadge } from '@/utils/toastConfig';
import CopyBadge from "@/components/ui/CopyBadge";
import * as HistoryService from '@/services/storageService';
import { ShareService } from '@/services/shareService';
import { log, error as logError, warn } from "@/services/logService";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useAuthModal } from "@/components/AuthProvider";
import { getTemplateSemanticId } from "@/utils/templateI18nUtils";
import { useAuth } from "@/hooks/useAuth";
import VipUpgradeModal from "@/components/VipUpgradeModal";
import { LoginPrompt } from "@/components/LoginPrompt";
import { getTemplateDisplayName as getTemplateDisplayNameUtil } from "@/utils/templateI18nUtils";
import { getAudioFileAbsolutePath } from '@/utils/inputUtils';

/**
 * 安全地调用触觉反馈，在web平台上会自动跳过
 */
const safeHaptics = {
  impactAsync: async (style: Haptics.ImpactFeedbackStyle) => {
    if (Platform.OS !== "web") {
      try {
        await Haptics.impactAsync(style);
      } catch (error) {
        console.log("触觉反馈失败，但不影响功能", error);
      }
    }
  },
  notificationAsync: async (type: Haptics.NotificationFeedbackType) => {
    if (Platform.OS !== "web") {
      try {
        await Haptics.notificationAsync(type);
      } catch (error) {
        console.log("触觉反馈失败，但不影响功能", error);
      }
    }
  },
};

// 类型定义
interface HistoryItem {
  id: string;
  title: string;
  time: string;
  date: string;
  tags: string[];
  audioDuration: string;
  audioProgress: number;
  originalText: string;
  optimizedText: string;
  dateGroup: string;
  expanded?: boolean;
  audioUri?: string; // 音频文件URI
  optimizedResults?: OptimizedResultDisplay[]; // 添加优化结果数组
}

// 用于显示的优化结果类型
interface OptimizedResultDisplay {
  text: string;
  timestamp: number;
  templateId: string;
  templateName: string;
  displayTime?: string; // 用于展示的格式化时间
}

interface TimeStampedText {
  text: string;
  timestamp: string;
}

/**
 * 历史详情页面
 * 显示单条历史记录的详细内容
 */
export default function HistoryDetailScreen() {
  // 获取路由参数
  const params = useLocalSearchParams();
  const itemId = params.id as string;
  
  // 获取底部安全区域高度
  const insets = useSafeAreaInsets();
  const theme = useAppTheme();
  const { t } = useTranslation();
  
  // 使用历史记录钩子
  const { selectedRecord, getRecordById, selectRecord } = useHistory();
  
  // 获取模板显示名称
  const getTemplateDisplayName = useCallback((templateName: string): string => {
    // 获取semantic_id
    const semanticId = getTemplateSemanticId(templateName);
    if (semanticId) {
      // 使用正确的翻译键格式
      const nameKey = `template.${semanticId}.name` as const;
      const translatedValue = t(nameKey);
      
      // 如果翻译成功且不等于key本身，使用翻译结果
      if (typeof translatedValue === 'string' && translatedValue !== nameKey) {
        return translatedValue;
      }
    }
    
    // 如果没有找到翻译或翻译失败，返回原始名称
    return templateName;
  }, [t]);
  
  // 音频播放钩子
  const { 
    isPlaying, 
    progress, 
    loadAudio, 
    togglePlay, 
    seekTo, 
    formatTime,
    position,
    duration,
    audioUri,
    isLoading,
    error,
    sound,
    stopPlayback
  } = useAudioPlayer();
  
  // 使用Redux dispatch
  const dispatch = useDispatch();
  
  // 使用语音识别钩子获取优化函数
  const { optimizeText, getLatestOptimizedText } = useSpeechRecording();
  
  // 使用模板钩子获取模板数据
  const { 
    templates, 
    selectedTemplate,
    selectTemplate: selectTemplateFromHook
  } = useTemplates();
  
  // 获取auth相关状态和方法
  const { checkIsLoggedIn } = useAuth();
  const { showLoginModal } = useAuthModal();
  
  // 从Redux获取用户状态
  const {
    user,
    isAuthenticated,
    isVIP: reduxIsVIP,
  } = useSelector((state: RootState) => state.auth);
  
  // 状态管理
  const [selectedItem, setSelectedItem] = useState<HistoryItem | null>(null);
  const [isReoptimized, setIsReoptimized] = useState(false);
  const [sourceRecordId, setSourceRecordId] = useState<string | null>(null);
  
  // 模板选择模态框状态
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  // 二次优化状态
  const [isOptimizing, setIsOptimizing] = useState(false);
  // 选中的模板ID
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
  // 选择使用的文本源：原始文本(original)或优化后文本(optimized)
  const [textSource, setTextSource] = useState<'original' | 'optimized'>('optimized');
  // 编辑中的文本
  const [editingText, setEditingText] = useState('');
  // 模态窗口动画完成状态
  const [modalAnimationComplete, setModalAnimationComplete] = useState(false);
  
  // 分享选择面板状态
  const [showShareModal, setShowShareModal] = useState(false);
  // 分享面板动画完成状态
  const [shareModalAnimationComplete, setShareModalAnimationComplete] = useState(false);
  
  // 音频播放器所需的钩子 - 这些钩子需要放在组件顶层，不能放在条件渲染中
  const [isDragging, setIsDragging] = useState(false);
  const [sliderWidth, setSliderWidth] = useState(0);
  const [dragProgress, setDragProgress] = useState(progress);
  const [sliderPosition, setSliderPosition] = useState(0);
  
  // 强制刷新记录数据的标志
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  
  // VIP升级相关状态
  const [vipUpgradeModalVisible, setVipUpgradeModalVisible] = useState(false);
  const [vipUpgradeScene, setVipUpgradeScene] = useState<string | null>(null);
  const [selectedVipTemplate, setSelectedVipTemplate] = useState<any | null>(null);
  
  // 登录提示相关状态
  const [loginPromptVisible, setLoginPromptVisible] = useState(false);
  const [loginPromptTitle, setLoginPromptTitle] = useState("");
  const [loginPromptMessage, setLoginPromptMessage] = useState("");
  const [pendingVipUpgrade, setPendingVipUpgrade] = useState(false);

  // 操作面板状态
  const [isActionPanelExpanded, setIsActionPanelExpanded] = useState(true); // 初始状态为展开
  const [scrollTimeout, setScrollTimeout] = useState<NodeJS.Timeout | null>(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false); // 用户是否正在滑动
  const [lastScrollTime, setLastScrollTime] = useState(0); // 最后一次滑动时间
  const [pendingExpand, setPendingExpand] = useState(false); // 是否有待执行的展开操作
  
  // VIP升级场景枚举
  enum VipUpgradeScene {
    TEMPLATE_USAGE = "template_usage", // 使用VIP模板进行二次优化
  }
  
  // 动画值
  const modalTranslateY = useSharedValue(300);
  const modalBackgroundOpacity = useSharedValue(0);
  // 分享面板动画值
  const shareModalTranslateY = useSharedValue(300);
  const shareModalBackgroundOpacity = useSharedValue(0);
  // 操作面板动画值
  const actionPanelTranslateX = useSharedValue(0); // 0为展开状态，正值为收起状态
  const actionPanelOpacity = useSharedValue(1);
  // 浮动按钮脉冲动画值
  const floatingButtonScale = useSharedValue(1);
  
  // 创建模态框的动画样式
  const modalAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: modalTranslateY.value }]
    };
  });

  // 创建模态框背景的动画样式
  const modalBackgroundAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: modalBackgroundOpacity.value
    };
  });
  
  // 创建分享面板的动画样式
  const shareModalAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: shareModalTranslateY.value }]
    };
  });

  // 创建分享面板背景的动画样式
  const shareModalBackgroundAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: shareModalBackgroundOpacity.value
    };
  });

  // 创建操作面板的动画样式
  const actionPanelAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: actionPanelTranslateX.value }],
      opacity: actionPanelOpacity.value
    };
  });

  // 创建浮动按钮的动画样式
  const floatingButtonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: floatingButtonScale.value }]
    };
  });
  
  // 音频进度同步
  useEffect(() => {
    if (!isDragging) {
      setDragProgress(progress);
    }
  }, [progress, isDragging]);
  
  // 强制刷新记录数据，确保获取到最新的optimizedResults - 移到组件顶层
  useEffect(() => {
    const refreshRecord = async () => {
      if (selectedItem && selectedItem.id) {
        log('手动刷新历史记录:', selectedItem.id);
        try {
          const record = await HistoryService.getHistoryRecordById(selectedItem.id);
          log('手动刷新结果:', {
            有优化结果数组: !!record?.optimizedResults,
            优化结果数量: record?.optimizedResults?.length || 0
          });
          
          // 如果记录存在且有新的优化结果，更新selectedItem
          if (record && record.optimizedResults && record.optimizedResults.length > 0) {
            const optimizedResultsDisplay = record.optimizedResults.map(result => ({
              ...result,
              displayTime: new Date(result.timestamp).toLocaleString([], { 
                hour: '2-digit', 
                minute: '2-digit',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
              })
            })).sort((a, b) => b.timestamp - a.timestamp);
            
            setSelectedItem(prev => prev ? {
              ...prev,
              optimizedResults: optimizedResultsDisplay,
              optimizedText: record.optimizedText
            } : null);
          }
        } catch (error) {
          logError('刷新历史记录失败:', error);
        }
      }
    };
    
    refreshRecord();
  }, [selectedItem?.id, refreshTrigger]);
  
  // 原始音频的分段文本（带时间戳）- 模拟数据，实际应从API获取
  const originalTimestampedText: { [key: string]: TimeStampedText[] } = {
    "1": [
      {
        timestamp: "0:05",
        text: "今天和张总讨论了新项目的进展情况，他对我们的工作表示满意，但是有几点需要注意的地方。",
      },
      {
        timestamp: "0:20",
        text: "第一，项目进度需要加快，目前的进度比计划慢了大约一周时间。",
      },
      { timestamp: "0:35", text: "第二，用户反馈需要更加及时地收集和分析。" },
      {
        timestamp: "0:45",
        text: "第三，下周二之前需要提交一份详细的项目报告，包括目前的进展、遇到的问题以及解决方案。",
      },
    ],
    "2": [
      {
        timestamp: "0:08",
        text: "今天早上的产品讨论很有收获。",
      },
      {
        timestamp: "0:15",
        text: "我们确定了三个关键功能：第一，用户可以自定义模板。",
      },
      { 
        timestamp: "0:30", 
        text: "第二，支持语音和文字输入。" 
      },
      {
        timestamp: "0:42",
        text: "第三，历史记录可以按标签筛选和搜索。",
      },
      {
        timestamp: "1:00",
        text: "下一步我们需要确定开发时间表和资源分配。",
      },
    ],
    "3": [
      {
        timestamp: "0:05",
        text: "最近终于搞定了那个项目，",
      },
      {
        timestamp: "0:12",
        text: "这周末有空出来聚一聚吗？",
      },
      { 
        timestamp: "0:20", 
        text: "我们可以去尝试那家新开的餐厅，听说评价很好。" 
      },
    ],
    "4": [
      {
        timestamp: "0:10",
        text: "本季度销售额同比增长15%，超出预期目标3个百分点。",
      },
      {
        timestamp: "0:25",
        text: "新产品线贡献了30%的增长，",
      },
      { 
        timestamp: "0:35", 
        text: "客户满意度提升了8%。" 
      },
    ],
    "5": [
      {
        timestamp: "0:05",
        text: "这几天出差，每天都会想起你。",
      },
      {
        timestamp: "0:15",
        text: "期待周末回家。",
      },
    ],
  };
  
  // 获取历史记录详情
  useEffect(() => {
    const fetchRecord = async () => {
      // 使用真实的API获取记录
      await selectRecord(itemId);
    };
    
    fetchRecord();
    
    // 返回函数，用于清理工作
    return () => {
      // 清理工作，停止音频播放
      if (isPlaying && sound) {
        stopPlayback();
      }
      // 清理滑动定时器
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  }, [itemId, selectRecord, isPlaying, sound, stopPlayback]);
  
  // 当selectedRecord更新时，更新UI
  useEffect(() => {
    if (selectedRecord) {
      log('详情页收到了selectedRecord:', {
        id: selectedRecord.id,
        原始文本长度: selectedRecord.originalText?.length,
        优化文本长度: selectedRecord.optimizedText?.length,
        模板: selectedRecord.templateName,
        有优化结果数组: !!selectedRecord.optimizedResults,
        优化结果数量: selectedRecord.optimizedResults?.length || 0
      });
      
      // 处理优化结果数组，如果有多个结果
      let optimizedResultsDisplay: OptimizedResultDisplay[] = [];
      
      if (selectedRecord.optimizedResults && selectedRecord.optimizedResults.length > 0) {
        log('有优化结果数组，数量:', selectedRecord.optimizedResults.length);
        // 处理结果数组，按时间戳降序排序（最新的在前）
        optimizedResultsDisplay = selectedRecord.optimizedResults.map(result => ({
          ...result,
          displayTime: new Date(result.timestamp).toLocaleString([], { 
            hour: '2-digit', 
            minute: '2-digit',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          })
        })).sort((a, b) => b.timestamp - a.timestamp);
      } else {
        log('没有优化结果数组，创建一个默认的');
        // 如果没有结果数组，使用主记录创建一个
        optimizedResultsDisplay = [{
          text: selectedRecord.optimizedText,
          timestamp: selectedRecord.timestamp,
          templateId: selectedRecord.templateId,
          templateName: selectedRecord.templateName,
          displayTime: new Date(selectedRecord.timestamp).toLocaleString([], { 
            hour: '2-digit', 
            minute: '2-digit',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          })
        }];
      }
      
      // 转换selectedRecord为UI所需的HistoryItem格式
      const item: HistoryItem = {
        id: selectedRecord.id,
        title: getTemplateDisplayName(selectedRecord.templateName), // 使用翻译后的模板名称
        time: new Date(selectedRecord.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        date: new Date(selectedRecord.timestamp).toLocaleDateString(),
        tags: extractTagsFromTemplate(selectedRecord.templateName),
        audioDuration: "0:00",
        audioProgress: 0,
        originalText: selectedRecord.originalText,
        optimizedText: selectedRecord.optimizedText,
        dateGroup: "",
        audioUri: selectedRecord.audioUri,
        optimizedResults: optimizedResultsDisplay.map(result => ({
          ...result,
          templateName: getTemplateDisplayName(result.templateName) // 翻译优化结果中的模板名称
        }))
      };
      
      setSelectedItem(item);
      
      // 检查是否为二次优化生成的记录（只要包含sourceRecordId字段就是二次优化记录）
      if (selectedRecord.sourceRecordId) {
        setIsReoptimized(true);
        setSourceRecordId(selectedRecord.sourceRecordId);
        log('检测到二次优化记录:', {
          recordId: selectedRecord.id,
          sourceRecordId: selectedRecord.sourceRecordId,
          hasAudioUri: !!selectedRecord.audioUri
        });
      } else {
        setIsReoptimized(false);
        setSourceRecordId(null);
      }
      
      // 如果有音频文件，加载它
      if (selectedRecord.audioUri) {
        log('详情页准备加载音频:', selectedRecord.audioUri);

        // 获取修复后的音频路径
        const fixedAudioPath = getAudioFileAbsolutePath(selectedRecord.audioUri || '');

        // 只有当修复后的路径与当前加载的路径不同时才重新加载
        if (fixedAudioPath && fixedAudioPath !== audioUri) {
          log('音频路径已修复，准备加载:', fixedAudioPath);

          // 尝试加载音频
          loadAudio(fixedAudioPath)
            .then(result => {
              if (result) {
                log('详情页音频加载成功');
              } else {
                warn('详情页音频加载未成功完成');
              }
            })
            .catch(error => {
              logError('详情页加载音频出错:', error);
            });
        } else {
          log('音频路径未变化，跳过重新加载');
        }
      }
    }
  }, [selectedRecord, loadAudio, audioUri, getTemplateDisplayName]);
  
  // 从模板名称提取标签
  const extractTagsFromTemplate = (templateName: string): string[] => {
    // 检查 templateName 是否存在
    if (!templateName) {
      return [];
    }
    // 根据模板名称返回相应的标签
    switch (templateName.toLowerCase()) {
      case "邮件格式":
        return ["工作", "邮件"];
      case "emoji风格":
        return ["聊天"];
      case "领导汇报":
        return ["工作", "会议"];
      case "朋友聊天":
        return ["聊天"];
      case "恋人表达":
        return ["个人"];
      case "学术论文":
        return ["工作"];
      case "演讲稿":
        return ["工作"];
      case "新闻报道":
        return ["工作"];
      case "故事叙述":
        return ["个人"];
      default:
        return [];
    }
  };
  
  // 返回列表页面
  const handleBackToList = () => {
    // 返回前停止音频播放
    if (isPlaying && sound) {
      stopPlayback();
    }
    router.back();
  };
  
  // 处理音频播放控制
  const handleTogglePlay = async () => {
    await togglePlay();
  };
  
  // 处理音频进度调整
  const handleSeek = async (percent: number) => {
    await seekTo(percent);
  };
  
  // 显示分享选择面板
  const showShareOptions = () => {
    // 先设置状态显示Modal
    setShowShareModal(true);
    // 立即开始动画，不需要延迟
    shareModalBackgroundOpacity.value = withTiming(1, {
      duration: 200,
      easing: Easing.out(Easing.quad)
    });
    shareModalTranslateY.value = withTiming(0, {
      duration: 280,
      easing: Easing.out(Easing.cubic)
    }, (finished) => {
      if (finished) {
        // 使用runOnJS在JS线程上安全更新React状态
        runOnJS(setShareModalAnimationComplete)(true);
      }
    });
  };

  // 获取要分享的优化文本
  const getOptimizedTextForSharing = (): string => {
    if (!selectedItem) return '';
    
    if (selectedItem.optimizedResults && selectedItem.optimizedResults.length > 1) {
      // 如果有多个优化结果，分享最新的一个结果
      const latestResult = selectedItem.optimizedResults[0];
      return latestResult.text;
    }
    
    // 默认分享当前选中记录的优化文本
    return selectedItem.optimizedText;
  };

  // 分享原始内容 - 优化版本
  const shareOriginalContent = async () => {
    if (!selectedItem?.originalText) {
      Alert.alert(t('common.error'), '没有原始内容可分享');
      return;
    }

    try {
      // 准备要分享的内容
      const shareContent = {
        message: selectedItem.originalText,
        title: `${selectedItem.title} - 原始内容`
      };

      // 执行分享操作
      const result = await Share.share({
        message: shareContent.message,
        title: shareContent.title
      });
      
      // 分享完成后再关闭面板
      if (result.action === Share.sharedAction) {
        handleCloseShareModal();
      }
    } catch (error) {
      logError('分享原始内容失败:', error);
      Alert.alert(t('common.error'), t('common.retry'));
    }
  };

  // 分享AI优化内容 - 优化版本
  const shareOptimizedContent = async () => {
    const optimizedText = getOptimizedTextForSharing();
    
    if (!optimizedText) {
      Alert.alert(t('common.error'), '没有优化内容可分享');
      return;
    }

    try {
      // 准备要分享的内容
      let title = selectedItem?.title || '知我AI输入法';
      if (selectedItem?.optimizedResults && selectedItem.optimizedResults.length > 1) {
        title = `${title} - ${t('history.detail.optimizationHistory')}`;
      }
      
      // 执行分享操作
      const result = await Share.share({
        message: optimizedText,
        title: `${title} - AI优化内容`
      });
      
      // 分享完成后再关闭面板
      if (result.action === Share.sharedAction) {
        handleCloseShareModal();
      }
    } catch (error) {
      logError('分享优化内容失败:', error);
      Alert.alert(t('common.error'), t('common.retry'));
    }
  };

  // 分享原始音频 - 优化版本
  const shareOriginalAudio = async () => {
    if (!selectedItem?.audioUri) {
      Alert.alert(t('common.info'), '该记录没有原始音频');
      return;
    }

    try {
      // 执行分享操作
      const result = await Share.share({
        url: selectedItem.audioUri,
        title: `${selectedItem.title} - 原始音频`,
        message: `${selectedItem.title} - 原始音频`
      });
      
      // 分享完成后再关闭面板
      if (result.action === Share.sharedAction) {
        handleCloseShareModal();
      }
    } catch (error: any) {
      if (error.message !== 'User did not share') {
        logError("分享原始音频失败", error);
        Alert.alert('错误', '分享失败，请重试');
      }
    }
  };

  // 关闭分享面板 - 优化版本
  const handleCloseShareModal = () => {
    // 如果已经在关闭过程中，直接返回
    if (!showShareModal) return;

    // 执行关闭动画
    shareModalBackgroundOpacity.value = withTiming(0, {
      duration: 200,
      easing: Easing.in(Easing.quad)
    });
    shareModalTranslateY.value = withTiming(300, {
      duration: 250,
      easing: Easing.in(Easing.back(1.2))
    }, (finished) => {
      if (finished) {
        runOnJS(setShowShareModal)(false);
        runOnJS(setShareModalAnimationComplete)(false);
      }
    });
  };
  
  // 复制优化文本
  const copyOptimizedText = () => {
    if (!selectedItem) return;

    if (selectedItem.optimizedResults && selectedItem.optimizedResults.length > 1) {
      // 如果有多个优化结果，提示用户去复制特定的单个结果
      showCenteredToast(t('history.detail.copySpecificResult'));
      return;
    }

    // 默认复制当前选中记录的优化文本
    Clipboard.setString(selectedItem.optimizedText);

    // 触发震动反馈
    safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

    // 显示Badge
    showCopyBadge();
  };
  
  // 显示模板选择模态框
  const handleShowTemplateSelector = () => {
    // 先设置状态显示Modal
    setShowTemplateModal(true);
    // 默认设置文本源为优化后的文本
    setTextSource('optimized');
    // 默认编辑文本为优化后的文本
    if (selectedItem) {
      setEditingText(selectedItem.optimizedText);
    }
    // 立即开始动画，不需要延迟
    modalBackgroundOpacity.value = withTiming(1, {
      duration: 200,
      easing: Easing.out(Easing.quad)
    });
    modalTranslateY.value = withTiming(0, {
      duration: 280,
      easing: Easing.out(Easing.cubic)
    }, (finished) => {
      if (finished) {
        // 使用runOnJS在JS线程上安全更新React状态
        runOnJS(setModalAnimationComplete)(true);
      }
    });
  };
  
  // 处理模板选择
  const handleSelectTemplate = (templateId: string) => {
    // 获取选中的模板详情
    const template = templates.find(t => t.id === templateId);
    if (!template) {
      log('[HistoryDetail] 未找到选中的模板:', templateId);
      return;
    }
    
    // 检查VIP模板的权限
    if (template.isVipOnly && !reduxIsVIP) {
      log('[HistoryDetail] VIP模板访问被拒绝:', template.title);
      
      // 先关闭模板选择Modal
      handleCloseTemplateModal();
      
      // 延迟一下再显示VIP升级Modal，确保模板选择Modal完全关闭
      setTimeout(() => {
        // 设置选中的VIP模板信息和场景
        setSelectedVipTemplate({
          id: Number(template.id),
          title: template.title,
          description: template.description,
          name_key: template.name_key,
          description_key: template.description_key,
          color: template.color || "#6a5ae1",
          borderColor: template.borderColor || "#6a5ae1",
          backgroundColor: template.backgroundColor || "#f5f3ff",
          isDefault: template.isDefault,
          category: template.category,
          isVipOnly: template.isVipOnly,
        });
        setVipUpgradeScene(VipUpgradeScene.TEMPLATE_USAGE);
        // 显示VIP升级Modal
        setVipUpgradeModalVisible(true);
      }, 300); // 等待模板选择Modal关闭动画完成
      return;
    }
    
    log('[HistoryDetail] 选择模板:', template.title);
    setSelectedTemplateId(templateId);
  };
  
  // 关闭模板选择模态框
  const handleCloseTemplateModal = () => {
    // 先执行关闭动画
    Keyboard.dismiss(); // 关闭键盘
    modalBackgroundOpacity.value = withTiming(0, {
      duration: 200,
      easing: Easing.in(Easing.quad)
    });
    modalTranslateY.value = withTiming(300, {
      duration: 250,
      easing: Easing.in(Easing.back(1.2))
    }, (finished) => {
      if (finished) {
        // 使用runOnJS在JS线程上安全更新React状态
        runOnJS(setShowTemplateModal)(false);
        runOnJS(setModalAnimationComplete)(false);
      }
    });
  };
  
  // 切换文本源
  const handleToggleTextSource = () => {
    if (!selectedItem) return;
    
    if (textSource === 'original') {
      setTextSource('optimized');
      setEditingText(selectedItem.optimizedText);
    } else {
      setTextSource('original');
      setEditingText(selectedItem.originalText);
    }
  };
  
  // 处理文本编辑
  const handleTextChange = (text: string) => {
    setEditingText(text);
  };
  
  // 处理点击背景关闭键盘
  const handleDismissKeyboard = () => {
    Keyboard.dismiss();
  };

  // 处理滑动事件，收起操作面板
  const handleScroll = useCallback(() => {
    const currentTime = Date.now();
    setLastScrollTime(currentTime);
    setIsUserScrolling(true);

    // 只在面板展开时才收起
    if (isActionPanelExpanded) {
      setIsActionPanelExpanded(false);
      // 动画收起操作面板 - 使用更流畅的动画
      actionPanelTranslateX.value = withTiming(220, {
        duration: 400,
        easing: Easing.bezier(0.25, 0.46, 0.45, 0.94)
      });
      actionPanelOpacity.value = withTiming(0, {
        duration: 300,
        easing: Easing.out(Easing.quad)
      });
    }

    // 清除之前的定时器
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }

    // 设置新的定时器，用于检测滑动停止
    const newTimeout = setTimeout(() => {
      setIsUserScrolling(false);

      // 检查是否有待执行的展开操作
      if (pendingExpand) {
        setPendingExpand(false);
        // 执行展开操作
        setIsActionPanelExpanded(true);
        actionPanelTranslateX.value = withTiming(0, {
          duration: 400,
          easing: Easing.bezier(0.25, 0.46, 0.45, 0.94)
        });
        actionPanelOpacity.value = withTiming(1, {
          duration: 350,
          easing: Easing.out(Easing.cubic)
        });
        // 停止脉冲动画
        floatingButtonScale.value = withTiming(1, {
          duration: 200,
          easing: Easing.out(Easing.quad)
        });
      }
    }, 150); // 150ms后认为滑动停止

    setScrollTimeout(newTimeout);
  }, [isActionPanelExpanded, scrollTimeout, actionPanelTranslateX, actionPanelOpacity, pendingExpand, floatingButtonScale]);

  // 切换操作面板展开/收起状态
  const toggleActionPanel = useCallback(() => {
    // 检查用户是否正在滑动
    const currentTime = Date.now();
    const timeSinceLastScroll = currentTime - lastScrollTime;

    if (isActionPanelExpanded) {
      // 收起操作
      setIsActionPanelExpanded(false);
      actionPanelTranslateX.value = withTiming(220, {
        duration: 400,
        easing: Easing.bezier(0.25, 0.46, 0.45, 0.94)
      });
      actionPanelOpacity.value = withTiming(0, {
        duration: 300,
        easing: Easing.out(Easing.quad)
      });
      // 开始浮动按钮的脉冲动画
      const startPulse = () => {
        floatingButtonScale.value = withTiming(1.1, {
          duration: 800,
          easing: Easing.inOut(Easing.ease)
        }, () => {
          floatingButtonScale.value = withTiming(1, {
            duration: 800,
            easing: Easing.inOut(Easing.ease)
          }, () => {
            if (!isActionPanelExpanded) {
              startPulse();
            }
          });
        });
      };
      startPulse();
    } else {
      // 展开操作 - 检查是否正在滑动
      if (isUserScrolling || timeSinceLastScroll < 200) {
        // 如果正在滑动，设置待执行标志，给用户即时反馈
        setPendingExpand(true);

        // 给浮动按钮一个点击反馈动画
        floatingButtonScale.value = withTiming(0.9, {
          duration: 100,
          easing: Easing.out(Easing.quad)
        }, () => {
          floatingButtonScale.value = withTiming(1.1, {
            duration: 200,
            easing: Easing.out(Easing.quad)
          });
        });

        return; // 不立即展开，等待滑动停止
      }

      // 立即展开
      setIsActionPanelExpanded(true);
      actionPanelTranslateX.value = withTiming(0, {
        duration: 400,
        easing: Easing.bezier(0.25, 0.46, 0.45, 0.94)
      });
      actionPanelOpacity.value = withTiming(1, {
        duration: 350,
        easing: Easing.out(Easing.cubic)
      });
      // 停止脉冲动画
      floatingButtonScale.value = withTiming(1, {
        duration: 200,
        easing: Easing.out(Easing.quad)
      });
    }
  }, [isActionPanelExpanded, actionPanelTranslateX, actionPanelOpacity, floatingButtonScale, isUserScrolling, lastScrollTime]);
  
  // 获取VIP升级Modal的标题和描述
  const getVipUpgradeContent = () => {
    switch (vipUpgradeScene) {
      case VipUpgradeScene.TEMPLATE_USAGE:
        if (selectedVipTemplate) {
          const templateName = getTemplateDisplayNameUtil({
            id: selectedVipTemplate.id.toString(),
            name: selectedVipTemplate.title,
            description: selectedVipTemplate.description || "",
            name_key: selectedVipTemplate.name_key,
            description_key: selectedVipTemplate.description_key,
            prompt_text: "",
            is_system: true,
          }, t);

          return {
            title: t("templates.vipTemplateTitle"),
            description: t("templates.vipTemplateMessage", { templateName }),
          };
        }
        return {
          title: t("templates.vipTemplateTitle"),
          description: t("templates.vipTemplateFallback"),
        };
      default:
        return {
          title: t("templates.vipTemplateTitle"),
          description: t("templates.vipTemplateFallback"),
        };
    }
  };
  
  // 处理VIP升级Modal的升级按钮
  const handleVipUpgrade = () => {
    setVipUpgradeModalVisible(false);
    setVipUpgradeScene(null);
    setSelectedVipTemplate(null);

    // 检查是否已登录
    if (!isAuthenticated) {
      // 未登录，显示登录提示并设置VIP升级标志
      setLoginPromptTitle(t("templates.vipRequired"));
      setLoginPromptMessage(t("templates.vipTemplateAccessMessage"));
      setLoginPromptVisible(true);
      setPendingVipUpgrade(true);
      (global as any).pendingVipUpgrade = true;
    } else {
      // 已登录，直接跳转到支付页面
      router.push("/payment");
    }
  };
  
  // 处理登录提示的登录按钮
  const handleLoginPromptLogin = () => {
    log('[HistoryDetail] 处理登录按钮点击 - pendingVipUpgrade状态:', pendingVipUpgrade);
    // 关闭登录提示
    setLoginPromptVisible(false);
    // 设置全局标志，表示登录后需要导航到支付页面
    (global as any).pendingVipUpgrade = true;
    // 显示登录模态框
    showLoginModal();
  };
  
  // 处理登录提示的取消按钮
  const handleLoginPromptCancel = () => {
    setLoginPromptVisible(false);
    setPendingVipUpgrade(false);
    (global as any).pendingVipUpgrade = false;
  };
  
  // 监听登录状态变化
  useEffect(() => {
    if (isAuthenticated && pendingVipUpgrade && (global as any).pendingVipUpgrade) {
      // 用户已登录且有待处理的VIP升级，跳转到支付页面
      log('[HistoryDetail] 检测到登录完成且有待处理的VIP升级，跳转到支付页面');
      // 延迟一下再跳转，确保登录模态框已经完全关闭
      setTimeout(() => {
        router.push('/payment');
        // 重置VIP升级标志
        setPendingVipUpgrade(false);
        (global as any).pendingVipUpgrade = false;
      }, 300);
    }
  }, [isAuthenticated, pendingVipUpgrade]);
  
  // 执行二次优化
  const handleReoptimize = async () => {
    if (!selectedItem || !selectedTemplateId || !editingText) return;
    
    setIsOptimizing(true);
    
    try {
      // 获取选中的模板详情
      const template = templates.find(t => t.id === selectedTemplateId);
      if (!template) {
        Alert.alert("错误", "未找到选中的模板");
        setIsOptimizing(false);
        return;
      }
      
      // 如果是默认模板，显示提示
      if (template.isDefault) {
        Toast.show({
          type: 'info',
          text1: '使用默认模板进行风格优化',
          position: 'bottom',
          visibilityTime: 2000,
        });
      }
      
      // 如果是默认模板，显示提示
      if (template.isDefault) {
        Toast.show({
          type: 'info',
          text1: t('home.textDisplay.usingDefaultTemplate'),
          position: 'bottom',
          visibilityTime: 2000,
        });
      }
      
      // VIP检查已在模板选择阶段完成，这里直接进行优化
      
      // 如果是默认模板，显示提示
      if (template.isDefault) {
        Toast.show({
          type: 'info',
          text1: '使用默认模板进行风格优化',
          position: 'bottom',
          visibilityTime: 2000,
        });
      }

      // 调用优化API，添加原始记录ID
      const success = await optimizeText({
        text: editingText, // 使用编辑后的文本进行优化
        templateId: selectedTemplateId,
        templateName: template.title,
        templatePrompt: template.prompt || `按照${template.title}风格优化文本: ${template.description}`,
        sourceRecordId: selectedItem.id, // 添加源记录ID，标记为二次优化
        isRegenerate: true // 标记为重新生成操作
      });
      
      if (success) {
        // 关闭模态框
        handleCloseTemplateModal();
        
        // 显示成功提示，并修改按钮为跳转到结果页
        setTimeout(() => {
          Alert.alert(
            t('history.detail.optimizationSuccess'), 
            t('history.detail.optimizationSuccessDesc'),
            [
              {
                text: t('history.detail.jumpToResult') as string,
                onPress: async () => {
                  try {
                    log('开始查找新创建的二次优化记录...');
                    
                    // 等待一下确保记录已保存
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    // 获取最新的历史记录，查找新创建的二次优化记录
                    const allRecords = await HistoryService.getHistoryRecords();
                    log('获取到历史记录总数:', allRecords.length);
                    
                    // 查找最新的二次优化记录（sourceRecordId等于当前记录ID）
                    const newRecord = allRecords.find(record => 
                      record.sourceRecordId === selectedItem.id &&
                      record.templateId === selectedTemplateId
                    );
                    
                    log('找到的新记录:', newRecord ? {
                      id: newRecord.id,
                      sourceRecordId: newRecord.sourceRecordId,
                      templateId: newRecord.templateId,
                      templateName: newRecord.templateName,
                      textLength: newRecord.optimizedText.length
                    } : null);
                    
                    if (newRecord) {
                      // 复制优化文本到剪贴板
                      Clipboard.setString(newRecord.optimizedText);
                      // 触发震动反馈
                      safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
                      showCopyBadge();
                      log('已复制文本到剪贴板');
                      
                      // 停止当前页面的音频播放
                      if (isPlaying && sound) {
                        stopPlayback();
                      }
                      
                      // 等待一下再跳转，确保复制操作完成
                      await new Promise(resolve => setTimeout(resolve, 200));
                      
                      // 跳转到新创建的记录详情页
                      log('准备跳转到记录详情页:', newRecord.id);
                      router.push(`/history/detail?id=${newRecord.id}`);
                      log('已执行跳转指令');
                    } else {
                      log('没有找到新创建的记录，尝试获取最新的优化文本');
                      
                      // 如果没找到记录，尝试从Redux获取最新的优化文本并复制
                      const latestOptimizedText = await getLatestOptimizedText();
                      if (latestOptimizedText) {
                        Clipboard.setString(latestOptimizedText);
                        // 触发震动反馈
                        safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
                        showCopyBadge();
                        log('已复制最新优化文本到剪贴板');
                      }
                      
                      // 返回历史列表
                      router.back();
                    }
                  } catch (error) {
                    logError('跳转到结果页失败:', error);
                    // 发生错误时回退到历史列表
                    router.back();
                  }
                }
              }
            ]
          );
        }, 300); // 等待模态框关闭动画完成
      } else {
        log('optimizeText 调用失败');
        Alert.alert(t('history.detail.optimizationFailed'), t('history.detail.optimizationFailedDesc'));
      }
    } catch (error) {
      logError("二次优化失败:", error);
      Alert.alert(t('history.detail.optimizationFailed'), t('history.detail.optimizationFailedDesc'));
    } finally {
      setIsOptimizing(false);
    }
  };
  
  // 处理点击查看原始记录的导航
  const handleViewSourceRecord = async () => {
    if (sourceRecordId) {
      try {
        // 先检查原始记录是否还存在
        const sourceRecord = await HistoryService.getHistoryRecordById(sourceRecordId);
        
        if (!sourceRecord) {
          // 原始记录不存在，显示提示
          Alert.alert(
            t('history.detail.sourceRecordDeleted'),
            t('history.detail.sourceRecordDeletedDesc'),
            [{ text: t('common.ok'), style: 'default' }]
          );
          return;
        }
        
        // 原始记录存在，停止音频播放并导航
        if (isPlaying && sound) {
          stopPlayback();
        }
        router.push(`/history/detail?id=${sourceRecordId}`);
      } catch (error) {
        logError('检查原始记录失败:', error);
        // 如果检查失败，也显示记录不存在的提示
        Alert.alert(
          t('history.detail.sourceRecordDeleted'),
          t('history.detail.sourceRecordDeletedDesc'),
          [{ text: t('common.ok'), style: 'default' }]
        );
      }
    }
  };
  
  // 强制刷新历史记录
  const handleRefreshRecord = () => {
    log('手动触发刷新历史记录');
    setRefreshTrigger(prev => prev + 1);
  };
  
  // 渲染音频播放控件
  const renderAudioPlayer = (
    isDetail: boolean = true
  ) => {
    const hasAudio = !!selectedItem?.audioUri;
    const displayDuration = formatTime(duration);
    const displayPosition = formatTime(position);
    
    // 使用组件顶层定义的状态
    
    // 创建滑动手势响应器
    const panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => hasAudio && !isLoading,
      onMoveShouldSetPanResponder: () => hasAudio && !isLoading,
      onPanResponderGrant: (e) => {
        setIsDragging(true);
        // 更新初始位置（相对于滑块容器）
        const touchX = e.nativeEvent.locationX;
        const progressPos = Math.max(0, Math.min(100, (touchX / sliderWidth) * 100));
        setDragProgress(progressPos);
      },
      onPanResponderMove: (e: GestureResponderEvent, gestureState: PanResponderGestureState) => {
        if (sliderWidth === 0) return;
        
        // 计算手指相对于滑块容器的位置
        const touchX = gestureState.moveX - sliderPosition;
        // 限制在0-100%范围内
        const newProgress = Math.max(0, Math.min(100, (touchX / sliderWidth) * 100));
        setDragProgress(newProgress);
      },
      onPanResponderRelease: (e, gestureState) => {
        if (sliderWidth === 0 || !hasAudio) return;
        
        // 计算最终位置（相对于滑块容器）
        const touchX = gestureState.moveX - sliderPosition;
        const newProgress = Math.max(0, Math.min(100, (touchX / sliderWidth) * 100));
        
        // 更新音频位置
        handleSeek(newProgress);
        setIsDragging(false);
      },
      onPanResponderTerminate: () => {
        setIsDragging(false);
      },
    });
    
    return (
      <View style={styles.detailAudioPlayer} lightColor="#FFFFFF" darkColor="#1E2122">
        {/* 播放按钮 */}
        <TouchableOpacity
          style={[
            styles.detailPlayButton,
            !hasAudio && { opacity: 0.5 },
            isLoading && styles.loadingButton
          ]}
          onPress={handleTogglePlay}
          activeOpacity={0.7}
          disabled={!hasAudio || isLoading}
        >
          <IconSymbol
            name={isLoading ? "hourglass" : (isPlaying ? "pause.fill" : "play.fill")}
            size={14}
            color="white"
          />
        </TouchableOpacity>
        
        {/* 进度条和时间显示容器 */}
        <View 
          style={styles.progressContainer} 
          lightColor="#FFFFFF" 
          darkColor="#1E2122"
          onLayout={(e) => {
            setSliderWidth(e.nativeEvent.layout.width);
            // 保存滑块容器在屏幕上的绝对位置，用于计算触摸点相对于滑块的位置
            e.target.measure((x, y, width, height, pageX, pageY) => {
              setSliderPosition(pageX);
            });
          }}
        >
          {/* 滑块手势区域 */}
          <View 
            style={styles.sliderContainer}
            lightColor="#FFFFFF" 
            darkColor="#1E2122"
            {...panResponder.panHandlers}
          >
            {/* 进度条背景 */}
            <View 
              style={[
                styles.progressBar, 
                { backgroundColor: theme === 'dark' ? "#26292B" : "#E5E7EB" }
              ]}>
              {/* 进度条填充部分 */}
              <View 
                style={[
                  styles.progress, 
                  { 
                    width: `${isDragging ? dragProgress : progress}%`,
                    backgroundColor: "#6a5ae1"
                  }
                ]} 
              />
              
              {/* 进度条滑块 */}
              {hasAudio && (
                <View 
                  style={[
                    styles.progressThumb,
                    { left: `${isDragging ? dragProgress : progress}%` },
                    isLoading && { opacity: 0.5 }
                  ]}
                />
              )}
            </View>
          </View>
          
          {/* 时间显示行 */}
          <View style={styles.timeRow} lightColor="transparent" darkColor="transparent">
            <Text style={styles.timeText}>
              {hasAudio ? displayPosition : "0:00"}
            </Text>
            <Text style={styles.timeText}>
              {hasAudio ? displayDuration : "0:00"}
            </Text>
          </View>
        </View>
      </View>
    );
  };
  
  // 渲染详情视图中的原始文本
  const renderOriginalText = () => {
    // 如果没有数据，显示空状态
    if (!selectedItem || !selectedItem.originalText) {
      return (
        <View style={styles.noContentContainer} lightColor="#f9fafb" darkColor="#26292B">
          <Text style={styles.noContentText}>{t('history.detail.noOriginalContent')}</Text>
        </View>
      );
    }

    // 如果有分段数据，显示带时间戳的文本
    if (originalTimestampedText[selectedItem.id] && originalTimestampedText[selectedItem.id].length > 0) {
      return (
        <View lightColor="transparent" darkColor="transparent">
          {originalTimestampedText[selectedItem.id].map((segment, index) => (
            <View key={index} style={[
              styles.originalTextSegment,
              theme === 'dark' && styles.originalTextSegmentDark
            ]} lightColor="transparent" darkColor="transparent">
              <View style={styles.timestampMark} lightColor="#F3F4F6" darkColor="#26292B">
                <Text style={styles.timestampText}>{segment.timestamp}</Text>
              </View>
              <Text style={styles.originalTextContent}>{segment.text}</Text>
            </View>
          ))}
        </View>
      );
    }

    // 否则，显示完整的原始文本
    return (
      <View lightColor="transparent" darkColor="transparent">
        <Text style={styles.originalTextContent}>{selectedItem.originalText}</Text>
      </View>
    );
  };

  // 渲染详情视图中的优化文本（带差异高亮）
  const renderOptimizedTextWithHighlights = () => {
    if (!selectedItem || !selectedItem.optimizedText) {
      return (
        <View style={styles.noContentContainer} lightColor="#f9fafb" darkColor="#26292B">
          <Text style={styles.noContentText}>{t('history.detail.noOptimizedContent')}</Text>
        </View>
      );
    }

    // 检查是否有多个优化结果
    const hasMultipleResults = selectedItem.optimizedResults && selectedItem.optimizedResults.length > 1;
    
    log('渲染优化文本:', { 
      hasMultipleResults, 
      optimizedResults: selectedItem.optimizedResults,
      优化结果数量: selectedItem.optimizedResults?.length || 0 
    });
    
    // 单个结果的显示逻辑（原有代码）
    const renderSingleResult = (text: string, itemId: string) => {
      const paragraphs = text.split("\n");

      // 如果选中的是邮件格式（ID为1），使用邮件专用高亮显示的方式
      if (itemId === "1") {
        // 使用现有的特殊格式化代码
        return (
          <View lightColor="transparent" darkColor="transparent">
            <Text style={styles.optimizedTextParagraph}>
              尊敬的<Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>张总</Text>：
            </Text>
            <Text style={styles.optimizedTextParagraph}>
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>您好！</Text>
            </Text>
            <Text style={styles.optimizedTextParagraph}>
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>感谢您今天抽出宝贵时间</Text>
              讨论新项目的进展情况。
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>非常高兴</Text>
              您对我们的工作表示满意<Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>。</Text>
            </Text>
            <Text style={styles.optimizedTextParagraph}>
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>根据我们的讨论，以下是</Text>
              需要注意的<Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>几点：</Text>
            </Text>
            <Text style={styles.optimizedTextParagraph}>
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>1. 项目进度：</Text>需要加快
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>当前</Text>进度，目前
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>比</Text>计划
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>滞后</Text>约一周时间
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>；</Text>
            </Text>
            <Text style={styles.optimizedTextParagraph}>
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>2. 用户反馈：</Text>需更加及时
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>地</Text>收集和分析
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>用户反馈数据；</Text>
            </Text>
            <Text style={styles.optimizedTextParagraph}>
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>3. 项目报告：</Text>需
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>在</Text>下周二
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>前</Text>提交
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>详细的</Text>项目报告，包括目前
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>进展</Text>、遇到的问题
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>及</Text>解决方案
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>。</Text>
            </Text>
            <Text style={styles.optimizedTextParagraph}>
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>
                我们团队将立即着手解决这些问题，确保项目顺利推进。
              </Text>
            </Text>
            <Text style={styles.optimizedTextParagraph}>
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>
                如有任何疑问，随时欢迎您的指导。
              </Text>
            </Text>
            <Text style={styles.optimizedTextParagraph}>
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>此致</Text>
            </Text>
            <Text style={styles.optimizedTextParagraph}>
              <Text style={[styles.highlightText, theme === 'dark' && styles.highlightTextDark]}>敬礼</Text>
            </Text>
          </View>
        );
      } 
      // 如果是Emoji风格（ID为2），显示emoji格式
      else if (itemId === "2") {
        return (
          <View lightColor="transparent" darkColor="transparent">
            <Text style={styles.optimizedTextParagraph}>{text}</Text>
          </View>
        );
      }

      // 其他类型的文本按段落显示
      return (
        <View lightColor="transparent" darkColor="transparent">
          {paragraphs.map((paragraph, index) => (
            <Text key={index} style={styles.optimizedTextParagraph}>{paragraph}</Text>
          ))}
        </View>
      );
    };

    // 修复：如果有多个优化结果，则显示结果列表
    if (hasMultipleResults && selectedItem.optimizedResults) {
      return (
        <View lightColor="transparent" darkColor="transparent">
          {/* 多优化结果提示信息 - 改为badge样式 */}
          <View style={[
            styles.badgeContainer, 
            theme === 'dark' ? styles.badgeContainerDark : styles.badgeContainerLight
          ]}>
            <Text style={[
              styles.badgeText,
              theme === 'dark' && styles.badgeTextDark
            ]}>
              {t('history.detail.optimizationHistory')}
            </Text>
          </View>
          
          {/* 迭代显示每个优化结果 */}
          {selectedItem.optimizedResults.map((result, index) => (
            <View 
              key={index} 
              style={[
                styles.optimizedResultItem,
                theme === 'dark' && styles.optimizedResultItemDark,
                index > 0 && styles.optimizedResultWithTopBorder,
                index > 0 && theme === 'dark' && { borderTopColor: '#2c3136' }
              ]}
              lightColor="transparent" 
              darkColor="transparent"
            >
              {/* 优化结果内容 */}
              <View style={styles.optimizedResultContent}>
                {renderSingleResult(result.text, selectedItem.id)}
              </View>
              
              {/* 复制当前优化结果的按钮 */}
              <TouchableOpacity 
                style={[
                  styles.resultCopyButton,
                  theme === 'dark' && styles.resultCopyButtonDark
                ]}
                onPress={() => {
                  Clipboard.setString(result.text);
                  // 触发震动反馈
                  safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
                  showCopyBadge();
                }}
              >
                <IconSymbol name="doc.on.doc" size={14} color={theme === 'dark' ? '#ECEDEE' : '#1F2937'} />
                <Text style={[
                  styles.resultCopyButtonText,
                  theme === 'dark' && { color: '#ECEDEE' }
                ]}>
                  {t('history.detail.copy')}
                </Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      );
    }
    
    // 只有一个结果的情况
    return renderSingleResult(selectedItem.optimizedText, selectedItem.id);
  };
  
  // 添加渲染音频区域的函数
  const renderAudioSection = () => {
    if (isReoptimized) {
      return (
        <View style={styles.reoptimizedInfoContainer} lightColor="transparent" darkColor="transparent">
          <View style={styles.reoptimizedInfoIcon} lightColor="transparent" darkColor="transparent">
            <IconSymbol name="arrow.triangle.2.circlepath" size={20} color="#6a5ae1" />
          </View>
          <View style={styles.reoptimizedInfoTextContainer} lightColor="transparent" darkColor="transparent">
            <Text style={styles.reoptimizedInfoTitle}>{t('history.detail.reoptimizedInfo.titleSimple')}</Text>
          </View>
          {sourceRecordId && (
            <TouchableOpacity
              style={[
                styles.viewSourceButton,
                theme === 'dark' && styles.viewSourceButtonDark
              ]}
              onPress={handleViewSourceRecord}
            >
              <IconSymbol name="doc.text" size={14} color={theme === 'dark' ? '#c7d2fe' : '#6a5ae1'} />
              <Text style={[
                styles.viewSourceButtonText,
                theme === 'dark' && styles.viewSourceButtonTextDark
              ]}>{t('history.detail.reoptimizedInfo.viewOriginalSimple')}</Text>
            </TouchableOpacity>
          )}
        </View>
      );
    } else {
      return renderAudioPlayer();
    }
  };
  
  // 创建分享面板手势处理器
  const shareModalGestureHandler = useAnimatedGestureHandler<any, { startY: number }>({
    onStart: (_, context) => {
      context.startY = shareModalTranslateY.value;
    },
    onActive: (event, context) => {
      const newTranslateY = context.startY + event.translationY;
      // 只允许向下拖拽
      if (newTranslateY >= 0) {
        shareModalTranslateY.value = newTranslateY;
        // 根据拖拽距离调整背景透明度
        const progress = Math.min(newTranslateY / 300, 1);
        shareModalBackgroundOpacity.value = 1 - progress * 0.6;
      }
    },
    onEnd: (event) => {
      const shouldClose = event.translationY > 100 || event.velocityY > 500;
      
      if (shouldClose) {
        // 关闭面板
        shareModalBackgroundOpacity.value = withTiming(0, {
          duration: 200,
          easing: Easing.in(Easing.quad)
        });
        shareModalTranslateY.value = withTiming(300, {
          duration: 250,
          easing: Easing.in(Easing.back(1.2))
        }, (finished) => {
          if (finished) {
            runOnJS(setShowShareModal)(false);
            runOnJS(setShareModalAnimationComplete)(false);
          }
        });
      } else {
        // 弹回原位
        shareModalBackgroundOpacity.value = withTiming(1, {
          duration: 200,
          easing: Easing.out(Easing.quad)
        });
        shareModalTranslateY.value = withTiming(0, {
          duration: 280,
          easing: Easing.out(Easing.cubic)
        });
      }
    },
  });

  // 创建模板选择面板手势处理器
  const modalGestureHandler = useAnimatedGestureHandler<any, { startY: number }>({
    onStart: (_, context) => {
      context.startY = modalTranslateY.value;
    },
    onActive: (event, context) => {
      const newTranslateY = context.startY + event.translationY;
      // 只允许向下拖拽
      if (newTranslateY >= 0) {
        modalTranslateY.value = newTranslateY;
        // 根据拖拽距离调整背景透明度
        const progress = Math.min(newTranslateY / 300, 1);
        modalBackgroundOpacity.value = 1 - progress * 0.6;
      }
    },
    onEnd: (event) => {
      const shouldClose = event.translationY > 100 || event.velocityY > 500;
      
      if (shouldClose) {
        // 关闭面板
        runOnJS(handleCloseTemplateModal)();
      } else {
        // 弹回原位
        modalBackgroundOpacity.value = withTiming(1, {
          duration: 200,
          easing: Easing.out(Easing.quad)
        });
        modalTranslateY.value = withTiming(0, {
          duration: 280,
          easing: Easing.out(Easing.cubic)
        });
      }
    },
  });
  
  return (
    <SafeAreaView style={[
      styles.container,
      theme === 'dark' ? { backgroundColor: '#151718' } : { backgroundColor: 'white' }
    ]} edges={['top', 'bottom']}>
      <StatusBar style={theme === 'dark' ? "light" : "dark"} />
      
      {/* 详情视图 */}
      <View style={styles.detailViewContainer} lightColor="#f9fafb" darkColor="#151718">
        {/* 详情页顶部导航 */}
        <View style={[
          styles.detailHeader,
          theme === 'dark' && styles.detailHeaderDark
        ]} lightColor="white" darkColor="#1E2122">
          <View style={styles.detailHeaderLeft} lightColor="transparent" darkColor="transparent">
            <TouchableOpacity style={styles.backButton} onPress={handleBackToList}>
              <IconSymbol name="chevron.left" size={20} color={theme === 'dark' ? "#9BA1A6" : "#6B7280"} />
            </TouchableOpacity>
            <Text style={[
              styles.detailTitle,
              theme === 'dark' && styles.detailTitleDark
            ]}>{selectedItem?.title || t('history.detail.title')}</Text>
          </View>
          <Text style={[
            styles.detailTime,
            theme === 'dark' && styles.detailTimeDark
          ]}>
            {selectedItem?.date} {selectedItem?.time}
          </Text>
        </View>

        {/* 音频播放控件或二次优化提示 */}
        <View style={[
          styles.detailAudioContainer,
          theme === 'dark' && styles.detailAudioContainerDark
        ]} lightColor="white" darkColor="#1E2122">
          {renderAudioSection()}
        </View>

        {/* 分屏对比视图 */}
        <View style={[
          styles.splitView, 
          { flex: 1 }, // 占据剩余空间
          theme === 'dark' && styles.splitViewDark
        ]} lightColor="transparent" darkColor="transparent">
          {/* 左侧：原始语音内容 */}
          <View style={styles.column} lightColor="#f9fafb" darkColor="#1A1D1E">
            <View style={[
              styles.columnTitleContainer,
              theme === 'dark' && styles.columnTitleContainerDark
            ]} lightColor="white" darkColor="#1E2122">
              <Text style={styles.columnTitle}>{t('history.detail.originalContent')}</Text>
            </View>
            <ScrollView 
              style={styles.columnContent}
              showsVerticalScrollIndicator={false}
              bounces={true}
              scrollEventThrottle={16}
            >
              {renderOriginalText()}
              
              {/* 添加原始内容复制按钮 - 使用与右侧优化结果复制按钮相同的样式 */}
              {selectedItem && selectedItem.originalText && (
                <TouchableOpacity 
                  style={[
                    styles.resultCopyButton,
                    theme === 'dark' && styles.resultCopyButtonDark,
                    { marginTop: 12 }
                  ]}
                  onPress={() => {
                    // 复制原始内容到剪贴板
                    Clipboard.setString(selectedItem.originalText);
                    // 触发震动反馈
                    safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
                    showCopyBadge();
                  }}
                >
                  <IconSymbol name="doc.on.doc" size={14} color={theme === 'dark' ? '#ECEDEE' : '#1F2937'} />
                  <Text style={[
                    styles.resultCopyButtonText,
                    theme === 'dark' && { color: '#ECEDEE' }
                  ]}>
                    {t('history.detail.copy')}
                  </Text>
                </TouchableOpacity>
              )}
            </ScrollView>
          </View>

          {/* 右侧：AI优化结果 */}
          <View style={[
            styles.column, 
            styles.rightColumn,
            theme === 'dark' && styles.rightColumnDark
          ]} lightColor="white" darkColor="#212529">
            <View style={[
              styles.columnTitleContainer,
              theme === 'dark' && styles.columnTitleContainerDark
            ]} lightColor="white" darkColor="#1E2122">
              <Text style={styles.columnTitle}>{t('history.detail.optimizedContent')}</Text>
            </View>
            <View style={styles.rightColumnContent} lightColor="transparent" darkColor="transparent">
              <ScrollView
                style={styles.columnScrollContent}
                showsVerticalScrollIndicator={true}
                bounces={true}
                scrollEventThrottle={16}
                overScrollMode="always"
                onScroll={handleScroll}
              >
                {renderOptimizedTextWithHighlights()}
                
                {/* 添加底部空白区，使用动态高度，确保最后的内容可以滚动到可见区域 */}
                <View style={{ height: insets.bottom > 0 ? insets.bottom + 170 : 180 }} lightColor="transparent" darkColor="transparent" />
              </ScrollView>
              
              {/* 操作面板 - 使用动画容器 */}
              <Animated.View style={[
                styles.actionButtonsContainer,
                {
                  bottom: insets.bottom > 0 ? insets.bottom : 20,
                  backgroundColor: theme === 'dark' ? 'rgba(30, 33, 34, 0.95)' : 'rgba(255, 255, 255, 0.95)'
                }, // 增加底部距离
                theme === 'dark' && styles.actionButtonsContainerDark,
                actionPanelAnimatedStyle
              ]}>
                <TouchableOpacity 
                  style={[styles.actionButtonHorizontal, theme === 'dark' && styles.actionButtonHorizontalDark]}
                  onPress={() => {
                    // 使用Badge提示
                    if (!selectedItem) return;
                    Clipboard.setString(selectedItem.optimizedText);
                    // 触发震动反馈
                    safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
                    showCopyBadge();
                  }}
                  activeOpacity={0.6}
                >
                  <View style={styles.buttonInnerContainer} lightColor="transparent" darkColor="transparent">
                    <View style={styles.actionButtonIconWrapper}>
                      <IconSymbol name="doc.on.doc" size={16} color="#ffffff" />
                    </View>
                    <Text style={[styles.actionButtonText, theme === 'dark' && styles.actionButtonTextDark]}>{t('history.detail.copy')}</Text>
                  </View>
                </TouchableOpacity>
                
                {/* 添加二次优化按钮 */}
                <TouchableOpacity 
                  style={[styles.actionButtonHorizontal, theme === 'dark' && styles.actionButtonHorizontalDark]}
                  onPress={handleShowTemplateSelector}
                  activeOpacity={0.6}
                >
                  <View style={styles.buttonInnerContainer} lightColor="transparent" darkColor="transparent">
                    <View style={styles.actionButtonIconWrapper}>
                      <IconSymbol name="sparkles" size={16} color="#ffffff" />
                    </View>
                    <Text style={[styles.actionButtonText, theme === 'dark' && styles.actionButtonTextDark]}>{t('history.detail.reoptimize')}</Text>
                  </View>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.actionButtonHorizontal, theme === 'dark' && styles.actionButtonHorizontalDark]}
                  onPress={showShareOptions}
                  activeOpacity={0.6}
                >
                  <View style={styles.buttonInnerContainer} lightColor="transparent" darkColor="transparent">
                    <View style={styles.actionButtonIconWrapper}>
                      <IconSymbol name="square.and.arrow.up" size={16} color="#ffffff" />
                    </View>
                    <Text style={[styles.actionButtonText, theme === 'dark' && styles.actionButtonTextDark]}>{t('history.detail.share')}</Text>
                  </View>
                </TouchableOpacity>
              </Animated.View>

              {/* 收起状态的浮动按钮 */}
              {!isActionPanelExpanded && (
                <Animated.View style={floatingButtonAnimatedStyle}>
                  <TouchableOpacity
                    style={[
                      styles.floatingActionButton,
                      { bottom: insets.bottom > 0 ? insets.bottom + 20 : 40 },
                      theme === 'dark' && styles.floatingActionButtonDark
                    ]}
                    onPress={toggleActionPanel}
                    activeOpacity={0.8}
                  >
                    <IconSymbol name="ellipsis" size={20} color="#ffffff" />
                  </TouchableOpacity>
                </Animated.View>
              )}
            </View>
          </View>
        </View>
      </View>
      
      {/* 模板选择模态框 - 使用animationType="none"以自定义动画 */}
      <Modal
        visible={showTemplateModal}
        onRequestClose={handleCloseTemplateModal}
        animationType="none"
        transparent={true}
      >
        <GestureHandlerRootView style={{ flex: 1 }}>
          <TouchableWithoutFeedback onPress={handleCloseTemplateModal}>
            <Animated.View style={[
              styles.modalOverlay,
              theme === 'dark' && styles.modalOverlayDark,
              modalBackgroundAnimatedStyle
            ]}>
              <KeyboardAvoidingView 
                behavior={Platform.OS === "ios" ? "padding" : "height"}
                style={styles.keyboardAvoidingView}
              >
                <PanGestureHandler onGestureEvent={modalGestureHandler}>
                  <Animated.View 
                    style={[
                      styles.modalContent, 
                      theme === 'dark' && styles.modalContentDark,
                      modalAnimatedStyle
                    ]}
                  >
                    {/* 拖拽指示器 */}
                    <View style={[styles.dragIndicator, theme === 'dark' && styles.dragIndicatorDark]} />
                    
                    {/* 模态框头部 */}
                    <TouchableWithoutFeedback onPress={() => {}}>
                      <View style={[
                        styles.modalHeader,
                        theme === 'dark' && styles.modalHeaderDark
                      ]}>
                        <Text style={[
                          styles.modalTitle,
                          theme === 'dark' && { color: '#ECEDEE' }
                        ]}>{t('history.detail.templateModal.title')}</Text>
                        <TouchableOpacity 
                          style={styles.modalCloseButton} 
                          onPress={handleCloseTemplateModal}
                        >
                          <IconSymbol name="xmark" size={20} color={theme === 'dark' ? "#ECEDEE" : "#1F2937"} />
                        </TouchableOpacity>
                      </View>
                    </TouchableWithoutFeedback>
                    
                    {/* 使用ScrollView包裹主要内容，确保可以滚动查看所有内容 */}
                    <ScrollView
                      style={styles.modalScrollContent}
                      contentContainerStyle={[
                        styles.modalScrollContentContainer,
                        theme === 'dark' && { backgroundColor: '#151718' }
                      ]}
                      showsVerticalScrollIndicator={true}
                    >
                      {/* 源文本选择按钮 */}
                      <View style={styles.sourceToggleContainer}>
                        <Text style={[
                          styles.sourceToggleLabel,
                          theme === 'dark' && { color: '#ECEDEE' }
                        ]}>{t('history.detail.templateModal.sourceText')}</Text>
                        <View style={styles.sourceToggleButtonsRow}>
                          <TouchableOpacity 
                            style={[
                              styles.sourceToggleButton,
                              textSource === 'original' && styles.sourceToggleButtonActive,
                              theme === 'dark' && styles.sourceToggleButtonDark,
                              textSource === 'original' && theme === 'dark' && styles.sourceToggleButtonActiveDark
                            ]}
                            onPress={handleToggleTextSource}
                          >
                            <Text style={[
                              styles.sourceToggleButtonText,
                              textSource === 'original' && styles.sourceToggleButtonTextActive,
                              theme === 'dark' && styles.sourceToggleButtonTextDark,
                              textSource === 'original' && theme === 'dark' && styles.sourceToggleButtonTextActiveDark
                            ]}>
                              {t('history.detail.templateModal.originalText')}
                            </Text>
                          </TouchableOpacity>
                          <TouchableOpacity 
                            style={[
                              styles.sourceToggleButton,
                              textSource === 'optimized' && styles.sourceToggleButtonActive,
                              theme === 'dark' && styles.sourceToggleButtonDark,
                              textSource === 'optimized' && theme === 'dark' && styles.sourceToggleButtonActiveDark
                            ]}
                            onPress={handleToggleTextSource}
                          >
                            <Text style={[
                              styles.sourceToggleButtonText,
                              textSource === 'optimized' && styles.sourceToggleButtonTextActive,
                              theme === 'dark' && styles.sourceToggleButtonTextDark,
                              textSource === 'optimized' && theme === 'dark' && styles.sourceToggleButtonTextActiveDark
                            ]}>
                              {t('history.detail.templateModal.optimizedText')}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                      
                      {/* 文本编辑区域 */}
                      <View style={styles.textEditContainer}>
                        <TextInput
                          style={[
                            styles.textEditInput,
                            theme === 'dark' && styles.textEditInputDark
                          ]}
                          value={editingText}
                          onChangeText={handleTextChange}
                          multiline
                          placeholder="编辑要优化的文本..."
                          placeholderTextColor={theme === 'dark' ? "#9BA1A6" : "#9CA3AF"}
                          numberOfLines={8}
                          textAlignVertical="top"
                        />
                      </View>
                      
                      {/* 模板选择部分 - 添加与优化源文本相同样式的标题 */}
                      <View style={styles.templateSectionContainer}>
                        <Text style={[
                          styles.sourceToggleLabel,
                          theme === 'dark' && { color: '#ECEDEE' }
                        ]}>{t('history.detail.templateModal.selectTemplate')}</Text>
                        {modalAnimationComplete && (
                          <View style={styles.templateSelectorWrapper}>
                            <TemplateSelector
                              templates={templates}
                              selectedTemplateId={selectedTemplateId}
                              onSelectTemplate={handleSelectTemplate}
                              hideTitle={true}
                              layoutType="iconTitle"
                            />
                          </View>
                        )}
                      </View>
                      
                      {/* 底部空白，调整为更小的高度 */}
                      <View style={{ height: 20 }} />
                    </ScrollView>
                    
                    {/* 模态框底部按钮 - 固定在底部 */}
                    <View style={[
                      styles.modalFooter,
                      theme === 'dark' && styles.modalFooterDark
                    ]}>
                      <TouchableOpacity 
                        style={[
                          styles.modalButton,
                          styles.cancelButton,
                          theme === 'dark' && styles.cancelButtonDark
                        ]}
                        onPress={handleCloseTemplateModal}
                      >
                        <Text style={[
                          styles.cancelButtonText,
                          theme === 'dark' && styles.cancelButtonTextDark
                        ]}>{t('common.cancel')}</Text>
                      </TouchableOpacity>
                      
                      <TouchableOpacity
                        style={[
                          styles.modalButton,
                          styles.confirmButton,
                          (!selectedTemplateId || isOptimizing || !editingText.trim()) && styles.disabledButton
                        ]}
                        onPress={handleReoptimize}
                        disabled={!selectedTemplateId || isOptimizing || !editingText.trim()}
                      >
                        <Text style={styles.confirmButtonText}>
                          {isOptimizing ? t('history.detail.templateModal.optimizing') : t('history.detail.templateModal.startOptimize')}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </Animated.View>
                </PanGestureHandler>
              </KeyboardAvoidingView>
            </Animated.View>
          </TouchableWithoutFeedback>
        </GestureHandlerRootView>
      </Modal>

      {/* 模板选择模态框加载指示器，处理全屏loading */}
      {modalAnimationComplete && isOptimizing && (
        <View style={styles.modalLoadingOverlay} lightColor="rgba(255,255,255,0.8)" darkColor="rgba(0,0,0,0.8)">
          <ActivityIndicator size="large" color={theme === 'dark' ? '#ECEDEE' : '#4f46e5'} />
          <Text style={styles.modalLoadingText}>正在优化内容...</Text>
        </View>
      )}

             {/* 分享选择面板 - 使用animationType="none"以自定义动画 */}
       <Modal
         visible={showShareModal}
         onRequestClose={handleCloseShareModal}
         animationType="none"
         transparent={true}
       >
         <GestureHandlerRootView style={{ flex: 1 }}>
           <TouchableWithoutFeedback onPress={handleCloseShareModal}>
             <Animated.View style={[
               styles.modalOverlay,
               theme === 'dark' && styles.modalOverlayDark,
               shareModalBackgroundAnimatedStyle
             ]}>
               <PanGestureHandler onGestureEvent={shareModalGestureHandler}>
                 <Animated.View style={[
                   styles.shareModalContent, 
                   theme === 'dark' && styles.shareModalContentDark,
                   shareModalAnimatedStyle
                 ]}>
                  {/* 拖拽指示器 */}
                  <View style={[styles.dragIndicator, theme === 'dark' && styles.dragIndicatorDark]} />
                  
                  {/* 模态框头部 */}
                  <TouchableWithoutFeedback onPress={() => {}}>
                    <View style={[
                      styles.modalHeader,
                      theme === 'dark' && styles.modalHeaderDark
                    ]}>
                      <Text style={[
                        styles.modalTitle,
                        theme === 'dark' && { color: '#ECEDEE' }
                      ]}>选择分享内容</Text>
                      <TouchableOpacity 
                        style={styles.modalCloseButton} 
                        onPress={handleCloseShareModal}
                      >
                        <IconSymbol name="xmark" size={20} color={theme === 'dark' ? "#ECEDEE" : "#1F2937"} />
                      </TouchableOpacity>
                    </View>
                  </TouchableWithoutFeedback>
                  
                  {/* 分享选项 */}
                  <View style={styles.shareOptionsContainer}>
                    <TouchableOpacity 
                      style={[styles.shareOption, theme === 'dark' && styles.shareOptionDark]}
                      onPress={shareOptimizedContent}
                    >
                      <View style={styles.shareOptionIconWrapper}>
                        <IconSymbol name="sparkles" size={24} color="#6a5ae1" />
                      </View>
                      <View style={styles.shareOptionTextContainer}>
                        <Text style={[styles.shareOptionTitle, theme === 'dark' && { color: '#ECEDEE' }]}>AI优化内容</Text>
                        <Text style={[styles.shareOptionDesc, theme === 'dark' && { color: '#9BA1A6' }]}>分享AI优化后的文本内容</Text>
                      </View>
                      <IconSymbol name="chevron.right" size={16} color={theme === 'dark' ? "#9BA1A6" : "#9CA3AF"} />
                    </TouchableOpacity>
                    
                    <TouchableOpacity 
                      style={[styles.shareOption, theme === 'dark' && styles.shareOptionDark]}
                      onPress={shareOriginalContent}
                    >
                      <View style={styles.shareOptionIconWrapper}>
                        <IconSymbol name="doc.text" size={24} color="#6a5ae1" />
                      </View>
                      <View style={styles.shareOptionTextContainer}>
                        <Text style={[styles.shareOptionTitle, theme === 'dark' && { color: '#ECEDEE' }]}>原始内容</Text>
                        <Text style={[styles.shareOptionDesc, theme === 'dark' && { color: '#9BA1A6' }]}>分享语音转文字的原始内容</Text>
                      </View>
                      <IconSymbol name="chevron.right" size={16} color={theme === 'dark' ? "#9BA1A6" : "#9CA3AF"} />
                    </TouchableOpacity>
                   
                    {selectedItem?.audioUri && (
                      <TouchableOpacity 
                        style={[styles.shareOption, theme === 'dark' && styles.shareOptionDark]}
                        onPress={shareOriginalAudio}
                      >
                        <View style={styles.shareOptionIconWrapper}>
                          <IconSymbol name="waveform" size={24} color="#6a5ae1" />
                        </View>
                        <View style={styles.shareOptionTextContainer}>
                          <Text style={[styles.shareOptionTitle, theme === 'dark' && { color: '#ECEDEE' }]}>原始音频</Text>
                          <Text style={[styles.shareOptionDesc, theme === 'dark' && { color: '#9BA1A6' }]}>分享原始录音文件</Text>
                        </View>
                        <IconSymbol name="chevron.right" size={16} color={theme === 'dark' ? "#9BA1A6" : "#9CA3AF"} />
                      </TouchableOpacity>
                    )}
                  </View>
                  
                  {/* 取消按钮 */}
                  <TouchableOpacity 
                    style={[styles.shareModalCancelButton, theme === 'dark' && styles.shareModalCancelButtonDark]}
                    onPress={handleCloseShareModal}
                  >
                    <Text style={[styles.shareModalCancelText, theme === 'dark' && { color: '#ECEDEE' }]}>取消</Text>
                  </TouchableOpacity>
                </Animated.View>
              </PanGestureHandler>
            </Animated.View>
          </TouchableWithoutFeedback>
        </GestureHandlerRootView>
      </Modal>

      {/* VIP升级Modal */}
      <VipUpgradeModal
        visible={vipUpgradeModalVisible}
        onClose={() => setVipUpgradeModalVisible(false)}
        onUpgrade={handleVipUpgrade}
        title={getVipUpgradeContent().title}
        description={getVipUpgradeContent().description}
      />

      {/* 登录提示Modal */}
      <LoginPrompt
        visible={loginPromptVisible}
        title={loginPromptTitle}
        message={loginPromptMessage}
        onLogin={handleLoginPromptLogin}
        onClose={handleLoginPromptCancel}
      />

      {/* 添加Toast组件到页面底部 */}
      <Toast />
    </SafeAreaView>
  );
}

// 在组件内部添加屏幕宽度检测
const windowWidth = Dimensions.get('window').width;
const isLargeScreen = windowWidth >= 375; // iPhone 6/7/8 Plus 及以上尺寸

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  detailViewContainer: {
    flex: 1,
  },
  detailHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    minHeight: 58,
  },
  detailHeaderDark: {
    backgroundColor: "#151718",
    borderBottomColor: "#2B2F31",
  },
  detailHeaderLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 8,
    padding: 0,
  },
  detailTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#111827",
  },
  detailTitleDark: {
    color: "#ECEDEE",
  },
  detailTime: {
    fontSize: 13,
    color: "#6B7280",
  },
  detailTimeDark: {
    color: "#9BA1A6",
  },
  splitView: {
    flex: 1,
    flexDirection: "row",
    borderTopWidth: 8,
    borderTopColor: "#f0f0f0",
  },
  splitViewDark: {
    borderTopColor: "#2B2F31",
  },
  column: {
    flex: 1,
    padding: 0,
  },
  rightColumn: {
    borderLeftWidth: 1,
    borderLeftColor: "#f0f0f0",
  },
  rightColumnDark: {
    borderLeftColor: "#2B2F31",
  },
  columnTitleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 0,
    minHeight: 52,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  columnTitleContainerDark: {
    borderBottomColor: "#2B2F31",
  },
  columnTitle: {
    fontSize: 15,
    fontWeight: "600",
    lineHeight: 20,
    flexShrink: 1,
  },
  columnContent: {
    flex: 1,
    padding: 16,
  },
  originalTextSegment: {
    marginBottom: 14,
    borderBottomWidth: 1,
    borderBottomColor: "#f5f5f5",
    paddingBottom: 12,
  },
  originalTextSegmentDark: {
    borderBottomColor: "#2B2F31",
  },
  timestampMark: {
    borderRadius: 4,
    padding: 3,
    paddingHorizontal: 6,
    alignSelf: "flex-start",
    marginBottom: 6,
  },
  timestampText: {
    fontSize: 11,
    fontWeight: "500",
  },
  originalTextContent: {
    fontSize: 15,
    lineHeight: 22,
  },
  optimizedTextParagraph: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 14,
  },
  highlightText: {
    backgroundColor: "#DCFCE7",
    borderRadius: 3,
    paddingHorizontal: 3,
    paddingVertical: 1,
  },
  highlightTextDark: {
    backgroundColor: "#052E16",
  },
  noContentContainer: {
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
    marginVertical: 12,
  },
  noContentText: {
    fontSize: 14,
    textAlign: "center",
  },
  detailAudioContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  detailAudioContainerDark: {
    borderBottomColor: "#2B2F31",
  },
  detailAudioPlayer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 0,
    marginTop: 0,
  },
  detailPlayButton: {
    width: 38,
    height: 38,
    borderRadius: 19,
    backgroundColor: "#6a5ae1",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 14,
    shadowColor: "#6a5ae1",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.4,
    shadowRadius: 3,
    elevation: 3,
  },
  loadingButton: {
    backgroundColor: "#9ca3af",
  },
  progressContainer: {
    flex: 1,
    marginHorizontal: 4,
    position: "relative",
  },
  sliderContainer: {
    width: "100%",
    height: 28,
    justifyContent: "center",
    paddingVertical: 10,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: "visible",
    position: "relative",
  },
  progress: {
    position: "absolute",
    left: 0,
    top: 0,
    height: "100%",
    backgroundColor: "#6a5ae1",
    borderRadius: 2,
  },
  progressThumb: {
    position: "absolute",
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: "#6a5ae1",
    top: -4,
    marginLeft: -6,
    borderWidth: 2,
    borderColor: "#fff",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  timeRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 8,
    paddingHorizontal: 4,
  },
  timeText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#6B7280",
  },
  rightColumnContent: {
    flex: 1,
    position: "relative",
  },
  columnScrollContent: {
    flex: 1,
    padding: 16,
  },
  actionButtonsContainer: {
    position: "absolute",
    bottom: 10,
    right: 10,
    left: 10,
    flexDirection: "column",
    justifyContent: "flex-start",
    paddingTop: 12,
    paddingBottom: 12,
    paddingHorizontal: 10,
    borderRadius: 20,
    shadowColor: "#6a5ae1",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    zIndex: 999,
    backdropFilter: 'blur(10px)',
  },
  actionButtonsContainerDark: {
    shadowColor: "#000",
    shadowOpacity: 0.3,
  },
  actionButtonHorizontal: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 12,
    marginVertical: 4,
    width: "100%",
    shadowColor: "#6a5ae1",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    minHeight: 48,
    borderWidth: 0,
  },
  actionButtonHorizontalDark: {
    backgroundColor: "rgba(38, 41, 43, 0.9)",
    shadowColor: "#000",
    shadowOpacity: 0.2,
  },
  buttonInnerContainer: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
  },
  actionButtonIconWrapper: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#6a5ae1",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    shadowColor: "#6a5ae1",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 2,
  },
  actionButtonText: {
    fontSize: 15,
    fontWeight: "600",
    color: "#1F2937",
    flexShrink: 1,
  },
  actionButtonTextDark: {
    color: "#ECEDEE",
  },
  // 模态框样式
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalOverlayDark: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  keyboardAvoidingView: {
    width: '100%',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
  },
  modalContentDark: {
    backgroundColor: '#151718',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    position: 'relative',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  modalHeaderDark: {
    borderBottomColor: '#2B2F31',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalCloseButton: {
    position: 'absolute',
    right: 20,
    padding: 5,
  },
  // 滚动内容区域
  modalScrollContent: {
    maxHeight: '100%',
  },
  modalScrollContentContainer: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
  },
  // 模板选择部分
  templateSectionContainer: {
    marginBottom: 16,
  },
  // 模板选择器容器
  templateSelectorWrapper: {
    paddingVertical: 5,
    marginBottom: 5,
  },
  // 源文本选择按钮样式
  sourceToggleContainer: {
    marginBottom: 16,
  },
  sourceToggleLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 10,
  },
  sourceToggleButtonsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sourceToggleButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  sourceToggleButtonDark: {
    borderColor: '#2B2F31',
    backgroundColor: '#26292B',
  },
  sourceToggleButtonActive: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6a5ae1',
  },
  sourceToggleButtonActiveDark: {
    backgroundColor: '#312e81',
    borderColor: '#6a5ae1',
  },
  sourceToggleButtonText: {
    fontSize: 13,
    color: '#4B5563',
  },
  sourceToggleButtonTextDark: {
    color: '#9BA1A6',
  },
  sourceToggleButtonTextActive: {
    color: '#6a5ae1',
    fontWeight: '500',
  },
  sourceToggleButtonTextActiveDark: {
    color: '#c7d2fe',
    fontWeight: '500',
  },
  // 文本编辑区域样式
  textEditContainer: {
    marginBottom: 16,
  },
  textEditInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    height: 140,
    backgroundColor: '#F9FAFB',
    fontSize: 14,
  },
  textEditInputDark: {
    borderColor: '#2B2F31',
    backgroundColor: '#1A1D1E',
    color: '#ECEDEE',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingBottom: Platform.OS === 'ios' ? 30 : 20,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    backgroundColor: 'white',
  },
  modalFooterDark: {
    borderTopColor: '#2B2F31',
    backgroundColor: '#151718',
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  cancelButtonDark: {
    backgroundColor: '#26292B',
    borderColor: '#2B2F31',
  },
  cancelButtonText: {
    color: '#4B5563',
    fontWeight: '500',
  },
  cancelButtonTextDark: {
    color: '#9BA1A6',
  },
  confirmButton: {
    backgroundColor: '#6a5ae1',
  },
  confirmButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.5,
  },
  // 二次优化记录提示样式
  reoptimizedInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  reoptimizedInfoIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0EEFE',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  reoptimizedInfoTextContainer: {
    flex: 1,
  },
  reoptimizedInfoTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 4,
  },
  reoptimizedInfoDesc: {
    fontSize: 13,
    color: '#6B7280',
  },
  viewSourceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    backgroundColor: '#EEF2FF',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#DDD6FE',
    gap: 4,
  },
  viewSourceButtonDark: {
    backgroundColor: '#312e81',
    borderColor: '#4338ca',
  },
  viewSourceButtonText: {
    fontSize: 12,
    color: '#6a5ae1',
    fontWeight: '500',
  },
  viewSourceButtonTextDark: {
    color: '#c7d2fe',
  },
  // 模板选择模态框加载指示器，处理全屏loading
  modalLoadingOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.8)',
  },
  modalLoadingText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4f46e5',
    marginTop: 10,
  },
  optimizedResultItem: {
    padding: 12,
    marginBottom: 12,
    borderRadius: 8,
    backgroundColor: 'transparent',
  },
  optimizedResultItemDark: {
    backgroundColor: 'transparent',
  },
  optimizedResultWithTopBorder: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  optimizedResultContent: {
    backgroundColor: 'transparent',
    marginBottom: 12,
  },
  resultCopyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    padding: 8,
    paddingHorizontal: 14,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  resultCopyButtonDark: {
    borderColor: '#2B2F31',
    backgroundColor: '#26292B',
  },
  resultCopyButtonText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
    marginLeft: 6,
  },
  badgeContainer: {
    padding: 4,
    borderRadius: 20,
    marginBottom: 15,
    alignSelf: 'flex-start',
    maxWidth: '100%',
    paddingHorizontal: 14,
  },
  
  badgeContainerLight: {
    backgroundColor: '#f0f7ff',
    borderWidth: 1,
    borderColor: '#dcebff',
  },
  
  badgeContainerDark: {
    backgroundColor: '#1a2835',
    borderWidth: 1,
    borderColor: '#2c3e50',
  },
  
  badgeText: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '500',
    color: '#4d88c4',
    textAlign: 'center',
  },
  
  badgeTextDark: {
    color: '#8ab4e8',
  },
  // 分享面板样式
  shareModalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
    paddingBottom: Platform.OS === 'ios' ? 34 : 20,
  },
  shareModalContentDark: {
    backgroundColor: '#151718',
  },
  shareOptionsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  shareOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  shareOptionDark: {
    backgroundColor: '#26292B',
    borderColor: '#2B2F31',
  },
  shareOptionIconWrapper: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#EEF2FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  shareOptionTextContainer: {
    backgroundColor: 'transparent',
    flex: 1,
  },
  shareOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  shareOptionDesc: {
    fontSize: 14,
    color: '#6B7280',
  },
  shareModalCancelButton: {
    marginHorizontal: 20,
    // marginTop: 10,
    marginBottom: 20, 
    paddingVertical: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  shareModalCancelButtonDark: {
    backgroundColor: '#26292B',
    borderColor: '#2B2F31',
  },
  shareModalCancelText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4B5563',
  },
  dragIndicator: {
    width: 40,
    height: 4,
    backgroundColor: '#D1D5DB',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  dragIndicatorDark: {
    backgroundColor: '#4B5563',
  },
  // 浮动操作按钮样式
  floatingActionButton: {
    position: 'absolute',
    right: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#6a5ae1',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#6a5ae1',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 12,
    zIndex: 1000,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  floatingActionButtonDark: {
    backgroundColor: '#6a5ae1',
    shadowColor: '#000',
    shadowOpacity: 0.6,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
});