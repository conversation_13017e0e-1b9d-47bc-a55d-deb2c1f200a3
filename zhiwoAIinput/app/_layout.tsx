import { DarkTheme, DefaultTheme, ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import * as Localization from 'expo-localization';
import { Stack, router } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState } from 'react';
import 'react-native-reanimated';
import { Provider } from 'react-redux';
import { I18nextProvider } from 'react-i18next';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import i18n, { initI18n } from '../utils/i18n';
import type { I18nType } from '../utils/i18n';
import { store } from '../store';
import { checkSession } from '../store/slices/authSlice';
import { initializeSpeechSettings } from '../store/slices/recordingSlice';
import { loadFastModeFromStorage } from '../store/slices/fastModeSlice';
import { loadStyleOptimizationFromStorage } from '../store/slices/styleOptimizationSlice';
import { initializeTemplates } from '../services/templateSyncService';
import { SystemPromptManager } from '../utils/systemPromptManager';
import { SpeechPromptManager } from '../utils/speechPromptManager';
import { initializeSensitiveWordService } from '../services/sensitiveWordService';
import { userIdService } from '../services/userIdService';
import { dataMigrationService } from '../services/dataMigrationService';
import AuthProvider from '../components/AuthProvider';
import { ThemeProvider } from '../components/ThemeProvider';
import ThemeWrapper from '../components/ThemeWrapper';
import { useAppTheme } from '../hooks/useAppTheme';
import OAuthDeepLinkHandler from '../components/OAuthDeepLinkHandler';
import * as WebBrowser from 'expo-web-browser';
import Toast from 'react-native-toast-message';
import { log, warn, debug, error as logError, info } from "@/services/logService";


// 添加全局标志，控制登录后是否重设导航
declare global {
  var preserveTabAfterLogin: boolean;
  var setPreserveTabAfterLogin: (value: boolean) => void;
  var pendingVipUpgrade: boolean;
}

// 明确设置保持标签状态的工具函数
global.setPreserveTabAfterLogin = (value: boolean) => {
  global.preserveTabAfterLogin = value;
  log(`[全局] 设置preserveTabAfterLogin = ${value}`);
};

// 默认设置为true，表示登录后保持当前标签页
global.preserveTabAfterLogin = true;
log('[全局] 初始化preserveTabAfterLogin =', global.preserveTabAfterLogin);

// 初始化VIP升级待处理状态
global.pendingVipUpgrade = false;

// 确保 WebBrowser 可以正确处理 OAuth 回调
WebBrowser.maybeCompleteAuthSession();

// 应用启动时进行会话验证
store.dispatch(checkSession());
// 初始化语音设置
store.dispatch(initializeSpeechSettings());
// 初始化极速模式设置
store.dispatch(loadFastModeFromStorage());
// 初始化风格模板优化设置
store.dispatch(loadStyleOptimizationFromStorage());
// 初始化模板数据
initializeTemplates();

// 异步初始化系统提示词（自动检查更新）
SystemPromptManager.autoUpdate().catch(error => {
  warn('[App] 系统提示词自动更新失败:', error);
});

// 异步初始化语音提示词（自动检查更新）
SpeechPromptManager.autoUpdate().catch(error => {
  warn('[App] 语音提示词自动更新失败:', error);
});

// 异步初始化敏感词服务（缓存机制和定时更新）
initializeSensitiveWordService().catch(error => {
  warn('[App] 敏感词服务初始化失败:', error);
});

// 异步初始化用户ID服务
userIdService.initialize().catch(error => {
  warn('[App] 用户ID服务初始化失败:', error);
});

// 异步执行数据迁移
dataMigrationService.autoMigrate().then(result => {
  if (result.needed) {
    if (result.success) {
      log('[App] 数据迁移完成');
    } else {
      warn('[App] 数据迁移失败');
    }
  } else {
    log('[App] 无需数据迁移');
  }
}).catch(error => {
  warn('[App] 数据迁移过程出错:', error);
});

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

// 包装应用的导航栈
function AppStack() {
  const colorScheme = useAppTheme();
  
  return (
    <NavigationThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <Stack
        screenOptions={{
          headerShown: false,
          animation: 'slide_from_right',
          animationDuration: 200,
        }}>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="login" options={{ 
          presentation: 'modal',
          animation: 'slide_from_bottom',
          animationDuration: 300,
          gestureEnabled: true,
          gestureDirection: 'vertical',
          contentStyle: { backgroundColor: 'transparent' }
        }} />
        <Stack.Screen name="payment" options={{ 
          presentation: 'modal',
          animation: 'slide_from_bottom',
          animationDuration: 300,
          gestureEnabled: true,
          gestureDirection: 'vertical',
        }} />
        <Stack.Screen name="new-template-modal" options={{ 
          presentation: 'modal',
          animation: 'slide_from_bottom',
          animationDuration: 300,
          gestureEnabled: true,
          gestureDirection: 'vertical',
        }} />
        <Stack.Screen name="template-manager" options={{ 
          headerShown: false,
          animation: 'slide_from_right',
          animationDuration: 200,
        }} />
        <Stack.Screen name="onboarding" options={{ 
          presentation: 'modal',
          animation: 'slide_from_bottom',
          animationDuration: 300,
          gestureEnabled: false, // 禁止手势关闭，强制完成引导
        }} />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      <Toast />
    </NavigationThemeProvider>
  );
}

function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });
  const [i18nInstance, setI18nInstance] = useState<I18nType | null>(null);
  const [i18nReady, setI18nReady] = useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        const instance = await initI18n();
        setI18nInstance(instance);
        setI18nReady(true);
      } catch (error) {
        logError('Failed to initialize i18n:', error);
      }
    };

    initializeApp();
  }, []);

  useEffect(() => {
    if (loaded && i18nReady) {
      SplashScreen.hideAsync();
    }
  }, [loaded, i18nReady]);

  if (!loaded || !i18nReady || !i18nInstance) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <I18nextProvider i18n={i18nInstance}>
        <Provider store={store}>
          <ThemeProvider>
            <AuthProvider>
              <ThemeWrapper>
                <AppStack />
                <OAuthDeepLinkHandler />
                <Toast />
              </ThemeWrapper>
            </AuthProvider>
          </ThemeProvider>
        </Provider>
      </I18nextProvider>
    </GestureHandlerRootView>
  );
}

export default RootLayout;