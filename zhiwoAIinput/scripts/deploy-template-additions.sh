#!/bin/bash

# 部署模板更新脚本
# 该脚本会更新现有"做notes记录"模板为纯文本格式，并添加新的"markdown格式的要点记录"和"原文优化"模板

echo "🚀 开始部署模板更新..."

# 检查是否在正确的目录
if [ ! -d "supabase" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    exit 1
fi

# 进入supabase目录
cd supabase

# 检查Supabase CLI是否已安装
if ! command -v supabase &> /dev/null; then
    echo "❌ 错误：未找到 Supabase CLI"
    echo "请运行: npm install -g supabase"
    exit 1
fi

echo "📋 检查当前迁移状态..."
supabase migration list

echo "🔄 应用新迁移..."
supabase migration up

# 检查迁移是否成功
if [ $? -eq 0 ]; then
    echo "✅ 迁移应用成功！"
    echo ""
    echo "📝 更新内容："
    echo "  - 修改'做notes记录'为纯文本格式"
    echo "  - 添加'markdown格式的要点记录'VIP模板"
    echo "  - 添加'原文优化'免费模板"
    echo ""
    echo "🔍 验证更新："
    echo "SELECT name, semantic_id, category, is_vip_only FROM style_templates WHERE semantic_id IN ('notes', 'markdown_notes', 'text_optimize') ORDER BY semantic_id;" | supabase db --db-url "$(supabase status | grep 'DB URL' | awk '{print $3}')" -c
else
    echo "❌ 迁移失败，请检查错误信息"
    exit 1
fi

echo ""
echo "🎉 模板更新部署完成！"
echo "现在可以在应用中使用更新后的模板了。" 