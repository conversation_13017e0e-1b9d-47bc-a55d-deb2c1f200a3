#!/bin/bash

# 恢复风格模板脚本
# 该脚本会重新插入所有系统模板数据

echo "🔄 开始恢复风格模板..."

# 检查是否在正确的目录
if [ ! -d "../supabase" ]; then
    echo "❌ 错误：请在项目根目录的scripts文件夹运行此脚本"
    exit 1
fi

# 进入supabase目录
cd ../supabase

echo "📊 重新加载模板数据..."

# 使用seed.sql恢复模板数据
supabase db reset --db-url "$(supabase status | grep 'DB URL' | awk '{print $3}')" || {
    echo "⚠️  直接重置失败，尝试手动执行seed脚本..."
    
    # 检查容器是否运行
    if ! supabase status | grep -q "supabase_db_"; then
        echo "📦 启动Supabase本地环境..."
        supabase start
    fi
    
    # 手动执行seed文件
    cat seed.sql | supabase db --db-url "$(supabase status | grep 'DB URL' | awk '{print $3}')" || {
        echo "❌ 无法执行seed脚本，请手动检查数据库连接"
        exit 1
    }
}

echo "✅ 风格模板恢复完成！"
echo ""
echo "📋 已恢复的模板类别："
echo "  - scenario: 发邮件、日常发信息、做notes记录、原文优化、markdown格式的要点记录、emoji表情、小红书文案、朋友圈文案"
echo "  - translation: 英语、日语、韩语、印尼语、德语、法语、西班牙语翻译"
echo "  - ai_prompt: Recraft生图、Midjourney V7、Stable Diffusion、即梦AI"
echo "  - writing: 正式公文、学术论文、新闻报道、创意文案、口语化表达"
echo ""
echo "🎉 现在可以在应用中正常使用所有模板了！" 