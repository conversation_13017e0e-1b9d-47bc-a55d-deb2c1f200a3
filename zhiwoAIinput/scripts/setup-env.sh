#!/bin/bash

# 知我AI输入法 - 环境变量管理脚本
# 用于统一管理本地开发和云端部署的环境变量

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：显示帮助信息
show_help() {
    cat << EOF
知我AI输入法 - 环境变量管理脚本

用法: $0 [命令] [选项]

命令:
  init [env]           初始化环境配置文件
                       env: development | staging | production
  
  check                检查当前环境配置
  
  setup-eas [env]      设置EAS构建环境变量
                       env: staging | production
  
  setup-supabase       设置Supabase边缘函数环境变量
  
  help                 显示此帮助信息

示例:
  $0 init development          # 初始化开发环境配置
  $0 check                     # 检查当前配置
  $0 setup-eas staging         # 设置EAS staging环境变量
  $0 setup-supabase           # 设置Supabase边缘函数环境变量

EOF
}

# 函数：初始化环境配置
init_env() {
    local env=${1:-development}
    local config_file=".env.${env}"
    
    print_info "初始化 ${env} 环境配置..."
    
    if [[ -f "$config_file" ]]; then
        print_warning "配置文件 $config_file 已存在"
        read -p "是否覆盖？ (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "取消操作"
            return 0
        fi
    fi
    
    # 从模板复制配置
    if [[ -f "env-config.template" ]]; then
        cp env-config.template "$config_file"
        
        # 根据环境调整配置
        case $env in
            development)
                sed -i.bak "s/APP_ENV=development/APP_ENV=development/" "$config_file"
                sed -i.bak "s/IS_DEV_ENV=true/IS_DEV_ENV=true/" "$config_file"
                ;;
            staging)
                sed -i.bak "s/APP_ENV=development/APP_ENV=staging/" "$config_file"
                sed -i.bak "s/IS_DEV_ENV=true/IS_DEV_ENV=false/" "$config_file"
                ;;
            production)
                sed -i.bak "s/APP_ENV=development/APP_ENV=production/" "$config_file"
                sed -i.bak "s/IS_DEV_ENV=true/IS_DEV_ENV=false/" "$config_file"
                ;;
        esac
        
        # 删除备份文件
        rm -f "${config_file}.bak"
        
        print_success "已创建 $config_file 配置文件"
        print_warning "请编辑 $config_file 文件并填入真实的配置值"
    else
        print_error "找不到 env-config.template 模板文件"
        return 1
    fi
}

# 函数：检查环境配置
check_env() {
    print_info "检查环境配置..."
    
    # 检查配置文件
    local config_files=(".env.development" ".env.staging" ".env.production" ".env.local" ".env")
    
    print_info "=== 配置文件状态 ==="
    for file in "${config_files[@]}"; do
        if [[ -f "$file" ]]; then
            print_success "✅ $file 存在"
        else
            print_warning "❌ $file 不存在"
        fi
    done
    
    # 检查Supabase配置
    print_info "=== Supabase边缘函数配置 ==="
    if [[ -f "supabase/.env.local" ]]; then
        print_success "✅ supabase/.env.local 存在"
    else
        print_warning "❌ supabase/.env.local 不存在"
    fi
    
    # 检查EAS配置
    print_info "=== EAS配置 ==="
    if command -v eas &> /dev/null; then
        print_success "✅ EAS CLI 已安装"
    else
        print_warning "❌ EAS CLI 未安装"
    fi
}

# 函数：设置EAS环境变量
setup_eas() {
    local env=${1:-staging}
    
    print_info "设置EAS ${env}环境变量..."
    
    if ! command -v eas &> /dev/null; then
        print_error "EAS CLI 未安装，请先安装: npm install -g @expo/eas-cli"
        return 1
    fi
    
    # 检查EAS CLI版本并提示升级
    local eas_version=$(eas --version 2>/dev/null || echo "unknown")
    if [[ "$eas_version" != "unknown" ]]; then
        print_success "✅ EAS CLI 已安装 (版本: $eas_version)"
        print_info "💡 如需升级，运行: npm install -g @expo/eas-cli"
    fi
    
    # 简化的登录检查，避免卡在账户选择
    print_info "检查EAS登录状态..."
    if ! timeout 10s eas whoami --non-interactive &> /dev/null; then
        print_warning "EAS登录检查超时或未登录"
        print_info "请确保已登录EAS: eas login"
        print_info "如果有多个账户，请选择正确的组织"
    else
        print_success "✅ EAS 已登录"
    fi
    
    print_warning "请手动在EAS网站上配置以下环境变量："
    print_info "🔗 https://expo.dev/accounts/[your-account]/projects/knowmetype/environment-variables"
    
    case $env in
        staging)
            cat << EOF

=== Staging 环境变量配置 ===
APP_ENV=staging
IS_DEV_ENV=false
SHOW_DEV_LOGIN=false
SUPABASE_URL=https://yenwfmoubflrluhqsyim.supabase.co
SUPABASE_ANON_KEY=[your-anon-key]
REVENUE_CAT_IOS_API_KEY=[your-ios-key]
REVENUE_CAT_ANDROID_API_KEY=[your-android-key]
IOS_APP_ID=**********
ANDROID_PACKAGE_NAME=com.mindpowerhk.knowmetype

EOF
            ;;
        production)
            cat << EOF

=== Production 环境变量配置 ===
APP_ENV=production
IS_DEV_ENV=false
SHOW_DEV_LOGIN=false
SUPABASE_URL=https://yenwfmoubflrluhqsyim.supabase.co
SUPABASE_ANON_KEY=[your-anon-key]
REVENUE_CAT_IOS_API_KEY=[your-ios-key]
REVENUE_CAT_ANDROID_API_KEY=[your-android-key]
IOS_APP_ID=**********
ANDROID_PACKAGE_NAME=com.mindpowerhk.knowmetype

EOF
            ;;
    esac
    
    print_warning "⚠️ 重要提醒："
    print_info "1. 请在EAS网站上手动添加上述环境变量"
    print_info "2. AI服务密钥应该在Supabase云端配置，不要在EAS中设置"
    print_info "3. 配置完成后，可以运行 'eas build --profile ${env}' 进行测试"
    
    # 提供直接的EAS环境变量设置命令（可选）
    print_info ""
    print_info "🚀 或者使用以下命令直接设置环境变量："
    print_info "eas env:set APP_ENV=${env} --environment=${env}"
    print_info "eas env:set IS_DEV_ENV=false --environment=${env}"
    print_info "eas env:set SHOW_DEV_LOGIN=false --environment=${env}"
    print_info "# ... 其他变量类似设置"
}

# 函数：设置Supabase环境变量
setup_supabase() {
    print_info "设置Supabase边缘函数环境变量..."
    
    cd supabase
    
    if [[ -f "env-setup.sh" ]]; then
        print_info "使用现有的Supabase环境变量脚本..."
        ./env-setup.sh cloud
    else
        print_error "找不到Supabase环境变量脚本"
        return 1
    fi
    
    cd ..
}

# 主函数
main() {
    case "${1:-help}" in
        "init")
            init_env "$2"
            ;;
        "check")
            check_env
            ;;
        "setup-eas")
            setup_eas "$2"
            ;;
        "setup-supabase")
            setup_supabase
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: ${1:-}"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 