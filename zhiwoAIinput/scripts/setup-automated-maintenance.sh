#!/bin/bash

# 自动化维护系统设置脚本
# 帮助用户快速配置GitHub Actions定时任务和相关组件

set -euo pipefail

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $*${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $*${NC}"
}

log_warn() {
    echo -e "${YELLOW}⚠️  $*${NC}"
}

log_error() {
    echo -e "${RED}❌ $*${NC}"
}

log_step() {
    echo -e "${BOLD}📋 $*${NC}"
}

# 检查必要的工具
check_dependencies() {
    log_step "检查必要工具..."
    
    local missing_tools=()
    
    if ! command -v git >/dev/null 2>&1; then
        missing_tools+=("git")
    fi
    
    if ! command -v supabase >/dev/null 2>&1; then
        missing_tools+=("supabase CLI")
    fi
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        echo "请先安装以下工具："
        for tool in "${missing_tools[@]}"; do
            echo "  - $tool"
        done
        exit 1
    fi
    
    log_success "所有必要工具已安装"
}

# 检查项目结构
check_project_structure() {
    log_step "检查项目结构..."
    
    local required_files=(
        "supabase/config.toml"
        "supabase/functions/revenuecat-check/index.ts"
        ".github/workflows/daily-maintenance.yml"
        "supabase/migrations/20250626000000_create_maintenance_logs.sql"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "缺少必要文件:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi
    
    log_success "项目结构检查通过"
}

# 部署数据库迁移
deploy_database_migration() {
    log_step "部署数据库迁移..."
    
    if ! supabase db push; then
        log_error "数据库迁移部署失败"
        exit 1
    fi
    
    log_success "数据库迁移部署成功"
}

# 部署边缘函数
deploy_edge_functions() {
    log_step "部署边缘函数..."
    
    if ! supabase functions deploy revenuecat-check; then
        log_error "边缘函数部署失败"
        exit 1
    fi
    
    log_success "边缘函数部署成功"
}

# 检查API密钥
check_api_keys() {
    log_step "检查API密钥配置..."
    
    # 检查本地环境变量
    if [ -z "${CHECK_API_SECRET:-}" ]; then
        log_warn "本地环境变量 CHECK_API_SECRET 未设置"
        
        # 尝试从.env文件读取
        if [ -f ".env" ] && grep -q "CHECK_API_SECRET" .env; then
            log_info "在.env文件中找到 CHECK_API_SECRET"
        else
            log_warn "建议设置 CHECK_API_SECRET 环境变量"
        fi
    else
        log_success "CHECK_API_SECRET 环境变量已设置"
    fi
    
    # 检查Supabase Secrets
    log_info "检查Supabase边缘函数密钥..."
    if supabase secrets list | grep -q "CHECK_API_SECRET"; then
        log_success "Supabase CHECK_API_SECRET 已设置"
    else
        log_warn "Supabase CHECK_API_SECRET 未设置"
        
        if [ -n "${CHECK_API_SECRET:-}" ]; then
            log_info "使用本地环境变量设置Supabase密钥..."
            supabase secrets set CHECK_API_SECRET="$CHECK_API_SECRET"
            log_success "Supabase密钥设置完成"
        else
            log_warn "请手动设置Supabase密钥"
        fi
    fi
}

# 测试边缘函数
test_edge_function() {
    log_step "测试边缘函数..."
    
    local function_url
    function_url=$(supabase functions get-url revenuecat-check 2>/dev/null || echo "")
    
    if [ -z "$function_url" ]; then
        log_error "无法获取边缘函数URL"
        return 1
    fi
    
    log_info "边缘函数URL: $function_url"
    
    # 获取API密钥
    local api_key=""
    if [ -n "${CHECK_API_SECRET:-}" ]; then
        api_key="$CHECK_API_SECRET"
    elif [ -f ".env" ] && grep -q "CHECK_API_SECRET" .env; then
        api_key=$(grep "CHECK_API_SECRET" .env | cut -d'=' -f2 | tr -d '"' | tr -d "'")
    fi
    
    if [ -z "$api_key" ]; then
        log_warn "无API密钥，跳过函数测试"
        return 0
    fi
    
    log_info "执行试运行测试..."
    local response
    response=$(curl -s -X POST "$function_url/check-vip" \
        -H "Authorization: Bearer $api_key" \
        -H "Content-Type: application/json" \
        -d '{"dry_run": true}' || echo "")
    
    if echo "$response" | jq -e '.success == true' >/dev/null 2>&1; then
        log_success "边缘函数测试成功"
        
        # 显示测试结果
        local users_checked
        users_checked=$(echo "$response" | jq -r '.result.total_users_checked // "N/A"')
        log_info "测试结果: 检查了 $users_checked 个用户"
    else
        log_error "边缘函数测试失败"
        log_info "响应: $response"
        return 1
    fi
}

# 显示配置信息
show_configuration_info() {
    log_step "配置信息摘要"
    
    echo "=========================="
    echo "🔧 自动化维护系统已配置完成"
    echo "=========================="
    echo
    echo "📅 定时调度:"
    echo "  - 每日UTC时间02:00自动执行"
    echo "  - 可在GitHub Actions中手动触发"
    echo
    echo "📊 监控方式:"
    echo "  - GitHub Actions执行日志"
    echo "  - 数据库maintenance_logs表"
    echo "  - 可选的Slack/Discord通知"
    echo
    echo "⚙️  下一步配置:"
    echo "  1. 在GitHub仓库中设置Secrets:"
    echo "     - SUPABASE_URL: $(supabase status | grep "API URL" | awk '{print $3}' || echo "请手动获取")"
    echo "     - CHECK_API_SECRET: 与边缘函数相同的密钥"
    echo "     - NOTIFICATION_WEBHOOK: (可选) Slack/Discord webhook URL"
    echo
    echo "  2. 提交并推送代码到GitHub:"
    echo "     git add ."
    echo "     git commit -m 'feat: 添加自动化订阅维护系统'"
    echo "     git push"
    echo
    echo "  3. 在GitHub Actions中测试工作流:"
    echo "     Actions → 每日订阅状态维护 → Run workflow"
    echo
    echo "📖 详细文档:"
    echo "  - docs/AUTOMATED_MAINTENANCE_SETUP.md"
    echo
}

# 主函数
main() {
    echo "🚀 开始设置自动化订阅维护系统"
    echo "=================================="
    echo
    
    # 检查当前目录
    if [ ! -f "supabase/config.toml" ]; then
        log_error "请在项目根目录（包含supabase文件夹的目录）中运行此脚本"
        exit 1
    fi
    
    # 执行检查和部署步骤
    check_dependencies
    check_project_structure
    
    # 询问是否部署
    read -p "是否现在部署数据库迁移和边缘函数? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        deploy_database_migration
        deploy_edge_functions
        check_api_keys
        
        # 询问是否测试
        read -p "是否测试边缘函数? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            test_edge_function || log_warn "测试失败，请检查配置"
        fi
    else
        log_info "跳过部署，仅显示配置信息"
    fi
    
    show_configuration_info
    
    log_success "设置完成！"
}

# 显示帮助信息
show_help() {
    cat << EOF
自动化订阅维护系统设置脚本

此脚本帮助您设置以下组件：
1. GitHub Actions定时工作流
2. 数据库维护日志表
3. 边缘函数部署和配置
4. API密钥验证

使用方法:
  $0              # 交互式设置
  $0 --help       # 显示此帮助信息

环境变量:
  CHECK_API_SECRET    边缘函数API密钥

文件:
  .env                可选的环境变量文件

EOF
}

# 处理命令行参数
if [[ "${1:-}" == "--help" || "${1:-}" == "-h" ]]; then
    show_help
    exit 0
fi

# 执行主函数
main "$@" 