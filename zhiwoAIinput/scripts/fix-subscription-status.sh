#!/bin/bash

# 订阅状态修复脚本
# 用于修复RevenueCat事件丢失导致的订阅状态不一致问题

# 配置
SUPABASE_PROJECT_ID="你的项目ID"
API_SECRET="你的API密钥"
API_URL="https://${SUPABASE_PROJECT_ID}.supabase.co/functions/v1/revenuecat-check"

echo "开始修复订阅状态..."

# 1. 检查并修复VIP状态
echo "1. 检查并修复VIP状态..."
curl -X POST "${API_URL}/check-vip" \
  -H "Authorization: Bearer ${API_SECRET}" \
  -H "Content-Type: application/json" \
  -d '{"dry_run": false}' \
  -o vip_status_result.json

echo "VIP状态检查结果："
cat vip_status_result.json | jq .
echo ""

# 2. 清理过期订阅
echo "2. 清理过期订阅..."
curl -X POST "${API_URL}/cleanup-expired" \
  -H "Authorization: Bearer ${API_SECRET}" \
  -H "Content-Type: application/json" \
  -d '{"dry_run": false}' \
  -o expired_cleanup_result.json

echo "过期订阅清理结果："
cat expired_cleanup_result.json | jq .
echo ""

# 3. 清理过期临时授权
echo "3. 清理过期临时授权..."
curl -X POST "${API_URL}/cleanup-temp-entitlements" \
  -H "Authorization: Bearer ${API_SECRET}" \
  -H "Content-Type: application/json" \
  -d '{"dry_run": false}' \
  -o temp_entitlements_result.json

echo "临时授权清理结果："
cat temp_entitlements_result.json | jq .
echo ""

# 4. 更新数据库统计
echo "4. 更新数据库统计..."
curl -X GET "${API_URL}/update-stats" \
  -H "Authorization: Bearer ${API_SECRET}" \
  -o stats_result.json

echo "数据库统计更新结果："
cat stats_result.json | jq .
echo ""

echo "订阅状态修复完成！"
echo "详细结果已保存到对应的JSON文件中。" 