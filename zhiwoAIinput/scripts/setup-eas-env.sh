#!/bin/bash

# EAS环境变量快速设置脚本
# 避免复杂的登录检查，专注于环境变量配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

show_help() {
    cat << EOF
EAS环境变量设置脚本

用法: $0 [环境] [方式]

参数:
  环境: staging | production
  方式: manual | cli | json

示例:
  $0 staging manual     # 显示staging环境变量配置信息
  $0 production cli     # 使用CLI命令设置production环境变量（需要新版EAS CLI）
  $0 staging json       # 在eas.json中配置环境变量（推荐方式）
  $0 staging           # 默认显示手动配置信息

注意:
  - json方式是最稳定的配置方法，推荐使用
  - cli方式需要EAS CLI >= 16.10.0版本才支持env:set命令

EOF
}

# 显示手动配置信息
show_manual_config() {
    local env=$1
    
    print_info "📋 请在EAS控制台中手动配置以下环境变量："
    print_info "🔗 https://expo.dev/accounts/mindpower-hongkong-limited/projects/knowmetype/environment-variables"
    
    echo -e "${GREEN}
=== $(echo $env | tr '[:lower:]' '[:upper:]') 环境变量配置 ===
${NC}"
    
    case $env in
        staging)
            cat << 'EOF'
变量名                    | 值
-------------------------|---------------------------
APP_ENV                  | staging
IS_DEV_ENV              | false
SHOW_DEV_LOGIN          | false
SUPABASE_URL            | https://yenwfmoubflrluhqsyim.supabase.co
SUPABASE_ANON_KEY       | [填入你的Supabase Anon Key]
REVENUE_CAT_IOS_API_KEY | [填入你的iOS RevenueCat Key]
REVENUE_CAT_ANDROID_API_KEY | [填入你的Android RevenueCat Key]
IOS_APP_ID              | **********
ANDROID_PACKAGE_NAME    | com.mindpowerhk.knowmetype
EOF
            ;;
        production)
            cat << 'EOF'  
变量名                    | 值
-------------------------|---------------------------
APP_ENV                  | production
IS_DEV_ENV              | false
SHOW_DEV_LOGIN          | false
SUPABASE_URL            | https://yenwfmoubflrluhqsyim.supabase.co
SUPABASE_ANON_KEY       | [填入你的Supabase Anon Key]
REVENUE_CAT_IOS_API_KEY | [填入你的iOS RevenueCat Key]
REVENUE_CAT_ANDROID_API_KEY | [填入你的Android RevenueCat Key]
IOS_APP_ID              | **********
ANDROID_PACKAGE_NAME    | com.mindpowerhk.knowmetype
EOF
            ;;
    esac
    
    echo -e "${YELLOW}
⚠️ 重要提醒：
• 请将 [填入你的...] 替换为实际的密钥值
• AI服务密钥不要在EAS中设置，应该在Supabase云端配置
• 配置完成后可运行: eas build --profile ${env}
${NC}"
}

# 使用CLI命令设置
setup_with_cli() {
    local env=$1
    
    print_info "🚀 使用CLI命令设置${env}环境变量..."
    
    if ! command -v eas &> /dev/null; then
        print_error "EAS CLI 未安装，请先安装: npm install -g @expo/eas-cli"
        return 1
    fi
    
    # 检查EAS CLI版本
    local eas_version=$(eas --version 2>/dev/null | grep -o 'eas-cli/[0-9.]*' | cut -d'/' -f2)
    if [[ -n "$eas_version" ]]; then
        print_info "当前EAS CLI版本: $eas_version"
        # 简单的版本检查（不够精确，但足够用）
        if [[ "$eas_version" < "16.10.0" ]]; then
            print_warning "⚠️ 您的EAS CLI版本可能不支持 env:set 命令"
            print_info "建议升级到最新版本: npm install -g @expo/eas-cli"
            print_info "或者使用 'json' 方式配置环境变量"
        fi
    fi
    
    print_warning "请确保你已经登录EAS CLI"
    print_info "如果未登录，请运行: eas login"
    
    echo -e "${BLUE}
即将设置以下环境变量到 ${env} 环境:
${NC}"
    
    local vars=(
        "APP_ENV=${env}"
        "IS_DEV_ENV=false"
        "SHOW_DEV_LOGIN=false"
        "IOS_APP_ID=**********"
        "ANDROID_PACKAGE_NAME=com.mindpowerhk.knowmetype"
    )
    
    for var in "${vars[@]}"; do
        echo "  • $var"
    done
    
    echo -e "${YELLOW}
注意：敏感信息需要你手动设置：
  • SUPABASE_ANON_KEY
  • REVENUE_CAT_IOS_API_KEY
  • REVENUE_CAT_ANDROID_API_KEY
${NC}"
    
    read -p "是否继续设置非敏感环境变量？ (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        for var in "${vars[@]}"; do
            local key=$(echo $var | cut -d'=' -f1)
            local value=$(echo $var | cut -d'=' -f2)
            
            print_info "设置 $key=$value"
            if eas env:set "$key=$value" --environment="$env" 2>/dev/null; then
                print_success "✅ $key 设置成功"
            else
                print_error "❌ $key 设置失败，请手动设置"
            fi
            sleep 1
        done
        
        print_success "非敏感环境变量设置完成"
        print_warning "请手动设置敏感信息:"
        print_info "eas env:set SUPABASE_ANON_KEY=[your-key] --environment=${env}"
        print_info "eas env:set REVENUE_CAT_IOS_API_KEY=[your-key] --environment=${env}"
        print_info "eas env:set REVENUE_CAT_ANDROID_API_KEY=[your-key] --environment=${env}"
    else
        print_info "取消设置，请使用手动方式配置"
    fi
}

# 使用eas.json配置环境变量
setup_with_json() {
    local env=$1
    
    print_info "📝 通过eas.json配置${env}环境变量..."
    
    if [[ ! -f "eas.json" ]]; then
        print_error "找不到eas.json文件"
        return 1
    fi
    
    print_success "✅ eas.json文件存在"
    
    # 检查当前配置
    if grep -q "\"$env\":" eas.json; then
        print_success "✅ 找到${env}环境配置"
        
        # 显示当前配置的环境变量
        print_info "当前${env}环境的环境变量配置:"
        grep -A 10 "\"$env\":" eas.json | grep -A 8 "\"env\":" | sed 's/^/  /'
        
        print_info ""
        print_info "💡 建议："
        print_info "1. 环境变量已在eas.json中配置，这是推荐的方式"
        print_info "2. 如需修改，直接编辑eas.json文件"
        print_info "3. 避免在eas.json中存储敏感信息（如API密钥）"
        print_info "4. 对于敏感信息，建议使用EAS Dashboard手动配置"
        print_info ""
        print_info "🔗 EAS Dashboard环境变量页面:"
        print_info "https://expo.dev/accounts/mindpower-hongkong-limited/projects/knowmetype/environment-variables"
        
    else
        print_warning "❌ 未找到${env}环境配置"
        print_info "请先在eas.json中添加${env}环境配置"
    fi
}

# 主函数
main() {
    local env=${1:-staging}
    local method=${2:-manual}
    
    if [[ "$env" != "staging" && "$env" != "production" ]]; then
        print_error "无效的环境: $env (支持: staging, production)"
        show_help
        exit 1
    fi
    
    case $method in
        manual)
            show_manual_config "$env"
            ;;
        cli)
            setup_with_cli "$env"
            ;;
        json)
            setup_with_json "$env"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "无效的方式: $method (支持: manual, cli, json)"
            show_help
            exit 1
            ;;
    esac
}

main "$@" 