#!/usr/bin/env node

/**
 * 敏感词过滤功能测试脚本
 * 用于验证敏感词数据库和过滤服务是否正常工作
 * 
 * 使用前请确保：
 * 1. 已运行迁移：20250628000000_create_sensitive_words.sql
 * 2. 环境变量已正确配置 EXPO_PUBLIC_SUPABASE_URL 和 EXPO_PUBLIC_SUPABASE_ANON_KEY
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// 从环境变量或配置文件读取Supabase配置
function loadSupabaseConfig() {
  // 首先尝试从环境变量读取
  const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
  
  if (supabaseUrl && supabaseKey) {
    return { supabaseUrl, supabaseKey };
  }
  
  // 尝试从env配置文件读取
  const envPath = path.join(__dirname, '../.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    let url = null;
    let key = null;
    
    for (const line of lines) {
      if (line.startsWith('EXPO_PUBLIC_SUPABASE_URL=')) {
        url = line.split('=')[1].trim();
      }
      if (line.startsWith('EXPO_PUBLIC_SUPABASE_ANON_KEY=')) {
        key = line.split('=')[1].trim();
      }
    }
    
    if (url && key) {
      return { supabaseUrl: url, supabaseKey: key };
    }
  }
  
  throw new Error('无法找到Supabase配置，请检查环境变量或.env文件');
}

// 测试用的文本样本
const testTexts = [
  '这是一段正常的文本内容，没有任何问题。',
  '今天天气很好，我要去明镜与点点看电影。',
  '感谢大家的支持，欢迎订阅我们的频道。',
  '字幕由 Amara.org 社群提供，请大家多多支持。',
  '《明镜》与《点点》是很受欢迎的节目。',
  '请不吝点赞 订阅 转发 打赏，这对我们很重要。',
  '这是一段包含明镜关键词的文本。',
  '点点是一个很常见的名字。',
  '这是一段完全正常的商务文本内容，用于测试系统功能。'
];

async function testSensitiveWords() {
  console.log('🚀 开始测试敏感词过滤功能...\n');
  
  try {
    // 加载Supabase配置
    console.log('📝 加载Supabase配置...');
    const { supabaseUrl, supabaseKey } = loadSupabaseConfig();
    
    // 创建Supabase客户端
    const supabase = createClient(supabaseUrl, supabaseKey);
    console.log('✅ Supabase客户端创建成功\n');
    
    // 测试数据库连接
    console.log('🔗 测试数据库连接...');
    const { data: testConnection, error: connectionError } = await supabase
      .from('sensitive_words')
      .select('count(*)');
    
    if (connectionError) {
      throw new Error(`数据库连接失败: ${connectionError.message}`);
    }
    console.log('✅ 数据库连接成功\n');
    
    // 获取敏感词列表
    console.log('📋 获取敏感词列表...');
    const { data: words, error: wordsError } = await supabase.rpc('get_active_sensitive_words');
    
    if (wordsError) {
      throw new Error(`获取敏感词失败: ${wordsError.message}`);
    }
    
    console.log(`✅ 成功获取 ${words.length} 个敏感词:`);
    words.forEach((word, index) => {
      console.log(`  ${index + 1}. ${word.word} (${word.category})`);
    });
    console.log('');
    
    // 测试文本过滤
    console.log('🔍 开始测试文本过滤...\n');
    
    for (let i = 0; i < testTexts.length; i++) {
      const text = testTexts[i];
      console.log(`测试 ${i + 1}/${testTexts.length}: ${text.substring(0, 30)}...`);
      
      // 调用检查函数
      const { data: result, error: checkError } = await supabase.rpc('check_text_for_sensitive_words', {
        input_text: text
      });
      
      if (checkError) {
        console.log(`❌ 检查失败: ${checkError.message}`);
        continue;
      }
      
      if (result && result.length > 0) {
        const checkResult = result[0];
        if (checkResult.contains_sensitive_word) {
          console.log(`🚫 检测到敏感词: ${checkResult.detected_words.join(', ')}`);
        } else {
          console.log('✅ 文本通过检查');
        }
      } else {
        console.log('⚠️  无检查结果');
      }
      console.log('');
    }
    
    // 统计测试结果
    console.log('📊 测试结果统计:');
    let filteredCount = 0;
    let passedCount = 0;
    
    for (const text of testTexts) {
      const { data: result, error: checkError } = await supabase.rpc('check_text_for_sensitive_words', {
        input_text: text
      });
      
      if (!checkError && result && result.length > 0 && result[0].contains_sensitive_word) {
        filteredCount++;
      } else {
        passedCount++;
      }
    }
    
    console.log(`✅ 通过检查: ${passedCount} 条`);
    console.log(`🚫 被过滤: ${filteredCount} 条`);
    console.log(`📈 过滤率: ${((filteredCount / testTexts.length) * 100).toFixed(1)}%`);
    
    console.log('\n🎉 敏感词过滤功能测试完成！');
    console.log('\n🔧 功能说明:');
    console.log('- 敏感词检查在语音转录阶段触发');
    console.log('- 检测到敏感词时显示弹窗提示，不显示转录文字');
    console.log('- 包含敏感词的内容直接终止流程，不会保存到历史记录');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testSensitiveWords();
}

module.exports = { testSensitiveWords }; 