#!/bin/bash

# 订阅状态维护Cron脚本
# 可以部署在云服务器上作为GitHub Actions的备用方案
# 使用方法：
# 1. 设置环境变量 SUPABASE_URL 和 CHECK_API_SECRET
# 2. 添加到crontab: 0 2 * * * /path/to/maintenance-cron.sh

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="${SCRIPT_DIR}/maintenance.log"
MAX_LOG_SIZE=10485760  # 10MB
TIMEOUT=900  # 15分钟超时

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S UTC')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log "${BLUE}INFO${NC}" "$@"
}

log_warn() {
    log "${YELLOW}WARN${NC}" "$@"
}

log_error() {
    log "${RED}ERROR${NC}" "$@"
}

log_success() {
    log "${GREEN}SUCCESS${NC}" "$@"
}

# 检查环境变量
check_environment() {
    log_info "检查环境变量..."
    
    if [[ -z "${SUPABASE_URL:-}" ]]; then
        log_error "SUPABASE_URL 环境变量未设置"
        exit 1
    fi
    
    if [[ -z "${CHECK_API_SECRET:-}" ]]; then
        log_error "CHECK_API_SECRET 环境变量未设置"
        exit 1
    fi
    
    # 验证URL格式
    if [[ ! "$SUPABASE_URL" =~ ^https?:// ]]; then
        log_error "SUPABASE_URL 格式不正确: $SUPABASE_URL"
        exit 1
    fi
    
    log_success "环境变量检查通过"
}

# 旋转日志文件
rotate_log() {
    if [[ -f "$LOG_FILE" ]] && [[ $(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE" 2>/dev/null || echo 0) -gt $MAX_LOG_SIZE ]]; then
        log_info "日志文件过大，进行旋转..."
        mv "$LOG_FILE" "${LOG_FILE}.old"
        touch "$LOG_FILE"
    fi
}

# 发送通知
send_notification() {
    local status="$1"
    local message="$2"
    local details="${3:-}"
    
    # 如果设置了webhook，发送通知
    if [[ -n "${NOTIFICATION_WEBHOOK:-}" ]]; then
        local color="good"
        local icon="✅"
        
        if [[ "$status" == "failed" ]]; then
            color="danger"
            icon="❌"
        elif [[ "$status" == "warning" ]]; then
            color="warning"
            icon="⚠️"
        fi
        
        local payload=$(cat <<EOF
{
    "text": "${icon} 知我AI输入法 - 订阅维护任务${message}",
    "attachments": [{
        "color": "${color}",
        "fields": [
            {"title": "执行时间", "value": "$(date '+%Y-%m-%d %H:%M:%S UTC')", "short": true},
            {"title": "执行者", "value": "Cron Script", "short": true}
            $(if [[ -n "$details" ]]; then echo ",{\"title\": \"详细信息\", \"value\": \"$details\", \"short\": false}"; fi)
        ]
    }]
}
EOF
        )
        
        curl -s -X POST "$NOTIFICATION_WEBHOOK" \
            -H "Content-Type: application/json" \
            -d "$payload" > /dev/null || log_warn "通知发送失败"
    fi
}

# 记录任务开始
start_maintenance_task() {
    local task_type="$1"
    local execution_id="cron_$(date +%s)"
    
    log_info "记录维护任务开始: $task_type"
    
    local response=$(curl -s --max-time 30 \
        -X POST "${SUPABASE_URL}/rest/v1/rpc/start_maintenance_task" \
        -H "Authorization: Bearer $CHECK_API_SECRET" \
        -H "Content-Type: application/json" \
        -H "apikey: $CHECK_API_SECRET" \
        -d "{
            \"p_task_type\": \"$task_type\",
            \"p_triggered_by\": \"cron_script\",
            \"p_execution_id\": \"$execution_id\"
        }" 2>/dev/null || echo "")
    
    if [[ -n "$response" ]]; then
        local log_id=$(echo "$response" | jq -r '.' 2>/dev/null || echo "")
        if [[ "$log_id" != "null" && -n "$log_id" ]]; then
            echo "$log_id"
            return 0
        fi
    fi
    
    log_warn "任务日志记录失败，继续执行"
    echo ""
}

# 完成任务记录
complete_maintenance_task() {
    local log_id="$1"
    local status="$2"
    local users_checked="${3:-0}"
    local users_updated="${4:-0}"
    local subscriptions_cleaned="${5:-0}"
    local temp_entitlements_cleaned="${6:-0}"
    local trials_cleaned="${7:-0}"
    local execution_result="${8:-null}"
    local error_message="${9:-null}"
    
    if [[ -z "$log_id" ]]; then
        return 0
    fi
    
    log_info "更新维护任务记录: $log_id"
    
    local update_payload=$(cat <<EOF
{
    "p_log_id": "$log_id",
    "p_status": "$status",
    "p_users_checked": $users_checked,
    "p_users_updated": $users_updated,
    "p_subscriptions_cleaned": $subscriptions_cleaned,
    "p_temp_entitlements_cleaned": $temp_entitlements_cleaned,
    "p_trials_cleaned": $trials_cleaned,
    "p_execution_result": $execution_result,
    "p_error_message": $error_message
}
EOF
    )
    
    curl -s --max-time 30 \
        -X POST "${SUPABASE_URL}/rest/v1/rpc/complete_maintenance_task" \
        -H "Authorization: Bearer $CHECK_API_SECRET" \
        -H "Content-Type: application/json" \
        -H "apikey: $CHECK_API_SECRET" \
        -d "$update_payload" > /dev/null || log_warn "任务记录更新失败"
}

# 执行维护任务
execute_maintenance() {
    local task_type="${1:-run-all}"
    local dry_run="${2:-false}"
    
    log_info "开始执行维护任务: $task_type (dry_run: $dry_run)"
    
    # 记录任务开始
    local log_id=$(start_maintenance_task "$task_type")
    
    # 构建API URL和请求体
    local api_url="${SUPABASE_URL}/functions/v1/revenuecat-check/${task_type}"
    local request_body="{\"dry_run\": $dry_run}"
    
    log_info "调用API: $api_url"
    log_info "请求体: $request_body"
    
    # 执行API调用
    local response
    local http_status
    
    response=$(timeout $TIMEOUT curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -X POST "$api_url" \
        -H "Authorization: Bearer $CHECK_API_SECRET" \
        -H "Content-Type: application/json" \
        -d "$request_body" 2>/dev/null || echo "HTTP_STATUS:000")
    
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    local response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    log_info "HTTP状态码: $http_status"
    
    # 检查执行结果
    if [[ "$http_status" == "200" ]]; then
        local success=$(echo "$response_body" | jq -r '.success' 2>/dev/null || echo "false")
        if [[ "$success" == "true" ]]; then
            log_success "维护任务执行成功"
            
            # 解析统计数据
            local users_checked=0
            local users_updated=0
            local subscriptions_cleaned=0
            local temp_entitlements_cleaned=0
            local trials_cleaned=0
            
            if [[ "$task_type" == "run-all" ]]; then
                users_checked=$(echo "$response_body" | jq -r '.result.vip_status_check.total_users_checked // 0' 2>/dev/null || echo "0")
                users_updated=$(echo "$response_body" | jq -r '.result.vip_status_check.users_to_update // 0' 2>/dev/null || echo "0")
                subscriptions_cleaned=$(echo "$response_body" | jq -r '.result.expired_cleanup.expired_subscriptions // 0' 2>/dev/null || echo "0")
                temp_entitlements_cleaned=$(echo "$response_body" | jq -r '.result.temp_entitlements_cleanup.expired_entitlements // 0' 2>/dev/null || echo "0")
                trials_cleaned=$(echo "$response_body" | jq -r '.result.trials_cleanup.expired_trials // 0' 2>/dev/null || echo "0")
            else
                users_checked=$(echo "$response_body" | jq -r '.result.total_users_checked // 0' 2>/dev/null || echo "0")
                users_updated=$(echo "$response_body" | jq -r '.result.users_to_update // 0' 2>/dev/null || echo "0")
            fi
            
            log_info "执行统计:"
            log_info "  检查用户数: $users_checked"
            log_info "  更新用户数: $users_updated"
            log_info "  清理订阅数: $subscriptions_cleaned"
            log_info "  清理临时授权数: $temp_entitlements_cleaned"
            log_info "  清理试用期数: $trials_cleaned"
            
            # 更新任务记录
            complete_maintenance_task "$log_id" "success" \
                "$users_checked" "$users_updated" "$subscriptions_cleaned" \
                "$temp_entitlements_cleaned" "$trials_cleaned" \
                "$(echo "$response_body" | jq -c '.' 2>/dev/null || echo 'null')" "null"
            
            # 检查是否有大量数据不一致
            local total_updated=$((users_updated + subscriptions_cleaned + temp_entitlements_cleaned + trials_cleaned))
            if [[ $total_updated -gt 10 ]]; then
                log_warn "发现较多数据不一致($total_updated个)，建议检查RevenueCat配置"
                send_notification "warning" "发现较多数据不一致" "更新记录数: $total_updated"
            else
                send_notification "success" "执行成功" "更新记录数: $total_updated"
            fi
            
            return 0
        else
            log_error "维护任务执行失败"
        fi
    else
        log_error "API调用失败，HTTP状态码: $http_status"
    fi
    
    # 记录错误
    local error_message="HTTP $http_status: 执行失败"
    complete_maintenance_task "$log_id" "failed" 0 0 0 0 0 \
        "$(echo "$response_body" | jq -c '.' 2>/dev/null || echo 'null')" "\"$error_message\""
    
    log_error "响应内容:"
    echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
    
    send_notification "failed" "执行失败" "$error_message"
    return 1
}

# 主函数
main() {
    local task_type="${1:-run-all}"
    local dry_run="${2:-false}"
    
    # 旋转日志
    rotate_log
    
    log_info "========================================"
    log_info "开始订阅状态维护任务"
    log_info "任务类型: $task_type"
    log_info "试运行: $dry_run"
    log_info "========================================"
    
    # 检查环境
    check_environment
    
    # 执行维护
    if execute_maintenance "$task_type" "$dry_run"; then
        log_success "维护任务完成"
        exit 0
    else
        log_error "维护任务失败"
        exit 1
    fi
}

# 使用帮助
usage() {
    cat << EOF
使用方法: $0 [task_type] [dry_run]

参数:
  task_type    任务类型 (默认: run-all)
               可选值: run-all, check-vip, cleanup-expired, 
                      cleanup-temp-entitlements, cleanup-trials, update-stats
  dry_run      是否为试运行 (默认: false)
               可选值: true, false

环境变量:
  SUPABASE_URL        Supabase项目URL (必需)
  CHECK_API_SECRET    API密钥 (必需)
  NOTIFICATION_WEBHOOK  通知webhook URL (可选)

示例:
  $0                           # 执行完整维护
  $0 check-vip true           # 试运行VIP状态检查
  $0 run-all false            # 正式执行完整维护

Crontab设置示例:
  # 每日凌晨2点执行完整维护
  0 2 * * * /path/to/maintenance-cron.sh run-all false

EOF
}

# 处理命令行参数
if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
    usage
    exit 0
fi

# 执行主函数
main "${1:-run-all}" "${2:-false}" 