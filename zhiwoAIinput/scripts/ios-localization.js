const fs = require('fs-extra');
const path = require('path');

module.exports = {
  name: 'ios-localization',
  
  async postPublish(config) {
    const { sourceDir, targetDir } = config;
    
    // 确保目标目录存在
    await fs.ensureDir(targetDir);
    
    // 复制本地化文件
    try {
      await fs.copy(sourceDir, targetDir, {
        overwrite: true,
        errorOnExist: false,
        recursive: true
      });
      
      console.log('✅ iOS本地化文件复制成功');
    } catch (error) {
      console.error('❌ iOS本地化文件复制失败:', error);
      throw error;
    }
  }
}; 