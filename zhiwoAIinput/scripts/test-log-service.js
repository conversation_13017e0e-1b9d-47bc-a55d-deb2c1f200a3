#!/usr/bin/env node

/**
 * 日志服务测试脚本
 * 用于验证日志服务在不同环境下的行为
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 开始测试日志服务...\n');

// 测试不同环境的日志行为
function testEnvironment(env) {
  console.log(`📍 测试 ${env} 环境:`);
  console.log('=' .repeat(40));
  
  try {
    // 设置环境变量并运行测试
    const result = execSync(`APP_ENV=${env} node -e "
      // 模拟 React Native 环境
      global.__DEV__ = ${env === 'development'};
      
      // 模拟 Constants
      const Constants = {
        expoConfig: {
          extra: {
            appEnv: '${env}',
            APP_ENV: '${env}',
            isDevEnv: '${env === 'development' ? 'true' : 'false'}'
          }
        }
      };
      
      // 模拟导入 envConfigService
      const envConfig = {
        getAppEnvironment: () => '${env}'
      };
      
      // 模拟 logService 的核心逻辑
      function shouldLog(level) {
        switch ('${env}') {
          case 'development':
            return true;
          case 'staging':
            return level === 'warn' || level === 'error';
          case 'production':
            return false;
          default:
            return false;
        }
      }
      
      const log = (...args) => {
        if (shouldLog('log')) {
          console.log('[LOG]', ...args);
        }
      };
      
      const info = (...args) => {
        if (shouldLog('info')) {
          console.info('[INFO]', ...args);
        }
      };
      
      const warn = (...args) => {
        if (shouldLog('warn')) {
          console.warn('[WARN]', ...args);
        }
      };
      
      const error = (...args) => {
        if (shouldLog('error')) {
          console.error('[ERROR]', ...args);
        }
      };
      
      const debug = (...args) => {
        if ('${env}' === 'development') {
          console.log('[DEBUG]', ...args);
        }
      };
      
      // 执行测试
      log('这是一个普通日志');
      info('这是一个信息日志');
      warn('这是一个警告日志');
      error('这是一个错误日志');
      debug('这是一个调试日志');
      
      console.log('✅ 测试完成');
    "`, { encoding: 'utf8' });
    
    console.log(result);
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
  
  console.log('\n');
}

// 测试各个环境
['development', 'staging', 'production'].forEach(testEnvironment);

// 检查日志服务文件是否存在
const logServicePath = path.join(__dirname, '..', 'services', 'logService.ts');
if (fs.existsSync(logServicePath)) {
  console.log('✅ logService.ts 文件存在');
} else {
  console.log('❌ logService.ts 文件不存在');
}

// 检查环境配置服务
const envConfigPath = path.join(__dirname, '..', 'services', 'envConfigService.ts');
if (fs.existsSync(envConfigPath)) {
  console.log('✅ envConfigService.ts 文件存在');
} else {
  console.log('❌ envConfigService.ts 文件不存在');
}

console.log('\n📋 使用说明:');
console.log('1. 在应用中导入: import { log, info, warn, error, debug } from "../services/logService"');
console.log('2. 替换 console.log 为相应的日志函数');
console.log('3. 设置正确的 APP_ENV 环境变量');
console.log('4. 在不同环境中验证日志行为');

console.log('\n🔧 环境变量设置:');
console.log('- 开发环境: APP_ENV=development');
console.log('- 测试环境: APP_ENV=staging');
console.log('- 生产环境: APP_ENV=production');

console.log('\n🎯 预期行为:');
console.log('- development: 显示所有日志 (log, info, warn, error, debug)');
console.log('- staging: 只显示 warn 和 error');
console.log('- production: 不显示任何日志');

console.log('\n✅ 日志服务测试完成!'); 