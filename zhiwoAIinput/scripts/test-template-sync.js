/**
 * 模板同步测试脚本
 * 用于验证模板同步功能是否正常工作
 */

// 使用说明：
// 1. 在终端中运行: node scripts/test-template-sync.js
// 2. 脚本会检查模板同步的各个环节
// 3. 可以用于调试模板同步问题

const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('📋 模板同步测试工具');
console.log('====================');
console.log('');
console.log('此脚本用于测试模板同步功能，包括：');
console.log('1. 检查缓存时间设置');
console.log('2. 测试强制刷新功能');
console.log('3. 验证模板同步逻辑');
console.log('');
console.log('🔧 修复摘要：');
console.log('- 将缓存时间从5分钟缩短到1分钟');
console.log('- 添加强制刷新功能 forceRefreshTemplates()');
console.log('- 添加清除缓存功能 clearTemplateCache()');
console.log('- 在 useTemplates hook 中添加 refreshTemplates() 方法');
console.log('');
console.log('🚀 如何使用强制刷新：');
console.log('');
console.log('方法1：在代码中调用');
console.log('```typescript');
console.log('import { useTemplates } from "@/hooks/useTemplates";');
console.log('');
console.log('const { refreshTemplates } = useTemplates();');
console.log('');
console.log('// 强制刷新模板');
console.log('const success = await refreshTemplates();');
console.log('if (success) {');
console.log('  console.log("模板刷新成功");');
console.log('} else {');
console.log('  console.log("模板刷新失败");');
console.log('}');
console.log('```');
console.log('');
console.log('方法2：直接调用服务');
console.log('```typescript');
console.log('import { forceRefreshTemplates } from "@/services/templateSyncService";');
console.log('');
console.log('// 强制刷新所有模板数据');
console.log('const result = await forceRefreshTemplates();');
console.log('console.log("刷新结果:", result);');
console.log('```');
console.log('');
console.log('方法3：清除缓存后重新加载');
console.log('```typescript');
console.log('import { clearTemplateCache } from "@/services/templateSyncService";');
console.log('import { useTemplates } from "@/hooks/useTemplates";');
console.log('');
console.log('// 清除缓存');
console.log('await clearTemplateCache();');
console.log('');
console.log('// 重新加载模板');
console.log('const { templates } = useTemplates();');
console.log('```');
console.log('');
console.log('📝 推荐在以下场景使用强制刷新：');
console.log('- 云端更新模板后需要立即同步');
console.log('- 用户手动刷新模板列表');
console.log('- 调试模板同步问题');
console.log('- VIP状态变更后同步用户模板');
console.log('');

rl.question('按回车键退出...', () => {
  rl.close();
}); 