#!/usr/bin/env node

/**
 * 测试孤立订阅事件处理功能
 * 
 * 这个脚本用于测试当RevenueCat发送订阅事件但用户不存在时的处理逻辑
 */

const testOrphanedEventHandling = async () => {
  console.log("🧪 开始测试孤立订阅事件处理功能");
  
  // 模拟RevenueCat webhook数据（用户不存在的情况）
  const testWebhookData = {
    "api_version": "1.0",
    "event": {
      "aliases": [
        "6aaa2bc0-a24b-4bd8-92eb-081bb3fec46b",
        "$RCAnonymousID:23497c4cd82d49fa93ac1e2418c15c16"
      ],
      "app_id": "app2da6f6d12f",
      "app_user_id": "6aaa2bc0-a24b-4bd8-92eb-081bb3fec46b",
      "commission_percentage": 0.15,
      "country_code": "US",
      "currency": "USD",
      "entitlement_id": null,
      "entitlement_ids": [
        "VIP"
      ],
      "environment": "SANDBOX",
      "event_timestamp_ms": Date.now(),
      "expiration_at_ms": Date.now() + (30 * 24 * 60 * 60 * 1000), // 30天后
      "id": "39DF37D4-FAD0-4BC6-9FA9-95360145B971",
      "is_family_share": false,
      "is_trial_conversion": false,
      "metadata": null,
      "offer_code": null,
      "original_app_user_id": "6aaa2bc0-a24b-4bd8-92eb-081bb3fec46b",
      "original_transaction_id": "2000000951989701",
      "period_type": "NORMAL",
      "presented_offering_id": "standard",
      "price": 96,
      "price_in_purchased_currency": 96,
      "product_id": "annual_subscription",
      "purchased_at_ms": Date.now() - 1000,
      "renewal_number": 20,
      "store": "APP_STORE",
      "subscriber_attributes": {
        "$attConsentStatus": {
          "updated_at_ms": Date.now() - 10000,
          "value": "notDetermined"
        }
      },
      "takehome_percentage": 0.85,
      "tax_percentage": 0,
      "transaction_id": "2000000963574150",
      "type": "RENEWAL"
    }
  };

  console.log("📋 测试数据:");
  console.log(`- 用户ID: ${testWebhookData.event.app_user_id}`);
  console.log(`- 事件类型: ${testWebhookData.event.type}`);
  console.log(`- 产品ID: ${testWebhookData.event.product_id}`);
  console.log(`- 交易ID: ${testWebhookData.event.transaction_id}`);

  try {
    // 发送测试请求到RevenueCat webhook端点
    const webhookUrl = process.env.SUPABASE_URL 
      ? `${process.env.SUPABASE_URL}/functions/v1/revenuecat-webhook`
      : 'http://localhost:54321/functions/v1/revenuecat-webhook';

    console.log(`🚀 发送测试请求到: ${webhookUrl}`);

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_ANON_KEY || 'test-key'}`,
        'X-RevenueCat-Signature': 'test-signature' // 在测试环境中可能需要
      },
      body: JSON.stringify(testWebhookData)
    });

    const responseData = await response.json();
    
    console.log("📥 响应状态:", response.status);
    console.log("📥 响应数据:", JSON.stringify(responseData, null, 2));

    // 验证响应
    if (response.status === 200) {
      if (responseData.processed === false && responseData.reason?.includes('用户不存在')) {
        console.log("✅ 测试通过: 正确处理了用户不存在的情况");
        console.log("✅ 已记录孤立事件，未创建虚假用户");
      } else if (responseData.processed === true) {
        console.log("⚠️  警告: 事件被正常处理，可能用户实际存在或逻辑有问题");
      } else {
        console.log("❓ 未知响应格式");
      }
    } else {
      console.log("❌ 测试失败: HTTP状态码不是200");
    }

  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message);
  }
};

// 测试数据库查询功能
const testDatabaseQueries = async () => {
  console.log("\n🔍 测试数据库查询功能");
  
  const queries = [
    {
      name: "查看孤立事件统计",
      sql: "SELECT * FROM public.orphaned_records_stats;"
    },
    {
      name: "查看孤立订阅事件",
      sql: "SELECT * FROM public.orphaned_subscription_events LIMIT 5;"
    },
    {
      name: "查看匹配建议",
      sql: "SELECT * FROM public.suggest_user_matches_for_orphaned_records() LIMIT 5;"
    }
  ];

  for (const query of queries) {
    console.log(`\n📊 ${query.name}:`);
    console.log(`SQL: ${query.sql}`);
    console.log("请在Supabase Dashboard或psql中执行此查询");
  }
};

// 显示使用说明
const showUsage = () => {
  console.log("\n📖 使用说明:");
  console.log("1. 确保已运行数据库迁移: 20250719000000_allow_orphaned_subscription_events.sql");
  console.log("2. 设置环境变量:");
  console.log("   - SUPABASE_URL: Supabase项目URL");
  console.log("   - SUPABASE_ANON_KEY: Supabase匿名密钥");
  console.log("3. 运行测试: node test-orphaned-events.js");
  console.log("\n🔧 手动测试步骤:");
  console.log("1. 清空测试用户数据（模拟数据库重启）");
  console.log("2. 发送RevenueCat webhook事件");
  console.log("3. 检查是否创建了孤立事件记录而不是用户记录");
  console.log("4. 验证profiles表中没有新增虚假用户");
};

// 主函数
const main = async () => {
  console.log("🎯 孤立订阅事件处理测试工具");
  console.log("=" * 50);
  
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    showUsage();
    return;
  }
  
  if (args.includes('--test-webhook')) {
    await testOrphanedEventHandling();
  }
  
  if (args.includes('--test-queries')) {
    await testDatabaseQueries();
  }
  
  if (args.length === 0) {
    showUsage();
    console.log("\n🚀 运行完整测试...");
    await testOrphanedEventHandling();
    await testDatabaseQueries();
  }
};

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testOrphanedEventHandling,
  testDatabaseQueries
};
