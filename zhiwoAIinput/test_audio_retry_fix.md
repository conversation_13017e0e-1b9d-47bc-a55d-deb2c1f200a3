# 语音转录重试问题修复测试

## 问题描述
语音转录时如果遇到失败，发起重试失败。问题可能是由于文件保存逻辑修改，在语音文件保存时从默认位置移动到期望位置，存入SQL数据库，导致重试时从原始地址找不到原始文件。

## 修复内容

### 1. 改进文件移动逻辑
- **问题**: 使用 `FileSystem.moveAsync()` 可能导致移动失败时原始文件丢失
- **修复**: 改为使用 `copyAsync()` + `deleteAsync()` 的安全移动方式
- **位置**: `zhiwoAIinput/services/expoSpeechService.ts` 第1978-2065行

**主要改进**:
1. 先复制文件到目标位置
2. 验证复制是否成功（检查文件存在性和大小）
3. 只有复制成功后才删除原文件
4. 如果任何步骤失败，保留原始文件路径

### 2. 增强重试时的文件查找逻辑
- **问题**: 重试时只检查单一路径，找不到文件就失败
- **修复**: 实现多策略文件查找机制
- **位置**: `zhiwoAIinput/services/expoSpeechService.ts` 第2716-2791行

**查找策略**:
1. 检查原始路径
2. 尝试添加/移除 `file://` 前缀
3. 从用户录音目录查找最新的录音文件
4. 按文件名排序获取最可能的文件

### 3. 添加最终验证机制
- **位置**: `zhiwoAIinput/services/expoSpeechService.ts` 第2066-2079行
- **功能**: 在文件处理完成后验证最终路径是否有效
- **备选方案**: 如果最终路径无效，尝试查找备选文件

## 测试步骤

### 测试场景1: 正常文件移动
1. 开始录音
2. 停止录音，触发文件移动
3. 验证文件是否成功移动到用户目录
4. 验证 `currentRecordingUri` 是否正确更新

### 测试场景2: 文件移动失败
1. 开始录音
2. 模拟文件移动失败（如目标目录权限问题）
3. 验证原始文件是否保留
4. 验证重试功能是否能找到原始文件

### 测试场景3: 重试时文件查找
1. 完成一次录音（文件已移动）
2. 模拟网络错误导致转录失败
3. 点击重试按钮
4. 验证是否能找到移动后的文件
5. 验证转录是否成功

### 测试场景4: 多文件环境下的重试
1. 完成多次录音，产生多个录音文件
2. 在最新录音的转录过程中模拟失败
3. 点击重试
4. 验证是否选择了正确的（最新的）录音文件

## 预期结果

1. **文件移动更安全**: 即使移动失败，原始文件也不会丢失
2. **重试更可靠**: 能够在多种情况下找到正确的音频文件
3. **错误恢复更好**: 提供多种备选方案，减少重试失败的概率
4. **用户体验改善**: 减少"无法找到音频文件，请重新录音"的错误

## 关键代码变更

### 安全文件移动
```typescript
// 使用复制+删除替代直接移动
await FileSystem.copyAsync({ from: sourceUri, to: targetUri });
const targetExists = await FileSystem.getInfoAsync(targetUri);
if (targetExists.exists && targetSize > 0) {
  await FileSystem.deleteAsync(sourceUri); // 只有复制成功才删除
  currentRecordingUri = targetUri;
} else {
  currentRecordingUri = sourceUri; // 保留原始路径
}
```

### 多策略文件查找
```typescript
const findValidAudioFile = async (audioUri: string): Promise<string | null> => {
  // 策略1: 检查原始路径
  // 策略2: 尝试路径格式变换
  // 策略3: 从用户目录查找最新文件
  // 返回第一个找到的有效文件路径
};
```

## 注意事项

1. 这些修改主要影响 WebAPI 模式的语音转录（whisper-1, gpt-4o-mini-transcribe）
2. 本地语音识别模式不受影响
3. 修改保持了向后兼容性，不会影响现有的录音文件
4. 增加了一些日志输出，便于调试和监控
