/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./app/**/*.{js,jsx,ts,tsx}", "./components/**/*.{js,jsx,ts,tsx}"],
  darkMode: "class",
  theme: {
    extend: {
      colors: {
        primary: {
          light: '#0a7ea4',
          dark: '#61dafb',
        },
        background: {
          light: '#ffffff',
          dark: '#151718',
        },
        card: {
          light: '#ffffff',
          dark: '#1E2122',
        },
        text: {
          primary: {
            light: '#11181C',
            dark: '#ECEDEE',
          },
          secondary: {
            light: '#687076',
            dark: '#9BA1A6',
          },
        },
        border: {
          light: '#E6E8EB',
          dark: '#2B2F31',
        },
        input: {
          light: '#F1F3F5',
          dark: '#26292B',
        },
      },
    },
  },
  plugins: [],
}

