{"version": "4", "redirects": {"https://esm.sh/@supabase/functions-js@^2.1.0?target=denonext": "https://esm.sh/@supabase/functions-js@2.4.4?target=denonext", "https://esm.sh/@supabase/functions-js@^2.1.5?target=denonext": "https://esm.sh/@supabase/functions-js@2.4.4?target=denonext", "https://esm.sh/@supabase/gotrue-js@^2.26.0?target=denonext": "https://esm.sh/@supabase/gotrue-js@2.69.1?target=denonext", "https://esm.sh/@supabase/gotrue-js@^2.56.0?target=denonext": "https://esm.sh/@supabase/gotrue-js@2.69.1?target=denonext", "https://esm.sh/@supabase/node-fetch@^2.6.14?target=denonext": "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext", "https://esm.sh/@supabase/postgrest-js@^1.7.0?target=denonext": "https://esm.sh/@supabase/postgrest-js@1.19.4?target=denonext", "https://esm.sh/@supabase/postgrest-js@^1.8.5?target=denonext": "https://esm.sh/@supabase/postgrest-js@1.19.4?target=denonext", "https://esm.sh/@supabase/realtime-js@^2.7.2?target=denonext": "https://esm.sh/@supabase/realtime-js@2.11.7?target=denonext", "https://esm.sh/@supabase/realtime-js@^2.8.4?target=denonext": "https://esm.sh/@supabase/realtime-js@2.11.7?target=denonext", "https://esm.sh/@supabase/storage-js@^2.5.1?target=denonext": "https://esm.sh/@supabase/storage-js@2.7.2?target=denonext", "https://esm.sh/@supabase/storage-js@^2.5.4?target=denonext": "https://esm.sh/@supabase/storage-js@2.7.2?target=denonext", "https://esm.sh/@types/ws@~8.5.14/index.d.mts": "https://esm.sh/@types/ws@8.5.14/index.d.mts", "https://esm.sh/bufferutil@^4.0.1?target=denonext": "https://esm.sh/bufferutil@4.0.9?target=denonext", "https://esm.sh/node-gyp-build@^4.3.0?target=denonext": "https://esm.sh/node-gyp-build@4.8.4?target=denonext", "https://esm.sh/tr46@~0.0.3?target=denonext": "https://esm.sh/tr46@0.0.3?target=denonext", "https://esm.sh/utf-8-validate@%3E=5.0.2?target=denonext": "https://esm.sh/utf-8-validate@6.0.5?target=denonext", "https://esm.sh/webidl-conversions@^3.0.0?target=denonext": "https://esm.sh/webidl-conversions@3.0.1?target=denonext", "https://esm.sh/whatwg-url@^5.0.0?target=denonext": "https://esm.sh/whatwg-url@5.0.0?target=denonext", "https://esm.sh/ws@^8.18.0?target=denonext": "https://esm.sh/ws@8.18.1?target=denonext"}, "remote": {"https://cdn.jsdelivr.net/gh/denoland/deno_std@0.177.0/async/abortable.ts": "73acfb3ed7261ce0d930dbe89e43db8d34e017b063cf0eaa7d215477bf53442e", "https://cdn.jsdelivr.net/gh/denoland/deno_std@0.177.0/async/deadline.ts": "c5facb0b404eede83e38bd2717ea8ab34faa2ffb20ef87fd261fcba32ba307aa", "https://cdn.jsdelivr.net/gh/denoland/deno_std@0.177.0/async/debounce.ts": "adab11d04ca38d699444ac8a9d9856b4155e8dda2afd07ce78276c01ea5a4332", "https://cdn.jsdelivr.net/gh/denoland/deno_std@0.177.0/async/deferred.ts": "42790112f36a75a57db4a96d33974a936deb7b04d25c6084a9fa8a49f135def8", "https://cdn.jsdelivr.net/gh/denoland/deno_std@0.177.0/async/delay.ts": "73aa04cec034c84fc748c7be49bb15cac3dd43a57174bfdb7a4aec22c248f0dd", "https://cdn.jsdelivr.net/gh/denoland/deno_std@0.177.0/async/mod.ts": "f04344fa21738e5ad6bea37a6bfffd57c617c2d372bb9f9dcfd118a1b622e576", "https://cdn.jsdelivr.net/gh/denoland/deno_std@0.177.0/async/mux_async_iterator.ts": "70c7f2ee4e9466161350473ad61cac0b9f115cff4c552eaa7ef9d50c4cbb4cc9", "https://cdn.jsdelivr.net/gh/denoland/deno_std@0.177.0/async/pool.ts": "fd082bd4aaf26445909889435a5c74334c017847842ec035739b4ae637ae8260", "https://cdn.jsdelivr.net/gh/denoland/deno_std@0.177.0/async/retry.ts": "5efa3ba450ac0c07a40a82e2df296287b5013755d232049efd7ea2244f15b20f", "https://cdn.jsdelivr.net/gh/denoland/deno_std@0.177.0/async/tee.ts": "47e42d35f622650b02234d43803d0383a89eb4387e1b83b5a40106d18ae36757", "https://cdn.jsdelivr.net/gh/denoland/deno_std@0.177.0/http/server.ts": "cbb17b594651215ba95c01a395700684e569c165a567e4e04bba327f41197433", "https://deno.land/std@0.177.0/async/abortable.ts": "73acfb3ed7261ce0d930dbe89e43db8d34e017b063cf0eaa7d215477bf53442e", "https://deno.land/std@0.177.0/async/deadline.ts": "c5facb0b404eede83e38bd2717ea8ab34faa2ffb20ef87fd261fcba32ba307aa", "https://deno.land/std@0.177.0/async/debounce.ts": "adab11d04ca38d699444ac8a9d9856b4155e8dda2afd07ce78276c01ea5a4332", "https://deno.land/std@0.177.0/async/deferred.ts": "42790112f36a75a57db4a96d33974a936deb7b04d25c6084a9fa8a49f135def8", "https://deno.land/std@0.177.0/async/delay.ts": "73aa04cec034c84fc748c7be49bb15cac3dd43a57174bfdb7a4aec22c248f0dd", "https://deno.land/std@0.177.0/async/mod.ts": "f04344fa21738e5ad6bea37a6bfffd57c617c2d372bb9f9dcfd118a1b622e576", "https://deno.land/std@0.177.0/async/mux_async_iterator.ts": "70c7f2ee4e9466161350473ad61cac0b9f115cff4c552eaa7ef9d50c4cbb4cc9", "https://deno.land/std@0.177.0/async/pool.ts": "fd082bd4aaf26445909889435a5c74334c017847842ec035739b4ae637ae8260", "https://deno.land/std@0.177.0/async/retry.ts": "5efa3ba450ac0c07a40a82e2df296287b5013755d232049efd7ea2244f15b20f", "https://deno.land/std@0.177.0/async/tee.ts": "47e42d35f622650b02234d43803d0383a89eb4387e1b83b5a40106d18ae36757", "https://deno.land/std@0.177.0/http/server.ts": "cbb17b594651215ba95c01a395700684e569c165a567e4e04bba327f41197433", "https://esm.sh/@supabase/functions-js@2.4.4/denonext/functions-js.mjs": "7adeb257410ef3c4a8a1eb9b4ff416c0075d1c32860ca04913c8a9dace1de6a6", "https://esm.sh/@supabase/functions-js@2.4.4?target=denonext": "e2476c61b8afb50cd0987ff03900f48daa646706d6bad319c347e38870b9e72b", "https://esm.sh/@supabase/gotrue-js@2.69.1/denonext/gotrue-js.mjs": "f750ba1c7bed3c55b044078ec8ccfa44367b5f27cc70dc41cbf51706f83378ee", "https://esm.sh/@supabase/gotrue-js@2.69.1?target=denonext": "292e91ea998566e337a060e8f0274dfdb02d0af1cc45366319c7a46fe53d3d31", "https://esm.sh/@supabase/node-fetch@2.6.15/denonext/node-fetch.mjs": "0bae9052231f4f6dbccc7234d05ea96923dbf967be12f402764580b6bf9f713d", "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext": "4d28c4ad97328403184353f68434f2b6973971507919e9150297413664919cf3", "https://esm.sh/@supabase/postgrest-js@1.19.4/denonext/postgrest-js.mjs": "2073b5552ba10c7a8302bffffae771e3aede1daf833382355dae239fb0ab2576", "https://esm.sh/@supabase/postgrest-js@1.19.4?target=denonext": "b526be6f497dc0c461937d1f877cf2e2ebf5dbd92cf2af098fc22a6362a9a569", "https://esm.sh/@supabase/realtime-js@2.11.7/denonext/realtime-js.mjs": "63b135b799827a3793cbdc82adfe73fade427b991d0b155eac6e7c97f6c0552c", "https://esm.sh/@supabase/realtime-js@2.11.7?target=denonext": "25567f84d1658232a29414054330863cc71b2249c288f00099a0c99ac01f2e18", "https://esm.sh/@supabase/storage-js@2.7.2/denonext/storage-js.mjs": "b1bf6c0dc5c1792976cf102be6d867236a1ce2bb0c091ca90c02ea6d7a087255", "https://esm.sh/@supabase/storage-js@2.7.2?target=denonext": "beeffa5b6f716944ee3342ee432016cea801ed1f241396b57e2cdcd50c36d8fe", "https://esm.sh/@supabase/supabase-js@2.24.0": "ebbb2ccfb27ffb548804a80278c4fd01d5a6eebc8cd24498558808ecbddae6fa", "https://esm.sh/@supabase/supabase-js@2.24.0/denonext/supabase-js.mjs": "0dd7358805ed5de1afd83e0e97bdbbbd537f74e4f86aee4834e9a062857fc164", "https://esm.sh/@supabase/supabase-js@2.38.4": "039569070c2e83c292b347c39879a10e76aac6079c166f2e2677453a7799e933", "https://esm.sh/@supabase/supabase-js@2.38.4/denonext/supabase-js.mjs": "c9a6b6de54aa47e98b836b90dd0f7dd292afc990762b2c84e77aca92e94d05ba", "https://esm.sh/bufferutil@4.0.9/denonext/bufferutil.mjs": "13dca4d5bb2c68cbe119f880fa3bd785b9a81a8e02e0834dae604b4b85295cd8", "https://esm.sh/bufferutil@4.0.9?target=denonext": "e32574569ab438facfcc3f412c659b0719bbf05477136ca176938c9a3ac45125", "https://esm.sh/node-gyp-build@4.8.4/denonext/node-gyp-build.mjs": "9a86f2d044fc77bd60aaa3d697c2ba1b818da5fb1b9aaeedec59a40b8e908803", "https://esm.sh/node-gyp-build@4.8.4?target=denonext": "261a6cedf1fdbf159798141ba1e2311ac1510682c5c8b55dacc8cf5fdee4aa06", "https://esm.sh/tr46@0.0.3/denonext/tr46.mjs": "5753ec0a99414f4055f0c1f97691100f13d88e48a8443b00aebb90a512785fa2", "https://esm.sh/tr46@0.0.3?target=denonext": "19cb9be0f0d418a0c3abb81f2df31f080e9540a04e43b0f699bce1149cba0cbb", "https://esm.sh/utf-8-validate@6.0.5/denonext/utf-8-validate.mjs": "66b8ea532a0c745068f5b96ddb1bae332c3036703243541d2e89e66331974d98", "https://esm.sh/utf-8-validate@6.0.5?target=denonext": "071bc33ba1a58297e23a34d69dd589fd06df04b0f373b382ff5da544a623f271", "https://esm.sh/webidl-conversions@3.0.1/denonext/webidl-conversions.mjs": "54b5c2d50a294853c4ccebf9d5ed8988c94f4e24e463d84ec859a866ea5fafec", "https://esm.sh/webidl-conversions@3.0.1?target=denonext": "4e20318d50528084616c79d7b3f6e7f0fe7b6d09013bd01b3974d7448d767e29", "https://esm.sh/whatwg-url@5.0.0/denonext/whatwg-url.mjs": "29b16d74ee72624c915745bbd25b617cfd2248c6af0f5120d131e232a9a9af79", "https://esm.sh/whatwg-url@5.0.0?target=denonext": "f001a2cadf81312d214ca330033f474e74d81a003e21e8c5d70a1f46dc97b02d", "https://esm.sh/ws@8.18.1/denonext/ws.mjs": "732cae76ba0acb311a561003d2f7ef569293cb9159d67dd800ab346b84f80432", "https://esm.sh/ws@8.18.1?target=denonext": "e99b670fc49b38e15a7576ddcd5bb01e123fe9b3a017db7f97898127811b4e27"}, "workspace": {"packageJson": {"dependencies": ["npm:@babel/core@^7.25.2", "npm:@expo/ngrok@^4.1.3", "npm:@expo/vector-icons@^14.0.2", "npm:@react-native-async-storage/async-storage@1.23.1", "npm:@react-native-community/slider@^4.5.6", "npm:@react-navigation/bottom-tabs@^7.2.0", "npm:@react-navigation/native@^7.0.14", "npm:@react-navigation/stack@^7.2.3", "npm:@reduxjs/toolkit@^2.6.1", "npm:@supabase/auth-js@^2.69.1", "npm:@supabase/supabase-js@^2.49.4", "npm:@types/jest@^29.5.12", "npm:@types/react-native-vector-icons@^6.4.18", "npm:@types/react-native@0.73", "npm:@types/react-test-renderer@^18.3.0", "npm:@types/react@~18.3.12", "npm:babel-plugin-module-resolver@^5.0.2", "npm:babel-plugin-transform-inline-environment-variables@~0.4.4", "npm:date-fns@^4.1.0", "npm:dotenv@^16.5.0", "npm:expo-apple-authentication@~7.1.3", "npm:expo-auth-session@~6.0.3", "npm:expo-av@~15.0.2", "npm:expo-blur@~14.0.3", "npm:expo-clipboard@~7.0.1", "npm:expo-constants@~17.0.8", "npm:expo-dev-client@~5.0.19", "npm:expo-file-system@~18.0.12", "npm:expo-font@~13.0.4", "npm:expo-haptics@~14.0.1", "npm:expo-linear-gradient@^14.0.2", "npm:expo-linking@~7.0.5", "npm:expo-router@~4.0.19", "npm:expo-speech-recognition@^1.1.1", "npm:expo-splash-screen@~0.29.22", "npm:expo-status-bar@~2.0.1", "npm:expo-symbols@~0.2.2", "npm:expo-system-ui@~4.0.8", "npm:expo-web-browser@~14.0.2", "npm:expo@~52.0.40", "npm:jest-expo@~52.0.6", "npm:jest@^29.2.1", "npm:nanoid@^3.3.6", "npm:nativewind@^2.0.11", "npm:patch-package@8", "npm:postinstall-postinstall@^2.1.0", "npm:react-dom@18.3.1", "npm:react-native-dotenv@^3.4.11", "npm:react-native-gesture-handler@~2.20.2", "npm:react-native-permissions@^5.3.0", "npm:react-native-purchases@^8.9.5", "npm:react-native-reanimated@~3.16.1", "npm:react-native-safe-area-context@4.12.0", "npm:react-native-screens@4.4", "npm:react-native-svg-transformer@^1.5.0", "npm:react-native-web@~0.19.13", "npm:react-native-webview@13.12.5", "npm:react-native@0.76.9", "npm:react-redux@^9.2.0", "npm:react-test-renderer@18.3.1", "npm:react@18.3.1", "npm:tailwindcss@^3.3.2", "npm:typescript@^5.8.2"]}}}