/**
 * 模板管理钩子函数
 */
import { useEffect, useState, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import {
  loadTemplates,
  saveTemplate,
  deleteTemplate,
  setDefaultTemplate,
  forceRefreshTemplates,
  Template
} from '@/store/slices/templateSlice';
import { debounceAsync } from '@/utils/debounce';

/**
 * 模板管理钩子
 * @returns 模板相关状态和函数
 */
export function useTemplates() {
  const dispatch = useDispatch<AppDispatch>();
  const { 
    templates, 
    defaultTemplateId, 
    loading, 
    error 
  } = useSelector((state: RootState) => state.template);
  
  // 本地状态
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null>(null);
  
  // 加载模板
  useEffect(() => {
    dispatch(loadTemplates());
  }, [dispatch]);
  
  // 当模板加载完成后，选择默认模板
  useEffect(() => {
    if (templates.length > 0 && !selectedTemplateId) {
      setSelectedTemplateId(defaultTemplateId);
    }
  }, [templates, defaultTemplateId, selectedTemplateId]);
  
  // 获取选中的模板
  const selectedTemplate = templates.find(template => template.id === selectedTemplateId) || null;
  // 获取系统模板
  const systemTemplates = templates.filter(template => template.isSystem);
  // 获取用户自定义模板
  const userTemplates = templates.filter(template => !template.isSystem);
  // 获取默认模板
  const defaultTemplate = templates.find(template => template.isDefault) || null;
  
  // 保存模板
  const saveTemplateHandler = useCallback(async (template: Template) => {
    const result = await dispatch(saveTemplate(template));
    return result.meta.requestStatus === 'fulfilled';
  }, [dispatch]);
  
  // 删除模板
  const deleteTemplateHandler = useCallback(async (templateId: string) => {
    const result = await dispatch(deleteTemplate(templateId));
    return result.meta.requestStatus === 'fulfilled';
  }, [dispatch]);
  
  // 设置默认模板
  const setDefaultTemplateHandler = useCallback(async (templateId: string) => {
    const result = await dispatch(setDefaultTemplate(templateId));
    return result.meta.requestStatus === 'fulfilled';
  }, [dispatch]);
  
  // 选择模板
  const selectTemplate = useCallback((templateId: string) => {
    setSelectedTemplateId(templateId);
  }, []);
  
  // 防抖的强制刷新模板
  const debouncedRefreshTemplates = useRef(
    debounceAsync(async () => {
      const result = await dispatch(forceRefreshTemplates());
      return result.meta.requestStatus === 'fulfilled';
    }, 500) // 500ms防抖
  );

  const refreshTemplates = useCallback(async () => {
    return await debouncedRefreshTemplates.current();
  }, [dispatch]);
  
  return {
    // 状态
    templates,
    systemTemplates,
    userTemplates,
    selectedTemplate,
    defaultTemplate,
    loading,
    error,
    // 操作函数
    saveTemplate: saveTemplateHandler,
    deleteTemplate: deleteTemplateHandler,
    setDefaultTemplate: setDefaultTemplateHandler,
    selectTemplate,
    refreshTemplates
  };
}