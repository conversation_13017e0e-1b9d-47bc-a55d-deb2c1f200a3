import { useState, useEffect, useCallback, useRef } from 'react';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { InterruptionModeIOS, InterruptionModeAndroid } from 'expo-av/build/Audio.types';
import { Platform, DeviceEventEmitter } from 'react-native';

// 定义全局事件名称常量
export const AUDIO_EVENTS = {
  APP_TAB_CHANGE: 'APP_TAB_CHANGE',
};

/**
 * 音频播放钩子函数
 * 处理音频文件的播放、暂停和进度控制
 */
export function useAudioPlayer() {
  // 状态管理
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [position, setPosition] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [audioUri, setAudioUri] = useState<string | null>(null);
  const [progress, setProgress] = useState(0); // 0-100范围的进度百分比
  const [isAudioReady, setIsAudioReady] = useState(false);
  const pendingAudioRef = useRef<string | null>(null);
  
  // 使用useRef存储定时器ID，避免闭包问题
  const progressTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 停止播放，用于页面退出时调用
  const stopPlayback = useCallback(async () => {
    try {
      if (sound && isPlaying) {
        await sound.pauseAsync();
        setIsPlaying(false);
        setProgress(0); // 重置进度
        console.log('音频播放已停止（页面切换）');
      }
    } catch (error) {
      console.error('停止音频播放失败:', error);
      // 即使出错，也设置为未播放状态
      setIsPlaying(false);
    }
  }, [sound, isPlaying]);

  // 监听标签页切换事件，停止播放
  useEffect(() => {
    const subscription = DeviceEventEmitter.addListener(
      AUDIO_EVENTS.APP_TAB_CHANGE,
      () => {
        console.log('接收到标签页切换事件，停止音频播放');
        stopPlayback();
      }
    );

    return () => {
      subscription.remove();
    };
  }, [stopPlayback]);

  // 初始化音频系统
  useEffect(() => {
    (async () => {
      try {
        // 确保音频系统已启用
        await Audio.setIsEnabledAsync(true);
        
        // 设置音频模式
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          playsInSilentModeIOS: true,
          staysActiveInBackground: false,
          interruptionModeIOS: InterruptionModeIOS.DoNotMix,
          interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
          shouldDuckAndroid: true,
          playThroughEarpieceAndroid: false,
        });
        
        setIsAudioReady(true);
        console.log('音频系统初始化成功');
        
        // 检查是否有待处理的音频加载请求
        if (pendingAudioRef.current) {
          console.log('音频系统就绪，执行待处理的音频加载请求:', pendingAudioRef.current);
          loadAudio(pendingAudioRef.current);
          pendingAudioRef.current = null;
        }
      } catch (error) {
        console.error('初始化音频系统失败:', error);
        setError('音频系统初始化失败');
      }
    })();
    
    // 清理资源
    return () => {
      if (sound) {
        sound.unloadAsync();
      }
      
      // 清除进度更新定时器
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
        progressTimerRef.current = null;
      }
    };
  }, []);

  // 播放状态更新回调
  const onPlaybackStatusUpdate = useCallback((status: any) => {
    if (status.isLoaded) {
      // 减少日志输出频率，避免控制台被刷屏
      const shouldLog = status.positionMillis % 1000 < 50; // 只在整秒附近输出日志
      if (shouldLog) {
        console.log('音频状态更新:', 
          status.positionMillis, 
          '/', 
          status.durationMillis, 
          ' - Playing:', 
          status.isPlaying
        );
      }
      
      // 使用ref存储之前的值，避免不必要的状态更新
      const currentDuration = status.durationMillis || 0;
      const currentPosition = status.positionMillis || 0;
      const currentIsPlaying = status.isPlaying || false;
      
      // 只有当值真正发生变化时才更新状态
      if (Math.abs(currentDuration - duration) > 100) { // 时长变化超过0.1秒
        setDuration(currentDuration);
      }
      
      if (Math.abs(currentPosition - position) > 200) { // 位置变化超过0.2秒
        setPosition(currentPosition);
      }
      
      if (currentIsPlaying !== isPlaying) { // 播放状态发生变化
        setIsPlaying(currentIsPlaying);
      }
      
      // 计算并更新进度百分比，只有显著变化时才更新
      if (currentDuration > 0) {
        const newProgressPercent = (currentPosition / currentDuration) * 100;
        const currentProgress = progress;
        
        // 只有当进度变化超过1%时才更新，避免频繁更新
        if (Math.abs(newProgressPercent - currentProgress) > 1) {
          setProgress(newProgressPercent);
        }
      }
      
      // 如果播放完成，重置位置
      if (status.didJustFinish) {
        setIsPlaying(false);
        setPosition(0);
        setProgress(0);
      }
    } else if (status.error) {
      console.error('音频播放错误:', status.error);
      setError(`播放错误: ${status.error}`);
    }
  }, []); // 移除所有依赖项，避免回调函数重新创建导致的循环

  // 设置播放进度更新定时器
  useEffect(() => {
    // 清除之前的定时器
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
      progressTimerRef.current = null;
    }
    
    // 当正在播放时，创建定时器获取实时播放位置
    if (isPlaying && sound) {
      progressTimerRef.current = setInterval(async () => {
        try {
          const status = await sound.getStatusAsync();
          if (status.isLoaded) {
            const currentPosition = status.positionMillis || 0;
            const currentDuration = status.durationMillis || 0;
            
            // 只有当位置或时长有明显变化时才更新状态
            if (Math.abs(currentPosition - position) > 500 || // 位置变化超过0.5秒
                Math.abs(currentDuration - duration) > 100) { // 时长变化超过0.1秒
              setPosition(currentPosition);
              
              if (currentDuration > 0) {
                const progressPercent = (currentPosition / currentDuration) * 100;
                // 只有当进度变化超过0.5%时才更新
                if (Math.abs(progressPercent - progress) > 0.5) {
                  setProgress(progressPercent);
                }
              }
            }
          }
        } catch (e) {
          console.warn('获取音频状态失败:', e);
        }
      }, 500); // 增加更新间隔到500毫秒，减少频率
    }
    
    return () => {
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
        progressTimerRef.current = null;
      }
    };
  }, [isPlaying, sound]); // 移除position, duration, progress作为依赖项，避免循环

  // 加载音频文件
  const loadAudio = useCallback(async (uri: string) => {
    try {
      if (!isAudioReady) {
        console.warn('音频系统尚未准备好，将在系统就绪后加载音频');
        pendingAudioRef.current = uri;
        return null;
      }
      
      setIsLoading(true);
      setError(null);
      
      // 卸载当前的音频
      if (sound) {
        await sound.unloadAsync();
      }
      
      // 确保URI格式正确
      let audioUriToLoad = uri;
      
      // 检查URI是否包含有效协议
      if (!audioUriToLoad.startsWith('file://') && !audioUriToLoad.startsWith('http://') && !audioUriToLoad.startsWith('https://')) {
        // 如果是本地文件但没有file://前缀，添加前缀
        if (audioUriToLoad.startsWith('/')) {
          audioUriToLoad = `file://${audioUriToLoad}`;
        }
      }
      
      // 检查文件是否存在 (仅对本地文件)
      if (audioUriToLoad.startsWith('file://')) {
        try {
          const fileInfo = await FileSystem.getInfoAsync(audioUriToLoad);
          if (!fileInfo.exists) {
            console.error('音频文件不存在:', audioUriToLoad);
            setError('音频文件不存在');
            setIsLoading(false);
            return null;
          }
          console.log('音频文件存在，大小:', fileInfo.size);
          
          // 如果文件大小为0，说明文件可能损坏
          if (fileInfo.size === 0) {
            console.error('音频文件可能损坏 (大小为0)');
            setError('音频文件损坏');
            setIsLoading(false);
            return null;
          }
        } catch (checkError) {
          console.error('检查音频文件失败:', checkError);
          // 继续尝试加载
        }
      }
      
      // 在任何平台上，确保音频模式正确设置
      try {
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          playsInSilentModeIOS: true,
          staysActiveInBackground: false,
          interruptionModeIOS: InterruptionModeIOS.DoNotMix,
          interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
          shouldDuckAndroid: true,
          playThroughEarpieceAndroid: false,
        });
      } catch (modeError) {
        console.warn('设置音频模式失败，尝试继续:', modeError);
      }
      
      // 给音频系统一些准备时间
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // 加载新的音频
      console.log('加载音频文件:', audioUriToLoad);
      
      // 尝试最多3次加载
      let newSound = null;
      let loadSuccess = false;
      let retryCount = 0;
      
      while (!loadSuccess && retryCount < 3) {
        try {
          // 如果不是第一次尝试，添加更长的延迟
          if (retryCount > 0) {
            console.log(`尝试第${retryCount + 1}次加载音频...`);
            await new Promise(resolve => setTimeout(resolve, 300));
          }
          
          // 创建音频对象
          const result = await Audio.Sound.createAsync(
            { uri: audioUriToLoad },
            { shouldPlay: false, progressUpdateIntervalMillis: 100 },
            onPlaybackStatusUpdate
          );
          
          newSound = result.sound;
          
          // 检查音频是否加载成功
          const status = await newSound.getStatusAsync();
          if (status.isLoaded) {
            loadSuccess = true;
          } else {
            console.warn(`音频加载验证失败 (尝试 ${retryCount + 1}/3)`);
            try {
              await newSound.unloadAsync();
            } catch (unloadError) {
              // 忽略卸载错误
            }
            newSound = null;
          }
        } catch (createError) {
          console.warn(`创建音频对象失败 (尝试 ${retryCount + 1}/3):`, createError);
        }
        
        retryCount++;
      }
      
      // 如果所有重试都失败
      if (!newSound || !loadSuccess) {
        console.error('多次尝试后音频加载失败');
        setError('音频加载失败');
        setIsLoading(false);
        return null;
      }
      
      // 音频加载成功
      const status = await newSound.getStatusAsync();
      
      // 强制手动更新一次状态
      onPlaybackStatusUpdate(status);
      
      // 更新引用
      setSound(newSound);
      setAudioUri(audioUriToLoad);
      setIsLoading(false);
      
      // 安全地访问durationMillis属性
      if (status.isLoaded) {
        console.log('音频加载成功，总时长:', status.durationMillis, 'ms');
      } else {
        console.log('音频加载成功，但无法获取时长信息');
      }
      
      return newSound;
    } catch (error: any) {
      console.error('音频处理失败:', error.message || error);
      setError(`音频处理失败: ${error.message || '未知错误'}`);
      setIsLoading(false);
      return null;
    }
  }, [sound, isAudioReady, onPlaybackStatusUpdate]);

  // 播放或暂停
  const togglePlay = useCallback(async () => {
    if (!isAudioReady) {
      console.warn('音频系统尚未准备好');
      return;
    }
    
    // 如果没有音频对象，直接返回
    if (!sound) {
      console.warn('没有音频文件可播放');
      setError('没有音频文件可播放');
      return;
    }
    
    try {
      // 处理暂停操作
      if (isPlaying) {
        // 安全暂停 - 先检查音频是否加载
        try {
          // 首先获取状态确认音频已加载
          const status = await sound.getStatusAsync().catch(() => null);
          
          if (status && status.isLoaded) {
            // 音频已加载，可以安全暂停
            await sound.pauseAsync();
            console.log('音频已暂停');
          } else {
            // 音频未加载，直接修改状态
            console.log('音频未加载，直接修改状态为暂停');
          }
        } catch (pauseError) {
          console.error('暂停音频失败:', pauseError);
        }
        
        // 无论暂停成功或失败，都设置状态为暂停
        setIsPlaying(false);
        return; // 暂停操作完成，直接返回
      }
      
      // 以下是播放操作的处理
      // 首先尝试直接播放音频
      try {
        // 获取当前状态检查是否播放完成
        const status = await sound.getStatusAsync();
        
        // 如果已经播放完成，从头开始播放
        if (status.isLoaded && status.positionMillis === status.durationMillis) {
          await sound.setPositionAsync(0);
        }
        
        // 确保回调正确
        await sound.setOnPlaybackStatusUpdate(onPlaybackStatusUpdate);
        
        // 尝试播放
        console.log('尝试直接播放音频...');
        await sound.playAsync();
        console.log('音频开始播放成功');
        return; // 播放成功，直接返回
      } catch (directPlayError: any) {
        // 直接播放失败，进入恢复流程
        console.warn('直接播放失败，尝试恢复...', directPlayError.message || directPlayError);
      }
      
      // 如果没有音频URI，无法进行恢复
      if (!audioUri) {
        console.error('没有音频URI，无法恢复播放');
        setError('没有有效的音频文件可播放');
        return;
      }
      
      // 进入恢复流程：重新加载并播放音频
      console.log('开始音频恢复流程...');
      
      // 先尝试卸载当前音频
      try {
        await sound.unloadAsync();
      } catch (unloadError) {
        console.warn('卸载音频失败，继续尝试:', unloadError);
      }
      
      // 创建新的音频对象
      try {
        console.log('重新加载音频:', audioUri);
        const { sound: newSound } = await Audio.Sound.createAsync(
          { uri: audioUri },
          { shouldPlay: false, progressUpdateIntervalMillis: 100 },
          onPlaybackStatusUpdate
        );
        
        // 更新引用
        setSound(newSound);
        console.log('音频重新加载成功');
        
        // 添加短暂延迟确保音频准备好
        await new Promise(resolve => setTimeout(resolve, 200));
        
        // 立即播放新加载的音频
        await newSound.playAsync();
        console.log('恢复成功：新加载的音频开始播放');
        
        // 更新UI状态
        const playingStatus = await newSound.getStatusAsync();
        onPlaybackStatusUpdate(playingStatus);
      } catch (reloadError) {
        console.error('音频恢复失败:', reloadError);
        setError('无法播放音频，请重试');
      }
    } catch (error: any) {
      console.error('音频播放过程中发生意外错误:', error);
      setError(`音频播放失败: ${error.message || '未知错误'}`);
    }
  }, [sound, isPlaying, isAudioReady, onPlaybackStatusUpdate, audioUri]);

  // 跳转到指定位置
  const seekTo = useCallback(async (percent: number) => {
    try {
      if (!sound) {
        console.warn('没有加载音频，无法跳转');
        return;
      }
      
      // 尝试获取音频状态
      let status;
      try {
        status = await sound.getStatusAsync();
      } catch (statusError) {
        console.error('获取音频状态失败:', statusError);
        setError('获取音频状态失败');
        return;
      }
      
      // 检查声音是否已加载
      if (!status.isLoaded) {
        console.warn('音频未正确加载，尝试恢复...');
        
        // 如果没有音频URI，无法进行恢复
        if (!audioUri) {
          console.error('没有音频URI，无法恢复进度调整');
          setError('音频未加载，无法调整进度');
          return;
        }
        
        try {
          // 尝试重新设置回调
          await sound.setOnPlaybackStatusUpdate(onPlaybackStatusUpdate);
          status = await sound.getStatusAsync();
          
          // 如果仍然未加载，尝试重新加载
          if (!status.isLoaded) {
            console.warn('尝试重新加载音频来调整进度');
            
            try {
              // 先尝试卸载当前音频
              await sound.unloadAsync();
            } catch (unloadError) {
              console.warn('卸载音频失败，继续尝试:', unloadError);
            }
            
            // 创建新的音频对象
            const { sound: newSound } = await Audio.Sound.createAsync(
              { uri: audioUri },
              { shouldPlay: false, progressUpdateIntervalMillis: 100 },
              onPlaybackStatusUpdate
            );
            
            // 更新引用
            setSound(newSound);
            
            // 获取新状态
            status = await newSound.getStatusAsync();
            if (!status.isLoaded) {
              console.error('重新加载后仍然无法获取音频状态');
              setError('无法调整音频进度，请重试');
              return;
            }
            
            console.log('成功重新加载音频以调整进度');
            
            // 确保百分比在0-100之间
            const clampedPercent = Math.max(0, Math.min(100, percent));
            const newPosition = Math.floor((clampedPercent / 100) * (status.durationMillis || 0));
            
            // 使用新的音频对象设置位置
            await newSound.setPositionAsync(newPosition);
            setPosition(newPosition);
            setProgress(clampedPercent);
            
            // 播放音频
            await newSound.playAsync();
            return;
          }
        } catch (recoveryError) {
          console.error('恢复音频状态失败:', recoveryError);
          setError('调整进度时发生错误，请重试');
          return;
        }
      }
      
      // 确保百分比在0-100之间
      const clampedPercent = Math.max(0, Math.min(100, percent));
      const newPosition = Math.floor((clampedPercent / 100) * (status.durationMillis || duration));
      
      try {
        await sound.setPositionAsync(newPosition);
        setPosition(newPosition);
        setProgress(clampedPercent);
        console.log(`音频进度已调整到 ${clampedPercent}%`);
      } catch (seekError) {
        console.error('设置音频位置失败:', seekError);
        setError('无法调整到指定位置');
        
        // 尝试播放音频（从头开始）
        try {
          console.warn('尝试从头开始播放');
          await sound.setPositionAsync(0);
          await sound.playAsync();
        } catch (finalError) {
          console.error('最终播放尝试也失败:', finalError);
        }
      }
    } catch (error: any) {
      console.error('跳转音频位置失败:', error.message || error);
      setError(`调整播放位置失败: ${error.message || '未知错误'}`);
    }
  }, [sound, duration, audioUri, onPlaybackStatusUpdate]);

  // 格式化时间
  const formatTime = useCallback((milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  return {
    isPlaying,
    duration,
    position,
    progress,
    isLoading,
    error,
    audioUri,
    isAudioReady,
    loadAudio,
    togglePlay,
    seekTo,
    formatTime,
    sound,
    setIsPlaying,
    stopPlayback
  };
}
