import { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useAuthModal } from '@/components/AuthProvider';
import { LoginPrompt } from '@/components/LoginPrompt';

/**
 * 授权和权限检查Hook
 * 提供统一的登录和VIP权限检查功能
 */
export const useAuth = () => {
  const { user, isAuthenticated, isVIP } = useSelector((state: RootState) => state.auth);
  const [loginPromptVisible, setLoginPromptVisible] = useState(false);
  const [loginPromptTitle, setLoginPromptTitle] = useState('需要登录');
  const [loginPromptMessage, setLoginPromptMessage] = useState('请先登录以使用此功能');
  
  // 使用 AuthModal context
  const { showLoginModal, hideLoginModal, loginModalVisible } = useAuthModal();
  
  // 当用户已登录时，自动隐藏登录提示
  useEffect(() => {
    if (isAuthenticated && loginPromptVisible) {
      console.log('useAuth: 用户已登录，隐藏登录提示');
      setLoginPromptVisible(false);
    }
  }, [isAuthenticated, loginPromptVisible]);
  
  /**
   * 检查用户是否已登录
   * 若未登录，可选择显示登录提示或直接显示登录模态框
   * @param options 配置选项
   * @returns 用户是否已登录
   */
  const checkIsLoggedIn = useCallback((options?: {
    showPrompt?: boolean; // 是否显示登录提示
    redirectToLogin?: boolean; // 是否直接显示登录模态框
    title?: string; // 自定义提示标题
    message?: string; // 自定义提示消息
  }) => {
    const { 
      showPrompt = true, 
      redirectToLogin = false,
      title = '需要登录',
      message = '请先登录以使用此功能'
    } = options || {};
    
    if (!isAuthenticated) {
      if (redirectToLogin) {
        console.log('useAuth: 直接显示登录模态框');
        showLoginModal();
      } else if (showPrompt) {
        console.log('useAuth: 显示登录提示框');
        setLoginPromptTitle(title);
        setLoginPromptMessage(message);
        setLoginPromptVisible(true);
      }
      
      return false;
    }
    
    return true;
  }, [isAuthenticated, showLoginModal]);
  
  /**
   * 检查用户是否为VIP
   * 未登录时可选择先检查登录状态
   * @param options 配置选项
   * @returns 用户是否为VIP
   */
  const checkIsVIP = (options?: {
    checkLoginFirst?: boolean; // 是否先检查登录状态
    showPrompt?: boolean; // 未登录时是否显示登录提示
    redirectToLogin?: boolean; // 未登录时是否直接显示登录模态框
    loginTitle?: string; // 登录提示标题
    loginMessage?: string; // 登录提示消息
    vipTitle?: string; // VIP提示标题
    vipMessage?: string; // VIP提示消息
  }) => {
    const {
      checkLoginFirst = true,
      showPrompt = true,
      redirectToLogin = false,
      loginTitle = '需要登录',
      loginMessage = '请先登录以使用此功能',
      vipTitle = 'VIP专属功能',
      vipMessage = '此功能仅限VIP会员使用，请先升级会员'
    } = options || {};
    
    // 先检查登录状态
    if (checkLoginFirst && !isAuthenticated) {
      if (redirectToLogin) {
        showLoginModal();
        return false;
      } else if (showPrompt) {
        setLoginPromptTitle(loginTitle);
        setLoginPromptMessage(loginMessage);
        setLoginPromptVisible(true);
        return false;
      }
      return false;
    }
    
    // 检查VIP状态
    if (!isVIP) {
      if (showPrompt) {
        setLoginPromptTitle(vipTitle);
        setLoginPromptMessage(vipMessage);
        setLoginPromptVisible(true);
      }
      return false;
    }
    
    return true;
  };
  
  /**
   * 隐藏登录提示
   */
  const hideLoginPrompt = useCallback(() => {
    console.log('useAuth: 隐藏登录提示');
    setLoginPromptVisible(false);
  }, []);
  
  return {
    user,
    isAuthenticated,
    isVIP,
    checkIsLoggedIn,
    checkIsVIP,
    loginPromptVisible,
    loginPromptTitle,
    loginPromptMessage,
    hideLoginPrompt,
    showLoginModal,
    hideLoginModal,
    loginModalVisible,
    LoginPromptComponent: LoginPrompt
  };
}; 