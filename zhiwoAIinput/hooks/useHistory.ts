/**
 * 历史记录钩子函数
 */
import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { 
  loadHistoryRecords,
  loadGroupedHistoryRecords,
  getHistoryRecordById,
  deleteHistoryRecord,
  clearAllHistoryRecords,
  setSelectedRecordId,
  clearSelectedRecord,
  resetHistoryError
} from '@/store/slices/historySlice';
import { HistoryRecord } from '@/services/storageService';
import * as HistoryService from '@/services/storageService';

/**
 * 历史记录钩子
 * @returns 历史记录相关状态和函数
 */
export function useHistory() {
  const dispatch = useDispatch<AppDispatch>();
  const { 
    records, 
    groupedRecords,
    selectedRecordId,
    selectedRecord,
    loading, 
    error 
  } = useSelector((state: RootState) => state.history);
  
  // 获取数据库最后更新时间戳
  const getLastUpdatedTimestamp = useCallback(async () => {
    try {
      // 获取最新时间戳
      return await HistoryService.getLastUpdatedTimestamp();
    } catch (error) {
      console.error('获取数据库时间戳失败:', error);
      return null;
    }
  }, []);

  // 加载所有历史记录
  const loadAllRecords = useCallback(async () => {
    const result = await dispatch(loadHistoryRecords());
    return result.meta.requestStatus === 'fulfilled';
  }, [dispatch]);
  
  // 加载分组历史记录
  const loadGroupedRecords = useCallback(async (limit?: number, t?: (key: string) => string) => {
    try {
      return await HistoryService.getGroupedHistoryRecords(limit, t);
    } catch (error) {
      console.error('加载分组历史记录失败:', error);
      return [];
    }
  }, []);
  
  // 获取所有模板名称
  const getAllTemplateNames = useCallback(async () => {
    try {
      return await HistoryService.getAllTemplateNamesFromHistory();
    } catch (error) {
      console.error('获取模板名称失败:', error);
      return [];
    }
  }, []);
  
  // 根据模板名称加载分组历史记录
  const loadGroupedRecordsByTemplate = useCallback(async (templateName?: string, limit?: number, t?: (key: string) => string) => {
    try {
      return await HistoryService.getGroupedHistoryRecordsByTemplate(templateName, limit, t);
    } catch (error) {
      console.error('根据模板加载历史记录失败:', error);
      return [];
    }
  }, []);
  
  // 获取指定模板的可见记录总数
  const getVisibleRecordsCountByTemplate = useCallback(async (templateName?: string) => {
    try {
      return await HistoryService.getVisibleHistoryRecordsCountByTemplate(templateName);
    } catch (error) {
      console.error('获取指定模板可见记录总数失败:', error);
      return 0;
    }
  }, []);
  
  // 获取指定模板的真实记录总数
  const getRealRecordsCountByTemplate = useCallback(async (templateName?: string) => {
    try {
      return await HistoryService.getRealHistoryRecordsCountByTemplate(templateName);
    } catch (error) {
      console.error('获取指定模板真实记录总数失败:', error);
      return 0;
    }
  }, []);
  
  // 获取历史记录详情
  const getRecordById = useCallback(async (id: string) => {
    const result = await dispatch(getHistoryRecordById(id));
    return result.meta.requestStatus === 'fulfilled';
  }, [dispatch]);
  
  // 获取隐藏记录数量
  const getHiddenRecordsCountByTemplate = useCallback(async (templateName?: string) => {
    try {
      return await HistoryService.getHiddenRecordsCountByTemplate(templateName);
    } catch (error) {
      console.error('获取指定模板隐藏记录数失败:', error);
      return 0;
    }
  }, []);
  
  // 获取可见范围内的模板名称
  const getAvailableTemplateNamesInVisibleRange = useCallback(async () => {
    try {
      return await HistoryService.getAvailableTemplateNamesInVisibleRange();
    } catch (error) {
      console.error('获取可见范围内的模板名称失败:', error);
      return [];
    }
  }, []);
  
  // 删除历史记录
  const deleteRecord = useCallback(async (id: string) => {
    const result = await dispatch(deleteHistoryRecord(id));
    return result.meta.requestStatus === 'fulfilled';
  }, [dispatch]);
  
  // 清空所有历史记录
  const clearAllRecords = useCallback(async () => {
    const result = await dispatch(clearAllHistoryRecords());
    return result.meta.requestStatus === 'fulfilled';
  }, [dispatch]);
  
  // 设置选中的历史记录ID
  const selectRecord = useCallback((id: string) => {
    dispatch(setSelectedRecordId(id));
    dispatch(getHistoryRecordById(id));
  }, [dispatch]);
  
  // 清除选中的历史记录
  const clearSelected = useCallback(() => {
    dispatch(clearSelectedRecord());
  }, [dispatch]);
  
  // 清除错误
  const clearError = useCallback(() => {
    dispatch(resetHistoryError());
  }, [dispatch]);
  
  return {
    // 状态
    records,
    groupedRecords,
    selectedRecordId,
    selectedRecord,
    loading,
    error,
    // 操作函数
    loadAllRecords,
    loadGroupedRecords,
    getAllTemplateNames,
    loadGroupedRecordsByTemplate,
    getVisibleRecordsCountByTemplate,
    getRealRecordsCountByTemplate,
    getRecordById,
    getHiddenRecordsCountByTemplate,
    getAvailableTemplateNamesInVisibleRange,
    deleteRecord,
    clearAllRecords,
    selectRecord,
    clearSelected,
    clearError,
    getLastUpdatedTimestamp
  };
} 