import { useColorScheme } from 'react-native';
import { useSelector } from 'react-redux';
import { selectThemeMode } from '@/store/slices/themeSlice';

/**
 * 获取应用当前主题
 * 根据用户选择的主题模式返回实际的主题色
 * 
 * @returns 'light' | 'dark' 当前应用的主题
 */
export function useAppTheme() {
  const themeMode = useSelector(selectThemeMode);
  const systemTheme = useColorScheme();
  
  // 如果设置为跟随系统，则返回系统主题
  // 如果系统主题为null，默认使用浅色模式
  if (themeMode === 'system') {
    return systemTheme ?? 'light';
  }
  
  // 否则返回用户设置的主题
  return themeMode;
} 