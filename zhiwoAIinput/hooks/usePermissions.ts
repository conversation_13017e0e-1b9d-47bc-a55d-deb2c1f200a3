/**
 * 权限管理Hook
 * 提供权限检查、请求和引导功能
 */
import { useState, useCallback, useEffect } from 'react';
import {
  ensurePermissions,
  quickCheckPermissions,
  setPermissionGuideCallback,
  checkPermissionStatus,
  type PermissionType,
  type PermissionStatus,
} from '@/services/permissionService';
import { log } from '@/services/logService';

export interface UsePermissionsResult {
  // 权限状态
  permissionStatus: PermissionStatus;
  isCheckingPermissions: boolean;
  
  // 权限引导Modal状态
  showPermissionGuide: boolean;
  permissionGuideType: PermissionType;
  
  // 方法
  checkPermissions: (type: PermissionType) => Promise<boolean>;
  ensurePermissionsAsync: (type: PermissionType) => Promise<boolean>;
  refreshPermissionStatus: () => Promise<void>;
  showPermissionModal: (type: PermissionType) => void;
  hidePermissionModal: () => void;
  onPermissionGranted: () => void;
}

/**
 * 权限管理Hook
 */
export const usePermissions = (): UsePermissionsResult => {
  const [permissionStatus, setPermissionStatus] = useState<PermissionStatus>({
    microphone: false,
    speech: false,
  });
  const [isCheckingPermissions, setIsCheckingPermissions] = useState(false);
  const [showPermissionGuide, setShowPermissionGuide] = useState(false);
  const [permissionGuideType, setPermissionGuideType] = useState<PermissionType>('both');

  // 刷新权限状态
  const refreshPermissionStatus = useCallback(async () => {
    try {
      setIsCheckingPermissions(true);
      const status = await checkPermissionStatus();
      setPermissionStatus(status);
      log('[权限Hook] 权限状态已更新:', status);
    } catch (error) {
      log('[权限Hook] 刷新权限状态失败:', error);
    } finally {
      setIsCheckingPermissions(false);
    }
  }, []);

  // 快速检查权限（不请求）
  const checkPermissions = useCallback(async (type: PermissionType): Promise<boolean> => {
    try {
      const result = await quickCheckPermissions(type);
      log(`[权限Hook] 快速检查权限 ${type}:`, result);
      return result;
    } catch (error) {
      log('[权限Hook] 快速检查权限失败:', error);
      return false;
    }
  }, []);

  // 确保权限（会触发权限引导）
  const ensurePermissionsAsync = useCallback(async (type: PermissionType): Promise<boolean> => {
    try {
      log(`[权限Hook] 确保权限: ${type}`);
      const result = await ensurePermissions(type);
      
      // 更新权限状态
      await refreshPermissionStatus();
      
      return result;
    } catch (error) {
      log('[权限Hook] 确保权限失败:', error);
      return false;
    }
  }, [refreshPermissionStatus]);

  // 显示权限引导Modal
  const showPermissionModal = useCallback((type: PermissionType) => {
    log(`[权限Hook] 显示权限引导Modal: ${type}`);
    setPermissionGuideType(type);
    setShowPermissionGuide(true);
  }, []);

  // 隐藏权限引导Modal
  const hidePermissionModal = useCallback(() => {
    log('[权限Hook] 隐藏权限引导Modal');
    setShowPermissionGuide(false);
  }, []);

  // 权限授予后的回调
  const onPermissionGranted = useCallback(async () => {
    log('[权限Hook] 权限已授予，刷新状态');
    await refreshPermissionStatus();
    hidePermissionModal();
  }, [refreshPermissionStatus, hidePermissionModal]);

  // 设置权限引导回调
  useEffect(() => {
    log('[权限Hook] 设置权限引导回调');
    setPermissionGuideCallback(showPermissionModal);
    
    // 初始化权限状态
    refreshPermissionStatus();
    
    // 清理回调
    return () => {
      setPermissionGuideCallback(null);
    };
  }, [showPermissionModal, refreshPermissionStatus]);

  return {
    // 状态
    permissionStatus,
    isCheckingPermissions,
    showPermissionGuide,
    permissionGuideType,
    
    // 方法
    checkPermissions,
    ensurePermissionsAsync,
    refreshPermissionStatus,
    showPermissionModal,
    hidePermissionModal,
    onPermissionGranted,
  };
}; 