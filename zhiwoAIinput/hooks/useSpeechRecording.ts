/**
 * 语音录制钩子函数
 */
import { useCallback, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { Platform } from 'react-native';
import { 
  startRecording, 
  pauseRecording, 
  resumeRecording, 
  stopRecording, 
  optimizeText,
  resetRecording,
  clearError,
  updateRecognizedText,
  cancelOptimization,
  setNetworkError,
  clearNetworkError,
  setWhisperLimitError,
  clearWhisperLimitError,
  hideWhisperLimitModal
} from '@/store/slices/recordingSlice';
import * as Haptics from 'expo-haptics';
import * as SpeechService from '@/services/expoSpeechService';
import * as storageService from '@/services/storageService';
import { filterSensitiveWords } from '@/services/sensitiveWordService';
import { useTranslation } from 'react-i18next';
import { Alert } from 'react-native';

// 防抖函数实现
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  
  return function(...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(() => {
      func(...args);
      timeout = null;
    }, wait);
  };
}

/**
 * 安全地调用触觉反馈，在web平台上会自动跳过
 */
const safeHaptics = {
  impactAsync: async (style: Haptics.ImpactFeedbackStyle) => {
    if (Platform.OS !== 'web') {
      try {
        await Haptics.impactAsync(style);
      } catch (error) {
        console.log('触觉反馈失败，但不影响功能', error);
      }
    }
  },
  notificationAsync: async (type: Haptics.NotificationFeedbackType) => {
    if (Platform.OS !== 'web') {
      try {
        await Haptics.notificationAsync(type);
      } catch (error) {
        console.log('触觉反馈失败，但不影响功能', error);
      }
    }
  }
};

/**
 * 语音录制钩子
 * @returns 语音录制相关状态和函数
 */
export function useSpeechRecording() {
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation();
  const { 
    recordingStatus, 
    recognizedText, 
    optimizedText, 
    audioUri,
    audioLevel,
    error,
    processingAI,
    selectedTemplateId,
    lastRecordingTimestamp,
    currentOptimizeRequestId
  } = useSelector((state: RootState) => state.recording);
  
  // 添加对优化结果的引用，避免闭包陷阱
  const optimizedTextRef = useRef(optimizedText);
  useEffect(() => {
    optimizedTextRef.current = optimizedText;
  }, [optimizedText]);
  
  // 在组件卸载时自动取消进行中的请求并释放资源
  useEffect(() => {
    return () => {
      console.log('语音录制钩子卸载，执行清理...');
      
      // 释放录音资源
      const cleanup = async () => {
        try {
          // 如果正在录音，先停止录音
          if (recordingStatus === 'recording' || recordingStatus === 'paused') {
            console.log('检测到录音未停止，正在停止录音...');
            // 使用服务直接停止，而不是通过Redux action，避免循环依赖
            await SpeechService.releaseAudioResources();
            console.log('录音资源已释放');
          }
          
          // 取消进行中的优化请求
          if (currentOptimizeRequestId) {
            console.log(`取消进行中的优化请求: ${currentOptimizeRequestId}`);
            dispatch(cancelOptimization());
          }
        } catch (error) {
          console.error('清理录音资源时出错:', error);
        }
      };
      
      cleanup();
    };
  }, [dispatch, currentOptimizeRequestId, recordingStatus]);
  
  // 开始录音
  const startRecordingHandler = useCallback(async () => {
    // 触发触觉反馈
    await safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    const result = await dispatch(startRecording());
    return result.meta.requestStatus === 'fulfilled';
  }, [dispatch]);
  
  // 暂停录音
  const pauseRecordingHandler = useCallback(async () => {
    // 触发触觉反馈
    await safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    const result = await dispatch(pauseRecording());
    return result.meta.requestStatus === 'fulfilled';
  }, [dispatch]);
  
  // 继续录音
  const resumeRecordingHandler = useCallback(async () => {
    // 触发触觉反馈
    await safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    const result = await dispatch(resumeRecording());
    return result.meta.requestStatus === 'fulfilled';
  }, [dispatch]);
  


  // 创建防抖版本的更新文本函数 - 包含敏感词检查，命中时终止流程
  const debouncedUpdateText = useCallback(
    debounce(async (text: string) => {
      console.log('防抖更新文本:', text.substring(0, 30) + '...');
      
      // 先进行敏感词检查，命中时直接终止
      try {
        const filterResult = await filterSensitiveWords(text);
        
        if (filterResult.isFiltered) {
          console.log('检测到敏感词，终止流程:', filterResult.detectedWords);
          
          // 显示敏感词提示弹窗
          Alert.alert(
            t('common.sensitiveWord.filterTitle'),
            t('common.sensitiveWord.filterMessage'),
            [
              {
                text: t('common.ok'),
                style: 'default'
              }
            ]
          );
          
          // 直接返回，不更新文本，不显示转录结果
          return;
        }
      } catch (sensitiveWordError) {
        console.warn('敏感词检查失败，继续正常流程:', sensitiveWordError);
        // 检查失败时继续正常流程
      }
      
      // 敏感词检查通过，更新Redux状态
      dispatch(updateRecognizedText(text));
    }, 300),
    [dispatch, t]
  );

  // 停止录音
  const stopRecordingHandler = useCallback(async (customText?: string, handleCallbackExternally: boolean = false) => {
    try {
      // 检查是否处于录音状态
      if (recordingStatus !== 'recording' && recordingStatus !== 'paused') {
        console.log('当前不在录音状态，无需停止');
        return 'stopped';
      }

      // 触发触觉反馈，提示用户操作正在处理
      await safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // 标记是否已处理完成，避免多次处理同一录音
      let isAlreadyProcessed = false;

      // 如果提供了手动文本，优先使用它
      if (customText) {
        console.log('使用自定义/已存在文本长度:', customText.length);
        // 使用防抖函数更新Redux状态
        debouncedUpdateText(customText);
      }
      
      // 开始停止录音，这里先获取设置，确保使用正确的转写模型
      const settings = await storageService.getUserSettings();
      console.log('从存储中获取的原始值:', JSON.stringify(settings));
      console.log('解析后的用户设置对象:', JSON.stringify(settings));
      console.log('语言模型设置:', settings.languageModel);
      console.log('语言模型设置类型:', typeof settings.languageModel);

      // 获取转写模型设置，并检查认证状态
      let selectedModel = settings.transcribeModel || 'native';
      
      // 检查用户认证状态，如果未认证且选择了需要认证的模型，才使用native模型
      try {
        const authState = await dispatch((dispatch, getState) => {
          return getState().auth.isAuthenticated;
        });
        
        // 只有在未认证且使用需要认证的模型时才强制切换
        // whisper-1模型对所有用户开放，包括未认证用户
        if (!authState && selectedModel === 'gpt-4o-mini-transcribe') {
          console.log('[认证检查] 用户未认证但选择了需要认证的模型，切换到whisper-1模型，原模型:', selectedModel);
          selectedModel = 'whisper-1'; // 切换到whisper-1而不是native
          
          // 同时更新存储中的设置，确保一致性
          await storageService.saveUserSettings({
            transcribeModel: 'whisper-1'
          });
          console.log('[认证检查] 已将未认证用户的转写模型调整为whisper-1');
        }
      } catch (authCheckError) {
        console.error('[认证检查] 检查认证状态失败，保持当前模型:', authCheckError);
        // 出错时不强制修改用户的选择
      }
      
      const isWhisperMode = selectedModel === 'whisper-1';
      
      // 通知Redux停止录音
      dispatch(stopRecording(customText));
      console.log('停止录音，使用文本长度:', customText?.length || 0);

      // 停止录音服务，使用对错误有更强容错能力的回调
      try {
        const status = await SpeechService.stopRecording({
          onResult: (text: string) => {
            // 如果已经处理过或者指定由外部处理回调，则不处理回调
            if (isAlreadyProcessed || handleCallbackExternally) {
              console.log('已处理过此录音或由外部处理，忽略回调:', text.substring(0, 30) + '...');
              return;
            }
            
            // 检查是否为进度状态更新
            const isStatusUpdate = 
              text.startsWith("正在录音中...") || 
              text.startsWith("正在聆听中...") ||
              text.includes("正在使用") ||
              text.includes("转写中");
              
            if (!isStatusUpdate && text) {
              console.log('停止录音回调 - 更新最终转写结果:', text.substring(0, 50) + '...');
              // 使用防抖函数更新Redux状态
              debouncedUpdateText(text);
              // 标记已处理，防止重复处理同一录音
              isAlreadyProcessed = true;
            } else {
              console.log('停止录音回调 - 收到进度或空更新，内容:', text);
            }
          },
          onError: (error: Error) => {
            // 错误不应该中断流程
            console.error('停止录音回调 onError:', error);
            
            // 对于"录音器不存在"的错误，这通常是因为录音器已被释放，可以忽略
            if (error.message.includes('Recorder does not exist')) {
              console.log('录音器已被释放，忽略此错误并继续处理');
            } else {
              console.error('停止录音出错:', error);
              
              // 优先检查是否是whisper每日使用限制错误
              if ((error as any).isWhisperLimit) {
                console.log('[useSpeechRecording] 检测到whisper使用限制错误，设置whisper限制状态');
                
                // 设置whisper限制错误状态，触发VIP升级引导
                dispatch(setWhisperLimitError({
                  message: error.message,
                  usageInfo: (error as any).usageInfo || {},
                  requiresVipUpgrade: true
                }));
                
                return;
              }
              
              // 扩展网络错误检测范围，但排除whisper限制错误
              const errorMessage = error.message || '';
              const isNetworkError = 
                errorMessage.includes('网络') ||
                errorMessage.includes('超时') ||
                errorMessage.includes('timeout') ||
                errorMessage.includes('network') ||
                errorMessage.includes('连接') ||
                errorMessage.includes('请求失败') ||
                errorMessage.includes('Failed to fetch') ||
                errorMessage.includes('Network request failed') ||
                errorMessage.includes('No network') ||
                errorMessage.includes('Connection') ||
                errorMessage.includes('ENOTFOUND') ||
                errorMessage.includes('ENETUNREACH') ||
                errorMessage.includes('ECONNREFUSED') ||
                (errorMessage.includes('语音转写失败') && !(error as any).isWhisperLimit);  // 只有非whisper限制的转写失败才算网络问题
                
              // 音频文件相关的真正错误
              const isAudioError = 
                errorMessage.includes('音频文件') ||
                errorMessage.includes('audio file') ||
                errorMessage.includes('文件损坏') ||
                errorMessage.includes('文件不存在') ||
                errorMessage.includes('invalid audio') ||
                errorMessage.includes('corrupted');
                
              if (isNetworkError && !isAudioError) {
                console.log('检测到网络错误，设置网络错误状态');
                dispatch(setNetworkError('网络连接错误，语音转写失败'));
              } else if (isAudioError) {
                console.log('检测到音频文件错误，不设置网络错误状态');
                // 音频错误不设置网络错误状态，让其他错误处理机制处理
              } else {
                console.log('其他类型错误，设置通用网络错误状态');
                dispatch(setNetworkError('网络连接错误，语音转写失败'));
              }
            }
          },
        });

        console.log('停止录音处理完成，返回状态:', status);
        
        // 如果是Whisper模式且不是由外部处理回调，等待最终转写结果
        if (isWhisperMode && !isAlreadyProcessed && !handleCallbackExternally) {
          console.log('等待Whisper模型转写处理完成...');
          
          // 检查录音状态，如果已经是error状态，说明服务层已经处理了错误
          if (status === 'error') {
            console.log('使用Whisper模式且由内部处理转写结果，跳过后续处理');
            return status;
          }
          
          // 使用更智能的等待机制，而不是固定时间
          let retryCount = 0;
          const maxRetries = 5;
          let validTextFound = false;
          
          while (retryCount < maxRetries && !validTextFound) {
            // 每次尝试间隔增加
            await new Promise(resolve => setTimeout(resolve, 800 + retryCount * 400));
            retryCount++;
            
            // 获取当前识别文本
            const currentText = await dispatch((dispatch, getState) => {
              return getState().recording.recognizedText || '';
            });
            
            // 检查文本是否为有效文本
            const isStatusUpdate = 
              currentText.startsWith("正在录音中...") || 
              currentText.startsWith("正在聆听中...") ||
              currentText.includes("已录制") ||
              currentText.includes("正在使用") ||
              currentText.includes("转写中");
            
              // 更强健的文本有效性判断
            const hasValidText = 
              currentText && 
              !isStatusUpdate && 
              currentText.trim().length > 0;
            
            if (hasValidText) {
              console.log(`第${retryCount}次尝试找到有效转写结果:`, currentText.substring(0, 50) + '...');
              
              // 使用防抖函数更新Redux状态
              debouncedUpdateText(currentText);
              
              // 标记已找到有效文本
              validTextFound = true;
              isAlreadyProcessed = true;
              break;
            } else {
              console.log(`第${retryCount}次尝试未找到有效转写结果`);
            }
          }
          
          // 如果所有重试后仍未找到有效文本，检查是否有错误信息
          if (!validTextFound) {
            const currentText = await dispatch((dispatch, getState) => {
              return getState().recording.recognizedText || '';
            });
            
            // 检查是否有错误信息
            const hasErrorMessage = currentText && currentText.includes("转写失败");
            if (hasErrorMessage) {
              console.log('检测到转写错误信息:', currentText);
              // 标记已处理
              isAlreadyProcessed = true;
            }
          }
          // 已在循环中处理完毕
        } else if (isWhisperMode && status !== 'error') {
          console.log('使用Whisper模式且由内部处理转写结果，跳过后续处理');
        }

        // 返回最终录音状态
        return status;
      } catch (stopError) {
        console.error('调用语音服务停止录音失败:', stopError);
        
        // 即使停止录音出错，也尝试获取现有的转写结果
        const currentText = await dispatch((dispatch, getState) => {
          return getState().recording.recognizedText || '';
        });
        
        if (currentText && currentText.trim().length > 0) {
          console.log('尽管停止录音出错，但已有有效转写文本:', currentText);
          // 无需操作，Redux状态中已有文本
        } else {
          console.log('停止录音出错且无有效转写文本，可能需要使用备用方法或提示用户重试');
        }
        
        return 'stopped'; // 即使出错也当作已停止处理
      }
    } catch (error) {
      console.error('停止录音错误:', error);
      // 确保即使出错，Redux状态也被正确设置为已停止
      dispatch(stopRecording(""));
      
      // 尝试查看当前是否已经有有效的转写文本
      try {
        const currentText = await dispatch((dispatch, getState) => {
          return getState().recording.recognizedText || '';
        });
        
        if (currentText && currentText.trim().length > 0) {
          console.log('尽管完全失败，但已有有效转写文本:', currentText);
          return 'stopped'; // 有文本则当作成功处理
        }
      } catch (getTextError) {
        console.error('获取当前转写文本失败:', getTextError);
      }
      
      return 'error';
    }
  }, [dispatch, recordingStatus]);
  
  // 添加优化请求防重复锁
  const optimizeRequestLockRef = useRef(false);
  
  // 使用AI优化文本
  const optimizeTextHandler = useCallback(async (
    params: {
      text: string;
      templateId: string;
      templatePrompt: string;
      templateName: string;
      sourceRecordId?: string; // 二次优化源记录ID，可选
      isRegenerate?: boolean; // 标记是否为重新生成操作
      regenerateForSameAudio?: boolean; // 标记是否为同一音频的重新生成
      isSameTemplate?: boolean; // 标记是否为相同模板的重新生成
      isFromFastMode?: boolean; // 标记是否来自极速模式
    }
  ) => {
    console.log(`[优化钩子] 开始优化，模板: ${params.templateName}, ID: ${params.templateId}`);
    console.log(`[优化钩子] 提示词长度: ${params.templatePrompt.length}`);
    console.log(`[优化钩子] 是否来自极速模式: ${params.isFromFastMode || false}`);
    
    // 检查是否已经有优化请求在进行中
    if (optimizeRequestLockRef.current) {
      console.log('[优化钩子] 已有优化请求在进行中，跳过重复调用');
      return false;
    }
    
    // 设置请求锁
    optimizeRequestLockRef.current = true;
    console.log('[优化钩子] 设置请求锁，开始执行优化');
    
    try {
      // 动态获取当前的请求ID，而不是依赖闭包
      const getCurrentRequestId = () => {
        return (dispatch as any)((dispatch: any, getState: any) => {
          return getState().recording.currentOptimizeRequestId;
        });
      };
      
      const currentId = await getCurrentRequestId();
      
      // 智能取消逻辑：只有在特定情况下才取消之前的请求
      if (currentId) {
        if (params.isFromFastMode) {
          // 如果是极速模式触发的，但已有请求在进行中，则不取消，直接退出
          console.log(`[优化钩子] 极速模式检测到已有请求在进行中(${currentId})，跳过极速模式执行`);
          return false;
        } else {
          // 如果是用户手动触发的，则取消之前的请求
          console.log(`[优化钩子] 用户手动触发，取消之前的请求: ${currentId}`);
          dispatch(cancelOptimization());
        }
      }
      
      const result = await dispatch(optimizeText(params));
      
      // 添加直接返回值检查，便于及时获取结果
      const success = result.meta.requestStatus === 'fulfilled';
      if (success && result.payload) {
        console.log('[优化钩子] 优化成功，直接从结果中获取优化文本，长度:', 
          (result.payload as any).optimizedText?.length);
      }
      
      return success;
    } catch (error) {
      console.error('[优化钩子] 优化过程中出错:', error);
      return false;
    } finally {
      // 释放请求锁
      console.log('[优化钩子] 释放请求锁');
      optimizeRequestLockRef.current = false;
    }
  }, [dispatch]); // 移除 currentOptimizeRequestId 依赖，防止钩子重新创建
  
  // 获取最新的优化文本 - 直接从Redux存储中获取最新状态
  const getLatestOptimizedText = useCallback(async () => {
    return await dispatch((dispatch, getState) => {
      return getState().recording.optimizedText || '';
    });
  }, [dispatch]);
  
  // 取消正在进行的优化请求
  const cancelOptimizationHandler = useCallback(() => {
    if (currentOptimizeRequestId) {
      console.log(`手动取消优化请求: ${currentOptimizeRequestId}`);
      dispatch(cancelOptimization());
      return true;
    }
    return false;
  }, [dispatch, currentOptimizeRequestId]);
  
  // 重置录音状态
  const resetRecordingHandler = useCallback(() => {
    // 取消可能正在进行的请求
    if (currentOptimizeRequestId) {
      dispatch(cancelOptimization());
    }
    dispatch(resetRecording());
  }, [dispatch, currentOptimizeRequestId]);
  
  // 清除错误
  const clearErrorHandler = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);
  
  // 更新识别的文本
  const updateRecognizedTextHandler = useCallback((text: string) => {
    dispatch(updateRecognizedText(text));
    return true;
  }, [dispatch]);
  
  return {
    // 状态
    recordingStatus,
    recognizedText,
    optimizedText,
    audioUri,
    audioLevel,
    error,
    processingAI,
    selectedTemplateId,
    lastRecordingTimestamp,
    hasActiveOptimization: !!currentOptimizeRequestId,
    // 操作函数
    startRecording: startRecordingHandler,
    pauseRecording: pauseRecordingHandler,
    resumeRecording: resumeRecordingHandler,
    stopRecording: stopRecordingHandler,
    optimizeText: optimizeTextHandler,
    cancelOptimization: cancelOptimizationHandler,
    getLatestOptimizedText,
    resetRecording: resetRecordingHandler,
    clearError: clearErrorHandler,
    updateRecognizedText: updateRecognizedTextHandler
  };
} 