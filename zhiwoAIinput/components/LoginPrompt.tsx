import React, { useCallback, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, Modal, Pressable, Text } from 'react-native';
import { View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useAuthModal } from '@/components/AuthProvider';
import { useSelector } from 'react-redux';
import { selectThemeMode } from '@/store/slices/themeSlice';
import { useTranslation } from 'react-i18next';
import { log } from '@/services/logService';

// 保留这些事件名以免破坏其他代码
export const AUTH_EVENTS = {
  NAVIGATE_TO_LOGIN: 'NAVIGATE_TO_LOGIN',
  FORCE_NAVIGATE: 'FORCE_NAVIGATE'
};

interface LoginPromptProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
  onLogin?: () => void; // 新增登录回调函数
}

/**
 * 登录提示组件
 * 当用户尝试访问需要登录的功能时显示此提示
 */
export const LoginPrompt: React.FC<LoginPromptProps> = ({
  visible,
  onClose,
  title = '需要登录',
  message = '请先登录以使用此功能',
  onLogin // 新增登录回调函数
}) => {
  const themeMode = useSelector(selectThemeMode);
  const currentTheme = useAppTheme();
  const isDark = currentTheme === 'dark';
  const { showLoginModal } = useAuthModal();
  const { t } = useTranslation();
  
  // 监听主题变化
  useEffect(() => {
    if (visible) {
      log('LoginPrompt: 当前主题:', currentTheme, '主题模式:', themeMode);
    }
  }, [visible, currentTheme, themeMode]);
  
  // 使用 useCallback 避免不必要的重渲染
  const handleClose = useCallback(() => {
    if (visible) {
      onClose();
    }
  }, [visible, onClose]);
  
  // 处理登录按钮点击
  const handleLogin = useCallback(() => {
    log('LoginPrompt: 用户点击登录');
    // 关闭当前提示
    handleClose();
    // 调用登录回调
    if (onLogin) {
      onLogin();
    } else {
      // 如果没有提供回调，直接使用context里的 showLoginModal
      showLoginModal();
    }
  }, [handleClose, onLogin, showLoginModal]);

  // 获取背景颜色
  const getModalBgColor = () => {
    return isDark ? '#1F2122' : '#fff';
  };
  
  // 获取文本颜色
  const getTextColor = () => {
    return isDark ? '#ECEDEE' : '#333';
  };
  
  // 获取次要文本颜色
  const getSecondaryTextColor = () => {
    return isDark ? '#9BA1A6' : '#6B7280';
  };
  
  // 如果不可见，则返回null而不是渲染Modal
  if (!visible) {
    return null;
  }
  
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={handleClose}
    >
      <Pressable 
        style={styles.overlay} 
        onPress={handleClose}
      >
        <View 
          style={[
            styles.container, 
            { backgroundColor: getModalBgColor() }
          ]}
          // 避免点击背景时关闭模态框
          onStartShouldSetResponder={() => true}
        >
          <View style={styles.header}>
            <View style={styles.iconContainer}>
              <LinearGradient
                colors={['#8364e2', '#6a5ae1']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.iconBackground}
              >
                <IconSymbol name="lock" size={24} color="white" />
              </LinearGradient>
            </View>
            
            <Text style={[styles.title, { color: getTextColor() }]}>
              {title}
            </Text>
            
            <Text style={[styles.message, { color: getSecondaryTextColor() }]}>
              {message}
            </Text>
          </View>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.loginButton}
              onPress={handleLogin}
            >
              <LinearGradient
                colors={['#8364e2', '#6a5ae1']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.loginButtonGradient}
              >
                <Text style={styles.loginButtonText}>
                  {t("login.login" as any)}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.skipButton}
              onPress={handleClose}
            >
              <Text 
                style={[styles.skipButtonText, { color: getSecondaryTextColor() }]}
                numberOfLines={2}
                adjustsFontSizeToFit={true}
                minimumFontScale={0.8}
              >
                {t("login.skipLogin" as any)}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  container: {
    width: '100%',
    maxWidth: 340,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
  },
  header: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    marginBottom: 20,
  },
  iconBackground: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  buttonContainer: {
    width: '100%',
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: 12,
  },
  skipButton: {
    width: '100%',
    paddingVertical: 16,
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  skipButtonText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: 18,
  },
  loginButton: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    minHeight: 48,
  },
  loginButtonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: 20,
  },
}); 