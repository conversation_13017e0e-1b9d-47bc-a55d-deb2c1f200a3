import React from "react";
import { Modal, View, TouchableOpacity, Linking, Platform } from "react-native";
import { useTranslation } from "react-i18next";
import * as Application from "expo-application";
import { ThemedText } from "@/components/ThemedText";
import { useTheme } from "@react-navigation/native";

interface FeedbackModalProps {
  isVisible: boolean;
  onClose: () => void;
  onRate: () => void;
}

const FeedbackModal: React.FC<FeedbackModalProps> = ({
  isVisible,
  onClose,
  onRate,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  console.log('[FeedbackModal] Rendering. isVisible:', isVisible);

  const handleFeedback = async () => {
    console.log('[FeedbackModal] handleFeedback called');
    const deviceInfo = `
User ID: ${Application.applicationId}
Platform: ${Platform.OS}
Version: ${Application.nativeApplicationVersion}
Build: ${Application.nativeBuildVersion}
    `.trim();

    const subject = t("feedback.emailSubject", "知我AI输入法 - 用户反馈");
    const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(
      subject
    )}&body=${encodeURIComponent(deviceInfo)}`;

    try {
      await Linking.openURL(mailtoUrl);
    } catch (error) {
      console.error("[FeedbackModal] Error opening mail client:", error);
    }
    onClose();
  };

  const handleRatePress = () => {
    console.log('[FeedbackModal] handleRatePress called');
    onRate();
    onClose();
  };

  const handleBackdropPress = () => {
    console.log('[FeedbackModal] handleBackdropPress called');
    onClose();
  };

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={() => {
        console.log('[FeedbackModal] Modal onRequestClose called');
        onClose();
      }}
    >
      <TouchableOpacity
        activeOpacity={1}
        onPress={handleBackdropPress}
        className="flex-1 justify-center items-center bg-black/50"
      >
        <View
          className="w-4/5 bg-white dark:bg-gray-800 rounded-lg p-4"
          onStartShouldSetResponder={() => true}
          onTouchEnd={(e) => e.stopPropagation()}
        >
          <ThemedText className="text-lg font-bold text-center mb-4">
            {t("feedback.title", "给我们评分")}
          </ThemedText>

          <ThemedText className="text-center mb-6">
            {t("feedback.message", "您的反馈对我们很重要！")}
          </ThemedText>

          <View className="flex-row justify-center space-x-4">
            <TouchableOpacity
              onPress={handleRatePress}
              className="flex-1 bg-blue-500 p-3 rounded-lg"
            >
              <ThemedText className="text-white text-center font-semibold">
                {t("feedback.rateUs", "给好评")}
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleFeedback}
              className="flex-1 bg-gray-200 dark:bg-gray-600 p-3 rounded-lg"
            >
              <ThemedText className="text-center font-semibold">
                {t("feedback.sendFeedback", "反馈问题")}
              </ThemedText>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            onPress={() => {
              console.log('[FeedbackModal] Cancel button pressed');
              onClose();
            }}
            className="mt-4"
          >
            <ThemedText className="text-center text-gray-500">
              {t("common.cancel", "取消")}
            </ThemedText>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

export default FeedbackModal;
