import React from "react";
import { Modal, View, Text, StyleSheet, TouchableOpacity } from "react-native";
import LottieView from "lottie-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useAppTheme } from "@/hooks/useAppTheme";
import { useTranslation } from "react-i18next";

interface LoginGuideModalProps {
  visible: boolean;
  onLogin: () => void;
  onSkip: () => void;
  animationSource?: any; // Lottie动画资源
}

const LoginGuideModal: React.FC<LoginGuideModalProps> = ({
  visible,
  onLogin,
  onSkip,
  animationSource,
}) => {
  const theme = useAppTheme();
  const isDark = theme === "dark";
  const { t } = useTranslation();

  if (!visible) return null;

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View
          style={[
            styles.container,
            { backgroundColor: isDark ? "#23272E" : "#fff" },
          ]}
        >
          <View style={styles.lottieWrapper}>
            <LottieView
              source={
                animationSource || require("@/assets/animations/initial.json")
              }
              autoPlay
              loop
              style={styles.lottie}
            />
          </View>
          <Text
            style={[styles.title, { color: isDark ? "#ECEDEE" : "#23272E" }]}
          >
            {t("loginGuide.title")}
          </Text>
          <Text
            style={[styles.desc, { color: isDark ? "#9BA1A6" : "#6B7280" }]}
          >
            {t("loginGuide.desc")}
          </Text>
          {/* 登录按钮在上方，主按钮样式 */}
          <View style={{ width: "80%", marginTop: 16 }}>
            <TouchableOpacity
              onPress={onLogin}
              activeOpacity={0.8}
              style={{ width: "100%" }}
            >
              <LinearGradient
                colors={["#8364e2", "#6a5ae1"]}
                style={styles.loginGradient}
              >
                <Text style={styles.loginText}>{t("loginGuide.login")}</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
          {/* skip按钮在下方，纯文字按钮样式，无下划线 */}
          <TouchableOpacity style={styles.skipBtnTextOnly} onPress={onSkip}>
            <Text
              style={[
                styles.skipTextTextOnly,
                {
                  color: isDark ? "#9BA1A6" : "#6B7280",
                  textDecorationLine: "none",
                },
              ]}
            >
              {t("loginGuide.skip")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  container: {
    width: "100%",
    maxWidth: 340,
    borderRadius: 20,
    paddingVertical: 28,
    paddingHorizontal: 0,
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowRadius: 16,
    shadowOffset: { width: 0, height: 4 },
    elevation: 8,
  },
  lottieWrapper: {
    width: "100%",
    height: 120,
    marginBottom: 12,
  },
  lottie: {
    width: "100%",
    height: "100%",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
    paddingHorizontal: 20,
  },
  desc: {
    fontSize: 15,
    marginBottom: 24,
    textAlign: "center",
    lineHeight: 22,
    paddingHorizontal: 20,
  },
  buttonRow: {
    flexDirection: "row",
    width: "100%",
    justifyContent: "space-between",
    marginTop: 8,
    paddingHorizontal: 20,
  },
  skipBtn: {
    flex: 1,
    marginRight: 10,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: "transparent",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#e5e7eb",
  },
  skipText: {
    fontSize: 15,
  },
  loginBtn: {
    flex: 1,
    borderRadius: 8,
    overflow: "hidden",
  },
  loginGradient: {
    width: "100%",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
  },
  loginText: {
    color: "white",
    fontWeight: "bold",
    fontSize: 16,
  },
  skipBtnTextOnly: {
    marginTop: 18,
    alignItems: "center",
  },
  skipTextTextOnly: {
    fontSize: 15,
    fontWeight: "500",
    // 不加下划线
  },
});

export default LoginGuideModal;
