import { SafeAreaView, SafeAreaViewProps } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';

export type ThemedSafeAreaViewProps = SafeAreaViewProps & {
  lightColor?: string;
  darkColor?: string;
};

export function ThemedSafeAreaView({ style, lightColor, darkColor, ...otherProps }: ThemedSafeAreaViewProps) {
  const theme = useAppTheme();
  const backgroundColor = theme === 'dark' ? darkColor : lightColor;

  return <SafeAreaView style={[{ backgroundColor }, style]} {...otherProps} />;
} 