/**
 * VIP升级Modal组件
 * 用于引导用户升级VIP，突出无限自定义模板权益
 */
import React from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet, 
  Modal, 
  TouchableWithoutFeedback 
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { IconSymbol } from './ui/IconSymbol';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { router } from 'expo-router';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useAuth } from '@/hooks/useAuth';

interface VipUpgradeModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  onUpgrade?: () => void;
}

export const VipUpgradeModal: React.FC<VipUpgradeModalProps> = ({
  visible,
  onClose,
  title,
  description,
  onUpgrade
}) => {
  const currentTheme = useAppTheme();
  const isDark = currentTheme === 'dark';
  const { t } = useTranslation();
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);
  const { showLoginModal } = useAuth();

  // 处理升级按钮点击
  const handleUpgrade = () => {
    onClose(); // 先关闭modal
    
    if (onUpgrade) {
      onUpgrade();
      return;
    }
    
    // 检查是否已登录
    if (!isAuthenticated) {
      // 未登录，显示登录模态框
      showLoginModal();
      // 可以在这里设置一个标志，表示登录后需要导航到支付页面
      global.pendingVipUpgrade = true;
    } else {
      // 已登录，直接导航到支付页面
      router.push('/payment');
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback onPress={(e) => e.stopPropagation()}>
            <View style={[
              styles.container,
              { backgroundColor: isDark ? '#1E2122' : '#FFFFFF' }
            ]}>
              {/* 关闭按钮 */}
              <TouchableOpacity
                style={styles.closeButton}
                onPress={onClose}
              >
                <IconSymbol 
                  name="xmark" 
                  size={20} 
                  color={isDark ? '#9BA1A6' : '#6B7280'} 
                />
              </TouchableOpacity>

              {/* VIP图标 */}
              <LinearGradient
                colors={['#FFD700', '#FFA500']}
                style={styles.vipIconContainer}
              >
                <IconSymbol name="crown.fill" size={32} color="#FFFFFF" />
              </LinearGradient>

              {/* 标题 */}
              <Text style={[
                styles.title,
                { color: isDark ? '#ECEDEE' : '#1F2937' }
              ]}>
                {title || t('templates.vipLimitTitle')}
              </Text>

              {/* 描述 */}
              <Text style={[
                styles.description,
                { color: isDark ? '#9BA1A6' : '#6B7280' }
              ]}>
                {description || t('templates.vipLimitDesc')}
              </Text>

              {/* VIP权益列表 */}
              <View style={styles.benefitsContainer}>
                <View style={styles.benefitItem}>
                  <IconSymbol 
                    name="doc.text" 
                    size={20} 
                    color={isDark ? '#8B5CF6' : '#6a5ae1'} 
                  />
                  <Text style={[
                    styles.benefitText,
                    { color: isDark ? '#ECEDEE' : '#1F2937' }
                  ]}>
                    {t("payment.benefits.unlimitedTemplates.title" as any)}
                  </Text>
                </View>
                
                <View style={styles.benefitItem}>
                  <IconSymbol 
                    name="cpu" 
                    size={20} 
                    color={isDark ? '#8B5CF6' : '#6a5ae1'} 
                  />
                  <Text style={[
                    styles.benefitText,
                    { color: isDark ? '#ECEDEE' : '#1F2937' }
                  ]}>
                    {t("payment.benefits.advancedModel.title" as any)}
                  </Text>
                </View>
                
                <View style={styles.benefitItem}>
                  <IconSymbol 
                    name="bolt" 
                    size={20} 
                    color={isDark ? '#8B5CF6' : '#6a5ae1'} 
                  />
                  <Text style={[
                    styles.benefitText,
                    { color: isDark ? '#ECEDEE' : '#1F2937' }
                  ]}>
                    {t("payment.benefits.priorityRequest.title" as any)}
                  </Text>
                </View>
                
                <View style={styles.benefitItem}>
                  <IconSymbol 
                    name="cloud.fill" 
                    size={20} 
                    color={isDark ? '#8B5CF6' : '#6a5ae1'} 
                  />
                  <Text style={[
                    styles.benefitText,
                    { color: isDark ? '#ECEDEE' : '#1F2937' }
                  ]}>
                    {t("payment.benefits.cloudSync.title" as any)}
                  </Text>
                </View>
              </View>

              {/* 升级按钮 */}
              <TouchableOpacity
                style={styles.upgradeButton}
                onPress={handleUpgrade}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={['#8B5CF6', '#6a5ae1']}
                  style={styles.upgradeButtonGradient}
                >
                  <IconSymbol name="bolt" size={16} color="#FFFFFF" />
                  <Text style={styles.upgradeButtonText}>
                    {t("settings.upgradeNow" as any)}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>

              {/* 价格提示 */}
              <Text style={[
                styles.priceText,
                { color: isDark ? '#9BA1A6' : '#6B7280' }
              ]}>
                {t("payment.priceHint" as any)}
              </Text>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  container: {
    width: '100%',
    maxWidth: 360,
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  vipIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  benefitsContainer: {
    width: '100%',
    marginBottom: 24,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  benefitText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  upgradeButton: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 12,
  },
  upgradeButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  upgradeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  priceText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default VipUpgradeModal; 