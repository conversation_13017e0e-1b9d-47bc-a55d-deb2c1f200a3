import React from 'react';
import Svg, { Circle, Path } from 'react-native-svg';

interface GoogleIconProps {
  size?: number;
}

export const GoogleIcon = ({ size = 24 }: GoogleIconProps) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24">
      <Path
        d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032 s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2 C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"
        fill="#FFC107"
      />
      <Path
        d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032 s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2 C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"
        fill="#FF3D00"
        clipRule="evenodd"
        clipPath="url(#clip0)"
      />
      <Path
        d="M3.117,7.254l3.258,2.392c0.878-2.625,3.306-4.515,6.17-4.515c1.498,0,2.866,0.549,3.921,1.453 l2.814-2.814C17.503,2.988,15.139,2,12.545,2C8.437,2,4.938,4.114,3.117,7.254z"
        fill="#FF3D00"
      />
      <Path
        d="M12.545,22c2.518,0,4.839-0.847,6.694-2.261l-3.095-2.619c-0.991,0.722-2.211,1.129-3.599,1.129 c-2.783,0-5.139-1.644-6.23-3.967l-3.239,2.493C4.988,19.956,8.465,22,12.545,22z"
        fill="#4CAF50"
      />
      <Path
        d="M21.979,12.212c0-0.625-0.062-1.266-0.154-1.88H12.545v3.821h5.445 c-0.348,1.139-1.076,2.156-2.026,2.915l0.001,0.001l3.094,2.619C21.211,17.629,21.979,15.077,21.979,12.212z"
        fill="#1976D2"
      />
    </Svg>
  );
};

export default GoogleIcon; 