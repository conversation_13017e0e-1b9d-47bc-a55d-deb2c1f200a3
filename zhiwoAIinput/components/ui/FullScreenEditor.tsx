import React, { useRef, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { IconSymbol } from './IconSymbol';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';

interface FullScreenEditorProps {
  visible: boolean;
  value: string;
  onChangeText: (text: string) => void;
  onClose: () => void;
  placeholder?: string;
  title?: string;
  maxLength?: number;
}

const { height: screenHeight } = Dimensions.get('window');

export const FullScreenEditor: React.FC<FullScreenEditorProps> = ({
  visible,
  value,
  onChangeText,
  onClose,
  placeholder,
  title,
  maxLength = 1000,
}) => {
  const theme = useAppTheme();
  const isDark = theme === 'dark';
  const { t } = useTranslation();
  const textInputRef = useRef<TextInput>(null);

  // 当模态框打开时自动聚焦
  useEffect(() => {
    if (visible && textInputRef.current) {
      // 延迟聚焦，确保模态框完全打开
      const timer = setTimeout(() => {
        textInputRef.current?.focus();
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [visible]);

  const handleSave = () => {
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: isDark ? '#151718' : '#FFFFFF' }
        ]}
      >
        <StatusBar style={isDark ? 'light' : 'dark'} />
        
        {/* 头部导航 */}
        <View style={[
          styles.header,
          {
            backgroundColor: isDark ? '#151718' : '#FFFFFF',
            borderBottomColor: isDark ? '#2B2F31' : '#E5E7EB'
          }
        ]}>
          <View style={{ flex: 1 }} />

          <Text style={[
            styles.headerTitle,
            { color: isDark ? '#ECEDEE' : '#111827' }
          ]}>
            {title || t('templates.editPrompt')}
          </Text>

          <View style={{ flex: 1, alignItems: 'flex-end' }}>
            <TouchableOpacity
              style={[
                styles.headerButton,
                styles.collapseButton,
                { backgroundColor: '#f3f4f6' },
                isDark && { backgroundColor: '#2B2F31' }
              ]}
              onPress={handleSave}
            >
              <IconSymbol
                name="arrow.down.right.and.arrow.up.left"
                size={16}
                color={isDark ? '#9BA1A6' : '#6B7280'}
              />
            </TouchableOpacity>
          </View>
        </View>

        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={0}
        >
          <ScrollView
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            <TextInput
              ref={textInputRef}
              style={[
                styles.textInput,
                {
                  color: isDark ? '#ECEDEE' : '#111827',
                  backgroundColor: isDark ? '#1E2122' : '#F9FAFB',
                  borderColor: isDark ? '#2B2F31' : '#E5E7EB',
                }
              ]}
              value={value}
              onChangeText={onChangeText}
              placeholder={placeholder || t('templates.promptPlaceholder', '请输入提示词内容...')}
              placeholderTextColor={isDark ? '#6B7280' : '#9CA3AF'}
              multiline
              textAlignVertical="top"
              maxLength={maxLength}
              autoFocus={false} // 手动控制聚焦
              scrollEnabled={false} // 禁用TextInput内部滚动，使用外层ScrollView
            />
          </ScrollView>

          {/* 字符计数 */}
          <View style={[
            styles.footer,
            { 
              backgroundColor: isDark ? '#151718' : '#FFFFFF',
              borderTopColor: isDark ? '#2B2F31' : '#E5E7EB'
            }
          ]}>
            <Text style={[
              styles.characterCount,
              { color: isDark ? '#9BA1A6' : '#6B7280' }
            ]}>
              {value.length}/{maxLength}
            </Text>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  collapseButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  headerButtonText: {
    fontSize: 16,
    marginLeft: 4,
  },
  collapseButtonText: {
    fontSize: 14,
    marginLeft: 4,
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  textInput: {
    minHeight: screenHeight * 0.7, // 占屏幕高度的70%
    fontSize: 16,
    lineHeight: 24,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    textAlignVertical: 'top',
  },
  footer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  characterCount: {
    fontSize: 14,
    textAlign: 'right',
  },
});
