/**
 * VIP升级引导卡片组件
 * 用于在历史记录列表底部引导用户升级VIP
 */
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { IconSymbol } from './IconSymbol';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { router } from 'expo-router';

interface VipUpgradeCardProps {
  hiddenRecordsCount: number; // 被隐藏的记录数量
  onUpgrade?: () => void; // 升级回调
}

export default function VipUpgradeCard({ hiddenRecordsCount, onUpgrade }: VipUpgradeCardProps) {
  const currentTheme = useAppTheme();
  const isDark = currentTheme === 'dark';
  const { t } = useTranslation();

  // 处理升级按钮点击
  const handleUpgrade = () => {
    if (onUpgrade) {
      onUpgrade();
    } else {
      // 导航到支付页面
      router.push('/payment');
    }
  };

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: isDark ? '#1E2122' : '#FFFFFF',
        borderColor: isDark ? '#2B2F31' : '#E5E7EB',
      }
    ]}>
      {/* 图标和主要信息 */}
      <View style={styles.header}>
        <View style={[
          styles.iconContainer,
          { backgroundColor: isDark ? 'rgba(139, 92, 246, 0.15)' : 'rgba(106, 90, 225, 0.1)' }
        ]}>
          <IconSymbol 
            name="clock.fill" 
            size={20} 
            color={isDark ? '#8B5CF6' : '#6a5ae1'} 
          />
        </View>
        
        <View style={styles.contentContainer}>
          <Text style={[
            styles.title,
            { color: isDark ? '#ECEDEE' : '#1F2937' }
          ]}>
            {t('history.vipUpgrade.title')}
          </Text>
          <Text style={[
            styles.subtitle,
            { color: isDark ? '#9BA1A6' : '#6B7280' }
          ]}>
            {t('history.vipUpgrade.subtitle', { count: hiddenRecordsCount })}
          </Text>
        </View>
      </View>

      {/* 升级按钮 */}
      <TouchableOpacity
        style={[
          styles.upgradeButton,
          { backgroundColor: isDark ? '#7C3AED' : '#6a5ae1' }
        ]}
        onPress={handleUpgrade}
        activeOpacity={0.8}
      >
        <IconSymbol 
          name="arrow.up.circle.fill" 
          size={16} 
          color="white" 
        />
        <Text style={styles.upgradeButtonText}>
          {t('history.vipUpgrade.upgradeButton')}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  upgradeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  upgradeButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
}); 