import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing, ViewStyle } from 'react-native';
import { ThemedText as Text } from '@/components/ThemedText';

interface LoadingAnimationProps {
  color?: string;
  size?: number;
  text?: string;
  textStyle?: any;
  isDark?: boolean;
}

/**
 * 高级加载动画组件
 * 显示一个环形脉冲波纹效果，比简单的点更有视觉吸引力
 */
const LoadingAnimation: React.FC<LoadingAnimationProps> = ({
  color = '#6a5ae1',
  size = 80,
  text,
  textStyle,
  isDark = false
}) => {
  // 创建多个动画值，用于不同大小的圆环
  const circle1 = useRef(new Animated.Value(0)).current;
  const circle2 = useRef(new Animated.Value(0)).current;
  const circle3 = useRef(new Animated.Value(0)).current;
  
  // 创建旋转动画值
  const rotation = useRef(new Animated.Value(0)).current;
  
  // 启动动画
  useEffect(() => {
    // 创建圆环波纹动画
    const createPulseAnimation = (animValue: Animated.Value, delay: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.timing(animValue, {
            toValue: 1,
            duration: 1500,
            delay,
            useNativeDriver: true,
            easing: Easing.bezier(0.25, 0.1, 0.25, 1),
          }),
          Animated.timing(animValue, {
            toValue: 0,
            duration: 0,
            useNativeDriver: true,
          }),
        ])
      );
    };
    
    // 创建旋转动画
    const rotationAnimation = Animated.loop(
      Animated.timing(rotation, {
        toValue: 1,
        duration: 4000,
        useNativeDriver: true,
        easing: Easing.linear,
      })
    );
    
    // 启动所有动画
    const circle1Anim = createPulseAnimation(circle1, 0);
    const circle2Anim = createPulseAnimation(circle2, 300);
    const circle3Anim = createPulseAnimation(circle3, 600);
    
    circle1Anim.start();
    circle2Anim.start();
    circle3Anim.start();
    rotationAnimation.start();
    
    // 清理动画
    return () => {
      circle1Anim.stop();
      circle2Anim.stop();
      circle3Anim.stop();
      rotationAnimation.stop();
    };
  }, [circle1, circle2, circle3, rotation]);
  
  // 创建旋转样式
  const rotateStyle = {
    transform: [
      {
        rotate: rotation.interpolate({
          inputRange: [0, 1],
          outputRange: ['0deg', '360deg'],
        }),
      },
    ],
  };
  
  // 计算样式
  const getCircleStyle = (animValue: Animated.Value, circleNum: number) => {
    const baseSize = size * 0.8;
    const scaleFactor = 0.4 + (circleNum * 0.3); // 0.4, 0.7, 1.0
    
    return {
      position: 'absolute' as const,
      width: baseSize,
      height: baseSize,
      borderRadius: baseSize / 2,
      borderWidth: 2,
      borderColor: color,
      opacity: animValue.interpolate({
        inputRange: [0, 1],
        outputRange: [0.8, 0],
      }),
      transform: [
        {
          scale: animValue.interpolate({
            inputRange: [0, 1],
            outputRange: [scaleFactor, scaleFactor + 0.5],
          }),
        },
      ],
    };
  };
  
  return (
    <View style={[styles.container, { width: size, height: size }]}>
      {/* 所有内容都放在一个容器内，确保对齐 */}
      <View style={styles.centeredContainer}>
        {/* 波纹圆环动画 */}
        <View style={styles.circlesWrapper}>
          <Animated.View style={rotateStyle}>
            <Animated.View style={[styles.circle, getCircleStyle(circle1, 1)]} />
            <Animated.View style={[styles.circle, getCircleStyle(circle2, 2)]} />
            <Animated.View style={[styles.circle, getCircleStyle(circle3, 3)]} />
          </Animated.View>
        </View>
        
        {/* 中心带有渐变色的圆形 */}
        <View style={[
          styles.centerCircle, 
          { 
            width: size * 0.4, 
            height: size * 0.4, 
            borderRadius: size * 0.2,
            backgroundColor: color 
          }
        ]}>
          <View style={[
            styles.innerCircle,
            {
              width: size * 0.25,
              height: size * 0.25,
              borderRadius: size * 0.125,
              backgroundColor: isDark ? '#1E2122' : 'white'
            }
          ]} />
        </View>
      </View>
      
      {/* 文本显示 */}
      {text && (
        <Text style={[
          styles.text, 
          { color: isDark ? '#9BA1A6' : '#6B7280' },
          textStyle
        ]}>
          {text}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  centeredContainer: {
    position: 'relative',
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  circlesWrapper: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  circle: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    // 使用transform确保圆环位于中心
    transform: [{ translateX: '-50%' }, { translateY: '-50%' }]
  },
  centerCircle: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#6a5ae1',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  innerCircle: {
    position: 'absolute',
  },
  text: {
    marginTop: 90,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  }
});

export default LoadingAnimation; 