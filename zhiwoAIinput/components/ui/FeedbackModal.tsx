import React from 'react';
import {
  View,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Linking,
  TouchableWithoutFeedback,
  Pressable,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useAppTheme } from '@/hooks/useAppTheme';
import { IconSymbol } from './IconSymbol';
import * as Application from 'expo-application';
import { ThemedText } from '@/components/ThemedText';
import type { TranslationResources } from '@/utils/i18n';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { LinearGradient } from 'expo-linear-gradient';

interface FeedbackModalProps {
  visible: boolean;
  onClose: () => void;
  onRate: () => void;
}

export const FeedbackModal: React.FC<FeedbackModalProps> = ({
  visible,
  onClose,
  onRate,
}) => {
  const { t } = useTranslation();
  const currentTheme = useAppTheme();
  const isDark = currentTheme === 'dark';
  const { user } = useSelector((state: RootState) => state.auth);

  const handleFeedback = async () => {
    const appVersion = Application.nativeApplicationVersion || '1.0.0';
    const platform = Platform.OS;
    const systemVersion = Platform.Version || 'unknown';
    const subject = encodeURIComponent(t('settings.feedbackEmailSubject', { platform, version: appVersion, defaultValue: '知我AI输入法反馈' }));
    const body = encodeURIComponent([
      t('settings.feedbackEmailBodyLine6', { defaultValue: '\n请在此描述您遇到的问题或建议：\n\n' }),
      t('settings.feedbackEmailBodyLine1', { defaultValue: '应用名称: 知我AI输入法' }),
      t('settings.feedbackEmailBodyLine2', { version: appVersion, defaultValue: `应用版本: ${appVersion}` }),
      t('settings.feedbackEmailBodyLine3', { platform, defaultValue: `系统类型: ${platform}` }),
      t('settings.feedbackEmailBodyLine4', { systemVersion, defaultValue: `系统版本: ${systemVersion}` }),
      t('settings.feedbackEmailBodyLine5', { userId: user?.id || t('common.notLoggedIn', { defaultValue: '未登录' }), defaultValue: `用户ID: ${user?.id || '未登录'}` })
    ].join('\n'));
    const mailtoUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    try {
      await Linking.openURL(mailtoUrl);
      onClose();
    } catch (error) {
      console.error('打开邮件客户端失败:', error);
    }
  };

  const renderText = (key: string, style: any) => {
    return (
      <ThemedText style={style}>
        {t(`settings.feedback.${key}` as any)}
      </ThemedText>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <Pressable 
        style={styles.modalOverlay}
        onPress={onClose}
      >
        <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
          <View style={[
            styles.modalContent,
            { 
              backgroundColor: isDark ? '#1E2122' : '#FFFFFF',
              shadowColor: isDark ? '#000000' : '#000000',
            }
          ]}>
            {/* 关闭按钮 */}
            <TouchableOpacity
              style={[
                styles.closeButton,
                { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }
              ]}
              onPress={onClose}
            >
              <IconSymbol 
                name="xmark" 
                size={16} 
                color={isDark ? '#9BA1A6' : '#6B7280'} 
              />
            </TouchableOpacity>

            {/* 标题 */}
            {renderText('modalTitle', styles.modalTitle)}
            {renderText('modalDescription', styles.modalDescription)}

            {/* 评分按钮 */}
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => {
                onRate();
                onClose();
              }}
            >
              <LinearGradient
                colors={isDark ? ['#4A5568', '#2D3748'] : ['#F7FAFC', '#EDF2F7']}
                style={[styles.buttonGradient, styles.rateButtonGradient]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <View style={[styles.iconContainer, { backgroundColor: isDark ? '#451A03' : '#FEF3C7' }]}>
                  <IconSymbol name="star.fill" size={24} color={isDark ? '#FDE047' : '#EABE41'} />
                </View>
                <View style={styles.buttonTextContainer}>
                  {renderText('rateButton', styles.buttonTitle)}
                  {renderText('rateDescription', styles.buttonDescription)}
                </View>
              </LinearGradient>
            </TouchableOpacity>

            {/* 反馈按钮 */}
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleFeedback}
            >
              <LinearGradient
                colors={isDark ? ['#4A5568', '#2D3748'] : ['#F7FAFC', '#EDF2F7']}
                style={[styles.buttonGradient, styles.feedbackButtonGradient]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <View style={[styles.iconContainer, { backgroundColor: isDark ? '#082F49' : '#E0F2FE' }]}>
                  <IconSymbol name="envelope.fill" size={24} color={isDark ? '#38BDF8' : '#0EA5E9'} />
                </View>
                <View style={styles.buttonTextContainer}>
                  {renderText('feedbackButton', styles.buttonTitle)}
                  {renderText('feedbackDescription', styles.buttonDescription)}
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </TouchableWithoutFeedback>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 24,
    padding: 24,
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 24,
    height: 24,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 16,
    lineHeight: 28,
  },
  modalDescription: {
    fontSize: 16,
    marginBottom: 32,
    textAlign: 'center',
    opacity: 0.8,
    paddingHorizontal: 16,
  },
  actionButton: {
    width: '100%',
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  buttonGradient: {
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  rateButtonGradient: {
    borderWidth: Platform.OS === 'ios' ? 0.5 : 0,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  feedbackButtonGradient: {
    borderWidth: Platform.OS === 'ios' ? 0.5 : 0,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  buttonTextContainer: {
    flex: 1,
  },
  buttonTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  buttonDescription: {
    fontSize: 14,
    opacity: 0.8,
  },
}); 