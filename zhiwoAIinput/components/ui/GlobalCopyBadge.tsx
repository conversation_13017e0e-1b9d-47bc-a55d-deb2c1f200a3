import React, { useEffect, useState } from 'react';
import { Dimensions, StyleSheet } from 'react-native';
import { registerBadgeShowFunction } from '@/utils/toastConfig';
import CopyBadge from './CopyBadge';
import { log } from '@/services/logService';

/**
 * 全局复制提示组件
 * 可以在应用的任何位置显示，不受限于父组件容器
 * 通过注册到toastConfig中使用，确保全局统一的复制提示体验
 */
const GlobalCopyBadge = () => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    // 注册全局Badge显示函数
    log('GlobalCopyBadge - 注册全局Badge显示函数');
    registerBadgeShowFunction(setVisible);
    
    // 组件卸载时清理
    return () => {
      log('GlobalCopyBadge - 清理全局Badge显示函数');
      registerBadgeShowFunction(() => {});
    };
  }, []);

  // 通过这种方式确保组件无论嵌套在何处都能正确显示
  return (
    <CopyBadge
      visible={visible}
      onHide={() => setVisible(false)}
    />
  );
};

export default GlobalCopyBadge; 