// This file is a fallback for using MaterialIcons on Android and web.

import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { SymbolWeight } from 'expo-symbols';
import React from 'react';
import { OpaqueColorValue, StyleProp, TextStyle } from 'react-native';

// Add your SFSymbol to MaterialIcons mappings here.
const MAPPING = {
  // See MaterialIcons here: https://icons.expo.fyi
  // See SF Symbols in the SF Symbols app on Mac.
  'house.fill': 'home',
  'paperplane.fill': 'send',
  'chevron.left.forwardslash.chevron.right': 'code',
  'chevron.right': 'chevron-right',
  // 知我AI输入法应用所需图标
  'mic.fill': 'mic',
  'clock.fill': 'history',
  'gear': 'settings',
  'paintpalette': 'palette',
  'textformat': 'text-format',
  'globe': 'language',
  'cpu': 'memory',
  'star': 'star',
  'doc.text': 'content-copy',
  'lock.shield': 'security',
  'info.circle': 'info',
  'lock.fill': 'lock',
  'doc.on.doc': 'content-copy',
  'square.and.arrow.up': 'share',
  'pause.fill': 'pause',
  'house': 'home',
  'ellipsis': 'more-horiz',
  'trash': 'delete',
  'chevron.left': 'chevron-left',
  'chevron.down': 'keyboard-arrow-down',
  'xmark': 'close',
  'bolt': 'bolt',
  'checkmark': 'check',
} as Partial<
  Record<
    import('expo-symbols').SymbolViewProps['name'],
    React.ComponentProps<typeof MaterialIcons>['name']
  >
>;

export type IconSymbolName = keyof typeof MAPPING;

/**
 * An icon component that uses native SFSymbols on iOS, and MaterialIcons on Android and web. This ensures a consistent look across platforms, and optimal resource usage.
 *
 * Icon `name`s are based on SFSymbols and require manual mapping to MaterialIcons.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<TextStyle>;
  weight?: SymbolWeight;
}) {
  const iconName = MAPPING[name];

  if (!iconName) {
    console.warn(`IconSymbol: 未找到图标映射 "${name}"，使用默认图标`);
    // 使用一个通用的默认图标而不是error
    return <MaterialIcons color={color} size={size} name="help-outline" style={style} />;
  }

  return <MaterialIcons color={color} size={size} name={iconName} style={style} />;
}
