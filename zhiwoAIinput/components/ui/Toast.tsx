import React, { useEffect, useRef } from 'react';
import { 
  Animated, 
  StyleSheet, 
  Text, 
  Platform,
  Dimensions
} from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';

const { width } = Dimensions.get('window');

interface ToastProps {
  visible: boolean;
  message: string;
  duration?: number;
  onHide?: () => void;
}

/**
 * 简单的Toast提示组件
 */
const Toast: React.FC<ToastProps> = ({ 
  visible, 
  message, 
  duration = 2000, 
  onHide 
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const visibleRef = useRef(visible);
  const theme = useAppTheme();
  const isDark = theme === 'dark';

  useEffect(() => {
    visibleRef.current = visible;
    if (visible) {
      // 淡入动画
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();

      // 设置定时器，在指定时间后淡出
      const timer = setTimeout(() => {
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start(() => {
          if (onHide) {
            onHide();
          }
        });
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [visible, duration, fadeAnim, onHide]);

  if (!visible && !visibleRef.current) {
    return null;
  }

  return (
    <Animated.View 
      style={[
        styles.container, 
        { 
          opacity: fadeAnim,
          backgroundColor: isDark ? '#333' : '#333',
        }
      ]}
    >
      <Text style={styles.text}>{message}</Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 100,
    left: width / 2 - 100,
    width: 200,
    padding: 10,
    borderRadius: 8,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 3,
      }
    }),
  },
  text: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
  },
});

export default Toast;