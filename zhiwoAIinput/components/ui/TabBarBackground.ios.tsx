import React from 'react';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { BlurView } from 'expo-blur';
import { StyleSheet, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';

export default function BlurTabBarBackground() {
  const theme = useAppTheme();
  
  // 在深色模式下使用较暗的模糊效果，调整模糊强度
  const tint = theme === 'dark' ? 'dark' : 'light';
  
  return (
    <>
      {/* 根据主题添加半透明背景层 */}
      <View 
        style={[
          StyleSheet.absoluteFill, 
          { 
            backgroundColor: theme === 'dark' ? '#151718' : '#ffffff', 
            opacity: theme === 'dark' ? 0.8 : 0.95,
            borderTopWidth: 1,
            borderTopColor: theme === 'dark' ? '#2B2F31' : '#f0f0f0',
          }
        ]}
      />
      <BlurView
        // 根据主题使用适合的模糊效果
        tint={tint}
        intensity={theme === 'dark' ? 40 : 85}
        style={StyleSheet.absoluteFill}
      />
    </>
  );
}

export function useBottomTabOverflow() {
  const tabHeight = useBottomTabBarHeight();
  const { bottom } = useSafeAreaInsets();
  return tabHeight - bottom;
}
