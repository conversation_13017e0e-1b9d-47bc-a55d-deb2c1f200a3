import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Easing,
  Dimensions,
  LayoutChangeEvent,
} from "react-native";
import { IconSymbol } from "./IconSymbol";
import { useAppTheme } from "@/hooks/useAppTheme";
import { useTranslation } from "react-i18next";
import { log } from "@/services/logService";

interface CopyBadgeProps {
  visible: boolean;
  onHide?: () => void;
  duration?: number;
}

/**
 * 轻量级复制成功提示徽章
 * 不抢眼，用于临时提示用户已复制内容到剪贴板
 */
const CopyBadge: React.FC<CopyBadgeProps> = ({
  visible,
  onHide,
  duration = 2000,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const theme = useAppTheme();
  const isDark = theme === "dark";
  const [badgeWidth, setBadgeWidth] = useState(0);
  const { t } = useTranslation();

  useEffect(() => {
    if (visible) {
      log("CopyBadge - 显示徽章");
      // 淡入动画
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
        easing: Easing.out(Easing.ease),
      }).start();

      // 设置定时器自动隐藏
      const timer = setTimeout(() => {
        log("CopyBadge - 准备隐藏徽章");
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
          easing: Easing.in(Easing.ease),
        }).start(() => {
          if (onHide) {
            onHide();
          }
          log("CopyBadge - 徽章已隐藏");
        });
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [visible, duration, fadeAnim, onHide]);

  if (!visible) {
    return null;
  }

  log("CopyBadge - 渲染徽章组件", { visible, theme });

  // 获取屏幕尺寸
  const { height } = Dimensions.get("window");
  // 计算底部30%的位置
  const bottomOffset = Math.round(height * 0.3);

  // 处理布局变化，获取badge真实宽度
  const onLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setBadgeWidth(width);
  };

  return (
    <Animated.View
      onLayout={onLayout}
      style={[
        styles.container,
        isDark ? styles.containerDark : styles.containerLight,
        {
          opacity: fadeAnim,
          bottom: bottomOffset,
          left: '50%',
          transform: [{ translateX: -badgeWidth / 2 }], // 使用真实宽度的一半来精确居中
        },
      ]}
    >
      <IconSymbol
        name="checkmark.circle.fill"
        size={16}
        color={isDark ? "#a3a3a3" : "#6b7280"}
      />
      <Text style={[styles.text, isDark ? styles.textDark : styles.textLight]}>
        {t("common.copySuccess")}
      </Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderRadius: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 9999, // 增加zIndex确保显示在最上层
  },
  containerLight: {
    backgroundColor: "rgba(249, 250, 251, 0.98)",
    borderWidth: 1,
    borderColor: "#e5e7eb",
  },
  containerDark: {
    backgroundColor: "rgba(32, 33, 35, 0.98)",
    borderWidth: 1,
    borderColor: "#374151",
  },
  text: {
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 6,
  },
  textLight: {
    color: "#4b5563",
  },
  textDark: {
    color: "#d1d5db",
  },
});

export default CopyBadge;
