import { View, StyleSheet } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';

// This is a shim for web and Android where the tab bar is generally opaque.
export default function TabBarBackground() {
  const theme = useAppTheme();
  
  return (
    <View 
      style={[
        StyleSheet.absoluteFill,
        {
          backgroundColor: theme === 'dark' ? '#151718' : '#ffffff',
          borderTopWidth: 1,
          borderTopColor: theme === 'dark' ? '#2B2F31' : '#f0f0f0',
        },
      ]}
    />
  );
}

export function useBottomTabOverflow() {
  return 0;
}
