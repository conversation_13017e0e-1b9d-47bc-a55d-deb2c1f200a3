import { View, type ViewProps, type StyleProp, type ViewStyle } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
  style?: StyleProp<ViewStyle>;
};

export function ThemedView({ style, lightColor, darkColor, ...otherProps }: ThemedViewProps) {
  const backgroundColor = useThemeColor({ light: lightColor, dark: darkColor }, 'background') as string | undefined;

  return <View style={[backgroundColor ? { backgroundColor } : {}, style]} {...otherProps} />;
}
