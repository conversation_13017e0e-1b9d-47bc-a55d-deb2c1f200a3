import React from 'react';
import { Text as RNText, TextProps as RNTextProps, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { warn } from '@/services/logService';

// 定义可用的翻译键类型
type TranslationKey = 
  | `common.${string}`
  | `home.${string}`
  | `history.${string}`
  | `settings.${string}`
  | `templates.${string}`
  | `errors.${string}`
  | `auth.${string}`;

export interface TextProps extends RNTextProps {
  tKey?: string; // 使用字符串类型以兼容动态键
  tParams?: Record<string, unknown>;
  children?: React.ReactNode;
  defaultValue?: string;
}

export const Text: React.FC<TextProps> = ({
  tKey,
  tParams,
  children,
  style,
  defaultValue = '',
  ...rest
}) => {
  const { t, ready } = useTranslation();
  
  let content: React.ReactNode = children;
  
  if (tKey && ready) {
    try {
      // 使用类型断言确保tKey是合法的翻译键
      const translation = t(tKey, {
        ...(tParams || {}),
        defaultValue: defaultValue as string
      } as any);
      
      if (translation === tKey && defaultValue) {
        content = defaultValue;
      } else {
        content = translation as React.ReactNode;
      }
    } catch (error) {
      warn(`Translation error for key "${tKey}":`, error);
      content = defaultValue || children;
    }
  }
  
  return (
    <RNText style={[styles.text, style]} {...rest}>
      {content}
    </RNText>
  );
};

const styles = StyleSheet.create({
  text: {
    color: '#000',
  },
});

export default Text;
