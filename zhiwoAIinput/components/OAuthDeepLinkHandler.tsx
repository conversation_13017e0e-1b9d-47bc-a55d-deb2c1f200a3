import React, { useEffect } from 'react';
import { Linking, Platform } from 'react-native';
import { supabase } from '@/services/supabaseService';
import { useDispatch } from 'react-redux';
import { checkSession, setUser } from '@/store/slices/authSlice';
import { AppDispatch } from '@/store';
import { router } from 'expo-router';
import Constants from 'expo-constants';
import * as WebBrowser from 'expo-web-browser';
import { useAuthModal } from './AuthProvider';
import { userService } from '@/services/supabaseService';
import { log, error as logError } from '@/services/logService';

/**
 * OAuth深链接处理组件
 * 监听应用的深链接并处理Supabase OAuth回调
 */
export const OAuthDeepLinkHandler: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { hideLoginModal } = useAuthModal();

  useEffect(() => {
    // 确保WebBrowser初始化，必须在组件加载时执行
    WebBrowser.maybeCompleteAuthSession();
    
    // 处理初始URL（应用从深链接启动时）
    const handleInitialURL = async () => {
      try {
        const url = await Linking.getInitialURL();
        if (url) {
          log('初始URL:', url);
          handleDeepLink(url);
        }
      } catch (error) {
        logError('获取初始URL失败:', error);
        // 确保关闭任何挂起的浏览器会话
        WebBrowser.maybeCompleteAuthSession();
      }
    };

    // 处理深链接
    const handleDeepLink = async (url: string) => {
      log('处理深链接:', url);
      
      // 如果检测到VIP升级流程，完全跳过深链接处理，避免干扰VIP升级
      if (global.pendingVipUpgrade) {
        log('OAuthDeepLinkHandler: 检测到待处理的VIP升级，跳过所有深链接处理');
        return;
      }
      
      // 确保关闭任何挂起的会话
      WebBrowser.maybeCompleteAuthSession();
      
      // Supabase OAuth回调参数检测
      if (url.includes('type=recovery') || 
          url.includes('type=signup') || 
          url.includes('type=magiclink') || 
          url.includes('type=invite') || 
          url.includes('#access_token=') || 
          url.includes('refresh_token=')) {
        
        try {
          // 尝试使用URL中的参数设置会话
          if (url.includes('#access_token=') || url.includes('refresh_token=')) {
            // 解析URL中的token参数并手动设置会话
            const accessToken = extractToken(url, 'access_token');
            const refreshToken = extractToken(url, 'refresh_token');
            
            if (accessToken) {
              const { data, error } = await supabase.auth.setSession({
                access_token: accessToken,
                refresh_token: refreshToken || '',
              });
              
              if (error) {
                logError('设置会话错误:', error);
                return;
              }
              
              if (data?.session) {
                log('OAuth登录成功，已设置会话');
                
                // 获取到用户信息后，直接更新 Redux 状态，不使用异步 thunk
                const user = data.session.user;
                log('深链接登录获取到用户信息:', user.id);

                try {
                  // 获取用户资料
                  const profileResult = await userService.getUserProfile(user.id);
                  
                  // 直接使用 setUser 更新用户状态
                  dispatch(setUser({
                    id: user.id,
                    email: user.email || '',
                    profile: profileResult.data,
                    // 从用户信息中提取授权提供方信息
                    providerType: user.app_metadata?.provider || '',
                    // 如果有头像，也设置头像URL
                    avatarUrl: user.user_metadata?.avatar_url || ''
                  }));
                  
                  // 等待状态更新完成后处理导航
                  log('等待状态更新...');
                  
                  // 增加等待时间确保状态更新
                  setTimeout(() => {
                    log('OAuthDeepLinkHandler: 准备关闭登录模态框');
                    
                    // 使用全局方法设置标志，确保在登录成功后不切换标签页
                    global.setPreserveTabAfterLogin(true);
                    
                    // 检查是否有待处理的VIP升级，如果有则跳过关闭登录模态框
                    if (global.pendingVipUpgrade) {
                      log('OAuthDeepLinkHandler: 检测到待处理的VIP升级，跳过关闭登录模态框');
                      return;
                    }
                    
                    hideLoginModal();
                  }, 500);
                } catch (error) {
                  logError('获取用户资料错误:', error);
                }
              }
            }
          } else {
            // 对于其他类型的回调，比如重置密码
            log('处理其他类型的OAuth回调');
            // 尝试刷新会话
            const { error, data } = await supabase.auth.getSession();
            if (!error && data?.session?.user) {
              // 获取到用户信息后，直接更新 Redux 状态
              const user = data.session.user;
              log('其他回调获取到用户信息:', user.id);

              try {
                // 获取用户资料
                const profileResult = await userService.getUserProfile(user.id);
                
                // 直接使用 setUser 更新用户状态
                dispatch(setUser({
                  id: user.id,
                  email: user.email || '',
                  profile: profileResult.data,
                  // 从用户信息中提取授权提供方信息
                  providerType: user.app_metadata?.provider || '',
                  // 如果有头像，也设置头像URL
                  avatarUrl: user.user_metadata?.avatar_url || ''
                }));
                
                // 等待状态更新完成后处理导航
                log('等待状态更新...');
                
                // 增加等待时间确保状态更新
                setTimeout(() => {
                  log('OAuthDeepLinkHandler: 准备关闭登录模态框(其他回调)');
                  
                  // 使用全局方法设置标志，确保在登录成功后不切换标签页
                  global.setPreserveTabAfterLogin(true);
                  
                  // 检查是否有待处理的VIP升级，如果有则跳过关闭登录模态框
                  if (global.pendingVipUpgrade) {
                    log('OAuthDeepLinkHandler: 检测到待处理的VIP升级(其他回调)，跳过关闭登录模态框');
                    return;
                  }
                  
                  hideLoginModal();
                }, 500);
              } catch (profileError) {
                logError('获取用户资料错误:', profileError);
              }
            }
          }
        } catch (error) {
          logError('处理深链接回调错误:', error);
          // 确保关闭任何挂起的浏览器会话
          try {
            WebBrowser.dismissAuthSession();
          } catch (dismissError) {
            log('关闭会话出错:', dismissError);
          }
          WebBrowser.maybeCompleteAuthSession();
        }
      }
    };

    // 从URL中提取指定参数值
    const extractToken = (url: string, tokenName: string): string => {
      // 处理 # 和 & 格式
      const hashMatch = new RegExp(`[#&]${tokenName}=([^&]*)`, 'i').exec(url);
      if (hashMatch && hashMatch[1]) {
        return hashMatch[1];
      }
      
      // 处理 ? 格式
      const queryMatch = new RegExp(`[?]${tokenName}=([^&]*)`, 'i').exec(url);
      if (queryMatch && queryMatch[1]) {
        return queryMatch[1];
      }
      
      return '';
    };

    // 订阅URL事件监听器
    const subscription = Linking.addEventListener('url', ({ url }) => {
      log('收到URL事件:', url);
      handleDeepLink(url);
    });

    // 处理应用启动时的URL
    handleInitialURL();

    // 清理函数
    return () => {
      subscription.remove();
    };
  }, [dispatch, hideLoginModal]);

  // 这个组件不渲染任何UI
  return null;
};

// 获取应用配置的scheme
const getAppScheme = (): string => {
  const scheme = Constants.expoConfig?.scheme;
  return typeof scheme === 'string' ? scheme : 'knowmetype';
};

export default OAuthDeepLinkHandler; 