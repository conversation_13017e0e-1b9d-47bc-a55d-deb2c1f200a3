/**
 * 日志服务测试组件
 * 用于验证日志服务在不同环境下的行为
 */
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { log, info, warn, error, debug, logGroup, logGroupEnd, logTable, getLogConfig } from '../services/logService';
import { debugRevenueCatConfig } from '../debug-revenuecat-config';

export default function LogTestComponent() {
  const testBasicLogs = () => {
    log('这是一个普通日志消息');
    info('这是一个信息日志消息');
    warn('这是一个警告日志消息');
    error('这是一个错误日志消息');
    debug('这是一个调试日志消息（仅开发环境）');
  };

  const testGroupLogs = () => {
    logGroup('测试分组日志');
    log('分组内的第一条消息');
    info('分组内的第二条消息');
    warn('分组内的警告消息');
    logGroupEnd();
  };

  const testTableLog = () => {
    const testData = [
      { id: 1, name: '测试用户1', status: 'active' },
      { id: 2, name: '测试用户2', status: 'inactive' },
      { id: 3, name: '测试用户3', status: 'pending' }
    ];
    logTable(testData);
  };

  const showLogConfig = () => {
    const config = getLogConfig();
    log('当前日志配置:', config);
    
    // 使用 console.log 确保在任何环境下都能看到配置信息
    log('=== 日志配置信息 ===');
    log(`当前环境: ${config.environment}`);
    log(`日志启用状态: ${config.loggingEnabled}`);
    log(`允许的日志级别: ${config.allowedLevels.join(', ')}`);
    log('===================');
  };

  const testErrorScenario = () => {
    try {
      // 模拟一个错误
      throw new Error('这是一个测试错误');
    } catch (err) {
      error('捕获到错误:', err);
      warn('错误处理完成');
    }
  };

  const testRevenueCatConfig = () => {
    log('=== 开始检查 RevenueCat 配置 ===');
    const result = debugRevenueCatConfig();
    log('RevenueCat 配置检查结果:', result);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>日志服务测试</Text>
      <Text style={styles.subtitle}>
        在不同环境下测试日志行为 (请查看控制台输出)
      </Text>

      <TouchableOpacity style={styles.button} onPress={testBasicLogs}>
        <Text style={styles.buttonText}>测试基础日志</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testGroupLogs}>
        <Text style={styles.buttonText}>测试分组日志</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testTableLog}>
        <Text style={styles.buttonText}>测试表格日志</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={showLogConfig}>
        <Text style={styles.buttonText}>显示日志配置</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testErrorScenario}>
        <Text style={styles.buttonText}>测试错误场景</Text>
      </TouchableOpacity>

      <TouchableOpacity style={[styles.button, styles.debugButton]} onPress={testRevenueCatConfig}>
        <Text style={styles.buttonText}>🔍 检查 RevenueCat 配置</Text>
      </TouchableOpacity>

      <View style={styles.infoBox}>
        <Text style={styles.infoTitle}>环境说明:</Text>
        <Text style={styles.infoText}>• development: 显示所有日志</Text>
        <Text style={styles.infoText}>• staging: 只显示 warn 和 error</Text>
        <Text style={styles.infoText}>• production: 不显示任何日志</Text>
        <Text style={styles.infoText}>• RevenueCat 配置检查: 帮助诊断订阅配置问题</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  debugButton: {
    backgroundColor: '#FF6B35',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  infoBox: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
}); 