import { BottomTabBarButtonProps } from '@react-navigation/bottom-tabs';
import { PlatformPressable } from '@react-navigation/elements';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';
import { log } from '@/services/logService';

/**
 * 安全地调用触觉反馈，在web平台上会自动跳过
 */
const safeHaptics = {
  impactAsync: async (style: Haptics.ImpactFeedbackStyle) => {
    if (Platform.OS !== 'web') {
      try {
        await Haptics.impactAsync(style);
      } catch (error) {
        log('触觉反馈失败，但不影响功能', error);
      }
    }
  }
};

export function HapticTab(props: BottomTabBarButtonProps) {
  return (
    <PlatformPressable
      {...props}
      onPressIn={(ev) => {
        // 移除iOS平台限制，让所有平台都有震动效果
        // 添加轻微的触觉反馈，跳过Web平台
        safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        
        props.onPressIn?.(ev);
      }}
    />
  );
}
