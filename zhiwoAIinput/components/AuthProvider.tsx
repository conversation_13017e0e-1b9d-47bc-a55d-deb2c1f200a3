import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { View, ActivityIndicator, AppState, AppStateStatus } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { checkSession } from '@/store/slices/authSlice';
import { RootState, AppDispatch } from '@/store';
import { fetchUserSubscription } from '@/store/slices/subscriptionSlice';
import { setVIPStatus } from '@/store/slices/authSlice';
import { router } from 'expo-router';
import { purchaseService } from '@/services/purchaseService';
import { subscriptionSyncService } from '@/services/subscriptionSyncService';
import { userMigrationService } from '@/services/userMigrationService';
import { log, error as logError, warn } from '@/services/logService';

// 创建一个上下文用于管理登录路由
interface AuthContextType {
  showLoginModal: () => void;
  hideLoginModal: () => void;
  loginModalVisible: boolean;
}

const AuthContext = createContext<AuthContextType>({
  showLoginModal: () => {},
  hideLoginModal: () => {},
  loginModalVisible: false
});

// 提供一个钩子直接访问 AuthContext
export const useAuthModal = () => useContext(AuthContext);

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading, user, isAuthenticated, isVIP } = useSelector((state: RootState) => state.auth);
  // 添加状态追踪登录模态框是否可见
  const [loginModalVisible, setLoginModalVisible] = useState(false);
  // 使用ref来记录是否已经执行过会话检查，避免触发重新渲染
  const initSessionCheckedRef = useRef(false);
  // 保存上一次的登录状态用于比较
  const previousAuthRef = useRef<boolean | null>(null);
  // 跟踪应用状态
  const appState = useRef(AppState.currentState);
  // 最后一次验证时间
  const lastVerificationRef = useRef<number>(Date.now());
  // 验证间隔（毫秒），默认10分钟
  const VERIFICATION_INTERVAL = 10 * 60 * 1000;
  
  // 定期验证会话有效性的函数
  const verifySession = async () => {
    // 如果用户已登录，且距离上次验证超过设定时间，重新验证
    if (isAuthenticated && Date.now() - lastVerificationRef.current > VERIFICATION_INTERVAL) {
      log('AuthProvider: 执行定期会话验证');
      lastVerificationRef.current = Date.now();
      dispatch(checkSession());
    }
  };
  
  // 监听应用状态变化
  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        log('AuthProvider: 应用从后台恢复，验证会话有效性');
        verifySession();
        
        // 应用恢复前台时检查订阅状态
        if (isAuthenticated && user?.id) {
          subscriptionSyncService.performForegroundCheck();
        }
      }
      
      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, [isAuthenticated, user]);
  
  // 初始化 RevenueCat SDK
  useEffect(() => {
    const initPurchaseService = async () => {
      try {
        await purchaseService.initialize();
        log('AuthProvider: RevenueCat SDK 初始化成功');
      } catch (error) {
        logError('AuthProvider: RevenueCat SDK 初始化失败:', error);
      }
    };
    
    initPurchaseService();
  }, []);
  
  // 定期验证会话有效性（每10分钟检查一次）
  useEffect(() => {
    const intervalId = setInterval(() => {
      verifySession();
    }, VERIFICATION_INTERVAL);
    
    return () => clearInterval(intervalId);
  }, [isAuthenticated]);
  
  useEffect(() => {
    // 初始化时检查会话，仅执行一次
    if (!initSessionCheckedRef.current) {
      log('AuthProvider: 首次初始化会话检查');
      dispatch(checkSession());
      initSessionCheckedRef.current = true;
    }
  }, [dispatch]);
  
  // 用户登录状态变化时执行操作
  useEffect(() => {
    const syncUserWithRevenueCat = async () => {
      // 如果用户已登录且有ID
      if (isAuthenticated && user?.id) {
        // 记录当前登录状态
        previousAuthRef.current = true;

        // 处理用户登录时的数据迁移
        try {
          await userMigrationService.handleUserLogin(user.id);
          log('AuthProvider: 用户登录数据迁移完成', user.id);
        } catch (migrationError) {
          warn('AuthProvider: 用户登录数据迁移失败，但应用将继续运行:', migrationError);
        }

        // 用户已登录，更新 RevenueCat 用户标识
        try {
          const result = await purchaseService.identifyUser(user.id);
          if (result) {
            log('AuthProvider: RevenueCat 用户标识成功', user.id);
          } else {
            log('AuthProvider: RevenueCat 未配置，跳过用户标识设置');
          }
        } catch (error) {
          warn('AuthProvider: RevenueCat 用户标识失败，应用将继续运行:', error);
          // 开发环境中，如果RevenueCat配置有问题，不要重试，直接跳过
          // 这样可以避免大量的错误日志和不必要的重试
        }
      }
      // 如果用户未登录，但之前是已登录状态(登出操作)
      else if (!isAuthenticated && previousAuthRef.current === true) {
        // 更新状态引用
        previousAuthRef.current = false;

        // 处理用户退出登录时的状态切换
        try {
          await userMigrationService.handleUserLogout();
          log('AuthProvider: 用户退出登录处理完成');
        } catch (migrationError) {
          warn('AuthProvider: 用户退出登录处理失败:', migrationError);
        }

        // 之前有登录用户，现在登出，需要重置 RevenueCat 用户
        try {
          await purchaseService.resetUser();
          log('AuthProvider: RevenueCat 用户重置成功');
        } catch (error) {
          logError('AuthProvider: RevenueCat 用户重置失败:', error);
        }
      } else if (!isAuthenticated) {
        // 首次加载且用户未登录，记录状态但不执行重置
        previousAuthRef.current = false;
        log('AuthProvider: 用户未登录，无需重置RevenueCat用户');
      }
    };
    
    syncUserWithRevenueCat();
  }, [isAuthenticated, user]);
  
  // 检查和同步用户订阅状态
  useEffect(() => {
    const syncSubscriptionStatus = async () => {
      if (isAuthenticated && user?.id) {
        log('AuthProvider: 启动订阅状态同步服务');
        
        try {
          // 启动定期同步服务
          await subscriptionSyncService.startPeriodicSync();
          
          // 执行启动时的完整同步
          await subscriptionSyncService.performStartupSync();
          
          log('AuthProvider: 订阅状态同步服务已启动');
        } catch (error) {
          logError('AuthProvider: 启动订阅同步服务失败:', error);
          
          // 回退到原有的同步逻辑
          try {
            const customerInfo = await purchaseService.getCustomerInfo();
            const isUserVIP = purchaseService.checkIsVIP(customerInfo);
            dispatch(setVIPStatus(isUserVIP));
            
            await purchaseService.getServerVIPStatus();
            log('AuthProvider: 已使用回退方式同步VIP状态');
          } catch (fallbackError) {
            logError('AuthProvider: 回退同步也失败:', fallbackError);
            dispatch(setVIPStatus(false));
          }
        }
      } else if (!isAuthenticated) {
        // 未登录用户，停止同步服务并设置VIP状态为false
        log('AuthProvider: 用户未登录，停止订阅同步服务');
        subscriptionSyncService.stopPeriodicSync();
        dispatch(setVIPStatus(false));
      }
    };
    
    syncSubscriptionStatus();
  }, [isAuthenticated, user, dispatch]);
  
  // 显示登录页面（通过路由导航）
  const showLoginModal = () => {
    log('AuthProvider: 导航到登录页面');
    setLoginModalVisible(true);
    router.push('/login');
  };
  
  // 隐藏登录页面（通过路由返回）
  const hideLoginModal = () => {
    log('AuthProvider: 从登录页面返回，当前preserveTabAfterLogin =', global.preserveTabAfterLogin);
    log('AuthProvider: 当前pendingVipUpgrade =', global.pendingVipUpgrade);
    setLoginModalVisible(false);
    
    // 如果有待处理的VIP升级，完全跳过任何导航操作，让VIP升级流程自己处理
    if (global.pendingVipUpgrade) {
      log('AuthProvider: 检测到待处理的VIP升级，完全跳过hideLoginModal的所有操作');
      // ⚠️ 不要重置标志！让VIP升级流程自己处理
      return;
    }
    
    // 确保路由回退正常运作
    if (router.canGoBack()) {
      try {
        log('AuthProvider: 执行返回操作');
        
        // 检查是否需要保持标签状态 
        if (global.preserveTabAfterLogin === undefined) {
          // 如果标志未设置，可能需要手动设置它以确保安全
          log('AuthProvider: 标志未设置，手动设置preserveTabAfterLogin = true');
          if (typeof global.setPreserveTabAfterLogin === 'function') {
            global.setPreserveTabAfterLogin(true);
          }
        }
        
        // 只是关闭登录页面，不要重定向到其他页面
        router.back();
      } catch (error) {
        logError('导航返回错误:', error);
        // 不再进行额外导航操作，避免在登录后自动跳转到某个页面
        log('登录页面关闭失败，但不执行额外导航');
      }
    } else {
      // 如果无法返回，不再自动跳转
      log('AuthProvider: 无法返回，但不进行额外导航');
    }
  };
  
  // 上下文值
  const contextValue: AuthContextType = {
    showLoginModal,
    hideLoginModal,
    loginModalVisible
  };
  
  // 不再因为加载状态而显示加载页面，而是直接返回子组件
  // 这样应用可以快速进入主流程，后台会自动完成认证检查
  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider; 