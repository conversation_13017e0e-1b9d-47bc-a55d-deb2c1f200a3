/**
 * 权限引导模态框组件
 * 当用户拒绝麦克风或语音识别权限时，引导用户到设置页面开启权限
 */
import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TouchableWithoutFeedback,
  Alert,
  Platform,
  Linking,
  AppState,
  AppStateStatus,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { IconSymbol } from './ui/IconSymbol';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { Audio } from 'expo-av';
import { ExpoSpeechRecognitionModule } from 'expo-speech-recognition';
import { log, error as logError } from '@/services/logService';

export interface PermissionGuideModalProps {
  visible: boolean;
  onClose: () => void;
  onPermissionGranted?: () => void;
  permissionType?: 'microphone' | 'speech' | 'both';
}

export type PermissionStatus = {
  microphone: boolean;
  speech: boolean;
};

export const PermissionGuideModal: React.FC<PermissionGuideModalProps> = ({
  visible,
  onClose,
  onPermissionGranted,
  permissionType = 'both',
}) => {
  const currentTheme = useAppTheme();
  const isDark = currentTheme === 'dark';
  const { t } = useTranslation();
  
  const [isCheckingPermissions, setIsCheckingPermissions] = useState(false);
  const [appStateListener, setAppStateListener] = useState<any>(null);

  // 检查权限状态
  const checkPermissionStatus = async (): Promise<PermissionStatus> => {
    try {
      log('检查权限状态');
      
      // 检查麦克风权限
      const audioPermission = await Audio.getPermissionsAsync();
      const microphoneGranted = audioPermission.status === 'granted';
      
      // 检查语音识别权限
      let speechGranted = false;
      try {
        const speechPermission = await ExpoSpeechRecognitionModule.getPermissionsAsync();
        speechGranted = speechPermission.granted;
      } catch (error) {
        logError('获取语音识别权限状态失败:', error);
        speechGranted = false;
      }
      
      log('权限状态检查结果:', { microphone: microphoneGranted, speech: speechGranted });
      
      return {
        microphone: microphoneGranted,
        speech: speechGranted,
      };
    } catch (error) {
      logError('检查权限状态失败:', error);
      return {
        microphone: false,
        speech: false,
      };
    }
  };

  // 打开系统设置
  const openSettings = async () => {
    try {
      log('打开系统设置');
      
      if (Platform.OS === 'ios') {
        await Linking.openURL('app-settings:');
      } else {
        await Linking.openURL('app-settings:');
      }
      
      // 监听应用状态变化，当用户返回应用时重新检查权限
      const handleAppStateChange = async (nextAppState: AppStateStatus) => {
        if (nextAppState === 'active') {
          log('应用重新激活，检查权限状态');
          setIsCheckingPermissions(true);
          
          // 延迟检查，确保权限状态已更新
          setTimeout(async () => {
            const status = await checkPermissionStatus();
            
            // 检查是否满足所需权限
            const hasRequiredPermissions = 
              (permissionType === 'microphone' && status.microphone) ||
              (permissionType === 'speech' && status.speech) ||
              (permissionType === 'both' && status.microphone && status.speech);
            
            setIsCheckingPermissions(false);
            
            if (hasRequiredPermissions) {
              log('权限已授予，关闭引导模态框');
              onPermissionGranted?.();
              onClose();
            }
          }, 1000);
        }
      };
      
      const listener = AppState.addEventListener('change', handleAppStateChange);
      setAppStateListener(listener);
      
    } catch (error) {
      logError('打开系统设置失败:', error);
      Alert.alert(
        t('common.error'),
        '无法打开系统设置，请手动前往设置 > 隐私与安全性 > 麦克风，开启应用权限'
      );
    }
  };

  // 清理监听器
  useEffect(() => {
    return () => {
      if (appStateListener) {
        appStateListener.remove();
      }
    };
  }, [appStateListener]);

  // 模态框关闭时清理监听器
  useEffect(() => {
    if (!visible && appStateListener) {
      appStateListener.remove();
      setAppStateListener(null);
    }
  }, [visible]);

  // 获取权限类型的显示文本
  const getPermissionTypeText = () => {
    switch (permissionType) {
      case 'microphone':
        return {
          title: t('common.permissions.microphone.title'),
          description: t('common.permissions.microphone.description'),
          icon: 'mic.fill' as const,
        };
      case 'speech':
        return {
          title: t('common.permissions.speech.title'),
          description: t('common.permissions.speech.description'),
          icon: 'waveform' as const,
        };
      case 'both':
      default:
        return {
          title: t('common.permissions.both.title'),
          description: t('common.permissions.both.description'),
          icon: 'mic.fill' as const,
        };
    }
  };

  const permissionInfo = getPermissionTypeText();

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback onPress={(e) => e.stopPropagation()}>
            <View style={[
              styles.container,
              { backgroundColor: isDark ? '#1E2122' : '#FFFFFF' }
            ]}>
              {/* 关闭按钮 */}
              <TouchableOpacity
                style={styles.closeButton}
                onPress={onClose}
              >
                <IconSymbol 
                  name="xmark" 
                  size={20} 
                  color={isDark ? '#9BA1A6' : '#6B7280'} 
                />
              </TouchableOpacity>

              {/* 权限图标 */}
              <LinearGradient
                colors={['#8364e2', '#6a5ae1']}
                style={styles.permissionIconContainer}
              >
                <IconSymbol 
                  name={permissionInfo.icon} 
                  size={32} 
                  color="#FFFFFF" 
                />
              </LinearGradient>

              {/* 标题 */}
              <Text style={[
                styles.title,
                { color: isDark ? '#ECEDEE' : '#1F2937' }
              ]}>
                {permissionInfo.title}
              </Text>

              {/* 描述 */}
              <Text style={[
                styles.description,
                { color: isDark ? '#9BA1A6' : '#6B7280' }
              ]}>
                {permissionInfo.description}
              </Text>

              {/* 权限说明列表 */}
              <View style={styles.permissionList}>
                {(permissionType === 'microphone' || permissionType === 'both') && (
                  <View style={styles.permissionItem}>
                    <IconSymbol 
                      name="mic" 
                      size={20} 
                      color={isDark ? '#8364e2' : '#6a5ae1'} 
                    />
                    <Text style={[
                      styles.permissionText,
                      { color: isDark ? '#ECEDEE' : '#1F2937' }
                    ]}>
                      {t('common.permissions.microphone.usage')}
                    </Text>
                  </View>
                )}
                
                {(permissionType === 'speech' || permissionType === 'both') && (
                  <View style={styles.permissionItem}>
                    <IconSymbol 
                      name="waveform" 
                      size={20} 
                      color={isDark ? '#8364e2' : '#6a5ae1'} 
                    />
                    <Text style={[
                      styles.permissionText,
                      { color: isDark ? '#ECEDEE' : '#1F2937' }
                    ]}>
                      {t('common.permissions.speech.usage')}
                    </Text>
                  </View>
                )}
              </View>

              {/* 操作按钮 */}
              <TouchableOpacity
                style={styles.settingsButton}
                onPress={openSettings}
                disabled={isCheckingPermissions}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={['#8364e2', '#6a5ae1']}
                  style={styles.settingsButtonGradient}
                >
                  {isCheckingPermissions ? (
                    <Text style={styles.settingsButtonText}>
                      {t('common.permissions.guide.checkingPermissions')}
                    </Text>
                  ) : (
                    <>
                      <IconSymbol name="gear" size={16} color="#FFFFFF" />
                      <Text style={styles.settingsButtonText}>
                        {t('common.permissions.guide.openSettings')}
                      </Text>
                    </>
                  )}
                </LinearGradient>
              </TouchableOpacity>

              {/* 跳过按钮 */}
              <TouchableOpacity
                style={styles.skipButton}
                onPress={onClose}
                disabled={isCheckingPermissions}
              >
                <Text style={[
                  styles.skipButtonText,
                  { color: isDark ? '#9BA1A6' : '#6B7280' }
                ]}>
                  {t('common.permissions.guide.skipForNow')}
                </Text>
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  container: {
    width: '100%',
    maxWidth: 380,
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  permissionIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  permissionList: {
    width: '100%',
    marginBottom: 24,
  },
  permissionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  permissionText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
    flex: 1,
  },
  settingsButton: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 12,
  },
  settingsButtonGradient: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  settingsButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  skipButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default PermissionGuideModal; 