import React, { useEffect } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';

/**
 * 语音波形动画组件
 * 用于在录音过程中显示动态波形效果，模拟原型中的波形动画
 */
const VoiceWave = () => {
  // 创建8个动画值，每个代表一条波形线
  const animations = [...Array(8)].map(() => new Animated.Value(0));
  
  useEffect(() => {
    // 为每个波形线创建循环动画，错开启动时间以产生波浪效果
    const createAnimation = (value: Animated.Value, delay: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.timing(value, {
            toValue: 1,
            duration: 900 + Math.random() * 200, // 添加随机性使波形更自然
            delay,
            useNativeDriver: false,
            easing: Easing.ease,
          }),
          Animated.timing(value, {
            toValue: 0,
            duration: 900 + Math.random() * 200,
            useNativeDriver: false,
            easing: Easing.ease,
          })
        ])
      );
    };
    
    // 启动所有波形动画
    const animationsArray = animations.map((value, index) => 
      createAnimation(value, index * 100)
    );
    
    Animated.parallel(animationsArray).start();
    
    // 清理动画
    return () => {
      animationsArray.forEach(anim => anim.stop());
    };
  }, []);
  
  return (
    <View style={styles.container}>
      {animations.map((anim, index) => (
        <Animated.View
          key={index}
          style={[
            styles.waveLine,
            {
              height: anim.interpolate({
                inputRange: [0, 1],
                outputRange: [3, index % 2 === 0 ? 7 : 9],
              }),
            },
          ]}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 40,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  waveLine: {
    width: 1,
    backgroundColor: '#4285F4',
    borderRadius: 0,
    marginHorizontal: 3,
  },
});

export default VoiceWave; 