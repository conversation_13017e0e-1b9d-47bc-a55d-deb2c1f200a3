import React from 'react';
import { TouchableOpacity, StyleSheet, View, Text } from 'react-native';
import { useTranslation } from 'react-i18next';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LinearGradient } from 'expo-linear-gradient';

interface RecordButtonProps {
  recordingStatus: 'idle' | 'recording' | 'paused' | 'stopped' | 'processing' | 'error';
  onStartRecording: () => void;
  onPauseRecording: () => void;
  onResumeRecording: () => void;
  onStopRecording: () => void;
  disabled?: boolean;
}

/**
 * 录音按钮组件
 * 根据不同的录音状态显示不同的按钮
 */
const RecordButton: React.FC<RecordButtonProps> = ({
  recordingStatus,
  onStartRecording,
  onPauseRecording,
  onResumeRecording,
  onStopRecording,
  disabled = false
}) => {
  const { t } = useTranslation();
  // 根据录音状态渲染不同的按钮
  const renderButton = () => {
    switch (recordingStatus) {
      case 'idle':
        // 初始状态 - 显示开始录音按钮
        return (
          <TouchableOpacity
            style={styles.recordButton}
            onPress={onStartRecording}
            disabled={disabled}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={['#8364e2', '#6a5ae1']}
              style={styles.gradientBackground}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <IconSymbol name="mic.fill" size={30} color="white" />
            </LinearGradient>
          </TouchableOpacity>
        );
        
      case 'recording':
        // 录音中 - 显示暂停按钮和完成按钮
        return (
          <View style={styles.buttonGroup}>
            <TouchableOpacity
              style={[styles.actionButton, styles.pauseButton]}
              onPress={onPauseRecording}
              disabled={disabled}
              activeOpacity={0.8}
            >
              <IconSymbol name="pause.fill" size={24} color="white" />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, styles.stopButton]}
              onPress={onStopRecording}
              disabled={disabled}
              activeOpacity={0.8}
            >
              <Text style={styles.stopText}>{t('home.finish')}</Text>
            </TouchableOpacity>
          </View>
        );
        
      case 'paused':
        // 暂停状态 - 显示继续录音按钮和完成按钮
        return (
          <View style={styles.buttonGroup}>
            <TouchableOpacity
              style={[styles.actionButton, styles.resumeButton]}
              onPress={onResumeRecording}
              disabled={disabled}
              activeOpacity={0.8}
            >
              <IconSymbol name="play.fill" size={24} color="white" />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, styles.stopButton]}
              onPress={onStopRecording}
              disabled={disabled}
              activeOpacity={0.8}
            >
              <Text style={styles.stopText}>{t('home.finish')}</Text>
            </TouchableOpacity>
          </View>
        );
        
      case 'stopped':
      case 'processing':
        // 停止状态或处理中 - 隐藏按钮
        return null;
        
      case 'error':
        // 错误状态 - 显示重试按钮
        return (
          <TouchableOpacity
            style={[styles.recordButton, styles.errorButton]}
            onPress={onStartRecording}
            disabled={disabled}
            activeOpacity={0.8}
          >
            <IconSymbol name="arrow.clockwise" size={30} color="white" />
          </TouchableOpacity>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <View style={styles.container}>
      {renderButton()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  recordButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  gradientBackground: {
    width: '100%',
    height: '100%',
    borderRadius: 35,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  pauseButton: {
    backgroundColor: '#f97316',
  },
  resumeButton: {
    backgroundColor: '#6a5ae1',
  },
  stopButton: {
    backgroundColor: '#10b981',
    width: 100,
    height: 50,
    borderRadius: 25,
  },
  stopText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  errorButton: {
    backgroundColor: '#ef4444',
  },
});

export default RecordButton;