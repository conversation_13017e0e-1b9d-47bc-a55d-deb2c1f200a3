import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { IconSymbol } from "../ui/IconSymbol";
import { useTranslation } from "react-i18next";

/**
 * 功能标签组件
 * 展示应用的核心特性，包括智能润色、场景适配和个性化模板
 */
const FeatureTags = () => {
  const { t } = useTranslation();
  
  return (
    <View style={styles.container}>
      <View style={[styles.featureTag, styles.tagPrimary]}>
        <IconSymbol
          name="wand.and.stars"
          size={10}
          color="#4f46e5"
          style={styles.featureTagIcon}
        />
        <Text style={[styles.featureTagText, { color: "#4f46e5" }]}>
          {t("home.featureTags.smartReworking")}
        </Text>
      </View>
      <View style={[styles.featureTag, styles.tagAccent]}>
        <IconSymbol
          name="gauge.medium"
          size={10}
          color="#ec4899"
          style={styles.featureTagIcon}
        />
        <Text style={[styles.featureTagText, { color: "#ec4899" }]}>
          {t("home.featureTags.sceneAdaptation")}
        </Text>
      </View>
      <View style={[styles.featureTag, styles.tagSecondary]}>
        <IconSymbol
          name="crown.fill"
          size={10}
          color="#8b5cf6"
          style={styles.featureTagIcon}
        />
        <Text style={[styles.featureTagText, { color: "#8b5cf6" }]}>
          {t("home.featureTags.personalizedTemplates")}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    marginTop: 0,
    marginBottom: 5,
    gap: 6,
  },
  featureTag: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 5,
    borderRadius: 16,
    marginHorizontal: 2,
    marginBottom: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    opacity: 0.85,
  },
  featureTagIcon: {
    marginRight: 4,
  },
  featureTagText: {
    fontSize: 10,
    fontWeight: "500",
    letterSpacing: 0.2,
  },
  tagPrimary: {
    backgroundColor: "rgba(79, 70, 229, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(79, 70, 229, 0.2)",
  },
  tagAccent: {
    backgroundColor: "rgba(236, 72, 153, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(236, 72, 153, 0.2)",
  },
  tagSecondary: {
    backgroundColor: "rgba(139, 92, 246, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(139, 92, 246, 0.2)",
  },
});

export default FeatureTags;
