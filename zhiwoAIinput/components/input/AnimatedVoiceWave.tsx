import React, { useEffect, useState, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';

interface AnimatedVoiceWaveProps {
  audioLevel?: number; // 0-1范围的音量级别
  barCount?: number; // 波形柱状条的数量
  barColor?: string; // 波形柱状条的颜色
  barHeight?: number; // 波形最大高度
  barWidth?: number; // 波形柱状条宽度
  barMargin?: number; // 波形柱状条间距
}

/**
 * 高级语音波形动画组件
 * 位于底部按钮上方，提供专业的波形效果
 */
const AnimatedVoiceWave: React.FC<AnimatedVoiceWaveProps> = ({
  audioLevel = 0.5,
  barCount = 30,
  barColor = '#6a5ae1',
  barHeight = 50,
  barWidth = 3,
  barMargin = 1.5
}) => {
  // 创建动画值数组
  const animatedValues = useRef(
    Array.from({ length: barCount }, () => new Animated.Value(0))
  ).current;
  
  // 波形高度随机变化计时器
  const [tickCount, setTickCount] = useState(0);
  
  // 创建随机波形高度数据
  const generateRandomHeights = () => {
    // 根据音量级别调整波形高度
    const minHeight = 0.2;
    const variableHeight = 0.8;
    const amplifier = Math.max(0.3, audioLevel); // 确保即使在静音时也有一些波形
    
    return Array.from(
      { length: barCount }, 
      () => minHeight + Math.random() * variableHeight * amplifier
    );
  };
  
  // 波形高度数据
  const [heights, setHeights] = useState(generateRandomHeights());
  
  useEffect(() => {
    // 设置波形数据更新计时器
    const timer = setInterval(() => {
      setTickCount(prev => prev + 1);
      setHeights(generateRandomHeights());
    }, 300);
    
    return () => clearInterval(timer);
  }, [audioLevel]);
  
  // 当高度数据更新时，应用动画
  useEffect(() => {
    const animations = heights.map((height, index) => {
      return Animated.timing(animatedValues[index], {
        toValue: height,
        duration: 300,
        useNativeDriver: false,
        easing: Easing.inOut(Easing.ease),
      });
    });
    
    Animated.parallel(animations).start();
  }, [heights, tickCount]);
  
  return (
    <View style={styles.container}>
      <View style={styles.waveContainer}>
        {animatedValues.map((value, index) => (
          <Animated.View
            key={index}
            style={[
              styles.bar,
              {
                width: barWidth,
                marginHorizontal: barMargin,
                backgroundColor: barColor,
                height: value.interpolate({
                  inputRange: [0, 1],
                  outputRange: [3, barHeight],
                }),
              },
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  waveContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    width: '80%',
  },
  bar: {
    borderRadius: 3,
  },
});

export default AnimatedVoiceWave; 