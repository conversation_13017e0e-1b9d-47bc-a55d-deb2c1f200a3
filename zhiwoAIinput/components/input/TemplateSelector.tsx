import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import TemplateCard from './TemplateCard';
import { Template } from '@/store/slices/templateSlice';
import { getTemplateDisplayName, getTemplateDisplayDescription } from '@/utils/templateI18nUtils';

interface TemplateSelectorProps {
  templates: Template[];
  selectedTemplateId: string | null;
  onSelectTemplate: (templateId: string) => void;
  hideTitle?: boolean;
  layoutType?: 'default' | 'iconTitle';
}

/**
 * 模板选择器组件
 * 用于选择语音识别后的AI风格模板
 */
const TemplateSelector = ({ 
  templates, 
  selectedTemplateId, 
  onSelectTemplate,
  hideTitle = false,
  layoutType = 'default'
}: TemplateSelectorProps) => {
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      {!hideTitle && <Text style={styles.title}>选择风格模板：</Text>}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}
      >
        {templates.map((template) => {
          // 将Template转换为StyleTemplateFromDB格式以使用多语言工具函数
          const templateForI18n = {
            id: template.id,
            name: template.title,
            description: template.description,
            name_key: template.name_key,
            description_key: template.description_key,
            prompt_text: template.prompt,
            is_system: template.isSystem
          };
          
          // 使用多语言工具函数获取本地化的名称和描述
          const displayName = getTemplateDisplayName(templateForI18n, t);
          const displayDescription = getTemplateDisplayDescription(templateForI18n, t);
          
          return (
            <TemplateCard
              key={template.id}
              template={{
                id: Number(template.id), // 转换为数字ID，兼容TemplateCard组件
                title: displayName,
                description: displayDescription,
                name_key: template.name_key,
                description_key: template.description_key,
                color: template.color || '#6a5ae1',
                borderColor: template.borderColor || '#6a5ae1',
                backgroundColor: template.backgroundColor || '#f5f3ff',
                isDefault: template.isDefault,
                isVipOnly: template.isVipOnly,
                icon: template.icon
              }}
              isActive={selectedTemplateId === template.id}
              onPress={() => onSelectTemplate(template.id)}
              style={styles.templateCard}
              layoutType={layoutType}
            />
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  title: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  scrollView: {
    paddingLeft: 20,
  },
  scrollContent: {
    paddingRight: 20,
    paddingTop: 8,    // 为顶部角标预留空间
    paddingBottom: 8, // 为底部角标预留空间
  },
  templateCard: {
    marginRight: 12,
  },
});

export default TemplateSelector; 