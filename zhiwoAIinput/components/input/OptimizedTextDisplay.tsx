import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity } from 'react-native';
import { IconSymbol } from '@/components/ui/IconSymbol';
import * as Clipboard from 'expo-clipboard';
import { ShareService } from '@/services/shareService';
import Toast from 'react-native-toast-message';
import { showCenteredToast, showCopyBadge } from '@/utils/toastConfig';
import { log, error as logError } from '@/services/logService';

interface OptimizedTextDisplayProps {
  text: string | null;
  loading: boolean;
  templateName?: string;
}

/**
 * AI优化文本显示组件
 * 显示优化后的文本内容，提供复制和分享功能
 */
const OptimizedTextDisplay = ({ text, loading, templateName }: OptimizedTextDisplayProps) => {
  // 复制文本到剪贴板
  const copyToClipboard = async () => {
    if (text) {
      await Clipboard.setStringAsync(text);
      // 使用全局Badge系统
      showCopyBadge();
    }
  };

  // 分享文本
  const shareText = async () => {
    if (text) {
      try {
        await ShareService.shareToWeChat({
          message: text,
          title: templateName ? `${templateName}风格文本` : '优化后的文本'
        });
      } catch (error) {
        logError('分享失败', error);
      }
    }
  };

  return (
    <View style={styles.container}>
      {templateName && (
        <View style={styles.header}>
          <Text style={styles.templateName}>{templateName}</Text>
        </View>
      )}
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6a5ae1" />
          <Text style={styles.loadingText}>AI正在优化文本...</Text>
        </View>
      ) : (
        <>
          <ScrollView 
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {text ? (
              <Text style={styles.text}>{text}</Text>
            ) : (
              <Text style={styles.placeholderText}>
                AI优化后的文本将显示在这里
              </Text>
            )}
          </ScrollView>
          
          {text && (
            <View style={styles.actions}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={copyToClipboard}
              >
                <IconSymbol name="doc.on.doc" size={20} color="#6a5ae1" />
                <Text style={styles.actionText}>复制</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={shareText}
              >
                <IconSymbol name="square.and.arrow.up" size={20} color="#6a5ae1" />
                <Text style={styles.actionText}>分享</Text>
              </TouchableOpacity>
            </View>
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    marginBottom: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  templateName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6a5ae1',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 16,
  },
  text: {
    fontSize: 16,
    lineHeight: 24,
    color: '#333',
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginTop: 20,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 20,
    padding: 6,
  },
  actionText: {
    fontSize: 14,
    color: '#6a5ae1',
    marginLeft: 4,
  },
});

export default OptimizedTextDisplay; 