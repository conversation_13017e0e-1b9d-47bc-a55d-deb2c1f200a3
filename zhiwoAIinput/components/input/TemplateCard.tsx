import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, View } from 'react-native';
import { useAppTheme } from '@/hooks/useAppTheme';
import { useTranslation } from 'react-i18next';
import { getTemplateDisplayName, getTemplateDisplayDescription } from '@/utils/templateI18nUtils';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { log } from '@/services/logService';

/**
 * 模板数据接口
 */
export interface Template {
  id: number | string;
  title: string; // 修正：name改为title
  description: string;
  name_key?: string;
  description_key?: string;
  color: string;
  borderColor: string;
  backgroundColor: string;
  isDefault?: boolean; // 是否为默认模板
  category?: string; // 模板分类
  isVipOnly?: boolean; // 是否为VIP专用模板
  icon?: string; // 模板图标（emoji或图标名）
}

interface TemplateCardProps {
  template: Template;
  isActive: boolean;
  onPress: (template: Template) => void;
  style?: ViewStyle;
  layoutType?: 'default' | 'iconTitle'; // 新增布局类型
}

/**
 * 模板卡片组件
 * 用于展示和选择风格模板
 */
const TemplateCard = ({ template, isActive, onPress, style, layoutType = 'default' }: TemplateCardProps) => {
  // 使用应用主题钩子
  const theme = useAppTheme();
  const isDark = theme === 'dark';
  const { t } = useTranslation();
  
  // 获取多语言显示名称和描述
  const getDisplayName = () => {
    // 如果已经是转换后的显示文本（没有多语言键），直接使用
    if (!template.name_key) {
      return template.title;
    }
    
    // 否则使用多语言工具函数
    const templateForI18n = {
      id: template.id.toString(),
      name: template.title,
      description: template.description,
      name_key: template.name_key,
      description_key: template.description_key,
      prompt_text: '', // TemplateCard不需要prompt
      is_system: true // 假设是系统模板，这个不影响多语言显示
    };
    return getTemplateDisplayName(templateForI18n, t);
  };
  
  const getDisplayDescription = () => {
    // 如果已经是转换后的显示文本（没有多语言键），直接使用
    if (!template.description_key) {
      return template.description;
    }
    
    // 否则使用多语言工具函数
    const templateForI18n = {
      id: template.id.toString(),
      name: template.title,
      description: template.description,
      name_key: template.name_key,
      description_key: template.description_key,
      prompt_text: '', // TemplateCard不需要prompt
      is_system: true // 假设是系统模板，这个不影响多语言显示
    };
    return getTemplateDisplayDescription(templateForI18n, t);
  };

  // 图标+标题布局
  if (layoutType === 'iconTitle') {
    // 检查模板是否为系统模板，根据不同类型设置不同的默认图标
    const isSystemTemplate = template.isDefault || template.category === 'system' || 
                             (template.id && (typeof template.id === 'string' && template.id.includes('-')));
    const iconToUse = template.icon || (isSystemTemplate ? '📝' : '👤');
    log(`[TemplateCard] 模板 ${template.title} 图标调试:`, {
      icon: template.icon,
      iconType: typeof template.icon,
      iconExists: !!template.icon,
      iconToUse,
      isSystemTemplate,
      templateId: template.id,
      allTemplateKeys: Object.keys(template)
    });
    
    return (
      <TouchableOpacity
        style={[
          styles.iconTitleCard,
          {
            borderColor: isActive ? template.color : isDark ? '#424242' : '#e0e0e0',
            backgroundColor: isActive 
              ? template.backgroundColor 
              : isDark ? '#1E2122' : 'white'
          },
          style
        ]}
        onPress={() => onPress(template)}
        activeOpacity={0.7}
      >
        {/* 图标 */}
        <View style={styles.iconContainer}>
          <Text style={styles.iconText}>
            {iconToUse}
          </Text>
        </View>
        
        {/* 标题 */}
        <View style={styles.titleContainer}>
          <Text
            style={[
              styles.iconTitleText,
              { color: template.color }
            ]}
            numberOfLines={2}
          >
            {getDisplayName()}
          </Text>
        </View>

        {/* VIP角标 */}
        {template.isVipOnly && (
          <View style={styles.vipBadge}>
            <Text style={styles.vipBadgeText}>VIP</Text>
          </View>
        )}

        {/* 默认模板角标 */}
        {template.isDefault && (
          <View style={styles.defaultBadge}>
            <Text style={styles.defaultBadgeText}>{t('common.default')}</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  }
  
  // 默认布局（原有的布局）
  return (
    <TouchableOpacity
      style={[
        styles.card,
        {
          borderColor: isActive ? template.color : isDark ? '#424242' : '#e0e0e0',
          backgroundColor: isActive 
            ? template.backgroundColor 
            : isDark ? '#1E2122' : 'white'
        },
        style
      ]}
      onPress={() => onPress(template)}
      activeOpacity={0.7}
    >
      <Text
        style={[
          styles.title,
          { color: template.color }
        ]}
        numberOfLines={1}
      >
        {getDisplayName()}
      </Text>
      <Text 
        style={[
          styles.description,
          isDark && styles.descriptionDark
        ]}
        numberOfLines={2}
      >
        {getDisplayDescription()}
      </Text>
      {template.isVipOnly && (
        <View style={styles.vipBadge}>
          <Text style={styles.vipBadgeText}>VIP</Text>
        </View>
      )}
      {template.isDefault && (
        <View style={styles.defaultBadge}>
          <Text style={styles.defaultBadgeText}>{t('common.default')}</Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    width: 120,
    minHeight: 80,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 12,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    justifyContent: 'center',
  },
  // 图标+标题布局样式
  iconTitleCard: {
    width: 120,
    height: 80,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 8,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    alignItems: 'center',
    justifyContent: 'space-between', // 确保图标和标题分别在顶部和底部
    flexDirection: 'column',
  },
  iconContainer: {
    height: 32, // 固定图标容器高度
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconText: {
    fontSize: 24,
    textAlign: 'center',
  },
  titleContainer: {
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  iconTitleText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: 14,
    flexShrink: 0, // 防止被压缩
  },
  title: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 4,
  },
  description: {
    fontSize: 12,
    color: '#666',
  },
  descriptionDark: {
    color: '#9BA1A6', // 深色模式下的描述文字颜色
  },
  vipBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#F59E0B',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minHeight: 16,
    minWidth: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  vipBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  defaultBadge: {
    position: 'absolute',
    top: -4,
    left: -4,
    backgroundColor: '#10B981',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minHeight: 16,
    minWidth: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  defaultBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
  },
});

export default TemplateCard; 