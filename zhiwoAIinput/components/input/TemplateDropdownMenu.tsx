import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  ScrollView,
  Pressable,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { Template } from '@/store/slices/templateSlice';
import { getTemplateDisplayName } from '@/utils/templateI18nUtils';
import { IconSymbol } from '@/components/ui/IconSymbol';

interface TemplateDropdownMenuProps {
  selectedTemplate: Template | null;
  templates: Template[];
  onSelectTemplate: (template: Template) => void;
  visible: boolean;
  onClose: () => void;
  isDark: boolean;
  buttonPosition: { x: number; y: number; width: number; height: number };
}

/**
 * 模板下拉菜单组件
 * 用于在结果页面快速切换模板
 */
const TemplateDropdownMenu: React.FC<TemplateDropdownMenuProps> = ({
  selectedTemplate,
  templates,
  onSelectTemplate,
  visible,
  onClose,
  isDark,
  buttonPosition,
}) => {
  const { t } = useTranslation();

  // 获取前六个模板
  const topSixTemplates = templates.slice(0, 6);

  const handleSelectTemplate = (template: Template) => {
    onSelectTemplate(template);
    onClose();
  };

  // 检查是否为emoji的辅助函数
  const isEmoji = (str: string): boolean => {
    // 使用正则表达式检测emoji
    const emojiRegex = /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F018}-\u{1F270}]|[\u{238C}-\u{2454}]|[\u{20D0}-\u{20FF}]|[\u{FE0F}]|[\u{200D}]$/u;
    return emojiRegex.test(str) || str.length <= 4; // 大部分emoji长度不超过4
  };

  const renderTemplateIcon = (template: Template) => {
    console.log(`[TemplateDropdownMenu] 渲染模板图标: ${template.title}, icon: "${template.icon}", length: ${template.icon?.length}`);

    if (template.icon) {
      // 如果是emoji，直接显示
      if (isEmoji(template.icon)) {
        console.log(`[TemplateDropdownMenu] 使用emoji图标: ${template.icon}`);
        return (
          <Text style={styles.templateEmoji}>{template.icon}</Text>
        );
      }
      // 如果是图标名称，使用IconSymbol组件
      console.log(`[TemplateDropdownMenu] 使用IconSymbol图标: ${template.icon}`);
      try {
        return (
          <IconSymbol
            name={template.icon as any}
            size={20}
            color={isDark ? '#ECEDEE' : '#333'}
          />
        );
      } catch (error) {
        console.warn(`模板图标渲染失败: ${template.title} (${template.id}), icon: ${template.icon}`, error);
        // 如果图标渲染失败，使用默认图标
        return (
          <Text style={styles.templateEmoji}>
            {template.isSystem ? '📝' : '👤'}
          </Text>
        );
      }
    }
    // 默认图标
    console.log(`[TemplateDropdownMenu] 使用默认图标: ${template.title}`);
    return (
      <Text style={styles.templateEmoji}>
        {template.isSystem ? '📝' : '👤'}
      </Text>
    );
  };

  const getTemplateDisplayNameForMenu = (template: Template) => {
    const templateForI18n = {
      id: template.id,
      name: template.title,
      description: template.description,
      name_key: template.name_key,
      description_key: template.description_key,
      prompt_text: template.prompt,
      is_system: template.isSystem,
    };
    return getTemplateDisplayName(templateForI18n, t);
  };

  if (!visible) return null;

  return (
    <Modal
      transparent
      visible={visible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <Pressable style={styles.overlay} onPress={onClose}>
        <View
          style={[
            styles.dropdown,
            {
              backgroundColor: isDark ? '#2B2F31' : '#FFFFFF',
              borderColor: isDark ? '#404040' : '#E5E5E5',
              top: buttonPosition.y + buttonPosition.height + 5,
              right: 20, // 固定右边距，与按钮对齐
              minWidth: Math.max(buttonPosition.width + 100, 200), // 更宽的菜单，最小200px
              maxWidth: 280, // 设置最大宽度避免过宽
            },
          ]}
        >
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
          >
            {topSixTemplates.map((template) => {
              const isSelected = selectedTemplate?.id === template.id;
              const displayName = getTemplateDisplayNameForMenu(template);
              
              return (
                <TouchableOpacity
                  key={template.id}
                  style={[
                    styles.menuItem,
                    {
                      backgroundColor: isSelected
                        ? isDark ? '#404040' : '#F3F4F6'
                        : 'transparent',
                    },
                  ]}
                  onPress={() => handleSelectTemplate(template)}
                >
                  <View style={styles.menuItemContent}>
                    <View style={styles.iconContainer}>
                      {renderTemplateIcon(template)}
                    </View>
                    <Text
                      style={[
                        styles.menuItemText,
                        {
                          color: isDark ? '#ECEDEE' : '#333',
                          fontWeight: isSelected ? '600' : '400',
                        },
                      ]}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {displayName}
                    </Text>
                    {isSelected && (
                      <IconSymbol
                        name="checkmark"
                        size={16}
                        color={isDark ? '#4A9EFF' : '#007AFF'}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  dropdown: {
    position: 'absolute',
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    maxHeight: 450, // 增加菜单最大高度
  },
  scrollView: {
    maxHeight: 420, // 增加滚动视图最大高度
  },
  menuItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 8,
    marginVertical: 2,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  templateEmoji: {
    fontSize: 20,
    textAlign: 'center',
  },
  menuItemText: {
    flex: 1,
    fontSize: 15,
    lineHeight: 20,
    marginRight: 8, // 给选中图标留出空间
  },
});

export default TemplateDropdownMenu;
