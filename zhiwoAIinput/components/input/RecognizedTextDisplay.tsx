import React from "react";
import { View, Text, StyleSheet, ScrollView } from "react-native";
import AnimatedVoiceWave from "./AnimatedVoiceWave";
import { useTranslation } from "react-i18next";
import { log, warn, error as logError } from "@/services/logService";
import LottieView from "lottie-react-native";
import { useColorScheme } from "../../hooks/useColorScheme";

interface RecognizedTextDisplayProps {
  text: string;
  recordingStatus:
    | "idle"
    | "recording"
    | "paused"
    | "stopped"
    | "processing"
    | "error"
    | "transcribing";
  audioLevel: number;
  isDark?: boolean;
  placeholderText?: string;
  showError?: boolean; // 是否显示错误状态
  isStatusText?: boolean; // 是否为状态文本，强制居中显示
  progressText?: string; // 可选的进度文本，显示在状态文本下方
}

// 在组件定义前添加这个辅助函数来判断文本是否是有意义的内容
const isActualContent = (text: string, t: any): boolean => {
  if (!text || text.trim() === "") {
    return false;
  }

  // 获取多语言状态文本，用于比较
  const statusTexts = [
    t("home.textDisplay.recording"),
    t("home.textDisplay.listening"),
    t("home.textDisplay.transcribing"),
    t("home.textDisplay.processing"),
    t("home.textDisplay.retryTranscribingShort"),
    t("home.textDisplay.retryTranscriptionShort"),
    t("home.textDisplay.transcribingShort"),
    t("home.textDisplay.transcriptionShort"),
    t("home.textDisplay.requestTimeout"),
    t("home.textDisplay.networkError"),
    t("home.networkError.retryFailed"),
    t("home.networkError.audioCorrupted"),
    t("home.networkError.retryFailedWithNetwork"), // 新增重试失败相关文本
  ];

  // 状态更新类文本模式 - 扩展匹配模式
  const statusUpdatePatterns = [
    // 录音状态更新
    /^正在录音中\.{1,3}\s*\d*秒*$/,
    /^正在聆听中\.{1,3}\s*已*录制*\d*秒*$/,
    /^录音中\.{1,3}\s*\d*秒*$/,
    /^聆听中\.{1,3}\s*\d*秒*$/,
    /^Recording\.{1,3}\s*\d*s*$/,
    /^Listening\.{1,3}\s*recorded*\d*s*$/,

    // Whisper模型转写状态
    /^正在使用.*?模型转写\.{1,3}$/,
    /^正在使用.*?模型转录\.{1,3}$/,
    /^正在转写中\.{1,3}$/,
    /^正在转录中\.{1,3}$/,
    /^转写中\.{1,3}$/,
    /^转录中\.{1,3}$/,
    /^使用.*?模型转写中\.{1,3}$/,
    /^使用.*?模型转录中\.{1,3}$/,
    /^正在处理\.{1,3}$/,
    /^Transcribing with.*?model\.{1,3}$/,
    /^Transcribing\.{1,3}$/,
    /^Processing\.{1,3}$/,

    // 重试相关状态 - 新增更多模式
    /^正在重试使用.*?模型转写\.{1,3}$/,
    /^正在重试使用.*?模型转录\.{1,3}$/,
    /^重试转写中\.{1,3}$/,
    /^重试转录中\.{1,3}$/,
    /^正在重试转写\.{1,3}$/,
    /^正在重试转录\.{1,3}$/,
    /^重试失败.*?请.*?重试$/,
    /^重试失败.*?请确保网络畅通.*?$/,
    /^多次重试失败.*?$/,
    /^Retrying transcription with.*?model\.{1,3}$/,
    /^Retrying transcription\.{1,3}$/,
    /^Retry failed.*?try again$/,
    /^Multiple retries failed.*?$/,

    // 其他状态更新模式
    /^请等待\.{1,3}$/,
    /^正在准备\.{1,3}$/,
    /^正在加载\.{1,3}$/,
    /^Waiting\.{1,3}$/,
    /^Loading\.{1,3}$/,
  ];

  // 检查是否匹配任何状态更新模式
  for (const pattern of statusUpdatePatterns) {
    if (pattern.test(text)) {
      log("文本匹配状态更新模式:", text);
      return false;
    }
  }

  // 检查是否与多语言状态文本匹配
  for (const statusText of statusTexts) {
    if (text.includes(statusText.replace(/\.+$/, ""))) {
      log("文本匹配多语言状态文本:", text);
      return false;
    }
  }

  // 简单字符串检查 - 如果包含这些关键字也认为是状态更新
  const statusKeywords = [
    "正在录音",
    "正在聆听",
    "正在转写",
    "正在转录",
    "正在使用",
    "请等待",
    "已录制",
    "录音中",
    "转写中",
    "正在重试",
    "重试转写",
    "重试转录",
    "重试失败",
    "请确保网络",
    "多次重试",
    "Recording",
    "Listening",
    "Transcribing",
    "Processing",
    "Retrying",
    "Waiting",
    "Loading",
    "retry",
    "network",
    "multiple retries",
  ];

  // 如果文本很短且包含状态关键字，可能是状态更新
  if (text.length < 100) {
    // 增加长度限制，因为重试失败提示可能较长
    for (const keyword of statusKeywords) {
      if (text.toLowerCase().includes(keyword.toLowerCase())) {
        log("短文本包含状态关键字，可能是状态更新:", text);
        return false;
      }
    }
  }

  // 错误信息是有意义的内容 - 使用多语言检查
  const errorTexts = [
    t("home.textDisplay.speechRecognitionFailed"),
    t("home.textDisplay.noVoiceContentDetected"),
    t("home.textDisplay.noVoiceContentDetectedRetry"),
    t("home.textDisplay.requestTimeout"),
    t("home.textDisplay.networkError"),
  ];

  for (const errorText of errorTexts) {
    if (text.includes(errorText)) {
      log("文本包含错误/警告信息:", text);
      return true;
    }
  }

  // 向后兼容的中文错误信息检查
  if (text.includes("语音转写失败") || text.includes("没有检测到语音内容")) {
    log("文本包含中文错误/警告信息:", text);
    return true;
  }

  // 简化的错误信息应该作为状态文本显示
  if (
    text === t("home.textDisplay.requestTimeout") ||
    text === t("home.textDisplay.networkError")
  ) {
    log("文本是简化的错误状态信息:", text);
    return false;
  }

  // 通过所有检查，认为是有意义的内容
  log(
    "文本是有意义的内容:",
    text.substring(0, 30) + (text.length > 30 ? "..." : "")
  );
  return true;
};

/**
 * 识别文本显示组件
 * 显示语音识别的实时文本和录音状态
 */
const RecognizedTextDisplay = ({
  text,
  recordingStatus,
  audioLevel,
  isDark = false,
  placeholderText,
  showError = false,
  isStatusText = false,
  progressText,
}: RecognizedTextDisplayProps) => {
  const colorScheme = useColorScheme();
  const { t } = useTranslation();

  // 判断是否正在录音
  const isRecording = recordingStatus === "recording";

  // 获取适合当前状态的占位文本
  const getPlaceholderText = () => {
    if (recordingStatus === "idle") {
      return t("home.textDisplay.speakNow");
    } else if (recordingStatus === "recording") {
      return t("home.textDisplay.listening");
    } else if (recordingStatus === "paused") {
      return t("home.textDisplay.pausedRecording");
    } else if (recordingStatus === "stopped") {
      return t("home.textDisplay.clickTemplate");
    } else {
      return t("home.textDisplay.waiting");
    }
  };

  // 计算应该显示的实际文本，避免显示状态更新类文本
  const displayText = React.useMemo(() => {
    // 如果显示错误，直接返回错误信息
    if (showError) {
      return text;
    }

    if (!text || text.trim() === "") {
      return "";
    }

    // 如果明确指定为状态文本，或者是状态更新类文本，返回空字符串以显示占位符
    if (isStatusText || !isActualContent(text, t)) {
      return "";
    }

    return text;
  }, [text, showError, isStatusText, t]);

  // 计算状态提示文本
  const statusDisplayText = React.useMemo(() => {
    if (showError) {
      return text;
    }

    // 如果明确指定为状态文本，直接显示原文本
    if (isStatusText) {
      return text;
    }

    // 如果是状态更新类文本，也显示原文本
    if (text && !isActualContent(text, t)) {
      return text;
    }

    // 否则使用占位符文本
    return placeholderText || getPlaceholderText();
  }, [text, showError, isStatusText, placeholderText, t]);

  return (
    <View style={[styles.container, isDark ? styles.containerDark : {}, isStatusText && styles.statusContainer]}>
      <ScrollView
        style={[
          styles.scrollView,
          // 当有录音动画时，为动画预留底部空间
          isRecording ? styles.scrollViewWithAnimation : styles.scrollViewFull
        ]}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {displayText ? (
          // 显示实际文本内容（左对齐）
          <Text
            style={[
              styles.text,
              isDark ? styles.textDark : {},
              // 使用多语言检查错误文本
              displayText.includes(
                t("home.textDisplay.speechRecognitionFailed")
              ) || displayText.includes("语音转写失败")
                ? styles.errorText
                : {},
              displayText.includes(
                t("home.textDisplay.noVoiceContentDetected")
              ) || displayText.includes("没有检测到语音内容")
                ? styles.warningText
                : {},
              isStatusText && styles.statusText,
            ]}
          >
            {displayText}
          </Text>
        ) : (
          // 显示状态提示文本（居中）
          <Text
            style={[
              styles.placeholderText,
              isDark ? styles.placeholderTextDark : {},
              showError ? styles.errorText : {},
              // 如果是状态文本或匹配状态更新模式，使用状态文本样式
              isStatusText || (text && !isActualContent(text, t))
                ? [styles.statusText, isDark ? styles.statusTextDark : {}]
                : {},
            ]}
          >
            {statusDisplayText}
          </Text>
        )}

        {/* 进度文本显示（灰色居中） */}
        {progressText && !showError && recordingStatus === "transcribing" && (
          <Text
            style={[styles.progressText, isDark ? styles.progressTextDark : {}]}
          >
            {progressText}
          </Text>
        )}
      </ScrollView>

      {/* 录音动画波形 - 固定在底部，不参与滚动 */}
      {isRecording && (
        <View style={styles.waveContainer}>
          <LottieView
            source={require("../../assets/animations/recording_type.json")}
            style={styles.lottieAnimation}
            autoPlay
            loop
          />
        </View>
      )}
      {recordingStatus === "transcribing" && (
        <View style={styles.waveContainer}>
          <LottieView
            source={require("../../assets/animations/transcribing.json")}
            style={styles.lottieAnimationNew}
            autoPlay
            loop
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
    borderRadius: 16,
    padding: 16,
    position: "relative",
  },
  containerDark: {
    backgroundColor: "#26292B",
  },
  scrollView: {
    flex: 1,
  },
  scrollViewWithAnimation: {
    flex: 1,
    marginBottom: 68, // 为动画预留空间（60px高度 + 8px底部间距）
  },
  scrollViewFull: {
    flex: 1,
    marginBottom: 0,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 16,
  },
  text: {
    fontSize: 16,
    lineHeight: 24,
    color: "#333",
  },
  textDark: {
    color: "#ECEDEE",
  },
  placeholderText: {
    fontSize: 16,
    color: "#999",
    textAlign: "center",
    marginTop: 20,
  },
  placeholderTextDark: {
    color: "#6B7280",
  },
  // 新增：状态文本样式（居中显示，适用于转写中、重试等状态）
  statusText: {
    fontSize: 16,
    color: "#6a5ae1",
    textAlign: "center",
    marginTop: 20,
    fontWeight: "500",
  },
  statusTextDark: {
    color: "#8364e2", // 深色模式下使用稍亮一点的紫色
  },
  // 新增：进度文本样式（灰色小字）
  progressText: {
    fontSize: 12,
    color: "#999",
    textAlign: "center",
    marginTop: 10,
  },
  progressTextDark: {
    color: "#6B7280",
  },
  waveContainer: {
    position: "absolute",
    bottom: 0,
    // width: "100%",
    left: 0,
    right: 0,
    height: 100,
    // justifyContent: "center",
    // alignItems: "center",
    marginTop: 20,
    // backgroundColor: "red",
  },
  lottieAnimation: {
    width: "100%",
    height: "100%",
  },
  lottieAnimationNew: {
    width: "100%",
    height: 120,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
  },
  errorText: {
    color: "#ef4444",
    fontWeight: "500",
    textAlign: "center",
    padding: 16,
  },
  warningText: {
    color: "#f59e0b",
    fontWeight: "500",
  },
  statusContainer: {
    // Add any specific styles for the status container if needed
  },
});

export default RecognizedTextDisplay;
