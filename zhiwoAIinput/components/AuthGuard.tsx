import React, { ComponentType, useEffect, useState } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useAuth } from '@/hooks/useAuth';
import { LoginPrompt } from './LoginPrompt';

interface AuthGuardProps {
  requireAuth?: boolean; // 是否需要登录
  requireVIP?: boolean; // 是否需要VIP
  loginTitle?: string; // 登录提示标题
  loginMessage?: string; // 登录提示消息
  vipTitle?: string; // VIP提示标题
  vipMessage?: string; // VIP提示消息
  redirectToLogin?: boolean; // 是否直接显示登录模态框
  fallback?: React.ReactNode; // 未满足条件时显示的组件
}

/**
 * 创建一个受保护的组件，用于验证用户权限
 * @param WrappedComponent 被包装的组件
 * @param options 配置选项
 * @returns 具有权限验证功能的高阶组件
 */
export const withAuthGuard = <P extends object>(
  WrappedComponent: ComponentType<P>,
  options: AuthGuardProps = {}
) => {
  const {
    requireAuth = false,
    requireVIP = false,
    loginTitle = '需要登录',
    loginMessage = '请先登录以使用此功能',
    vipTitle = 'VIP专属功能',
    vipMessage = '此功能仅限VIP会员使用，请先升级会员',
    redirectToLogin = false,
    fallback = null,
  } = options;

  // 返回一个新的组件
  const GuardedComponent = (props: P) => {
    const { 
      isAuthenticated, 
      isVIP, 
      loginPromptVisible, 
      loginPromptTitle, 
      loginPromptMessage, 
      hideLoginPrompt,
      showLoginModal 
    } = useAuth();
    const [isAuthorized, setIsAuthorized] = useState(false);
    const [isChecking, setIsChecking] = useState(true);

    // 检查权限
    useEffect(() => {
      let authorized = true;

      // 检查是否需要登录
      if (requireAuth && !isAuthenticated) {
        authorized = false;
        if (redirectToLogin) {
          // 不直接调用，而是在LoginPrompt的回调中调用
          // 以避免循环依赖问题
        } else {
          hideLoginPrompt();
        }
      }

      // 检查是否需要VIP
      if (requireVIP && (!isAuthenticated || !isVIP)) {
        authorized = false;
        if (!redirectToLogin) {
          hideLoginPrompt();
        }
      }

      setIsAuthorized(authorized);
      setIsChecking(false);
    }, [isAuthenticated, isVIP, requireAuth, requireVIP]);

    if (isChecking) {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color="#6a5ae1" />
        </View>
      );
    }

    // 如果未授权，显示fallback或者空白页面
    if (!isAuthorized) {
      return (
        <>
          {fallback}
          <LoginPrompt
            visible={loginPromptVisible}
            onClose={hideLoginPrompt}
            title={requireVIP ? vipTitle : loginTitle}
            message={requireVIP ? vipMessage : loginMessage}
            onLogin={showLoginModal}
          />
        </>
      );
    }

    // 授权通过，渲染被包装的组件
    return <WrappedComponent {...props} />;
  };

  // 保持组件名称以便于调试
  GuardedComponent.displayName = `withAuthGuard(${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })`;

  return GuardedComponent;
};

/**
 * 权限守卫组件，用于保护需要特定权限的内容
 */
export const AuthGuard: React.FC<AuthGuardProps & { children: React.ReactNode }> = ({
  children,
  requireAuth = false,
  requireVIP = false,
  loginTitle = '需要登录',
  loginMessage = '请先登录以使用此功能',
  vipTitle = 'VIP专属功能',
  vipMessage = '此功能仅限VIP会员使用，请先升级会员',
  redirectToLogin = false,
  fallback = null,
}) => {
  const { 
    isAuthenticated, 
    isVIP, 
    checkIsLoggedIn, 
    checkIsVIP,
    loginPromptVisible,
    loginPromptTitle,
    loginPromptMessage,
    hideLoginPrompt,
    showLoginModal
  } = useAuth();
  const [isChecking, setIsChecking] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    let authorized = true;
    
    // 检查登录权限
    if (requireAuth && !isAuthenticated) {
      authorized = false;
      if (redirectToLogin) {
        // 直接显示登录模态框处理在LoginPrompt中进行
        checkIsLoggedIn({ 
          showPrompt: true,
          title: loginTitle,
          message: loginMessage
        });
      } else {
        checkIsLoggedIn({ 
          showPrompt: true,
          title: loginTitle,
          message: loginMessage
        });
      }
    }
    
    // 检查VIP权限
    if (requireVIP && (!isAuthenticated || !isVIP)) {
      authorized = false;
      if (requireAuth && !isAuthenticated) {
        // 已经在登录检查中处理过了，不再重复处理
      } else if (redirectToLogin && !isAuthenticated) {
        // 直接显示登录模态框处理在LoginPrompt中进行
        checkIsVIP({ 
          showPrompt: true,
          loginTitle: loginTitle,
          loginMessage: loginMessage,
          vipTitle, 
          vipMessage
        });
      } else {
        checkIsVIP({ 
          showPrompt: true,
          loginTitle: loginTitle,
          loginMessage: loginMessage,
          vipTitle, 
          vipMessage
        });
      }
    }
    
    setIsAuthorized(authorized);
    setIsChecking(false);
  }, [isAuthenticated, isVIP, requireAuth, requireVIP]);

  if (isChecking) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#6a5ae1" />
      </View>
    );
  }

  return (
    <>
      {isAuthorized ? children : fallback}
      <LoginPrompt
        visible={loginPromptVisible}
        onClose={hideLoginPrompt}
        title={loginPromptTitle}
        message={loginPromptMessage}
        onLogin={showLoginModal}
      />
    </>
  );
}; 