import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';

type LanguageCode = 'zh' | 'zh-Hans' | 'zh-Hant' | 'en';

interface LanguageIconProps {
  code: LanguageCode;
  size?: number;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const getLanguageCode = (code: LanguageCode): string => {
  switch (code) {
    case 'zh-Hans':
      return '中';
    case 'zh-Hant':
      return '繁';
    case 'en':
      return 'EN';
    default:
      return code.toUpperCase();
  }
};

const getBackgroundColor = (code: LanguageCode): string => {
  switch (code) {
    case 'zh-Hans':
      return '#1890ff';
    case 'zh-Hant':
      return '#722ed1';
    case 'en':
      return '#13c2c2';
    default:
      return '#8c8c8c';
  }
};

export const LanguageIcon: React.FC<LanguageIconProps> = ({
  code,
  size = 24,
  style,
  textStyle,
}) => {
  const backgroundColor = getBackgroundColor(code);
  const languageCode = getLanguageCode(code);

  return (
    <View
      style={[
        styles.container,
        {
          width: size,
          height: size,
          borderRadius: size / 4,
          backgroundColor,
          justifyContent: 'center',
          alignItems: 'center',
        },
        style,
      ]}
    >
      <Text
        style={[
          styles.text,
          {
            color: 'white',
            fontSize: size * 0.5,
            lineHeight: size,
            fontWeight: 'bold',
          },
          textStyle,
        ]}
      >
        {languageCode}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  text: {
    textAlign: 'center',
    textAlignVertical: 'center',
    includeFontPadding: false,
  },
});

export default LanguageIcon;
