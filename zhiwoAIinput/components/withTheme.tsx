import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/useAppTheme';
import { Colors } from '@/constants/Colors';

/**
 * 高阶组件：为现有组件添加主题支持
 * 自动根据当前主题调整样式
 * 
 * @param Component 要包装的React组件
 * @returns 支持主题的组件
 */
export function withTheme<P>(Component: React.ComponentType<P>): React.FC<P> {
  return function ThemedComponent(props: P) {
    const theme = useAppTheme();
    
    return (
      <Component 
        {...props} 
        style={[
          // @ts-ignore - 合并原有样式
          props.style,
          // 组件可能有特定的主题样式
          // @ts-ignore
          props.lightStyle && theme === 'light' ? props.lightStyle : null,
          // @ts-ignore
          props.darkStyle && theme === 'dark' ? props.darkStyle : null,
        ]}
      />
    );
  };
}

// 常用组件的主题版本
export const ThemedView = withTheme(View);
export const ThemedText = withTheme(Text);
export const ThemedScrollView = withTheme(ScrollView);
export const ThemedTouchableOpacity = withTheme(TouchableOpacity);
export const ThemedSafeAreaView = withTheme(SafeAreaView);

/**
 * 创建一组主题化样式
 * 
 * @param lightStyles 浅色主题样式
 * @param darkStyles 深色主题样式
 * @returns 当前主题下的样式
 */
export function createThemedStyles<T extends StyleSheet.NamedStyles<T>>(
  lightStyles: T, 
  darkStyles: T
): T {
  const theme = useAppTheme();
  return theme === 'light' ? lightStyles : darkStyles;
}

/**
 * 根据当前主题获取颜色
 * 
 * @param lightColor 浅色主题下的颜色
 * @param darkColor 深色主题下的颜色
 * @returns 当前主题下的颜色
 */
export function getThemedColor(lightColor: string, darkColor: string): string {
  const theme = useAppTheme();
  return theme === 'light' ? lightColor : darkColor;
}

/**
 * 从颜色常量中获取当前主题的颜色
 * 
 * @param colorName 颜色名称
 * @returns 当前主题下的颜色
 */
export function useThemeColor(colorName: string): any {
  const theme = useAppTheme();
  // 只从颜色常量中获取简单的颜色值，不获取复杂对象
  if (typeof Colors[theme][colorName as keyof typeof Colors.light] === 'string') {
    return Colors[theme][colorName as keyof typeof Colors.light];
  }
  return null;
} 