import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useColorScheme } from 'nativewind';
import { useAppTheme } from '@/hooks/useAppTheme';

interface ThemeWrapperProps {
  children: React.ReactNode;
}

/**
 * 主题包装器组件
 * 
 * 将Redux的主题状态与NativeWind的深色模式同步
 * 这使得我们可以在组件中使用dark:类名来设置深色模式样式
 */
export default function ThemeWrapper({ children }: ThemeWrapperProps) {
  const appTheme = useAppTheme();
  const { colorScheme, setColorScheme } = useColorScheme();
  
  // 当应用主题变化时，更新NativeWind的深色模式
  useEffect(() => {
    if (appTheme !== colorScheme) {
      setColorScheme(appTheme);
    }
  }, [appTheme, colorScheme, setColorScheme]);
  
  return (
    <View style={styles.container}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
}); 