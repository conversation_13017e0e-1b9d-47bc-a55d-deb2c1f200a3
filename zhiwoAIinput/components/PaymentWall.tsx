/**
 * 支付墙组件
 * 显示应用内购买选项，让用户升级到VIP会员
 */
import React, { useState, useEffect, useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Platform,
  Image,
  TouchableWithoutFeedback,
  Linking,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { IconSymbol, IconSymbolName } from "./ui/IconSymbol";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useAppTheme } from "@/hooks/useAppTheme";
import Purchases, {
  PurchasesPackage,
  PurchasesStoreProduct,
} from "react-native-purchases";
import { purchaseService, SUBSCRIPTION_TYPE } from "@/services/purchaseService";
import { useTranslation } from 'react-i18next';
import { log, error as logError, warn } from "@/services/logService";

// 定义SUBSCRIPTION_TYPE类型
type SubscriptionTypeValue =
  (typeof SUBSCRIPTION_TYPE)[keyof typeof SUBSCRIPTION_TYPE];

// 定义试用期信息接口
interface TrialInfo {
  hasFreeTrial: boolean;
  trialDays: number;
}

// 支付墙组件属性
interface PaymentWallProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  initialSubscriptionType?: SubscriptionTypeValue;
  switchPlan?: boolean; // 是否为套餐切换场景
}

// 定义introductoryOffer接口，用于处理试用期信息
interface IntroductoryOffer {
  price: number;
  periodUnit: "DAY" | "WEEK" | "MONTH" | "YEAR";
  periodNumberOfUnits: number;
}

/**
 * 订阅选项组件属性
 */
interface SubscriptionOptionProps {
  title: string;
  price: string;
  description: string;
  duration: string;
  isRecommended?: boolean;
  isSelected: boolean;
  onSelect: () => void;
  isDark: boolean;
}

/**
 * VIP权益项组件属性
 */
interface BenefitItemProps {
  icon: IconSymbolName;
  title: string;
  description: string;
  isDark: boolean;
}

/**
 * 支付墙组件
 * 展示VIP订阅选项和福利
 */
export const PaymentWall: React.FC<PaymentWallProps> = ({
  visible,
  onClose,
  onSuccess,
  initialSubscriptionType,
  switchPlan = false,
}) => {
  const isDark = useAppTheme() === "dark";
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);
  const userProfile = useSelector((state: RootState) => state.auth.user?.profile);
  const currentSubscription = useSelector((state: RootState) => state.subscription.currentSubscription);
  
  // 使用 useTranslation hook 获取 i18n 实例和 t 函数
  const { t, i18n } = useTranslation();
  
  // 监听语言变化，强制组件在语言变化时重新渲染
  const [_, setLang] = useState(i18n.language);
  useEffect(() => {
    const handleLanguageChange = () => {
      setLang(i18n.language);
    };
    
    i18n.on('languageChanged', handleLanguageChange);
    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, [i18n]);

  // 状态
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [selectedSubscription, setSelectedSubscription] =
    useState<SubscriptionTypeValue>(
      initialSubscriptionType || SUBSCRIPTION_TYPE.MONTHLY
    );
  const [monthlySub, setMonthlySub] = useState<PurchasesPackage | null>(null);
  const [yearlySub, setYearlySub] = useState<PurchasesPackage | null>(null);
  const [monthlyPrice, setMonthlyPrice] = useState("--");
  const [yearlyPrice, setYearlyPrice] = useState("--");
  const [hasPricesLoaded, setHasPricesLoaded] = useState(false);

  // 试用期相关状态
  const [monthlyTrialInfo, setMonthlyTrialInfo] = useState<TrialInfo>({
    hasFreeTrial: false,
    trialDays: 0,
  });
  const [yearlyTrialInfo, setYearlyTrialInfo] = useState<TrialInfo>({
    hasFreeTrial: false,
    trialDays: 0,
  });

  // 计算价格和折扣
  const {
    monthlyPriceValue,
    yearlyPriceValue,
    yearlyMonthlyPrice,
    discountPercentage,
  } = useMemo(() => {
    // 如果价格还没有加载，返回默认值
    if (!hasPricesLoaded || monthlyPrice === "--" || yearlyPrice === "--") {
      return {
        monthlyPriceValue: 0,
        yearlyPriceValue: 0,
        yearlyMonthlyPrice: "--",
        discountPercentage: 0,
      };
    }

    // 提取价格数字，移除货币符号
    const monthlyMatch = monthlyPrice.match(/\d+(\.\d+)?/);
    const yearlyMatch = yearlyPrice.match(/\d+(\.\d+)?/);

    // 获取价格数值
    const monthlyValue = monthlyMatch ? parseFloat(monthlyMatch[0]) : 0;
    const yearlyValue = yearlyMatch ? parseFloat(yearlyMatch[0]) : 0;

    // 计算年度订阅的月均价格
    const yearlyMonthly = yearlyValue / 12;

    // 计算折扣百分比
    const discount =
      monthlyValue > 0
        ? Math.round((1 - yearlyValue / (monthlyValue * 12)) * 100)
        : 0;

    return {
      monthlyPriceValue: monthlyValue,
      yearlyPriceValue: yearlyValue,
      yearlyMonthlyPrice: yearlyMonthly.toFixed(2),
      discountPercentage: discount,
    };
  }, [monthlyPrice, yearlyPrice, hasPricesLoaded]);

  // 获取当前用户的套餐类型
  const getCurrentUserPlanType = () => {
    if (!switchPlan) return null;
    
    // 优先从用户资料中获取产品ID
    if (userProfile?.vip_product_id) {
      const productId = userProfile.vip_product_id.toLowerCase();
      
      if (productId.includes('month') || productId.includes('monthly')) {
        return SUBSCRIPTION_TYPE.MONTHLY;
      } 
      
      if (productId.includes('year') || productId.includes('yearly') || productId.includes('annual')) {
        return SUBSCRIPTION_TYPE.YEARLY;
      }
    }
    
    // 其次从Redux获取订阅信息
    if (currentSubscription?.planId) {
      const planId = currentSubscription.planId.toLowerCase();
      
      if (planId.includes('month') || planId.includes('monthly')) {
        return SUBSCRIPTION_TYPE.MONTHLY;
      }
      
      if (planId.includes('year') || planId.includes('yearly') || planId.includes('annual')) {
        return SUBSCRIPTION_TYPE.YEARLY;
      }
    }
    
    return null;
  };

  // 判断套餐是否应该被禁用（当前套餐在切换场景下应该被禁用）
  const isPlanDisabled = (planType: SubscriptionTypeValue) => {
    if (!switchPlan) return false;
    const currentPlan = getCurrentUserPlanType();
    return currentPlan === planType;
  };

  // 加载订阅产品
  useEffect(() => {
    if (visible) {
      log("[PaymentWall] 初始化订阅类型:", initialSubscriptionType);
      log("[PaymentWall] 套餐切换模式:", switchPlan);
      if (switchPlan) {
        log("[PaymentWall] 当前用户套餐类型:", getCurrentUserPlanType());
      }
      loadOfferings();
    }
  }, [visible, initialSubscriptionType]);

  /**
   * 检查套餐是否包含试用期
   * @param pkg 订阅套餐
   * @returns 试用期信息
   */
  const checkTrialPeriod = (pkg: PurchasesPackage): TrialInfo => {
    const defaultResult = { hasFreeTrial: false, trialDays: 0 };

    try {
      // 使用any类型暂时解决TypeScript类型问题
      const product = pkg.product as any;

      // 检查introductoryPrice是否存在
      if (!product.introductoryPrice) {
        return defaultResult;
      }

      const introPeriod = product.introductoryPrice as IntroductoryOffer;

      // 检查是否是免费试用
      if (introPeriod.price === 0) {
        // 获取试用期长度（天数）
        let trialDays = 0;

        switch (introPeriod.periodUnit) {
          case "DAY":
            trialDays = introPeriod.periodNumberOfUnits;
            break;
          case "WEEK":
            trialDays = introPeriod.periodNumberOfUnits * 7;
            break;
          case "MONTH":
            trialDays = introPeriod.periodNumberOfUnits * 30;
            break;
          case "YEAR":
            trialDays = introPeriod.periodNumberOfUnits * 365;
            break;
          default:
            trialDays = 0;
        }

        return {
          hasFreeTrial: true,
          trialDays,
        };
      }
    } catch (error) {
      logError("[PaymentWall] 解析试用期信息出错:", error);
    }

    return defaultResult;
  };

  /**
   * 加载可用订阅产品
   */
  const loadOfferings = async () => {
    try {
      setLoading(true);
      const offerings = await purchaseService.getOfferings();

      if (offerings.current?.availablePackages.length) {
        const packages = offerings.current.availablePackages;

        // 查找月度和年度套餐
        const monthly = packages.find((p) => p.packageType === "MONTHLY");
        const yearly = packages.find((p) => p.packageType === "ANNUAL");

        let pricesLoaded = false;

        if (monthly) {
          setMonthlySub(monthly);
          setMonthlyPrice(monthly.product.priceString);
          pricesLoaded = true;

          // 检查月度套餐是否有免费试用期
          const monthlyTrial = checkTrialPeriod(monthly);
          setMonthlyTrialInfo(monthlyTrial);

          log("[PaymentWall] 月度套餐价格:", monthly.product.priceString);
          log("[PaymentWall] 月度套餐试用期信息:", monthlyTrial);
        }

        if (yearly) {
          setYearlySub(yearly);
          setYearlyPrice(yearly.product.priceString);
          pricesLoaded = true;

          // 检查年度套餐是否有免费试用期
          const yearlyTrial = checkTrialPeriod(yearly);
          setYearlyTrialInfo(yearlyTrial);

          log("[PaymentWall] 年度套餐价格:", yearly.product.priceString);
          log("[PaymentWall] 年度套餐试用期信息:", yearlyTrial);
        }

        // 只有在至少获取到一个套餐价格时才标记为已加载
        setHasPricesLoaded(pricesLoaded);
        
        if (pricesLoaded) {
          log("[PaymentWall] 订阅价格加载成功");
        } else {
          warn("[PaymentWall] 未找到可用的订阅套餐");
        }
      } else {
        warn("[PaymentWall] 未找到可用的订阅套餐");
        setHasPricesLoaded(false);
      }
    } catch (error) {
      logError("[PaymentWall] 获取订阅套餐失败:", error);
      setHasPricesLoaded(false);
      // 不显示错误提示，而是保持显示"--"
      warn("[PaymentWall] 订阅信息获取失败，将显示占位符价格");
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理购买
   */
  const handlePurchase = async () => {
    try {
      if (!isAuthenticated) {
        Alert.alert("请先登录", "请登录后再进行订阅");
        return;
      }

      let packageToPurchase =
        selectedSubscription === SUBSCRIPTION_TYPE.MONTHLY
          ? monthlySub
          : yearlySub;

      if (!packageToPurchase) {
        logError("[PaymentWall] 未找到选中的套餐");
        Alert.alert("订阅错误", "未找到可用的订阅套餐，请稍后重试");
        return;
      }

      setProcessing(true);

      // 执行购买
      const customerInfo = await purchaseService.purchasePackage(
        packageToPurchase
      );

      // 验证购买是否成功
      const isVIP = purchaseService.checkIsVIP(customerInfo);

      if (isVIP) {
        Alert.alert("订阅成功", "您已成功订阅VIP会员，感谢您的支持！");
        if (onSuccess) {
          onSuccess();
        }
        onClose();
      } else {
        Alert.alert("订阅未完成", "订阅处理未完成，请稍后检查您的订阅状态");
      }
    } catch (error: any) {
      // 用户取消购买不需要显示错误
      if (error.code && error.code !== "CANCELLED") {
        logError("[PaymentWall] 购买失败:", error);
        Alert.alert("购买失败", "处理订阅时出现问题，请稍后重试");
      }
    } finally {
      setProcessing(false);
    }
  };

  /**
   * 恢复购买
   */
  const handleRestorePurchases = async () => {
    try {
      setProcessing(true);

      const customerInfo = await purchaseService.restorePurchases();
      const isVIP = purchaseService.checkIsVIP(customerInfo);

      if (isVIP) {
        Alert.alert("恢复成功", "您的VIP会员已恢复");
        if (onSuccess) {
          onSuccess();
        }
        onClose();
      } else {
        Alert.alert("未找到订阅", "未找到可恢复的活跃订阅");
      }
    } catch (error) {
      logError("[PaymentWall] 恢复购买失败:", error);
      Alert.alert("恢复失败", "无法恢复您的购买，请稍后重试");
    } finally {
      setProcessing(false);
    }
  };

  /**
   * 获取当前选择的订阅套餐的试用期信息
   */
  const getCurrentTrialInfo = (): TrialInfo => {
    return selectedSubscription === SUBSCRIPTION_TYPE.MONTHLY
      ? monthlyTrialInfo
      : yearlyTrialInfo;
  };

  /**
   * 处理隐私政策链接
   */
  const handlePrivacyPolicy = () => {
    const privacyUrl = 'https://www.knowme-type.com/en/privacy-policy';
    Linking.openURL(privacyUrl).catch((err) => logError('打开隐私政策失败', err));
  };

  /**
   * 处理服务条款链接
   */
  const handleTermsOfService = () => {
    const termsUrl = 'https://www.knowme-type.com/en/terms-of-service';
    Linking.openURL(termsUrl).catch((err) => logError('打开服务条款失败', err));
  };

  /**
   * 获取按钮文字
   */
  const getButtonText = () => {
    if (loading) return t('payment.loading.loading');
    if (processing) return t('payment.loading.processing');
    if (!hasPricesLoaded) return t('payment.loading.loading');

    // 获取当前选择套餐的试用期信息
    const trialInfo =
      selectedSubscription === SUBSCRIPTION_TYPE.MONTHLY
        ? monthlyTrialInfo
        : yearlyTrialInfo;

    // 如果有免费试用期
    if (trialInfo.hasFreeTrial) {
      return t('payment.startTrial', { days: trialInfo.trialDays });
    }

    // 没有免费试用时，使用更具吸引力的文案
    return selectedSubscription === SUBSCRIPTION_TYPE.MONTHLY
      ? t("payment.subscribeNow", { type: t("payment.monthly") })
      : t("payment.subscribeNow", { type: t("payment.yearly") });
  };

  /**
   * 渲染VIP权益项
   */
  const BenefitItem: React.FC<BenefitItemProps> = ({
    icon,
    title,
    description,
    isDark,
  }) => (
    <View style={styles.benefitItem}>
      <View
        style={[
          styles.benefitIcon,
          { backgroundColor: isDark ? "#2D3748" : "#F3F4F6" },
        ]}
      >
        <IconSymbol
          name={icon}
          size={24}
          color={isDark ? "#6366F1" : "#4F46E5"}
        />
      </View>
      <View style={styles.benefitTextContainer}>
        <Text
          style={[
            styles.benefitTitle,
            { color: isDark ? "#E5E7EB" : "#1F2937" },
          ]}
        >
          {title}
        </Text>
        <Text
          style={[
            styles.benefitDescription,
            { color: isDark ? "#9CA3AF" : "#6B7280" },
          ]}
        >
          {description}
        </Text>
      </View>
    </View>
  );

  /**
   * 订阅选项组件
   */
  const SubscriptionOption: React.FC<SubscriptionOptionProps> = ({
    title,
    price,
    description,
    duration,
    isRecommended,
    isSelected,
    onSelect,
    isDark,
  }) => (
    <TouchableOpacity
      style={[
        styles.subscriptionOption,
        {
          backgroundColor: isDark ? "#1E2122" : "#FFFFFF",
          borderColor: isSelected
            ? isDark
              ? "#6366F1"
              : "#4F46E5"
            : isDark
            ? "#2B2F31"
            : "#E5E7EB",
        },
        isSelected && styles.selectedOption,
      ]}
      onPress={onSelect}
    >
      <View style={styles.optionHeader}>
        <Text
          style={[
            styles.optionTitle,
            { color: isDark ? "#E5E7EB" : "#1F2937" },
          ]}
        >
          {title}
        </Text>

        {isRecommended && (
          <View
            style={[
              styles.recommendedBadge,
              { backgroundColor: isDark ? "#374151" : "#EFF6FF" },
            ]}
          >
            <Text
              style={[
                styles.recommendedText,
                { color: isDark ? "#93C5FD" : "#3B82F6" },
              ]}
            >
              推荐
            </Text>
          </View>
        )}
      </View>

      <View style={styles.priceContainer}>
        <Text style={[styles.price, { color: isDark ? "#E5E7EB" : "#1F2937" }]}>
          {price}
        </Text>
        <Text
          style={[styles.duration, { color: isDark ? "#9CA3AF" : "#6B7280" }]}
        >
          {duration}
        </Text>
      </View>

      <Text
        style={[
          styles.optionDescription,
          { color: isDark ? "#9CA3AF" : "#6B7280" },
        ]}
      >
        {description}
      </Text>

      <View
        style={[
          styles.selectionIndicator,
          {
            borderColor: isSelected
              ? isDark
                ? "#6366F1"
                : "#4F46E5"
              : isDark
              ? "#4B5563"
              : "#D1D5DB",
            backgroundColor: isSelected
              ? isDark
                ? "#6366F1"
                : "#4F46E5"
              : "transparent",
          },
        ]}
      >
        {isSelected && (
          <IconSymbol name="checkmark" size={12} color="#FFFFFF" />
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDark ? "#121212" : "#FFFFFF" },
      ]}
    >
      {/* 标题栏 */}
      <View
        style={[
          styles.header,
          {
            borderBottomColor: isDark ? "#2B2F31" : "#F3F4F6",
            backgroundColor: isDark ? "#121212" : "#FFFFFF",
            zIndex: 10,
          },
        ]}
      >
        <Text style={[styles.title, { color: isDark ? "#E5E7EB" : "#1F2937" }]}>
          {t("payment.title")}
        </Text>

        <TouchableOpacity
          style={styles.closeButton}
          onPress={onClose}
          disabled={processing}
        >
          <IconSymbol
            name="xmark"
            size={20}
            color={isDark ? "#9CA3AF" : "#6B7280"}
          />
        </TouchableOpacity>
      </View>

      {/* 内容区域 */}
      <View style={styles.contentWrapper}>
        <ScrollView
          style={styles.scrollContent}
          contentContainerStyle={[
            styles.contentContainer,
            { backgroundColor: isDark ? "#121212" : "#FFFFFF" },
          ]}
          showsVerticalScrollIndicator={false}
          bounces={true}
        >
          {/* 1. 主引导块 */}
          <View style={styles.mainPromoContainer}>
            <View style={styles.logoContainer}>
              <Image
                source={require("@/assets/images/icon.png")}
                style={styles.appLogo}
                resizeMode="contain"
              />
            </View>

            <Text
              style={[
                styles.promoTitle,
                { color: isDark ? "#E5E7EB" : "#1F2937" },
              ]}
            >
              {t("payment.getVip")}
            </Text>

            <Text
              style={[
                styles.promoSubtitle,
                { color: isDark ? "#9CA3AF" : "#6B7280" },
              ]}
            >
              {t("payment.unlockPremium")}
            </Text>

            <View style={styles.awardBadge}>
              <View style={styles.awardIconLeft}>
                <IconSymbol
                  name="laurel.leading"
                  size={24}
                  color={isDark ? "#9CA3AF" : "#9CA3AF"}
                />
              </View>
              <View style={styles.awardTextContainer}>
                <Text
                  style={[
                    styles.awardText,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {t("payment.award2025")}
                </Text>
                <Text
                  style={[
                    styles.awardText,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {t("payment.awardApp")}
                </Text>
              </View>
              <View style={styles.awardIconRight}>
                <IconSymbol
                  name="laurel.trailing"
                  size={24}
                  color={isDark ? "#9CA3AF" : "#9CA3AF"}
                />
              </View>
            </View>
          </View>

          {/* 2. VIP特权 - 已有内容 */}
          <Text
            style={[
              styles.sectionTitle,
              { color: isDark ? "#E5E7EB" : "#1F2937" },
            ]}
          >
            {t("payment.vipBenefits")}
          </Text>

          <BenefitItem
            icon="doc.text"
            title={t("payment.benefits.unlimitedTemplates.title")}
            description={t("payment.benefits.unlimitedTemplates.description")}
            isDark={isDark}
          />

          <BenefitItem
            icon="cpu"
            title={t("payment.benefits.advancedModel.title")}
            description={t("payment.benefits.advancedModel.description")}
            isDark={isDark}
          />

          <BenefitItem
            icon="bolt"
            title={t("payment.benefits.priorityRequest.title")}
            description={t("payment.benefits.priorityRequest.description")}
            isDark={isDark}
          />

          <BenefitItem
            icon="cloud.fill"
            title={t("payment.benefits.cloudSync.title")}
            description={t("payment.benefits.cloudSync.description")}
            isDark={isDark}
          />

          {/* 3. 用户评价区 */}
          <Text
            style={[
              styles.sectionTitle,
              { color: isDark ? "#E5E7EB" : "#1F2937", marginTop: 32 },
            ]}
          >
            {t("payment.userReviews")}
          </Text>

          {/* 评价卡片1 */}
          {(() => {
            const review = t("payment.reviews.review1", { returnObjects: true }) as { title: string; content: string; author: string };
            return (
              <View
                style={[
                  styles.reviewCard,
                  { backgroundColor: isDark ? "#1E2122" : "#F9FAFB" },
                ]}
              >
                <View style={styles.reviewHeader}>
                  <Text
                    style={[
                      styles.reviewTitle,
                      { color: isDark ? "#E5E7EB" : "#1F2937" },
                    ]}
                  >
                    {review.title}
                  </Text>
                  <View style={styles.starsContainer}>
                    {[1, 2, 3, 4, 5].map((_, index) => (
                      <IconSymbol
                        key={index}
                        name="star.fill"
                        size={12}
                        color="#FBBF24"
                      />
                    ))}
                  </View>
                </View>
                <Text
                  style={[
                    styles.reviewContent,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.content}
                </Text>
                <Text
                  style={[
                    styles.reviewAuthor,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.author}
                </Text>
              </View>
            );
          })()}

          {/* 评价卡片2 - 张艺 */}
          {(() => {
            const review = t("payment.reviews.review2", { returnObjects: true }) as { title: string; content: string; author: string };
            return (
              <View
                style={[
                  styles.reviewCard,
                  { backgroundColor: isDark ? "#1E2122" : "#F9FAFB" },
                ]}
              >
                <View style={styles.reviewHeader}>
                  <Text
                    style={[
                      styles.reviewTitle,
                      { color: isDark ? "#E5E7EB" : "#1F2937" },
                    ]}
                  >
                    {review.title}
                  </Text>
                  <View style={styles.starsContainer}>
                    {[1, 2, 3, 4, 5].map((_, index) => (
                      <IconSymbol
                        key={index}
                        name="star.fill"
                        size={12}
                        color="#FBBF24"
                      />
                    ))}
                  </View>
                </View>
                <Text
                  style={[
                    styles.reviewContent,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.content}
                </Text>
                <Text
                  style={[
                    styles.reviewAuthor,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.author}
                </Text>
              </View>
            );
          })()}

          {/* 评价卡片3 - 王琳 */}
          {(() => {
            const review = t("payment.reviews.review3", { returnObjects: true }) as { title: string; content: string; author: string };
            return (
              <View
                style={[
                  styles.reviewCard,
                  { backgroundColor: isDark ? "#1E2122" : "#F9FAFB" },
                ]}
              >
                <View style={styles.reviewHeader}>
                  <Text
                    style={[
                      styles.reviewTitle,
                      { color: isDark ? "#E5E7EB" : "#1F2937" },
                    ]}
                  >
                    {review.title}
                  </Text>
                  <View style={styles.starsContainer}>
                    {[1, 2, 3, 4, 5].map((_, index) => (
                      <IconSymbol
                        key={index}
                        name={index < 4 ? "star.fill" : "star.leadinghalf.fill"}
                        size={12}
                        color="#FBBF24"
                      />
                    ))}
                  </View>
                </View>
                <Text
                  style={[
                    styles.reviewContent,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.content}
                </Text>
                <Text
                  style={[
                    styles.reviewAuthor,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.author}
                </Text>
              </View>
            );
          })()}

          {/* 评价卡片4 - 陈浩 */}
          {(() => {
            const review = t("payment.reviews.review4", { returnObjects: true }) as { title: string; content: string; author: string };
            return (
              <View
                style={[
                  styles.reviewCard,
                  { backgroundColor: isDark ? "#1E2122" : "#F9FAFB" },
                ]}
              >
                <View style={styles.reviewHeader}>
                  <Text
                    style={[
                      styles.reviewTitle,
                      { color: isDark ? "#E5E7EB" : "#1F2937" },
                    ]}
                  >
                    {review.title}
                  </Text>
                  <View style={styles.starsContainer}>
                    {[1, 2, 3, 4].map((_, index) => (
                      <IconSymbol
                        key={index}
                        name="star.fill"
                        size={12}
                        color="#FBBF24"
                      />
                    ))}
                    <IconSymbol name="star" size={12} color="#FBBF24" />
                  </View>
                </View>
                <Text
                  style={[
                    styles.reviewContent,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.content}
                </Text>
                <Text
                  style={[
                    styles.reviewAuthor,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.author}
                </Text>
              </View>
            );
          })()}

          {/* 评价卡片5 - 刘佳 */}
          {(() => {
            const review = t("payment.reviews.review5", { returnObjects: true }) as { title: string; content: string; author: string };
            return (
              <View
                style={[
                  styles.reviewCard,
                  { backgroundColor: isDark ? "#1E2122" : "#F9FAFB" },
                ]}
              >
                <View style={styles.reviewHeader}>
                  <Text
                    style={[
                      styles.reviewTitle,
                      { color: isDark ? "#E5E7EB" : "#1F2937" },
                    ]}
                  >
                    {review.title}
                  </Text>
                  <View style={styles.starsContainer}>
                    {[1, 2, 3, 4, 5].map((_, index) => (
                      <IconSymbol
                        key={index}
                        name="star.fill"
                        size={12}
                        color="#FBBF24"
                      />
                    ))}
                  </View>
                </View>
                <Text
                  style={[
                    styles.reviewContent,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.content}
                </Text>
                <Text
                  style={[
                    styles.reviewAuthor,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.author}
                </Text>
              </View>
            );
          })()}

          {/* 评价卡片6 - 杨花 */}
          {(() => {
            const review = t("payment.reviews.review6", { returnObjects: true }) as { title: string; content: string; author: string };
            return (
              <View
                style={[
                  styles.reviewCard,
                  { backgroundColor: isDark ? "#1E2122" : "#F9FAFB" },
                ]}
              >
                <View style={styles.reviewHeader}>
                  <Text
                    style={[
                      styles.reviewTitle,
                      { color: isDark ? "#E5E7EB" : "#1F2937" },
                    ]}
                  >
                    {review.title}
                  </Text>
                  <View style={styles.starsContainer}>
                    {[1, 2, 3, 4, 5].map((_, index) => (
                      <IconSymbol
                        key={index}
                        name="star.fill"
                        size={12}
                        color="#FBBF24"
                      />
                    ))}
                  </View>
                </View>
                <Text
                  style={[
                    styles.reviewContent,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.content}
                </Text>
                <Text
                  style={[
                    styles.reviewAuthor,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.author}
                </Text>
              </View>
            );
          })()}

          {/* 评价卡片7 - 周星 */}
          {(() => {
            const review = t("payment.reviews.review7", { returnObjects: true }) as { title: string; content: string; author: string };
            return (
              <View
                style={[
                  styles.reviewCard,
                  { backgroundColor: isDark ? "#1E2122" : "#F9FAFB" },
                ]}
              >
                <View style={styles.reviewHeader}>
                  <Text
                    style={[
                      styles.reviewTitle,
                      { color: isDark ? "#E5E7EB" : "#1F2937" },
                    ]}
                  >
                    {review.title}
                  </Text>
                  <View style={styles.starsContainer}>
                    {[1, 2, 3, 4].map((_, index) => (
                      <IconSymbol
                        key={index}
                        name="star.fill"
                        size={12}
                        color="#FBBF24"
                      />
                    ))}
                    <IconSymbol
                      name="star.leadinghalf.fill"
                      size={12}
                      color="#FBBF24"
                    />
                  </View>
                </View>
                <Text
                  style={[
                    styles.reviewContent,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.content}
                </Text>
                <Text
                  style={[
                    styles.reviewAuthor,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.author}
                </Text>
              </View>
            );
          })()}

          {/* 评价卡片8 - 赵国际 */}
          {(() => {
            const review = t("payment.reviews.review8", { returnObjects: true }) as { title: string; content: string; author: string };
            return (
              <View
                style={[
                  styles.reviewCard,
                  { backgroundColor: isDark ? "#1E2122" : "#F9FAFB" },
                ]}
              >
                <View style={styles.reviewHeader}>
                  <Text
                    style={[
                      styles.reviewTitle,
                      { color: isDark ? "#E5E7EB" : "#1F2937" },
                    ]}
                  >
                    {review.title}
                  </Text>
                  <View style={styles.starsContainer}>
                    {[1, 2, 3, 4, 5].map((_, index) => (
                      <IconSymbol
                        key={index}
                        name="star.fill"
                        size={12}
                        color="#FBBF24"
                      />
                    ))}
                  </View>
                </View>
                <Text
                  style={[
                    styles.reviewContent,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.content}
                </Text>
                <Text
                  style={[
                    styles.reviewAuthor,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.author}
                </Text>
              </View>
            );
          })()}

          {/* 评价卡片9 - 孙光 */}
          {(() => {
            const review = t("payment.reviews.review9", { returnObjects: true }) as { title: string; content: string; author: string };
            return (
              <View
                style={[
                  styles.reviewCard,
                  { backgroundColor: isDark ? "#1E2122" : "#F9FAFB" },
                ]}
              >
                <View style={styles.reviewHeader}>
                  <Text
                    style={[
                      styles.reviewTitle,
                      { color: isDark ? "#E5E7EB" : "#1F2937" },
                    ]}
                  >
                    {review.title}
                  </Text>
                  <View style={styles.starsContainer}>
                    {[1, 2, 3, 4, 5].map((_, index) => (
                      <IconSymbol
                        key={index}
                        name="star.fill"
                        size={12}
                        color="#FBBF24"
                      />
                    ))}
                  </View>
                </View>
                <Text
                  style={[
                    styles.reviewContent,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.content}
                </Text>
                <Text
                  style={[
                    styles.reviewAuthor,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {review.author}
                </Text>
              </View>
            );
          })()}

          {/* 隐私协议和条款 */}
          <Text
            style={[
              styles.termsText,
              { color: isDark ? "#9CA3AF" : "#6B7280" },
            ]}
          >
            {t("payment.subscriptionTerms", {
              store:
                Platform.OS === "ios"
                  ? t("payment.subscriptionTermsIos")
                  : t("payment.subscriptionTermsAndroid"),
            })}
          </Text>

          {/* 底部空间，确保可以滚动到底部，不被底部支付面板遮挡 */}
          <View style={styles.bottomSpace} />
        </ScrollView>
      </View>

      {/* 底部固定操作区 - 包含支付选项和购买按钮 */}
      <View
        style={[
          styles.footer,
          {
            position: "absolute",
            bottom: 0,
            left: 0,
            right: 0,
            borderTopColor: isDark ? "#2B2F31" : "#F3F4F6",
            backgroundColor: isDark ? "#121212" : "#FFFFFF",
            paddingBottom: 34, // 为底部安全区域留出空间
            paddingTop: 16,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: -2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 5,
          },
        ]}
      >
        {/* 支付选项 */}
        {!loading && (
          <View style={styles.subscriptionOptionsContainer}>
            {/* 年度套餐选项 */}
            <TouchableOpacity
              style={[
                styles.subscriptionCard,
                {
                  borderColor:
                    selectedSubscription === SUBSCRIPTION_TYPE.YEARLY
                      ? "#8B5CF6"
                      : isDark
                      ? "#2B2F31"
                      : "#E5E7EB",
                  backgroundColor: isPlanDisabled(SUBSCRIPTION_TYPE.YEARLY) 
                    ? (isDark ? "#1A1A1A" : "#F5F5F5")
                    : (isDark ? "#1E2122" : "#FFFFFF"),
                  borderWidth:
                    selectedSubscription === SUBSCRIPTION_TYPE.YEARLY ? 2 : 1,
                  opacity: isPlanDisabled(SUBSCRIPTION_TYPE.YEARLY) ? 0.6 : 1,
                },
              ]}
              onPress={() => {
                if (!isPlanDisabled(SUBSCRIPTION_TYPE.YEARLY)) {
                  setSelectedSubscription(SUBSCRIPTION_TYPE.YEARLY);
                }
              }}
              disabled={isPlanDisabled(SUBSCRIPTION_TYPE.YEARLY)}
            >
              {/* 折扣标签放在卡片外部，不会影响内部布局 */}
              {isPlanDisabled(SUBSCRIPTION_TYPE.YEARLY) ? (
                <View style={[styles.discountBadge, { backgroundColor: "#9CA3AF" }]}>
                  <Text style={styles.discountText}>{t("payment.currentPackage")}</Text>
                </View>
              ) : (
                <View style={styles.discountBadge}>
                  <Text style={styles.discountText}>
                    {discountPercentage}% OFF
                  </Text>
                </View>
              )}

              <View style={styles.cardContentContainer}>
                <View style={styles.subscriptionTitleRow}>
                  <Text
                    style={[
                      styles.subscriptionTitle,
                      { color: isDark ? "#E5E7EB" : "#1F2937" },
                    ]}
                  >
                    {t("payment.yearly")}
                  </Text>

                  <View
                    style={[
                      styles.radioButton,
                      {
                        borderColor:
                          selectedSubscription === SUBSCRIPTION_TYPE.YEARLY
                            ? "#8B5CF6"
                            : "#E5E7EB",
                      },
                    ]}
                  >
                    {selectedSubscription === SUBSCRIPTION_TYPE.YEARLY && (
                      <View style={styles.radioButtonInner}>
                        <IconSymbol
                          name="checkmark"
                          size={14}
                          color="#FFFFFF"
                        />
                      </View>
                    )}
                  </View>
                </View>

                <Text
                  style={[
                    styles.subscriptionPrice,
                    { color: isDark ? "#E5E7EB" : "#1F2937" },
                  ]}
                >
                  {hasPricesLoaded ? `$${yearlyMonthlyPrice}${t('payment.periodUnit.month' as any)}` : `--${t('payment.periodUnit.month' as any)}`}
                </Text>

                <Text
                  style={[
                    styles.subscriptionBillingInfo,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {hasPricesLoaded ? (
                    <>
                      {t("payment.billedAs", {
                        price: yearlyPriceValue.toFixed(2),
                        period: "year",
                      })}
                      {yearlyTrialInfo.hasFreeTrial && (
                        <>，{yearlyTrialInfo.trialDays}天免费试用后收费</>
                      )}
                    </>
                  ) : (
                    '价格加载中...'
                  )}
                </Text>
              </View>
            </TouchableOpacity>

            {/* 月度套餐选项 */}
            <TouchableOpacity
              style={[
                styles.subscriptionCard,
                {
                  borderColor:
                    selectedSubscription === SUBSCRIPTION_TYPE.MONTHLY
                      ? "#8B5CF6"
                      : isDark
                      ? "#2B2F31"
                      : "#E5E7EB",
                  backgroundColor: isPlanDisabled(SUBSCRIPTION_TYPE.MONTHLY) 
                    ? (isDark ? "#1A1A1A" : "#F5F5F5")
                    : (isDark ? "#1E2122" : "#FFFFFF"),
                  borderWidth:
                    selectedSubscription === SUBSCRIPTION_TYPE.MONTHLY ? 2 : 1,
                  opacity: isPlanDisabled(SUBSCRIPTION_TYPE.MONTHLY) ? 0.6 : 1,
                },
              ]}
              onPress={() => {
                if (!isPlanDisabled(SUBSCRIPTION_TYPE.MONTHLY)) {
                  setSelectedSubscription(SUBSCRIPTION_TYPE.MONTHLY);
                }
              }}
              disabled={isPlanDisabled(SUBSCRIPTION_TYPE.MONTHLY)}
            >
              {/* 为月度套餐添加当前套餐标签 */}
              {isPlanDisabled(SUBSCRIPTION_TYPE.MONTHLY) && (
                <View style={[styles.discountBadge, { backgroundColor: "#9CA3AF" }]}>
                  <Text style={styles.discountText}>{t("payment.currentPackage")}</Text>
                </View>
              )}
              
              <View style={styles.cardContentContainer}>
                <View style={styles.subscriptionTitleRow}>
                  <Text
                    style={[
                      styles.subscriptionTitle,
                      { color: isDark ? "#E5E7EB" : "#1F2937" },
                    ]}
                  >
                    {t("payment.monthly")}
                  </Text>

                  <View
                    style={[
                      styles.radioButton,
                      {
                        borderColor:
                          selectedSubscription === SUBSCRIPTION_TYPE.MONTHLY
                            ? "#8B5CF6"
                            : "#E5E7EB",
                      },
                    ]}
                  >
                    {selectedSubscription === SUBSCRIPTION_TYPE.MONTHLY && (
                      <View style={styles.radioButtonInner}>
                        <IconSymbol
                          name="checkmark"
                          size={14}
                          color="#FFFFFF"
                        />
                      </View>
                    )}
                  </View>
                </View>

                <Text
                  style={[
                    styles.subscriptionPrice,
                    { color: isDark ? "#E5E7EB" : "#1F2937" },
                  ]}
                >
                  {hasPricesLoaded ? `$${monthlyPriceValue.toFixed(2)}${t('payment.periodUnit.month' as any)}` : `--${t('payment.periodUnit.month' as any)}`}
                </Text>

                <Text
                  style={[
                    styles.subscriptionBillingInfo,
                    { color: isDark ? "#9CA3AF" : "#6B7280" },
                  ]}
                >
                  {hasPricesLoaded ? (
                    <>
                      {t("payment.billedAs", {
                        price: monthlyPriceValue.toFixed(2),
                        period: "month",
                      })}
                      {monthlyTrialInfo.hasFreeTrial && (
                        <>，{monthlyTrialInfo.trialDays}天免费试用后收费</>
                      )}
                    </>
                  ) : (
                    '价格加载中...'
                  )}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        )}

        {/* 购买按钮 */}
        <TouchableOpacity
          style={[
            styles.purchaseButton,
            { opacity: processing || loading || !hasPricesLoaded ? 0.7 : 1 },
          ]}
          onPress={handlePurchase}
          disabled={processing || loading || !hasPricesLoaded}
        >
          <LinearGradient
            colors={["#6A5AE1", "#8364E2"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.purchaseButtonGradient}
          >
            {processing ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.purchaseButtonText}>{getButtonText()}</Text>
            )}
          </LinearGradient>
        </TouchableOpacity>

        {/* 底部链接 */}
        <View style={styles.footerLinks}>
          <TouchableOpacity
            style={styles.footerLink}
            onPress={handleRestorePurchases}
            disabled={processing}
          >
            <Text style={[styles.footerLinkText, { color: "#8B5CF6" }]}>
              {t("payment.restorePurchases")}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.footerLink} onPress={handleTermsOfService}>
            <Text style={[styles.footerLinkText, { color: "#8B5CF6" }]}>
              {t("payment.termsOfService")}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.footerLink} onPress={handlePrivacyPolicy}>
            <Text style={[styles.footerLinkText, { color: "#8B5CF6" }]}>
              {t("payment.privacyPolicy")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentWrapper: {
    flex: 1,
    marginBottom: 320, // 为底部固定面板留出空间
  },
  outsideClickArea: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F3F4F6",
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  scrollContent: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 24,
    paddingTop: 16,
    paddingHorizontal: 16,
  },

  // 主引导块样式
  mainPromoContainer: {
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 24,
    paddingBottom: 32,
  },
  logoContainer: {
    marginBottom: 16,
    width: 80,
    height: 80,
    justifyContent: "center",
    alignItems: "center",
  },
  appLogo: {
    width: 80,
    height: 80,
    borderRadius: 20,
  },
  logoBackground: {
    width: 64,
    height: 64,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  promoTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
  },
  promoSubtitle: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 16,
    textAlign: "center",
    paddingHorizontal: 10,
  },
  awardBadge: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 4,
  },
  awardIconLeft: {
    marginRight: 4,
  },
  awardIconRight: {
    marginLeft: 4,
  },
  awardTextContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  awardText: {
    fontSize: 13,
    textAlign: "center",
  },

  // 用户评价区域样式
  reviewCard: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 12,
  },
  reviewHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  reviewTitle: {
    fontSize: 15,
    fontWeight: "bold",
  },
  starsContainer: {
    flexDirection: "row",
  },
  reviewContent: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  reviewAuthor: {
    fontSize: 13,
    textAlign: "right",
    fontStyle: "italic",
  },

  vipCard: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 24,
    borderRadius: 16,
    padding: 20,
    shadowColor: "#4F46E5",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  vipBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: "flex-start",
    marginBottom: 12,
  },
  vipBadgeText: {
    color: "#FFFFFF",
    fontWeight: "bold",
    fontSize: 12,
    marginLeft: 4,
  },
  vipCardTitle: {
    color: "#FFFFFF",
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 8,
  },
  vipCardSubtitle: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 14,
    lineHeight: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginVertical: 16,
    paddingHorizontal: 20,
  },

  // 支付选项(新增底部固定选项样式)
  subscriptionOptionsContainer: {
    flexDirection: "row",
    marginBottom: 16,
    justifyContent: "space-between",
  },
  subscriptionCard: {
    flex: 1,
    justifyContent: "flex-start",
    alignItems: "flex-start",
    padding: 16,
    marginHorizontal: 5,
    borderRadius: 12,
    backgroundColor: "#FFFFFF",
    position: "relative",
    paddingTop: 24, // 为折扣标签预留空间
  },
  cardContentContainer: {
    width: "100%",
  },
  discountBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    backgroundColor: "#8B5CF6",
    position: "absolute",
    top: -10,
    left: "50%",
    transform: [{ translateX: -25 }], // 向左偏移标签宽度的一半以实现居中
    zIndex: 1,
    minWidth: 50, // 设置最小宽度，使计算居中更准确
    alignItems: "center", // 文字在标签内居中
  },
  discountText: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  subscriptionTitleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
    width: "100%",
  },
  subscriptionTitle: {
    fontSize: 16,
    fontWeight: "bold",
  },
  radioButton: {
    width: 22,
    height: 22,
    borderRadius: 11,
    borderWidth: 2,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
  },
  radioButtonInner: {
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: "#8B5CF6",
    justifyContent: "center",
    alignItems: "center",
  },
  subscriptionPrice: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 8,
  },
  subscriptionBillingInfo: {
    fontSize: 12,
    lineHeight: 16,
  },
  subscriptionOption: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 5,
    position: "relative",
  },
  selectedOption: {
    borderWidth: 2,
  },
  optionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: "bold",
  },
  recommendedBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  recommendedText: {
    fontSize: 12,
    fontWeight: "bold",
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "baseline",
    marginBottom: 8,
  },
  price: {
    fontSize: 20,
    fontWeight: "bold",
    marginRight: 4,
  },
  duration: {
    fontSize: 14,
  },
  optionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  selectionIndicator: {
    position: "absolute",
    right: 16,
    top: 16,
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    justifyContent: "center",
    alignItems: "center",
  },

  // 优化VIP特权部分
  benefitItem: {
    flexDirection: "row",
    alignItems: "center", // 改为center，使图标与标题在同一水平线上
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  benefitIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  benefitTextContainer: {
    flex: 1,
    justifyContent: "center", // 添加垂直居中
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 4,
  },
  benefitDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  termsText: {
    fontSize: 12,
    textAlign: "center",
    marginTop: 24,
    marginHorizontal: 20,
    lineHeight: 18,
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  purchaseButton: {
    borderRadius: 8,
    overflow: "hidden",
    marginBottom: 12,
  },
  purchaseButtonGradient: {
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  purchaseButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
  footerLinks: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 12,
  },
  footerLink: {
    padding: 8,
    marginHorizontal: 12,
  },
  footerLinkText: {
    fontSize: 14,
    fontWeight: "500",
  },
  loadingContainer: {
    padding: 40,
    alignItems: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  bottomSpace: {
    height: 40,
  },
});

export default PaymentWall;
