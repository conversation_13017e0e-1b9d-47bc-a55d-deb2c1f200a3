import React, { createContext, useEffect, useContext } from 'react';
import { useColorScheme, AppState, AppStateStatus } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { selectThemeMode, loadThemeMode } from '@/store/slices/themeSlice';
import { loadFastModeFromStorage } from '@/store/slices/fastModeSlice';
import { AppDispatch } from '@/store';

// 创建主题上下文
type ThemeContextType = 'light' | 'dark';
const ThemeContext = createContext<ThemeContextType>('light');

// 导出hook用于获取当前主题
export const useTheme = () => useContext(ThemeContext);

// ThemeProvider属性
interface ThemeProviderProps {
  children: React.ReactNode;
}

/**
 * 主题提供者组件
 * 根据用户选择的主题模式和系统主题设置，计算并提供当前应用的主题
 */
export function ThemeProvider({ children }: ThemeProviderProps) {
  const themeMode = useSelector(selectThemeMode);
  const systemColorScheme = useColorScheme();
  const dispatch = useDispatch<AppDispatch>();
  
  // 计算当前主题
  const currentTheme: ThemeContextType = 
    themeMode === 'system' 
      ? (systemColorScheme || 'light') as ThemeContextType 
      : themeMode;

  // 初始加载保存的主题设置和极速模式设置
  useEffect(() => {
    dispatch(loadThemeMode());
    dispatch(loadFastModeFromStorage());
  }, [dispatch]);

  // 监听系统主题变化
  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      // 当应用从后台切换到前台时，重新检查系统主题
      if (nextAppState === 'active' && themeMode === 'system') {
        // 主题会自动更新，因为useColorScheme会触发重新渲染
      }
    });

    return () => {
      subscription.remove();
    };
  }, [themeMode]);

  return (
    <ThemeContext.Provider value={currentTheme}>
      {children}
    </ThemeContext.Provider>
  );
} 