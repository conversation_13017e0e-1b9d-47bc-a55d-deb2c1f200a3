const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// 添加路径别名支持
config.resolver.alias = {
  '@': path.resolve(__dirname, './'),
  '@components': path.resolve(__dirname, './components'),
  '@screens': path.resolve(__dirname, './app'),
  '@assets': path.resolve(__dirname, './assets'),
  '@hooks': path.resolve(__dirname, './hooks'),
  '@utils': path.resolve(__dirname, './utils'),
  '@services': path.resolve(__dirname, './services'),
  '@store': path.resolve(__dirname, './store'),
  '@constants': path.resolve(__dirname, './constants'),
  '@types': path.resolve(__dirname, './types'),
};

// 确保支持的文件扩展名
config.resolver.sourceExts = [...config.resolver.sourceExts, 'mjs', 'cjs'];

module.exports = config; 