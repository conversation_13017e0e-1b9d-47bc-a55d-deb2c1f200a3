/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#0a7ea4';
const tintColorDark = '#61dafb';

export const Colors = {
  light: {
    text: '#11181C',
    secondaryText: '#687076',
    background: '#fff',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
    cardBackground: '#fff',
    border: '#E6E8EB',
    input: '#F1F3F5',
    primaryButton: '#0a7ea4',
    primaryButtonText: '#fff',
    secondaryButton: '#F1F3F5',
    secondaryButtonText: '#11181C',
    dialogBackground: '#fff',
    success: '#16A34A',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
    vipGradientStart: '#6366f1',
    vipGradientEnd: '#4f46e5',
    vipBadgeStart: '#fde047',
    vipBadgeEnd: '#ffc107',
    freeGradientStart: '#8364e2',
    freeGradientEnd: '#6a5ae1',
    freeBadgeStart: '#ffb655',
    freeBadgeEnd: '#ff8c37',
    settingsItemIconBg: {
      purple: '#F3E8FF',
      green: '#DCFCE7',
      blue: '#DBEAFE',
      amber: '#FEF3C7',
      sky: '#E0F2FE',
      red: '#FEE2E2',
    },
    settingsItemIcon: {
      purple: '#9333EA',
      green: '#16A34A',
      blue: '#2563EB',
      amber: '#D97706',
      yellow: '#EABE41',
      sky: '#0EA5E9',
      red: '#EF4444',
    },
  },
  dark: {
    text: '#ECEDEE',
    secondaryText: '#9BA1A6',
    background: '#151718',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
    cardBackground: '#1E2122',
    border: '#2B2F31',
    input: '#26292B',
    primaryButton: '#61dafb',
    primaryButtonText: '#151718',
    secondaryButton: '#26292B',
    secondaryButtonText: '#ECEDEE',
    dialogBackground: '#1E2122',
    success: '#22C55E',
    warning: '#FBBF24',
    error: '#F87171',
    info: '#60A5FA',
    vipGradientStart: '#4338ca',
    vipGradientEnd: '#3730a3',
    vipBadgeStart: '#fbbf24',
    vipBadgeEnd: '#d97706',
    freeGradientStart: '#6d28d9',
    freeGradientEnd: '#5b21b6',
    freeBadgeStart: '#f97316',
    freeBadgeEnd: '#ea580c',
    settingsItemIconBg: {
      purple: '#3B0764',
      green: '#052E16',
      blue: '#082F49',
      amber: '#451A03',
      sky: '#082F49',
      red: '#450A0A',
    },
    settingsItemIcon: {
      purple: '#C084FC',
      green: '#4ADE80',
      blue: '#60A5FA',
      amber: '#FBBF24',
      yellow: '#FDE047',
      sky: '#38BDF8',
      red: '#F87171',
    },
  },
};
