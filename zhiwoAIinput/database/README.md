# 数据库配置和问卷数据统计

## 概述

本目录包含用户onboarding问卷数据收集和统计的数据库配置文件。

## 文件说明

- `queries.sql` - 常用的数据查询和统计SQL语句
- `README.md` - 本文档
- `../supabase/migrations/` - Supabase迁移文件目录

## 迁移文件

项目使用Supabase迁移系统管理数据库变更：

- `20241219000001_create_onboarding_surveys.sql` - 创建问卷数据表
- `20241219000002_add_onboarding_triggers_and_views.sql` - 添加触发器和统计视图

## 数据库表结构

### onboarding_surveys 表

用于存储用户完成onboarding流程时的问卷数据。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | UUID | 主键ID |
| user_id | UUID | 用户ID（可为空，匿名用户） |
| source | TEXT | 用户了解应用的来源 |
| use_cases | JSONB | 用户选择的使用场景数组 |
| selected_templates | JSONB | 用户选择的风格模板ID数组 |
| device_info | JSONB | 设备信息（平台、版本等） |
| completed_at | TIMESTAMP | 问卷完成时间 |
| created_at | TIMESTAMP | 记录创建时间 |
| updated_at | TIMESTAMP | 记录更新时间 |

## 统计视图

系统创建了多个视图来方便数据统计和分析：

### 1. onboarding_survey_stats
基础统计视图，按来源和日期分组：
- `source` - 用户来源
- `total_count` - 总问卷数
- `registered_users` - 注册用户数
- `anonymous_users` - 匿名用户数
- `completion_date` - 完成日期

### 2. onboarding_use_cases_stats
使用场景统计视图，展开JSON数组：
- `source` - 用户来源
- `use_case` - 具体使用场景
- `count` - 选择次数
- `completion_date` - 完成日期

### 3. onboarding_templates_stats
模板选择统计视图，展开JSON数组：
- `source` - 用户来源
- `template_id` - 模板ID
- `count` - 选择次数
- `completion_date` - 完成日期

### 4. admin_onboarding_overview
管理员概览视图，显示详细的问卷信息。

## 部署说明

### 方法1：使用Supabase CLI（推荐）

如果您使用Supabase CLI，迁移会自动执行：

```bash
# 拉取远程迁移
supabase db pull

# 应用迁移
supabase db push
```

### 方法2：手动执行迁移

在Supabase控制台的SQL编辑器中按顺序执行：

1. `supabase/migrations/20241219000001_create_onboarding_surveys.sql`
2. `supabase/migrations/20241219000002_add_onboarding_triggers_and_views.sql`

### 方法3：使用迁移API

您也可以通过编程方式执行迁移：

```javascript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(url, key)

// 执行迁移文件
await supabase.rpc('exec_sql', { 
  sql: migrationFileContent 
})
```

## 权限配置

迁移文件已自动配置了行级安全策略（RLS）：
- 登录用户只能查看和创建自己的问卷记录
- 匿名用户可以创建问卷记录
- 管理员（<EMAIL>）可以查看所有数据用于统计

## 数据收集

应用会在用户完成onboarding流程时自动保存问卷数据到数据库。

## 数据查询和统计

### 基础统计

```sql
-- 查看问卷总数
SELECT COUNT(*) FROM onboarding_surveys;

-- 查看今日新增问卷
SELECT COUNT(*) FROM onboarding_surveys 
WHERE DATE(completed_at) = CURRENT_DATE;
```

### 详细统计

使用 `queries.sql` 中的预定义查询语句，包含15种不同的统计分析：

1. **整体概览** - 总问卷数、用户数等
2. **来源分析** - 用户了解应用的渠道统计
3. **使用场景分析** - 用户选择的使用场景分布
4. **模板偏好分析** - 用户选择的风格模板统计
5. **设备平台分析** - iOS/Android用户分布
6. **时间趋势分析** - 每日问卷完成量
7. **交叉分析** - 来源与使用场景的关联
8. **详细数据查看** - 最近的问卷详情
9. **基础统计视图** - 按来源和日期的汇总
10. **使用场景统计视图** - 详细的场景选择数据
11. **模板选择统计视图** - 详细的模板选择数据
12. **管理员概览视图** - 完整的问卷信息
13. **热门使用场景分析** - 跨来源的场景受欢迎程度
14. **热门模板分析** - 跨来源的模板受欢迎程度
15. **时间趋势分析** - 最近7天的每日统计

### 统计视图

使用预创建的统计视图：

```sql
-- 基础统计
SELECT * FROM onboarding_survey_stats 
ORDER BY completion_date DESC;

-- 使用场景详细统计
SELECT * FROM onboarding_use_cases_stats
ORDER BY completion_date DESC, count DESC;

-- 模板选择详细统计
SELECT * FROM onboarding_templates_stats
ORDER BY completion_date DESC, count DESC;

-- 管理员概览
SELECT * FROM admin_onboarding_overview
LIMIT 100;
```

## 数据导出

### 导出CSV格式

在Supabase控制台中可以直接导出查询结果为CSV文件，方便在Excel或其他工具中进一步分析。

### API访问

可以通过Supabase REST API访问统计数据，用于构建数据仪表板。

## 迁移管理

### 创建新迁移

如果需要修改表结构，请创建新的迁移文件：

```bash
# 使用Supabase CLI创建新迁移
supabase migration new description_of_change

# 或手动创建，命名格式：
# YYYYMMDDHHMMSS_description.sql
```

### 回滚迁移

如果需要回滚，可以创建反向迁移：

```sql
-- 例如：删除表的回滚迁移
DROP TABLE IF EXISTS onboarding_surveys CASCADE;
DROP VIEW IF EXISTS onboarding_survey_stats CASCADE;
DROP VIEW IF EXISTS onboarding_use_cases_stats CASCADE;
DROP VIEW IF EXISTS onboarding_templates_stats CASCADE;
DROP VIEW IF EXISTS admin_onboarding_overview CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
```

## 已修复的问题

### PostgreSQL聚合函数限制

原始的统计视图中使用了以下SQL语句，会导致执行错误：

```sql
-- 错误的写法（会报错）
ARRAY_AGG(DISTINCT jsonb_array_elements_text(use_cases)) as all_use_cases
```

**错误原因**：PostgreSQL不允许在聚合函数内直接使用集合返回函数。

**解决方案**：将复杂的JSON数组统计拆分为独立的视图：
- `onboarding_use_cases_stats` - 专门统计使用场景
- `onboarding_templates_stats` - 专门统计模板选择

这样既避免了SQL错误，又提供了更详细和灵活的统计功能。

## 注意事项

1. **版本控制** - 迁移文件应纳入版本控制
2. **顺序执行** - 迁移必须按时间戳顺序执行
3. **幂等性** - 迁移应设计为可重复执行
4. **备份** - 执行迁移前建议备份数据库
5. **测试** - 在生产环境执行前应在测试环境验证

## 扩展功能

### 未来可以添加的功能

1. **实时仪表板** - 基于数据创建可视化仪表板
2. **自动报告** - 定期生成统计报告
3. **A/B测试** - 对比不同引导流程的效果
4. **用户画像** - 基于问卷数据构建用户画像

## 技术支持

如有问题，请查看：
1. [Supabase迁移文档](https://supabase.com/docs/guides/cli/local-development#database-migrations)
2. [PostgreSQL官方文档](https://www.postgresql.org/docs/)
3. 项目技术文档 