-- 查询onboarding问卷统计数据的SQL语句集合

-- 1. 查看所有问卷数据概览
SELECT 
  COUNT(*) as total_surveys,
  COUNT(DISTINCT user_id) as unique_users,
  COUNT(CASE WHEN user_id IS NULL THEN 1 END) as anonymous_surveys,
  MIN(completed_at) as first_survey,
  MAX(completed_at) as latest_survey
FROM onboarding_surveys;

-- 2. 按来源统计
SELECT 
  source,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM onboarding_surveys
GROUP BY source
ORDER BY count DESC;

-- 3. 按使用场景统计（展开JSON数组）
SELECT 
  use_case,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM onboarding_surveys), 2) as percentage
FROM onboarding_surveys,
LATERAL jsonb_array_elements_text(use_cases) as use_case
GROUP BY use_case
ORDER BY count DESC;

-- 4. 按选择模板统计（展开JSON数组）
SELECT 
  template_id,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM onboarding_surveys), 2) as percentage
FROM onboarding_surveys,
LATERAL jsonb_array_elements_text(selected_templates) as template_id
GROUP BY template_id
ORDER BY count DESC;

-- 5. 按设备平台统计
SELECT 
  device_info->>'platform' as platform,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM onboarding_surveys
WHERE device_info->>'platform' IS NOT NULL
GROUP BY device_info->>'platform'
ORDER BY count DESC;

-- 6. 按日期统计（最近30天）
SELECT 
  DATE(completed_at) as survey_date,
  COUNT(*) as daily_count,
  COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as registered_users,
  COUNT(CASE WHEN user_id IS NULL THEN 1 END) as anonymous_users
FROM onboarding_surveys
WHERE completed_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(completed_at)
ORDER BY survey_date DESC;

-- 7. 来源和使用场景交叉分析
SELECT 
  source,
  use_case,
  COUNT(*) as count
FROM onboarding_surveys,
LATERAL jsonb_array_elements_text(use_cases) as use_case
GROUP BY source, use_case
ORDER BY source, count DESC;

-- 8. 详细数据查看（最近100条）
SELECT 
  id,
  CASE 
    WHEN user_id IS NOT NULL THEN '注册用户'
    ELSE '匿名用户'
  END as user_type,
  source,
  use_cases,
  selected_templates,
  device_info->>'platform' as platform,
  completed_at
FROM onboarding_surveys
ORDER BY completed_at DESC
LIMIT 100;

-- 9. 基础统计视图数据
SELECT * FROM onboarding_survey_stats
ORDER BY completion_date DESC, total_count DESC
LIMIT 50;

-- 10. 使用场景统计视图数据
SELECT * FROM onboarding_use_cases_stats
ORDER BY completion_date DESC, count DESC
LIMIT 50;

-- 11. 模板选择统计视图数据
SELECT * FROM onboarding_templates_stats
ORDER BY completion_date DESC, count DESC
LIMIT 50;

-- 12. 管理员概览视图数据
SELECT * FROM admin_onboarding_overview
LIMIT 100;

-- 13. 综合分析：热门使用场景
SELECT 
  use_case,
  COUNT(*) as total_selections,
  COUNT(DISTINCT source) as from_sources,
  ARRAY_AGG(DISTINCT source) as sources
FROM onboarding_use_cases_stats
GROUP BY use_case
ORDER BY total_selections DESC;

-- 14. 综合分析：热门模板
SELECT 
  template_id,
  COUNT(*) as total_selections,
  COUNT(DISTINCT source) as from_sources,
  ARRAY_AGG(DISTINCT source) as sources
FROM onboarding_templates_stats
GROUP BY template_id
ORDER BY total_selections DESC;

-- 15. 时间趋势分析：最近7天每日统计
SELECT 
  completion_date,
  SUM(total_count) as daily_total,
  SUM(registered_users) as daily_registered,
  SUM(anonymous_users) as daily_anonymous
FROM onboarding_survey_stats
WHERE completion_date >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY completion_date
ORDER BY completion_date DESC; 