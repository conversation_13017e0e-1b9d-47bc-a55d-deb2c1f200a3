/**
 * RevenueCat 配置调试脚本
 * 用于检查EAS打包后的RevenueCat配置状态
 */

import Constants from 'expo-constants';
import { Platform } from 'react-native';
import { log, warn, error } from './services/logService';

export const debugRevenueCatConfig = () => {
  console.log('🔍 开始检查 RevenueCat 配置...');
  console.log('==========================================');
  
  // 1. 检查环境变量
  console.log('📋 环境信息:');
  console.log(`  - 平台: ${Platform.OS}`);
  console.log(`  - 是否为开发环境: ${__DEV__}`);
  console.log(`  - APP_ENV: ${Constants.expoConfig?.extra?.appEnv || 'undefined'}`);
  console.log('');

  // 2. 检查 Constants.expoConfig 中的配置
  console.log('🔧 Expo Config 配置:');
  const extraConfig = Constants.expoConfig?.extra;
  console.log(`  - revenueCatIosApiKey: ${extraConfig?.revenueCatIosApiKey ? '✅ 已配置' : '❌ 未配置'}`);
  console.log(`  - revenueCatAndroidApiKey: ${extraConfig?.revenueCatAndroidApiKey ? '✅ 已配置' : '❌ 未配置'}`);
  console.log('');

  // 3. 检查 process.env 中的配置
  console.log('🌍 Process Env 配置:');
  console.log(`  - REVENUE_CAT_IOS_API_KEY: ${process.env.REVENUE_CAT_IOS_API_KEY ? '✅ 已配置' : '❌ 未配置（正常，通过 Expo Config 传递）'}`);
  console.log(`  - REVENUE_CAT_ANDROID_API_KEY: ${process.env.REVENUE_CAT_ANDROID_API_KEY ? '✅ 已配置' : '❌ 未配置（正常，通过 Expo Config 传递）'}`);
  console.log('');

  // 4. 模拟 purchaseService 中的密钥获取逻辑
  console.log('🔑 API 密钥获取结果:');
  const iosKey = extraConfig?.revenueCatIosApiKey || process.env.REVENUE_CAT_IOS_API_KEY || '';
  const androidKey = extraConfig?.revenueCatAndroidApiKey || process.env.REVENUE_CAT_ANDROID_API_KEY || '';
  
  console.log(`  - iOS API Key: ${iosKey ? '✅ 可用' : '❌ 不可用'}`);
  console.log(`  - Android API Key: ${androidKey ? '✅ 可用' : '❌ 不可用'}`);
  console.log('');

  // 5. 检查当前平台的密钥
  const currentPlatformKey = Platform.select({
    ios: iosKey,
    android: androidKey,
    default: iosKey,
  });
  
  console.log(`🎯 当前平台 (${Platform.OS}) 密钥状态:`);
  console.log(`  - 密钥: ${currentPlatformKey ? '✅ 可用' : '❌ 不可用'}`);
  if (currentPlatformKey) {
    console.log(`  - 密钥前缀: ${currentPlatformKey.substring(0, 8)}...`);
  }
  console.log('');

  // 6. 密钥获取机制说明
  console.log('🔄 密钥获取机制说明:');
  console.log('  📋 EAS 环境变量传递流程:');
  console.log('     eas.json → app.config.ts → Constants.expoConfig.extra → purchaseService');
  console.log('  ⚠️  process.env 在客户端通常不直接可用（安全考虑）');
  console.log('  ✅ 优先级: Expo Config > Process Env > 默认值');
  console.log('');

  // 7. 诊断建议
  console.log('💡 诊断建议:');
  if (!currentPlatformKey) {
    console.log('  ❌ 当前平台的 RevenueCat API 密钥不可用');
    console.log('  📝 请检查:');
    console.log('     1. eas.json 中是否配置了对应环境的 REVENUE_CAT_*_API_KEY');
    console.log('     2. app.config.ts 中是否正确传递了密钥到 extra 配置');
    console.log('     3. 是否使用了正确的构建配置 (development/staging/production)');
  } else {
    console.log('  ✅ RevenueCat API 密钥配置正常');
    
    // 额外说明
    if (extraConfig?.revenueCatIosApiKey && !process.env.REVENUE_CAT_IOS_API_KEY) {
      console.log('  ℹ️  Process Env 未配置但密钥可用 - 这是正常的！');
      console.log('     密钥通过 Expo Config 正确传递，无需担心');
    }
  }
  
  console.log('==========================================');
  console.log('🔍 RevenueCat 配置检查完成');
  
  return {
    hasIosKey: !!iosKey,
    hasAndroidKey: !!androidKey,
    currentPlatformKey: !!currentPlatformKey,
    config: {
      ios: iosKey,
      android: androidKey,
    }
  };
};

// 导出用于在应用中调用
export default debugRevenueCatConfig; 