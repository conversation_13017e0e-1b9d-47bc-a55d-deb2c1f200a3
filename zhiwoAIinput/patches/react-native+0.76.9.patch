diff --git a/node_modules/react-native/React/Base/RCTBridgeModule.h b/node_modules/react-native/React/Base/RCTBridgeModule.h
index a77f7ab..64e713d 100644
--- a/node_modules/react-native/React/Base/RCTBridgeModule.h
+++ b/node_modules/react-native/React/Base/RCTBridgeModule.h
@@ -161,7 +161,7 @@ RCT_EXTERN_C_END
  * Instead of overriding this, directly dispatch the code onto main queue when necessary.
  * Example: dispatch_async(dispatch_get_main_queue, ^{ ... });
  */
-@property (nonatomic, strong, readonly) dispatch_queue_t methodQueue RCT_DEPRECATED;
+@property (nonatomic, assign, readonly) dispatch_queue_t methodQueue RCT_DEPRECATED;
 
 /**
  * Wrap the parameter line of your method implementation with this macro to
diff --git a/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.h b/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.h
new file mode 100644
index 0000000..237c5fe
--- /dev/null
+++ b/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.h
@@ -0,0 +1,54 @@
+
+/*
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by GenerateRCTThirdPartyFabricComponentsProviderH
+ */
+
+#pragma GCC diagnostic push
+#pragma GCC diagnostic ignored "-Wreturn-type-c-linkage"
+
+#import <React/RCTComponentViewProtocol.h>
+
+#ifdef __cplusplus
+extern "C" {
+#endif
+
+Class<RCTComponentViewProtocol> RCTThirdPartyFabricComponentsProvider(const char *name);
+#if RCT_NEW_ARCH_ENABLED
+#ifndef RCT_DYNAMIC_FRAMEWORKS
+
+
+Class<RCTComponentViewProtocol> RNGestureHandlerButtonCls(void) __attribute__((used)); // 1
+Class<RCTComponentViewProtocol> RNGestureHandlerRootViewCls(void) __attribute__((used)); // 1
+
+Class<RCTComponentViewProtocol> RNCSafeAreaProviderCls(void) __attribute__((used)); // 3
+Class<RCTComponentViewProtocol> RNCSafeAreaViewCls(void) __attribute__((used)); // 3
+Class<RCTComponentViewProtocol> RNSFullWindowOverlayCls(void) __attribute__((used)); // 4
+Class<RCTComponentViewProtocol> RNSModalScreenCls(void) __attribute__((used)); // 4
+Class<RCTComponentViewProtocol> RNSScreenContainerCls(void) __attribute__((used)); // 4
+Class<RCTComponentViewProtocol> RNSScreenContentWrapperCls(void) __attribute__((used)); // 4
+Class<RCTComponentViewProtocol> RNSScreenFooterCls(void) __attribute__((used)); // 4
+Class<RCTComponentViewProtocol> RNSScreenCls(void) __attribute__((used)); // 4
+Class<RCTComponentViewProtocol> RNSScreenNavigationContainerCls(void) __attribute__((used)); // 4
+Class<RCTComponentViewProtocol> RNSScreenStackHeaderConfigCls(void) __attribute__((used)); // 4
+Class<RCTComponentViewProtocol> RNSScreenStackHeaderSubviewCls(void) __attribute__((used)); // 4
+Class<RCTComponentViewProtocol> RNSScreenStackCls(void) __attribute__((used)); // 4
+Class<RCTComponentViewProtocol> RNSSearchBarCls(void) __attribute__((used)); // 4
+#if !TARGET_OS_OSX && !TARGET_OS_TV && !TARGET_OS_VISION
+Class<RCTComponentViewProtocol> RNCWebViewCls(void) __attribute__((used)); // 5
+#endif
+
+
+#endif
+#endif
+
+#ifdef __cplusplus
+}
+#endif
+
+#pragma GCC diagnostic pop
+
diff --git a/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.mm b/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.mm
new file mode 100644
index 0000000..f3b9fcb
--- /dev/null
+++ b/node_modules/react-native/React/Fabric/RCTThirdPartyFabricComponentsProvider.mm
@@ -0,0 +1,69 @@
+
+/**
+ * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
+ *
+ * Do not edit this file as changes may cause incorrect behavior and will be lost
+ * once the code is regenerated.
+ *
+ * @generated by GenerateRCTThirdPartyFabricComponentsProviderCpp
+ */
+
+// OSS-compatibility layer
+
+#import "RCTThirdPartyFabricComponentsProvider.h"
+
+#import <string>
+#import <unordered_map>
+
+Class<RCTComponentViewProtocol> RCTThirdPartyFabricComponentsProvider(const char *name) {
+  static std::unordered_map<std::string, Class (*)(void)> sFabricComponentsClassMap = {
+    #if RCT_NEW_ARCH_ENABLED
+    #ifndef RCT_DYNAMIC_FRAMEWORKS
+
+
+    {"RNGestureHandlerButton", RNGestureHandlerButtonCls}, // 1
+
+    {"RNGestureHandlerRootView", RNGestureHandlerRootViewCls}, // 1
+
+
+    {"RNCSafeAreaProvider", RNCSafeAreaProviderCls}, // 3
+
+    {"RNCSafeAreaView", RNCSafeAreaViewCls}, // 3
+
+    {"RNSFullWindowOverlay", RNSFullWindowOverlayCls}, // 4
+
+    {"RNSModalScreen", RNSModalScreenCls}, // 4
+
+    {"RNSScreenContainer", RNSScreenContainerCls}, // 4
+
+    {"RNSScreenContentWrapper", RNSScreenContentWrapperCls}, // 4
+
+    {"RNSScreenFooter", RNSScreenFooterCls}, // 4
+
+    {"RNSScreen", RNSScreenCls}, // 4
+
+    {"RNSScreenNavigationContainer", RNSScreenNavigationContainerCls}, // 4
+
+    {"RNSScreenStackHeaderConfig", RNSScreenStackHeaderConfigCls}, // 4
+
+    {"RNSScreenStackHeaderSubview", RNSScreenStackHeaderSubviewCls}, // 4
+
+    {"RNSScreenStack", RNSScreenStackCls}, // 4
+
+    {"RNSSearchBar", RNSSearchBarCls}, // 4
+#if !TARGET_OS_OSX && !TARGET_OS_TV && !TARGET_OS_VISION
+
+    {"RNCWebView", RNCWebViewCls}, // 5
+#endif
+
+    #endif
+    #endif
+  };
+
+  auto p = sFabricComponentsClassMap.find(name);
+  if (p != sFabricComponentsClassMap.end()) {
+    auto classFunc = p->second;
+    return classFunc();
+  }
+  return nil;
+}
diff --git a/node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec b/node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec
index 6680c60..23a930a 100644
--- a/node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec
+++ b/node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec
@@ -29,7 +29,7 @@ Pod::Spec.new do |spec|
   spec.license     = package['license']
   spec.author      = "Facebook"
   spec.source      = source
-  spec.platforms   = { :osx => "10.13", :ios => "15.1", :visionos => "1.0" }
+  spec.platforms   = { :osx => "10.13", :ios => "15.1" }
 
   spec.preserve_paths      = '**/*.*'
   spec.source_files        = ''
@@ -41,7 +41,6 @@ Pod::Spec.new do |spec|
 
   spec.ios.vendored_frameworks = "destroot/Library/Frameworks/ios/hermes.framework"
   spec.osx.vendored_frameworks = "destroot/Library/Frameworks/macosx/hermes.framework"
-  spec.visionos.vendored_frameworks = "destroot/Library/Frameworks/xros/hermes.framework"
 
   if HermesEngineSourceType::isPrebuilt(source_type) then
 
@@ -50,7 +49,6 @@ Pod::Spec.new do |spec|
       ss.source_files = "destroot/include/hermes/**/*.h"
       ss.header_mappings_dir = "destroot/include"
       ss.ios.vendored_frameworks = "destroot/Library/Frameworks/universal/hermes.xcframework"
-      ss.visionos.vendored_frameworks = "destroot/Library/Frameworks/universal/hermes.xcframework"
       ss.osx.vendored_frameworks = "destroot/Library/Frameworks/macosx/hermes.framework"
     end
 
