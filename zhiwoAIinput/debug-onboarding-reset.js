/**
 * 调试脚本：重置引导状态后的bug修复验证
 * 
 * 使用方法：
 * 1. 在应用开发模式下运行
 * 2. 在设置 -> 数据管理 -> 重置引导状态 后
 * 3. 在React Native调试控制台中查看输出
 */

// 模拟重置引导状态后的状态检查
const debugOnboardingReset = {
  
  /**
   * 检查重置引导状态后的关键状态
   */
  checkPostResetState: () => {
    console.log('=== 重置引导状态后状态检查 ===');
    
    // 检查AsyncStorage
    console.log('检查AsyncStorage状态...');
    
    // 检查Redux状态
    console.log('检查Redux录音状态...');
    
    // 检查全局变量
    console.log('检查全局变量状态...');
    
    return {
      timestamp: new Date().toISOString(),
      checked: true
    };
  },

  /**
   * 模拟极速模式API调用
   */
  simulateFastModeAPICall: () => {
    console.log('=== 模拟极速模式API调用 ===');
    
    console.log('检查AbortController状态...');
    console.log('检查请求ID状态...');
    console.log('检查API请求锁状态...');
    
    return {
      success: true,
      message: '模拟调用成功'
    };
  },

  /**
   * 跟踪状态跳转
   */
  trackStateTransition: (fromState, toState, trigger) => {
    console.log('=== 状态跳转跟踪 ===', {
      from: fromState,
      to: toState,
      trigger: trigger,
      timestamp: new Date().toISOString(),
      stackTrace: new Error().stack
    });
  },

  /**
   * 跟踪API调用
   */
  trackAPICall: (apiName, requestId, abortSignal) => {
    console.log('=== API调用跟踪 ===', {
      apiName,
      requestId,
      hasAbortSignal: !!abortSignal,
      isAborted: abortSignal?.aborted,
      timestamp: new Date().toISOString()
    });
    
    if (abortSignal) {
      abortSignal.addEventListener('abort', () => {
        console.log('=== API调用被取消 ===', {
          apiName,
          requestId,
          timestamp: new Date().toISOString()
        });
      });
    }
  },

  /**
   * 检查useEffect重复触发
   */
  checkEffectTriggers: () => {
    const triggerCount = window.debugEffectTriggerCount || 0;
    window.debugEffectTriggerCount = triggerCount + 1;
    
    console.log('=== useEffect触发计数 ===', {
      count: window.debugEffectTriggerCount,
      timestamp: new Date().toISOString()
    });
    
    if (window.debugEffectTriggerCount > 10) {
      console.warn('⚠️ useEffect触发次数过多，可能存在循环依赖');
    }
  },

  /**
   * 重置调试计数器
   */
  resetDebugCounters: () => {
    window.debugEffectTriggerCount = 0;
    console.log('调试计数器已重置');
  },

  /**
   * 检查状态锁定情况
   */
  checkStateLocks: (locks) => {
    console.log('=== 状态锁定检查 ===', {
      locks,
      timestamp: new Date().toISOString()
    });
    
    const hasActiveLocks = Object.values(locks).some(lock => lock);
    if (hasActiveLocks) {
      console.warn('⚠️ 检测到活跃的状态锁定');
    }
  },

  /**
   * 生成状态快照
   */
  createStateSnapshot: (state) => {
    const snapshot = {
      ...state,
      timestamp: new Date().toISOString(),
      hash: JSON.stringify(state).length // 简单的状态哈希
    };
    
    console.log('=== 状态快照 ===', snapshot);
    return snapshot;
  },

  /**
   * 比较状态快照
   */
  compareSnapshots: (snapshot1, snapshot2) => {
    const changed = snapshot1.hash !== snapshot2.hash;
    console.log('=== 状态快照比较 ===', {
      changed,
      snapshot1: snapshot1.hash,
      snapshot2: snapshot2.hash,
      timeDiff: new Date(snapshot2.timestamp).getTime() - new Date(snapshot1.timestamp).getTime()
    });
    
    return changed;
  }
};

// 导出调试工具
global.debugOnboardingReset = debugOnboardingReset;

// 添加全局错误监听
global.addEventListener?.('error', (error) => {
  console.error('=== 全局错误捕获 ===', {
    message: error.message,
    filename: error.filename,
    lineno: error.lineno,
    timestamp: new Date().toISOString()
  });
});

console.log('调试脚本已加载，可以通过 global.debugOnboardingReset 访问调试工具');

module.exports = debugOnboardingReset; 