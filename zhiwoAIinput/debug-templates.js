// 调试模板数据的简单脚本
console.log('=== 调试模板数据 ===');

// 检查模板服务
import('./services/templateService.js').then(templateService => {
  console.log('模板服务加载成功');
  
  templateService.getAllTemplates().then(templates => {
    console.log(`获取到 ${templates.length} 个模板:`);
    
    templates.slice(0, 6).forEach((template, index) => {
      console.log(`模板 ${index + 1}:`, {
        id: template.id,
        title: template.title,
        icon: template.icon,
        iconType: typeof template.icon,
        iconExists: !!template.icon,
        isDefault: template.isDefault,
        category: template.category
      });
    });
  }).catch(error => {
    console.error('获取模板失败:', error);
  });
}).catch(error => {
  console.error('加载模板服务失败:', error);
}); 