module.exports = function (api) {
  api.cache(true);
  return {
    presets: [
      [
        "babel-preset-expo",
        {
          jsxRuntime: "automatic",
          web: {
            unstable_transformProfile: "hermes",
          },
        },
      ],
    ],
    plugins: [
      // React Navigation配置
      "react-native-reanimated/plugin",

      // 模块解析器配置
      [
        "module-resolver",
        {
          root: ["./"],
          extensions: [".ios.js", ".android.js", ".js", ".ts", ".tsx", ".json"],
          alias: {
            "@": "./",
            "@components": "./components",
            "@screens": "./app",
            "@assets": "./assets",
            "@hooks": "./hooks",
            "@utils": "./utils",
            "@services": "./services",
            "@store": "./store",
            "@constants": "./constants",
            "@types": "./types",
          },
        },
      ],

      // 改进环境变量配置
      [
        "transform-inline-environment-variables",
        {
          include: ["EXPO_OS"],
        },
      ],

      // 注意：移除了 react-native-dotenv 插件以避免与 transform-inline-environment-variables 冲突
      // 如需使用 .env 文件，建议使用 Expo 的内置环境变量支持或其他方式
    ],
  };
};