# RevenueCat Webhook 配置指南

本文档提供如何在知我 AI 输入法应用中设置 RevenueCat Webhook 的完整指南，以确保订阅状态能够及时同步到后端。

## 背景

Webhook 是 RevenueCat 用来向我们的服务器发送订阅状态变更通知的机制。通过正确配置 Webhook，我们可以：

1. 及时获取订阅状态变更（购买、续订、取消、退款等）
2. 在服务端验证和更新用户的 VIP 状态
3. 记录详细的交易历史
4. 实现更准确的数据分析和报告

## 前提条件

1. 已设置好 RevenueCat 项目（参考 [revenuecat_setup.md](revenuecat_setup.md)）
2. 已有 Supabase 项目和访问权限
3. 已安装 Supabase CLI 工具

## 步骤 1: 设置 Supabase Edge Function

我们使用 Supabase Edge Function 来处理来自 RevenueCat 的 Webhook 请求。

### 1.1 安装 Supabase CLI

```bash
# 使用npm安装
npm install -g supabase

# 或使用yarn
yarn global add supabase
```

### 1.2 登录 Supabase

```bash
supabase login
```

按照提示创建访问令牌并完成登录。

### 1.3 链接项目

```bash
supabase link --project-ref your-project-ref
```

项目 ref 可以在 Supabase 控制台项目设置中找到。

### 1.4 部署 Edge Function

```bash
# 确保你在应用根目录下
cd /path/to/knowmeAIinput

# 部署函数，不需要JWT验证
supabase functions deploy revenuecat-webhook --no-verify-jwt
```

## 步骤 2: 在 Supabase 中设置环境变量

我们需要设置一些环境变量，以便 Edge Function 能够正确处理 Webhook：

```bash
# 设置RevenueCat Webhook签名密钥
supabase secrets set REVENUECAT_WEBHOOK_SECRET=your_webhook_signing_secret

# 设置服务角色密钥（必要时）
supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# 设置Supabase URL（通常自动配置）
supabase secrets set SUPABASE_URL=https://your-project-ref.supabase.co
```

## 步骤 3: 在 RevenueCat 中配置 Webhook

1. 登录 [RevenueCat 控制台](https://app.revenuecat.com/)
2. 选择你的项目
3. 进入 **项目设置 > 服务器 > Webhooks**
4. 点击 **添加 Webhook**
5. 填写以下信息：
   - **URL**: `https://your-project-ref.supabase.co/functions/v1/revenuecat-webhook`
   - **事件类型**: 选择所有事件（强烈建议）：
     * 初始购买 (INITIAL_PURCHASE)
     * 续订 (RENEWAL)
     * 产品更改 (PRODUCT_CHANGE)
     * 取消 (CANCELLATION)
     * 到期 (EXPIRATION)
     * 账单问题 (BILLING_ISSUE)
     * 取消恢复 (UNCANCELLATION)
   - **状态**: 启用
6. 保存配置
7. 复制生成的 **Webhook 签名密钥**，这就是上面用到的 `REVENUECAT_WEBHOOK_SECRET`

## 步骤 4: 应用数据库迁移

```bash
# 应用数据库迁移
supabase db push
```

这将创建/更新以下表：
- `profiles` 表（添加 VIP 状态相关字段）
- `subscriptions` 表（存储订阅记录）
- `billing_issues` 表（记录账单问题）

## 步骤 5: 测试 Webhook

1. 在 RevenueCat 控制台中，进入 **Webhooks** 页面
2. 找到刚刚创建的 Webhook，点击 **测试**
3. 选择一个事件类型，如 `INITIAL_PURCHASE`
4. 点击 **发送测试事件**
5. 在 Supabase 控制台的 **Edge Functions > Logs** 中查看日志，确认请求已被正确处理

## 故障排除

### Webhook 签名验证失败

- 确保 `REVENUECAT_WEBHOOK_SECRET` 环境变量设置正确
- 检查 RevenueCat 控制台中的 Webhook 密钥是否与环境变量匹配
- 确保请求未被修改（代理、防火墙等可能会修改请求）

### 数据库操作失败

- 检查 Supabase 权限设置，确保 Edge Function 有足够的权限
- 验证表结构是否正确
- 检查 RLS 策略是否正确配置

### 用户 VIP 状态未更新

- 确认 `app_user_id` 正确匹配我们系统中的用户 ID
- 检查 RevenueCat 事件是否包含正确的权益信息
- 确保 `profiles` 表中已存在用户记录

## 最佳实践

1. **健壮性处理**: 我们的 Webhook 处理器已设计为能够处理重复的事件
2. **日志记录**: 所有 Webhook 事件都会被记录，便于调试
3. **错误处理**: 即使在出错的情况下，也会尽可能继续处理
4. **安全验证**: 所有请求都会验证签名，防止伪造

## 生产环境注意事项

- 确保测试完成后才在生产环境部署
- 监控 Webhook 日志，确保正常工作
- 设置提醒，在 Webhook 失败时通知团队
- 考虑设置备份同步机制，以防 Webhook 失败 