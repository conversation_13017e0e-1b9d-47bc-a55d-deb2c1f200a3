# 录音文件保存和读取逻辑整体升级

## 概述

本次升级实现了录音文件的持久化存储和用户隔离功能，主要包括：

1. **SQLite数据库存储**：使用SQLite替代AsyncStorage存储历史记录
2. **用户隔离**：不同用户的录音文件和历史记录完全隔离
3. **路径动态修复**：重装应用后自动修复录音文件路径
4. **数据迁移**：用户登录状态变化时自动迁移数据
5. **持久化存储**：录音文件保存在documentDirectory，覆盖安装时保留

## 新增服务

### 1. SQLiteService (`services/sqliteService.ts`)

负责SQLite数据库的管理和操作：

- 按用户ID创建独立的数据库实例
- 管理历史记录表和录音文件记录表
- 提供CRUD操作接口
- 支持数据迁移功能

**主要方法：**
```typescript
// 获取用户数据库
await sqliteService.getDatabase(userId)

// 保存历史记录
await sqliteService.saveHistoryRecord(userId, record)

// 获取历史记录
await sqliteService.getHistoryRecords(userId, limit, offset)

// 迁移历史记录
await sqliteService.migrateHistoryRecords(fromUserId, toUserId)
```

### 2. UserIdService (`services/userIdService.ts`)

管理用户ID和登录状态：

- 生成和管理匿名用户ID
- 获取当前用户ID（登录用户或匿名用户）
- 处理用户登录/退出状态变化
- 提供用户状态查询接口

**主要方法：**
```typescript
// 初始化服务
await userIdService.initialize()

// 获取当前用户ID
const userId = await userIdService.getCurrentUserId()

// 检查是否为匿名用户
const isAnonymous = await userIdService.isAnonymousUser()

// 处理用户登录
await userIdService.handleUserLogin(userId)
```

### 3. RecordingFileService (`services/recordingFileService.ts`)

管理录音文件的存储和路径：

- 按用户ID隔离录音文件存储
- 生成用户专属的录音文件路径
- 实现路径解析和动态修复
- 支持录音文件迁移

**主要方法：**
```typescript
// 获取录音文件路径
const filePath = await recordingFileService.getRecordingFilePath()

// 修复录音路径
const fixedPath = await recordingFileService.fixRecordingPath(originalPath)

// 迁移录音文件
await recordingFileService.migrateRecordingFiles(fromUserId, toUserId)
```

### 4. NewHistoryService (`services/newHistoryService.ts`)

新的历史记录服务，使用SQLite存储：

- 替代原有的AsyncStorage存储方式
- 支持按用户ID隔离的历史记录管理
- 自动修复音频文件路径
- 提供向后兼容的接口

**主要方法：**
```typescript
// 保存历史记录
await newHistoryService.saveHistoryRecord(record)

// 获取历史记录
const records = await newHistoryService.getHistoryRecords()

// 迁移历史记录
await newHistoryService.migrateHistoryRecords(fromUserId, toUserId)
```

### 5. UserMigrationService (`services/userMigrationService.ts`)

处理用户状态变化时的数据迁移：

- 用户登录时从匿名用户迁移数据
- 用户退出时切换到匿名用户状态
- 提供数据清理和统计功能

**主要方法：**
```typescript
// 处理用户登录
await userMigrationService.handleUserLogin(userId)

// 处理用户退出
await userMigrationService.handleUserLogout()

// 获取用户数据统计
const stats = await userMigrationService.getUserDataStats(userId)
```

## 文件存储结构

```
DocumentDirectory/
└── recordings/
    ├── anonymous_xxx/          # 匿名用户录音文件
    │   ├── recording_abc.m4a
    │   └── recording_def.m4a
    ├── user_123/               # 登录用户录音文件
    │   ├── recording_ghi.m4a
    │   └── recording_jkl.m4a
    └── user_456/
        └── recording_mno.m4a
```

## 数据库结构

每个用户都有独立的SQLite数据库文件：`knowmetype.db.{userId}`

### 历史记录表 (history_records)
```sql
CREATE TABLE history_records (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  timestamp INTEGER NOT NULL,
  original_text TEXT NOT NULL,
  optimized_text TEXT NOT NULL,
  template_id TEXT NOT NULL,
  template_name TEXT NOT NULL,
  audio_uri TEXT,
  source_record_id TEXT,
  optimized_results TEXT,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);
```

### 录音文件记录表 (recording_files)
```sql
CREATE TABLE recording_files (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  file_path TEXT NOT NULL,
  relative_path TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER,
  duration INTEGER,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);
```

## 升级流程

### 1. 应用启动时
- 初始化用户ID服务
- 检查当前用户状态（匿名或已登录）
- 自动创建用户专属的录音目录

### 2. 用户登录时
- 触发数据迁移流程
- 将匿名用户的历史记录和录音文件迁移到登录用户
- 更新用户ID服务状态

### 3. 用户退出时
- 切换回匿名用户状态
- 保持数据隔离

### 4. 录音文件保存时
- 使用用户专属的文件路径
- 同时保存文件记录到数据库
- 自动修复路径格式

## 服务架构说明

### 新旧服务关系

1. **完全替代关系**：
   - `newHistoryService.ts` **完全替代** `historyService.ts`
   - `storageService.ts` 作为**兼容层**，内部路由到新服务
   - 现有代码无需修改，自动享受新功能

2. **服务分工**：
   - `historyService.ts` - 原有服务（保留但不再使用）
   - `newHistoryService.ts` - 新的SQLite历史记录服务（包含所有业务逻辑）
   - `storageService.ts` - 兼容层（代理到新服务）
   - `dataMigrationService.ts` - 数据迁移服务

### 向后兼容性

为了保持向后兼容，修改了现有的 `storageService.ts`：

- 添加了 `USE_SQLITE_SERVICE` 开关（默认启用）
- 保持原有接口不变，包括所有方法签名
- 内部自动路由到新的SQLite服务
- 包含完整的VIP权限控制逻辑
- 支持所有原有的分组、筛选、统计功能

### 数据迁移

新增了 `dataMigrationService.ts` 处理数据迁移：

- 自动检测AsyncStorage中的历史记录
- 一次性迁移到SQLite数据库
- 保留原始数据备份
- 迁移完成后清理原始数据

## 测试和验证

### 最终综合验证（推荐）

提供了最终综合验证脚本 (`services/finalValidation.ts`)：

```typescript
import { runFinalValidationWithReport } from '@/services/finalValidation';

// 运行最终综合验证并打印详细报告
const success = await runFinalValidationWithReport();
if (success) {
  console.log('🎉 升级完全成功！');
} else {
  console.log('⚠️ 发现问题，请检查报告。');
}
```

### 快速验证脚本

提供了快速验证脚本 (`services/upgradeValidation.ts`)：

```typescript
import { runValidationWithReport } from '@/services/upgradeValidation';

// 运行验证并打印详细报告
const success = await runValidationWithReport();
if (success) {
  console.log('升级验证成功！');
} else {
  console.log('升级验证失败，请检查报告。');
}
```

### 服务集成验证

提供了服务集成验证脚本 (`services/integrationValidation.ts`)：

```typescript
import { runIntegrationValidationWithReport } from '@/services/integrationValidation';

// 验证历史记录页面是否正确使用新服务
const success = await runIntegrationValidationWithReport();
```

### 录音保存流程测试

如果录音后历史记录不显示，使用录音保存测试脚本 (`services/recordingSaveTest.ts`)：

```typescript
import { runRecordingSaveTestWithReport, checkCurrentHistoryStatus } from '@/services/recordingSaveTest';

// 测试录音保存流程
const success = await runRecordingSaveTestWithReport();

// 检查当前历史记录状态
await checkCurrentHistoryStatus();
```

### 调试工具

提供了详细的调试脚本 (`services/debugRecordingFlow.ts`)：

```typescript
import { runFullDebug } from '@/services/debugRecordingFlow';

// 运行完整的调试流程
await runFullDebug();
```

### 完整测试套件

提供了完整的测试套件 (`services/recordingUpgradeTest.ts`)：

```typescript
import { runAllTests, getUpgradeStatusSummary } from '@/services/recordingUpgradeTest';

// 运行所有测试
await runAllTests();

// 获取升级状态
const status = await getUpgradeStatusSummary();
console.log('升级状态:', status);
```

## 使用示例

### 基本使用（无需修改现有代码）

```typescript
import { saveHistoryRecord, getHistoryRecords } from '@/services/storageService';

// 保存历史记录（自动使用SQLite）
await saveHistoryRecord({
  id: 'record_123',
  timestamp: Date.now(),
  originalText: '原始文本',
  optimizedText: '优化文本',
  templateId: 'template_1',
  templateName: '模板名称',
  audioUri: '/path/to/recording.m4a'
});

// 获取历史记录（自动从SQLite读取）
const records = await getHistoryRecords();
```

### 高级使用（直接使用新服务）

```typescript
import { newHistoryService } from '@/services/newHistoryService';
import { userIdService } from '@/services/userIdService';

// 获取当前用户ID
const userId = await userIdService.getCurrentUserId();

// 直接使用新的历史记录服务
const records = await newHistoryService.getHistoryRecords(userId);
```

## 注意事项

1. **数据迁移**：首次升级时会自动迁移现有的AsyncStorage数据到SQLite
2. **文件路径**：录音文件路径会自动修复，无需手动处理
3. **用户隔离**：不同用户的数据完全隔离，确保隐私安全
4. **性能优化**：SQLite提供更好的查询性能和数据管理能力
5. **错误处理**：所有服务都包含完善的错误处理和日志记录

## 故障排除

如果遇到问题，可以：

1. 查看日志输出，所有操作都有详细的日志记录
2. 运行测试套件验证功能是否正常
3. 检查用户ID服务的初始化状态
4. 验证SQLite数据库文件是否正确创建

## 未来扩展

这个架构为未来的功能扩展提供了良好的基础：

- 支持云端同步
- 支持数据备份和恢复
- 支持更复杂的用户权限管理
- 支持数据分析和统计
