# 录音文件保存和读取逻辑升级 - 故障排除指南

## 🚨 常见问题

### 问题1：WebAPI录音播放失败，文件名不匹配

**症状：** WebAPI模型（如whisper）录制的语音无法播放，提示文件不存在

**典型错误：**
```
音频文件不存在: .../recording-08DEE392-023E-4FD4-9F81-B7574D8B8CCD.m4a
```

**问题原因：** 文件移动时生成了新文件名，但历史记录保存的是原始文件名

**解决方法：**

```typescript
import { runFullFileNamePreservationDiagnosis } from '@/services/fileNamePreservationTest';

// 运行文件名保持诊断
await runFullFileNamePreservationDiagnosis();
```

### 问题2：WebAPI转写第一次失败，重试后成功

**症状：** 使用WebAPI语音转录模型时，第一次提交转录失败，重试后正常

**典型日志：**
```
[whisper-1模式] 使用录音时获取的URI: .../Library/Caches/AV/recording-xxx.m4a
ERROR [whisper-1模式] 检查文件路径失败: [Error: 无法找到有效的录音文件]
```

**问题原因：** 录音文件已移动到Documents目录，但转写时仍使用原始的缓存路径

**解决方法：**

```typescript
import { runFullTranscriptionUriDiagnosis } from '@/services/transcriptionUriTest';

// 运行转写URI诊断
await runFullTranscriptionUriDiagnosis();
```

### 问题2：录音文件保存到错误位置

**症状：** 录音文件被保存到 `Library/Caches/AV/` 目录而不是用户目录

**可能原因：**
- expo-av 使用默认的缓存目录
- 录音配置中的URI参数不被支持
- 文件移动逻辑失败

**解决方法：**

```typescript
import { runRecordingPathTestWithReport } from '@/services/recordingPathTest';

// 测试录音文件路径配置
await runRecordingPathTestWithReport();
```

### 问题2：录音后历史记录不显示

**症状：** 录音完成后，在历史记录页面看不到任何记录

**可能原因：**
1. 录音保存流程失败
2. 数据库初始化问题
3. 用户ID服务异常
4. 数据迁移未完成
5. UI组件获取数据失败

**解决步骤：**

#### 步骤1：检查录音文件路径

```typescript
import { runRecordingPathTestWithReport } from '@/services/recordingPathTest';

// 检查录音文件是否保存到正确位置
await runRecordingPathTestWithReport();
```

#### 步骤2：运行快速诊断

```typescript
import { checkCurrentHistoryStatus } from '@/services/recordingSaveTest';

// 检查当前历史记录状态
await checkCurrentHistoryStatus();
```

#### 步骤2：运行录音保存测试

```typescript
import { runRecordingSaveTestWithReport } from '@/services/recordingSaveTest';

// 测试录音保存流程
const success = await runRecordingSaveTestWithReport();
```

#### 步骤3：运行完整调试

```typescript
import { runFullDebug } from '@/services/debugRecordingFlow';

// 运行完整的调试流程
await runFullDebug();
```

#### 步骤4：运行最终验证

```typescript
import { runFinalValidationWithReport } from '@/services/finalValidation';

// 运行最终综合验证
const success = await runFinalValidationWithReport();
```

### 问题2：数据迁移失败

**症状：** 旧的历史记录没有迁移到新系统

**解决方法：**

```typescript
import { dataMigrationService } from '@/services/dataMigrationService';

// 检查迁移状态
const status = await dataMigrationService.getMigrationStatus();
console.log('迁移状态:', status);

// 强制重新迁移
if (!status.migrationCompleted) {
  const result = await dataMigrationService.forceMigration();
  console.log('强制迁移结果:', result);
}
```

### 问题3：录音URI不匹配

**症状：** 录音文件实际存在，但历史记录中保存的URI与实际文件名不匹配

**典型日志：**
```
找到有效文件路径: .../recording_eeCdmuFPVGHkm2DuvgFYS.m4a
RecordingFileService: 修复后的文件不存在 .../recording-0EC735B8-77DB-4CDB-B7BB-FA6DB389D470.m4a
```

**解决方法：**

```typescript
import { runFullRecordingUriDiagnosis } from '@/services/recordingUriTest';

// 运行完整的录音URI诊断和修复
await runFullRecordingUriDiagnosis();
```

### 问题4：路径修复逻辑错误修复正确路径

**症状：** 转写成功但历史记录播放失败，实际文件存在但路径不匹配

**典型日志：**
```
录音文件移动成功: .../recording_GCHA0S3YbRKuhks-6S-aJ.m4a
准备加载新音频: .../recording-37739559-26D2-47A0-90CF-C2E5FEED5E00.m4a
音频文件不存在
```

**问题原因：** 路径修复逻辑错误地"修复"了已经正确的路径

**解决方法：**

```typescript
import { runFullPathFixDiagnosis } from '@/services/pathFixTest';

// 运行路径修复诊断
await runFullPathFixDiagnosis();
```

### 问题5：二次优化记录缺失视觉标识

**症状：** 二次优化的历史记录看起来和普通记录一样，没有紫色边框和"重新优化"徽章

**问题原因：** storageService 转换记录时丢失了 sourceRecordId 字段

**解决方法：**

```typescript
import { runFullSourceRecordIdFixDiagnosis } from '@/services/sourceRecordIdFixTest';

// 运行 sourceRecordId 修复诊断
await runFullSourceRecordIdFixDiagnosis();
```

### 问题6：二次优化记录详情页显示错误

**症状：** 二次优化记录的详情页没有显示跳转到原始记录的链接，仍显示音频播放器

**问题原因：** 详情页检测逻辑要求同时满足"无音频URI"和"有sourceRecordId"，但二次优化记录可能意外包含音频URI

**解决方法：**

```typescript
import { runFullSecondaryOptimizationDetectionDiagnosis } from '@/services/secondaryOptimizationDetectionTest';

// 运行二次优化检测诊断
await runFullSecondaryOptimizationDetectionDiagnosis();
```

### 问题7：二次优化记录详情页调试

**症状：** 需要调试二次优化记录在详情页面的显示问题

**调试方法：**

```typescript
import { runFullDetailPageDiagnosis } from '@/services/detailPageDebugTest';

// 运行详情页面完整诊断
await runFullDetailPageDiagnosis();
```

**期望的正确显示：**
- 顶部显示"二次优化记录"标题
- 显示"查看原始记录"按钮
- 不显示音频播放控件
- 可以正常跳转到原始记录

### 问题8：覆盖安装后录音文件无法访问

**症状：** 覆盖安装应用后，数据库记录正常但录音文件无法播放

**典型日志：**
```
SQLiteService: 获取历史记录成功 (count: 5)  ✅ 数据库正常
RecordingFileService: 修复后的文件不存在 file:///.../recording-xxx.m4a  ❌ 文件无法访问
```

**问题原因：**
- 数据库记录保留，但录音文件路径需要重新映射
- 文件可能存在但路径不匹配
- 需要扫描文件系统并重建路径映射

**解决方法：**

```typescript
import { runFullPostInstallRecoveryDiagnosis } from '@/services/postInstallRecoveryTest';

// 运行覆盖安装后录音文件恢复诊断
await runFullPostInstallRecoveryDiagnosis();
```

**诊断功能：**
- 扫描整个录音目录结构
- 对比数据库记录与实际文件
- 制定文件恢复计划
- 自动修复可恢复的记录

### 问题9：录音文件无法播放

**症状：** 历史记录中的录音文件点击后无法播放，提示"音频文件不存在"

**可能原因：**
- 录音文件路径错误
- 文件被移动或删除
- 路径格式不正确
- URI不匹配（见问题3）
- 路径修复逻辑问题（见问题4）
- 覆盖安装导致路径失效（见问题8）

**解决方法：**

#### 方法1：运行录音文件路径诊断

```typescript
import { runFullAudioPathDiagnosis } from '@/services/audioPathTest';

// 运行完整的录音文件路径诊断和修复
await runFullAudioPathDiagnosis();
```

#### 方法2：手动修复单个文件路径

```typescript
import { recordingFileService } from '@/services/recordingFileService';

// 修复录音文件路径
const originalPath = '/old/path/recording.m4a';
const fixedPath = await recordingFileService.fixRecordingPath(originalPath);
console.log('修复后的路径:', fixedPath);
```

#### 方法3：批量修复所有路径

```typescript
import { fixAllAudioFilePaths } from '@/services/audioPathTest';

// 修复所有错误的录音文件路径
const result = await fixAllAudioFilePaths();
console.log(`修复成功: ${result.fixed} 个，失败: ${result.failed} 个`);
```

### 问题4：用户切换后数据混乱

**症状：** 用户登录/退出后看到错误的历史记录

**解决方法：**

```typescript
import { userMigrationService } from '@/services/userMigrationService';

// 处理用户登录
await userMigrationService.handleUserLogin('new_user_id');

// 处理用户退出
await userMigrationService.handleUserLogout();
```

## 🔧 调试工具使用指南

### 1. 快速状态检查

```typescript
import { quickStatusCheck } from '@/services/finalValidation';

// 快速检查所有关键服务状态
await quickStatusCheck();
```

### 2. 详细调试信息

```typescript
import { debugRecordingFlow } from '@/services/debugRecordingFlow';

// 获取详细的调试信息
await debugRecordingFlow();
```

### 3. 追踪保存调用链

```typescript
import { traceRecordingSaveCall } from '@/services/debugRecordingFlow';

// 追踪录音保存的完整调用链
await traceRecordingSaveCall();
```

### 4. 检查UI数据获取

```typescript
import { checkUIHistoryFetch } from '@/services/debugRecordingFlow';

// 检查UI组件的数据获取是否正常
await checkUIHistoryFetch();
```

## 📋 检查清单

在报告问题之前，请确认以下项目：

- [ ] 应用已完全重启
- [ ] 用户ID服务已初始化
- [ ] 数据迁移已完成
- [ ] SQLite数据库可以正常访问
- [ ] 录音权限已授予
- [ ] 网络连接正常（如果使用云端转写）

## 🔍 日志分析

查看控制台日志中的关键信息：

### 成功的录音保存日志示例：
```
✅ 用户ID服务正常 - 当前用户: user_123 (匿名: true)
💾 保存历史记录到SQLite数据库: record_456
✅ 录音文件记录已保存到数据库
📊 数据库中的记录数量: 5
```

### 失败的录音保存日志示例：
```
❌ 用户ID服务初始化失败: Error: ...
❌ SQLite数据库连接失败: Error: ...
❌ 保存历史记录失败: Error: ...
```

## 🆘 获取帮助

如果上述步骤都无法解决问题，请提供以下信息：

1. **运行环境信息：**
   - 设备类型（iOS/Android）
   - 应用版本
   - 系统版本

2. **错误日志：**
   - 控制台完整错误信息
   - 调试脚本的输出结果

3. **重现步骤：**
   - 详细的操作步骤
   - 预期结果 vs 实际结果

4. **验证结果：**
   ```typescript
   // 运行这个并提供输出结果
   import { runFinalValidationWithReport } from '@/services/finalValidation';
   await runFinalValidationWithReport();
   ```

## 🔄 重置和恢复

### 完全重置（谨慎使用）

如果所有方法都失败，可以考虑完全重置：

```typescript
import { newHistoryService } from '@/services/newHistoryService';
import { userIdService } from '@/services/userIdService';

// 警告：这将删除所有历史记录！
const userId = await userIdService.getCurrentUserId();
await newHistoryService.clearAllHistoryRecords(userId);

// 重新初始化
await userIdService.initialize();
```

### 数据备份

在重置之前，建议备份数据：

```typescript
import { newHistoryService } from '@/services/newHistoryService';
import { userIdService } from '@/services/userIdService';

// 导出所有历史记录
const userId = await userIdService.getCurrentUserId();
const allRecords = await newHistoryService.getHistoryRecords(userId, 1000, 0);
console.log('备份数据:', JSON.stringify(allRecords, null, 2));
```

## 📞 技术支持

如果问题仍然存在，请联系技术支持并提供：
- 完整的调试输出
- 设备和应用信息
- 详细的问题描述

记住：大多数问题都可以通过运行验证脚本来诊断和解决！
