# 软删除函数处理的表和字段总结

## 更新的软删除函数将处理以下表和字段：

### 1. `profiles` 表
- ✅ `is_deleted_by_user` → `true`
- ✅ `deleted_at` → 当前时间
- ✅ `email` → `deleted_时间戳_原邮箱` （避免唯一约束冲突）
- ✅ `username` → `deleted_时间戳_原用户名` （避免唯一约束冲突）

### 2. `subscriptions` 表  
- ✅ `is_deleted_by_user` → `true`
- ✅ `deleted_at` → 当前时间

### 3. `subscription_events` 表
- ✅ `is_deleted_by_user` → `true`  
- ✅ `deleted_at` → 当前时间

### 4. `user_identifiers` 表
- ✅ `user_id` → `deleted_时间戳_原user_id` （避免冲突）
- ✅ `anonymous_id` → `deleted_时间戳_原anonymous_id` （避免唯一约束冲突）
- ✅ `updated_at` → 当前时间

### 5. `auth.users` 表
- ✅ `email` → `deleted_时间戳_原邮箱` （释放邮箱供重新注册）
- ✅ `raw_user_meta_data` → 添加删除标记和原始数据备份

### 6. 审计日志
- ✅ `user_deletion_logs` → 记录删除操作
- ✅ `error_logs` → 记录异常（如果发生）

## 解决的问题：

1. **邮箱重复注册问题** - 修改 `profiles.email` 和 `auth.users.email`
2. **用户名重复问题** - 修改 `profiles.username` 
3. **用户标识符冲突** - 修改 `user_identifiers` 表中的 `user_id` 和 `anonymous_id`
4. **数据完整性** - 保留所有原始数据用于业务分析
5. **审计追踪** - 完整的删除操作日志记录

## 效果：
用户注销后可以立即使用相同的邮箱、用户名重新注册，同时保留所有历史数据用于业务分析。 