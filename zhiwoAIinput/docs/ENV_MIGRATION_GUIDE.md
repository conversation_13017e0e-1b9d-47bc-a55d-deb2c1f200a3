# 环境变量迁移指南

## 🎯 迁移目标

本指南将帮助你从旧的环境变量架构迁移到新的统一环境变量管理架构，解决以下问题：

- ✅ EAS构建时无法获取环境变量
- ✅ 客户端API密钥安全性问题
- ✅ 开发/测试/生产环境配置混乱
- ✅ 环境变量配置分散管理

## 🏗️ 新架构概览

### 核心设计原则

1. **环境分离**：开发、测试、生产环境独立配置
2. **安全优先**：API密钥统一在云端管理，客户端无硬编码
3. **统一管理**：通过脚本和服务统一管理环境变量
4. **构建兼容**：支持EAS云端构建和本地开发

### 架构对比

| 组件 | 旧架构 | 新架构 |
|------|--------|--------|
| 客户端配置 | `.env` (不被git跟踪) | `.env.development` (本地) + EAS环境变量 (云端) |
| AI API密钥 | 硬编码在客户端 | 统一在Supabase Edge Functions |
| 环境区分 | 混乱，难以区分 | 清晰的environment标识 |
| 构建支持 | EAS构建失败 | 完全支持EAS构建 |

## 📋 迁移步骤

### 步骤1: 备份现有配置

```bash
# 备份现有的.env文件
cp .env .env.backup

# 备份Supabase配置
cp supabase/.env.local supabase/.env.local.backup
```

### 步骤2: 设置新的环境配置

```bash
# 使用脚本初始化开发环境配置
./scripts/setup-env.sh init development

# 编辑开发环境配置文件
# 将原有.env文件的内容复制到.env.development
vim .env.development
```

### 步骤3: 配置Supabase边缘函数密钥

```bash
# 设置Supabase云端环境变量
./scripts/setup-env.sh setup-supabase
```

### 步骤4: 配置EAS环境变量

```bash
# 显示staging环境配置信息（推荐）
./scripts/setup-eas-env.sh staging

# 显示production环境配置信息  
./scripts/setup-eas-env.sh production

# 使用CLI命令设置（如果你想尝试）
./scripts/setup-eas-env.sh staging cli
```

然后在EAS控制台中设置环境变量：
https://expo.dev/accounts/[your-account]/projects/knowmetype/environment-variables

### 步骤5: 验证迁移结果

```bash
# 检查环境配置
./scripts/setup-env.sh check

# 本地开发测试
npm start

# EAS构建测试
eas build --profile staging
```

## 🔧 配置详解

### 1. 客户端环境变量

**开发环境** (`.env.development`)：
```bash
APP_ENV=development
IS_DEV_ENV=true
SHOW_DEV_LOGIN=true
SUPABASE_URL=https://yenwfmoubflrluhqsyim.supabase.co
SUPABASE_ANON_KEY=your-anon-key
REVENUE_CAT_IOS_API_KEY=your-ios-key
REVENUE_CAT_ANDROID_API_KEY=your-android-key
```

**EAS环境变量** (测试/生产环境)：
```bash
APP_ENV=staging  # 或 production
IS_DEV_ENV=false
SHOW_DEV_LOGIN=false
SUPABASE_URL=https://yenwfmoubflrluhqsyim.supabase.co
SUPABASE_ANON_KEY=your-anon-key
REVENUE_CAT_IOS_API_KEY=your-ios-key
REVENUE_CAT_ANDROID_API_KEY=your-android-key
```

### 2. Supabase边缘函数密钥

**云端Secrets**（通过`supabase secrets set`设置）：
```bash
SILICONFLOW_API_KEY=sk-your-key
OPENAI_API_KEY=sk-your-key
ANTHROPIC_API_KEY=sk-your-key
ELEVENLABS_API_KEY=your-key
ENVIRONMENT=production
```

**本地开发**（`supabase/.env.local`）：
```bash
SILICONFLOW_API_KEY=sk-your-key
OPENAI_API_KEY=sk-your-key
ENVIRONMENT=development
DEBUG=true
```

## 🚨 重要注意事项

### 安全性

1. **不要在客户端存储AI API密钥**
   - 所有AI调用通过Supabase边缘函数代理
   - 客户端只需要知道代理URL

2. **环境变量优先级**
   ```
   EAS环境变量 > .env.development > .env.local > .env > 默认值
   ```

3. **敏感信息保护**
   - API密钥只在Supabase云端存储
   - 本地开发文件不提交到git

### 开发流程

1. **本地开发**：
   ```bash
   # 使用.env.development配置
   npm start
   ```

2. **测试构建**：
   ```bash
   # 使用EAS staging环境变量
   eas build --profile staging
   ```

3. **生产发布**：
   ```bash
   # 使用EAS production环境变量
   eas build --profile production
   ```

## 🔄 常见问题解决

### Q: EAS构建时提示环境变量未定义？

**A**: 确保在EAS控制台中设置了所有必要的环境变量：
1. 访问 https://expo.dev/accounts/[your-account]/projects/knowmetype/environment-variables
2. 添加对应环境的变量
3. 重新构建

### Q: AI服务调用失败？

**A**: 检查Supabase边缘函数配置：
```bash
# 检查云端secrets
supabase secrets list

# 测试边缘函数
curl -X POST https://your-project.supabase.co/functions/v1/unified-proxy \
  -H "Authorization: Bearer your-anon-key" \
  -H "Content-Type: application/json" \
  -d '{"provider": "siliconflow", "message": "test"}'
```

### Q: 本地开发时配置不生效？

**A**: 检查配置文件加载顺序：
```bash
# 检查当前环境配置
./scripts/setup-env.sh check

# 确保使用正确的配置文件
ls -la .env*
```

## 📚 相关文档

- [环境配置服务 API](./services/envConfigService.ts)
- [Supabase环境变量管理](./supabase/ENV_MANAGEMENT_GUIDE.md)
- [EAS构建配置](./eas.json)

## 🎉 迁移完成检查清单

- [ ] 备份了原有配置文件
- [ ] 创建了`.env.development`配置文件
- [ ] 设置了Supabase云端secrets
- [ ] 配置了EAS环境变量
- [ ] 验证了本地开发环境
- [ ] 测试了EAS构建
- [ ] 删除了旧的配置文件（可选）

---

**🎯 迁移成功标志**：
- 本地开发正常运行
- EAS构建不再报错
- AI服务调用成功
- 不同环境配置正确区分