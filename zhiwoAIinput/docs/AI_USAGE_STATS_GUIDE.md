# AI 使用统计功能指南

## 🎯 功能概述

AI 使用统计页面提供了详细的 AI 服务使用情况分析，帮助用户了解自己的 AI 调用习惯和成本消耗。

## 🚪 如何访问

### 开发模式下访问
1. 打开应用
2. 进入 **设置** 标签页
3. 点击 **数据管理**
4. 在 **开发调试** 部分找到 **AI 使用统计**
5. 点击进入统计页面

> ⚠️ **注意**: 此功能仅在开发模式（`__DEV__ = true`）下可见

### 📱 路由路径
- **文件位置**: `app/ai-usage.tsx`
- **路由路径**: `/ai-usage`
- **导航方式**: `router.push('/ai-usage')`

## 📊 功能特性

### 今日统计仪表板
- **请求次数**: 当天的 AI 调用总数
- **总 Tokens**: 当天消耗的 Token 总量
- **估算成本**: 基于 SiliconFlow 定价的成本估算
- **平均每次**: 每次请求的平均 Token 消耗

### 使用记录列表
- **模型信息**: 显示使用的 AI 模型（如 Qwen2.5-7B、DeepSeek-V3）
- **时间戳**: 精确的请求时间
- **Token 详情**: 输入、输出和总 Token 数量
- **成本信息**: 每次请求的成本估算
- **VIP 标识**: 区分普通用户和 VIP 用户

### 使用提示
- 免费用户限制：每小时 10 次请求
- VIP 用户限制：每小时 100 次请求
- 成本估算基于 SiliconFlow 定价
- 所有请求都经过安全验证和加密

## 🔧 技术实现

### 路由配置
```typescript
// app/_layout.tsx
<Stack.Screen name="ai-usage" options={{ 
  headerShown: false,
  animation: 'slide_from_right',
  animationDuration: 200,
}} />

// 导航调用
router.push('/ai-usage')
```

### 数据来源
- 数据存储在 `unified_proxy_logs` 表中
- 只显示 AI 相关的记录（有模型信息的记录）
- 支持实时数据刷新

### API 服务
- `getUserAIUsageStats()`: 获取历史使用记录
- `getTodayUsageStats()`: 获取今日统计数据

### 安全特性
- 用户只能查看自己的使用记录
- 数据通过 RLS (Row Level Security) 保护
- 所有 API 调用都需要用户认证

## 🐛 故障排除

### 数据不显示
1. 确保用户已登录
2. 检查是否有 AI 调用记录
3. 确认数据库迁移已执行

### 页面无法访问
1. 确保在开发模式下运行
2. 检查路由配置是否正确
3. 清理缓存并重新启动应用

### 路由问题
- 确保 `app/_layout.tsx` 中包含 `ai-usage` 路由配置
- 确保导航路径为 `/ai-usage`
- 检查文件是否在正确位置：`app/ai-usage.tsx`

### 成本估算不准确
- 成本估算基于 SiliconFlow 的公开定价
- 实际成本可能因为优惠、套餐等因素有所不同
- 仅供参考，不作为实际计费依据

## 🔮 未来计划

- [ ] 添加图表可视化
- [ ] 支持时间范围筛选
- [ ] 添加成本预警功能
- [ ] 支持数据导出
- [ ] 添加使用趋势分析

## 📞 技术支持

如遇问题，请检查：
1. 开发环境配置
2. 数据库连接状态
3. 用户认证状态
4. 控制台错误日志
5. 路由配置是否正确 