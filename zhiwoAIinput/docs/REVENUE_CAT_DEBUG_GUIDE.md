# RevenueCat 配置调试指南

## 问题描述
使用 EAS 打包后，在客户端拉取不到订阅费用信息，而在开发环境测试正常。

## 问题原因
1. **环境变量配置问题**：`app.config.ts` 中 RevenueCat API 密钥只在 development 环境下读取
2. **EAS 配置不完整**：`production` 环境缺少 RevenueCat API 密钥配置

## 解决方案

### 1. 修复环境变量配置
已修复 `app.config.ts` 中的配置逻辑，现在所有环境都会读取 RevenueCat API 密钥。

### 2. 更新 EAS 配置
已在 `eas.json` 的 `production` 环境中添加必要的环境变量：
```json
{
  "production": {
    "env": {
      "REVENUE_CAT_IOS_API_KEY": "appl_tcbarojWSepMEauXlqTvOMAjzrT",
      "REVENUE_CAT_ANDROID_API_KEY": "goog_MKRccEeGhvIcEijaBubjgcaPYrn"
    }
  }
}
```

### 3. 修复价格显示逻辑
修复了 PaymentWall 组件中的价格显示问题：
- 当无法获取真实订阅价格时，显示 "--" 而不是默认价格
- 添加价格加载状态跟踪，确保用户了解当前状态
- 在价格未加载完成时禁用购买按钮，防止错误操作

## 使用 Development Build 调试

### 方案一：创建 Development Build (推荐)

```bash
# 1. 创建 development build
eas build --platform ios --profile development

# 2. 安装到设备
eas build:run --platform ios --latest

# 3. 或者使用 development-device 配置
eas build --platform ios --profile development-device
```

### 方案二：使用 Staging Build 测试

```bash
# 使用 staging 配置进行测试（已配置 RevenueCat 密钥）
eas build --platform ios --profile staging
```

## 调试工具使用

### 1. 在应用中检查配置
打开应用后，按以下步骤访问调试工具：
1. 进入"设置"页面
2. 点击"数据管理" 
3. 在"开发调试"部分（仅开发版本显示）找到"日志测试工具"
4. 点击"🔍 检查 RevenueCat 配置"按钮

### 2. 控制台输出
调试工具会在控制台输出详细的配置信息：
- 环境信息（平台、环境变量）
- Expo Config 配置状态
- Process Env 配置状态
- API 密钥获取结果
- 当前平台密钥状态
- 诊断建议

### 3. 手动检查
在调试版本中，可以手动添加以下代码进行检查：

```typescript
import { debugRevenueCatConfig } from '../debug-revenuecat-config';

// 在组件中调用
const checkConfig = () => {
  const result = debugRevenueCatConfig();
  console.log('配置检查结果:', result);
};
```

## 常见问题排查

### 1. API 密钥未配置
**症状**：调试工具显示"❌ 不可用"
**解决**：检查 `eas.json` 中对应环境的 `REVENUE_CAT_*_API_KEY` 配置

### 2. 环境变量未传递
**症状**：Constants.expoConfig.extra 中没有密钥
**解决**：检查 `app.config.ts` 中的 extra 配置是否正确

### 3. 平台密钥错误
**症状**：iOS 使用了 Android 密钥或反之
**解决**：确保在对应平台使用正确的 API 密钥

### 4. 构建配置错误
**症状**：使用了错误的构建配置
**解决**：确认使用正确的 EAS 构建 profile

## 验证步骤

### 1. 构建并测试
```bash
# 重新构建应用
eas build --platform ios --profile production

# 安装并测试
# 检查控制台日志中的 RevenueCat 初始化信息
```

### 2. 检查日志
在应用启动时，查看以下日志：
- `[PurchaseService] RevenueCat SDK初始化成功`
- `[PurchaseService] 同步购买状态`

### 3. 测试订阅功能
尝试：
- 获取可用的订阅包
- 检查价格显示是否正确（应显示具体价格或"--"）
- 进行订阅购买（只有在价格加载成功时才能操作）
- 检查 VIP 状态同步

### 4. 验证价格显示修复
在 Payment 页面中检查：
- 价格加载失败时应显示 "--/月" 而不是具体数字
- 按钮文本应显示"价格加载中..."而不是可操作状态
- 购买按钮应处于禁用状态，直到价格加载完成

## 调试命令参考

```bash
# 查看当前构建状态
eas build:list

# 取消正在进行的构建
eas build:cancel [BUILD_ID]

# 查看构建日志
eas build:view [BUILD_ID]

# 本地运行 development client
npx expo start --dev-client

# 清除构建缓存
eas build --clear-cache
```

## 注意事项

1. **密钥安全**：生产环境的 API 密钥应该通过 EAS 环境变量管理，不要直接写在代码中
2. **环境区分**：确保不同环境使用对应的 RevenueCat 项目和密钥
3. **缓存清理**：如果配置更改后问题仍存在，尝试清除构建缓存
4. **日志监控**：在生产环境中监控 RevenueCat 相关的错误日志

## 联系支持

如果问题仍然存在，请提供：
1. 调试工具的完整输出
2. 应用的构建日志
3. RevenueCat Dashboard 中的错误信息
4. 具体的错误复现步骤 