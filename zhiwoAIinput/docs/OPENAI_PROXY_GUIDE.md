# OpenAI API 代理配置指南

由于网络限制，在某些地区直接访问OpenAI API可能需要代理。本指南提供了多种解决方案。

## 方案1：本地VPN/代理配置（推荐用于开发）

### 步骤1：启动VPN软件
确保你的VPN软件已启动，常见软件包括：
- Clash (默认端口: 7890)
- V2Ray (默认端口: 10809)
- Shadowsocks (默认端口: 1080)
- <PERSON><PERSON> (默认端口: 8888)

### 步骤2：检测代理端口
运行检测脚本：
```bash
./check-proxy.sh
```

### 步骤3：配置环境变量
在 `.env.development` 文件中添加：
```bash
# 使用检测到的代理端口
HTTP_PROXY=http://127.0.0.1:7890
HTTPS_PROXY=http://127.0.0.1:7890
```

### 步骤4：启动代理服务
```bash
# 使用代理启动
HTTP_PROXY=http://127.0.0.1:7890 bash supabase/deploy-unified.sh local-test unified-proxy 8001
```

## 方案2：使用OpenAI API代理服务（推荐用于生产）

### 选项A：公共代理服务
在 `.env.development` 文件中配置：
```bash
# 使用代理服务（需要替换为实际的代理URL）
OPENAI_API_BASE_URL=https://your-proxy-service.com/v1
OPENAI_API_KEY=你的OpenAI密钥
```

### 选项B：自建Cloudflare Workers代理
创建一个Cloudflare Workers脚本：
```javascript
export default {
  async fetch(request, env) {
    const url = new URL(request.url);
    const targetUrl = `https://api.openai.com${url.pathname}${url.search}`;
    
    const modifiedRequest = new Request(targetUrl, {
      method: request.method,
      headers: request.headers,
      body: request.body
    });
    
    return fetch(modifiedRequest);
  }
}
```

然后使用Workers URL：
```bash
OPENAI_API_BASE_URL=https://your-worker.your-subdomain.workers.dev
```

## 方案3：使用国内AI服务替代

如果OpenAI访问困难，可以使用国内的AI服务：

### 通义千问（推荐）
```bash
# 已经在项目中配置，无需额外设置
SILICONFLOW_API_KEY=你的SiliconFlow密钥
```

### 其他选择
- 百度文心一言
- 腾讯混元
- 字节云雀
- 智谱GLM

## 测试代理配置

### 测试本地代理
```bash
# 测试代理是否能访问OpenAI
curl --proxy "http://127.0.0.1:7890" \
  "https://api.openai.com/v1/models" \
  -H "Authorization: Bearer your-api-key"
```

### 测试统一代理服务
```bash
curl -X POST "http://192.168.1.57:8001" \
  -H "Content-Type: application/json" \
  -d '{
    "service": "openai",
    "path": "/v1/chat/completions",
    "body": {
      "model": "gpt-3.5-turbo",
      "messages": [{"role": "user", "content": "Hello"}],
      "max_tokens": 10
    }
  }'
```

## 常见问题解决

### 1. VPN已开启但代理检测失败
```bash
# 手动检查VPN软件的代理设置
# Clash: 查看 Settings > General > HTTP Port
# V2Ray: 查看 Preferences > Advanced > Local HTTP
```

### 2. 代理连接超时
```bash
# 增加超时时间
export DENO_TLS_CA_STORE=system
export DENO_CERT=/path/to/ca-certificates.crt
```

### 3. SSL证书错误
某些代理可能会导致SSL证书验证失败，可以在测试环境中暂时跳过：
```bash
# 仅在开发环境使用
export NODE_TLS_REJECT_UNAUTHORIZED=0
```

## 生产环境建议

1. **使用Cloudflare Workers**: 部署自己的代理服务
2. **服务器部署**: 在海外服务器部署统一代理
3. **付费代理服务**: 使用稳定的商业代理服务
4. **国内AI服务**: 优先使用SiliconFlow等国内服务

## 安全注意事项

1. **API密钥保护**: 始终通过服务端代理访问，不要在客户端暴露API密钥
2. **代理选择**: 选择可信的代理服务，避免泄露敏感数据
3. **监控使用**: 定期检查API使用情况，防止滥用

## 自动配置脚本

运行以下命令进行自动配置：
```bash
# 检测并配置代理
./check-proxy.sh

# 启动带代理的统一服务
bash supabase/deploy-unified.sh local-test unified-proxy 8001
``` 