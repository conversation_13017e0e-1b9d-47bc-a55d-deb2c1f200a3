# RevenueCat 配置指南

本文档提供如何在知我 AI 输入法应用中设置 RevenueCat 服务的指南。

## 所需材料

1. 苹果开发者账号 (Apple Developer Program)
2. Google Play 开发者账号 (对 Android 平台)
3. RevenueCat 账号 (可在 [RevenueCat 官网](https://www.revenuecat.com/) 免费注册)

## 步骤 1: 创建 RevenueCat 项目

1. 登录 [RevenueCat 控制台](https://app.revenuecat.com/)
2. 点击 "Create New Project"
3. 输入项目名称 "知我AI输入法" 并选择移动应用
4. 配置应用平台:
   - iOS: `com.mindpowerhk.knowmetype`
   - Android: `com.mindpowerhk.knowmetype`

## 步骤 2: 创建产品和报价

1. 在项目中，前往 "Products" 标签
2. 点击 "Create Product"
3. 添加以下产品:

   **月度 VIP 订阅**
   - 产品 ID: `monthly_vip`
   - 类型: 订阅
   - 默认价格: ¥18.00/月

   **年度 VIP 订阅**
   - 产品 ID: `yearly_vip`
   - 类型: 订阅
   - 默认价格: ¥108.00/年

4. 前往 "Offerings" 标签创建一个报价
   - 名称: `standard`
   - 为该报价添加月度和年度 VIP 产品

## 步骤 3: 配置 App Store Connect（iOS）

1. 登录 [App Store Connect](https://appstoreconnect.apple.com/)
2. 导航至 "我的应用" > "知我AI输入法" > "应用内购买项目"
3. 创建与 RevenueCat 中相同的两个订阅产品，使用相同的产品 ID
4. 配置价格级别、订阅期限和续订信息
5. 提交审核

## 步骤 4: 配置 Google Play Console（Android）

1. 登录 [Google Play Console](https://play.google.com/console)
2. 导航至您的应用 > "Monetize" > "Products" > "Subscriptions"
3. 创建月度和年度订阅产品，与 RevenueCat 中使用相同的产品 ID
4. 设置价格和订阅条款

## 步骤 5: 配置 API 密钥

1. 在 RevenueCat 控制台项目设置中，获取 API 密钥
2. 将 API 密钥添加到项目根目录的 `.env` 文件中

```
REVENUE_CAT_IOS_API_KEY=your_ios_api_key_here
REVENUE_CAT_ANDROID_API_KEY=your_android_api_key_here
```

3. 确保 `app.config.ts` 文件已配置正确，应该已经包含如下代码：

```typescript
extra: {
  // ... 其他配置 ...
  
  // RevenueCat配置
  revenueCatIosApiKey: process.env.REVENUE_CAT_IOS_API_KEY || '',
  revenueCatAndroidApiKey: process.env.REVENUE_CAT_ANDROID_API_KEY || '',
  
  // ... 其他配置 ...
},
```

4. 如果需要重新构建应用，请运行：

```
expo prebuild --clean
```

## 步骤 6: 集成服务器端验证（可选但推荐）

如果您需要在服务器端验证订阅状态:

1. 在 RevenueCat 控制台项目设置中，获取服务器端 API 密钥
2. 在您的 Supabase 函数中使用这个密钥来验证用户的订阅

## 测试沙盒环境

### iOS 测试

1. 在 App Store Connect 创建沙盒测试账户
2. 在测试设备上注销 App Store 账户
3. 使用沙盒测试账户登录
4. 启动应用并进行测试购买

### Android 测试

1. 在 Google Play Console 中将应用添加到测试轨道
2. 将您的 Google 账户添加为测试者
3. 使用测试设备登录您的测试账户
4. 在测试设备上安装应用并进行测试购买

## 生产环境设置

在正式发布应用前:

1. 确保已完成所有测试
2. 确认 App Store 和 Google Play 中的产品已获得批准
3. 在 RevenueCat 控制台中切换到生产模式

## 故障排除

如果您遇到问题:

1. 检查 RevenueCat 日志以获取详细错误信息
2. 确保所有产品 ID 在 RevenueCat 和应用商店之间保持一致
3. 验证 API 密钥是否正确配置在 `.env` 文件中
4. 检查 `app.config.ts` 是否正确读取了环境变量
5. 检查设备是否能够进行应用内购买

如需帮助，可以参考 [RevenueCat 文档](https://docs.revenuecat.com/) 或联系 RevenueCat 支持。 