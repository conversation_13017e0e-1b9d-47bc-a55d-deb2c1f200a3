# 问卷模板选择与应用显示逻辑集成

## 概述

本文档说明了用户在onboarding问卷中选择的模板如何影响应用中模板的显示顺序和用户体验。

## 问题描述

之前的实现中存在以下问题：

1. **问卷选择与显示逻辑断层**：用户在问卷第四步选择的模板只是保存在`SELECTED_TEMPLATES`存储中，但没有被应用到模板显示逻辑
2. **跳过问卷的影响**：用户跳过问卷后，没有模板偏好设置，导致模板按默认顺序显示，未体现用户意图
3. **用户体验不一致**：问卷的目的是了解用户偏好，但选择的结果没有在应用中体现

## 解决方案

### 1. 新增模板偏好初始化功能

在 `onboardingService.ts` 中添加了 `initializeTemplatePreferences` 函数：

```typescript
export const initializeTemplatePreferences = async (): Promise<void> => {
  // 获取问卷选择的模板
  const selectedTemplates = await getSelectedTemplates();
  
  if (selectedTemplates.length === 0) {
    console.log('[OnboardingService] 没有选择的模板，跳过偏好设置初始化');
    return;
  }

  // 检查是否已有偏好设置（避免覆盖用户自定义设置）
  const currentPreferences = await getUserTemplatePreferences();
  if (currentPreferences.length > 0) {
    console.log('[OnboardingService] 已存在模板偏好设置，跳过初始化');
    return;
  }

  // 创建新的偏好设置，选中的模板排在前面
  const newPreferences = [];
  let displayOrder = 0;

  // 首先添加用户选择的模板，给予最高优先级
  for (const templateId of selectedTemplates) {
    const template = systemTemplates.find(t => t.id === templateId);
    if (template) {
      newPreferences.push({
        templateId,
        templateType: 'system' as const,
        isHidden: false,
        displayOrder: displayOrder++,
      });
    }
  }

  // 然后添加其他系统模板，排在选中模板之后
  for (const template of systemTemplates) {
    if (!selectedTemplates.includes(template.id)) {
      newPreferences.push({
        templateId: template.id,
        templateType: 'system' as const,
        isHidden: false,
        displayOrder: displayOrder++,
      });
    }
  }

  // 保存偏好设置
  await saveUserTemplatePreferences(newPreferences);
};
```

### 2. 集成到应用启动流程

在 `templateSyncService.ts` 的 `initializeTemplates` 函数中集成了偏好初始化：

```typescript
export const initializeTemplates = async (): Promise<void> => {
  try {
    console.log('[TemplateSync] 初始化模板数据...');
    
    // 同步模板数据
    const templates = await syncSystemTemplates();
    
    if (templates.length > 0) {
      console.log(`[TemplateSync] 模板数据初始化完成，共 ${templates.length} 个模板`);
      
      // 初始化用户模板偏好设置（基于问卷选择）
      try {
        const { initializeTemplatePreferences } = await import('./onboardingService');
        await initializeTemplatePreferences();
      } catch (preferenceError) {
        console.warn('[TemplateSync] 初始化模板偏好设置失败:', preferenceError);
      }
    }
  } catch (error) {
    console.error('[TemplateSync] 模板数据初始化失败:', error);
  }
};
```

### 3. 跳过问卷的处理

跳过问卷的用户不会有模板选择，因此不需要初始化偏好设置，模板将按默认顺序显示。

## 工作流程

### 完整问卷流程

1. **用户完成问卷**：在第四步选择偏好的模板
2. **保存选择**：调用 `saveSelectedTemplates(selectedTemplates)` 保存用户选择
3. **完成引导**：调用 `completeOnboarding(survey)` 完成引导流程
4. **应用启动**：在 `_layout.tsx` 中调用 `initializeTemplates()`
5. **偏好初始化**：在模板初始化后调用 `initializeTemplatePreferences()`
6. **设置偏好**：将选中的模板设置为高优先级显示顺序
7. **显示效果**：用户选择的模板在应用中排在前面

### 跳过问卷流程

1. **用户跳过问卷**：点击"跳过问卷"按钮
2. **直接完成引导**：调用 `completeOnboarding()` 不保存问卷数据
3. **应用启动**：同样调用 `initializeTemplates()`
4. **跳过偏好初始化**：由于没有选择的模板，跳过偏好设置初始化
5. **默认显示**：模板按系统默认顺序显示

## 技术细节

### 模板显示逻辑

模板的显示顺序由 `templatePreferenceService.ts` 中的偏好设置控制：

```typescript
// 应用用户偏好设置：自定义排序
const sortedTemplates = visibleTemplates.sort((a, b) => {
  const prefA = preferenceMap.get(a.id);
  const prefB = preferenceMap.get(b.id);
  
  // 有偏好设置的按displayOrder排序，没有的按原始顺序排在后面
  if (prefA && prefB) {
    return prefA.displayOrder - prefB.displayOrder;
  } else if (prefA && !prefB) {
    return -1; // 有偏好设置的排在前面
  } else if (!prefA && prefB) {
    return 1; // 有偏好设置的排在前面
  } else {
    return 0; // 都没有偏好设置，保持原序
  }
});
```

### 防止覆盖用户设置

为了避免覆盖用户的自定义偏好设置，初始化函数会检查是否已存在偏好设置：

```typescript
// 如果已有偏好设置，说明不是首次使用，不覆盖用户设置
if (currentPreferences.length > 0) {
  console.log('[OnboardingService] 已存在模板偏好设置，跳过初始化');
  return;
}
```

## 用户体验改进

### 之前的问题

- 用户精心选择的模板在应用中没有体现
- 问卷的价值没有得到充分利用
- 用户可能需要在模板管理页面重新排序

### 改进后的体验

- **有意义的问卷**：用户的选择直接影响应用体验
- **个性化显示**：选择的模板排在前面，便于访问
- **一致性体验**：问卷选择与应用显示保持一致
- **灵活性保持**：用户仍可在模板管理页面自定义排序

## 测试场景

### 场景1：完成问卷选择模板

1. 用户在问卷中选择模板：`["emoji", "friend", "email"]`
2. 完成引导后，应用中模板显示顺序应为：
   - Emoji风格（排序0）
   - 朋友聊天（排序1）
   - 邮件格式（排序2）
   - 其他模板（排序3+）

### 场景2：跳过问卷

1. 用户点击"跳过问卷"
2. 应用中模板按系统默认顺序显示
3. 用户可后续在模板管理页面自定义

### 场景3：已有偏好设置的用户

1. 用户已在模板管理页面设置了偏好
2. 再次重置应用不会覆盖现有偏好设置
3. 保护用户的个性化配置

## 注意事项

1. **首次使用检测**：只在首次使用且无偏好设置时初始化
2. **错误处理**：偏好设置初始化失败不影响应用启动
3. **性能考虑**：异步初始化，不阻塞主流程
4. **兼容性**：兼容现有的模板管理功能

## 结论

通过这次改进，我们成功地将问卷选择与应用显示逻辑连接起来，提升了用户体验的连贯性和个性化程度。用户在问卷中的选择现在能够真正影响应用的使用体验，让引导流程更有价值。 