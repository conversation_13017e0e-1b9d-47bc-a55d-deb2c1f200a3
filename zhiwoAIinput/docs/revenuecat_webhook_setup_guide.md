# RevenueCat Webhook 设置指南

本文档提供如何在知我 AI 输入法应用中设置 RevenueCat Webhook 的完整指南，确保订阅状态能够及时同步到 Supabase 后端。

## 已完成工作

1. 已集成 RevenueCat SDK 到 React Native 应用中
2. 已在 Supabase 中设置数据库表结构，包括：
   - `profiles` 表 - 包含 `is_vip` 字段
   - `subscriptions` 表 - 存储用户订阅信息
   - `subscription_plans` 表 - 存储订阅计划详情
   - `payments` 表 - 存储支付记录
   - `billing_issues` 表 - 存储账单问题记录

3. 已开发 RevenueCat Webhook 处理函数，支持以下事件：
   - `INITIAL_PURCHASE` - 首次购买
   - `RENEWAL` - 续订
   - `PRODUCT_CHANGE` - 产品更改
   - `CANCELLATION` - 取消订阅
   - `EXPIRATION` - 订阅到期
   - `BILLING_ISSUE` - 账单问题
   - `UNCANCELLATION` - 取消恢复

4. 已部署 Edge Function 到 Supabase 项目并设置 JWT 验证配置

## 接下来的步骤

1. **完成 Webhook 函数部署**：
   - 使用 Supabase 管理界面禁用 JWT 验证（因为 RevenueCat 的请求不会带有 JWT）
   - 或者使用 Supabase CLI 命令重新部署：`supabase functions deploy revenuecat-webhook --no-verify-jwt`

2. **设置环境变量**：
   - 在 Supabase 管理界面设置 `REVENUECAT_WEBHOOK_SECRET` 环境变量，值为从 RevenueCat 控制台获取的 Webhook 签名密钥

3. **配置 RevenueCat Webhook**：
   - 登录 RevenueCat 控制台，进入 Webhooks 设置
   - 添加新的 Webhook，URL 设置为：`https://[YOUR_PROJECT_REF].supabase.co/functions/v1/revenuecat-webhook`
   - 选择需要监听的事件类型（建议全选）
   - 保存 Webhook 设置

4. **测试 Webhook**：
   - 使用 RevenueCat 控制台的"测试 Webhook"功能发送测试事件
   - 检查 Supabase 日志确认 Edge Function 是否正确接收和处理事件
   - 验证数据库中的订阅状态是否正确更新

5. **监控和维护**：
   - 定期检查 Supabase 日志，确保 Webhook 处理正常
   - 监控 `billing_issues` 表，及时处理可能出现的账单问题

## 常见问题排查

- **401 未授权错误**：确保已禁用 Edge Function 的 JWT 验证
- **签名验证失败**：确保 `REVENUECAT_WEBHOOK_SECRET` 环境变量设置正确
- **数据库错误**：检查表结构是否与代码匹配，以及是否有相应的字段

## 参考资源

- [RevenueCat API 文档](https://www.revenuecat.com/docs/webhooks)
- [Supabase Edge Functions 文档](https://supabase.com/docs/guides/functions)
- [项目内部开发文档](../docs/revenuecat_setup.md)

最后更新日期：2025-04-28 