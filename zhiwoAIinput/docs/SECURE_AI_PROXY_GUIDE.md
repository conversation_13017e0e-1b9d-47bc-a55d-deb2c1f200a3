# 安全 AI 代理方案配置指南

## 🎯 方案概述

通过后台动态分配 API 密钥，而不是在前端硬编码，这是一个**极其优秀**的安全实践！

### ✅ 方案优势

1. **🔒 安全性**
   - API 密钥不会暴露在客户端代码中
   - 无法通过逆向工程获取 API 密钥
   - 可以随时撤销或更换 API 密钥

2. **⚙️ 管理性**
   - 动态控制 API 使用
   - 为不同用户分配不同限制
   - 基于 VIP 状态提供不同级别服务

3. **📊 监控性**
   - 追踪每个用户的 API 使用情况
   - 实现使用统计和成本控制
   - 检测异常使用模式

## 🚀 实现架构

```
客户端 App
    ↓ 用户身份验证
Supabase 边缘函数 (AI 代理)
    ↓ 安全的 API 密钥
SiliconFlow API
```

## 📝 实施步骤

### 1. 部署边缘函数

```bash
# 进入 supabase 目录
cd zhiwoAIinput/supabase

# 给部署脚本添加执行权限
chmod +x deploy-ai-proxy.sh

# 运行部署脚本
./deploy-ai-proxy.sh
```

### 2. 配置环境变量

创建或编辑 `.env` 文件：

```env
# SiliconFlow API 密钥 (必需)
SILICONFLOW_API_KEY=sk-your-actual-siliconflow-api-key-here

# OpenAI API 密钥 (可选)
OPENAI_API_KEY=sk-your-openai-api-key-here

# Supabase 项目 ID (可选)
SUPABASE_PROJECT_ID=your-project-id
```

### 3. 运行数据库迁移

```bash
# 在 supabase 目录下运行
supabase db push
```

### 4. 启用安全模式

在应用中，安全模式已自动启用（生产环境）。你可以通过修改 `services/aiService.ts` 中的 `USE_SECURE_MODE` 配置来控制：

```typescript
// 生产环境使用安全模式，开发环境可选择
const USE_SECURE_MODE = Constants.expoConfig?.extra?.isDevEnv !== 'true';
```

## 🔧 功能特性

### 速率限制

- **免费用户**: 10次/小时，50次/天，最大2000 tokens/请求
- **VIP用户**: 100次/小时，1000次/天，最大4000 tokens/请求

### 模型权限控制

- **免费用户**: 只能使用 `Qwen/Qwen2.5-7B-Instruct`
- **VIP用户**: 可以使用所有模型，包括 `deepseek-ai/DeepSeek-V3`

### 使用监控

- 实时记录所有 API 调用
- 统计 tokens 使用情况
- 估算成本（基于 SiliconFlow 定价）
- 用户可查看个人使用统计

## 📱 客户端使用

### 查看使用统计

用户可以通过以下路径查看 AI 使用统计：

```typescript
// 导航到使用统计页面
router.push('/profile/ai-usage');
```

### API 调用

应用会自动选择安全模式或传统模式：

```typescript
// 自动使用安全模式（生产环境）
const result = await optimizeText(text, templatePrompt, model);
```

## 🛡️ 安全特性

### 身份验证

- 所有请求都需要有效的 Supabase 用户会话
- 自动验证用户身份和权限

### 数据隔离

- 每个用户只能查看自己的使用统计
- RLS (Row Level Security) 策略保护数据安全

### 错误处理

- 详细的错误信息和状态码
- 友好的用户提示信息
- 完整的错误日志记录

## 📊 监控和维护

### 查看边缘函数日志

```bash
# 查看实时日志
supabase functions logs ai-proxy

# 查看特定时间范围的日志
supabase functions logs ai-proxy --from="2024-12-29 00:00:00" --to="2024-12-29 23:59:59"
```

### 更新 API 密钥

```bash
# 更新 SiliconFlow API 密钥
supabase secrets set SILICONFLOW_API_KEY="new-api-key-here"

# 重新部署边缘函数
supabase functions deploy ai-proxy
```

### 成本监控

你可以通过以下 SQL 查询监控成本：

```sql
-- 查看今日总成本
SELECT 
  SUM(cost_estimate) as total_cost,
  COUNT(*) as total_requests,
  SUM(total_tokens) as total_tokens
FROM ai_usage_logs 
WHERE DATE(request_timestamp) = CURRENT_DATE;

-- 查看用户使用排行
SELECT 
  user_id,
  COUNT(*) as requests,
  SUM(total_tokens) as tokens,
  SUM(cost_estimate) as cost
FROM ai_usage_logs 
WHERE DATE(request_timestamp) = CURRENT_DATE
GROUP BY user_id 
ORDER BY cost DESC 
LIMIT 10;
```

## 🔄 切换模式

### 强制使用传统模式（仅开发）

如果需要在开发环境中使用传统模式：

```typescript
// 在 services/aiService.ts 中修改
const USE_SECURE_MODE = false; // 强制使用传统模式
```

### 强制使用安全模式

```typescript
// 在 services/aiService.ts 中修改
const USE_SECURE_MODE = true; // 强制使用安全模式
```

## 🚨 故障排除

### 常见问题

1. **"处理失败，无法完成文本优化"**
   - 检查边缘函数是否已部署
   - 确认 API 密钥已正确设置
   - 查看边缘函数日志

2. **"用户未登录，无法调用 AI 服务"**
   - 确保用户已登录
   - 检查 Supabase 会话是否有效

3. **"API 调用频率超限"**
   - 用户达到速率限制
   - 提示用户稍后再试或升级 VIP

### 调试步骤

1. 检查边缘函数状态：
   ```bash
   supabase functions list
   ```

2. 查看实时日志：
   ```bash
   supabase functions logs ai-proxy --follow
   ```

3. 测试边缘函数：
   ```bash
   curl -X POST "https://your-project.supabase.co/functions/v1/ai-proxy" \
     -H "Authorization: Bearer your-access-token" \
     -H "Content-Type: application/json" \
     -d '{"model":"Qwen/Qwen2.5-7B-Instruct","messages":[{"role":"user","content":"Hello"}]}'
   ```

## 📈 扩展功能

### 多 API 密钥负载均衡

你可以在边缘函数中实现多个 API 密钥的负载均衡：

```typescript
// 在边缘函数中添加
const API_KEYS = [
  Deno.env.get('SILICONFLOW_API_KEY_1'),
  Deno.env.get('SILICONFLOW_API_KEY_2'),
  Deno.env.get('SILICONFLOW_API_KEY_3'),
];

// 随机选择或负载均衡选择
const apiKey = API_KEYS[Math.floor(Math.random() * API_KEYS.length)];
```

### 高级缓存

实现响应缓存以减少 API 调用：

```typescript
// 在边缘函数中添加缓存逻辑
const cacheKey = `ai_cache:${hashInput}`;
const cachedResponse = await getCachedResponse(cacheKey);
if (cachedResponse) {
  return cachedResponse;
}
```

## ✅ 验证清单

- [ ] 边缘函数已成功部署
- [ ] API 密钥已安全设置
- [ ] 数据库表已创建
- [ ] 安全模式已启用
- [ ] 用户可以正常使用 AI 功能
- [ ] 使用统计页面正常显示
- [ ] 速率限制正常工作
- [ ] VIP 权限控制有效

## 🎉 总结

这个安全 AI 代理方案为你的应用提供了：

- **企业级安全性**: API 密钥完全隐藏
- **精细化控制**: 基于用户身份的权限和限制
- **完整监控**: 详细的使用统计和成本控制
- **高可用性**: 边缘函数的全球分布和高性能
- **易于维护**: 集中化的 API 密钥管理

这是一个**生产就绪**的解决方案，远优于在客户端硬编码 API 密钥的传统做法！

---

**恭喜！🎊 你已经成功实现了一个安全、可扩展、易监控的 AI 服务代理系统！** 