# 孤立订阅事件解决方案

## 问题描述

当数据库重启或用户数据被清空后，RevenueCat仍然会发送订阅事件，但系统中找不到对应的用户记录。之前的处理方式是自动创建匿名用户记录，这会导致：

1. 创建虚假的用户数据
2. 用户数据来源不一致  
3. profiles表中出现无效的用户记录

## 解决方案概述

通过以下方式解决这个问题：

1. **数据库迁移**: 修改表结构支持孤立事件记录
2. **边缘函数修改**: 添加用户存在性检查，避免自动创建用户
3. **事件记录**: 为不存在的用户记录孤立订阅事件

## 实施的修改

### 1. 数据库迁移 (20250719000000_allow_orphaned_subscription_events.sql)

- 为 `subscription_events` 和 `subscriptions` 表添加 `app_user_id` 字段
- 将 `user_id` 字段改为可空
- 创建新的数据库函数：
  - `check_user_exists()`: 检查用户是否存在（不自动创建）
  - `record_orphaned_subscription_event()`: 记录孤立事件
  - `link_orphaned_records_to_user()`: 关联孤立记录到用户
- 创建视图和统计功能

### 2. 边缘函数修改

#### 新增函数

```typescript
// 检查用户是否存在（不自动创建）
async function checkUserExists(userId: string): Promise<string | null>

// 记录孤立订阅事件
async function recordOrphanedEvent(...)

// 处理用户存在性检查的通用函数
async function handleUserExistenceForEvent(...)
```

#### 修改的事件处理函数

- `handleSubscriptionActive()`: 订阅激活事件
- `handleTrialStarted()`: 试用开始事件  
- `handleTrialConversion()`: 试用转付费事件
- `handleTemporaryEntitlementGrant()`: 临时授权事件
- `handleBillingIssue()`: 账单问题事件

#### 处理逻辑

```typescript
// 新的处理流程
const userCheck = await handleUserExistenceForEvent(
  appUserId, 
  eventType, 
  eventData, 
  true // 跳过用户创建
);

if (!userCheck.shouldContinue) {
  // 用户不存在，已记录孤立事件，停止处理
  return;
}

// 用户存在，继续正常处理
```

## 使用方法

### 1. 运行数据库迁移

```bash
# 在Supabase Dashboard中运行迁移文件
# 或使用Supabase CLI
supabase db push
```

### 2. 部署边缘函数

```bash
# 部署修改后的RevenueCat webhook函数
supabase functions deploy revenuecat-webhook
```

### 3. 监控孤立事件

```sql
-- 查看孤立事件统计
SELECT * FROM public.orphaned_records_stats;

-- 查看具体的孤立事件
SELECT * FROM public.orphaned_subscription_events;

-- 查看匹配建议
SELECT * FROM public.suggest_user_matches_for_orphaned_records();
```

### 4. 处理孤立记录

```sql
-- 手动关联孤立记录到用户
SELECT public.link_orphaned_records_to_user(
  '6aaa2bc0-a24b-4bd8-92eb-081bb3fec46b',  -- app_user_id
  'real-user-uuid-here'                     -- actual user_id
);

-- 自动处理高置信度匹配
SELECT * FROM public.auto_link_high_confidence_matches(95);
```

## 测试

使用提供的测试脚本验证功能：

```bash
# 运行测试
node test-orphaned-events.js

# 只测试webhook
node test-orphaned-events.js --test-webhook

# 只测试数据库查询
node test-orphaned-events.js --test-queries
```

## 预期行为

### 用户存在时
- 正常处理订阅事件
- 更新用户VIP状态
- 记录订阅和支付信息

### 用户不存在时
- 记录孤立订阅事件到 `subscription_events` 表
- 不创建虚假用户记录
- 返回成功响应但标记为未处理
- 保留完整的事件信息用于后续匹配

## 数据完整性

1. **约束保证**: 每条记录至少有一个用户标识（user_id或app_user_id）
2. **索引优化**: 为app_user_id字段创建索引提高查询性能
3. **RLS策略**: 更新行级安全策略支持孤立记录访问控制

## 运维建议

1. **定期监控**: 检查孤立记录数量，及时发现问题
2. **匹配处理**: 定期运行匹配建议，处理可关联的记录
3. **数据备份**: 批量操作前确保数据已备份
4. **日志记录**: 边缘函数记录详细日志便于排查

## 优势

1. **数据一致性**: 保证用户数据创建来源的唯一性
2. **事件完整性**: 不丢失任何订阅事件信息
3. **可追溯性**: 完整保留原始RevenueCat数据
4. **可恢复性**: 支持后续用户匹配和数据关联
5. **向后兼容**: 不影响现有的正常用户处理流程

## 注意事项

1. 孤立记录不会影响正常的订阅功能
2. 用户重新注册后可通过匹配功能关联历史记录
3. 孤立记录保留完整事件信息不会丢失数据
4. 系统保证用户数据创建来源的唯一性
