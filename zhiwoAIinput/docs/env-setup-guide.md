# 环境变量设置指南

## 问题说明

从日志可以看到代理调用返回503错误：
```
代理调用失败: 503
代理服务调用失败，尝试直接调用
```

这是因为 `unified-proxy` 边缘函数没有正确加载API密钥，导致无法调用外部API服务。

## 解决方案

### 1. 创建 .env 文件

在项目根目录 `zhiwoAIinput/` 下创建 `.env` 文件：

```bash
# 进入项目根目录
cd zhiwoAIinput

# 创建 .env 文件
touch .env
```

### 2. 配置 .env 文件内容

在 `.env` 文件中添加以下内容：

```bash
# 知我AI输入法环境变量配置

# Supabase配置
SUPABASE_URL=https://yenwfmoubflrluhqsyim.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InllbndmbW91YmZscmx1aHFzeWltIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MjM2NDMsImV4cCI6MjA1ODk5OTY0M30.P9t2dkOhfgPnt53WpDB2gQ6OLWJNvfPNFMqmihmTzXQ

# SiliconFlow API密钥（必填 - 用于AI文本优化）
SILICONFLOW_API_KEY=你的实际SiliconFlow API密钥

# OpenAI API密钥（可选 - 用于语音转文字）
OPENAI_API_KEY=你的实际OpenAI API密钥

# RevenueCat配置
REVENUE_CAT_IOS_API_KEY=你的iOS RevenueCat密钥
REVENUE_CAT_ANDROID_API_KEY=你的Android RevenueCat密钥

# 开发环境配置
USE_LOCAL_SUPABASE=false
LOCAL_IP=*************
LOCAL_SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
IS_DEV_ENV=true
SHOW_DEV_LOGIN=false

# 应用商店配置
IOS_APP_ID=6744967748
ANDROID_PACKAGE_NAME=com.mindpowerhk.knowmetype

# RevenueCat Webhook密钥（用于测试）
REVENUECAT_WEBHOOK_SECRET=test_webhook_secret
```

### 3. 重要提醒

**必须填写真实的API密钥：**

- `SILICONFLOW_API_KEY`: 从 [SiliconFlow](https://siliconflow.cn) 获取
- `OPENAI_API_KEY`: 从 [OpenAI](https://platform.openai.com) 获取

**不要提交 .env 文件到Git：**
- `.env` 文件已经在 `.gitignore` 中被忽略
- 这是为了保护你的API密钥安全

### 4. 验证配置

重新启动代理服务：

```bash
cd zhiwoAIinput/supabase
bash direct-run.sh unified-proxy 8001
```

如果配置正确，你应该看到：
```
加载 ../.env 文件中的环境变量...
将运行函数: unified-proxy 在端口: 8001
```

如果配置有问题，会看到警告：
```
警告: 未找到 .env 文件或API密钥未设置
请在项目根目录创建 .env 文件并设置以下变量:
SILICONFLOW_API_KEY=你的SiliconFlow密钥
OPENAI_API_KEY=你的OpenAI密钥
```

### 5. 测试代理服务

启动代理服务后，在应用中测试AI功能，日志应该显示：

```
尝试通过代理调用 siliconflow API...
代理调用成功: siliconflow
AI服务调用通过代理完成
```

而不是：
```
代理调用失败: 503
回退到直接调用
```

## 文件结构

```
zhiwoAIinput/
├── .env                    # 环境变量文件（你需要创建）
├── app.config.ts          # 应用配置（会读取.env）
├── supabase/
│   ├── direct-run.sh      # 本地运行脚本（会加载.env）
│   ├── direct-run-public.sh # 公网运行脚本（会加载.env）
│   └── functions/
│       └── unified-proxy/ # 统一代理函数
└── services/
    ├── unifiedProxyService.ts # 代理客户端
    ├── aiService.ts          # AI服务（使用代理）
    └── openaiSpeechService.ts # 语音服务（使用代理）
```

## 故障排除

### 问题1：仍然显示503错误
- 检查 `.env` 文件是否在正确位置（项目根目录）
- 检查API密钥是否正确填写
- 重启代理服务

### 问题2：找不到 .env 文件
- 确保文件名是 `.env`（注意前面的点）
- 确保文件在 `zhiwoAIinput/` 目录下
- 检查文件权限

### 问题3：API密钥无效
- 验证SiliconFlow API密钥是否有效
- 验证OpenAI API密钥是否有效
- 检查API密钥是否有足够的配额

### 问题4：代理服务启动失败
- 检查端口是否被占用
- 查看完整的错误日志
- 尝试使用不同的端口

## 安全注意事项

1. **永远不要提交 .env 文件到版本控制**
2. **定期轮换API密钥**
3. **限制API密钥的权限范围**
4. **监控API使用量和费用**

配置完成后，统一代理服务应该能正常工作，所有API调用都会通过代理进行，提供更好的管理和监控能力。 