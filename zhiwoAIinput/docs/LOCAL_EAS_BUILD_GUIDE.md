# 本地EAS构建指南

## 🎯 概述

现在你已经配置了完整的环境变量架构，可以支持本地EAS构建，同时根据不同的构建环境自动加载正确的配置。

## 📁 环境文件结构

```
zhiwoAIinput/
├── .env.development    # 开发环境 (本地Supabase)
├── .env.staging        # 测试环境 (云端Supabase)
├── .env.production     # 生产环境 (云端Supabase)
├── env-config.template # 配置模板文件
└── supabase/
    └── .env.local      # 边缘函数本地开发配置
```

## 🚀 本地EAS构建命令

### 开发环境构建
```bash
# 使用本地Supabase数据库
APP_ENV=development eas build --profile development --local
```

### 测试环境构建
```bash
# 使用云端Supabase数据库
APP_ENV=staging eas build --profile staging --local
```

### 生产环境构建
```bash
# 使用云端Supabase数据库
APP_ENV=production eas build --profile production --local
```

## 🔧 环境变量自动加载机制

### 加载优先级
1. **APP_ENV环境变量** (最高优先级)
2. **对应的.env文件** (如 .env.staging)
3. **默认值** (最低优先级)

### 配置示例

#### 开发环境 (.env.development)
- ✅ `USE_LOCAL_SUPABASE=true` - 使用本地数据库
- ✅ `SUPABASE_URL=http://127.0.0.1:54321` - 本地地址
- ✅ `IS_DEV_ENV=true` - 开发模式

#### 测试/生产环境 (.env.staging/.env.production)
- ✅ `USE_LOCAL_SUPABASE=false` - 使用云端数据库
- ✅ `SUPABASE_URL=https://yenwfmoubflrluhqsyim.supabase.co` - 云端地址
- ✅ `IS_DEV_ENV=false` - 生产模式

## 📱 构建配置详解

### EAS配置文件 (eas.json)
```json
{
  "build": {
    "development": {
      "env": { "APP_ENV": "development" }
    },
    "staging": {
      "env": { "APP_ENV": "staging" }
    },
    "production": {
      "env": { "APP_ENV": "production" }
    }
  }
}
```

## 🔐 安全性考虑

### 客户端配置 (安全)
- ✅ **Supabase配置** - 可以安全存储
- ✅ **RevenueCat密钥** - 可以安全存储
- ✅ **应用商店配置** - 可以安全存储

### 云端配置 (敏感)
- 🚫 **AI服务密钥** - 仅在Supabase云端存储
- 🚫 **第三方API密钥** - 通过边缘函数代理

## 📝 使用流程

### 1. 本地开发
```bash
# 启动本地Supabase
cd supabase && supabase start

# 开发环境打包测试
APP_ENV=development eas build --profile development --local
```

### 2. 测试部署
```bash
# 确保Supabase边缘函数已部署
cd supabase && ./deploy-cloud.sh -e staging

# 测试环境打包
APP_ENV=staging eas build --profile staging --local
```

### 3. 生产发布
```bash
# 生产环境打包
APP_ENV=production eas build --profile production --local
```

## 🛠️ 故障排查

### 问题1: 环境变量未加载
**解决方案**:
```bash
# 检查环境文件状态
./scripts/setup-env.sh check

# 验证APP_ENV设置
echo $APP_ENV
```

### 问题2: Supabase连接错误
**开发环境**:
```bash
# 确保本地Supabase运行
cd supabase && supabase status
```

**测试/生产环境**:
```bash
# 检查云端Supabase配置
curl -I https://yenwfmoubflrluhqsyim.supabase.co
```

### 问题3: AI服务调用失败
**解决方案**:
```bash
# 检查边缘函数部署
cd supabase && supabase functions list

# 测试统一代理
curl -X POST https://yenwfmoubflrluhqsyim.supabase.co/functions/v1/unified-proxy \
  -H "Authorization: Bearer [anon-key]" \
  -H "Content-Type: application/json" \
  -d '{"provider": "siliconflow", "message": "test"}'
```

## 🎉 验证构建成功

### 1. 检查构建日志
确保看到正确的环境配置输出：
```
🔧 当前环境: staging
🗄️ Supabase模式: 云端数据库
🔗 Supabase URL: https://yenwfmoubflrluhqsyim.supabase.co
```

### 2. 测试应用功能
- [ ] 用户登录/注册
- [ ] AI文本优化功能
- [ ] 应用内购买
- [ ] 语音输入

### 3. 监控日志
查看应用启动时的环境配置日志，确认配置正确加载。

## 📚 相关文档

- [环境变量迁移指南](./ENV_MIGRATION_GUIDE.md)
- [环境配置服务API](./services/envConfigService.ts)
- [EAS构建配置](./eas.json)
- [Supabase环境变量管理](./supabase/ENV_MANAGEMENT_GUIDE.md) 