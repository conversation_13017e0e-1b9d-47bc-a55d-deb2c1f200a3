# 评价功能说明

## 功能概述

知我AI输入法应用现已集成评价功能，允许用户对应用进行评分和评价。该功能遵循Apple和Google的官方指南，提供最佳的用户体验。

## 功能特性

### 1. 系统级应用内评分
- 使用官方 `expo-store-review` 库
- 遵循 iOS `SKStoreReviewController` 和 Android `ReviewManager` API
- 系统级评分弹窗，无需离开应用
- 自动限制评分请求频率，避免打扰用户

### 2. 应用商店评分页面
- 支持直接跳转到App Store和Google Play的评分页面
- 智能回退机制：如果应用内评分不可用，自动跳转到应用商店
- 支持深度链接和浏览器回退

### 3. 多语言支持
- 简体中文
- 繁体中文
- 英文

## 技术实现

### 核心文件

1. **评价服务** (`services/ratingService.ts`)
   - `RatingService.isInAppReviewAvailable()` - 检查应用内评分可用性
   - `RatingService.requestInAppReview()` - 请求应用内评分
   - `RatingService.openStoreForReview()` - 打开应用商店评分页面
   - `RatingService.smartRatingRequest()` - 智能评分请求

2. **设置页面集成** (`app/(tabs)/settings/index.tsx`)
   - 在设置页面添加"评价我们"选项
   - 点击后调用智能评分请求

3. **多语言支持**
   - `locales/zh-Hans/translation.json` - 简体中文翻译
   - `locales/zh-Hant/translation.json` - 繁体中文翻译
   - `locales/en/translation.json` - 英文翻译

### 配置信息

需要在 `services/ratingService.ts` 中配置正确的应用信息：

```typescript
// App Store ID (需要替换为实际的应用ID)
private static readonly IOS_APP_ID = '982107779'; // 替换为实际的App Store ID

// Google Play包名 (已配置为与app.json一致)
private static readonly ANDROID_PACKAGE_NAME = 'com.mindpowerhk.knowmetype';
```

## 配置说明

### 环境变量配置

评价功能需要配置以下环境变量：

1. **创建 `.env` 文件**
   在项目根目录创建 `.env` 文件，添加以下配置：

```bash
# 应用商店配置
IOS_APP_ID=your_ios_app_id
ANDROID_PACKAGE_NAME=your_android_package_name
```

2. **获取应用ID和包名**
   - **iOS App Store ID**: 在 [App Store Connect](https://appstoreconnect.apple.com) 中找到您的应用，应用ID是一串数字
   - **Android包名**: 与 `app.json` 中的 `android.package` 保持一致

3. **配置示例**
```bash
# 示例配置
IOS_APP_ID=982107779
ANDROID_PACKAGE_NAME=com.mindpowerhk.knowmetype
```

### 代码实现

评价服务现在从环境变量中动态读取配置：

```typescript
// 从环境变量中获取App Store ID
private static get IOS_APP_ID(): string {
  return Constants.expoConfig?.extra?.iosAppId || '982107779';
}

// 从环境变量中获取Android包名
private static get ANDROID_PACKAGE_NAME(): string {
  return Constants.expoConfig?.extra?.androidPackageName || 'com.mindpowerhk.knowmetype';
}
```

这样可以确保：
- 开发和生产环境使用不同的配置
- 避免在代码中硬编码敏感信息
- 便于CI/CD流程中的配置管理

## 使用方法

### 用户操作流程

1. 打开应用设置页面
2. 点击"评价我们"选项
3. 系统会自动选择最佳的评分方式：
   - 如果支持应用内评分，显示系统级评分弹窗
   - 如果不支持，自动跳转到应用商店评分页面

### 开发者集成

```typescript
import { RatingService } from '@/services/ratingService';

// 智能评分请求（推荐使用）
await RatingService.smartRatingRequest();

// 或者分步骤调用
const isAvailable = await RatingService.isInAppReviewAvailable();
if (isAvailable) {
  await RatingService.requestInAppReview();
} else {
  await RatingService.openStoreForReview();
}
```

## 最佳实践

### 何时请求评分

遵循官方指南，在以下情况下请求评分：

1. **用户完成重要操作后** - 如成功优化文本、保存模板等
2. **用户表现出参与度** - 如多次使用应用、创建自定义模板等
3. **逻辑暂停点** - 避免在用户执行时间敏感任务时打断

### 避免的做法

1. **不要过度请求** - 系统会自动限制频率
2. **不要在首次启动时请求** - 让用户先体验应用
3. **不要在用户忙碌时请求** - 如正在录音、处理文本时
4. **不要使用按钮触发** - 应该在自然的用户流程中触发

## 平台特性

### iOS
- 使用 `SKStoreReviewController`
- 系统自动限制每365天最多显示3次
- 在TestFlight环境下不会显示评分弹窗
- 支持直接跳转到App Store评分页面

### Android
- 使用 `ReviewManager` API
- 需要Android 5.0+和Google Play商店
- 支持直接跳转到Google Play评分页面
- 自动处理Google Play服务不可用的情况

## 错误处理

评价服务包含完善的错误处理机制：

1. **网络错误** - 自动重试或回退到浏览器
2. **权限错误** - 显示友好的错误提示
3. **平台不支持** - 自动回退到应用商店页面
4. **API限制** - 遵循平台限制，避免过度请求

## 测试

### 开发环境测试
- iOS: 在开发环境下会显示测试评分界面
- Android: 需要使用内部测试轨道或内部应用共享

### 生产环境
- 确保应用已发布到相应的应用商店
- 测试用户需要从应用商店下载应用
- 遵循各平台的测试指南

## 注意事项

1. **App Store ID**: 需要在应用发布到App Store后获取真实的App Store ID
2. **包名**: 已配置为与app.json中的包名一致
3. **权限**: 无需额外权限，使用系统API
4. **频率限制**: 遵循平台限制，避免滥用

## 更新日志

- **v1.0.0** - 初始版本，支持应用内评分和应用商店跳转
- 集成到设置页面
- 多语言支持
- 完善的错误处理机制 