# deploy-unified.sh 脚本修复记录

## 问题描述

在运行 `deploy-unified.sh` 脚本时遇到以下问题：

1. **端口占用错误**：
   ```
   error: Uncaught (in promise) AddrInUse: Address already in use (os error 48)
   ```

2. **端口参数不起作用**：
   - 即使传递了端口参数（如 `8081`），脚本仍然尝试使用默认端口 `8000`
   - `local-test` 模式不支持端口参数，只有 `local-public` 模式支持

## 问题原因

1. **端口8000被占用**：系统中有其他deno进程占用了默认端口8000
2. **脚本参数处理缺陷**：`local-test` 模式的参数解析没有包含端口参数
3. **环境变量传递问题**：脚本没有正确设置 `PORT` 环境变量给Edge Function

## 修复方案

### 1. 停止占用端口的进程
```bash
# 查找占用端口的进程
lsof -i :8000

# 停止进程
kill 27277
```

### 2. 修复脚本参数处理

**修复前：**
```bash
local-test)
    FUNCTION_NAME=${1:-"revenuecat-webhook"}
    ;;
```

**修复后：**
```bash
local-test)
    FUNCTION_NAME=${1:-"revenuecat-webhook"}
    PORT=${2:-8000}
    ;;
```

### 3. 修复环境变量传递

**修复前：**
```bash
run_local_test() {
    info "======== 本地测试模式 ========"
    info "函数: $FUNCTION_NAME"
    
    validate_function
    setup_local_env
    
    cd "functions/$FUNCTION_NAME"
    # ... 启动函数
}
```

**修复后：**
```bash
run_local_test() {
    info "======== 本地测试模式 ========"
    info "函数: $FUNCTION_NAME"
    info "端口: $PORT"
    
    validate_function
    setup_local_env
    
    # 设置端口环境变量
    export PORT="$PORT"
    
    cd "functions/$FUNCTION_NAME"
    # ... 启动函数
}
```

### 4. 更新帮助文档

**修复前：**
```
local-test [函数名]         本地测试模式，运行指定的边缘函数
```

**修复后：**
```
local-test [函数名] [端口]   本地测试模式，运行指定的边缘函数
```

## 修复验证

修复后运行测试：
```bash
cd supabase
bash deploy-unified.sh local-test unified-proxy 8090
```

**成功输出：**
```
[INFO] ======== 本地测试模式 ========
[INFO] 函数: unified-proxy
[INFO] 端口: 8090
[INFO] 设置本地环境变量...
[INFO] 加载 .env 文件中的环境变量...
统一代理服务将在端口 8090 启动
统一代理服务正在监听端口 8090...
可用的服务: siliconflow, openai
Listening on http://localhost:8090/
```

## 使用示例

现在所有三种模式都支持正确的参数：

```bash
# 本地测试模式（默认端口8000）
bash deploy-unified.sh local-test unified-proxy

# 本地测试模式（指定端口8090）
bash deploy-unified.sh local-test unified-proxy 8090

# 本地公网模式（指定端口8001）
bash deploy-unified.sh local-public unified-proxy 8001

# 云端部署模式
bash deploy-unified.sh cloud -e production
```

## 相关文件修改

| 文件 | 修改内容 | 说明 |
|------|----------|------|
| `supabase/deploy-unified.sh` | 添加端口参数解析和环境变量传递 | 支持端口自定义 |
| - | 更新帮助文档 | 反映新的参数支持 |

## 注意事项

1. **端口选择**：避免使用常用端口（8000, 8080, 8081等），建议使用8090+
2. **进程管理**：使用完毕后记得停止Edge Function进程
3. **环境变量**：确保 `.env.local` 文件中有必要的API密钥配置

---

**修复完成时间**：2025年1月13日  
**修复状态**：✅ 成功  
**测试状态**：✅ 通过 