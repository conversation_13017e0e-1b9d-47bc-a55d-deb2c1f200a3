# 知我AI输入法 - 语音转文字模型调用方案

## 1. 实现框架概述

### 用户类型与服务匹配逻辑
- **免费用户**：
  - 默认使用 `expo-speech-recognition` 进行语音识别
  - 不具备高级语音识别模型使用权限
  - 对于安卓设备，特别是没有内置本地语音服务的手机，使用 Web API 备选方案
  - **如需云端备选转写，使用 OpenAI Whisper (`whisper-1`) 作为模型**
  
- **VIP用户**：
  - 默认使用 `expo-speech-recognition` 进行语音识别
  - 可选择使用高级语音识别模型（OpenAI GPT-4o mini Transcribe，模型名为 `gpt-4o-mini-transcribe`）
  - 具备更高的转录精度和可靠性

### 技术实现路径
1. **主路径**：使用 `expo-speech-recognition` 封装的设备原生语音识别
2. **备选路径**（安卓/免费用户）：对于无法使用本地服务的安卓设备，或免费用户云端转写，使用 OpenAI Whisper (`whisper-1`)
3. **高级路径**（VIP）：集成 OpenAI GPT-4o mini Transcribe API (`gpt-4o-mini-transcribe`)

## 2. 现有代码架构分析

### 语音识别服务
- 主要服务文件：`services/expoSpeechService.ts`
- 包含功能：
  - 开始/暂停/恢复/停止录音
  - 设置语音识别监听器
  - 处理识别结果
  - 错误处理和服务降级

### 状态管理
- Redux slice：`store/slices/recordingSlice.ts`
- 异步操作：使用 createAsyncThunk 处理录音和优化文本的异步流程
- 状态包含：录音状态、识别文本、优化文本、错误处理等

### 用户VIP状态管理
- 通过 `purchaseService.ts` 和 `supabaseService.ts` 处理 VIP 权限
- 数据库表 `profiles` 中包含 `is_vip` 和 `api_key_type` 等字段记录用户状态

## 3. OpenAI Transcription API 集成

### API基本信息
- 免费用户云端转写模型：**Whisper (`whisper-1`)**
- VIP用户高级转写模型：**GPT-4o mini Transcribe (`gpt-4o-mini-transcribe`)**
- API端点：https://api.openai.com/v1/audio/transcriptions
- 文档参考：https://platform.openai.com/docs/guides/realtime-transcription

### API请求格式
```typescript
// POST 请求
{
  "file": <音频文件>, // multipart/form-data
  "model": "gpt-4o-mini-transcribe",
  "language": "zh", // 中文
  "response_format": "text" // 或 "verbose_json" 获取更详细信息
}
```

## 4. 集成方案

### 4.1 服务层整合

创建新的 OpenAI 语音识别服务文件 `services/openaiSpeechService.ts`：

```typescript
import * as FileSystem from 'expo-file-system';
import { Audio } from 'expo-av';
import Constants from 'expo-constants';

// OpenAI API配置
const OPENAI_API_URL = 'https://api.openai.com/v1/audio/transcriptions';
const MODEL = 'gpt-4o-mini-transcribe';

// 获取API密钥
const getOpenAIApiKey = async (): Promise<string> => {
  // 从环境变量或配置获取API密钥
  return Constants.expoConfig?.extra?.openaiApiKey || '';
};

// 使用OpenAI API进行音频文件转写
export const transcribeAudioFile = async (
  audioUri: string,
  language: string = 'zh'
): Promise<string> => {
  try {
    // 检查文件是否存在
    const fileInfo = await FileSystem.getInfoAsync(audioUri);
    if (!fileInfo.exists) {
      throw new Error('音频文件不存在');
    }

    // 获取API密钥
    const apiKey = await getOpenAIApiKey();
    if (!apiKey) {
      throw new Error('OpenAI API密钥未配置');
    }

    // 准备表单数据
    const formData = new FormData();
    formData.append('file', {
      uri: audioUri,
      name: 'audio.m4a',
      type: 'audio/m4a'
    } as any);
    formData.append('model', MODEL);
    formData.append('language', language);
    formData.append('response_format', 'text');

    // 发送API请求
    const response = await fetch(OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
      },
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`OpenAI API错误: ${errorData.error?.message || response.statusText}`);
    }

    // 解析响应
    const transcription = await response.text();
    return transcription;
  } catch (error) {
    console.error('OpenAI音频转写失败:', error);
    throw error;
  }
};
```

### 4.2 扩展现有语音服务

在 `expoSpeechService.ts` 中添加使用 OpenAI API 的备选方案：

```typescript
// 引入OpenAI语音识别服务
import * as OpenAISpeechService from './openaiSpeechService';

// ...现有代码...

// 添加备用的OpenAI语音识别处理
export const tryAlternativeSpeechRecognition = async (
  audioUri: string, 
  callback?: SpeechRecognitionCallback
): Promise<boolean> => {
  try {
    console.log('尝试使用OpenAI API进行语音识别...');
    
    // 判断用户是否为VIP，非VIP用户不使用OpenAI API
    const { isVip } = await storageService.getVipStatus();
    const useOpenAI = isVip && await storageService.getUseAdvancedTranscription();
    
    if (!useOpenAI) {
      console.log('非VIP用户或未启用高级转写，跳过OpenAI API');
      return false;
    }
    
    // 使用OpenAI API转写
    const transcribedText = await OpenAISpeechService.transcribeAudioFile(audioUri);
    
    if (transcribedText) {
      console.log('OpenAI转写成功，文本长度:', transcribedText.length);
      
      // 更新全局变量
      currentRecognizedText = transcribedText;
      fullRecognizedText = transcribedText;
      
      // 调用回调
      callback?.onResult?.(transcribedText);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('OpenAI备选语音识别失败:', error);
    return false;
  }
};
```

### 4.3 StorageService 扩展

在 `storageService.ts` 中添加高级转写设置：

```typescript
// 存储键
const STORAGE_KEYS = {
  // ...现有键...
  USE_ADVANCED_TRANSCRIPTION: 'use_advanced_transcription'
};

// 获取是否使用高级转写设置
export const getUseAdvancedTranscription = async (): Promise<boolean> => {
  try {
    const value = await AsyncStorage.getItem(STORAGE_KEYS.USE_ADVANCED_TRANSCRIPTION);
    return value === 'true';
  } catch (error) {
    console.error('获取高级转写设置失败:', error);
    return false;
  }
};

// 设置是否使用高级转写
export const setUseAdvancedTranscription = async (useAdvanced: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.USE_ADVANCED_TRANSCRIPTION, useAdvanced ? 'true' : 'false');
  } catch (error) {
    console.error('保存高级转写设置失败:', error);
  }
};
```

### 4.4 Redux 状态扩展

在 `recordingSlice.ts` 中添加高级转写相关状态和操作：

```typescript
// 扩展RecordingState接口
interface RecordingState {
  // ...现有字段...
  useAdvancedTranscription: boolean; // 是否使用高级转写
}

// 初始状态
const initialState: RecordingState = {
  // ...现有初始状态...
  useAdvancedTranscription: false
};

// 添加切换高级转写的reducer
const recordingSlice = createSlice({
  name: 'recording',
  initialState,
  reducers: {
    // ...现有reducers...
    toggleAdvancedTranscription: (state) => {
      state.useAdvancedTranscription = !state.useAdvancedTranscription;
    },
    setAdvancedTranscription: (state, action: PayloadAction<boolean>) => {
      state.useAdvancedTranscription = action.payload;
    }
  },
  // ...现有extraReducers...
});
```

### 4.5 设置界面集成

在设置页面中增加高级转写选项（仅VIP用户可见）：

```typescript
// 在设置页面添加高级转写选项
<View>
  {isVIP && (
    <SettingItem
      title="使用高级语音识别"
      description="使用OpenAI GPT-4o mini进行更准确的语音转写"
      rightElement={
        <Switch
          value={useAdvancedTranscription}
          onValueChange={toggleAdvancedTranscription}
        />
      }
    />
  )}
</View>
```

## 5. 环境配置

### app.config.ts 扩展

在 `app.config.ts` 中添加 OpenAI API 配置：

```typescript
extra: {
  // ...现有配置...
  openaiApiKey: process.env.OPENAI_API_KEY || '',
},
```

### .env 文件更新

添加 OpenAI API 密钥环境变量：

```
# 添加OpenAI API密钥
OPENAI_API_KEY=your_openai_api_key_here
```

## 6. 实现路线图

### 第一阶段：基础集成
1. 创建 OpenAI 语音转写服务
2. 扩展存储服务以支持高级转写设置
3. 更新 Redux 状态
4. 修改环境配置

### 第二阶段：UI集成
1. 在设置页面添加高级转写选项
2. 更新使用说明和提示文本

### 第三阶段：测试和优化
1. 测试不同场景下的语音识别效果
2. 优化API调用流程和错误处理
3. 添加监控和记录日志 

## 7. 实现进度

### 已完成工作

1. **基础框架搭建**：
   - 创建 `openaiSpeechService.ts` 服务文件，实现 OpenAI 音频转写功能
   - 扩展 `storageService.ts`，添加高级转写设置的保存和获取功能
   - 扩展 `expoSpeechService.ts`，添加 OpenAI 转写作为备选方案
   - 更新 Redux 状态管理，添加高级转写相关状态和操作
   - 在设置界面添加高级转写开关（仅VIP用户可见）
   - 配置环境变量，支持 OpenAI API 密钥

2. **功能逻辑实现**：
   - 实现用户类型差异化处理（免费用户 vs VIP用户）
   - 实现备选转写路径（安卓设备）
   - 实现设置的本地存储和状态同步

### 下一步计划

1. **功能测试**：
   - 使用真实 OpenAI API 密钥进行音频转写测试
   - 测试不同场景下的转写效果（中文、多人对话、背景噪音等）
   - 验证VIP权限控制和设置界面功能

2. **优化改进**：
   - 优化错误处理和重试机制
   - 添加音频格式转换功能，确保与 OpenAI API 兼容
   - 实现转写进度反馈，提升用户体验
   - 添加音频分段处理，解决长时间录音的问题

3. **文档完善**：
   - 编写详细的使用说明和API文档
   - 更新用户指南，介绍高级转写功能

## 8. 注意事项

1. **API密钥安全**：
   - OpenAI API 密钥应妥善保管，不应直接硬编码在应用中
   - 生产环境应考虑使用后端服务代理API调用，避免密钥泄露
   
2. **计费控制**：
   - OpenAI API 是按使用量计费的，应实现用量控制和限制机制
   - 考虑为VIP用户设置每月转写时长上限，防止过度使用

3. **备选方案**：
   - 如果 OpenAI 服务不稳定或不可用，应有备选转写服务
   - 考虑集成多种转写API，提高服务可用性 