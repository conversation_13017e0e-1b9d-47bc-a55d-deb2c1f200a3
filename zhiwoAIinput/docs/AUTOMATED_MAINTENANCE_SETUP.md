# 自动化订阅维护设置指南

本文档描述如何设置自动化订阅状态维护系统，确保RevenueCat订阅数据与Supabase数据库保持同步。

## 📋 系统概述

自动化维护系统包含以下组件：

1. **GitHub Actions工作流** - 定时执行维护任务
2. **数据库日志表** - 记录执行历史和结果
3. **监控和通知** - 失败时发送告警
4. **revenuecat-check边缘函数** - 实际执行维护逻辑

## 🚀 设置步骤

### 1. 配置GitHub Secrets

在GitHub仓库中设置以下Secrets（Settings → Secrets and variables → Actions）：

| Secret名称 | 说明 | 示例值 |
|-----------|------|--------|
| `SUPABASE_URL` | Supabase项目URL | `https://xxx.supabase.co` |
| `CHECK_API_SECRET` | revenuecat-check函数的API密钥 | 与部署时设置的密钥一致 |
| `NOTIFICATION_WEBHOOK` | 可选：通知webhook URL | Slack/Discord webhook URL |

**设置方法：**
```bash
# 在GitHub仓库页面：
# Settings → Secrets and variables → Actions → New repository secret
```

### 2. 部署数据库迁移

确保已应用最新的数据库迁移，创建维护日志表：

```bash
cd zhiwoAIinput/supabase
supabase db push
```

### 3. 验证边缘函数部署

确保`revenuecat-check`函数已正确部署：

```bash
# 检查函数是否存在
supabase functions list

# 如果未部署，执行部署
supabase functions deploy revenuecat-check
```

### 4. 测试自动化工作流

首次设置后，建议手动触发工作流进行测试：

1. 在GitHub仓库中：`Actions` → `每日订阅状态维护` → `Run workflow`
2. 选择`dry_run: true`进行试运行
3. 检查执行日志和结果

## ⏰ 调度配置

### 默认调度

- **频率**: 每日UTC时间02:00执行
- **任务**: 运行完整维护（`run-all`）
- **模式**: 正式执行（非试运行）

### 自定义调度

可以通过修改`.github/workflows/daily-maintenance.yml`中的cron表达式调整执行时间：

```yaml
schedule:
  - cron: '0 2 * * *'  # 每日02:00 UTC
  # - cron: '0 */6 * * *'  # 每6小时执行一次
  # - cron: '0 2 * * 1'   # 每周一02:00执行
```

## 📊 监控和日志

### 执行日志查看

1. **GitHub Actions日志**：
   - 在仓库中：`Actions` → 选择对应的工作流运行
   - 查看详细的执行步骤和输出

2. **数据库日志**：
   ```sql
   -- 查看最近的维护执行记录
   SELECT * FROM public.maintenance_logs 
   ORDER BY started_at DESC 
   LIMIT 10;
   
   -- 查看各任务类型的最新状态
   SELECT * FROM public.latest_maintenance_status;
   ```

### 执行结果摘要

每次执行后，GitHub Actions会生成执行摘要，包含：
- 执行状态和时间
- 处理的数据统计
- 错误信息（如果有）

### 通知配置

#### Slack通知

1. 创建Slack Webhook：
   - 在Slack中：Apps → Incoming Webhooks → Add to Slack
   - 选择频道并获取Webhook URL

2. 在GitHub Secrets中设置：
   ```
   NOTIFICATION_WEBHOOK = https://hooks.slack.com/services/xxx/yyy/zzz
   ```

#### Discord通知

1. 创建Discord Webhook：
   - 在Discord频道设置中：Integrations → Webhooks → New Webhook
   - 复制Webhook URL

2. 在GitHub Secrets中设置相同的`NOTIFICATION_WEBHOOK`

## 🔧 故障排除

### 常见问题

#### 1. 工作流执行失败
**可能原因**：
- API密钥错误或过期
- Supabase服务不可用
- 网络连接问题

**解决方法**：
- 检查GitHub Secrets配置
- 验证API密钥有效性
- 查看详细错误日志

#### 2. 权限错误
**可能原因**：
- API密钥权限不足
- RLS策略阻止访问

**解决方法**：
- 确保使用Service Role密钥
- 检查数据库RLS策略

#### 3. 数据不一致数量过多
**现象**：收到大量数据不一致告警

**可能原因**：
- RevenueCat webhook配置问题
- 网络问题导致webhook丢失
- 时区或时间同步问题

**解决方法**：
- 检查RevenueCat webhook配置
- 验证webhook URL和密钥
- 检查webhook历史记录

### 调试方法

#### 1. 手动执行测试
```bash
# 直接调用边缘函数测试
curl -X POST "https://your-project.supabase.co/functions/v1/revenuecat-check/run-all" \
  -H "Authorization: Bearer YOUR_API_SECRET" \
  -H "Content-Type: application/json" \
  -d '{"dry_run": true}'
```

#### 2. 查看详细日志
```sql
-- 查看特定时间段的执行记录
SELECT * FROM public.maintenance_logs 
WHERE started_at >= '2024-01-24 00:00:00'::timestamptz
ORDER BY started_at DESC;

-- 查看错误详情
SELECT 
  task_type,
  started_at,
  error_message,
  error_details
FROM public.maintenance_logs 
WHERE status = 'failed'
ORDER BY started_at DESC;
```

## 📈 性能优化

### 建议配置

1. **执行频率**：
   - 对于大多数应用：每日一次足够
   - 高频交易应用：每6-12小时一次
   - 测试期间：每小时一次

2. **超时设置**：
   - 当前设置：15分钟
   - 用户数量< 10万：5-10分钟足够
   - 用户数量> 10万：可调整至30分钟

3. **错误重试**：
   - GitHub Actions自带重试机制
   - 网络错误会自动重试
   - 可以手动重新运行失败的工作流

### 扩展性考虑

随着用户数量增长，可以考虑：

1. **分批处理**：
   - 修改边缘函数支持分页处理
   - 减少单次执行的内存和时间消耗

2. **并行处理**：
   - 将不同类型的维护任务分离
   - 使用多个工作流并行执行

3. **增量处理**：
   - 只处理最近变更的数据
   - 使用时间戳过滤减少处理量

## 🔒 安全考虑

1. **API密钥管理**：
   - 定期轮换API密钥
   - 使用最小权限原则
   - 避免在日志中泄露密钥

2. **访问控制**：
   - 限制GitHub Actions工作流的访问权限
   - 定期审查仓库权限

3. **数据保护**：
   - 维护日志不包含敏感用户信息
   - 定期清理历史日志记录

## 📝 维护建议

1. **定期检查**：
   - 每周查看维护执行状态
   - 监控数据不一致趋势
   - 关注错误日志

2. **性能监控**：
   - 跟踪执行时间变化
   - 监控处理的数据量
   - 观察系统负载影响

3. **配置更新**：
   - 根据业务需求调整执行频率
   - 更新通知配置
   - 优化维护逻辑

## 📞 支持

如遇到问题，可以：

1. 查看GitHub Actions执行日志
2. 检查数据库maintenance_logs表
3. 参考本文档的故障排除部分
4. 手动执行边缘函数进行测试 