# 日志服务迁移指南

## 📖 概述

为了在生产环境中禁用日志打印，我们创建了统一的日志管理服务 `logService`。该服务根据 `APP_ENV` 环境变量自动控制日志的打印行为。

## 🎯 环境日志策略

| 环境 | 允许的日志级别 | 说明 |
|------|----------------|------|
| `development` | 全部 (`log`, `info`, `warn`, `error`, `debug`) | 开发环境允许所有日志 |
| `staging` | 部分 (`warn`, `error`) | 测试环境只允许警告和错误日志 |
| `production` | 无 | 生产环境禁用所有日志 |

## 🔧 使用方法

### 1. 导入日志服务

```typescript
// 导入所有日志函数
import { log, info, warn, error, debug } from '../services/logService';

// 或者导入日志服务实例
import { logService } from '../services/logService';

// 或者导入特殊功能
import { logGroup, logGroupEnd, logTable, logWithTimestamp } from '../services/logService';
```

### 2. 替换现有的 console.log

#### 之前 (console.log)
```typescript
console.log('用户登录成功:', user);
console.info('系统初始化完成');
console.warn('网络连接不稳定');
console.error('API调用失败:', error);
```

#### 之后 (logService)
```typescript
log('用户登录成功:', user);
info('系统初始化完成');
warn('网络连接不稳定');
error('API调用失败:', error);
```

### 3. 使用调试日志

调试日志只在开发环境中显示：

```typescript
debug('详细的调试信息:', complexObject);
debug('函数执行状态:', { step: 1, data: result });
```

### 4. 使用高级日志功能

#### 分组日志
```typescript
logGroup('用户认证流程');
log('步骤1: 验证用户名密码');
log('步骤2: 检查用户权限');
log('步骤3: 生成访问令牌');
logGroupEnd();
```

#### 表格日志
```typescript
const userData = [
  { id: 1, name: '用户A', status: 'active' },
  { id: 2, name: '用户B', status: 'inactive' }
];
logTable(userData);
```

#### 带时间戳的日志
```typescript
logWithTimestamp('info', 'API请求开始');
logWithTimestamp('error', 'API请求失败:', errorDetails);
```

## 📝 迁移步骤

### 步骤 1: 在服务文件中导入日志服务

在每个需要日志的服务文件顶部添加：

```typescript
import { log, info, warn, error, debug } from './logService';
```

### 步骤 2: 批量替换 console.log

使用编辑器的查找替换功能：

- 查找: `console.log(`
- 替换: `log(`

- 查找: `console.info(`
- 替换: `info(`

- 查找: `console.warn(`
- 替换: `warn(`

- 查找: `console.error(`
- 替换: `error(`

### 步骤 3: 识别调试日志

将仅用于开发调试的日志改为 `debug()`：

```typescript
// 开发调试信息
debug('组件渲染状态:', componentState);
debug('API响应数据:', responseData);
debug('用户交互事件:', event);
```

### 步骤 4: 验证迁移效果

在不同环境中测试：

```typescript
// 获取当前日志配置
import { getLogConfig } from '../services/logService';

const config = getLogConfig();
console.log('日志配置:', config);
// 输出示例：
// {
//   environment: 'production',
//   loggingEnabled: false,
//   allowedLevels: []
// }
```

## 🔄 已迁移的文件示例

### services/supabaseService.ts (部分示例)

```typescript
import { log, warn, error, debug, info } from './logService';

// 之前
console.log('[Supabase] 开发环境状态:', config);

// 之后
debug('[Supabase] 开发环境状态:', config);
```

## 🚨 注意事项

### 1. 错误处理中的日志

错误日志在所有环境中都应该保留：

```typescript
try {
  // 业务逻辑
} catch (err) {
  error('操作失败:', err); // 在 staging 和 production 中会被禁用
  // 如果需要在生产环境记录错误，使用其他错误监控服务
}
```

### 2. 关键信息日志

对于真正需要在生产环境中保留的日志，可以直接使用 `console.log`：

```typescript
// 系统启动信息 - 需要在生产环境保留
console.log('应用启动成功, 版本:', appVersion);

// 其他调试信息 - 使用日志服务
debug('详细启动配置:', startupConfig);
```

### 3. 第三方库的日志

有些第三方库可能有自己的日志系统，这些通常需要在库的配置中单独处理：

```typescript
// RevenueCat 配置示例
import { configure } from 'react-native-purchases';

configure({
  // ...
  debugLogsEnabled: __DEV__, // 只在开发环境启用调试日志
});
```

## 🛠️ 环境配置

确保在不同环境的配置文件中正确设置 `APP_ENV`：

### .env.development
```bash
APP_ENV=development
```

### .env.staging  
```bash
APP_ENV=staging
```

### .env.production
```bash
APP_ENV=production
```

### eas.json
```json
{
  "build": {
    "staging": {
      "env": {
        "APP_ENV": "staging"
      }
    },
    "production": {
      "env": {
        "APP_ENV": "production"
      }
    }
  }
}
```

## 📊 预期效果

迁移完成后，在不同环境中的日志行为：

- **开发环境**: 所有日志正常显示，便于调试
- **测试环境**: 只显示警告和错误，减少噪音
- **生产环境**: 完全禁用日志，提升性能和安全性

## ✅ 验证清单

- [ ] 创建并配置 `logService.ts`
- [ ] 在关键服务文件中导入日志服务
- [ ] 替换 `console.log` 为相应的日志函数
- [ ] 将调试信息改为 `debug()` 调用
- [ ] 在不同环境中测试日志行为
- [ ] 确认生产环境中没有日志输出
- [ ] 更新团队开发文档

## 🔄 批量迁移脚本

如果需要批量迁移，可以使用以下脚本：

```bash
#!/bin/bash

# 在 services 目录下批量替换
find ./services -name "*.ts" -type f | xargs sed -i '' 's/console\.log(/log(/g'
find ./services -name "*.ts" -type f | xargs sed -i '' 's/console\.info(/info(/g'
find ./services -name "*.ts" -type f | xargs sed -i '' 's/console\.warn(/warn(/g'
find ./services -name "*.ts" -type f | xargs sed -i '' 's/console\.error(/error(/g'

echo "批量替换完成，请手动检查并添加 import 语句"
``` 