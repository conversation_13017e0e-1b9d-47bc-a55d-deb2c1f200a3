# 项目运行指南

## 一、运行iOS应用

### 第一步：安装依赖
1. 删除 `node_modules` 文件夹和 `package-lock.json` 文件
2. 执行以下命令重新安装依赖：
   ```bash
   npm install
   ```

### 第二步：预构建项目
执行以下命令进行预构建：
```bash
npx expo prebuild --clean
```

> 注意：这一步会生成 iOS 和 Android 文件夹。

### 第三步：安装 iOS 依赖
```bash
cd ios
pod install
cd ..
```

### 第四步：运行应用
```bash
npx expo run:ios --device
```

选择真机进行安装。

### 后续运行
```bash
npx expo start -c
```

> 提示：
> 1. 记录IP地址
> 2. 在 `.env` 文件中修改 `Local_IP`
> 3. 重新执行 `npx expo start -c` 运行应用

## 二、运行 Supabase 本地数据库

### 1. 启动本地数据库
```bash
cd supabase
bash run-local.sh
```

> 此脚本会自动完成数据库迁移和初始化数据导入。

### 2. 创建测试用户
1. 打开浏览器访问：
   ```
   http://127.0.0.1:54323/project/default/auth/users
   ```
2. 在 `auth_user` 表中新建测试用户，例如：
   - 邮箱：<EMAIL>
   - 设置密码

> 系统会自动创建关联表信息。

### 3. 登录应用
打开App，使用测试账号密码登录。

## 三、处理支付业务

### 1. 启动支付服务
```bash
cd supabase
bash direct-run-public.sh
```

### 2. 配置 RevenueCat
1. 在控制台日志中复制 ngrok 的公网 IP
2. 登录 RevenueCat 后台
3. 创建测试 webhook
4. 配置对应的公网 IP

## 常见问题

1. 如果遇到依赖问题，请先清除缓存：
   ```bash
   watchman watch-del-all
   rm -rf node_modules
   npm cache clean --force
   npm install
   ```

2. iOS 构建失败时，可以尝试：
   ```bash
   cd ios
   pod deintegrate
   pod install
   cd ..
   ```