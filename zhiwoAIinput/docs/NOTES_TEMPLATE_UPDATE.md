# 模板更新说明

## 更新概述

本次更新对"做notes记录"模板进行了优化，并新增了"markdown格式的要点记录"和"原文优化"模板，以更好地满足用户的不同文本处理需求。

## 更新内容

### 1. 修改"做notes记录"模板

**更新前（markdown格式）：**
```
整理成结构化笔记。用标题、列表、要点分层。关键信息加粗，行动项用→标记。

示例：
输入：客户需求UI界面要简洁支付流程三步完成增加夜间模式
输出：
## 客户需求文档
### 核心功能
• **UI设计** → 极简风格
• **支付流程** → 3步内完成  
• **视觉模式** → 增加暗黑主题
```

**更新后（纯文本格式）：**
```
整理成结构化的纯文本笔记。用段落分层，序号标记，重要信息用【】高亮，用→标示要点。

示例：
输入：客户需求UI界面要简洁支付流程三步完成增加夜间模式
输出：
客户需求文档

核心功能：
1. UI设计 → 【极简风格】要求  
2. 支付流程 → 必须【3步内完成】
3. 视觉模式 → 【增加暗黑主题】功能

→ 优先级：支付流程优化最重要
```

### 2. 新增"markdown格式的要点记录"模板

- **语义ID**: `markdown_notes`
- **VIP模板**: 是
- **分类**: scenario
- **描述**: 使用markdown格式生成结构化要点记录，支持标题层级和格式化
- **提示词**: 保持原有markdown格式的输出逻辑

### 3. 新增"原文优化"模板

- **语义ID**: `text_optimize`
- **免费模板**: 是
- **分类**: scenario
- **描述**: 保持原文基础上修正错误、删除重复和语气词，优化段落结构
- **提示词**: 
```
仅做必要的文本优化。修正错误，删除重复词汇和语气词，优化段落呈现，尽可能保持原文表达。

示例：
输入：这个这个方案呢其实还是很不错的嗯我觉得可以考虑一下但是呢还需要再优化优化一下
输出：
这个方案很不错，可以考虑，但还需要进一步优化。
```

## 文件更新列表

### 数据库文件
- `supabase/seed.sql` - 更新seed数据
- `supabase/migrations/20250618150800_update_notes_templates.sql` - 新增迁移文件

### 代码文件
- `services/templateService.ts` - 更新fallback模板和语义ID映射

### 国际化文件
- `locales/zh-Hans/translation.json` - 中文简体翻译
- `locales/zh-Hant/translation.json` - 中文繁体翻译
- `locales/en/translation.json` - 英文翻译

### 部署文件
- `scripts/deploy-template-additions.sh` - 部署脚本

## 部署指南

### 1. 开发环境部署

```bash
# 运行部署脚本
./scripts/deploy-template-additions.sh
```

### 2. 生产环境部署

```bash
# 进入supabase目录
cd supabase

# 应用迁移
supabase migration up

# 验证更新
supabase db --db-url "your-db-url" -c "SELECT name, semantic_id, category, is_vip_only FROM style_templates WHERE semantic_id IN ('notes', 'markdown_notes', 'text_optimize') ORDER BY semantic_id;"
```

## 验证检查

部署完成后，请验证以下内容：

1. **模板数量**: 确认新增了2个模板（markdown_notes、text_optimize）
2. **VIP权限**: 确认"markdown格式的要点记录"为VIP专用，"原文优化"为免费模板
3. **语义ID**: 确认三个模板的semantic_id正确设置
4. **提示词格式**: 确认"做notes记录"使用纯文本格式，"markdown格式的要点记录"使用markdown格式，"原文优化"使用最小化干预原则

## 回滚方案

如需回滚，可以执行以下SQL：

```sql
-- 恢复"做notes记录"的原始提示词
UPDATE style_templates 
SET 
  description = '将零散信息转化为结构化笔记，支持学习/会议/灵感记录场景。',
  prompt = '整理成结构化笔记。用标题、列表、要点分层。关键信息加粗，行动项用→标记。

示例：
输入：客户需求UI界面要简洁支付流程三步完成增加夜间模式
输出：
## 客户需求文档
### 核心功能
• **UI设计** → 极简风格
• **支付流程** → 3步内完成  
• **视觉模式** → 增加暗黑主题'
WHERE name = '做notes记录';

-- 删除新增的模板
DELETE FROM style_templates WHERE semantic_id IN ('markdown_notes', 'text_optimize');
```

## 注意事项

1. 该更新不影响现有用户的模板偏好设置
2. "做notes记录"和"原文优化"为免费模板，"markdown格式的要点记录"为VIP专用
3. 更新会自动同步到应用的fallback模板系统
4. 国际化翻译已同步更新

## 预期影响

- **用户体验**: 提供更清晰的纯文本笔记格式和原文优化功能
- **功能丰富度**: VIP用户可选择markdown格式的高级笔记功能，免费用户享有原文优化
- **兼容性**: 保持向后兼容，不影响现有功能 