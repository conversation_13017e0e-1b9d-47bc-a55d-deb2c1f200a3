# 敏感词过滤功能实现指南

## 概述

知我AI输入法的敏感词过滤功能是一个完整的内容审核系统，能够自动检测和过滤语音转录结果中的不当内容。当检测到敏感词时，系统会自动替换为多语言的"优化失败，请重试"提示信息。

## 架构设计

### 1. 数据库层
- **表结构**: `sensitive_words` 表存储敏感词信息
- **索引优化**: 为活跃状态、分类和词汇创建索引
- **函数接口**: 提供获取和检查敏感词的数据库函数
- **安全策略**: 使用RLS策略控制访问权限

### 2. 服务层
- **缓存机制**: 24小时本地缓存，减少网络请求
- **定时更新**: 6小时自动更新敏感词库
- **版本控制**: 支持强制刷新缓存
- **容错处理**: 服务异常时使用原始文本，不影响核心功能

### 3. 应用层
- **初始化**: 应用启动时自动初始化敏感词服务
- **转录阶段检查**: 在语音转录完成时进行敏感词检查
- **直接终止**: 检测到敏感词时显示弹窗提示，不显示转录文字
- **双重保护**: 转录阶段和历史记录保存时都进行检查

## 核心组件

### 数据库结构

```sql
-- 敏感词表
CREATE TABLE sensitive_words (
    id BIGSERIAL PRIMARY KEY,
    word TEXT NOT NULL UNIQUE,
    category TEXT DEFAULT 'general',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 获取活跃敏感词函数
CREATE OR REPLACE FUNCTION get_active_sensitive_words()
RETURNS TABLE (word TEXT, category TEXT, updated_at TIMESTAMP WITH TIME ZONE);

-- 检查文本敏感词函数
CREATE OR REPLACE FUNCTION check_text_for_sensitive_words(input_text TEXT)
RETURNS TABLE (contains_sensitive_word BOOLEAN, detected_words TEXT[]);
```

### 敏感词服务类

```typescript
class SensitiveWordService {
  // 初始化服务
  async initialize(): Promise<void>
  
  // 从服务端更新敏感词
  async updateFromServer(): Promise<boolean>
  
  // 过滤文本中的敏感词
  async filterText(text: string): Promise<FilterResult>
  
  // 获取统计信息
  getStats(): { totalWords: number; categories: string[]; ... }
}
```

### 接口定义

```typescript
export interface SensitiveWord {
  word: string;
  category: string;
  updated_at: string;
}

export interface FilterResult {
  isFiltered: boolean;
  detectedWords: string[];
  originalText: string;
}
```

## 使用方法

### 1. 基本使用

```typescript
import { filterSensitiveWords } from '@/services/sensitiveWordService';

// 过滤文本
const result = await filterSensitiveWords("需要检查的文本");
if (result.isFiltered) {
  console.log('检测到敏感词:', result.detectedWords);
  // 使用过滤后的提示信息
}
```

### 2. 在录音流程中使用

敏感词过滤已自动集成到 `useSpeechRecording` hook 中：

```typescript
// 在 useSpeechRecording.ts 中
const filterAndUpdateText = useCallback(async (text: string) => {
  const filterResult = await filterSensitiveWords(text);
  
  if (filterResult.isFiltered) {
    return t('common.sensitiveWord.optimizationFailed'); // 使用多语言过滤提示
  }
  return text; // 使用原始文本
}, []);
```

### 3. 管理敏感词

```typescript
import { sensitiveWordService } from '@/services/sensitiveWordService';

// 强制更新敏感词库
await sensitiveWordService.forceUpdate();

// 获取当前敏感词列表
const currentWords = sensitiveWordService.getCurrentWords();

// 获取统计信息
const stats = sensitiveWordService.getStats();
```

## 配置选项

### 缓存配置

```typescript
const CACHE_EXPIRY_HOURS = 24;        // 缓存有效期
const AUTO_UPDATE_INTERVAL_HOURS = 6; // 自动更新间隔
const CURRENT_CACHE_VERSION = '1.0.0'; // 缓存版本
```

### 敏感词分类

- `video_watermark`: 视频水印相关
- `subtitle_watermark`: 字幕水印相关  
- `engagement_prompt`: 互动提示相关
- `general`: 通用敏感词

## 国际化支持

### 中文翻译 (zh-Hans)
```json
{
  "sensitiveWord": {
    "filterTitle": "内容不合规",
    "filterMessage": "检测到不当内容，请重新录制",
    "filterMessageShort": "内容包含不当信息，请重试",
    "optimizationFailed": "优化失败，请重试"
  }
}
```

### 英文翻译 (en)
```json
{
  "sensitiveWord": {
    "filterTitle": "Content Not Compliant",
    "filterMessage": "Inappropriate content detected, please re-record",
    "filterMessageShort": "Content contains inappropriate information, please retry",
    "optimizationFailed": "Optimization failed, please retry"
  }
}
```

## API 参考

### 导出函数

```typescript
// 过滤敏感词（主要接口）
export const filterSensitiveWords: (text: string) => Promise<FilterResult>

// 初始化敏感词服务
export const initializeSensitiveWordService: () => Promise<void>

// 强制更新敏感词库
export const updateSensitiveWords: () => Promise<boolean>
```

### 数据库函数

```sql
-- 获取活跃敏感词
SELECT * FROM get_active_sensitive_words();

-- 检查文本敏感词
SELECT * FROM check_text_for_sensitive_words('测试文本');
```

## 性能优化

### 1. 缓存策略
- **本地缓存**: 使用 AsyncStorage 存储敏感词
- **缓存有效期**: 24小时有效期，平衡实时性和性能
- **版本控制**: 支持强制清空缓存，确保数据一致性

### 2. 网络优化
- **防抖更新**: 避免频繁的文本更新请求
- **后台更新**: 异步更新敏感词库，不阻塞用户操作
- **容错处理**: 网络异常时使用缓存数据

### 3. 检索优化
- **数据库索引**: 为敏感词检索优化索引
- **内存缓存**: 敏感词加载到内存中进行检查
- **批量处理**: 一次性获取所有敏感词，减少查询次数

## 故障排除

### 常见问题

1. **敏感词服务初始化失败**
   - 检查网络连接
   - 验证 Supabase 配置
   - 查看应用日志

2. **敏感词检查不生效**
   - 确认服务初始化成功
   - 检查缓存是否过期
   - 验证数据库函数是否正常

3. **过滤结果不准确**
   - 检查敏感词数据库内容
   - 验证过滤逻辑
   - 更新敏感词库

### 调试工具

1. **测试脚本**: `scripts/test-sensitive-words.js`
2. **服务统计**: `sensitiveWordService.getStats()`
3. **日志输出**: 查看详细的过滤日志

### 日志分析

```typescript
// 检查服务状态
console.log('敏感词统计:', sensitiveWordService.getStats());

// 检查缓存状态
const cacheData = await AsyncStorage.getItem('sensitive_words_cache');
console.log('缓存数据:', JSON.parse(cacheData || '[]').length);
```

## 测试指南

### 运行测试脚本

```bash
# 进入项目目录
cd zhiwoAIinput

# 运行敏感词测试
node scripts/test-sensitive-words.js
```

### 测试用例

```typescript
// 测试敏感词检测
const testTexts = [
  "明镜与点点",                    // 应被过滤
  "字幕由 Amara.org 社群提供",     // 应被过滤
  "这是正常的文本内容",             // 不应被过滤
  "欢迎订阅我们的频道",             // 应被过滤
];
```

## 安全考虑

### 1. 数据安全
- **RLS策略**: 只允许认证用户读取敏感词
- **函数权限**: 使用 SECURITY DEFINER 控制函数权限
- **数据传输**: 通过 HTTPS 加密传输

### 2. 隐私保护
- **本地处理**: 敏感词检查在客户端进行
- **日志脱敏**: 避免在日志中记录原始敏感内容
- **缓存清理**: 应用卸载时自动清理缓存

### 3. 容错设计
- **降级策略**: 服务异常时不影响核心功能
- **错误隔离**: 过滤失败不阻塞用户操作
- **监控告警**: 记录异常情况用于后续优化

## 维护指南

### 1. 敏感词管理

```sql
-- 添加新的敏感词
INSERT INTO sensitive_words (word, category) VALUES ('新敏感词', 'category');

-- 禁用敏感词
UPDATE sensitive_words SET is_active = false WHERE word = '敏感词';

-- 删除敏感词
DELETE FROM sensitive_words WHERE word = '敏感词';
```

### 2. 性能监控

- 监控缓存命中率
- 追踪过滤响应时间
- 统计敏感词检出率

### 3. 定期维护

- 定期清理无效敏感词
- 更新敏感词分类
- 优化过滤算法

## 扩展计划

### 1. 功能扩展
- 支持正则表达式匹配
- 添加白名单机制
- 实现模糊匹配算法

### 2. 管理界面
- 敏感词管理后台
- 实时统计监控
- 批量导入导出

### 3. 算法优化
- 使用 AC 自动机算法
- 支持多语言敏感词
- 智能学习和更新 

## 优化后的使用流程

### 最新检查机制
根据用户需求，我们将敏感词检查提前到转录阶段，实现更严格的内容过滤：

1. **语音录制**: 用户进行语音输入
2. **转录处理**: 语音转换为文字
3. **敏感词检查**: 立即对转录文字进行敏感词检查
4. **结果处理**: 
   - **包含敏感词**: 显示多语言弹窗提示，不显示转录文字，直接终止流程
   - **不包含敏感词**: 正常显示转录文字，允许后续优化操作

### 关键优化点

#### 1. 用户体验改善
- **完整显示**: 用户可以看到完整的转录文本，敏感词检查不会影响文本显示
- **及时反馈**: 在关键操作点提供明确的提示信息
- **多语言支持**: 使用 i18n 翻译键显示本地化的提示信息

#### 2. 数据安全控制
- **关键节点检查**: 在AI优化前进行检查，确保不当内容不会被处理
- **历史记录过滤**: 包含敏感词的内容不会保存到历史记录
- **双重检查**: 在 `saveInputSession` 和 `startOptimizing` 两个关键函数中都进行检查

#### 3. 性能优化
- **按需检查**: 只有在用户主动触发优化时才进行检查，减少不必要的API调用
- **异步处理**: 敏感词检查不会阻塞语音转录流程
- **容错机制**: 检查失败时不影响正常功能使用

### 实现细节

#### 弹窗提示实现
```typescript
// 在 startOptimizing 函数中添加
Alert.alert(
  t('common.sensitiveWord.filterTitle'),
  t('common.sensitiveWord.filterMessage'),
  [
    {
      text: t('common.ok'),
      style: 'default'
    }
  ]
);
```

#### 历史记录过滤实现
```typescript
// 在 saveInputSession 函数中添加检查
const originalFilterResult = await filterSensitiveWords(originalText);
if (originalFilterResult.isFiltered) {
  log('敏感词检查：跳过保存历史记录');
  return; // 直接返回，不保存
}
```

### 测试验证

使用测试脚本验证新的实现：
```bash
cd zhiwoAIinput
node scripts/test-sensitive-words.js
```

测试脚本会验证：
- 数据库连接是否正常
- 敏感词获取是否成功
- 文本过滤功能是否工作正常
- 统计过滤效果

### 故障排除

如果遇到问题，请检查：
1. **翻译键路径**: 确保 `common.sensitiveWord.*` 翻译键存在
2. **服务初始化**: 确认敏感词服务在应用启动时正确初始化
3. **数据库迁移**: 确认使用了最新的迁移文件 `20250628000000_create_sensitive_words.sql` 