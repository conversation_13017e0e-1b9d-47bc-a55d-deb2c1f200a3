# API 密钥配置指南

## 问题诊断

你遇到的 "处理失败，无法完成文本优化，请重试" 错误很可能是 **API 密钥配置问题**。

## ✅ 已修复的配置问题

我已经为你修复了以下关键配置问题：

1. **添加了 `react-native-dotenv` 支持**：在 `babel.config.js` 中添加了环境变量插件
2. **完善了类型定义**：更新了 `types/env.d.ts` 文件
3. **改进了 API 密钥获取逻辑**：在 `utils/config.ts` 中添加了多源获取机制
4. **移除了不安全的硬编码密钥**：清理了 `app.config.ts` 中的敏感信息
5. **添加了详细的调试日志**：在 `services/aiService.ts` 中增加了诊断信息

## 解决步骤

### 1. 创建 .env 文件

在项目根目录 `zhiwoAIinput/` 下创建 `.env` 文件：

```bash
# 在 zhiwoAIinput 目录下执行
touch .env
```

### 2. 配置 API 密钥

在 `.env` 文件中添加以下内容：

```env
# SiliconFlow API 密钥 (必需) - 用于AI文本优化
SILICONFLOW_API_KEY=sk-your-actual-siliconflow-api-key-here

# OpenAI API 密钥 (可选) - 用于语音转写
OPENAI_API_KEY=sk-your-openai-api-key-here

# RevenueCat 配置 (可选) - 用于VIP订阅
REVENUE_CAT_IOS_API_KEY=your-ios-api-key
REVENUE_CAT_ANDROID_API_KEY=your-android-api-key

# 应用配置 (可选)
IOS_APP_ID=6744967748
ANDROID_PACKAGE_NAME=com.mindpowerhk.knowmetype

# Supabase 配置 (可选，已有默认值)
SUPABASE_URL=https://yenwfmoubflrluhqsyim.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key

# 开发环境配置 (可选)
USE_LOCAL_SUPABASE=false
IS_DEV_ENV=false
SHOW_DEV_LOGIN=false
```

**最重要的是配置 `SILICONFLOW_API_KEY`**，这是解决你当前问题的关键！

### 3. 获取 SiliconFlow API 密钥

1. 访问 [SiliconFlow 控制台](https://cloud.siliconflow.cn/)
2. 注册/登录账户
3. 创建 API 密钥
4. 复制密钥并替换上面的 `sk-your-actual-siliconflow-api-key`

### 4. 验证配置

你可以通过以下方式验证配置：

1. **开发环境测试**：
   ```bash
   # 重启开发服务器
   npx expo start --clear
   ```

2. **检查控制台日志**：
   在应用中触发 AI 优化，查看控制台中的 "=== API密钥调试信息 ===" 日志

### 5. 临时测试方案

如果环境变量仍然无法正常工作，你可以临时在 `app.config.ts` 中直接设置：

```typescript
// 在 app.config.ts 中找到这一行：
const siliconflowApiKey = process.env.SILICONFLOW_API_KEY || '';

// 临时修改为（记得替换为真实密钥）：
const siliconflowApiKey = process.env.SILICONFLOW_API_KEY || 'sk-your-actual-api-key';
```

⚠️ **注意**：直接在代码中写入 API 密钥仅用于测试，正式发布前请务必移除！

### 6. 重新构建应用

配置完成后，需要重新构建应用：

```bash
# 清理缓存并重新构建
npx expo start --clear

# 或者对于 EAS Build
eas build --platform ios --clear-cache
```

## 常见问题

### Q: 为什么开发环境正常，打包后就不行？
A: Expo 在构建时会处理环境变量，如果 `.env` 文件不存在或配置错误，构建后的应用就无法获取到正确的 API 密钥。

### Q: 如何确认 API 密钥是否正确？
A: 查看应用控制台日志中的调试信息，或者使用 curl 测试：

```bash
curl -X POST "https://api.siliconflow.cn/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "Qwen/Qwen2.5-7B-Instruct",
    "messages": [{"role": "user", "content": "test"}],
    "max_tokens": 100
  }'
```

### Q: API 密钥泄露了怎么办？
A: 立即前往 SiliconFlow 控制台删除旧密钥并生成新密钥。

## 联系支持

如果按照以上步骤仍然无法解决问题，请提供：
1. 控制台中的 "=== API密钥调试信息 ===" 日志
2. API 调用失败的详细错误信息
3. 使用的 Expo/React Native 版本 