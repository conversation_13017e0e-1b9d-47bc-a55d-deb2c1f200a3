# iOS 多语言本地化配置指南

## 概述
本文档介绍如何在知我AI输入法项目中配置 iOS 应用的多语言支持，包括应用名称和系统权限提示等内容的本地化。

## 配置步骤

### 1. 基础配置

#### 1.1 在 app.json 中添加多语言支持
```json
{
  "expo": {
    "ios": {
      "infoPlist": {
        "CFBundleAllowMixedLocalizations": true,
        "CFBundleDevelopmentRegion": "en",
        "CFBundleLocalizations": ["en", "zh-Hans", "zh-Hant"]
      }
    }
  }
}
```

#### 1.2 在 Info.plist 中设置基本配置
```xml
<key>CFBundleDevelopmentRegion</key>
<string>en</string>
<key>CFBundleLocalizations</key>
<array>
  <string>en</string>
  <string>zh-Hans</string>
  <string>zh-<PERSON>t</string>
</array>
<key>CFBundleName</key>
<string>$(PRODUCT_NAME)</string>
<key>CFBundleDisplayName</key>
<string>$(PRODUCT_NAME)</string>
```

### 2. 创建本地化文件

#### 2.1 目录结构
在 iOS 项目的 Supporting 目录下创建以下结构：
```
Supporting/
  ├── en.lproj/
  │   └── InfoPlist.strings
  ├── zh-Hans.lproj/
  │   └── InfoPlist.strings
  └── zh-Hant.lproj/
      └── InfoPlist.strings
```

#### 2.2 InfoPlist.strings 文件内容示例
每个语言版本的 InfoPlist.strings 文件都需要包含相应语言的翻译文本：

英文版本 (en.lproj/InfoPlist.strings):
```
CFBundleDisplayName = "KnowmeType";
CFBundleName = "KnowmeType";
NSMicrophoneUsageDescription = "Please allow microphone access for voice recognition and recording";
// ... 其他系统提示文本 ...
```

简体中文版本 (zh-Hans.lproj/InfoPlist.strings):
```
CFBundleDisplayName = "知我AI输入法";
CFBundleName = "知我AI输入法";
NSMicrophoneUsageDescription = "请允许访问麦克风以进行语音识别和录音";
// ... 其他系统提示文本 ...
```

繁体中文版本 (zh-Hant.lproj/InfoPlist.strings):
```
CFBundleDisplayName = "知我AI輸入法";
CFBundleName = "知我AI輸入法";
NSMicrophoneUsageDescription = "請允許訪問麥克風以進行語音識別和錄音";
// ... 其他系统提示文本 ...
```

### 3. 文件编码要求
所有的 InfoPlist.strings 文件必须使用 UTF-16 编码。可以使用以下命令转换文件编码：
```bash
iconv -f UTF-8 -t UTF-16 InfoPlist.strings > InfoPlist.strings.tmp
mv InfoPlist.strings.tmp InfoPlist.strings
```

### 4. 关键步骤：在 Xcode 中添加本地化文件

⚠️ **重要提示：这是实现多语言支持的关键步骤，必须手动在 Xcode 中完成**

1. 打开 KnowmeType.xcworkspace
2. 在项目导航器中展开 KnowmeType 项目
3. 右键点击 Supporting 文件夹
4. 选择 "Add Files to 'KnowmeType'"
5. 在文件选择器中导航到项目的 Supporting 目录
6. 选择所有的 .lproj 文件夹
7. 在选项中：
   - 勾选 "Create groups"（不要选择 "Create folder references"）
   - 确保目标（target）KnowmeType 被选中
8. 点击 "Add" 按钮

### 5. 验证配置

1. 清理项目：
   - 在 Xcode 中选择 Product -> Clean Build Folder
   - 删除 DerivedData：`rm -rf ~/Library/Developer/Xcode/DerivedData/KnowmeType-*`

2. 重新构建项目

3. 测试多语言支持：
   - 在模拟器中更改系统语言
   - 验证应用名称是否正确显示
   - 验证各项系统权限提示是否正确显示对应语言的文本

## 常见问题

1. **文本不显示或显示为默认英文**
   - 检查 InfoPlist.strings 文件是否正确添加到 Xcode 项目中
   - 确认文件编码是否为 UTF-16
   - 验证文件内容格式是否正确

2. **更改语言后应用名称未更新**
   - 删除应用后重新安装
   - 确保 Info.plist 中的配置正确
   - 检查 InfoPlist.strings 文件是否包含正确的 CFBundleDisplayName 设置

3. **部分语言未生效**
   - 确保对应语言的 .lproj 文件夹已正确添加到项目中
   - 检查 app.json 和 Info.plist 中的语言配置是否完整
   - 验证 InfoPlist.strings 文件的内容和编码

## 维护建议

1. 在添加新的系统权限或更新提示文本时，确保同时更新所有语言版本的 InfoPlist.strings 文件
2. 保持文件编码的一致性，总是使用 UTF-16
3. 在进行版本更新时，检查并验证所有语言的显示是否正常

## 相关文档

- [Apple 本地化文档](https://developer.apple.com/documentation/xcode/localization)
- [Info.plist 配置指南](https://developer.apple.com/documentation/bundleresources/information_property_list)
- [Expo 本地化文档](https://docs.expo.dev/guides/localization/) 