# Whisper模式超时竞态条件Bug修复

## 问题描述

用户使用Whisper模式进行语音转写时遇到的异步竞态条件问题：

1. **转写超时错误触发**：`OpenAI转写失败: [Error: 请求超时]`
2. **错误处理被调用**：`停止录音回调 onError: [Error: 转写请求超时，请检查网络连接或尝试较短的音频文件]`
3. **但转写实际成功了**：`第1次尝试找到有效转写结果: 这个非常的牛逼,特别牛逼...`
4. **最终显示成功**：`OpenAI代理调用成功`

## 根本原因

在 `Promise.race([fetchPromise, timeoutPromise])` 中：
- 超时Promise先完成并抛出错误
- 但实际的API调用在后台继续进行
- 当API调用最终成功时，会更新全局状态变量
- Hook层的等待逻辑检测到了延迟的成功结果
- 导致同时触发错误回调和成功回调

## 解决方案

### 1. 增强超时处理逻辑 (`expoSpeechService.ts`)

```typescript
// 检查是否是超时错误，给时间等待可能的延迟成功
const isTimeoutError = transcribeError instanceof Error && 
  (transcribeError.message.includes('超时') || 
   transcribeError.message.includes('timeout') ||
   transcribeError.message.includes('请求超时'));

if (isTimeoutError) {
  console.log('[Whisper模式] 检测到超时错误，等待可能的延迟成功结果...');
  
  // 等待3秒，检查是否有延迟的成功结果
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // 检查当前文本状态，如果有有效文本说明转写实际成功了
  const hasValidResult = currentRecognizedText && 
    currentRecognizedText.trim().length > 0 &&
    !currentRecognizedText.includes('正在') &&
    !currentRecognizedText.includes('转写中');
  
  if (hasValidResult) {
    console.log('[Whisper模式] 检测到延迟成功结果，忽略超时错误');
    // 调用成功回调而不是错误回调
    callback.onResult(currentRecognizedText);
    return 'stopped';
  }
}
```

### 2. 优化Hook层等待逻辑 (`useSpeechRecording.ts`)

```typescript
// 检查录音状态，如果已经是error状态，说明服务层已经处理了错误
if (status === 'error') {
  console.log('使用Whisper模式且由内部处理转写结果，跳过后续处理');
  return status;
}
```

### 3. 改进OpenAI服务超时处理 (`openaiSpeechService.ts`)

```typescript
// 创建AbortController用于请求取消
const abortController = new AbortController();
let isRequestCancelled = false;

// 修改超时Promise以包含请求取消逻辑
const enhancedTimeoutPromise = timeoutPromise.catch((error) => {
  console.log('请求超时，正在取消API调用...');
  isRequestCancelled = true;
  abortController.abort();
  return Promise.reject(new Error('请求超时'));
});

// 检查请求是否已被取消
const fetchPromise = callOpenAIAPI(...).then(result => {
  if (isRequestCancelled) {
    console.log('API调用完成但请求已被取消，忽略结果');
    throw new Error('请求已取消');
  }
  return result;
});
```

## 修复效果

1. **消除竞态条件**：超时后正确取消API调用，避免延迟成功
2. **智能错误处理**：对于超时错误，等待检查是否有延迟成功
3. **避免重复处理**：Hook层检查状态，避免重复等待
4. **用户体验提升**：减少假阳性错误，提高转写成功率

## 测试建议

1. 在网络不稳定环境下测试Whisper转写
2. 使用较长的音频文件测试超时场景
3. 验证真正的超时错误仍能正确处理
4. 确认延迟成功的情况能被正确捕获 