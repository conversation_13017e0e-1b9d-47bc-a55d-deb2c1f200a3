# Supabase 配置修复记录

## 问题描述

在修改环境变量架构后，运行 `supabase/run-local.sh` 时出现以下错误：

```
failed to parse config: decoding failed due to the following error(s):
'functions[unified-proxy]' has invalid keys: cors
'' has invalid keys: function_env
```

## 错误原因

Supabase CLI 2.24.3 版本不再支持以下配置项：
1. `cors = true` 在 `[functions.unified-proxy]` 配置块中
2. `[function_env]` 配置块本身

## 修复方案

### 1. 修复 `supabase/config.toml`

**修复前：**
```toml
[functions.unified-proxy]
cors = true

[function_env]
SILICONFLOW_API_KEY = "your_siliconflow_api_key_here"
OPENAI_API_KEY = "your_openai_api_key_here"
# ... 其他配置项
```

**修复后：**
```toml
[functions.unified-proxy]
verify_jwt = false

# Edge Functions 环境变量配置
# 在Supabase CLI 2.24.3中，environment变量通过.env.local文件管理
# 或者通过supabase functions serve命令的--env-file参数传递

[env]
# 这里配置本地开发时的基础环境变量
# 优先级：--env-file > .env.local > config.toml > 默认值
```

### 2. 环境变量管理更新

在新版本中：
- **环境变量** 通过 `supabase/.env.local` 文件管理
- **CORS 配置** 需要在 Edge Function 代码中手动处理
- **函数配置** 主要通过 `[functions.function-name]` 块设置

### 3. 验证修复

运行以下命令验证修复：
```bash
# 检查配置文件是否正确
cd supabase
supabase status

# 测试本地环境启动
cd ..
bash supabase/run-local.sh
```

## 相关文件

| 文件 | 修改内容 | 说明 |
|------|----------|------|
| `supabase/config.toml` | 移除 `cors` 和 `[function_env]` 配置 | 兼容新版本 CLI |
| `supabase/.env.local` | 保持不变 | 环境变量通过此文件管理 |
| `supabase/run-local.sh` | 无需修改 | 脚本仍然正常工作 |

## 成功标志

修复成功后，运行 `bash supabase/run-local.sh` 应该：
1. ✅ 无配置解析错误
2. ✅ 正常启动所有服务
3. ✅ 成功运行数据库迁移
4. ✅ 显示完整的服务状态信息

## 未来注意事项

1. **Supabase CLI 版本升级**：注意关注新版本的配置语法变化
2. **Edge Functions 环境变量**：统一通过 `.env.local` 文件管理
3. **CORS 处理**：需要在 Edge Function 代码中手动添加 CORS 头
4. **配置文件结构**：遵循最新的 Supabase CLI 配置规范

---

**修复完成时间**：2025年1月13日  
**Supabase CLI 版本**：2.24.3  
**修复状态**：✅ 成功 