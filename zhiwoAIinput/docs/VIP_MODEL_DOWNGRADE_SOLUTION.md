# VIP模型自动降级解决方案

## 问题描述

当VIP用户过期回退到普通用户时：
1. 设置页面仍显示VIP模型选项
2. 后台数据表中的模型设置仍保留VIP模型
3. 用户可能仍在使用需要VIP权限的AI模型

## 解决方案

### 1. 数据库层面（后台自动处理）

#### 新增数据库触发器
文件：`supabase/migrations/20250124000000_add_vip_downgrade_trigger.sql`

- **触发器函数**：`handle_vip_downgrade()`
  - 监控 `profiles.is_vip` 字段变化
  - 当从 `true` 变为 `false` 时自动执行
  - 检查用户当前使用的模型是否需要VIP权限
  - 自动将VIP模型回退到免费模型：
    - 文本模型：回退到 `qwen2.5-7b`
    - 语音模型：回退到 `native`

- **手动修复函数**：`fix_non_vip_user_models()`
  - 用于修复现有数据不一致问题
  - 查找所有非VIP用户但使用VIP模型的情况
  - 批量修复模型设置

#### 工作原理
```sql
-- 当用户VIP状态变化时自动触发
CREATE TRIGGER vip_downgrade_trigger
  AFTER UPDATE OF is_vip ON public.profiles
  FOR EACH ROW
  WHEN (OLD.is_vip IS DISTINCT FROM NEW.is_vip)
  EXECUTE FUNCTION public.handle_vip_downgrade();
```

### 2. 客户端层面（实时同步）

#### VIP降级服务
文件：`services/vipDowngradeService.ts`

- **自动检查**：`checkAndSyncUserModels()`
  - 检查用户VIP状态和当前模型设置
  - 如果发现不一致，自动更新为免费模型

- **实时监听**：`setupVipStatusListener()`
  - 监听数据库 `profiles` 表变化
  - 监听 `user_settings` 表变化
  - 自动同步模型设置到本地状态

- **手动同步**：`manualSync()`
  - 提供手动触发同步的能力

#### 设置页面集成
文件：`app/(tabs)/settings/index.tsx`

- 集成VIP降级服务
- 用户登录时自动设置监听器
- VIP状态变化时自动更新本地模型选择
- 静默处理，无toast提醒

### 3. 数据流程

```
VIP过期 → 后台webhook更新is_vip=false → 
数据库触发器执行 → 自动回退模型设置 → 
客户端监听到变化 → 更新本地状态 → 
UI显示正确的免费模型
```

### 4. 使用方法

#### 应用新的数据库迁移
```bash
# 如果使用本地Supabase
cd supabase && ./run-local.sh

# 如果使用云端Supabase
npx supabase db push
```

#### 修复现有数据
```sql
-- 在Supabase SQL编辑器中执行
SELECT * FROM public.fix_non_vip_user_models();
```

### 5. 技术细节

#### 支持的模型回退规则
- **免费文本模型**：`qwen2.5-7b`
- **免费语音模型**：`native`

#### 监控和日志
- 所有降级操作都会记录到 `subscription_events` 表
- 事件类型：`VIP_MODEL_DOWNGRADE`
- 包含完整的前后对比数据

#### 安全考虑
- 触发器只在VIP状态实际变化时执行
- 客户端验证用户权限后才更新
- 行级安全(RLS)确保用户只能访问自己的设置

### 6. 测试方案

1. **VIP过期测试**：
   - 创建VIP用户，设置VIP模型
   - 手动将`is_vip`设为false
   - 验证模型自动回退

2. **客户端同步测试**：
   - 在设置页面选择VIP模型
   - 在后台直接修改VIP状态
   - 验证设置页面自动更新

3. **数据修复测试**：
   - 创建不一致的测试数据
   - 运行修复函数
   - 验证数据一致性

## 总结

此解决方案确保：
- ✅ 后台自动处理VIP模型降级
- ✅ 前端实时同步状态变化
- ✅ 静默处理，无用户干扰
- ✅ 完整的错误处理和日志记录
- ✅ 支持批量修复现有数据问题 