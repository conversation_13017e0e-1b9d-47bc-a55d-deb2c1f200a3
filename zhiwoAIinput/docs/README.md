# Welcome to your Expo app 👋

This is an [Expo](https://expo.dev) project created with [`create-expo-app`](https://www.npmjs.com/package/create-expo-app).

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
    npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Known Issues and Patches

### React Native 0.76.9 iOS Compilation Issue

React Native 0.76.9有一个已知问题，在iOS平台上可能会出现编译错误：

```
'methodQueue' declared as a property with 'strong' attribute cannot be used with primitive type 'dispatch_queue_t' (aka 'struct dispatch_queue_s *')
```

我们使用了patch-package来解决这个问题，它会在`npm install`后自动应用补丁。补丁将React Native源码中的`strong`修饰符改为`assign`，修复了这个编译错误。

如果您重新安装依赖后遇到同样的错误，请确保运行了`npm install`命令，这会触发postinstall脚本自动应用补丁。

## Get a fresh project

When you're ready, run:

```bash
npm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.

# 知我 AI 输入法

知我 AI 输入法是一款基于人工智能的智能输入法应用，提供高效、准确的文字输入体验，并融合了多种智能辅助功能。

## 技术栈

- **React Native** - 跨平台移动应用开发框架
- **Expo** - React Native 开发工具链
- **Redux** - 状态管理
- **React Navigation** - 导航系统
- **NativeWind** - 样式管理 (Tailwind CSS for React Native)
- **TypeScript** - 类型安全的 JavaScript 超集
- **Supabase** - 后端数据库和认证服务
- **RevenueCat** - 应用内支付和订阅管理
- **Languine** - 全球化和本地化
- **Sentry** - 错误监控和性能分析

## 项目结构

- `app/` - 应用程序代码
- `components/` - UI 组件
- `services/` - 服务层代码
- `hooks/` - 自定义 React Hooks
- `store/` - Redux 状态管理
- `utils/` - 实用工具函数
- `assets/` - 静态资源文件
- `types/` - TypeScript 类型定义
- `constants/` - 常量定义
- `supabase/` - Supabase 相关代码和函数

## RevenueCat 集成

本项目已集成 RevenueCat 用于处理应用内购买和订阅功能。主要功能包括：

- 通过 RevenueCat SDK 处理应用内支付购买
- 管理用户订阅状态和权益
- 通过 Supabase Edge Functions 同步订阅状态到后端数据库

### 集成文档

详细说明文档可在以下位置找到：

- [RevenueCat 配置指南](docs/revenuecat_setup.md) - 基础设置指南
- [RevenueCat Webhook 配置指南](docs/revenuecat_webhook_setup_guide.md) - Webhook设置和同步机制
- [Webhook Edge Function 说明](supabase/functions/revenuecat-webhook/README.md) - Edge Function 实现细节

### 重要注意事项

- 请确保在 RevenueCat Dashboard 中设置了正确的应用内购买产品
- 确保按照文档说明配置 Webhook，以便订阅状态能够正确同步
- 测试时使用沙盒环境避免真实扣费

## 开发环境设置

### 前提条件

- Node.js 14+
- Yarn 或 npm
- React Native 开发环境
- Expo CLI

### 安装依赖

```bash
# 使用 yarn
yarn install

# 或使用 npm
npm install
```

### 运行开发服务器

```bash
# 使用 Expo
yarn start

# 在 iOS 模拟器中运行
yarn ios

# 在 Android 模拟器中运行
yarn android
```

## 部署

### iOS 应用

```bash
eas build --platform ios
```

### Android 应用

```bash
eas build --platform android
```

## 许可证

本项目受专有许可证保护，未经授权不得使用、复制或分发。
