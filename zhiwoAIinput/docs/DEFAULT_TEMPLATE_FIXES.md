# 默认模板问题修复总结

## 问题描述

用户反馈了两个问题：
1. 重新reset db后，进入模板个性化页面可以看到"发邮件"有默认标识，但在风格模板管理页面和input页面中都没有默认模板
2. 在onboarding流程的第四步选择模板后，也没有成功设置默认模板

## 根本原因分析

### 1. 存储键名不一致问题
- `templateService.ts` 使用：`'knowme_default_template_id'`
- `templateSlice.ts` 使用：`'default_template_id'`

这导致两个服务使用不同的键名存储默认模板ID，造成数据不同步。

### 2. 默认模板标识被偏好设置覆盖
在 `getAllTemplates` 函数中，偏好设置会错误地覆盖默认模板的设置：

```typescript
// 原有问题代码：
isDefault: preference.isDefault || template.isDefault
```

当偏好设置中没有明确设置 `isDefault: true` 时，即使模板在 `templatesWithDefault` 中被设置为默认，也会被覆盖为 `false`。

### 3. onboarding流程缺少默认模板设置
onboarding完成时只是保存了选择的模板列表，但没有将第一个选择的模板设置为默认模板。

## 修复内容

### 1. 统一存储键名
修改 `templateSlice.ts` 中的存储键名：

```typescript
// 修复前：
DEFAULT_TEMPLATE_ID: 'default_template_id'

// 修复后：
DEFAULT_TEMPLATE_ID: 'knowme_default_template_id'
```

### 2. 修复默认模板标识逻辑
修改 `getAllTemplates` 函数中的偏好设置应用逻辑：

```typescript
// 修复前：
const finalTemplates = templatesWithDefault.map(template => {
  const preference = preferenceMap.get(template.id);
  if (preference) {
    return {
      ...template,
      isHidden: preference.isHidden,
      position: preference.position,
      isDefault: preference.isDefault || template.isDefault  // 问题在这里
    };
  }
  return template;
});

// 修复后：
const finalTemplates = templatesWithDefault.map(template => {
  const preference = preferenceMap.get(template.id);
  if (preference) {
    return {
      ...template,
      isHidden: preference.isHidden,
      // 保持从 templatesWithDefault 中设置的 isDefault 状态
      // 不允许偏好设置覆盖默认模板的判断
    };
  }
  return template;
});
```

### 3. onboarding流程中设置默认模板
在 `initializeTemplatePreferences` 函数中添加默认模板设置：

```typescript
// 设置第一个选择的模板为默认模板
if (selectedTemplates.length > 0) {
  try {
    const { setDefaultTemplateId } = await import('./templateService');
    await setDefaultTemplateId(selectedTemplates[0]);
    console.log('[OnboardingService] 已设置默认模板:', selectedTemplates[0]);
  } catch (defaultError) {
    console.error('[OnboardingService] 设置默认模板失败:', defaultError);
  }
}
```

### 4. 清理旧的systemTemplates引用
移除了对不存在的 `systemTemplates` 导出的引用，改为使用云端同步的模板数据。

## 修复验证

修复完成后，用户应该能够看到：

1. **模板个性化页面**：显示正确的默认模板标识
2. **风格模板管理页面**：显示正确的默认模板标识  
3. **input页面**：显示正确的默认模板
4. **onboarding第四步**：选择模板后能正确设置默认模板

## 预防措施

1. **统一存储键名**：所有涉及默认模板ID的地方都使用 `'knowme_default_template_id'`
2. **明确职责分工**：默认模板的判断逻辑只在 `templateService.ts` 中处理，偏好设置不干涉
3. **完整的onboarding流程**：确保用户选择的第一个模板被设置为默认模板

## 相关文件

- `zhiwoAIinput/services/templateService.ts`
- `zhiwoAIinput/store/slices/templateSlice.ts`  
- `zhiwoAIinput/services/onboardingService.ts`
- `zhiwoAIinput/app/onboarding.tsx`
- `zhiwoAIinput/app/template-manager.tsx` 