# 录音文件保存和读取逻辑升级 - 迁移指南

## 🎯 概述

本次升级将录音文件存储从 AsyncStorage 迁移到 SQLite，并实现了用户隔离和路径动态修复功能。**现有代码无需修改**，升级会自动生效。

## 🔄 自动迁移流程

### 1. 应用启动时自动执行
- 检测 AsyncStorage 中的历史记录
- 自动迁移到 SQLite 数据库
- 保留原始数据备份
- 迁移完成后清理原始数据

### 2. 用户状态变化时自动处理
- 用户登录：匿名用户数据自动迁移到登录用户
- 用户退出：切换回匿名用户状态
- 多账号切换：自动处理数据隔离

## 📝 代码使用方式

### 现有代码（无需修改）

```typescript
// 现有代码继续正常工作
import { 
  saveHistoryRecord, 
  getHistoryRecords, 
  getGroupedHistoryRecords,
  deleteHistoryRecord 
} from '@/services/storageService';

// 保存历史记录
await saveHistoryRecord({
  id: 'record_123',
  timestamp: Date.now(),
  originalText: '原始文本',
  optimizedText: '优化文本',
  templateId: 'template_1',
  templateName: '模板名称',
  audioUri: '/path/to/recording.m4a'
});

// 获取历史记录
const records = await getHistoryRecords();

// 获取分组历史记录（带VIP权限控制）
const groupedRecords = await getGroupedHistoryRecords(50, t);
```

### 新增功能（可选使用）

```typescript
// 直接使用新的SQLite服务（高级用法）
import { newHistoryService } from '@/services/newHistoryService';
import { userIdService } from '@/services/userIdService';

// 获取当前用户ID
const userId = await userIdService.getCurrentUserId();

// 检查是否为匿名用户
const isAnonymous = await userIdService.isAnonymousUser();

// 获取指定用户的历史记录
const userRecords = await newHistoryService.getHistoryRecords(userId);

// 获取真实记录数量（不受VIP限制）
const realCount = await newHistoryService.getRealHistoryRecordsCount(userId);
```

## 🔧 验证升级结果

### 快速验证

```typescript
import { runValidationWithReport } from '@/services/upgradeValidation';

// 运行验证并查看详细报告
const success = await runValidationWithReport();
```

### 完整测试

```typescript
import { runAllTests } from '@/services/recordingUpgradeTest';

// 运行所有测试
await runAllTests();
```

## 📊 功能对比

| 功能 | 升级前 | 升级后 |
|------|--------|--------|
| 存储方式 | AsyncStorage | SQLite |
| 用户隔离 | ❌ | ✅ |
| 路径修复 | ❌ | ✅ |
| 数据持久化 | 部分 | 完全 |
| VIP权限控制 | ✅ | ✅ |
| 分组筛选 | ✅ | ✅ |
| 性能 | 一般 | 优化 |

## 🚨 注意事项

### 1. 数据安全
- 迁移过程会保留原始数据备份
- 迁移失败时数据不会丢失
- 建议在重要更新前手动备份数据

### 2. 性能影响
- 首次启动可能稍慢（执行数据迁移）
- 后续使用性能会有显著提升
- 大量历史记录的查询更快

### 3. 存储空间
- SQLite 数据库文件会占用一定空间
- 录音文件按用户隔离存储
- 定期清理可释放空间

## 🔍 故障排除

### 常见问题

1. **迁移失败**
   ```typescript
   // 检查迁移状态
   import { dataMigrationService } from '@/services/dataMigrationService';
   const status = await dataMigrationService.getMigrationStatus();
   console.log('迁移状态:', status);
   
   // 强制重新迁移
   const success = await dataMigrationService.forceMigration();
   ```

2. **录音文件无法播放**
   ```typescript
   // 手动修复录音路径
   import { recordingFileService } from '@/services/recordingFileService';
   const fixedPath = await recordingFileService.fixRecordingPath(originalPath);
   ```

3. **历史记录显示异常**
   ```typescript
   // 检查用户ID状态
   import { userIdService } from '@/services/userIdService';
   const summary = await userIdService.getUserSummary();
   console.log('用户状态:', summary);
   ```

### 日志查看

所有操作都有详细的日志记录，可以通过以下方式查看：

```typescript
import { log } from '@/services/logService';

// 日志会自动输出到控制台
// 查找包含以下关键词的日志：
// - "SQLiteService"
// - "DataMigrationService" 
// - "UserIdService"
// - "NewHistoryService"
```

## 📈 性能优化建议

### 1. 定期清理
```typescript
// 清理旧的历史记录
await newHistoryService.clearAllHistoryRecords();

// 清理迁移临时数据
await dataMigrationService.cleanupMigrationData();
```

### 2. 批量操作
```typescript
// 批量获取记录时使用分页
const records = await newHistoryService.getHistoryRecords(userId, 50, 0);
```

### 3. 索引优化
SQLite 数据库已经创建了必要的索引，无需手动优化。

## 🔮 未来功能

基于新的架构，未来可以轻松实现：

1. **云端同步**
   ```typescript
   // 导出用户数据
   const userData = await exportUserData(userId);
   
   // 导入用户数据
   await importUserData(userId, userData);
   ```

2. **数据分析**
   ```typescript
   // 利用SQL进行复杂查询
   const stats = await getUserStatistics(userId);
   ```

3. **多设备同步**
   ```typescript
   // 基于用户ID的跨设备数据同步
   await syncUserDataAcrossDevices(userId);
   ```

## ✅ 验收清单

升级完成后，请确认以下功能正常：

- [ ] 应用启动正常，无错误提示
- [ ] 历史记录显示正常
- [ ] 录音功能正常工作
- [ ] 录音文件可以正常播放
- [ ] VIP权限控制正常
- [ ] 用户登录/退出功能正常
- [ ] 数据迁移已完成（检查日志）

## 📞 技术支持

如果遇到问题，请：

1. 运行验证脚本检查状态
2. 查看控制台日志信息
3. 检查是否有未处理的错误
4. 必要时可以强制重新迁移数据

升级过程中的所有操作都有完善的错误处理和回滚机制，确保数据安全。
