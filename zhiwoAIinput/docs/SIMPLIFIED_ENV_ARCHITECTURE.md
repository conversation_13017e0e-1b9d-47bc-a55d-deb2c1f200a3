# 知我AI输入法 - 简化环境变量架构

## 修正说明

根据你的反馈，我们已经简化了环境变量管理架构，移除了复杂的切换逻辑，采用直接配置的方式。

## 新架构特点

### ✅ 简化后的架构
- **直接配置**: 每个环境文件直接包含该环境需要的具体配置值
- **无需切换逻辑**: 去除了复杂的 `USE_LOCAL_SUPABASE`、`LOCAL_IP` 等切换变量
- **更清晰的配置**: 一目了然的环境配置，减少混淆

### 🏗️ 文件结构
```
zhiwoAIinput/
├── .env.development      # 开发环境配置（本地Supabase）
├── .env.staging         # 测试环境配置（云端Supabase）
├── .env.production      # 生产环境配置（云端Supabase）
└── env-config.template  # 配置模板
```

## 环境配置对比

### 开发环境 (.env.development)
```bash
# 本地Supabase配置
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
```

### 测试/生产环境 (.env.staging / .env.production)
```bash
# 云端Supabase配置
SUPABASE_URL=https://yenwfmoubflrluhqsyim.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InllbndmbW91YmZscmx1aHFzeWltIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MjM2NDMsImV4cCI6MjA1ODk5OTY0M30.P9t2dkOhfgPnt53WpDB2gQ6OLWJNvfPNFMqmihmTzXQ
SUPABASE_SERVICE_ROLE_KEY=your_cloud_supabase_service_role_key_here
```

> ⚠️ **注意**: 测试和生产环境需要你在Supabase控制台获取真实的云端 service role key

## 代码更新

### app.config.ts 简化
- 移除了 `useLocalSupabase`、`localIp`、`localSupabaseKey` 等复杂变量
- 直接读取 `SUPABASE_URL`、`SUPABASE_ANON_KEY`、`SUPABASE_SERVICE_ROLE_KEY`
- 简化了配置逻辑，提高了可维护性

### envConfigService.ts 优化
- 新增 `isLocalSupabase()` 方法，通过URL判断是否为本地环境
- 简化了 `getSupabaseConfig()` 方法
- 移除了复杂的环境切换逻辑

## 使用方法

### 开发时
```bash
APP_ENV=development npx expo start
```

### 构建时
```bash
# 开发版本
APP_ENV=development eas build --profile development --local

# 测试版本
APP_ENV=staging eas build --profile staging --local

# 生产版本
APP_ENV=production eas build --profile production --local
```

## 验证配置

运行以下命令验证配置：
```bash
# 检查开发环境
grep -E "(SUPABASE_URL|SUPABASE_ANON_KEY|SUPABASE_SERVICE_ROLE_KEY)" .env.development

# 检查测试环境
grep -E "(SUPABASE_URL|SUPABASE_ANON_KEY|SUPABASE_SERVICE_ROLE_KEY)" .env.staging

# 检查生产环境
grep -E "(SUPABASE_URL|SUPABASE_ANON_KEY|SUPABASE_SERVICE_ROLE_KEY)" .env.production
```

## 后续需要做的

1. **获取云端 service role key**: 从Supabase控制台获取真实的service role key，替换 `.env.staging` 和 `.env.production` 中的占位符
2. **测试环境切换**: 确保应用在不同环境下能正确连接到对应的数据库
3. **EAS构建测试**: 测试EAS构建是否能正确读取环境变量

---

*修正完成 ✅ - 现在环境配置更加清晰直观了！* 