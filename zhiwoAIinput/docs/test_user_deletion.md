# 用户删除和重新注册测试

## 修复总结

### 问题分析
1. 原软删除函数只是修改了`auth.users`中的邮箱地址，但保留了用户记录
2. 用户注销后仍可以登录，但因为RLS策略阻止访问profiles表而报错
3. 相同邮箱无法重新注册，因为auth.users记录仍然存在
4. **关键问题**：`ON DELETE CASCADE`约束会在删除auth.users时自动删除所有相关数据

### 解决方案
1. **真正删除auth.users记录**：让用户无法再用原邮箱登录
2. **修复外键约束**：避免CASCADE删除我们想保留的数据
3. **保留public表软删除记录**：用于数据分析
4. **更新唯一约束**：只对未删除用户强制邮箱/用户名唯一性
5. **优化注册触发器**：处理已删除用户的邮箱重复使用

### 修改内容

#### 1. 外键约束修复 (20250116000005_fix_cascade_constraints.sql)
```sql
-- 移除profiles表的CASCADE约束，避免删除auth.users时删除profiles记录
ALTER TABLE public.profiles DROP CONSTRAINT profiles_id_fkey;

-- 其他表改为SET NULL或重新引用profiles表
ALTER TABLE public.sessions ADD CONSTRAINT sessions_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;
```

#### 2. 软删除函数 (20250116000006_update_soft_delete_final.sql)
```sql
-- 软删除profiles和相关记录，然后删除auth.users
UPDATE profiles SET is_deleted_by_user = true, deleted_at = NOW();
DELETE FROM auth.users WHERE id = target_user_id;
```

#### 3. 数据库约束 (20250116000004_update_rls_policies.sql)
```sql
-- 部分唯一索引：只对未删除用户强制唯一性
CREATE UNIQUE INDEX profiles_email_unique_active 
ON public.profiles (email) 
WHERE (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL);
```

#### 4. 注册触发器优化
处理已删除用户邮箱的重复使用，为新用户创建全新记录。

## 测试步骤

### 测试场景1：用户注销
1. 使用邮箱 `<EMAIL>` 注册并登录
2. 创建一些数据（会话、自定义模板等）
3. 执行"注销账号"操作
4. 验证：
   - [ ] 用户无法再用原邮箱登录
   - [ ] 本地数据被清空
   - [ ] profiles表有软删除标记
   - [ ] auth.users记录被删除

### 测试场景2：相同邮箱重新注册
1. 用相同邮箱 `<EMAIL>` 重新注册
2. 验证：
   - [ ] 注册成功
   - [ ] 创建了新的用户记录（不同的user_id）
   - [ ] 可以正常登录和使用
   - [ ] 原来的数据不可见（因为是新用户）

### 测试场景3：数据隔离
1. 验证新用户看不到被删除用户的数据
2. 验证已删除用户的数据在数据库中仍然存在（用于分析）

## 数据库验证查询

### 检查软删除记录
```sql
-- 查看被软删除的用户
SELECT id, email, is_deleted_by_user, deleted_at 
FROM profiles 
WHERE is_deleted_by_user = true;

-- 查看auth.users表是否还有记录
SELECT id, email FROM auth.users 
WHERE email = '<EMAIL>';
```

### 检查数据完整性
```sql
-- 验证邮箱唯一性约束
SELECT email, COUNT(*) 
FROM profiles 
WHERE is_deleted_by_user = false OR is_deleted_by_user IS NULL
GROUP BY email 
HAVING COUNT(*) > 1;
```

## 预期结果
- 用户注销后无法再登录
- 相同邮箱可以重新注册成为新用户
- 数据分析需求得到满足（保留软删除记录）
- 没有数据冲突或权限错误 