import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { syncSystemTemplates } from '@/services/templateSyncService';
import { log, warn, error as logError } from '@/services/logService';

// 存储键名
const STORAGE_KEYS = {
  USER_TEMPLATES: 'knowme_user_templates',
  DEFAULT_TEMPLATE_ID: 'knowme_default_template_id'
};

// 模板数据接口
export interface Template {
  id: string;
  title: string;
  description: string;
  name_key?: string;
  description_key?: string;
  prompt: string;
  isSystem: boolean;
  isDefault?: boolean;
  color?: string;
  borderColor?: string;
  backgroundColor?: string;
  category?: string; // 模板分类
  isVipOnly?: boolean; // 是否为VIP专用模板
  isSynced?: boolean; // 是否已同步到云端（仅对自定义模板有意义）
  isHidden?: boolean; // 是否隐藏（根据用户偏好设置）
  displayOrder?: number; // 显示顺序（根据用户偏好设置）
  icon?: string; // 模板图标（emoji或图标名）
}

// 切片状态接口
interface TemplateState {
  templates: Template[];
  defaultTemplateId: string;
  loading: boolean;
  error: string | null;
}

// 初始状态
const initialState: TemplateState = {
  templates: [],
  defaultTemplateId: "text_optimize",
  loading: true,
  error: null
};

// 异步thunk: 加载模板
export const loadTemplates = createAsyncThunk(
  'template/loadTemplates',
  async (_, { rejectWithValue }) => {
    try {
      // 先尝试同步用户模板（如果用户是VIP）
      try {
        const { syncUserTemplates } = await import('@/services/templateSyncService');
        const syncedTemplates = await syncUserTemplates();
        if (syncedTemplates.length > 0) {
          log('[TemplateSlice] 成功同步用户模板:', syncedTemplates.length, '个');
        }
      } catch (syncError) {
        warn('[TemplateSlice] 同步用户模板失败，使用本地数据:', syncError);
      }
      
      // 获取所有模板（现在已包含同步的数据）
      const { getAllTemplates, ensureDefaultTemplateExists } = await import('@/services/templateService');
      const allTemplates = await getAllTemplates();

      // 确保默认模板存在
      await ensureDefaultTemplateExists();

      // 加载默认模板ID
      let defaultTemplateId = await AsyncStorage.getItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID);
      log('[TemplateSlice] loadTemplates - 从AsyncStorage读取的默认模板ID:', defaultTemplateId);
      
      // 如果没有设置默认模板，使用text_optimize模板的实际ID
      if (!defaultTemplateId) {
        // 查找text_optimize模板的实际ID
        const textOptimizeTemplate = allTemplates.find(t => t.id === 'text_optimize' || (t as any).semanticId === 'text_optimize');
        if (textOptimizeTemplate) {
          defaultTemplateId = textOptimizeTemplate.id;
          log('[TemplateSlice] 没有设置默认模板，使用text_optimize模板ID:', defaultTemplateId);
          // 保存到AsyncStorage，确保设置持久化
          await AsyncStorage.setItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID, defaultTemplateId);
        } else {
          // 如果找不到text_optimize模板，使用第一个可用模板
          defaultTemplateId = allTemplates.length > 0 ? allTemplates[0].id : "text_optimize";
          log('[TemplateSlice] 找不到text_optimize模板，使用第一个可用模板ID:', defaultTemplateId);
          // 保存到AsyncStorage，确保设置持久化
          await AsyncStorage.setItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID, defaultTemplateId);
        }
      }
      
      // 确保模板已正确设置isDefault标记
      const templatesWithDefault = allTemplates.map(template => ({
        ...template,
        isDefault: template.id === defaultTemplateId
      }));
      
      return {
        templates: templatesWithDefault,
        defaultTemplateId
      };
    } catch (error) {
      // 优化：如果本地有缓存数据，返回fulfilled而不是rejected
      try {
        const { getAllTemplates } = await import('@/services/templateService');
        const allTemplates = await getAllTemplates();
        if (allTemplates && allTemplates.length > 0) {
          // 查找默认模板ID
          let defaultTemplateId = await AsyncStorage.getItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID);
          if (!defaultTemplateId) {
            const textOptimizeTemplate = allTemplates.find(t => t.id === 'text_optimize' || (t as any).semanticId === 'text_optimize');
            if (textOptimizeTemplate) {
              defaultTemplateId = textOptimizeTemplate.id;
            } else {
              defaultTemplateId = allTemplates.length > 0 ? allTemplates[0].id : "text_optimize";
            }
          }
          const templatesWithDefault = allTemplates.map(template => ({
            ...template,
            isDefault: template.id === defaultTemplateId
          }));
          return {
            templates: templatesWithDefault,
            defaultTemplateId
          };
        }
      } catch (fallbackError) {
        // fallback也失败，走reject
      }
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('加载模板失败');
    }
  }
);

// 异步thunk: 保存模板
export const saveTemplate = createAsyncThunk(
  'template/saveTemplate',
  async (template: Template, { rejectWithValue, getState }) => {
    try {
      const state = getState() as { template: TemplateState };
      const { templates } = state.template;
      
      // 检查是否存在相同ID的模板
      const existingIndex = templates.findIndex(t => t.id === template.id);
      
      let updatedTemplates: Template[];
      
      if (existingIndex >= 0) {
        // 更新现有模板
        updatedTemplates = templates.map((t, index) => 
          index === existingIndex ? { ...template } : t
        );
      } else {
        // 添加新模板
        updatedTemplates = [...templates, template];
      }
      
      // 获取用户自定义模板
      const userTemplates = updatedTemplates.filter(t => !t.isSystem);
      
      // 保存到AsyncStorage
      await AsyncStorage.setItem(STORAGE_KEYS.USER_TEMPLATES, JSON.stringify(userTemplates));
      
      // VIP用户：同步到云端
      if (!template.isSystem) {
        try {
          const { getVipStatus } = await import('@/services/storageService');
          const { saveUserTemplateToCloud } = await import('@/services/templateSyncService');
          
          const vipStatus = await getVipStatus();
          if (vipStatus.isVip) {
            log('[TemplateSlice] VIP用户，同步模板到云端:', template.title);
            
            // 转换为LocalTemplate格式
            const localTemplate = {
              id: template.id,
              title: template.title,
              description: template.description,
              prompt: template.prompt,
              isSystem: template.isSystem,
              color: template.color,
              borderColor: template.borderColor,
              backgroundColor: template.backgroundColor,
              position: 999 // 用户模板排在最后
            };
            
            const cloudSaveSuccess = await saveUserTemplateToCloud(localTemplate);
            if (cloudSaveSuccess) {
              log('[TemplateSlice] 成功同步模板到云端');
              // 更新同步状态
              if (existingIndex >= 0) {
                updatedTemplates[existingIndex] = { ...template, isSynced: true };
              } else {
                updatedTemplates[updatedTemplates.length - 1] = { ...template, isSynced: true };
              }
            } else {
              warn('[TemplateSlice] 同步模板到云端失败');
            }
          } else {
            log('[TemplateSlice] 非VIP用户，跳过云端同步');
          }
        } catch (cloudError) {
          logError('[TemplateSlice] 云端同步模板失败:', cloudError);
          // 不阻塞本地保存操作
        }
      }
      
      // 如果是默认模板，更新默认模板ID
      if (template.isDefault) {
        await AsyncStorage.setItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID, template.id);
        
        // 确保只有一个默认模板
        updatedTemplates = updatedTemplates.map(t => ({
          ...t,
          isDefault: t.id === template.id
        }));
      }
      
      return updatedTemplates;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('保存模板失败');
    }
  }
);

// 异步thunk: 删除模板
export const deleteTemplate = createAsyncThunk(
  'template/deleteTemplate',
  async (templateId: string, { rejectWithValue, getState }) => {
    try {
      const state = getState() as { template: TemplateState };
      const { templates, defaultTemplateId } = state.template;
      
      // 查找要删除的模板
      const templateToDelete = templates.find(t => t.id === templateId);
      
      // 如果模板不存在或是系统模板，拒绝删除
      if (!templateToDelete || templateToDelete.isSystem) {
        return rejectWithValue('无法删除系统模板');
      }
      
      // 如果是默认模板，先找到一个系统模板作为新的默认模板
      let newDefaultId = defaultTemplateId;
      if (templateToDelete.isDefault) {
        const firstSystemTemplate = templates.find(t => t.isSystem);
        if (firstSystemTemplate) {
          newDefaultId = firstSystemTemplate.id;
          // 立即更新默认模板ID
          await AsyncStorage.setItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID, newDefaultId);
          log(`[TemplateSlice] 已设置新的默认模板: ${firstSystemTemplate.title}`);
        }
      }
      
      // VIP用户：从云端删除模板
      try {
        const { getVipStatus } = await import('@/services/storageService');
        const { deleteUserTemplateFromCloud } = await import('@/services/templateSyncService');
        
        const vipStatus = await getVipStatus();
        if (vipStatus.isVip) {
          log('[TemplateSlice] VIP用户，从云端删除模板:', templateId);
          const cloudDeleteSuccess = await deleteUserTemplateFromCloud(templateId);
          if (cloudDeleteSuccess) {
            log('[TemplateSlice] 成功从云端删除模板');
          } else {
            warn('[TemplateSlice] 从云端删除模板失败');
          }
        } else {
          log('[TemplateSlice] 非VIP用户，跳过云端删除');
        }
      } catch (cloudError) {
        logError('[TemplateSlice] 云端删除模板失败:', cloudError);
        // 不阻塞本地删除操作
      }
      
      // 过滤掉要删除的模板
      const updatedTemplates = templates.filter(t => t.id !== templateId);
      
      // 获取用户自定义模板
      const userTemplates = updatedTemplates.filter(t => !t.isSystem);
      
      // 保存到AsyncStorage
      await AsyncStorage.setItem(STORAGE_KEYS.USER_TEMPLATES, JSON.stringify(userTemplates));
      
      // 更新模板的默认状态
      const templatesWithUpdatedDefault = updatedTemplates.map(t => ({
        ...t,
        isDefault: t.id === newDefaultId
      }));
      
      return {
        templates: templatesWithUpdatedDefault,
        defaultTemplateId: newDefaultId
      };
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('删除模板失败');
    }
  }
);

// 异步thunk: 设置默认模板
export const setDefaultTemplate = createAsyncThunk(
  'template/setDefaultTemplate',
  async (templateId: string, { rejectWithValue, getState }) => {
    try {
      const state = getState() as { template: TemplateState };
      const { templates } = state.template;
      
      // 确保模板存在
      const templateExists = templates.some(t => t.id === templateId);
      if (!templateExists) {
        return rejectWithValue('模板不存在');
      }
      
      // 保存到AsyncStorage
      await AsyncStorage.setItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID, templateId);
      
      // 更新模板的默认状态
      const updatedTemplates = templates.map(t => ({
        ...t,
        isDefault: t.id === templateId
      }));
      
      return {
        templates: updatedTemplates,
        defaultTemplateId: templateId
      };
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('设置默认模板失败');
    }
  }
);

// 异步thunk: 强制刷新模板
export const forceRefreshTemplates = createAsyncThunk(
  'template/forceRefreshTemplates',
  async (_, { rejectWithValue }) => {
    try {
      log('[TemplateSlice] 开始强制刷新模板...');
      
      // 调用强制刷新服务
      const { forceRefreshTemplates } = await import('@/services/templateSyncService');
      await forceRefreshTemplates();
      
      // 重新获取所有模板（强制刷新）
      const { getAllTemplates, ensureDefaultTemplateExists } = await import('@/services/templateService');
      const allTemplates = await getAllTemplates(true);
      
      // 确保默认模板存在
      await ensureDefaultTemplateExists();
      
      // 加载默认模板ID
      let defaultTemplateId = await AsyncStorage.getItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID);
      log('[TemplateSlice] forceRefreshTemplates - 从AsyncStorage读取的默认模板ID:', defaultTemplateId);
      
      // 如果没有设置默认模板，使用text_optimize模板的实际ID
      if (!defaultTemplateId) {
        // 查找text_optimize模板的实际ID
        const textOptimizeTemplate = allTemplates.find(t => t.id === 'text_optimize' || (t as any).semanticId === 'text_optimize');
        if (textOptimizeTemplate) {
          defaultTemplateId = textOptimizeTemplate.id;
          log('[TemplateSlice] 没有设置默认模板，使用text_optimize模板ID:', defaultTemplateId);
          // 保存到AsyncStorage，确保设置持久化
          await AsyncStorage.setItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID, defaultTemplateId);
        } else {
          // 如果找不到text_optimize模板，使用第一个可用模板
          defaultTemplateId = allTemplates.length > 0 ? allTemplates[0].id : "text_optimize";
          log('[TemplateSlice] 找不到text_optimize模板，使用第一个可用模板ID:', defaultTemplateId);
          // 保存到AsyncStorage，确保设置持久化
          await AsyncStorage.setItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID, defaultTemplateId);
        }
      }
      
      // 确保模板已正确设置isDefault标记
      const templatesWithDefault = allTemplates.map(template => ({
        ...template,
        isDefault: template.id === defaultTemplateId
      }));
      
      return {
        templates: templatesWithDefault,
        defaultTemplateId
      };
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('强制刷新模板失败');
    }
  }
);

// 创建切片
const templateSlice = createSlice({
  name: 'template',
  initialState,
  reducers: {
    resetTemplateError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    // 加载模板
    builder.addCase(loadTemplates.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(loadTemplates.fulfilled, (state, action) => {
      state.loading = false;
      state.templates = action.payload.templates;
      state.defaultTemplateId = action.payload.defaultTemplateId;
    });
    builder.addCase(loadTemplates.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
    
    // 保存模板
    builder.addCase(saveTemplate.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(saveTemplate.fulfilled, (state, action) => {
      state.loading = false;
      state.templates = action.payload;
      // 更新默认模板ID
      const defaultTemplate = action.payload.find(t => t.isDefault);
      if (defaultTemplate) {
        state.defaultTemplateId = defaultTemplate.id;
      }
    });
    builder.addCase(saveTemplate.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
    
    // 删除模板
    builder.addCase(deleteTemplate.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(deleteTemplate.fulfilled, (state, action) => {
      state.loading = false;
      state.templates = action.payload.templates;
      state.defaultTemplateId = action.payload.defaultTemplateId;
    });
    builder.addCase(deleteTemplate.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
    
    // Sync cases
    builder.addCase(setDefaultTemplate.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(setDefaultTemplate.fulfilled, (state, action) => {
      state.loading = false;
      state.templates = action.payload.templates;
      state.defaultTemplateId = action.payload.defaultTemplateId;
    });
    builder.addCase(setDefaultTemplate.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
    
    // 强制刷新模板
    builder.addCase(forceRefreshTemplates.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(forceRefreshTemplates.fulfilled, (state, action) => {
      state.loading = false;
      state.templates = action.payload.templates;
      state.defaultTemplateId = action.payload.defaultTemplateId;
    });
    builder.addCase(forceRefreshTemplates.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
  }
});

export const { resetTemplateError } = templateSlice.actions;

export default templateSlice.reducer; 