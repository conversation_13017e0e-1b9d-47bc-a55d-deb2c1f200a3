import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { authService, userService } from '../../services/supabaseService';
import { fetchUserSubscription } from '../slices/subscriptionSlice';
import { resetUserSettings } from '../../services/storageService';
import { log, error as logError } from '@/services/logService';

// 定义用户类型
interface User {
  id: string;
  email: string;
  profile?: any;
  // 登录方式
  providerType?: 'wechat' | 'apple' | 'google' | 'email' | 'phone' | string;
  // 用户头像URL
  avatarUrl?: string;
  // 显示名称
  displayName?: string;
  // 用户名
  username?: string;
}

// 定义认证状态
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isVIP: boolean;
  isLoading: boolean;
  error: string | null;
}

// 初始状态
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isVIP: false,
  isLoading: false,
  error: null,
};

// 异步登录动作
export const login = createAsyncThunk(
  'auth/login',
  async ({ email, password }: { email: string; password: string }, { rejectWithValue }) => {
    try {
      const { data, error } = await authService.signIn(email, password);
      
      if (error) {
        return rejectWithValue(error.message);
      }
      
      if (data?.user) {
        // 获取用户资料
        const { data: profileData } = await userService.getUserProfile(data.user.id);
        return {
          id: data.user.id,
          email: data.user.email || '',
          profile: profileData,
          // 设置为邮箱登录方式
          providerType: 'email',
          // 如果有avatar_url也设置头像
          avatarUrl: data.user.user_metadata?.avatar_url || '',
        };
      }
      
      return null;
    } catch (error: any) {
      return rejectWithValue(error.message || '登录失败');
    }
  }
);

// 异步注册动作
export const register = createAsyncThunk(
  'auth/register',
  async ({ email, password }: { email: string; password: string }, { rejectWithValue }) => {
    try {
      const { data, error } = await authService.signUp(email, password);
      
      if (error) {
        return rejectWithValue(error.message);
      }
      
      if (data?.user) {
        // 创建用户资料
        await userService.createUserProfile({
          id: data.user.id,
          email: data.user.email || email,
          created_at: new Date().toISOString(),
        });
        
        return {
          id: data.user.id,
          email: data.user.email || email,
          // 设置为邮箱注册方式
          providerType: 'email',
        };
      }
      
      return null;
    } catch (error: any) {
      return rejectWithValue(error.message || '注册失败');
    }
  }
);

// 检查会话
export const checkSession = createAsyncThunk(
  'auth/checkSession',
  async (_, { rejectWithValue, dispatch }) => {
    log('[AUTH] 开始检查用户会话状态');
    try {
      // 确保在会话检查前设置保持标签状态的标志
      if (typeof global.setPreserveTabAfterLogin === 'function') {
        global.setPreserveTabAfterLogin(true);
        log('[AUTH] 在会话检查前设置 preserveTabAfterLogin = true');
      }
      
      const { data, error } = await authService.getSession();
      
      if (error) {
        log('[AUTH] 会话检查错误:', error.message);
        return rejectWithValue(error.message);
      }
      
      if (data?.session?.user) {
        const user = data.session.user;
        log('[AUTH] 会话检查成功，已找到有效会话, 用户ID:', user.id);
        
        // 获取用户资料
        const { data: profileData, error: profileError } = await userService.getUserProfile(user.id);
        
        // 如果用户资料不存在，说明数据库中没有此用户，应该登出
        if (profileError && (profileError.code === 'PGRST116' || profileError.message?.includes('no rows'))) {
          log('[AUTH] 用户在数据库中不存在，强制登出');
          await authService.signOut();
          return rejectWithValue('用户在数据库中不存在');
        }
        
        if (profileError) {
          log('[AUTH] 获取用户资料错误:', profileError);
          return rejectWithValue(profileError.message || '获取用户资料失败');
        }
        
        log('[AUTH] 获取到用户资料');
        
        // 如果已登录，获取用户订阅状态以更新VIP状态
        dispatch(fetchUserSubscription(user.id));
        
        // 打印登录信息和元数据
        log('[AUTH] 用户身份验证信息:', {
          id: user.id,
          provider: user.app_metadata?.provider,
          avatar: user.user_metadata?.avatar_url
        });
        
        return {
          id: user.id,
          email: user.email || '',
          profile: profileData,
          // 从用户信息中提取授权提供方信息
          providerType: user.app_metadata?.provider || 'email',
          // 如果有avatar_url也设置头像
          avatarUrl: user.user_metadata?.avatar_url || '',
        };
      }
      
      log('[AUTH] 会话检查完成，没有找到有效会话');
      return null;
    } catch (error: any) {
      log('[AUTH] 会话检查发生异常:', error.message || '未知错误');
      return rejectWithValue(error.message || '获取会话失败');
    }
  }
);

// 退出登录
export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      const { error } = await authService.signOut();
      
      if (error) {
        return rejectWithValue(error.message);
      }
      
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message || '退出登录失败');
    }
  }
);

// 创建Auth Slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User | null>) => {
      state.user = action.payload;
      state.isAuthenticated = !!action.payload;
    },
    setVIPStatus: (state, action: PayloadAction<boolean>) => {
      const wasVIP = state.isVIP;
      const isVIP = action.payload;
      
      state.isVIP = isVIP;
      
      // 如果从非VIP变为VIP，触发删除记录和模板同步到云端
      if (!wasVIP && isVIP) {
        log('[AUTH] 用户升级为VIP，开始同步删除记录和本地模板到云端...');
        setTimeout(() => {
          import('../../services/templateService').then(({ syncDeletedRecordsToCloud, syncLocalTemplatesToCloud }) => {
            // 优先同步删除记录
            syncDeletedRecordsToCloud()
              .then((deletedCount) => {
                if (deletedCount > 0) {
                  log(`[AUTH] VIP升级时已同步 ${deletedCount} 条删除记录到云端`);
                }
                // 然后同步本地模板
                return syncLocalTemplatesToCloud();
              })
              .catch(error => {
                logError('[AUTH] VIP升级后同步失败:', error);
              });
          });
        }, 500);
      }
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // 登录状态处理
    builder
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = !!action.payload;
        
        // 登录成功后同步用户模板和偏好设置
        if (action.payload) {
          // 使用异步函数，避免阻塞redux状态更新，等待VIP状态更新
          setTimeout(() => {
            // 同步模板和偏好设置
            Promise.all([
              import('../../services/templateService').then(({ syncUserTemplatesOnLogin }) => 
                syncUserTemplatesOnLogin()
              ),
              import('../../services/templatePreferenceService').then(({ syncUserPreferencesOnLogin }) => 
                syncUserPreferencesOnLogin()
              ),
              // 添加极速模式同步 - 本地优先同步到云端
              import('../../services/fastModeSyncService').then(({ fastModeSyncService }) => 
                fastModeSyncService.forceSyncFromLocal()
              )
            ]).catch(error => {
              logError('[AUTH] 登录后数据同步失败:', error);
            });
          }, 1000);
        }
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
    
    // 注册状态处理
    builder
      .addCase(register.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = !!action.payload;
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
    
    // 会话检查状态处理
    builder
      .addCase(checkSession.pending, (state) => {
        state.isLoading = true;
        log('[AUTH REDUCER] checkSession.pending - isLoading = true');
      })
      .addCase(checkSession.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = !!action.payload;
        log('[AUTH REDUCER] checkSession.fulfilled - 更新认证状态:', {
          isAuthenticated: !!action.payload,
          hasUser: !!action.payload,
          userId: action.payload?.id
        });
        
        // 会话检查成功后同步用户模板和偏好设置
        if (action.payload) {
          // 使用异步函数，避免阻塞redux状态更新，等待VIP状态更新
          setTimeout(() => {
            // 同步模板和偏好设置
            Promise.all([
              import('../../services/templateService').then(({ syncUserTemplatesOnLogin }) => 
                syncUserTemplatesOnLogin()
              ),
              import('../../services/templatePreferenceService').then(({ syncUserPreferencesOnLogin }) => 
                syncUserPreferencesOnLogin()
              )
            ]).catch(error => {
              logError('[AUTH] 会话检查后数据同步失败:', error);
            });
          }, 1000);
        }
      })
      .addCase(checkSession.rejected, (state) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
        log('[AUTH REDUCER] checkSession.rejected - 重置认证状态');
        
        // 会话失效时也要重置用户设置
        resetUserSettings();
        
        // 清理语音录音相关资源
        setTimeout(() => {
          import('../../services/expoSpeechService').then((speechService) => {
            speechService.releaseAudioResources(true).catch(error => {
              log('[AUTH] 会话失效时清理语音资源失败，但不影响流程:', error);
            });
          });
        }, 100);
      });
    
    // 退出登录状态处理
    builder
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
        state.isVIP = false;
        
        // 重置用户设置
        resetUserSettings();
        
        // 清理语音录音相关资源，确保退出登录后使用正确的模型
        setTimeout(() => {
          import('../../services/expoSpeechService').then((speechService) => {
            speechService.releaseAudioResources(true).catch(error => {
              log('[AUTH] 退出登录时清理语音资源失败，但不影响流程:', error);
            });
          });
        }, 100);
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setUser, setVIPStatus, clearError } = authSlice.actions;
export default authSlice.reducer; 