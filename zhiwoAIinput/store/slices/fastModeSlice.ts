import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { saveUserSettings, getUserSettings } from '../../services/storageService';
import supabaseService from '../../services/supabaseService';
import fastModeSyncService from '../../services/fastModeSyncService';
import { error as logError } from '@/services/logService';

interface FastModeState {
  isFastModeOn: boolean;
  isLoading: boolean;
  error: string | null;
  isSyncing: boolean; // 新增：用于跟踪云端同步状态
}

const initialState: FastModeState = {
  isFastModeOn: false,
  isLoading: false,
  error: null,
  isSyncing: false,
};

// 异步thunk：从本地存储加载极速模式设置
export const loadFastModeFromStorage = createAsyncThunk(
  'fastMode/loadFromStorage',
  async () => {
    const settings = await getUserSettings();
    return settings.fastMode || false;
  }
);

// 异步thunk：立即更新本地设置（本地优先）
export const toggleFastModeLocal = createAsyncThunk(
  'fastMode/toggleLocal',
  async (fastMode: boolean) => {
    try {
      // 立即保存到本地存储
      await saveUserSettings({ fastMode });
      
      // 使用后台同步服务进行云端同步（完全异步，不阻塞）
      const { data: sessionData } = await supabaseService.supabase.auth.getSession();
      if (sessionData?.session?.user?.id) {
        // 异步加入同步队列，不等待结果
        fastModeSyncService.queueSync(sessionData.session.user.id, fastMode);
      }
      
      return fastMode;
    } catch (error) {
      logError('保存极速模式设置到本地失败:', error);
      throw error;
    }
  }
);

// 强制同步本地设置到云端（用于登录后的首次同步）
export const forceSyncLocalToCloud = createAsyncThunk(
  'fastMode/forceSyncLocal',
  async () => {
    await fastModeSyncService.forceSyncFromLocal();
    return true;
  }
);

// 异步thunk：从云端同步极速模式设置（仅在应用启动或登录时使用）
export const syncFastModeFromCloud = createAsyncThunk(
  'fastMode/syncFromCloud',
  async (_, { rejectWithValue }) => {
    try {
      // 使用后台同步服务进行同步
      const synced = await fastModeSyncService.syncFromCloud();
      
      if (synced) {
        // 如果有更新，重新读取本地设置
        const localSettings = await getUserSettings();
        return localSettings.fastMode || false;
      }
      
      // 没有更新，返回当前本地设置
      const localSettings = await getUserSettings();
      return localSettings.fastMode || false;
    } catch (error) {
      logError('从云端同步极速模式设置失败:', error);
      return rejectWithValue('同步设置失败');
    }
  }
);

const fastModeSlice = createSlice({
  name: 'fastMode',
  initialState,
  reducers: {
    // 立即切换状态（用于即时响应）
    toggleFastModeImmediate: (state) => {
      state.isFastModeOn = !state.isFastModeOn;
    },
    setFastMode: (state, action: PayloadAction<boolean>) => {
      state.isFastModeOn = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 从本地存储加载
      .addCase(loadFastModeFromStorage.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadFastModeFromStorage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isFastModeOn = action.payload;
      })
      .addCase(loadFastModeFromStorage.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '加载设置失败';
      })
      // 本地优先切换
      .addCase(toggleFastModeLocal.pending, (state) => {
        // 不设置 isLoading，保持界面响应
        state.error = null;
      })
      .addCase(toggleFastModeLocal.fulfilled, (state, action) => {
        state.isFastModeOn = action.payload;
      })
      .addCase(toggleFastModeLocal.rejected, (state, action) => {
        state.error = action.error.message || '保存设置失败';
        // 如果本地保存失败，回滚状态
        state.isFastModeOn = !state.isFastModeOn;
      })
      // 强制同步到云端（登录后使用）
      .addCase(forceSyncLocalToCloud.pending, (state) => {
        state.isSyncing = true;
      })
      .addCase(forceSyncLocalToCloud.fulfilled, (state) => {
        state.isSyncing = false;
      })
      .addCase(forceSyncLocalToCloud.rejected, (state) => {
        state.isSyncing = false;
      })
      // 从云端同步
      .addCase(syncFastModeFromCloud.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(syncFastModeFromCloud.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isFastModeOn = action.payload;
      })
      .addCase(syncFastModeFromCloud.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { 
  toggleFastModeImmediate,
  setFastMode,
  clearError
} = fastModeSlice.actions;

// 统一的切换操作，避免状态不一致
export const toggleFastMode = createAsyncThunk(
  'fastMode/toggle',
  async (_, { getState, dispatch }) => {
    const currentState = (getState() as any).fastMode.isFastModeOn;
    const targetState = !currentState;
    
    // 立即更新UI状态
    dispatch(setFastMode(targetState));
    
    // 异步保存到本地和云端
    await dispatch(toggleFastModeLocal(targetState));
    
    return targetState;
  }
);

export default fastModeSlice.reducer; 