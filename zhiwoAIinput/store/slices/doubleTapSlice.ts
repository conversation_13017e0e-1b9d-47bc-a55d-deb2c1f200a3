import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface DoubleTapState {
  lastPressTime: number;
  isResetting: boolean;
  pressCount: number;
}

const initialState: DoubleTapState = {
  lastPressTime: 0,
  isResetting: false,
  pressCount: 0,
};

const doubleTapSlice = createSlice({
  name: 'doubleTap',
  initialState,
  reducers: {
    incrementPressCount: (state) => {
      state.pressCount += 1;
    },
    resetPressCount: (state) => {
      state.pressCount = 0;
    },
    updateLastPressTime: (state, action: PayloadAction<number>) => {
      state.lastPressTime = action.payload;
    },
    setIsResetting: (state, action: PayloadAction<boolean>) => {
      state.isResetting = action.payload;
    },
    resetDoubleTapState: (state) => {
      state.lastPressTime = 0;
      state.isResetting = false;
      state.pressCount = 0;
    },
  },
});

export const { 
  incrementPressCount, 
  resetPressCount,
  updateLastPressTime, 
  setIsResetting, 
  resetDoubleTapState 
} = doubleTapSlice.actions;
export default doubleTapSlice.reducer; 