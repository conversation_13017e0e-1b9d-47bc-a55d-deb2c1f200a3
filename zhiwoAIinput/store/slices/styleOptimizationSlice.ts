import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { saveUserSettings, getUserSettings } from '../../services/storageService';
import supabaseService from '../../services/supabaseService';
import styleOptimizationSyncService from '../../services/styleOptimizationSyncService';
import { error as logError } from '@/services/logService';

interface StyleOptimizationState {
  isStyleOptimizationOn: boolean;
  isLoading: boolean;
  error: string | null;
  isSyncing: boolean; // 用于跟踪云端同步状态
}

const initialState: StyleOptimizationState = {
  isStyleOptimizationOn: true, // 默认开启风格模板优化
  isLoading: false,
  error: null,
  isSyncing: false,
};

// 异步thunk：从本地存储加载风格模板优化设置
export const loadStyleOptimizationFromStorage = createAsyncThunk(
  'styleOptimization/loadFromStorage',
  async () => {
    const settings = await getUserSettings();
    return settings.styleOptimization !== undefined ? settings.styleOptimization : true; // 默认开启
  }
);

// 异步thunk：立即更新本地设置（本地优先）
export const toggleStyleOptimizationLocal = createAsyncThunk(
  'styleOptimization/toggleLocal',
  async (styleOptimization: boolean) => {
    try {
      // 立即保存到本地存储
      await saveUserSettings({ styleOptimization });
      
      // 使用后台同步服务进行云端同步（完全异步，不阻塞）
      const { data: sessionData } = await supabaseService.supabase.auth.getSession();
      if (sessionData?.session?.user?.id) {
        // 异步加入同步队列，不等待结果
        styleOptimizationSyncService.queueSync(sessionData.session.user.id, styleOptimization);
      }
      
      return styleOptimization;
    } catch (error) {
      logError('保存风格模板优化设置到本地失败:', error);
      throw error;
    }
  }
);

// 强制同步本地设置到云端（用于登录后的首次同步）
export const forceSyncLocalToCloud = createAsyncThunk(
  'styleOptimization/forceSyncLocal',
  async () => {
    await styleOptimizationSyncService.forceSyncFromLocal();
    return true;
  }
);

// 异步thunk：从云端同步风格模板优化设置（仅在应用启动或登录时使用）
export const syncStyleOptimizationFromCloud = createAsyncThunk(
  'styleOptimization/syncFromCloud',
  async (_, { rejectWithValue }) => {
    try {
      // 使用后台同步服务进行同步
      const synced = await styleOptimizationSyncService.syncFromCloud();
      
      if (synced) {
        // 如果有更新，重新读取本地设置
        const localSettings = await getUserSettings();
        return localSettings.styleOptimization !== undefined ? localSettings.styleOptimization : true;
      }
      
      // 没有更新，返回当前本地设置
      const localSettings = await getUserSettings();
      return localSettings.styleOptimization !== undefined ? localSettings.styleOptimization : true;
    } catch (error) {
      logError('从云端同步风格模板优化设置失败:', error);
      return rejectWithValue('同步设置失败');
    }
  }
);

const styleOptimizationSlice = createSlice({
  name: 'styleOptimization',
  initialState,
  reducers: {
    // 立即切换状态（用于即时响应）
    toggleStyleOptimizationImmediate: (state) => {
      state.isStyleOptimizationOn = !state.isStyleOptimizationOn;
    },
    setStyleOptimization: (state, action: PayloadAction<boolean>) => {
      state.isStyleOptimizationOn = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 从本地存储加载
      .addCase(loadStyleOptimizationFromStorage.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadStyleOptimizationFromStorage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isStyleOptimizationOn = action.payload;
      })
      .addCase(loadStyleOptimizationFromStorage.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '加载设置失败';
      })
      // 本地优先切换
      .addCase(toggleStyleOptimizationLocal.pending, (state) => {
        // 不设置 isLoading，保持界面响应
        state.error = null;
      })
      .addCase(toggleStyleOptimizationLocal.fulfilled, (state, action) => {
        state.isStyleOptimizationOn = action.payload;
      })
      .addCase(toggleStyleOptimizationLocal.rejected, (state, action) => {
        state.error = action.error.message || '保存设置失败';
        // 如果本地保存失败，回滚状态
        state.isStyleOptimizationOn = !state.isStyleOptimizationOn;
      })
      // 强制同步到云端（登录后使用）
      .addCase(forceSyncLocalToCloud.pending, (state) => {
        state.isSyncing = true;
      })
      .addCase(forceSyncLocalToCloud.fulfilled, (state) => {
        state.isSyncing = false;
      })
      .addCase(forceSyncLocalToCloud.rejected, (state) => {
        state.isSyncing = false;
      })
      // 从云端同步
      .addCase(syncStyleOptimizationFromCloud.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(syncStyleOptimizationFromCloud.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isStyleOptimizationOn = action.payload;
      })
      .addCase(syncStyleOptimizationFromCloud.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string || '同步设置失败';
      });
  }
});

export const { 
  toggleStyleOptimizationImmediate, 
  setStyleOptimization, 
  clearError 
} = styleOptimizationSlice.actions;

// 统一的切换操作，避免状态不一致
export const toggleStyleOptimization = createAsyncThunk(
  'styleOptimization/toggle',
  async (_, { getState, dispatch }) => {
    const currentState = (getState() as any).styleOptimization.isStyleOptimizationOn;
    const targetState = !currentState;
    
    // 立即更新UI状态
    dispatch(setStyleOptimization(targetState));
    
    // 异步保存到本地和云端
    await dispatch(toggleStyleOptimizationLocal(targetState));
    
    return targetState;
  }
);

export default styleOptimizationSlice.reducer; 