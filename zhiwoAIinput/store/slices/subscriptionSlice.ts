import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { subscriptionService, paymentService } from '../../services/supabaseService';

// 定义订阅计划类型
interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
}

// 定义订阅状态类型
interface Subscription {
  id: string;
  userId: string;
  planId: string;
  status: 'active' | 'canceled' | 'expired';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  plan?: SubscriptionPlan;
}

// 定义支付记录类型
interface Payment {
  id: string;
  userId: string;
  amount: number;
  currency: string;
  status: 'succeeded' | 'pending' | 'failed';
  createdAt: string;
  description: string;
}

// 定义订阅状态
interface SubscriptionState {
  currentSubscription: Subscription | null;
  availablePlans: SubscriptionPlan[];
  paymentHistory: Payment[];
  isLoading: boolean;
  error: string | null;
}

// 初始状态
const initialState: SubscriptionState = {
  currentSubscription: null,
  availablePlans: [],
  paymentHistory: [],
  isLoading: false,
  error: null,
};

// 异步获取可用订阅计划
export const fetchSubscriptionPlans = createAsyncThunk(
  'subscription/fetchPlans',
  async (_, { rejectWithValue }) => {
    try {
      const { data, error } = await subscriptionService.getSubscriptionPlans();
      
      if (error) {
        return rejectWithValue(error.message);
      }
      
      return data || [];
    } catch (error: any) {
      return rejectWithValue(error.message || '获取订阅计划失败');
    }
  }
);

// 异步获取用户当前订阅
export const fetchUserSubscription = createAsyncThunk(
  'subscription/fetchUserSubscription',
  async (userId: string, { rejectWithValue }) => {
    try {
      const { data, error } = await subscriptionService.getUserSubscription(userId);
      
      if (error) {
        return rejectWithValue(error.message);
      }
      
      if (!data) {
        return null;
      }
      
      return {
        id: data.id,
        userId: data.user_id,
        planId: data.subscription_plans.id,
        status: data.status,
        currentPeriodStart: data.current_period_start,
        currentPeriodEnd: data.current_period_end,
        plan: {
          id: data.subscription_plans.id,
          name: data.subscription_plans.name,
          description: data.subscription_plans.description,
          price: data.subscription_plans.price,
          currency: data.subscription_plans.currency,
          interval: data.subscription_plans.interval,
          features: data.subscription_plans.features || [],
        },
      };
    } catch (error: any) {
      return rejectWithValue(error.message || '获取用户订阅失败');
    }
  }
);

// 异步获取支付历史
export const fetchPaymentHistory = createAsyncThunk(
  'subscription/fetchPaymentHistory',
  async (userId: string, { rejectWithValue }) => {
    try {
      const { data, error } = await paymentService.getPaymentHistory(userId);
      
      if (error) {
        return rejectWithValue(error.message);
      }
      
      if (!data) {
        return [];
      }
      
      return data.map((payment: any) => ({
        id: payment.id,
        userId: payment.user_id,
        amount: payment.amount,
        currency: payment.currency,
        status: payment.status,
        createdAt: payment.created_at,
        description: payment.description,
      }));
    } catch (error: any) {
      return rejectWithValue(error.message || '获取支付历史失败');
    }
  }
);

// 创建订阅Slice
const subscriptionSlice = createSlice({
  name: 'subscription',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    resetSubscriptionState: () => initialState,
  },
  extraReducers: (builder) => {
    // 获取订阅计划状态处理
    builder
      .addCase(fetchSubscriptionPlans.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSubscriptionPlans.fulfilled, (state, action) => {
        state.isLoading = false;
        state.availablePlans = action.payload;
      })
      .addCase(fetchSubscriptionPlans.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
    
    // 获取用户订阅状态处理
    builder
      .addCase(fetchUserSubscription.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserSubscription.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentSubscription = action.payload;
      })
      .addCase(fetchUserSubscription.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
    
    // 获取支付历史状态处理
    builder
      .addCase(fetchPaymentHistory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPaymentHistory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.paymentHistory = action.payload;
      })
      .addCase(fetchPaymentHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, resetSubscriptionState } = subscriptionSlice.actions;
export default subscriptionSlice.reducer; 