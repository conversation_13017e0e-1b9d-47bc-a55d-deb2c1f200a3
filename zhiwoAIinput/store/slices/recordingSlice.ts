import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import * as SpeechService from '@/services/expoSpeechService';
import * as AIService from '@/services/aiService';
import { saveHistoryRecord, getUseAdvancedTranscription, setUseAdvancedTranscription } from '@/services/storageService';
import { generateId } from '@/utils/helpers';
import { RootState } from '@/store';
import * as HistoryService from '@/services/storageService';
import { HistoryRecord } from '@/services/storageService';
import { logout } from './authSlice';
import { log, error as logError, warn } from '@/services/logService';

// 定义语音记录状态接口
interface RecordingState {
  recordingStatus: 'idle' | 'recording' | 'paused' | 'stopped' | 'processing' | 'error';
  recognizedText: string;
  optimizedText: string | null;
  audioUri: string | null;
  selectedTemplateId: string | null;
  audioLevel: number; // 0-1范围的音量级别
  error: string | null;
  processingAI: boolean;
  lastRecordingTimestamp: number | null;
  pauses: { startTime: number; endTime: number }[];
  currentOptimizeRequestId: string | null; // 当前正在处理的优化请求ID
  useAdvancedTranscription: boolean; // 是否使用高级转写功能（OpenAI）
  // 网络错误状态
  networkError: string | null;
  showNetworkError: boolean;
  // whisper使用限制错误状态
  whisperLimitError: {
    message: string;
    usageInfo: any;
    requiresVipUpgrade: boolean;
  } | null;
  showWhisperLimitModal: boolean;
}

// 初始状态
const initialState: RecordingState = {
  recordingStatus: 'idle',
  recognizedText: '',
  optimizedText: null,
  audioUri: null,
  selectedTemplateId: null,
  audioLevel: 0,
  error: null,
  processingAI: false,
  lastRecordingTimestamp: null,
  pauses: [],
  currentOptimizeRequestId: null,
  useAdvancedTranscription: false, // 默认不使用高级转写
  // 网络错误状态
  networkError: null,
  showNetworkError: false,
  // whisper使用限制错误状态
  whisperLimitError: null,
  showWhisperLimitModal: false
};

// 在Redux外部维护AbortController实例
let currentAbortController: AbortController | null = null;

// 音频暂停点
interface PauseMarker {
  startTime: number;
  endTime: number;
}

// 异步Thunk: 开始录音
export const startRecording = createAsyncThunk(
  'recording/startRecording',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      // 确保先清空之前的识别文本
      dispatch(updateRecognizedText(''));
      
      log('开始录音并设置回调...');
      
      // 开始录音并设置回调
      const status = await SpeechService.startRecording({
        onStart: () => {
          log('语音识别开始');
          // 确保状态更新为recording
          dispatch({ type: 'recording/startRecording/pending' });
        },
        onRecognizing: (text) => {
          // 检查是否是状态更新消息（来自whisper模式的状态更新）
          if (text.startsWith('__status_update__:')) {
            // 这是状态更新消息，不应该更新到recognizedText
            // 可以在UI层面显示，但不要保存到Redux状态
            log('收到状态更新消息:', text);
            // 不调用dispatch(updateRecognizedText)，避免更新Redux状态
          } else {
            // 实际的转写内容，正常更新到Redux
            log('正在识别，文本长度:', text.length);
            dispatch(updateRecognizedText(text));
          }
        },
        onResult: (text) => {
          // 更新最终识别文本
          log('识别结果，文本长度:', text.length);
          dispatch(updateRecognizedText(text));
        },
        onError: (error: any) => {
          // 改进错误处理，防止关键错误导致应用崩溃
          logError('语音识别错误，但继续保持录音状态:', error);
          
          // 特殊处理：检查是否是whisper每日使用限制错误
          if (error && error.isWhisperLimit) {
            log('[RecordingSlice] 检测到whisper使用限制错误，设置特殊错误状态');
            
            try {
              // 设置特殊的错误状态，用于触发VIP升级引导
              dispatch(setError(null)); // 先清除一般错误
              dispatch(setWhisperLimitError({
                message: error.message,
                usageInfo: error.usageInfo,
                requiresVipUpgrade: true
              }));
              return;
            } catch (e) {
              warn('设置whisper限制错误状态失败:', e);
            }
          }
          
          try {
            dispatch(setError(error.message || '语音识别出现问题，但录音仍在继续'));
          } catch (e) {
            warn('设置错误状态失败:', e);
          }
        },
        onAudioLevel: (level) => {
          // 更新音量级别
          dispatch(updateAudioLevel(level));
        }
      });
      
      if (!status) {
        return rejectWithValue('开始录音失败');
      }
      
      return status;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('开始录音失败');
    }
  }
);

// 异步Thunk: 暂停录音
export const pauseRecording = createAsyncThunk(
  'recording/pauseRecording',
  async (_, { rejectWithValue, getState }) => {
    try {
      // 暂停前，保存当前状态
      const state = getState() as { recording: RecordingState };
      const { recognizedText } = state.recording;
      
      const status = await SpeechService.pauseRecording();
      if (status === 'error') {
        return rejectWithValue('暂停录音失败');
      }
      
      // 返回当前识别的文本，确保不丢失
      return {
        status,
        recognizedText
      };
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('暂停录音失败');
    }
  }
);

// 异步Thunk: 继续录音
export const resumeRecording = createAsyncThunk(
  'recording/resumeRecording',
  async (_, { dispatch, getState, rejectWithValue }) => {
    try {
      // 获取当前已保存的文本
      const state = getState() as RootState;
      const { recognizedText } = state.recording;
      
      log('继续录音，当前已保存的文本长度:', recognizedText?.length || 0);
      
      // 记录继续录音的时间点
      dispatch(addPause({ endTime: new Date().getTime() }));
      
      // 继续录音 - speechService内部会处理文本的保存和合并
      const status = await SpeechService.resumeRecording();
      
      if (status === 'error') {
        return rejectWithValue('继续录音失败');
      }
      
      return status;
    } catch (error) {
      logError('恢复录音失败:', error);
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('继续录音失败');
    }
  }
);

// 异步Thunk: 停止录音
export const stopRecording = createAsyncThunk(
  'recording/stopRecording',
  async (customText: string | undefined, { rejectWithValue, dispatch, getState }) => {
    try {
      // 获取当前状态
      const state = getState() as RootState;
      const { recognizedText: currentReduxText } = state.recording;
      
      // 如果提供了自定义文本（来自UI），它应该优先，因为它可能包含所有段落
      const textToUse = customText || currentReduxText || '';
      log('停止录音，使用文本长度:', textToUse.length);
      
      // Redux层不再直接调用语音服务的stopRecording，避免重复调用
      // 改为直接获取录音状态信息，由Hook层负责实际的转写处理
      const status: 'stopped' | 'error' = 'stopped'; // 直接返回stopped状态
      
      // 由于我们不再直接调用服务，status总是'stopped'，移除错误检查
      
      // 获取录音文件路径
      const audioUri = SpeechService.getCurrentRecordingUri();
      // 获取暂停标记点
      const pauseMarkers = SpeechService.getRecordingPauseMarkers();
      
      log('录音已停止，文件路径:', audioUri, '暂停点:', pauseMarkers);
      
      return {
        status,
        audioUri,
        pauseMarkers
      };
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('停止录音失败');
    }
  }
);

// 全局锁，防止Redux层面的重复调用
let thunkExecutionLock = false;

/**
 * 从音频URI中提取标准化的文件标识符
 * 用于智能匹配不同路径下的同一个录音文件
 */
const extractAudioFileIdentifier = (audioUri: string): string | null => {
  if (!audioUri) return null;

  const fileName = audioUri.split('/').pop();
  if (!fileName) return null;

  // 移除常见的录音文件前缀和扩展名，提取核心标识符
  return fileName
    .replace(/^recording_/, '')
    .replace(/\.(m4a|wav|mp3|aac)$/i, '')
    .toLowerCase();
};

// 异步Thunk: 使用AI优化文本
export const optimizeText = createAsyncThunk(
  'recording/optimizeText',
  async (
    {
      text,
      templateId,
      templatePrompt,
      templateName,
      sourceRecordId,
      isRegenerate,
      regenerateForSameAudio,
      isSameTemplate,
      targetRecordId
    }: {
      text: string;
      templateId: string;
      templatePrompt: string;
      templateName: string;
      sourceRecordId?: string; // 二次优化的源记录ID，可选
      isRegenerate?: boolean; // 标记是否为重新生成操作
      regenerateForSameAudio?: boolean; // 标记是否为同一音频的重新生成
      isSameTemplate?: boolean; // 标记是否为相同模板的重新生成
      targetRecordId?: string; // 要更新的目标记录ID，用于重新生成时直接指定记录
    },
    { rejectWithValue, getState, dispatch }
  ) => {
    // Redux层面的重复调用保护
    if (thunkExecutionLock) {
      log('[Redux Thunk] 检测到重复调用，拒绝执行');
      return rejectWithValue('重复调用被阻止');
    }
    
    // 设置执行锁
    thunkExecutionLock = true;
    log('[Redux Thunk] 设置执行锁，开始处理优化请求');
    
    try {
      log('执行optimizeText Thunk, 模板:', templateName, isRegenerate ? '(重新生成)' : '');
      log(`[optimizeText Thunk] 模板ID: ${templateId}, 模板名称: ${templateName}`);
      log(`[optimizeText Thunk] 提示词长度: ${templatePrompt.length}`);
      log(`[optimizeText Thunk] isRegenerate: ${isRegenerate}, isSameTemplate: ${isSameTemplate}`);
      log(`[模板提示词内容] ${templatePrompt}`);
      
      // 获取当前状态
      const state = getState() as { recording: RecordingState };

      // 总是从语音服务获取最新的录音URI，因为文件可能已经被移动到正确位置
      let audioUri = SpeechService.getCurrentRecordingUri();
      log('从语音服务获取当前录音URI:', audioUri);

      // 如果语音服务中没有URI，才使用Redux状态中的URI作为备用
      if (!audioUri) {
        audioUri = state.recording.audioUri;
        log('语音服务中无URI，使用Redux状态中的URI:', audioUri);
      }
      
      // 如果有正在进行的请求，取消它
      if (currentAbortController) {
        log('取消之前的优化请求');
        currentAbortController.abort();
      }
      
      // 创建新的AbortController并保存到外部变量
      currentAbortController = new AbortController();
      
      // 生成请求ID
      const requestId = generateId();
      
      // 更新Redux中的请求ID
      dispatch(setCurrentRequestId(requestId));
      
      log(`开始优化请求，ID: ${requestId}`);
      
      // 调用AI服务优化文本
      let optimizedResult: { text: string, requestId?: string };
      try {
        // 尝试使用真实API
        log('调用AI优化API，文本长度:', text.length);
        // 获取当前用户选择的模型
        const currentModel = await AIService.getCurrentLanguageModel();
        log(`[Redux优化] 获取到当前模型: ${currentModel}`);
        
        optimizedResult = await AIService.optimizeText(
          text, 
          templatePrompt, 
          currentModel, // 使用用户选择的模型而不是默认模型
          currentAbortController.signal,
          requestId
        );
        log('AI优化文本成功，长度:', optimizedResult.text.length);
        log(`[AI优化结果] 原文: "${text}"`);
        log(`[AI优化结果] 优化后: "${optimizedResult.text}"`);
        log(`[AI优化结果] 提示词长度: ${templatePrompt.length}`);
      } catch (apiError) {
        // 如果是因为请求被取消而失败，直接抛出错误
        if (apiError instanceof Error && 
            (apiError.name === 'AbortError' || apiError.message.includes('abort'))) {
          log('优化请求被取消');
          throw apiError;
        }
        
        // 将API错误向上传递，不尝试使用模拟服务
        logError('AI API调用失败:', apiError);
        throw apiError;
      }
      
      // 检查当前请求ID是否仍然匹配Redux中的ID
      const currentState = getState() as { recording: RecordingState };
      if (currentState.recording.currentOptimizeRequestId !== requestId) {
        log(`请求已过期，当前Redux请求ID: ${currentState.recording.currentOptimizeRequestId}, 本次请求ID: ${requestId}`);
        return rejectWithValue('请求已过期');
      }
      
      // 检查请求ID是否匹配，防止过期响应
      if (optimizedResult.requestId !== requestId) {
        log(`请求ID不匹配，忽略结果 - 当前:${requestId}, 响应:${optimizedResult.requestId}`);
        return rejectWithValue('请求已过期');
      }
      
      // 提前更新Redux状态，确保其他组件能及时获取到结果
      dispatch(updateOptimizedText({
        optimizedText: optimizedResult.text,
        selectedTemplateId: templateId
      }));
      
      // 创建历史记录 - 根据模板是否相同决定处理方式
      let historyRecord: HistoryRecord;

      // 如果是重新生成操作，需要根据模板是否相同来决定处理方式
      if (isRegenerate) {
        if (isSameTemplate && targetRecordId) {
          // 有明确的目标记录ID，直接查找该记录进行更新
          log('重新生成：使用指定的目标记录ID进行更新:', targetRecordId);

          const allRecords = await HistoryService.getHistoryRecords();
          const existingRecord = allRecords.find(record => record.id === targetRecordId);

          if (existingRecord) {
            log('找到目标记录，将更新该记录:', existingRecord.id);

            // 使用现有记录ID，更新为最新结果
            historyRecord = {
              id: existingRecord.id, // 使用现有记录ID
              timestamp: Date.now(),
              originalText: text,
              optimizedText: optimizedResult.text,
              templateId,
              templateName,
              audioUri: audioUri,
              optimizedResults: existingRecord.optimizedResults || [] // 复制原有的optimizedResults数组
            };

            // 添加新的优化结果到数组中
            if (!historyRecord.optimizedResults) {
              historyRecord.optimizedResults = [];
            }

            historyRecord.optimizedResults.push({
              text: optimizedResult.text,
              timestamp: Date.now(),
              templateId,
              templateName
            });

            log('已添加新的优化结果，现在共有 ' + historyRecord.optimizedResults.length + ' 条结果');
          } else {
            log('未找到指定的目标记录，创建新记录');

            // 创建新记录
            historyRecord = {
              id: generateId(),
              timestamp: Date.now(),
              originalText: text,
              optimizedText: optimizedResult.text,
              templateId,
              templateName,
              audioUri: audioUri || undefined,
              sourceRecordId, // 保留源记录ID
              optimizedResults: [{
                text: optimizedResult.text,
                timestamp: Date.now(),
                templateId,
                templateName
              }]
            };
          }
        } else if (isSameTemplate) {
          // 相同模板的重新生成，但没有指定目标记录ID，使用原有的查找逻辑
          log('相同模板的重新生成，查找现有记录进行合并');

          // 获取所有历史记录
          const allRecords = await HistoryService.getHistoryRecords();

          log('查找现有记录的条件:', {
            audioUri,
            text,
            templateId,
            recordsCount: allRecords.length
          });

          // 智能查找匹配的历史记录，支持多种匹配策略
          let existingRecord = allRecords.find(record => {
            // 标准化文本进行比较，去除首尾空格和省略号
            const normalizeText = (str: string) => str.trim().replace(/\.{3}$/, '');
            const normalizedRecordText = normalizeText(record.originalText);
            const normalizedCurrentText = normalizeText(text);

            const matches = record.audioUri === audioUri &&
              normalizedRecordText === normalizedCurrentText &&
              record.templateId === templateId &&
              !record.sourceRecordId; // 确保不是二次优化记录

            if (record.audioUri === audioUri) {
              log('找到相同音频URI的记录:', {
                recordId: record.id,
                recordOriginalText: record.originalText,
                recordTemplateId: record.templateId,
                recordSourceRecordId: record.sourceRecordId,
                normalizedRecordText,
                normalizedCurrentText,
                textMatches: normalizedRecordText === normalizedCurrentText,
                templateMatches: record.templateId === templateId,
                notSecondaryOptimization: !record.sourceRecordId,
                overallMatch: matches
              });
            }

            return matches;
          });

          // 如果精确匹配失败，尝试文件标识符匹配（处理重试失败后路径变化的情况）
          if (!existingRecord && audioUri) {
            log('重新生成：精确audioUri匹配失败，尝试文件标识符匹配');
            const currentFileId = extractAudioFileIdentifier(audioUri);

            if (currentFileId) {
              existingRecord = allRecords.find(record => {
                if (!record.audioUri || record.sourceRecordId) return false;

                const recordFileId = extractAudioFileIdentifier(record.audioUri);

                // 标准化文本进行比较
                const normalizeText = (str: string) => str.trim().replace(/\.{3}$/, '');
                const normalizedRecordText = normalizeText(record.originalText);
                const normalizedCurrentText = normalizeText(text);

                return recordFileId === currentFileId &&
                       normalizedRecordText === normalizedCurrentText &&
                       record.templateId === templateId;
              });

              if (existingRecord) {
                log('重新生成：通过文件标识符匹配找到现有记录:', existingRecord.id, '原audioUri:', existingRecord.audioUri, '当前audioUri:', audioUri, '文件标识符:', currentFileId);
              }
            }
          }

          if (existingRecord) {
            log('找到相同模板的现有记录，将更新该记录:', existingRecord.id);

            // 使用现有记录ID，更新为最新结果
            historyRecord = {
              id: existingRecord.id, // 使用现有记录ID
              timestamp: Date.now(),
              originalText: text,
              optimizedText: optimizedResult.text,
              templateId,
              templateName,
              audioUri: audioUri,
              optimizedResults: existingRecord.optimizedResults || [] // 复制原有的optimizedResults数组
            };

            // 添加新的优化结果到数组中
            if (!historyRecord.optimizedResults) {
              historyRecord.optimizedResults = [];
            }

            historyRecord.optimizedResults.push({
              text: optimizedResult.text,
              timestamp: Date.now(),
              templateId,
              templateName
            });
          } else {
            // 没找到现有记录，创建新记录
            log('没找到相同模板的现有记录，创建新记录');
            historyRecord = {
              id: generateId(),
              timestamp: Date.now(),
              originalText: text,
              optimizedText: optimizedResult.text,
              templateId,
              templateName,
              audioUri: audioUri || undefined,
              sourceRecordId, // 保留源记录ID
              optimizedResults: [{
                text: optimizedResult.text,
                timestamp: Date.now(),
                templateId,
                templateName
              }]
            };
          }
        } else {
          // 不同模板的重新生成，创建新记录
          log('不同模板的重新生成，创建新记录');
          historyRecord = {
            id: generateId(),
            timestamp: Date.now(),
            originalText: text,
            optimizedText: optimizedResult.text,
            templateId,
            templateName,
            audioUri: audioUri || undefined,
            sourceRecordId, // 保留源记录ID
            optimizedResults: [{
              text: optimizedResult.text,
              timestamp: Date.now(),
              templateId,
              templateName
            }]
          };
        }
      }
      // 如果有audioUri且不是二次优化且不是重新生成，尝试查找现有记录（首次优化）
      else if (audioUri && !sourceRecordId && !isRegenerate) {
        log('处理有音频的优化记录，查找现有记录');
        
        // 获取所有历史记录
        const allRecords = await HistoryService.getHistoryRecords();
        log('查找记录，当前音频URI:', audioUri);
        
        // 智能查找匹配的历史记录，支持多种匹配策略
        let existingRecord = allRecords.find(record =>
          record.audioUri === audioUri &&
          record.originalText === text &&
          record.templateId === templateId && // 确保模板ID相同
          !record.sourceRecordId // 确保不是二次优化记录
        );

        // 如果精确匹配失败，尝试文件标识符匹配（处理路径变化的情况）
        if (!existingRecord && audioUri) {
          log('精确audioUri匹配失败，尝试文件标识符匹配');
          const currentFileId = extractAudioFileIdentifier(audioUri);

          if (currentFileId) {
            existingRecord = allRecords.find(record => {
              if (!record.audioUri || record.sourceRecordId) return false;

              const recordFileId = extractAudioFileIdentifier(record.audioUri);

              return recordFileId === currentFileId &&
                     record.originalText === text &&
                     record.templateId === templateId;
            });

            if (existingRecord) {
              log('通过文件标识符匹配找到现有记录:', existingRecord.id, '原audioUri:', existingRecord.audioUri, '当前audioUri:', audioUri, '文件标识符:', currentFileId);
            }
          }
        }
        
        if (existingRecord) {
          log('找到相同模板的现有记录，将添加新的优化结果:', existingRecord.id);
          
          // 使用现有记录ID，而不是生成新ID
          historyRecord = {
            id: existingRecord.id, // 使用现有记录ID
            timestamp: Date.now(),
            originalText: text,
            optimizedText: optimizedResult.text,
            templateId,
            templateName,
            audioUri: audioUri,
            optimizedResults: existingRecord.optimizedResults || [] // 复制原有的optimizedResults数组
          };
          
          // 添加新的优化结果到数组中
          if (!historyRecord.optimizedResults) {
            historyRecord.optimizedResults = [];
          }
          
          historyRecord.optimizedResults.push({
            text: optimizedResult.text,
            timestamp: Date.now(),
            templateId,
            templateName
          });
          
          log('已添加新的优化结果，现在共有 ' + historyRecord.optimizedResults.length + ' 条结果');
        } else {
          log('没有找到相同模板的现有记录，创建新记录');
          
          // 创建新记录
          historyRecord = {
            id: generateId(),
            timestamp: Date.now(),
            originalText: text,
            optimizedText: optimizedResult.text,
            templateId,
            templateName,
            audioUri: audioUri,
            optimizedResults: [{
              text: optimizedResult.text,
              timestamp: Date.now(),
              templateId,
              templateName
            }]
          };
        }
      } else {
        // 其他情况：二次优化、没有音频、或重新生成但不是相同模板，创建独立记录
        if (isRegenerate && !isSameTemplate) {
          log('不同模板的重新生成，创建新记录');
        } else if (sourceRecordId) {
          log('创建二次优化记录');
        } else {
          log('创建无音频记录或其他情况记录');
        }

        historyRecord = {
          id: generateId(),
          timestamp: Date.now(),
          originalText: text,
          optimizedText: optimizedResult.text,
          templateId,
          templateName,
          audioUri: sourceRecordId ? undefined : (audioUri || undefined), // 二次优化不传递音频
          sourceRecordId, // 添加源记录ID字段，二次优化时有值
          optimizedResults: [{
            text: optimizedResult.text,
            timestamp: Date.now(),
            templateId,
            templateName
          }]
        };
      }
      
      // 保存到历史记录 - 添加明确的日志记录
      try {
        log('optimizeText将保存历史记录，记录ID:', historyRecord.id);
        log('历史记录内容摘要:', {
          id: historyRecord.id,
          原文长度: historyRecord.originalText.length,
          优化文本长度: historyRecord.optimizedText.length,
          模板: historyRecord.templateName,
          音频: historyRecord.audioUri ? '有' : '无',
          源记录ID: historyRecord.sourceRecordId || '无(首次优化)',
          有优化结果数组: !!historyRecord.optimizedResults,
          优化结果数量: historyRecord.optimizedResults?.length || 0,
          是否重新生成: isRegenerate || false
        });
        
        // 直接使用historyService的saveHistoryRecord函数保存记录，确保optimizedResults数组正确保存
        const saved = await HistoryService.saveHistoryRecord(historyRecord);
        log('optimizeText历史记录保存成功:', saved, '记录ID:', historyRecord.id);
        
        // 验证保存结果
        const savedRecord = await HistoryService.getHistoryRecordById(historyRecord.id);
        log('验证保存的记录内容:', {
          id: savedRecord?.id,
          有优化结果数组: !!savedRecord?.optimizedResults,
          优化结果数量: savedRecord?.optimizedResults?.length || 0
        });

        // 发送历史记录变更事件，通知历史记录页面刷新
        if (saved) {
          const { DeviceEventEmitter } = require('react-native');
          if (isRegenerate) {
            DeviceEventEmitter.emit('HISTORY_RECORD_UPDATED');
            log('发送历史记录更新事件');
          } else {
            DeviceEventEmitter.emit('HISTORY_RECORD_ADDED');
            log('发送历史记录新增事件');
          }
        }
      } catch (saveError) {
        logError('保存历史记录失败，但继续返回优化文本:', saveError);
      }
      
      // 清除当前请求状态
      currentAbortController = null;
      dispatch(setCurrentRequestId(null));
      
      return {
        optimizedText: optimizedResult.text,
        selectedTemplateId: templateId,
        historyRecordId: historyRecord.id  // 返回已保存记录的ID，方便跟踪
      };
    } catch (error) {
      logError('AI文本优化失败:', error);
      
      // 清除当前请求ID，表示请求已失败
      dispatch(setCurrentRequestId(null));
      currentAbortController = null;
      
      // 如果是请求取消导致的，不要显示为错误
      if (error instanceof Error && 
          (error.name === 'AbortError' || error.message.includes('abort'))) {
        return rejectWithValue('请求已取消');
      }
      
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('AI文本优化失败');
    } finally {
      // 无论成功还是失败，都要释放执行锁
      log('[Redux Thunk] 释放执行锁');
      thunkExecutionLock = false;
    }
  }
);

// 添加一个取消优化的action creator
export const cancelOptimization = createAsyncThunk(
  'recording/cancelOptimization',
  async (_, { getState, dispatch }) => {
    const state = getState() as { recording: RecordingState };
    const { currentOptimizeRequestId } = state.recording;
    
    if (currentAbortController && currentOptimizeRequestId) {
      log(`取消优化请求，ID: ${currentOptimizeRequestId}`);
      currentAbortController.abort();
      
      // 清除状态
      currentAbortController = null;
      
      // 更新Redux中的requestId
      dispatch(setCurrentRequestId(null));
      
      return { success: true };
    }
    
    return { success: false };
  }
);

// 异步Thunk: 获取语音识别状态
export const getRecognitionStatus = createAsyncThunk(
  'recording/getRecognitionStatus',
  async (callback: (currentText: string) => void, { dispatch }) => {
    try {
      // 调用语音服务来获取当前识别的文本
      SpeechService.getRecognitionStatus((text) => {
        if (text && text.trim()) {
          // 使用回调将文本传递给调用者
          callback(text);
        }
      });
      
      return { success: true };
    } catch (error) {
      logError('获取语音识别状态失败:', error);
      throw error;
    }
  }
);

// 异步Thunk: 初始化语音设置
export const initializeSpeechSettings = createAsyncThunk(
  'recording/initializeSpeechSettings',
  async (_, { dispatch }) => {
    try {
      // 从存储中加载高级转写设置
      const useAdvancedTranscription = await getUseAdvancedTranscription();
      
      // 更新Redux状态
      dispatch(setAdvancedTranscription(useAdvancedTranscription));
      
      return { useAdvancedTranscription };
    } catch (error) {
      logError('初始化语音设置失败:', error);
      return { useAdvancedTranscription: false };
    }
  }
);

// 异步Thunk: 切换高级转写设置
export const toggleAdvancedTranscriptionThunk = createAsyncThunk(
  'recording/toggleAdvancedTranscription',
  async (_, { getState, dispatch }) => {
    try {
      // 获取当前状态
      const state = getState() as RootState;
      const currentSetting = state.recording.useAdvancedTranscription;
      
      // 切换状态
      const newSetting = !currentSetting;
      
      // 保存到本地存储
      await setUseAdvancedTranscription(newSetting);
      
      // 更新Redux状态
      dispatch(setAdvancedTranscription(newSetting));
      
      return newSetting;
    } catch (error) {
      logError('切换高级转写设置失败:', error);
      return false;
    }
  }
);

// 录音切片
const recordingSlice = createSlice({
  name: 'recording',
  initialState,
  reducers: {
    // 更新识别文本
    updateRecognizedText: (state, action: PayloadAction<string>) => {
      state.recognizedText = action.payload;
    },
    // 更新优化文本 - 直接方式
    updateOptimizedText: (state, action: PayloadAction<{
      optimizedText: string;
      selectedTemplateId: string;
    }>) => {
      state.optimizedText = action.payload.optimizedText;
      state.selectedTemplateId = action.payload.selectedTemplateId;
      state.processingAI = false;
      log('通过直接action更新优化文本，长度:', action.payload.optimizedText.length);
    },
    // 更新音量级别
    updateAudioLevel: (state, action: PayloadAction<number>) => {
      state.audioLevel = action.payload;
    },
    // 添加暂停记录
    addPause: (state, action: PayloadAction<{ startTime?: number, endTime?: number }>) => {
      const { startTime, endTime } = action.payload;
      
      // 如果有开始时间，添加一个新的暂停记录
      if (startTime) {
        state.pauses.push({ startTime, endTime: 0 });
      } 
      // 如果有结束时间，更新最后一个暂停记录
      else if (endTime && state.pauses.length > 0) {
        state.pauses[state.pauses.length - 1].endTime = endTime;
      }
    },
    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
    // 重置录音状态
    resetRecording: (state) => {
      state.recordingStatus = 'idle';
      state.recognizedText = '';
      state.optimizedText = null;
      state.audioUri = null;
      state.selectedTemplateId = null;
      state.audioLevel = 0;
      state.error = null;
      state.processingAI = false;
      state.lastRecordingTimestamp = null;
      state.pauses = [];
      state.currentOptimizeRequestId = null;
      
      // 重置外部AbortController
      if (currentAbortController) {
        currentAbortController.abort();
        currentAbortController = null;
      }
      
      // 重置Redux层面的执行锁
      thunkExecutionLock = false;
      log('[Redux] 重置状态时释放Thunk执行锁');
    },
    // 设置当前请求ID
    setCurrentRequestId: (state, action: PayloadAction<string | null>) => {
      state.currentOptimizeRequestId = action.payload;
    },
    // 设置高级转写开关状态
    setAdvancedTranscription: (state, action: PayloadAction<boolean>) => {
      state.useAdvancedTranscription = action.payload;
    },
    // 设置网络错误
    setNetworkError: (state, action: PayloadAction<string | null>) => {
      state.networkError = action.payload;
      state.showNetworkError = !!action.payload;
    },
    // 清除网络错误
    clearNetworkError: (state) => {
      state.networkError = null;
      state.showNetworkError = false;
    },
    // 设置whisper使用限制错误
    setWhisperLimitError: (state, action: PayloadAction<{
      message: string;
      usageInfo: any;
      requiresVipUpgrade: boolean;
    }>) => {
      state.whisperLimitError = action.payload;
      state.showWhisperLimitModal = true;
    },
    // 清除whisper使用限制错误
    clearWhisperLimitError: (state) => {
      state.whisperLimitError = null;
      state.showWhisperLimitModal = false;
    },
    // 隐藏whisper限制模态框
    hideWhisperLimitModal: (state) => {
      state.showWhisperLimitModal = false;
    }
  },
  extraReducers: (builder) => {
    // 开始录音
    builder.addCase(startRecording.pending, (state) => {
      state.recordingStatus = 'recording';
      state.recognizedText = '';
      state.optimizedText = null;
      state.audioUri = null;
      state.error = null;
      state.lastRecordingTimestamp = Date.now();
      state.pauses = [];
    });
    builder.addCase(startRecording.fulfilled, (state, action) => {
      state.recordingStatus = action.payload ? 'recording' : 'error';
      state.error = null;
      state.lastRecordingTimestamp = Date.now();
      state.pauses = []; // 重置暂停记录
    });
    builder.addCase(startRecording.rejected, (state) => {
      state.recordingStatus = 'error';
    });
    
    // 暂停录音
    builder.addCase(pauseRecording.pending, (state) => {
      state.recordingStatus = 'paused';
      // 添加暂停记录的开始时间
      state.pauses.push({
        startTime: Date.now(),
        endTime: 0
      });
    });
    builder.addCase(pauseRecording.fulfilled, (state) => {
      state.recordingStatus = 'paused';
    });
    builder.addCase(pauseRecording.rejected, (state) => {
      state.recordingStatus = 'error';
    });
    
    // 继续录音
    builder.addCase(resumeRecording.pending, (state) => {
      state.recordingStatus = 'recording';
    });
    builder.addCase(resumeRecording.fulfilled, (state) => {
      state.recordingStatus = 'recording';
    });
    builder.addCase(resumeRecording.rejected, (state) => {
      state.recordingStatus = 'error';
    });
    
    // 停止录音
    builder.addCase(stopRecording.pending, (state) => {
      state.recordingStatus = 'stopped';
    });
    builder.addCase(stopRecording.fulfilled, (state, action) => {
      state.recordingStatus = 'stopped';
      if (action.payload && action.payload.audioUri) {
        state.audioUri = action.payload.audioUri;
      }
    });
    builder.addCase(stopRecording.rejected, (state) => {
      state.recordingStatus = 'error';
    });
    
    // 添加对优化文本处理的状态更新
    builder.addCase(optimizeText.pending, (state) => {
      state.processingAI = true;
      state.optimizedText = null; // 清空之前的优化结果
      log('Redux状态更新：开始处理AI优化');
    });
    builder.addCase(optimizeText.fulfilled, (state, action) => {
      state.processingAI = false;
      if (action.payload && action.payload.optimizedText) {
        state.optimizedText = action.payload.optimizedText;
        state.selectedTemplateId = action.payload.selectedTemplateId;
        log('Redux状态更新：优化文本已保存，长度:', action.payload.optimizedText.length);
        log('优化记录ID:', action.payload.historyRecordId);
      } else {
        logError('Redux状态更新失败：优化结果为空');
      }
    });
    builder.addCase(optimizeText.rejected, (state, action) => {
      state.processingAI = false;
      state.error = 'AI文本优化失败';
      logError('Redux状态更新：AI优化失败', action.error);
    });
    
    // 处理初始化语音设置
    builder.addCase(initializeSpeechSettings.fulfilled, (state, action) => {
      state.useAdvancedTranscription = action.payload.useAdvancedTranscription;
    });
    
    // 处理退出登录 - 重置语音转写设置
    builder.addCase(logout.fulfilled, (state) => {
      // 退出登录时，重置高级转写设置为false（对应native模式）
      state.useAdvancedTranscription = false;
      log('Redux状态更新：退出登录，语音转写设置已重置为native模式');
    });
  }
});

// 导出action creators
export const {
  updateRecognizedText,
  updateOptimizedText,
  updateAudioLevel,
  addPause,
  setError,
  clearError,
  resetRecording,
  setCurrentRequestId,
  setAdvancedTranscription,
  setNetworkError,
  clearNetworkError,
  setWhisperLimitError,
  clearWhisperLimitError,
  hideWhisperLimitModal
} = recordingSlice.actions;

// 创建一个Thunk来更新识别的文本
export const updateRecognizedTextThunk = (text: string) => {
  return async (dispatch: any) => {
    dispatch(updateRecognizedText(text));
    return { success: true };
  };
};

export default recordingSlice.reducer; 