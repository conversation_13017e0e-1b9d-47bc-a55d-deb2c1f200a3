import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import * as HistoryService from '@/services/storageService';
import { HistoryRecord, HistoryGroup } from '@/services/storageService';
import { log, error as logError, warn } from '@/services/logService';

// 历史记录状态接口
interface HistoryState {
  records: HistoryRecord[];
  groupedRecords: HistoryGroup[];
  selectedRecordId: string | null;
  selectedRecord: HistoryRecord | null;
  loading: boolean;
  error: string | null;
}

// 初始状态
const initialState: HistoryState = {
  records: [],
  groupedRecords: [],
  selectedRecordId: null,
  selectedRecord: null,
  loading: false,
  error: null
};

// 异步thunk: 加载所有历史记录
export const loadHistoryRecords = createAsyncThunk(
  'history/loadHistoryRecords',
  async (_, { rejectWithValue }) => {
    try {
      const records = await HistoryService.getHistoryRecords();
      return records;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('加载历史记录失败');
    }
  }
);

// 异步thunk: 加载按日期分组的历史记录
export const loadGroupedHistoryRecords = createAsyncThunk(
  'history/loadGroupedHistoryRecords',
  async (limit: number | undefined, { rejectWithValue }) => {
    try {
      log('开始获取分组历史记录...' + (limit ? `，限制加载${limit}条` : ''));
      const startTime = Date.now();
      
      // 获取分组历史记录
      const groupedRecords = await HistoryService.getGroupedHistoryRecords(limit);
      
      // 打印加载信息
      const recordCount = groupedRecords.reduce((sum, group) => sum + group.data.length, 0);
      log(`历史记录加载完成，用时: ${Date.now() - startTime}ms, 共 ${groupedRecords.length} 组, ${recordCount} 条记录`);
      
      // 检查记录ID唯一性
      const allIds = new Set<string>();
      const duplicateIds = new Set<string>();
      
      // 检查是否有重复ID
      groupedRecords.forEach(group => {
        group.data.forEach(record => {
          if (allIds.has(record.id)) {
            duplicateIds.add(record.id);
          } else {
            allIds.add(record.id);
          }
        });
      });
      
      // 如果发现重复ID，记录日志
      if (duplicateIds.size > 0) {
        warn(`发现 ${duplicateIds.size} 个重复ID的历史记录:`, Array.from(duplicateIds));
      }
      
      return groupedRecords;
    } catch (error) {
      logError('加载历史记录失败:', error);
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('加载历史记录失败');
    }
  }
);

// 异步thunk: 根据ID获取历史记录
export const getHistoryRecordById = createAsyncThunk(
  'history/getHistoryRecordById',
  async (id: string, { rejectWithValue }) => {
    try {
      const record = await HistoryService.getHistoryRecordById(id);
      if (!record) {
        return rejectWithValue('未找到历史记录');
      }
      return record;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('获取历史记录失败');
    }
  }
);

// 异步thunk: 删除历史记录
export const deleteHistoryRecord = createAsyncThunk(
  'history/deleteHistoryRecord',
  async (id: string, { rejectWithValue, dispatch }) => {
    try {
      const success = await HistoryService.deleteHistoryRecord(id);
      if (!success) {
        return rejectWithValue('删除历史记录失败');
      }
      
      // 删除成功后重新加载历史记录
      dispatch(loadGroupedHistoryRecords());
      
      return id;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('删除历史记录失败');
    }
  }
);

// 异步thunk: 清空所有历史记录
export const clearAllHistoryRecords = createAsyncThunk(
  'history/clearAllHistoryRecords',
  async (_, { rejectWithValue }) => {
    try {
      const success = await HistoryService.clearAllHistoryRecords();
      if (!success) {
        return rejectWithValue('清空历史记录失败');
      }
      return true;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('清空历史记录失败');
    }
  }
);

// 创建历史记录切片
const historySlice = createSlice({
  name: 'history',
  initialState,
  reducers: {
    setSelectedRecordId: (state, action: PayloadAction<string | null>) => {
      state.selectedRecordId = action.payload;
    },
    clearSelectedRecord: (state) => {
      state.selectedRecordId = null;
      state.selectedRecord = null;
    },
    resetHistoryError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    // 处理加载所有历史记录
    builder.addCase(loadHistoryRecords.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(loadHistoryRecords.fulfilled, (state, action) => {
      state.loading = false;
      state.records = action.payload;
    });
    builder.addCase(loadHistoryRecords.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
    
    // 处理加载分组历史记录
    builder.addCase(loadGroupedHistoryRecords.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(loadGroupedHistoryRecords.fulfilled, (state, action) => {
      state.loading = false;
      state.groupedRecords = action.payload;
    });
    builder.addCase(loadGroupedHistoryRecords.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
    
    // 处理获取历史记录
    builder.addCase(getHistoryRecordById.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(getHistoryRecordById.fulfilled, (state, action) => {
      state.loading = false;
      state.selectedRecord = action.payload;
    });
    builder.addCase(getHistoryRecordById.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
    
    // 处理删除历史记录
    builder.addCase(deleteHistoryRecord.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(deleteHistoryRecord.fulfilled, (state, action) => {
      state.loading = false;
      // 从records中删除记录
      state.records = state.records.filter(record => record.id !== action.payload);
      
      // 如果删除的是当前选中的记录，清除选中状态
      if (state.selectedRecordId === action.payload) {
        state.selectedRecordId = null;
        state.selectedRecord = null;
      }
    });
    builder.addCase(deleteHistoryRecord.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
    
    // 处理清空所有历史记录
    builder.addCase(clearAllHistoryRecords.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(clearAllHistoryRecords.fulfilled, (state) => {
      state.loading = false;
      state.records = [];
      state.groupedRecords = [];
      state.selectedRecordId = null;
      state.selectedRecord = null;
    });
    builder.addCase(clearAllHistoryRecords.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
  }
});

export const { 
  setSelectedRecordId, 
  clearSelectedRecord,
  resetHistoryError
} = historySlice.actions;

export default historySlice.reducer; 