import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { saveLanguage, getLanguage } from '@/utils/languageStorage';

export type LanguageCode = 'zh-Hans' | 'zh-Hant' | 'en';

interface LanguageState {
  language: LanguageCode;
  loading: boolean;
}

// 初始化状态
const initialState: LanguageState = {
  language: 'zh-Hans', // 默认简体中文
  loading: false,
};

// 异步Thunk action用于加载保存的语言设置
export const loadLanguage = createAsyncThunk(
  'language/loadLanguage',
  async () => {
    return await getLanguage();
  }
);

// 异步Thunk action用于保存语言设置
export const saveLanguagePreference = createAsyncThunk(
  'language/saveLanguagePreference',
  async (language: LanguageCode) => {
    await saveLanguage(language);
    return language;
  }
);

export const languageSlice = createSlice({
  name: 'language',
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<LanguageCode>) => {
      state.language = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // 处理加载语言设置
      .addCase(loadLanguage.pending, (state) => {
        state.loading = true;
      })
      .addCase(loadLanguage.fulfilled, (state, action) => {
        state.language = action.payload;
        state.loading = false;
      })
      .addCase(loadLanguage.rejected, (state) => {
        state.loading = false;
      })
      // 处理保存语言设置
      .addCase(saveLanguagePreference.pending, (state) => {
        state.loading = true;
      })
      .addCase(saveLanguagePreference.fulfilled, (state, action) => {
        state.language = action.payload;
        state.loading = false;
      })
      .addCase(saveLanguagePreference.rejected, (state) => {
        state.loading = false;
      });
  },
});

// 导出actions
export const { setLanguage } = languageSlice.actions;

// 导出语言选择器
export const selectCurrentLanguage = (state: { language: LanguageState }) => state.language.language;

export default languageSlice.reducer;
