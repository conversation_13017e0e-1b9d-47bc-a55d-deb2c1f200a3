import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Subscription } from '@/types';

// VIP状态接口
interface VIPState {
  isVIP: boolean;
  currentSubscription: Subscription | null;
  lastChecked: string | null;
}

// 初始状态
const initialState: VIPState = {
  isVIP: false,
  currentSubscription: null,
  lastChecked: null,
};

// 创建VIP Slice
const vipSlice = createSlice({
  name: 'vip',
  initialState,
  reducers: {
    setVIPStatus: (state, action: PayloadAction<boolean>) => {
      state.isVIP = action.payload;
      state.lastChecked = new Date().toISOString();
    },
    setCurrentSubscription: (state, action: PayloadAction<Subscription | null>) => {
      state.currentSubscription = action.payload;
    },
    resetVIPState: () => initialState,
  },
});

export const { setVIPStatus, setCurrentSubscription, resetVIPState } = vipSlice.actions;
export default vipSlice.reducer; 