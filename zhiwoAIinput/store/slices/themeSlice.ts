import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { Appearance } from 'react-native';
import { saveThemeMode as saveThemeModeToStorage, getThemeMode as getThemeModeFromStorage } from '@/utils/themeStorage';

export type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeState {
  themeMode: ThemeMode;
  loading: boolean;
}

// 初始化状态
const initialState: ThemeState = {
  themeMode: 'system', // 默认跟随系统
  loading: false,
};

// 异步Thunk action用于加载保存的主题设置
export const loadThemeMode = createAsyncThunk(
  'theme/loadThemeMode',
  async () => {
    return await getThemeModeFromStorage();
  }
);

// 异步Thunk action用于保存主题设置
export const saveThemeMode = createAsyncThunk(
  'theme/saveThemeMode',
  async (themeMode: ThemeMode) => {
    await saveThemeModeToStorage(themeMode);
    return themeMode;
  }
);

export const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setThemeMode: (state, action: PayloadAction<ThemeMode>) => {
      state.themeMode = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // 处理加载主题设置
      .addCase(loadThemeMode.pending, (state) => {
        state.loading = true;
      })
      .addCase(loadThemeMode.fulfilled, (state, action) => {
        state.themeMode = action.payload;
        state.loading = false;
      })
      .addCase(loadThemeMode.rejected, (state) => {
        state.loading = false;
      })
      // 处理保存主题设置
      .addCase(saveThemeMode.pending, (state) => {
        state.loading = true;
      })
      .addCase(saveThemeMode.fulfilled, (state, action) => {
        state.themeMode = action.payload;
        state.loading = false;
      })
      .addCase(saveThemeMode.rejected, (state) => {
        state.loading = false;
      });
  },
});

// 导出actions
export const { setThemeMode } = themeSlice.actions;

// 创建选择器来获取当前实际主题色（考虑跟随系统的情况）
export const selectEffectiveTheme = (state: { theme: ThemeState }) => {
  if (state.theme.themeMode === 'system') {
    return Appearance.getColorScheme() || 'light';
  }
  return state.theme.themeMode;
};

// 导出主题模式选择器
export const selectThemeMode = (state: { theme: ThemeState }) => state.theme.themeMode;

export default themeSlice.reducer; 