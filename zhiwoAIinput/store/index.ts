import { configureStore } from '@reduxjs/toolkit';
import doubleTapReducer from './slices/doubleTapSlice';
import templateReducer from './slices/templateSlice';
import recordingReducer from './slices/recordingSlice';
import historyReducer from './slices/historySlice';
import authReducer from './slices/authSlice';
import subscriptionReducer from './slices/subscriptionSlice';
import themeReducer from './slices/themeSlice';
import languageReducer from './slices/languageSlice';
import vipReducer from './slices/vipSlice';
import fastModeReducer from './slices/fastModeSlice';
import styleOptimizationReducer from './slices/styleOptimizationSlice';

export const store = configureStore({
  reducer: {
    doubleTap: doubleTapReducer,
    template: templateReducer,
    recording: recordingReducer,
    history: historyReducer,
    auth: authReducer,
    subscription: subscriptionReducer,
    theme: themeReducer,
    language: languageReducer,
    vip: vipReducer,
    fastMode: fastModeReducer,
    styleOptimization: styleOptimizationReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch; 