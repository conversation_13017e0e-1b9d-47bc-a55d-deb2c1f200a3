#!/bin/bash

# 停止当前运行的Supabase实例
echo "停止当前运行的Supabase..."
supabase stop

# 确保docker正在运行
if ! docker info > /dev/null 2>&1; then
  echo "Docker 未在运行，请启动Docker并重试"
  exit 1
fi

# 启动Supabase，并使用网络选项
echo "启动Supabase，使用网络选项..."
supabase start --debug

# 在docker-compose.yml中查找容器名称
EDGE_RUNTIME_CONTAINER=$(docker ps --filter "name=supabase_edge_runtime_" --format "{{.Names}}")

if [ -z "$EDGE_RUNTIME_CONTAINER" ]; then
  echo "找不到Edge Runtime容器，请检查Supabase是否已正确启动"
else
  echo "找到Edge Runtime容器: $EDGE_RUNTIME_CONTAINER"
  
  # 复制预缓存的依赖到容器中
  echo "正在准备复制预缓存依赖到容器..."
  
  # 创建临时目录用于存放依赖文件
  TEMP_DIR=$(mktemp -d)
  
  # 从本地缓存提取需要的文件
  mkdir -p $TEMP_DIR/std/http/
  
  # 从现有缓存或CDN下载必要的文件
  curl -sL https://cdn.jsdelivr.net/gh/denoland/deno_std@0.177.0/http/server.ts -o $TEMP_DIR/http_server.ts
  
  echo "临时文件准备完成，正在复制到容器..."
  
  # 复制文件到容器
  docker cp $TEMP_DIR/http_server.ts $EDGE_RUNTIME_CONTAINER:/root/http_server.ts
  
  # 清理临时目录
  rm -rf $TEMP_DIR
  
  echo "依赖文件已复制到容器"
fi

echo "Supabase 启动完成!" 