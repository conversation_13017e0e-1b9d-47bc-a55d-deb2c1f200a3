# Supabase边缘函数环境变量模板
# 使用说明：
# 1. 复制此文件为 .env.local （本地开发用）
# 2. 填入真实的API密钥
# 3. 云端部署时使用 supabase secrets set 命令设置

# ===========================================
# AI服务API密钥
# ===========================================
SILICONFLOW_API_KEY="your_siliconflow_api_key_here"
OPENAI_API_KEY="your_openai_api_key_here"
ANTHROPIC_API_KEY="your_anthropic_api_key_here"
ELEVENLABS_API_KEY="your_elevenlabs_api_key_here"

# ===========================================
# 本地开发配置
# ===========================================
PORT="8000"
ENVIRONMENT="development"
DEBUG="true"

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL="info"
LOG_FORMAT="json"

# ===========================================
# 限流配置
# ===========================================
RATE_LIMIT_REQUESTS="100"
RATE_LIMIT_WINDOW="60"

# ===========================================
# 超时配置
# ===========================================
REQUEST_TIMEOUT="30000"
RESPONSE_TIMEOUT="30000" 