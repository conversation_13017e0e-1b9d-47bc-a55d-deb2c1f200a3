#!/bin/bash

# 代理服务测试脚本
set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

PORT=${1:-"8001"}
HOST="http://localhost:$PORT"

echo -e "${YELLOW}🧪 测试代理服务 - $HOST${NC}"

# 1. 测试健康检查（OPTIONS请求）
echo -e "${YELLOW}1. 测试 CORS 预检请求...${NC}"
curl -s -X OPTIONS "$HOST" \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: content-type" \
  -w "HTTP状态码: %{http_code}\n" || echo -e "${RED}❌ CORS预检失败${NC}"

echo ""

# 函数：测试AI服务
test_ai_service() {
    local service_name=$1
    local service_display=$2
    local model=$3
    local test_message=$4
    
    echo -e "${YELLOW}🔧 测试 $service_display 代理...${NC}"
    
    local request_body
    if [[ "$service_name" == "anthropic" ]]; then
        # Anthropic API格式
        request_body='{
            "service": "'$service_name'",
            "path": "/v1/messages",
            "method": "POST",
            "body": {
                "model": "'$model'",
                "max_tokens": 100,
                "messages": [
                    {
                        "role": "user",
                        "content": "'$test_message'"
                    }
                ]
            }
        }'
    else
        # OpenAI兼容格式 (SiliconFlow, OpenAI)
        request_body='{
            "service": "'$service_name'",
            "path": "/v1/chat/completions",
            "method": "POST",
            "body": {
                "model": "'$model'",
                "messages": [
                    {
                        "role": "user",
                        "content": "'$test_message'"
                    }
                ],
                "max_tokens": 100
            }
        }'
    fi
    
    local response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$HOST" \
        -H "Content-Type: application/json" \
        -d "$request_body")
    
    # 分离响应体和状态码
    local http_body=$(echo $response | sed -E 's/HTTPSTATUS\:[0-9]{3}$//')
    local http_status=$(echo $response | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')
    
    echo -e "   HTTP状态码: $http_status"
    
    if [[ $http_status -eq 200 ]]; then
        echo -e "${GREEN}   ✅ $service_display 代理成功${NC}"
        local content
        if [[ "$service_name" == "anthropic" ]]; then
            content=$(echo $http_body | jq -r '.content[0].text // .error.message // "无法解析响应"' 2>/dev/null || echo "响应格式异常")
        else
            content=$(echo $http_body | jq -r '.choices[0].message.content // .error.message // "无法解析响应"' 2>/dev/null || echo "响应格式异常")
        fi
        echo -e "   响应预览: $content"
    elif [[ $http_status -eq 503 ]]; then
        echo -e "${RED}   ❌ 服务不可用 (503) - 可能是API密钥未配置${NC}"
        echo -e "   响应详情: $(echo $http_body | jq . 2>/dev/null || echo $http_body)"
    else
        echo -e "${RED}   ❌ 请求失败 ($http_status)${NC}"
        echo -e "   响应详情: $(echo $http_body | jq . 2>/dev/null || echo $http_body)"
    fi
    
    echo ""
}

# 2. 测试所有AI服务
echo -e "${YELLOW}2. 测试所有AI服务...${NC}"

# 测试 SiliconFlow
test_ai_service "siliconflow" "SiliconFlow" "Qwen/Qwen2.5-7B-Instruct" "你好，请简单回复"

# 测试 OpenAI
test_ai_service "openai" "OpenAI" "gpt-3.5-turbo" "Hello, please reply briefly"

# 测试 Anthropic
test_ai_service "anthropic" "Anthropic" "claude-3-haiku-20240307" "Hello, please reply briefly"

# 测试 ElevenLabs 语音服务
echo -e "${YELLOW}🔧 测试 ElevenLabs 语音服务...${NC}"
ELEVENLABS_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$HOST" \
  -H "x-proxy-service: elevenlabs" \
  -H "x-proxy-path: /v1/voices")

ELEVENLABS_BODY=$(echo $ELEVENLABS_RESPONSE | sed -E 's/HTTPSTATUS\:[0-9]{3}$//')
ELEVENLABS_STATUS=$(echo $ELEVENLABS_RESPONSE | tr -d '\n' | sed -E 's/.*HTTPSTATUS:([0-9]{3})$/\1/')

echo -e "   HTTP状态码: $ELEVENLABS_STATUS"

if [[ $ELEVENLABS_STATUS -eq 200 ]]; then
    echo -e "${GREEN}   ✅ ElevenLabs 代理成功${NC}"
    echo -e "   可用语音数量: $(echo $ELEVENLABS_BODY | jq '.voices | length' 2>/dev/null || echo "无法解析")"
elif [[ $ELEVENLABS_STATUS -eq 503 ]]; then
    echo -e "${RED}   ❌ 服务不可用 (503) - 可能是API密钥未配置${NC}"
    echo -e "   响应详情: $(echo $ELEVENLABS_BODY | jq . 2>/dev/null || echo $ELEVENLABS_BODY)"
else
    echo -e "${RED}   ❌ 请求失败 ($ELEVENLABS_STATUS)${NC}"
    echo -e "   响应详情: $(echo $ELEVENLABS_BODY | jq . 2>/dev/null || echo $ELEVENLABS_BODY)"
fi

echo ""

# 3. 测试服务状态查询
echo -e "${YELLOW}3. 测试服务状态查询...${NC}"
echo -e "${YELLOW}   测试 SiliconFlow 模型列表...${NC}"
curl -s -X GET "$HOST" \
  -H "x-proxy-service: siliconflow" \
  -H "x-proxy-path: /v1/models" \
  -w "   HTTP状态码: %{http_code}\n" | head -3

echo ""
echo -e "${GREEN}🏁 测试完成${NC}" 