#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}========== 使用公网URL运行函数 ==========${NC}"

# 1. 检查参数
FUNCTION_NAME=${1:-"revenuecat-webhook"}
PORT=${2:-"8000"}
echo -e "${YELLOW}将运行函数: ${FUNCTION_NAME} 在端口: ${PORT}${NC}"

# 支持的函数列表
SUPPORTED_FUNCTIONS=("revenuecat-webhook" "revenuecat-check" "unified-proxy")
if [[ ! " ${SUPPORTED_FUNCTIONS[@]} " =~ " ${FUNCTION_NAME} " ]]; then
    echo -e "${YELLOW}支持的函数: ${SUPPORTED_FUNCTIONS[*]}${NC}"
fi

# 2. 检查ngrok是否已安装
if ! command -v ngrok &> /dev/null; then
    echo -e "${RED}未找到ngrok，请先安装它: https://ngrok.com/download${NC}"
    echo -e "或者使用Homebrew安装: brew install ngrok"
    exit 1
fi

# 3. 设置环境变量
echo -e "${YELLOW}设置环境变量...${NC}"
# 确保Deno可用
export PATH="$HOME/.deno/bin:$PATH"
# Supabase URL和密钥
export SUPABASE_URL="http://127.0.0.1:54321"
export SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
export SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"
# RevenueCat webhook 测试密钥
export REVENUECAT_WEBHOOK_SECRET="test_webhook_secret" 

# 加载 .env 文件中的额外环境变量（如果存在）
# 尝试多个可能的 .env 文件位置
ENV_PATHS=("../.env" "./.env" "../../.env")
for env_path in "${ENV_PATHS[@]}"; do
    if [[ -f "$env_path" ]]; then
        echo -e "${YELLOW}加载 $env_path 文件中的环境变量...${NC}"
        set -o allexport
        source "$env_path"
        set +o allexport
        break
    fi
done

# 如果没有找到 .env 文件，显示提示信息
if [[ -z "${SILICONFLOW_API_KEY:-}" && -z "${OPENAI_API_KEY:-}" ]]; then
    echo -e "${RED}警告: 未找到 .env 文件或API密钥未设置${NC}"
    echo -e "${YELLOW}请在项目根目录创建 .env 文件并设置以下变量:${NC}"
    echo -e "${YELLOW}SILICONFLOW_API_KEY=你的SiliconFlow密钥${NC}"
    echo -e "${YELLOW}OPENAI_API_KEY=你的OpenAI密钥${NC}"
    echo ""
fi

# 统一代理函数特定的环境变量
if [[ "$FUNCTION_NAME" == "unified-proxy" ]]; then
    if [[ -z "${SILICONFLOW_API_KEY:-}" ]]; then
        echo -e "${RED}警告: SILICONFLOW_API_KEY 未设置，AI 代理功能可能无法正常工作${NC}"
    fi
    if [[ -z "${OPENAI_API_KEY:-}" ]]; then
        echo -e "${YELLOW}注意: OPENAI_API_KEY 未设置，将仅使用 SiliconFlow API${NC}"
    fi
fi

# 设置缓存目录
export DENO_DIR="$(pwd)/.deno_cache"
mkdir -p $DENO_DIR

# 4. 切换到函数目录
cd "$(dirname $0)/../supabase/functions/$FUNCTION_NAME"
echo -e "${YELLOW}当前目录: $(pwd)${NC}"

# 5. 创建一个临时文件用于存储URL
URL_FILE=$(mktemp)

# 尝试使用 ngrok
NGROK_AVAILABLE=false
PUBLIC_URL=""

# 6. 启动ngrok并保存URL到临时文件
echo -e "${YELLOW}尝试启动ngrok...${NC}"
ngrok http $PORT --log=stdout > /dev/null &
NGROK_PID=$!

# 等待ngrok启动
sleep 2

# 获取ngrok URL
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | grep -o '"public_url":"[^"]*' | grep -o 'http[^"]*')
if [ -z "$NGROK_URL" ]; then
    echo -e "${YELLOW}无法使用ngrok，将尝试使用Expo URL作为替代...${NC}"
    kill $NGROK_PID 2>/dev/null

    # 尝试使用 Expo URL
    if [ -n "${EXPO_PUBLIC_URL}" ]; then
        PUBLIC_URL="${EXPO_PUBLIC_URL}"
        echo -e "${GREEN}使用Expo公网URL: ${PUBLIC_URL}${NC}"
    else
        # 尝试获取当前设备 IP
        DEVICE_IP=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -n 1)
        if [ -n "$DEVICE_IP" ]; then
            PUBLIC_URL="http://${DEVICE_IP}:${PORT}"
            echo -e "${YELLOW}使用本地网络URL: ${PUBLIC_URL}${NC}"
            echo -e "${YELLOW}注意：此URL只能在同一网络下访问${NC}"
        else
            echo -e "${RED}无法获取可用的公网URL，将使用localhost继续${NC}"
            PUBLIC_URL="http://localhost:${PORT}"
        fi
    fi
else
    PUBLIC_URL=$NGROK_URL
    NGROK_AVAILABLE=true
    echo -e "${GREEN}获取到ngrok公网URL: ${PUBLIC_URL}${NC}"
fi

if [[ "$FUNCTION_NAME" == "revenuecat-webhook" ]]; then
    echo -e "${YELLOW}您可以在RevenueCat后台配置此URL作为webhook端点${NC}"
elif [[ "$FUNCTION_NAME" == "unified-proxy" ]]; then
    echo -e "${YELLOW}您可以使用此URL作为AI代理API端点${NC}"
fi
echo $PUBLIC_URL

# 7. 运行函数
echo -e "${YELLOW}使用本地Deno启动函数...${NC}"
echo -e "${YELLOW}按Ctrl+C终止服务器${NC}"
echo -e "${GREEN}-----------------------------------${NC}"

# 使用trap确保程序退出时清理ngrok进程
if [ "$NGROK_AVAILABLE" = true ]; then
    trap 'echo -e "${YELLOW}停止ngrok...${NC}"; kill $NGROK_PID 2>/dev/null; rm -f $URL_FILE' EXIT
else
    trap 'rm -f $URL_FILE' EXIT
fi

# 使用参数来确保网络可用，并访问环境变量
deno run --allow-net --allow-env --allow-read --import-map=./import_map.json index.ts

echo -e "${GREEN}========== 执行完成 ==========${NC}"