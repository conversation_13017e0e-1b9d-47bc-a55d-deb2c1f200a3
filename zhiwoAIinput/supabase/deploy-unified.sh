#!/bin/bash
# 知我AI输入法 - 统一部署脚本
# 支持本地测试和云端部署

set -euo pipefail

# 颜色定义
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly RED='\033[0;31m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# 全局变量
DEPLOYMENT_MODE=""
ENVIRONMENT=${ENVIRONMENT:-staging}
FUNCTION_NAME=""
PORT=${PORT:-8000}
BACKUP_ENABLED=${BACKUP_ENABLED:-true}
DRY_RUN=${DRY_RUN:-false}
FORCE_DEPLOY=${FORCE_DEPLOY:-false}

# 打印函数
info() { echo -e "${GREEN}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
debug() { echo -e "${BLUE}[DEBUG]${NC} $1"; }

# 显示使用帮助
show_help() {
    cat << EOF
知我AI输入法 - 统一部署脚本

用法:
    $0 <模式> [选项]

模式:
    local-test [函数名] [端口]   本地测试模式，运行指定的边缘函数
    local-public [函数名] [端口] 本地公网模式，使用ngrok暴露函数
    cloud                      云端部署模式，部署所有函数到Supabase

支持的函数:
    - revenuecat-webhook       RevenueCat支付回调处理
    - revenuecat-check        订阅状态检查  
    - unified-proxy           统一代理服务 (AI + 通用代理)

云端部署选项:
    -e, --environment ENV     部署环境 (staging/production) [默认: staging]
    -d, --dry-run            预演模式，不执行实际部署
    -f, --force              强制部署，跳过确认
    -b, --no-backup          跳过备份步骤

示例:
    $0 local-test unified-proxy            # 本地测试统一代理函数
    $0 local-public unified-proxy 8001     # 使用公网URL测试统一代理函数
    $0 cloud                               # 部署到staging环境
    $0 cloud -e production -f              # 强制部署到生产环境

环境变量:
    本地测试需要: SILICONFLOW_API_KEY, OPENAI_API_KEY (可选)
    云端部署需要: PROJECT_ID, SUPABASE_ACCESS_TOKEN
EOF
}

# 解析命令行参数
parse_arguments() {
    if [[ $# -eq 0 ]]; then
        error "请指定部署模式"
        show_help
        exit 1
    fi
    
    DEPLOYMENT_MODE="$1"
    shift
    
    case "$DEPLOYMENT_MODE" in
        local-test)
            FUNCTION_NAME=${1:-"revenuecat-webhook"}
            PORT=${2:-8000}
            ;;
        local-public)
            FUNCTION_NAME=${1:-"revenuecat-webhook"}
            PORT=${2:-8000}
            ;;
        cloud)
            while [[ $# -gt 0 ]]; do
                case $1 in
                    -e|--environment)
                        ENVIRONMENT="$2"
                        shift 2
                        ;;
                    -d|--dry-run)
                        DRY_RUN=true
                        shift
                        ;;
                    -f|--force)
                        FORCE_DEPLOY=true
                        shift
                        ;;
                    -b|--no-backup)
                        BACKUP_ENABLED=false
                        shift
                        ;;
                    -h|--help)
                        show_help
                        exit 0
                        ;;
                    *)
                        error "未知参数: $1"
                        show_help
                        exit 1
                        ;;
                esac
            done
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            error "未知部署模式: $DEPLOYMENT_MODE"
            show_help
            exit 1
            ;;
    esac
}

# 设置本地环境变量
setup_local_env() {
    info "设置本地环境变量..."
    
    # 确保Deno可用
    export PATH="$HOME/.deno/bin:$PATH"
    
    # Supabase本地环境
    export SUPABASE_URL="http://127.0.0.1:54321"
    export SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
    export SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"
    export REVENUECAT_WEBHOOK_SECRET="test_webhook_secret"
    
    # 加载环境变量文件
    # 优先加载特定环境的配置文件
    local env_file=""
    if [[ "$DEPLOYMENT_MODE" == "local-test" || "$DEPLOYMENT_MODE" == "local-public" ]]; then
        # 本地测试模式优先使用开发环境配置
        if [[ -f "../.env.development" ]]; then
            env_file="../.env.development"
        elif [[ -f "../.env" ]]; then
            env_file="../.env"
        fi
    else
        # 云端部署模式根据环境选择配置文件
        if [[ -f "../.env.${ENVIRONMENT}" ]]; then
            env_file="../.env.${ENVIRONMENT}"
        elif [[ -f "../.env" ]]; then
            env_file="../.env"
        fi
    fi
    
    if [[ -n "$env_file" ]]; then
        info "加载环境变量文件: $env_file"
        set -o allexport
        source "$env_file"
        set +o allexport
    else
        warn "未找到环境变量文件，将使用系统环境变量"
    fi
    
    # 统一代理函数特定的环境变量检查
    if [[ "$FUNCTION_NAME" == "unified-proxy" ]]; then
        if [[ -z "${SILICONFLOW_API_KEY:-}" ]]; then
            warn "SILICONFLOW_API_KEY 未设置，AI代理功能可能无法正常工作"
        fi
        if [[ -z "${OPENAI_API_KEY:-}" ]]; then
            info "OPENAI_API_KEY 未设置，将仅使用 SiliconFlow API"
        fi
        
        # HTTP代理设置（用于访问国外API）
        if [[ -n "${HTTP_PROXY:-}" ]]; then
            info "检测到HTTP代理设置: $HTTP_PROXY"
            export https_proxy="$HTTP_PROXY"
            export HTTPS_PROXY="$HTTP_PROXY"
        elif [[ -n "${HTTPS_PROXY:-}" ]]; then
            info "检测到HTTPS代理设置: $HTTPS_PROXY"
            export HTTP_PROXY="$HTTPS_PROXY"
            export http_proxy="$HTTPS_PROXY"
        else
            # 尝试自动检测常见的代理端口
            for port in 7890 8080 1087 1080; do
                if nc -z localhost $port 2>/dev/null; then
                    info "自动检测到本地代理端口: $port"
                    export HTTP_PROXY="http://127.0.0.1:$port"
                    export HTTPS_PROXY="http://127.0.0.1:$port"
                    export http_proxy="http://127.0.0.1:$port"
                    export https_proxy="http://127.0.0.1:$port"
                    break
                fi
            done
            
            if [[ -n "${HTTP_PROXY:-}" ]]; then
                info "已自动配置代理: $HTTP_PROXY"
            else
                warn "未检测到代理设置，访问OpenAI API可能失败"
                info "如需使用代理，请在.env.development中设置 HTTP_PROXY=http://127.0.0.1:端口"
            fi
        fi
    fi
    
    # 设置缓存目录
    export DENO_DIR="$(pwd)/.deno_cache"
    mkdir -p $DENO_DIR
}

# 验证函数存在
validate_function() {
    local supported_functions=("revenuecat-webhook" "revenuecat-check" "unified-proxy")
    
    if [[ ! " ${supported_functions[@]} " =~ " ${FUNCTION_NAME} " ]]; then
        error "不支持的函数: $FUNCTION_NAME"
        info "支持的函数: ${supported_functions[*]}"
        exit 1
    fi
    
    if [[ ! -d "functions/$FUNCTION_NAME" ]]; then
        error "函数目录不存在: functions/$FUNCTION_NAME"
        exit 1
    fi
}

# 本地测试模式
run_local_test() {
    info "======== 本地测试模式 ========"
    info "函数: $FUNCTION_NAME"
    info "端口: $PORT"
    
    validate_function
    setup_local_env
    
    # 设置端口环境变量
    export PORT="$PORT"
    
    cd "functions/$FUNCTION_NAME"
    info "当前目录: $(pwd)"
    
    info "启动函数..."
    info "按 Ctrl+C 停止服务器"
    echo -e "${GREEN}-----------------------------------${NC}"
    
    deno run --allow-net --allow-env --allow-read --import-map=./import_map.json index.ts
}

# 本地公网模式
run_local_public() {
    info "======== 本地公网模式 ========"
    info "函数: $FUNCTION_NAME"
    info "端口: $PORT"
    
    # 检查ngrok
    if ! command -v ngrok &> /dev/null; then
        error "未找到ngrok，请先安装: https://ngrok.com/download"
        exit 1
    fi
    
    validate_function
    setup_local_env
    
    # 启动ngrok
    info "启动ngrok..."
    ngrok http $PORT --log=stdout > /dev/null &
    NGROK_PID=$!
    
    sleep 2
    
    # 获取公网URL
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | grep -o '"public_url":"[^"]*' | grep -o 'http[^"]*' | head -1)
    
    if [ -n "$NGROK_URL" ]; then
        info "✅ 公网URL: $NGROK_URL"
        
        if [[ "$FUNCTION_NAME" == "revenuecat-webhook" ]]; then
            info "可以在RevenueCat后台配置此URL作为webhook端点"
        elif [[ "$FUNCTION_NAME" == "unified-proxy" ]]; then
            info "可以使用此URL作为统一代理API端点"
        fi
    else
        warn "无法获取ngrok URL，使用本地地址"
        kill $NGROK_PID 2>/dev/null
    fi
    
    # 设置清理函数
    trap 'info "停止ngrok..."; kill $NGROK_PID 2>/dev/null' EXIT
    
    cd "functions/$FUNCTION_NAME"
    info "当前目录: $(pwd)"
    
    info "启动函数..."
    info "按 Ctrl+C 停止服务器"
    echo -e "${GREEN}-----------------------------------${NC}"
    
    deno run --allow-net --allow-env --allow-read --import-map=./import_map.json index.ts
}

# 云端部署模式
run_cloud_deploy() {
    info "======== 云端部署模式 ========"
    info "环境: $ENVIRONMENT"
    
    if [ "$DRY_RUN" = "true" ]; then
        warn "这是预演模式，不会执行实际操作"
    fi
    
    # 调用原来的云端部署脚本
    if [ "$DRY_RUN" = "true" ]; then
        export DRY_RUN=true
    fi
    if [ "$FORCE_DEPLOY" = "true" ]; then
        export FORCE_DEPLOY=true
    fi
    if [ "$BACKUP_ENABLED" = "false" ]; then
        export BACKUP_ENABLED=false
    fi
    export ENVIRONMENT="$ENVIRONMENT"
    
    # 构建参数
    local args=()
    args+=("-e" "$ENVIRONMENT")
    
    if [ "$DRY_RUN" = "true" ]; then
        args+=("--dry-run")
    fi
    
    if [ "$FORCE_DEPLOY" = "true" ]; then
        args+=("--force")
    fi
    
    if [ "$BACKUP_ENABLED" = "false" ]; then
        args+=("--no-backup")
    fi
    
    # 执行云端部署脚本
    ./deploy-cloud.sh "${args[@]}"
}

# 主函数
main() {
    parse_arguments "$@"
    
    case "$DEPLOYMENT_MODE" in
        local-test)
            run_local_test
            ;;
        local-public)
            run_local_public
            ;;
        cloud)
            run_cloud_deploy
            ;;
        *)
            error "未知部署模式: $DEPLOYMENT_MODE"
            exit 1
            ;;
    esac
}

# 错误处理
trap 'error "执行过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@" 