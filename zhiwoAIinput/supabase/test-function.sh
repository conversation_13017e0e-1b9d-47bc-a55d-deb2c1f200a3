#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}========== Edge Function 本地测试脚本 ==========${NC}"

# 1. 检查参数
FUNCTION_NAME=${1:-"revenuecat-webhook"}
echo -e "${YELLOW}将测试函数: ${FUNCTION_NAME}${NC}"

# 2. 设置环境变量
echo -e "${YELLOW}设置环境变量...${NC}"
# Supabase URL和密钥 - 从run-local.sh输出中获取
export SUPABASE_URL="http://127.0.0.1:54321"
export SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
export SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"

# RevenueCat webhook 测试密钥
export REVENUECAT_WEBHOOK_SECRET="test_webhook_secret" 

# 3. 运行函数
echo -e "${YELLOW}启动本地函数服务器...${NC}"
echo -e "${YELLOW}按Ctrl+C终止测试服务器${NC}"
echo -e "${GREEN}-----------------------------------${NC}"

cd $(dirname $0)/..
# 直接使用已导出的环境变量，不使用env-file参数
supabase functions serve $FUNCTION_NAME --no-verify-jwt

echo -e "${GREEN}========== 测试完成 ==========${NC}" 