#!/bin/bash

# Supabase边缘函数环境变量设置脚本
# 用于本地开发和云端部署的环境变量管理

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：检查必需的工具
check_requirements() {
    print_info "检查必需的工具..."
    
    if ! command -v supabase &> /dev/null; then
        print_error "Supabase CLI 未安装。请访问 https://supabase.com/docs/guides/cli 安装"
        exit 1
    fi
    
    print_success "所有必需工具已安装"
}

# 函数：设置本地环境变量
setup_local_env() {
    print_info "设置本地环境变量..."
    
    # 检查是否存在 .env.local 文件
    if [[ ! -f ".env.local" ]]; then
        if [[ -f "env.template" ]]; then
            print_info "从模板创建 .env.local 文件..."
            cp env.template .env.local
            print_warning "请编辑 .env.local 文件并填入真实的API密钥"
        else
            print_error "找不到 env.template 文件"
            exit 1
        fi
    else
        print_info ".env.local 文件已存在"
    fi
    
    # 验证环境变量
    print_info "验证环境变量配置..."
    source .env.local
    
    local missing_vars=()
    
    if [[ -z "$SILICONFLOW_API_KEY" || "$SILICONFLOW_API_KEY" == "your_siliconflow_api_key_here" ]]; then
        missing_vars+=("SILICONFLOW_API_KEY")
    fi
    
    if [[ -z "$OPENAI_API_KEY" || "$OPENAI_API_KEY" == "your_openai_api_key_here" ]]; then
        missing_vars+=("OPENAI_API_KEY")
    fi
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        print_warning "以下环境变量需要配置: ${missing_vars[*]}"
        print_warning "请编辑 .env.local 文件并填入真实的API密钥"
    else
        print_success "本地环境变量配置完成"
    fi
}

# 函数：设置云端环境变量
setup_cloud_env() {
    print_info "设置云端环境变量..."
    
    # 检查是否登录到 Supabase
    if ! supabase projects list &> /dev/null; then
        print_error "请先登录到 Supabase: supabase login"
        exit 1
    fi
    
    # 从 .env.local 文件读取变量并设置到云端
    if [[ -f ".env.local" ]]; then
        source .env.local
        
        print_info "设置云端secrets..."
        
        # 设置 AI 服务密钥
        if [[ -n "$SILICONFLOW_API_KEY" && "$SILICONFLOW_API_KEY" != "your_siliconflow_api_key_here" ]]; then
            supabase secrets set SILICONFLOW_API_KEY="$SILICONFLOW_API_KEY"
            print_success "SILICONFLOW_API_KEY 已设置"
        fi
        
        if [[ -n "$OPENAI_API_KEY" && "$OPENAI_API_KEY" != "your_openai_api_key_here" ]]; then
            supabase secrets set OPENAI_API_KEY="$OPENAI_API_KEY"
            print_success "OPENAI_API_KEY 已设置"
        fi
        
        if [[ -n "$ANTHROPIC_API_KEY" && "$ANTHROPIC_API_KEY" != "your_anthropic_api_key_here" ]]; then
            supabase secrets set ANTHROPIC_API_KEY="$ANTHROPIC_API_KEY"
            print_success "ANTHROPIC_API_KEY 已设置"
        fi
        
        if [[ -n "$ELEVENLABS_API_KEY" && "$ELEVENLABS_API_KEY" != "your_elevenlabs_api_key_here" ]]; then
            supabase secrets set ELEVENLABS_API_KEY="$ELEVENLABS_API_KEY"
            print_success "ELEVENLABS_API_KEY 已设置"
        fi
        
        # 设置其他配置
        supabase secrets set ENVIRONMENT="production"
        supabase secrets set PORT="8000"
        
        print_success "云端环境变量设置完成"
    else
        print_error "找不到 .env.local 文件"
        exit 1
    fi
}

# 函数：列出云端环境变量
list_cloud_secrets() {
    print_info "列出云端secrets..."
    supabase secrets list
}

# 函数：显示帮助信息
show_help() {
    echo "Supabase边缘函数环境变量管理工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  local    设置本地开发环境变量"
    echo "  cloud    设置云端环境变量"
    echo "  list     列出云端环境变量"
    echo "  help     显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 local     # 设置本地环境"
    echo "  $0 cloud     # 设置云端环境"
    echo "  $0 list      # 查看云端配置"
}

# 主函数
main() {
    case "${1:-}" in
        "local")
            check_requirements
            setup_local_env
            ;;
        "cloud")
            check_requirements
            setup_cloud_env
            ;;
        "list")
            check_requirements
            list_cloud_secrets
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "无效的选项: ${1:-}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 