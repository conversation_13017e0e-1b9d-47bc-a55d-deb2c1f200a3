// 订阅状态检查和维护函数
// 该函数用于检查用户的订阅状态，确保VIP状态与订阅记录一致
// 可以通过定时任务或手动触发运行

// @deno-types="npm:@types/node"
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

// 声明Deno命名空间
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

// 初始化Supabase客户端，使用环境变量中的凭证
const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? 'http://127.0.0.1:54321';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

// 使用服务角色密钥初始化Supabase客户端以允许全权限访问
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// 安全密钥，用于验证API请求
const apiSecretKey = Deno.env.get("CHECK_API_SECRET") || "local_development_key";

// 日志函数
async function logMessage(message: string, level: 'info' | 'warning' | 'error' = 'info'): Promise<void> {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
}

// 记录维护任务到数据库
async function recordMaintenanceLog(
  taskType: string,
  triggeredBy: string = 'manual',
  executionId?: string
): Promise<string | null> {
  try {
    const { data, error } = await supabase.rpc('start_maintenance_task', {
      p_task_type: taskType,
      p_triggered_by: triggeredBy,
      p_execution_id: executionId
    });
    
    if (error) {
      await logMessage(`维护日志记录失败: ${error.message}`, 'warning');
      return null;
    }
    
    await logMessage(`维护任务已记录，日志ID: ${data}`);
    return data;
  } catch (error: any) {
    await logMessage(`维护日志记录异常: ${error.message}`, 'warning');
    return null;
  }
}

// 更新维护任务结果
async function updateMaintenanceLog(
  logId: string,
  status: string,
  result: any,
  errorMessage?: string
): Promise<void> {
  try {
    // 从结果中提取统计数据
    let usersChecked = 0;
    let usersUpdated = 0;
    let subscriptionsCleaned = 0;
    let tempEntitlementsCleaned = 0;
    let trialsCleaned = 0;
    
    if (result) {
      if (result.total_users_checked !== undefined) {
        usersChecked = typeof result.total_users_checked === 'number' ? result.total_users_checked : 0;
      }
      
      if (result.users_to_update !== undefined) {
        usersUpdated = typeof result.users_to_update === 'number' ? result.users_to_update : 0;
      }
      
      if (result.expired_subscriptions !== undefined) {
        subscriptionsCleaned = typeof result.expired_subscriptions === 'number' ? result.expired_subscriptions : 0;
      }
      
      if (result.expired_entitlements !== undefined) {
        tempEntitlementsCleaned = typeof result.expired_entitlements === 'number' ? result.expired_entitlements : 0;
      }
      
      if (result.expired_trials !== undefined) {
        trialsCleaned = typeof result.expired_trials === 'number' ? result.expired_trials : 0;
      }
      
      // 处理run-all的复合结果
      if (result.vip_status_check) {
        usersChecked = result.vip_status_check.total_users_checked || 0;
        usersUpdated = result.vip_status_check.users_to_update || 0;
      }
      if (result.expired_cleanup) {
        subscriptionsCleaned = result.expired_cleanup.expired_subscriptions || 0;
      }
      if (result.temp_entitlements_cleanup) {
        tempEntitlementsCleaned = result.temp_entitlements_cleanup.expired_entitlements || 0;
      }
      if (result.trials_cleanup) {
        trialsCleaned = result.trials_cleanup.expired_trials || 0;
      }
    }
    
    const { error } = await supabase.rpc('complete_maintenance_task', {
      p_log_id: logId,
      p_status: status,
      p_users_checked: usersChecked,
      p_users_updated: usersUpdated,
      p_subscriptions_cleaned: subscriptionsCleaned,
      p_temp_entitlements_cleaned: tempEntitlementsCleaned,
      p_trials_cleaned: trialsCleaned,
      p_execution_result: result ? JSON.stringify(result) : null,
      p_error_message: errorMessage || null
    });
    
    if (error) {
      await logMessage(`维护日志更新失败: ${error.message}`, 'warning');
    } else {
      await logMessage(`维护日志更新成功: ${logId}`);
    }
  } catch (error: any) {
    await logMessage(`维护日志更新异常: ${error.message}`, 'warning');
  }
}

// 检查并修复用户VIP状态
async function checkAndFixVIPStatus(userId?: string, dryRun = true): Promise<any> {
  try {
    // 构建SQL查询 (现在包含临时授权和试用订阅)
    let query = `
      WITH active_subs AS (
        -- 检查常规订阅
        SELECT 
          user_id,
          product_id,
          expires_at,
          environment,
          'subscription' as source
        FROM 
          public.subscriptions
        WHERE 
          status = 'active' 
          AND (expires_at IS NULL OR expires_at > NOW())
          ${userId ? "AND user_id = $1" : ""}
        
        UNION ALL
        
        -- 检查有效的临时授权
        SELECT
          user_id,
          product_id,
          expires_at,
          'production' as environment,
          'temporary_entitlement' as source
        FROM
          public.temporary_entitlements
        WHERE
          status = 'active'
          AND expires_at > NOW()
          ${userId ? "AND user_id = $1" : ""}
        
        UNION ALL
        
        -- 检查试用期
        SELECT
          user_id,
          product_id,
          trial_end_at as expires_at,
          environment,
          'trial' as source
        FROM
          public.subscriptions
        WHERE
          is_trial = true
          AND status = 'active'
          AND trial_end_at > NOW()
          ${userId ? "AND user_id = $1" : ""}
      ),
      
      expected_vip_status AS (
        SELECT 
          p.id,
          p.is_vip,
          CASE WHEN s.user_id IS NOT NULL THEN true ELSE false END as should_be_vip,
          s.product_id as vip_product_id,
          s.expires_at as vip_expires_at,
          s.environment as vip_environment,
          s.source as vip_source,
          sp.features as plan_features
        FROM 
          public.profiles p
        LEFT JOIN 
          active_subs s ON p.id = s.user_id
        LEFT JOIN 
          public.subscription_plans sp ON s.product_id = sp.plan_id
        WHERE 
          p.is_vip != CASE WHEN s.user_id IS NOT NULL THEN true ELSE false END
          ${userId ? "AND p.id = $1" : ""}
      ),
      
      users_to_update AS (
        SELECT 
          id,
          is_vip,
          should_be_vip,
          vip_product_id,
          vip_expires_at,
          vip_environment,
          vip_source,
          plan_features
        FROM expected_vip_status
      )
      
      SELECT json_agg(
        json_build_object(
          'id', id,
          'is_vip', is_vip,
          'should_be_vip', should_be_vip,
          'vip_product_id', vip_product_id,
          'vip_expires_at', vip_expires_at,
          'vip_environment', vip_environment,
          'vip_source', vip_source,
          'plan_features', plan_features
        )
      ) as users_data FROM users_to_update
    `;
    
    // 执行查询
    const { data: result, error } = await supabase.rpc('exec_sql', {
      query: query,
      params: userId ? [userId] : []
    });
    
    if (error) {
      throw new Error(`查询错误: ${error.message}`);
    }

    // 修复：正确处理jsonb返回值
    let users: any[] = [];
    try {
      if (result && typeof result === 'object' && result.users_data) {
        // 如果返回的是包含users_data字段的对象
        users = result.users_data || [];
      } else if (result && Array.isArray(result)) {
        // 如果直接返回数组
        users = result;
      } else if (result && typeof result === 'string') {
        // 如果返回字符串，尝试解析JSON
        const parsed = JSON.parse(result);
        if (parsed && parsed.users_data) {
          users = parsed.users_data || [];
        } else if (Array.isArray(parsed)) {
          users = parsed;
        }
      } else {
        // 其他情况，默认为空数组
        users = [];
      }
      
      // 确保users是数组，并过滤掉null值
      if (!Array.isArray(users)) {
        users = [];
      } else {
        users = users.filter(user => user !== null);
      }
    } catch (parseError: any) {
      await logMessage(`解析查询结果时出错: ${parseError.message}`, 'warning');
      users = [];
    }
    
    await logMessage(`找到 ${users.length} 个需要更新VIP状态的用户`);
    
    if (dryRun) {
      return {
        users_to_update: users,
        dry_run: true,
        message: "仅查询模式，未执行更新"
      };
    }

    // 如果不是仅查询模式，执行更新
    const updates = [];
    for (const user of users) {
      let updateQuery = '';
      let params: any[] = [];
      
      if (user.should_be_vip) {
        // 用户应该是VIP但当前不是
        updateQuery = `
          UPDATE public.profiles
          SET 
            is_vip = true,
            vip_product_id = $1,
            vip_expires_at = $2,
            vip_environment = $3,
            vip_source = $4,
            vip_is_trial = $5,
            vip_updated_at = NOW(),
            voice_minutes_limit = $6,
            api_key_type = $7
          WHERE id = $8
          RETURNING json_build_object(
            'id', id,
            'is_vip', is_vip,
            'vip_product_id', vip_product_id,
            'vip_expires_at', vip_expires_at,
            'vip_source', vip_source
          ) as update_result
        `;
        
        const voiceMinutes = user.plan_features?.voice_minutes || 0;
        const apiKeyType = user.plan_features?.api_key_type || 'free';
        const isTrial = user.vip_source === 'trial';
        
        params = [
          user.vip_product_id, 
          user.vip_expires_at, 
          user.vip_environment || 'production',
          user.vip_source || 'subscription',
          isTrial,
          voiceMinutes,
          apiKeyType,
          user.id
        ];
      } else {
        // 用户不应该是VIP但当前是
        updateQuery = `
          UPDATE public.profiles
          SET 
            is_vip = false,
            vip_updated_at = NOW(),
            vip_source = NULL,
            vip_is_trial = false,
            voice_minutes_limit = 0,
            api_key_type = 'free'
          WHERE id = $1
          RETURNING json_build_object(
            'id', id,
            'is_vip', is_vip,
            'vip_product_id', vip_product_id,
            'vip_expires_at', vip_expires_at
          ) as update_result
        `;
        
        params = [user.id];
      }
      
      // 新增：updateQuery 有效性检查
      if (!updateQuery || typeof updateQuery !== 'string' || !updateQuery.trim()) {
        updates.push({
          user_id: user.id,
          status: 'error',
          error: '未生成有效的SQL更新语句',
          details: user
        });
        continue;
      }
      
      try {
        const { data: updateResult, error: updateError } = await supabase.rpc('exec_sql', {
          query: updateQuery,
          params: params
        });
        
        if (updateError) {
          throw updateError;
        }
        
        await logMessage(`更新用户 ${user.id} 的VIP状态为 ${user.should_be_vip ? 'VIP' : '非VIP'}`);
        updates.push({ 
          user_id: user.id, 
          status: 'success', 
          new_vip_status: user.should_be_vip,
          vip_source: user.should_be_vip ? user.vip_source : null,
          details: updateResult
        });
      } catch (updateError: any) {
        await logMessage(`更新用户 ${user.id} 的VIP状态失败: ${updateError.message}`, 'error');
        updates.push({ 
          user_id: user.id, 
          status: 'error', 
          error: updateError.message 
        });
      }
    }
    
    return {
      total_users_checked: userId ? 1 : 'all',
      users_to_update: users.length,
      updates: updates,
      dry_run: false
    };
  } catch (error: any) {
    await logMessage(`检查和修复VIP状态时出错: ${error.message}`, 'error');
    throw error;
  }
}

// 清理过期订阅
async function cleanupExpiredSubscriptions(dryRun = true): Promise<any> {
  try {
    let query = `
      SELECT json_agg(
        json_build_object(
          'id', id,
          'user_id', user_id,
          'product_id', product_id,
          'status', status,
          'expires_at', expires_at
        )
      ) as expired_subs_data
      FROM public.subscriptions
      WHERE status = 'expired' AND (expires_at IS NOT NULL AND expires_at < NOW())
    `;
    const { data: result, error } = await supabase.rpc('exec_sql', {
      query: query,
      params: []
    });
    if (error) {
      throw new Error(`查询错误: ${error.message}`);
    }
    
    let subs: any[] = [];
    try {
      if (result && typeof result === 'object' && result.expired_subs_data) {
        subs = result.expired_subs_data || [];
      } else if (result && Array.isArray(result)) {
        subs = result;
      } else if (result && typeof result === 'string') {
        const parsed = JSON.parse(result);
        if (parsed && parsed.expired_subs_data) {
          subs = parsed.expired_subs_data || [];
        } else if (Array.isArray(parsed)) {
          subs = parsed;
        }
      } else {
        subs = [];
      }
      
      if (!Array.isArray(subs)) {
        subs = [];
      } else {
        subs = subs.filter(sub => sub !== null);
      }
    } catch (parseError: any) {
      await logMessage(`解析过期订阅查询结果时出错: ${parseError.message}`, 'warning');
      subs = [];
    }
    if (dryRun) {
      return {
        expired_subscriptions: subs.length,
        dry_run: true
      };
    }
    // 如果不是仅查询模式，执行更新
    const updates = [];
    for (const sub of subs) {
      const updateQuery = `
        UPDATE public.subscriptions
        SET 
          status = 'expired',
          event_type = 'EXPIRATION',
          last_event_at = NOW()
        WHERE id = $1
        RETURNING id, status, event_type
      `;
      // 新增：updateQuery 有效性检查
      if (!updateQuery || typeof updateQuery !== 'string' || !updateQuery.trim()) {
        updates.push({
          subscription_id: sub.id,
          status: 'error',
          error: '未生成有效的SQL更新语句',
          details: sub
        });
        continue;
      }
      try {
        const { data: updateResult, error: updateError } = await supabase.rpc('exec_sql', {
          query: updateQuery,
          params: [sub.id]
        });
        
        if (updateError) {
          throw updateError;
        }
        
        await logMessage(`更新订阅 ${sub.id} 的状态为expired`);
        updates.push({ 
          subscription_id: sub.id, 
          user_id: sub.user_id,
          status: 'success', 
          details: updateResult
        });
      } catch (updateError: any) {
        await logMessage(`更新订阅 ${sub.id} 的状态失败: ${updateError.message}`, 'error');
        updates.push({ 
          subscription_id: sub.id, 
          user_id: sub.user_id,
          status: 'error', 
          error: updateError.message 
        });
      }
    }
    
    return {
      expired_subscriptions: subs.length,
      updates: updates,
      dry_run: false
    };
  } catch (error: any) {
    await logMessage(`清理过期订阅时出错: ${error.message}`, 'error');
    throw error;
  }
}

// 清理过期的临时授权
async function cleanupExpiredTemporaryEntitlements(dryRun = true): Promise<any> {
  try {
    let query = `
      SELECT json_agg(
        json_build_object(
          'id', id,
          'user_id', user_id,
          'product_id', product_id,
          'status', status,
          'expires_at', expires_at
        )
      ) as expired_entitlements_data
      FROM public.temporary_entitlements
      WHERE status = 'expired' AND (expires_at IS NOT NULL AND expires_at < NOW())
    `;
    const { data: result, error } = await supabase.rpc('exec_sql', {
      query: query,
      params: []
    });
    if (error) {
      throw new Error(`查询错误: ${error.message}`);
    }
    
    let ents: any[] = [];
    try {
      if (result && typeof result === 'object' && result.expired_entitlements_data) {
        ents = result.expired_entitlements_data || [];
      } else if (result && Array.isArray(result)) {
        ents = result;
      } else if (result && typeof result === 'string') {
        const parsed = JSON.parse(result);
        if (parsed && parsed.expired_entitlements_data) {
          ents = parsed.expired_entitlements_data || [];
        } else if (Array.isArray(parsed)) {
          ents = parsed;
        }
      } else {
        ents = [];
      }
      
      if (!Array.isArray(ents)) {
        ents = [];
      } else {
        ents = ents.filter(ent => ent !== null);
      }
    } catch (parseError: any) {
      await logMessage(`解析过期临时授权查询结果时出错: ${parseError.message}`, 'warning');
      ents = [];
    }
    if (dryRun) {
      return {
        expired_entitlements: ents.length,
        dry_run: true
      };
    }
    // 如果不是仅查询模式，执行更新
    const updates = [];
    for (const entitlement of ents) {
      const updateQuery = `
        UPDATE public.temporary_entitlements
        SET 
          status = 'expired',
          updated_at = NOW()
        WHERE id = $1
        RETURNING id, status
      `;
      // 新增：updateQuery 有效性检查
      if (!updateQuery || typeof updateQuery !== 'string' || !updateQuery.trim()) {
        updates.push({
          entitlement_id: entitlement.id,
          status: 'error',
          error: '未生成有效的SQL更新语句',
          details: entitlement
        });
        continue;
      }
      try {
        const { data: updateResult, error: updateError } = await supabase.rpc('exec_sql', {
          query: updateQuery,
          params: [entitlement.id]
        });
        
        if (updateError) {
          throw updateError;
        }
        
        await logMessage(`更新临时授权 ${entitlement.id} 的状态为expired`);
        updates.push({ 
          entitlement_id: entitlement.id, 
          user_id: entitlement.user_id,
          status: 'success', 
          details: updateResult
        });
      } catch (updateError: any) {
        await logMessage(`更新临时授权 ${entitlement.id} 的状态失败: ${updateError.message}`, 'error');
        updates.push({ 
          entitlement_id: entitlement.id, 
          user_id: entitlement.user_id,
          status: 'error', 
          error: updateError.message 
        });
      }
    }
    
    return {
      expired_entitlements: ents.length,
      updates: updates,
      dry_run: false
    };
  } catch (error: any) {
    await logMessage(`清理过期临时授权时出错: ${error.message}`, 'error');
    throw error;
  }
}

// 清理过期的试用期
async function cleanupExpiredTrials(dryRun = true): Promise<any> {
  try {
    let query = `
      SELECT json_agg(
        json_build_object(
          'id', id,
          'user_id', user_id,
          'product_id', product_id,
          'status', status,
          'trial_end_at', trial_end_at
        )
      ) as expired_trials_data
      FROM public.subscriptions
      WHERE is_trial = true AND status = 'expired' AND (trial_end_at IS NOT NULL AND trial_end_at < NOW())
    `;
    const { data: result, error } = await supabase.rpc('exec_sql', {
      query: query,
      params: []
    });
    if (error) {
      throw new Error(`查询错误: ${error.message}`);
    }
    
    let trials: any[] = [];
    try {
      if (result && typeof result === 'object' && result.expired_trials_data) {
        trials = result.expired_trials_data || [];
      } else if (result && Array.isArray(result)) {
        trials = result;
      } else if (result && typeof result === 'string') {
        const parsed = JSON.parse(result);
        if (parsed && parsed.expired_trials_data) {
          trials = parsed.expired_trials_data || [];
        } else if (Array.isArray(parsed)) {
          trials = parsed;
        }
      } else {
        trials = [];
      }
      
      if (!Array.isArray(trials)) {
        trials = [];
      } else {
        trials = trials.filter(trial => trial !== null);
      }
    } catch (parseError: any) {
      await logMessage(`解析过期试用期查询结果时出错: ${parseError.message}`, 'warning');
      trials = [];
    }
    if (dryRun) {
      return {
        expired_trials: trials.length,
        dry_run: true
      };
    }
    // 如果不是仅查询模式，执行更新
    const updates = [];
    for (const trial of trials) {
      const updateQuery = `
        UPDATE public.subscriptions
        SET 
          status = 'expired',
          event_type = 'TRIAL_EXPIRED',
          last_event_at = NOW()
        WHERE id = $1
        RETURNING id, status, event_type
      `;
      // 新增：updateQuery 有效性检查
      if (!updateQuery || typeof updateQuery !== 'string' || !updateQuery.trim()) {
        updates.push({
          subscription_id: trial.id,
          status: 'error',
          error: '未生成有效的SQL更新语句',
          details: trial
        });
        continue;
      }
      try {
        const { data: updateResult, error: updateError } = await supabase.rpc('exec_sql', {
          query: updateQuery,
          params: [trial.id]
        });
        
        if (updateError) {
          throw updateError;
        }
        
        await logMessage(`更新试用期订阅 ${trial.id} 的状态为expired`);
        updates.push({ 
          subscription_id: trial.id, 
          user_id: trial.user_id,
          status: 'success', 
          details: updateResult
        });
      } catch (updateError: any) {
        await logMessage(`更新试用期订阅 ${trial.id} 的状态失败: ${updateError.message}`, 'error');
        updates.push({ 
          subscription_id: trial.id, 
          user_id: trial.user_id,
          status: 'error', 
          error: updateError.message 
        });
      }
    }
    
    return {
      expired_trials: trials.length,
      updates: updates,
      dry_run: false
    };
  } catch (error: any) {
    await logMessage(`清理过期试用期时出错: ${error.message}`, 'error');
    throw error;
  }
}

// 运行数据库统计更新
async function updateDatabaseStats(): Promise<any> {
  try {
    const tables = [
      'public.profiles',
      'public.subscriptions',
      'public.subscription_plans',
      'public.subscription_events',
      'public.temporary_entitlements',
      'public.subscription_extension_history',
      'public.invoices',
      'public.billing_issues',
      'public.payments'
    ];
    
    const results = [];
    
    for (const table of tables) {
      const query = `ANALYZE ${table}`;
      
      try {
        await supabase.rpc('exec_sql', {
          query: query
        });
        
        await logMessage(`更新表 ${table} 的统计信息成功`);
        results.push({
          table: table,
          status: 'success'
        });
      } catch (error: any) {
        await logMessage(`更新表 ${table} 的统计信息失败: ${error.message}`, 'error');
        results.push({
          table: table,
          status: 'error',
          error: error.message
        });
      }
    }
    
    return {
      tables_analyzed: results.length,
      results: results
    };
  } catch (error: any) {
    await logMessage(`更新数据库统计信息时出错: ${error.message}`, 'error');
    throw error;
  }
}

// 验证API请求秘钥
function validateApiKey(request: Request): boolean {
  // 先尝试从自定义头部获取密钥
  const checkApiSecret = request.headers.get('X-Check-Api-Secret');
  if (checkApiSecret && checkApiSecret === apiSecretKey) {
    return true;
  }
  
  // 兼容旧的 Authorization 头部方式
  const authHeader = request.headers.get('Authorization') || request.headers.get('authorization');
  if (!authHeader) {
    return false;
  }
  const key = authHeader.startsWith('Bearer ') ? authHeader.substring(7) : authHeader;
  return key === apiSecretKey;
}

// 处理请求的主函数
serve(async (req: Request) => {
  let logId: string | null = null;
  let action: string = '';
  
  try {
    // 安全验证
    if (!validateApiKey(req)) {
      return new Response(JSON.stringify({ 
        error: "未授权访问" 
      }), { 
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // 只接受GET请求和POST请求
    if (req.method !== 'GET' && req.method !== 'POST') {
      return new Response(JSON.stringify({ 
        error: "仅支持GET和POST请求" 
      }), { 
        status: 405,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // 解析请求参数
    const url = new URL(req.url);
    let params: any = {};
    
    if (req.method === 'GET') {
      // 从URL参数中获取
      for (const [key, value] of url.searchParams.entries()) {
        params[key] = value;
      }
    } else {
      // 从POST请求体中获取
      try {
        const body = await req.json();
        params = body;
      } catch (error: any) {
        return new Response(JSON.stringify({ 
          error: "无效的JSON请求体" 
        }), { 
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
    }
    
    // 获取操作类型
    action = url.pathname.split('/').pop() || params.action;
    
    // 默认为仅查询模式
    const dryRun = params.dry_run !== false && params.dryRun !== false;
    
    // 获取触发来源和执行ID
    const triggeredBy = params.triggered_by || 'manual';
    const executionId = params.execution_id;
    
    // 记录维护任务开始（如果不是试运行模式或明确要求记录日志）
    if (!dryRun || params.log_maintenance === true) {
      logId = await recordMaintenanceLog(action, triggeredBy, executionId);
    }
    
    let result;
    switch (action) {
      case 'check-vip':
        // 检查用户VIP状态
        result = await checkAndFixVIPStatus(params.user_id, dryRun);
        break;
        
      case 'cleanup-expired':
        // 清理过期订阅
        result = await cleanupExpiredSubscriptions(dryRun);
        break;
        
      case 'cleanup-temp-entitlements':
        // 清理过期临时授权
        result = await cleanupExpiredTemporaryEntitlements(dryRun);
        break;
        
      case 'cleanup-trials':
        // 清理过期试用期
        result = await cleanupExpiredTrials(dryRun);
        break;
        
      case 'update-stats':
        // 更新数据库统计
        result = await updateDatabaseStats();
        break;
        
      case 'run-all':
        // 运行所有维护操作
        const vipResult = await checkAndFixVIPStatus(undefined, dryRun);
        const cleanupResult = await cleanupExpiredSubscriptions(dryRun);
        const cleanupTempResult = await cleanupExpiredTemporaryEntitlements(dryRun);
        const cleanupTrialsResult = await cleanupExpiredTrials(dryRun);
        const statsResult = await updateDatabaseStats();
        
        result = {
          vip_status_check: vipResult,
          expired_cleanup: cleanupResult,
          temp_entitlements_cleanup: cleanupTempResult,
          trials_cleanup: cleanupTrialsResult,
          stats_update: statsResult,
          dry_run: dryRun
        };
        break;
        
      default:
        return new Response(JSON.stringify({ 
          error: "无效的操作",
          available_actions: [
            "check-vip", 
            "cleanup-expired", 
            "cleanup-temp-entitlements",
            "cleanup-trials",
            "update-stats",
            "run-all"
          ]
        }), { 
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
    }
    
    // 更新维护日志（成功）
    if (logId) {
      await updateMaintenanceLog(logId, 'success', result);
    }
    
    return new Response(JSON.stringify({
      success: true,
      action: action,
      result: result,
      log_id: logId
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" }
    });
  } catch (error: any) {
    await logMessage(`处理请求时出错: ${error.message}`, 'error');
    
    // 更新维护日志（失败）
    if (logId) {
      await updateMaintenanceLog(logId, 'failed', null, error.message);
    }
    
    return new Response(JSON.stringify({ 
      error: "处理请求时出错",
      message: error instanceof Error ? error.message : String(error),
      log_id: logId
    }), { 
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}); 