# RevenueCat订阅状态检查函数

该Edge Function用于检查和维护订阅状态，确保用户VIP状态与订阅记录保持一致。

## 功能说明

此函数提供以下功能：

1. **检查VIP状态** - 查找并修复用户VIP状态与实际订阅记录不一致的情况，现已支持检查临时授权和试用期
2. **清理过期订阅** - 更新已过期但状态仍为active的订阅记录
3. **清理过期临时授权** - 更新已过期但状态仍为active的临时授权记录
4. **清理过期试用期** - 更新已过期但状态仍为active的试用期订阅记录
5. **更新数据库统计** - 运行ANALYZE命令更新数据库统计信息，提高查询性能

## 部署方法

从项目根目录运行以下命令：

```bash
# 本地测试
supabase functions serve revenuecat-check --no-verify-jwt

# 部署到云端
supabase functions deploy revenuecat-check
```

部署完成后，设置API密钥：

```bash
supabase secrets set CHECK_API_SECRET="你的安全密钥"
```

## API使用说明

### 1. 检查VIP状态

检查并修复所有用户的VIP状态：

```bash
curl -X GET "https://xxxxxxxxxxxx.supabase.co/functions/v1/revenuecat-check/check-vip" \
  -H "Authorization: Bearer 你的安全密钥"
```

检查特定用户：

```bash
curl -X POST "https://xxxxxxxxxxxx.supabase.co/functions/v1/revenuecat-check/check-vip" \
  -H "Authorization: Bearer 你的安全密钥" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "用户UUID", "dry_run": false}'
```

### 2. 清理过期订阅

```bash
curl -X GET "https://xxxxxxxxxxxx.supabase.co/functions/v1/revenuecat-check/cleanup-expired" \
  -H "Authorization: Bearer 你的安全密钥"
```

执行实际更新（非只读模式）：

```bash
curl -X POST "https://xxxxxxxxxxxx.supabase.co/functions/v1/revenuecat-check/cleanup-expired" \
  -H "Authorization: Bearer 你的安全密钥" \
  -H "Content-Type: application/json" \
  -d '{"dry_run": false}'
```

### 3. 清理过期临时授权

```bash
curl -X GET "https://xxxxxxxxxxxx.supabase.co/functions/v1/revenuecat-check/cleanup-temp-entitlements" \
  -H "Authorization: Bearer 你的安全密钥"
```

执行实际更新（非只读模式）：

```bash
curl -X POST "https://xxxxxxxxxxxx.supabase.co/functions/v1/revenuecat-check/cleanup-temp-entitlements" \
  -H "Authorization: Bearer 你的安全密钥" \
  -H "Content-Type: application/json" \
  -d '{"dry_run": false}'
```

### 4. 清理过期试用期

```bash
curl -X GET "https://xxxxxxxxxxxx.supabase.co/functions/v1/revenuecat-check/cleanup-trials" \
  -H "Authorization: Bearer 你的安全密钥"
```

执行实际更新（非只读模式）：

```bash
curl -X POST "https://xxxxxxxxxxxx.supabase.co/functions/v1/revenuecat-check/cleanup-trials" \
  -H "Authorization: Bearer 你的安全密钥" \
  -H "Content-Type: application/json" \
  -d '{"dry_run": false}'
```

### 5. 更新数据库统计

```bash
curl -X GET "https://xxxxxxxxxxxx.supabase.co/functions/v1/revenuecat-check/update-stats" \
  -H "Authorization: Bearer 你的安全密钥"
```

### 6. 运行所有维护操作

一次执行所有维护操作（包括检查VIP状态、清理所有过期记录和更新统计）：

```bash
curl -X GET "https://xxxxxxxxxxxx.supabase.co/functions/v1/revenuecat-check/run-all" \
  -H "Authorization: Bearer 你的安全密钥"
```

实际更新模式（非只读）：

```bash
curl -X POST "https://xxxxxxxxxxxx.supabase.co/functions/v1/revenuecat-check/run-all" \
  -H "Authorization: Bearer 你的安全密钥" \
  -H "Content-Type: application/json" \
  -d '{"dry_run": false}'
```

## 自动化维护

推荐设置定时任务（例如使用cron或GitHub Actions）定期运行此函数，确保订阅状态始终保持同步。

示例Shell脚本：

```bash
#!/bin/bash
# 每日运行的订阅维护脚本

API_URL="https://xxxxxxxxxxxx.supabase.co/functions/v1/revenuecat-check/run-all"
API_KEY="你的安全密钥"

# 运行所有维护操作并记录结果
curl -X POST "$API_URL" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"dry_run": false}' > maintenance_log_$(date +%Y%m%d).json

echo "维护任务已完成，结果已保存到日志文件"
```

## VIP状态检查流程

VIP状态检查会同时考虑三种VIP来源：

1. **标准订阅** - 通过常规付费订阅获得的VIP权限
2. **临时授权** - 通过临时授权系统授予的VIP权限
3. **试用期订阅** - 通过试用期获得的VIP权限

当检查用户的VIP状态时，函数会验证这三种来源的任何一种是否有效，并相应地更新用户的VIP状态和来源信息。

## 返回示例

```json
{
  "success": true,
  "action": "check-vip",
  "result": {
    "total_users_checked": "all",
    "users_to_update": 2,
    "updates": [
      {
        "user_id": "uuid-1",
        "status": "success",
        "new_vip_status": true,
        "vip_source": "temporary_entitlement",
        "details": [...]
      },
      {
        "user_id": "uuid-2",
        "status": "success",
        "new_vip_status": false,
        "details": [...]
      }
    ],
    "dry_run": false
  }
}
```

运行所有维护操作的返回示例：

```json
{
  "success": true,
  "action": "run-all",
  "result": {
    "vip_status_check": {
      "total_users_checked": "all",
      "users_to_update": 5,
      "updates": [...]
    },
    "expired_cleanup": {
      "expired_subscriptions": 3,
      "updates": [...]
    },
    "temp_entitlements_cleanup": {
      "expired_entitlements": 2,
      "updates": [...]
    },
    "trials_cleanup": {
      "expired_trials": 1,
      "updates": [...]
    },
    "stats_update": {
      "tables_analyzed": 9,
      "results": [...]
    },
    "dry_run": false
  }
}
``` 