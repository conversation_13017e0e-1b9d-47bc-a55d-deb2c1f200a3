// RevenueCat Webhook处理函数
// 该函数负责接收和处理RevenueCat的webhook事件通知，并更新用户的订阅状态

// @deno-types="npm:@types/node"
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

// 声明Deno命名空间
declare const Deno: {
  env: {
    get(key: string): string | undefined;
  };
};

// 初始化Supabase客户端，使用环境变量中的凭证
const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? 'http://127.0.0.1:54321';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

// 使用服务角色密钥初始化Supabase客户端以允许全权限访问
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// 从环境变量获取RevenueCat webhook签名密钥
const rcWebhookSigningSecret = Deno.env.get("REVENUECAT_WEBHOOK_SECRET") as string;
console.log("环境变量中的签名密钥(部分):", rcWebhookSigningSecret ? `${rcWebhookSigningSecret.slice(0, 3)}...${rcWebhookSigningSecret.slice(-3)}` : "未设置");

// 在函数顶部添加更多日志记录
console.log("RevenueCat Webhook处理函数已启动，版本：1.0.1");
console.log("环境：", Deno.env.get('SUPABASE_URL') ? "生产环境" : "本地开发环境");

// 检查用户是否存在的函数（不自动创建）
async function checkUserExists(userId: string): Promise<string | null> {
  try {
    if (!userId) {
      return null;
    }

    console.log(`检查用户是否存在: ${userId}`);

    // 检查是否为RevenueCat匿名ID
    if (userId.startsWith('$RCAnonymousID:')) {
      console.log(`检测到RevenueCat匿名ID: ${userId}`);

      // 查找是否已有对应的用户记录
      const { data: existingProfile, error: existingError } = await supabase
        .from('profiles')
        .select('id')
        .eq('anonymous_id', userId)
        .maybeSingle();

      if (!existingError && existingProfile) {
        console.log(`找到匿名ID对应的用户: ${existingProfile.id}`);
        return existingProfile.id;
      }

      console.log(`匿名ID ${userId} 没有对应的用户记录`);
      return null;
    }

    // 使用数据库函数检查用户是否存在
    const { data, error } = await supabase.rpc('check_user_exists', {
      p_user_id: userId
    });

    if (error) {
      console.error("检查用户存在时出错:", error);
      return null;
    }

    if (data) {
      console.log(`用户存在: ${data}`);
      return data;
    } else {
      console.log(`用户不存在: ${userId}`);
      return null;
    }
  } catch (error) {
    console.error(`检查用户存在时出错: ${error}`);
    return null;
  }
}

// 记录孤立订阅事件的函数
async function recordOrphanedEvent(
  appUserId: string,
  eventType: string,
  eventTimestamp: string,
  transactionId?: string,
  originalTransactionId?: string,
  productId?: string,
  eventData?: any
): Promise<string | null> {
  try {
    console.log(`记录孤立订阅事件: 用户 ${appUserId}, 事件类型 ${eventType}`);

    const { data, error } = await supabase.rpc('record_orphaned_subscription_event', {
      p_app_user_id: appUserId,
      p_event_type: eventType,
      p_event_timestamp: eventTimestamp,
      p_transaction_id: transactionId || null,
      p_original_transaction_id: originalTransactionId || null,
      p_product_id: productId || null,
      p_event_data: eventData || {}
    });

    if (error) {
      console.error("记录孤立事件失败:", error);
      return null;
    }

    console.log(`成功记录孤立事件: ${data}`);
    return data;
  } catch (error) {
    console.error(`记录孤立事件异常: ${error}`);
    return null;
  }
}

// 处理用户存在性检查的通用函数
async function handleUserExistenceForEvent(
  appUserId: string,
  eventType: string,
  eventData: any,
  skipUserCreation: boolean = true
): Promise<{ userExists: boolean; userId?: string; shouldContinue: boolean }> {
  try {
    if (!appUserId) {
      console.error("缺少app_user_id");
      return { userExists: false, shouldContinue: false };
    }

    // 检查用户是否存在
    const existingUserId = await checkUserExists(appUserId);

    if (existingUserId) {
      console.log(`用户 ${appUserId} 存在，正常处理事件`);
      return { userExists: true, userId: existingUserId, shouldContinue: true };
    } else if (skipUserCreation) {
      console.log(`用户 ${appUserId} 不存在，记录孤立事件`);

      // 记录孤立事件
      const eventTimestamp = eventData.event_timestamp_ms ?
        new Date(eventData.event_timestamp_ms).toISOString() :
        new Date().toISOString();

      await recordOrphanedEvent(
        appUserId,
        eventType,
        eventTimestamp,
        eventData.transaction_id,
        eventData.original_transaction_id,
        eventData.product_id,
        eventData
      );

      return { userExists: false, shouldContinue: false };
    } else {
      // 如果不跳过用户创建，则使用原有的ensureUserExists逻辑
      console.log(`用户 ${appUserId} 不存在，尝试创建用户`);
      const userEmail = eventData.subscriber_attributes?.["$email"]?.value || eventData.email || null;
      const userId = await ensureUserExists(appUserId, userEmail);
      return { userExists: true, userId: userId, shouldContinue: true };
    }
  } catch (error) {
    console.error(`处理用户存在性检查失败: ${error}`);

    if (skipUserCreation) {
      // 如果检查失败且跳过用户创建，记录孤立事件作为备用方案
      try {
        const eventTimestamp = eventData.event_timestamp_ms ?
          new Date(eventData.event_timestamp_ms).toISOString() :
          new Date().toISOString();

        await recordOrphanedEvent(
          appUserId,
          eventType,
          eventTimestamp,
          eventData.transaction_id,
          eventData.original_transaction_id,
          eventData.product_id,
          eventData
        );

        return { userExists: false, shouldContinue: false };
      } catch (orphanError) {
        console.error(`记录孤立事件也失败: ${orphanError}`);
        return { userExists: false, shouldContinue: false };
      }
    } else {
      return { userExists: false, shouldContinue: false };
    }
  }
}

// 确保用户存在的函数
async function ensureUserExists(userId: string, email?: string): Promise<string> {
  try {
    if (!userId) {
      throw new Error("用户ID不能为空");
    }
    
    console.log(`确保用户存在: ${userId}, 邮箱: ${email || 'N/A'}`);

    // 检查是否为RevenueCat匿名ID
    if (userId.startsWith('$RCAnonymousID:')) {
      console.log(`检测到RevenueCat匿名ID: ${userId}`);
      
      // 为匿名ID生成一个稳定的UUID (v5 UUID基于namespace和原始ID)
      const namespace = '54f535fa-7bce-46c3-88c5-4f7938caa383'; // 固定namespace
      // 使用crypto API来生成UUID v5 
      const encoder = new TextEncoder();
      const nameBuffer = encoder.encode(userId);
      const namespaceBuffer = new Uint8Array([...namespace.replace(/-/g, '')].map(c => parseInt(c, 16)));
      
      // 计算UUID v5 (这是一个简化版本)
      const hash = await crypto.subtle.digest('SHA-1', new Uint8Array([...namespaceBuffer, ...nameBuffer]));
      const hashArray = Array.from(new Uint8Array(hash));
      
      // 设置UUID v5的版本和变体位
      hashArray[6] = (hashArray[6] & 0x0f) | 0x50; // 版本5
      hashArray[8] = (hashArray[8] & 0x3f) | 0x80; // 变体1
      
      // 格式化为UUID字符串
      const stableId = [...hashArray.slice(0, 16)].map(b => b.toString(16).padStart(2, '0')).join('');
      const formattedId = `${stableId.slice(0, 8)}-${stableId.slice(8, 12)}-${stableId.slice(12, 16)}-${stableId.slice(16, 20)}-${stableId.slice(20)}`;
      
      console.log(`为匿名ID生成稳定UUID: ${formattedId}`);
      
      try {
        // 尝试在users表中创建匿名用户记录
        const { error: userInsertError } = await supabase.auth.admin.createUser({
          id: formattedId,
          email: `anonymous-${formattedId.slice(0, 8)}@revenuecat.local`,
          password: crypto.randomUUID(),
          user_metadata: {
            original_id: userId,
            source: 'revenuecat_anonymous',
            created_at: new Date().toISOString()
          }
        });
        
        if (userInsertError) {
          console.error("创建匿名用户记录失败:", userInsertError);
          console.log("尝试软失败并继续...");
        }
        
        // 创建用户资料
        const { error: profileError } = await supabase
          .from('profiles')
          .upsert({
            id: formattedId,
            anonymous_id: userId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        
        if (profileError) {
          console.error("创建匿名用户资料失败:", profileError);
          
          // 检查用户是否已经存在
          const { data: existingProfile } = await supabase
            .from('profiles')
            .select('id')
            .eq('anonymous_id', userId)
            .maybeSingle();
            
          if (existingProfile) {
            console.log(`找到匿名用户对应的资料: ${existingProfile.id}`);
            return existingProfile.id;
          }
          
          // 如果处理失败，记录错误但返回原ID
          console.log("无法处理匿名ID，将使用原始ID继续");
          return userId;
        }
        
        // 成功创建匿名用户
        console.log(`成功创建匿名用户: ${formattedId}`);
        return formattedId;
      } catch (error) {
        console.error("处理匿名ID异常:", error);
        return userId; // 返回原始ID
      }
    }
    
    // 非匿名ID的处理流程与原来一致
    // 调用Supabase函数确保用户存在
    const { data, error } = await supabase.rpc('ensure_user_exists', {
      p_user_id: userId,
      p_email: email
    });
    
    if (error) {
      console.error("确保用户存在时出错:", error);
      // 添加更多错误信息
      console.error("错误代码:", error.code);
      console.error("错误详情:", error.details);
      console.error("错误提示:", error.hint);
      throw error;
    }
    
    console.log("用户确认存在:", data);
    return data;
  } catch (error) {
    console.error(`确保用户存在时出错: ${error}`);
    // 添加更细粒度的错误处理
    if (error instanceof Error) {
      console.error("错误类型:", error.name);
      console.error("错误消息:", error.message);
      console.error("错误堆栈:", error.stack);
    }
    throw error;
  }
}

// 验证RevenueCat授权头
function verifyAuthHeader(authHeader: string | null): boolean {
  try {
    if (!authHeader) {
      console.error("未提供授权头");
      return false;
    }
    
    if (!rcWebhookSigningSecret) {
      console.error("环境变量REVENUECAT_WEBHOOK_SECRET未设置");
      return false;
    }
    
    // 输出部分信息以便调试，但不输出完整的敏感信息
    console.log("收到的授权头(部分):", `${authHeader.slice(0, 3)}...${authHeader.slice(-3)}`);
    
    // 支持多种格式的验证方式
    // 1. 直接比较原始值
    if (authHeader === rcWebhookSigningSecret) {
      console.log("授权头直接匹配成功");
      return true;
    }
    
    // 2. 处理Bearer前缀情况
    const authValue = authHeader.startsWith("Bearer ") 
      ? authHeader.substring(7) 
      : authHeader;
    
    const envValue = rcWebhookSigningSecret.startsWith("Bearer ") 
      ? rcWebhookSigningSecret.substring(7) 
      : rcWebhookSigningSecret;
    
    if (authValue === envValue) {
      console.log("授权头在处理Bearer前缀后匹配成功");
      return true;
    }
    
    console.error("授权验证失败: 值不匹配");
    return false;
  } catch (error) {
    console.error("验证授权头出错:", error);
    return false;
  }
}

// 检查是否是测试请求的函数
function isTestRequest(payload: string, url: string, eventData: any): boolean {
  // 添加更多识别测试请求的逻辑
  const isTest = (
    payload.includes("test") ||
    url.includes("test") ||
    (eventData.test === true) || 
    (eventData.event?.type === "TEST") ||
    payload.toLowerCase().includes("\"type\":\"test\"") ||
    payload.toLowerCase().includes("test event") ||
    // 增加对RevenueCat测试事件标识的识别
    (eventData.environment === "SANDBOX") ||
    url.includes("webhook-test")
  );
  
  if (isTest) {
    console.log("检测到测试请求:", {
      来源URL: url,
      事件类型: eventData.type || eventData.event?.type || "未知",
      环境: eventData.environment || "未知"
    });
  }
  
  return isTest;
}

// 记录请求信息到数据库，用于调试和监控
async function logWebhookRequest(payload: string, eventType: string, app_user_id: string | null, isProcessed: boolean, error?: any) {
  try {
    // 记录请求到日志
    console.log("\n----- 记录Webhook请求信息 -----");
    console.log(`记录webhook请求: 类型=${eventType}, 用户ID=${app_user_id || "未找到"}, 处理=${isProcessed}`);
    
    // 如果有用户ID和事件类型，尝试将事件记录到subscription_events表
    if (app_user_id && eventType) {
      try {
        const eventData = JSON.parse(payload);
        const data = eventData.event || eventData;
        
        // 查找对应的订阅ID
        let subscriptionId = null;
        if (data.transaction_id || data.original_transaction_id) {
          const { data: sub, error: subError } = await supabase
            .from('subscriptions')
            .select('id')
            .or(`transaction_id.eq.${data.transaction_id},original_transaction_id.eq.${data.original_transaction_id}`)
            .maybeSingle();
            
          if (!subError && sub) {
            subscriptionId = sub.id;
          }
        }
        
        // 如果找不到特定订阅ID，尝试获取该用户最近的订阅
        if (!subscriptionId) {
          const { data: recentSub, error: recentSubError } = await supabase
            .from('subscriptions')
            .select('id')
            .eq('user_id', app_user_id)
            .order('created_at', { ascending: false })
            .limit(1)
            .maybeSingle();
            
          if (!recentSubError && recentSub) {
            subscriptionId = recentSub.id;
          }
        }
        
        // 记录事件到subscription_events表
        if (subscriptionId) {
          const { error: eventError } = await supabase
            .from('subscription_events')
            .insert({
              user_id: app_user_id,
              subscription_id: subscriptionId,
              event_type: eventType,
              event_timestamp: new Date().toISOString(),
              transaction_id: data.transaction_id,
              original_transaction_id: data.original_transaction_id,
              product_id: data.product_id,
              event_data: data
            });
            
          if (eventError) {
            console.error("记录订阅事件失败:", eventError);
          } else {
            console.log(`成功记录订阅事件 ${eventType} 到subscription_events表`);
          }
        } else {
          console.log(`未找到对应的订阅ID，无法记录到subscription_events表`);
        }
      } catch (parseError) {
        console.error("解析或记录订阅事件失败:", parseError);
      }
    }
    console.log("----- 记录Webhook请求完成 -----\n");
  } catch (logError) {
    console.error("记录webhook请求失败:", logError);
  }
}

// 新增函数：标准化取消原因
function normalizeReasonDescription(reason: string | null): string {
  if (!reason) return '未指定原因';
  
  // 转换为小写以便匹配
  const lowerReason = reason.toLowerCase();
  
  // 常见取消原因的映射
  const reasonMap: Record<string, string> = {
    // Apple常见原因
    'customer_cancelled': '用户主动取消',
    'billing_error': '账单错误',
    'price_increase': '价格上涨',
    'rejected_price_increase': '用户拒绝价格上涨',
    'declined': '支付被拒',
    'failed_billing': '账单支付失败',
    'subscription_restored': '订阅已恢复',
    'app_deleted': '应用被删除',
    'refund': '用户申请退款',
    'product_unavailable': '产品不可用',
    'customer_did_not_agree_to_price_increase': '用户不同意价格上涨',
    'upgrade': '用户升级订阅',
    'downgrade': '用户降级订阅',
    'app_developer_initiated': '开发者取消',
    
    // Google常见原因
    'user_cancelled': '用户主动取消',
    'payment_issue': '支付问题',
    'replacement': '订阅被替换',
    'system_cancelled': '系统取消',
    'preference_updated': '用户偏好更新',
    'subscription_not_used': '订阅未使用',
    'subscription_too_expensive': '订阅价格过高',
    'technical_issues': '技术问题',
    'subscription_plan_changed': '订阅计划变更',
    'better_app_found': '找到更好的应用',
    'payment_declined': '支付被拒绝',
    'account_issues': '账户问题',
    
    // 通用描述
    'voluntary': '用户自愿取消',
    'involuntary': '非自愿取消（如支付问题）',
    'unknown': '未知原因',
    'expiration': '订阅到期',
    'other': '其他原因'
  };
  
  // 尝试精确匹配
  if (reasonMap[reason]) {
    return reasonMap[reason];
  }
  
  // 尝试模糊匹配
  for (const [key, value] of Object.entries(reasonMap)) {
    if (lowerReason.includes(key)) {
      return value;
    }
  }
  
  // 如果没有匹配，返回原始原因
  return reason;
}

// 新增记录详细错误日志的函数
async function logError(eventType: string, userId: string | null, error: any) {
  try {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : null;
    
    const logData = {
      event_type: eventType,
      user_id: userId,
      error_message: errorMessage,
      error_stack: errorStack,
      timestamp: new Date().toISOString()
    };
    
    console.log("记录错误日志:", logData);
    
    // 可选：将错误写入数据库表
    // await supabase.from('webhook_errors').insert(logData);
  } catch (logError) {
    console.error("记录错误日志失败:", logError);
  }
}

// 在顶部函数区域添加新函数
async function findSubscriptionByIdentifiers(user_id: string, identifiers: { [key: string]: string }, product_id?: string): Promise<string | null> {
  try {
    let subscriptionId = null;
    
    // 尝试使用提供的标识符查找订阅
    for (const [field, value] of Object.entries(identifiers)) {
      if (!value) continue;
      
      const { data: sub, error: err } = await supabase
        .from('subscriptions')
        .select('id')
        .eq(field, value)
        .maybeSingle();
        
      if (!err && sub) {
        subscriptionId = sub.id;
        console.log(`找到对应的订阅记录(${field}): ${subscriptionId}`);
        break;
      }
    }
    
    // 如果没找到特定订阅，尝试基于产品ID和用户ID搜索
    if (!subscriptionId && product_id) {
      const { data: prodSubscription, error: prodSubError } = await supabase
        .from('subscriptions')
        .select('id')
        .eq('user_id', user_id)
        .eq('product_id', product_id)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();
        
      if (!prodSubError && prodSubscription) {
        subscriptionId = prodSubscription.id;
        console.log(`基于产品ID和用户ID找到订阅: ${subscriptionId}`);
      }
    }
    
    // 如果还是没找到，尝试获取该用户最新的活跃订阅
    if (!subscriptionId) {
      const { data: latestSubscription, error: latestError } = await supabase
        .from('subscriptions')
        .select('id')
        .eq('user_id', user_id)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();
      
      if (!latestError && latestSubscription) {
        subscriptionId = latestSubscription.id;
        console.log(`找到用户最新的活跃订阅: ${subscriptionId}`);
      }
    }
    
    return subscriptionId;
  } catch (error) {
    console.error("搜索订阅记录时出错:", error);
    return null;
  }
}

// 主处理函数
serve(async (req: Request) => {
  console.log("收到RevenueCat webhook请求 URL:", req.url);
  console.log("\n==================== 开始处理新事件 ====================\n");
  
  try {
    // 验证请求方法
    if (req.method !== "POST") {
      return new Response(JSON.stringify({ error: "只接受POST请求" }), { 
        status: 405,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // 读取请求体
    let payload: string;
    try {
      payload = await req.text();
      console.log("请求体:", payload.length > 500 ? 
        payload.slice(0, 200) + "..." + payload.slice(-200) : payload);
    } catch (readError) {
      console.error("读取请求体失败:", readError);
      return new Response(JSON.stringify({ error: "读取请求体失败" }), { 
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // 解析JSON数据
    let eventData;
    try {
      eventData = JSON.parse(payload);
      console.log("成功解析JSON数据");
    } catch (parseError) {
      console.error("解析请求体JSON出错:", parseError);
      return new Response(JSON.stringify({ 
        error: "无效的JSON数据",
        received: payload.slice(0, 100)
      }), { 
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // 从请求头获取授权信息 - 检查多种可能的头
    const authHeader = req.headers.get("Authorization") || 
                      req.headers.get("authorization");
    
    console.log("Authorization头存在:", !!authHeader);
    
    // 检查是否是测试请求
    const testRequest = isTestRequest(payload, req.url, eventData);
    if (testRequest) {
      console.log("检测到测试请求，将跳过授权验证");
    }
    
    // 如果不是测试请求且授权验证失败，则返回错误
    if (!testRequest && !verifyAuthHeader(authHeader)) {
      console.error("授权验证失败或缺少授权头");
      return new Response(JSON.stringify({ 
        error: "授权验证失败", 
        auth_header_present: !!authHeader,
        expected_format: rcWebhookSigningSecret ? "与环境变量匹配的密钥" : "未设置环境变量"
      }), { 
        status: 401,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // 如果是测试请求，直接返回成功
    if (testRequest) {
      console.log("接收到RevenueCat测试请求，已允许通过");
      return new Response(JSON.stringify({ 
        received: true, 
        message: "测试请求已成功接收，签名验证已跳过",
        auth_status: authHeader ? "提供了授权" : "未提供授权"
      }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // 解析事件数据
    let eventType = "未知类型";
    let eventObject = null;
    let app_user_id = null;
    
    // 根据RevenueCat文档解析事件数据结构
    console.log("开始解析事件数据结构");
    
    // API版本检查
    if (eventData.api_version) {
      console.log("API版本:", eventData.api_version);
    }
    
    // 提取event对象 (根据RevenueCat文档，webhook数据以event对象包装)
    if (eventData.event && typeof eventData.event === 'object') {
      eventObject = eventData.event;
      eventType = eventObject.type || "未知类型";
      // 使用app_user_id作为统一变量，不再使用appUserId
      app_user_id = eventObject.app_user_id;
      
      console.log("解析到标准事件对象，类型:", eventType);
      
      // 记录关键事件信息
      console.log("事件详情:", {
        类型: eventType,
        用户ID: app_user_id || "未找到",
        产品ID: eventObject.product_id || "未找到",
        到期时间: eventObject.expiration_at_ms ? new Date(eventObject.expiration_at_ms).toISOString() : "未找到"
      });
      
      // 在处理事件前检查用户是否存在
      if (app_user_id) {
        try {
          // 检查用户是否存在（不自动创建）
          const existingUserId = await checkUserExists(app_user_id);

          if (existingUserId) {
            console.log(`用户 ${app_user_id} 存在，正常处理事件`);
            // 用户存在，可以正常处理订阅事件
          } else {
            console.log(`用户 ${app_user_id} 不存在，记录孤立事件`);

            // 用户不存在，只记录事件，不创建用户
            const eventTimestamp = eventObject.event_timestamp_ms ?
              new Date(eventObject.event_timestamp_ms).toISOString() :
              new Date().toISOString();

            await recordOrphanedEvent(
              app_user_id,
              eventType,
              eventTimestamp,
              eventObject.transaction_id,
              eventObject.original_transaction_id,
              eventObject.product_id,
              eventObject
            );

            // 记录完孤立事件后直接返回成功响应
            return new Response(JSON.stringify({
              received: true,
              event_type: eventType,
              processed: false,
              reason: "用户不存在，已记录孤立事件"
            }), {
              status: 200,
              headers: { "Content-Type": "application/json" }
            });
          }
        } catch (userError) {
          console.error(`检查用户存在失败: ${userError}`);
          // 如果检查失败，记录孤立事件作为备用方案
          try {
            const eventTimestamp = eventObject.event_timestamp_ms ?
              new Date(eventObject.event_timestamp_ms).toISOString() :
              new Date().toISOString();

            await recordOrphanedEvent(
              app_user_id,
              eventType,
              eventTimestamp,
              eventObject.transaction_id,
              eventObject.original_transaction_id,
              eventObject.product_id,
              eventObject
            );

            return new Response(JSON.stringify({
              received: true,
              event_type: eventType,
              processed: false,
              reason: "用户检查失败，已记录孤立事件"
            }), {
              status: 200,
              headers: { "Content-Type": "application/json" }
            });
          } catch (orphanError) {
            console.error(`记录孤立事件也失败: ${orphanError}`);
            // 继续处理，避免阻止整个webhook
          }
        }
      }
    } else {
      console.log("未找到标准event对象，尝试直接使用数据");
      eventObject = eventData;
      if (eventData.type) {
        eventType = eventData.type;
      }
      app_user_id = eventData.app_user_id;
      
      console.log("事件数据结构:", JSON.stringify(Object.keys(eventData)));
    }
    
    if (!eventObject) {
      console.error("无法解析事件数据");
      return new Response(JSON.stringify({ 
        error: "无法解析事件数据",
        received: true
      }), { 
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }
    
    console.log("开始处理事件，类型:", eventType);
    
    // 处理不同类型的事件
    let processed = true;
    try {
      switch (eventType) {
        case "INITIAL_PURCHASE":
        case "RENEWAL":
          console.log(`开始处理${eventType}事件`);
          await handleSubscriptionActive(eventObject);
          break;
        case "PRODUCT_CHANGE":
          console.log(`开始处理PRODUCT_CHANGE事件`);
          await handleProductChange(eventObject);
          break;
        case "CANCELLATION":
          console.log(`开始处理${eventType}事件`);
          // 检查是否为试用期取消
          if (eventObject.period_type === 'TRIAL') {
            console.log("检测到试用期取消事件");
            // 保持原有eventType，但在handleSubscriptionInactive函数中进行特殊处理
          }
          await handleSubscriptionInactive(eventObject);
          break;
        case "TRIAL_CANCELLATION":
          console.log(`开始处理TRIAL_CANCELLATION事件`);
          await handleSubscriptionInactive({
            ...eventObject,
            period_type: 'TRIAL',
            cancellation_reason: 'trial_cancelled'
          });
          break;
        case "BILLING_ISSUE":
          console.log(`开始处理BILLING_ISSUE事件`);
          await handleBillingIssue(eventObject);
          break;
        case "SUBSCRIPTION_PAUSED":
          console.log(`开始处理SUBSCRIPTION_PAUSED事件`);
          await handleSubscriptionPaused(eventObject);
          break;
        case "EXPIRATION":
          console.log(`开始处理EXPIRATION事件`);
          await handleSubscriptionInactive(eventObject);
          break;
        case "TRANSFER":
          console.log(`开始处理TRANSFER事件`);
          await handleSubscriptionTransfer(eventObject);
          break;
        case "REFUND":
          console.log(`开始处理REFUND事件`);
          await handleRefund(eventObject);
          break;
        case "REFUND_REVERSED":
          console.log(`开始处理REFUND_REVERSED事件`);
          await handleRefundReversed(eventObject);
          break;
        case "SUBSCRIPTION_EXTENDED":
          console.log(`开始处理SUBSCRIPTION_EXTENDED事件`);
          await handleSubscriptionExtended(eventObject);
          break;
        case "TRIAL_STARTED":
          console.log(`开始处理TRIAL_STARTED事件`);
          await handleTrialStarted(eventObject);
          break;
        case "TRIAL_CONVERSION":
          console.log(`开始处理TRIAL_CONVERSION事件`);
          await handleTrialConversion(eventObject);
          break;
        case "INVOICE_ISSUANCE":
          console.log(`开始处理INVOICE_ISSUANCE事件`);
          await handleInvoiceIssuance(eventObject);
          break;
        case "TEMPORARY_ENTITLEMENT_GRANT":
          console.log(`开始处理TEMPORARY_ENTITLEMENT_GRANT事件`);
          await handleTemporaryEntitlementGrant(eventObject);
          break;
        default:
          console.log(`未处理的事件类型: ${eventType}`);
          break;
      }
    } catch (processingError) {
      console.error(`处理${eventType}事件失败:`, processingError);
      
      // 记录错误到日志，但不向客户端返回错误，确保RevenueCat认为请求成功
      await logWebhookRequest(payload, eventType, app_user_id, false, processingError);
      
      // 虽然处理失败，但仍返回200状态码
      return new Response(JSON.stringify({ 
        received: true,
        event_type: eventType,
        processed: false,
        error: processingError instanceof Error ? processingError.message : String(processingError)
      }), {
        status: 200, // 返回200确保RevenueCat不会重试
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // 记录请求处理结果
    await logWebhookRequest(payload, eventType, app_user_id, processed);
    
    // 返回成功响应
    return new Response(JSON.stringify({ 
      received: true,
      event_type: eventType,
      processed: processed
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" }
    });
    
  } catch (error) {
    console.error("处理webhook时出错:", error);
    return new Response(JSON.stringify({ 
      error: "处理webhook时出错", 
      message: error instanceof Error ? error.message : String(error)
    }), { 
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  } finally {
    // 在每个webhook事件处理完成后打印分割线
    console.log("\n==================== 事件处理结束 ====================\n");
  }
});

// 处理订阅激活事件（首次购买、续订、产品更改）
async function handleSubscriptionActive(eventData: any) {
  try {
    console.log("\n----- 开始处理订阅激活事件 -----\n");
    console.log("处理订阅活跃事件 - 开始:", JSON.stringify({
      用户ID: eventData.app_user_id,
      产品ID: eventData.product_id,
      事件类型: eventData.type,
      时间戳: new Date().toISOString()
    }));
    
    console.log("处理订阅活跃事件:", {
      用户ID: eventData.app_user_id,
      产品ID: eventData.product_id,
      到期时间戳: eventData.expiration_at_ms,
      环境: eventData.environment
    });
    
    // 提取用户ID，使用let而不是const
    let appUserId = eventData.app_user_id;
    if (!appUserId) {
      throw new Error("缺少app_user_id");
    }

    // 检查用户是否存在，如果不存在则记录孤立事件
    const userCheck = await handleUserExistenceForEvent(
      appUserId,
      eventData.type || 'SUBSCRIPTION_ACTIVE',
      eventData,
      true // 跳过用户创建
    );

    if (!userCheck.shouldContinue) {
      console.log(`用户 ${appUserId} 不存在，已记录孤立事件，停止处理`);
      return;
    }

    // 使用检查返回的用户ID
    appUserId = userCheck.userId!;
    console.log(`已确认用户 ${appUserId} 存在`);
    
    // 获取产品ID
    const productId = eventData.product_id;
    if (!productId) {
      throw new Error("缺少product_id");
    }
    
    // 根据RevenueCat文档获取关键数据
    const original_transaction_id = eventData.original_transaction_id;
    const transaction_id = eventData.transaction_id;
    const store = eventData.store;
    const environment = eventData.environment || "PRODUCTION";
    const period_type = eventData.period_type;
    const is_family_share = eventData.is_family_share || false;
    const country_code = eventData.country_code;
    const presented_offering_id = eventData.presented_offering_id;
    const is_trial_conversion = eventData.is_trial_conversion || false;
    
    // 处理entitlements数据
    let entitlements = [];
    if (eventData.entitlement_id) {
      entitlements.push(eventData.entitlement_id);
    }
    if (eventData.entitlement_ids && Array.isArray(eventData.entitlement_ids)) {
      entitlements = [...new Set([...entitlements, ...eventData.entitlement_ids])];
    }
    
    // 获取时间戳并转换为Date对象
    const purchased_at = eventData.purchased_at_ms ? new Date(eventData.purchased_at_ms) : new Date();
    const expires_at = eventData.expiration_at_ms ? new Date(eventData.expiration_at_ms) : null;
    
    // 检查是否为试用期
    const is_trial = eventData.period_type === 'TRIAL';
    console.log(`订阅类型: ${is_trial ? '试用期' : '正式订阅'}`);
    
    console.log("提取的关键数据:", {
      app_user_id: appUserId,
      product_id: productId,
      transaction_id,
      original_transaction_id,
      store,
      environment,
      period_type,
      is_family_share,
      country_code,
      presented_offering_id,
      entitlements,
      purchased_at: purchased_at.toISOString(),
      expires_at: expires_at ? expires_at.toISOString() : "未设置",
      is_trial
    });
    
    // 查找对应的订阅计划
    let planId = productId;
    
    // RevenueCat产品ID与Supabase订阅计划ID的映射关系
    const productToPlanMap: Record<string, string> = {
      'monthly_subscription': 'monthly_subscription',
      'yearly_subscription': 'yearly_subscription',
      'com.subscription.weekly': 'weekly_subscription',
      'com.subscription.monthly': 'monthly_subscription',
      'com.subscription.yearly': 'yearly_subscription'
      // 可以添加更多映射
    };
    
    // 如果在映射表中有对应关系，使用映射后的planId
    if (productToPlanMap[productId]) {
      planId = productToPlanMap[productId];
      console.log(`使用映射的订阅计划ID: ${planId}`);
    }
    
    try {
      // 从订阅计划表中查找对应的计划ID
      let planQuery = supabase.from('subscription_plans').select('id');
      
      // 根据商店类型选择正确的产品ID列
      if (store && store.toLowerCase().includes('app_store')) {
        planQuery = planQuery.eq('apple_product_id', productId);
        console.log(`使用Apple商店产品ID ${productId} 查询订阅计划`);
      } else if (store && store.toLowerCase().includes('play_store')) {
        planQuery = planQuery.eq('google_product_id', productId);
        console.log(`使用Google商店产品ID ${productId} 查询订阅计划`);
      } else {
        // 尝试两种ID都查询
        planQuery = planQuery.or(`apple_product_id.eq.${productId},google_product_id.eq.${productId}`);
        console.log(`使用通用商店产品ID ${productId} 查询订阅计划`);
      }
      
      const { data: planData, error: planError } = await planQuery;
      
      if (planError) {
        console.error("查找订阅计划失败:", planError);
        console.log(`未找到对应的订阅计划，尝试直接使用产品ID: ${productId}`);
        // 如果查询失败，尝试直接使用原始产品ID
        planId = productId;
      } else if (planData && planData.length > 0) {
        planId = planData[0].id;
        console.log(`找到对应的订阅计划: ${planId}`);
      } else {
        console.log(`未找到产品ID为 ${productId} 的订阅计划，回退到原始产品ID`);
        // 使用原始产品ID
        planId = productId;
      }
    } catch (planError) {
      console.error("查询订阅计划时出错:", planError);
      // 继续使用原始产品ID
    }
    
    // 查询用户是否存在
    const { data: userExists, error: userError } = await supabase
      .from('profiles')
      .select('id, updated_at, is_vip, vip_updated_at')
      .eq('id', appUserId)
      .single();
      
    if (userError || !userExists) {
      console.error(`用户ID ${appUserId} 在profiles表中不存在! 错误:`, userError);
      console.log(`尝试查看是否有任何相似ID存在`);
      
      // 如果存在别名，尝试查找
      if (eventData.aliases && Array.isArray(eventData.aliases)) {
        console.log(`收到别名列表:`, eventData.aliases);
        
        for (const alias of eventData.aliases) {
          try {
            const { data: aliasUser } = await supabase
              .from('profiles')
              .select('id, is_vip, vip_updated_at')
              .eq('id', alias)
              .single();
              
            if (aliasUser) {
              console.log(`通过别名 ${alias} 找到了用户!`);
              // 使用找到的用户ID
              appUserId = alias;
              break;
            }
          } catch (aliasError) {
            console.error(`查询别名 ${alias} 失败:`, aliasError);
          }
        }
      }
      
      // 如果依然找不到用户，记录错误并结束处理
      if (!appUserId || (userError && !eventData.aliases)) {
        console.error(`无法找到有效的用户ID，无法更新VIP状态`);
        return;
      }
    }
    
    // 更新用户资料，设置为VIP
    const now = new Date().toISOString();
    
    console.log(`开始更新用户 ${appUserId} 的VIP状态为true`);
    
    // 查询订阅计划，获取api_key_type和voice_minutes等配置
    let v_api_key_type = 'premium'; // 默认值
    let v_voice_minutes = 0;
    
    try {
      const { data: planFeatures, error: planFeaturesError } = await supabase
        .from('subscription_plans')
        .select('features')
        .eq('plan_id', planId)
        .single();
        
      if (!planFeaturesError && planFeatures && planFeatures.features) {
        v_api_key_type = planFeatures.features.api_key_type || 'premium';
        v_voice_minutes = parseInt(planFeatures.features.voice_minutes || '0', 10);
        console.log(`从订阅计划获取配置: api_key_type=${v_api_key_type}, voice_minutes=${v_voice_minutes}`);
      } else {
        console.log(`未找到订阅计划或特性，使用默认配置: api_key_type=${v_api_key_type}`);
      }
    } catch (featuresError) {
      console.error("获取订阅计划特性失败:", featuresError);
      console.log("使用默认特性配置");
    }
    
    const profileUpdateData: any = {
        is_vip: true,
        vip_product_id: productId,
        vip_expires_at: expires_at ? expires_at.toISOString() : null,
        vip_environment: environment,
        vip_updated_at: now,
        updated_at: now,
        api_key_type: v_api_key_type, // 确保更新api_key_type
        voice_minutes_limit: v_voice_minutes // 同时更新语音额度
    };
    
    // 如果存在vip_since列，并且用户之前不是VIP或者vip_since为空，则设置vip_since
    try {
      const { data: vipStatus } = await supabase
        .from('profiles')
        .select('is_vip, vip_since')
        .eq('id', appUserId)
        .single();
        
      if (!vipStatus?.is_vip || !vipStatus?.vip_since) {
        profileUpdateData.vip_since = now;
      }
    } catch (vipError) {
      console.log("检查VIP起始时间时出错，将设置为当前时间:", vipError);
      profileUpdateData.vip_since = now;
    }
    
    // 如果是试用期，设置试用标记
    if (is_trial) {
      profileUpdateData.vip_is_trial = true;
    }
    
    const profileUpdateResult = await supabase
      .from('profiles')
      .update(profileUpdateData)
      .eq('id', appUserId);
    
    if (profileUpdateResult.error) {
      console.error("更新用户VIP状态失败:", profileUpdateResult.error);
    } else {
      console.log(`成功更新用户 ${appUserId} 的VIP状态为true`);
    }
    
    // 使用transaction_id查询现有订阅
    const subscription_identifier = original_transaction_id || transaction_id;
    if (!subscription_identifier) {
      console.error("缺少交易ID，无法更新订阅记录");
      return;
    }
    
    console.log(`查询是否存在订阅记录，交易ID: ${subscription_identifier}`);
    // 更安全的查询方式，避免SQL注入和语法错误
    let existingSubscription = null;
    let existingSubError = null;
    
    try {
      // 先检查payment_provider_subscription_id
      const { data: sub1, error: err1 } = await supabase
        .from('subscriptions')
        .select('id')
        .eq('payment_provider_subscription_id', subscription_identifier)
        .maybeSingle();
        
      if (!err1 && sub1) {
        existingSubscription = sub1;
      } else {
        // 检查transaction_id
        const { data: sub2, error: err2 } = await supabase
          .from('subscriptions')
          .select('id')
          .eq('transaction_id', subscription_identifier)
          .maybeSingle();
          
        if (!err2 && sub2) {
          existingSubscription = sub2;
        } else {
          // 检查original_transaction_id
          const { data: sub3, error: err3 } = await supabase
            .from('subscriptions')
            .select('id')
            .eq('original_transaction_id', subscription_identifier)
            .maybeSingle();
            
          if (!err3 && sub3) {
            existingSubscription = sub3;
          } else {
            existingSubError = err3 || err2 || err1;
          }
        }
      }
    } catch (queryError) {
      console.error("查询订阅记录出错:", queryError);
      existingSubError = { message: "查询出错", details: queryError };
    }
    
    if (existingSubError && existingSubError.code !== 'PGRST116') {  // PGRST116是"没有找到行"的错误
      console.error("查询现有订阅失败:", existingSubError);
    }
    
    if (existingSubscription) {
      // 更新现有订阅记录
      console.log(`更新用户 ${appUserId} 的现有订阅记录 ${existingSubscription.id}`);
      
      const updateData: any = {
        status: 'active',
        product_id: productId,
        period_type: period_type,
        entitlements: JSON.stringify(entitlements),
        is_family_share: is_family_share,
        country_code: country_code,
        presented_offering_id: presented_offering_id,
        is_trial_conversion: is_trial_conversion,
        environment: environment,
        purchase_date: purchased_at.toISOString(),
        expires_at: expires_at ? expires_at.toISOString() : null,
        auto_renew_status: true,
        webhook_received_at: now,
        event_type: eventData.type || 'RENEWAL',
        last_event_at: now
        // 不设置updated_at字段，由数据库触发器自动更新
      };
      
      try {
        const { error: updateSubError } = await supabase
          .from('subscriptions')
          .update(updateData)
          .eq('id', existingSubscription.id);
        
        if (updateSubError) {
          console.error("更新订阅记录失败:", updateSubError);
          console.log("继续执行其他步骤，稍后将尝试应用数据库迁移以修复结构问题");
        } else {
          console.log(`成功更新用户 ${appUserId} 的订阅记录`);
        }
        
        // 无论更新是否成功，都继续处理支付记录
        // 因为VIP状态更新对用户更重要
        await createPaymentRecord(appUserId, existingSubscription.id, eventData);
      } catch (updateError) {
        console.error("更新订阅记录过程中发生异常:", updateError);
        await logError("update_subscription_exception", appUserId, updateError);
      }
    } else {
      // 创建新订阅记录
      console.log(`为用户 ${appUserId} 创建新订阅记录`);
      
      // 构建订阅数据，移除不存在的字段
      const subscriptionData: any = {
        user_id: appUserId,
        product_id: productId,
        status: 'active',
        period_type: period_type,
        entitlements: JSON.stringify(entitlements),
        is_family_share: is_family_share,
        country_code: country_code,
        presented_offering_id: presented_offering_id,
        is_trial_conversion: is_trial_conversion,
        environment: environment,
        store: store || 'unknown',
        transaction_id: transaction_id,
        original_transaction_id: original_transaction_id, 
        purchase_date: purchased_at.toISOString(),
        expires_at: expires_at ? expires_at.toISOString() : null,
        webhook_received_at: now,
        created_at: now,
        auto_renew_status: true,
        event_type: eventData.type || 'INITIAL_PURCHASE',
        last_event_at: now
      };
      
      // 如果是试用期，添加试用期相关字段
      if (is_trial) {
        subscriptionData.is_trial = true;
        subscriptionData.trial_start_at = purchased_at.toISOString();
        subscriptionData.trial_end_at = expires_at ? expires_at.toISOString() : null;
        subscriptionData.trial_type = 'standard';
        
        // 计算试用期时长(天)
        if (expires_at) {
          const trial_days = Math.round((expires_at.getTime() - purchased_at.getTime()) / (1000 * 60 * 60 * 24));
          subscriptionData.trial_duration_days = trial_days;
        }
      }
      
      console.log("准备插入订阅数据:", subscriptionData);
      
      const { data: newSubscription, error: newSubError } = await supabase
        .from('subscriptions')
        .insert(subscriptionData)
        .select('id')
        .single();
      
      if (newSubError) {
        console.error("创建订阅记录失败:", newSubError);
        
        // 检查数据库schema是否匹配
        console.log("检查订阅表schema");
        const { data: columnInfo, error: columnError } = await supabase
          .from('subscriptions')
          .select('*')
          .limit(1);
          
        if (columnError) {
          console.error("获取订阅表schema失败:", columnError);
        } else if (columnInfo && columnInfo.length > 0) {
          console.log("订阅表列名:", Object.keys(columnInfo[0] || {}));
        } else {
          console.log("订阅表中没有数据");
        }
      } else {
        console.log(`创建用户 ${appUserId} 的新订阅记录成功，ID: ${newSubscription?.id}`);
        
        // 记录首次支付
        if (newSubscription) {
          await createPaymentRecord(appUserId, newSubscription.id, eventData);
          
          // 尝试查找并更新相关发票状态
          if (eventData.id) {
            try {
              const { data: updateResult, error: updateError } = await supabase.rpc(
                'update_invoice_to_paid',
                {
                  p_invoice_id: eventData.id,
                  p_subscription_id: newSubscription.id,
                  p_payment_date: now
                }
              );
              
              if (updateError) {
                console.error("更新发票状态失败:", updateError);
              } else if (updateResult) {
                console.log(`成功将发票 ${eventData.id} 状态更新为已支付`);
              } else {
                console.log(`未找到ID为 ${eventData.id} 的待支付发票，可能是直接购买而非由发票转化`);
              }
            } catch (invoiceError) {
              console.error("处理发票更新时出错:", invoiceError);
            }
          }
          
          // 验证之前可能存在的临时授权
          if (transaction_id) {
            try {
              // 调用验证临时授权的函数
              const { data: validationResult, error: validationError } = await supabase.rpc(
                'validate_temporary_entitlement',
                {
                  p_transaction_id: transaction_id,
                  p_is_valid: true,
                  p_subscription_id: newSubscription.id
                }
              );
              
              if (validationError) {
                console.error("验证临时授权失败:", validationError);
              } else if (validationResult) {
                console.log(`成功验证与交易ID ${transaction_id} 关联的临时授权`);
              } else {
                console.log(`未找到与交易ID ${transaction_id} 关联的临时授权`);
              }
            } catch (validationError) {
              console.error("处理临时授权验证时出错:", validationError);
            }
          }
        }
      }
    }
    
    // 确认VIP状态是否正确设置
    const { data: profileCheck, error: profileCheckError } = await supabase
      .from('profiles')
      .select('is_vip, vip_since, vip_expires_at')
      .eq('id', appUserId)
      .single();
    
    if (profileCheckError) {
      console.error("检查VIP状态失败:", profileCheckError);
    } else {
      console.log(`用户 ${appUserId} 的当前VIP状态:`, profileCheck);
    }
    
    console.log(`用户 ${appUserId} 的VIP状态处理完成`);
    console.log("处理订阅活跃事件 - 完成");
    console.log("\n----- 结束处理订阅激活事件 -----\n");
  } catch (error) {
    console.error("处理订阅激活事件失败:", error);
    // 添加测试辅助信息
    console.error("事件数据:", JSON.stringify(eventData).slice(0, 500));
    // 记录错误细节但不中断处理
    await logError("subscription_active", eventData.app_user_id, error);
    // 不抛出异常，让webhook继续处理其他任务
  }
}

// 修改 createPaymentRecord 函数，处理is_trial_conversion字段
async function createPaymentRecord(userId: string, subscriptionId: string, eventData: any) {
  try {
    // 提取价格信息 - 优先使用本地货币价格
    const amount = eventData.price_in_purchased_currency || eventData.price || 0;
    const currency = eventData.currency || 'USD';
    const transaction_id = eventData.transaction_id;
    const payment_method = eventData.store || 'app_store';
    const event_type = eventData.type || 'UNKNOWN';
    const product_id = eventData.product_id || '';
    const is_trial_conversion = eventData.is_trial_conversion || false;
    const period_type = eventData.period_type || '';
    const is_trial = period_type === 'TRIAL';
    
    // 构建有意义的支付描述
    let description = '';
    
    // 根据事件类型生成不同的描述
    switch (event_type) {
      case 'INITIAL_PURCHASE':
        if (is_trial) {
          description = `开始试用 ${product_id}`;
        } else if (is_trial_conversion) {
          description = `试用转付费 ${product_id}`;
        } else {
        description = `首次购买 ${product_id}`;
        }
        break;
      case 'RENEWAL':
        description = is_trial_conversion ? 
          `试用转付费续订 ${product_id}` : 
          `续订 ${product_id}`;
        break;
      case 'PRODUCT_CHANGE':
        description = `产品变更到 ${product_id}`;
        break;
      case 'SUBSCRIPTION_EXTENDED':
        description = `订阅延长 ${product_id}`;
        // 如果有新的到期时间，添加到描述中
        if (eventData.expiration_at_ms) {
          const expirationDate = new Date(eventData.expiration_at_ms);
          description += ` 至 ${expirationDate.toISOString().split('T')[0]}`;
        }
        break;
      case 'TRIAL_STARTED':
        description = `开始试用 ${product_id}`;
        break;
      case 'TRIAL_CONVERSION':
        description = `试用转付费 ${product_id}`;
        break;
      default:
        description = `${event_type} - ${product_id}`;
    }
    
    // 添加国家/地区信息
    if (eventData.country_code) {
      description += ` (${eventData.country_code})`;
    }
    
    console.log("准备创建支付记录:", {
      用户ID: userId,
      订阅ID: subscriptionId,
      金额: amount,
      货币: currency,
      交易ID: transaction_id,
      支付方式: payment_method,
      描述: description,
      是否为试用转付费: is_trial_conversion
    });
    
    // 准备元数据
    const metadata = { 
      ...eventData,
      // 如果有subscriber_attributes但很大，只保留email等关键信息
      subscriber_attributes: eventData.subscriber_attributes ? {
        email: eventData.subscriber_attributes["$email"] || null,
        // 可以添加其他有用的属性
      } : null
    };
    
    // 构造支付记录数据
    const paymentData = {
      user_id: userId,
      subscription_id: subscriptionId,
      amount: amount,
      currency: currency,
      status: 'succeeded',
      payment_method: payment_method,
      payment_provider: 'revenuecat',
      payment_provider_payment_id: transaction_id,
      description: description,
      metadata: {
        ...metadata,
        is_trial_conversion: is_trial_conversion
      }
    };
    
    // 调用数据库函数创建支付记录
    const { data, error } = await supabase.rpc('create_payment_record', {
      p_user_id: userId,
      p_subscription_id: subscriptionId,
      p_amount: amount,
      p_currency: currency,
      p_payment_method: payment_method,
      p_transaction_id: transaction_id,
      p_description: description,
      p_metadata: paymentData.metadata
    });
    
    if (error) {
      console.error("调用create_payment_record函数失败:", error);
      
      // 备用方案：直接插入记录
      console.log("使用备用方案直接创建支付记录");
      
      const { error: insertError } = await supabase
        .from('payments')
        .insert(paymentData);
        
      if (insertError) {
        console.error("直接创建支付记录失败:", insertError);
      } else {
        console.log(`成功创建用户 ${userId} 的支付记录（备用方案）`);
      }
    } else {
      console.log(`成功创建用户 ${userId} 的支付记录，ID: ${data}`);
    }
  } catch (error) {
    console.error("创建支付记录时出错:", error);
    // 记录错误但不中断处理
    await logError("create_payment", userId, error);
  }
}

// 处理订阅失效事件（取消、到期）
async function handleSubscriptionInactive(eventData: any) {
  try {
    // 向后兼容不同的数据结构
    // 确保我们始终使用正确的字段，无论数据是直接提供还是嵌套在event对象中
    const data = eventData.event ? eventData.event : eventData;
    
    // 检查是否为退款事件
    // 1. 价格为负值的取消事件
    // 2. 明确标记为refund的事件
    // 3. RevenueCat的特定退款标识
    const isRefund = (
      (data.price < 0 || data.price_in_purchased_currency < 0) || 
      (data.refunded === true) || 
      (data.is_refund === true) ||
      (data.type === 'CANCELLATION' && 
        (data.cancellation_reason?.toLowerCase().includes('refund') || 
         data.cancel_reason?.toLowerCase().includes('refund')))
    );
    
    // 检查是否为试用期取消
    const isTrial = data.period_type === 'TRIAL';
    const cancelReason = data.cancellation_reason || data.cancel_reason || null;
    
    console.log("处理订阅失效事件:", {
      用户ID: data.app_user_id,
      事件类型: data.type,
      产品ID: data.product_id,
      过期类型: data.type === 'EXPIRATION' ? data.expiration_reason : '不适用',
      是否退款: isRefund ? '是' : '否',
      退款金额: isRefund ? Math.abs(data.price || 0) : 0,
      退款币种: data.currency || 'USD',
      是否为试用期: isTrial ? '是' : '否',
      取消原因: cancelReason || '未指定'
    });
    
    // 使用let而不是const声明用户ID
    let app_user_id = data.app_user_id;
    if (!app_user_id) {
      throw new Error("缺少app_user_id");
    }
    
    // 尝试获取用户邮箱
    let user_email = null;
    if (data.subscriber_attributes && data.subscriber_attributes["$email"]) {
      user_email = data.subscriber_attributes["$email"].value;
    } else if (data.email) {
      user_email = data.email;
    }
    
    // 确保用户存在于数据库中
    await ensureUserExists(app_user_id, user_email);
    console.log(`已确认用户 ${app_user_id} 存在`);
    
    // 获取产品ID
    const productId = data.product_id;
    if (!productId) {
      throw new Error("缺少product_id");
    }
    
    const { transaction_id, original_transaction_id } = data;
    const subscription_identifier = original_transaction_id || transaction_id;
    const now = new Date().toISOString();
    
    // 提取取消原因和过期原因（如果存在）
    let expirationReason = null;
    
    if (data.type === 'CANCELLATION') {
      // 尝试从不同位置提取取消原因
      // 取消原因已在上面提取
                  
      // 记录详细的事件数据，帮助调试
      console.log("CANCELLATION事件详细数据:", JSON.stringify({
        事件类型: data.type,
        用户ID: app_user_id,
        产品ID: productId,
        取消原因: cancelReason,
        是否退款: isRefund,
        退款金额: isRefund ? Math.abs(data.price || 0) : 0,
        退款币种: data.currency || 'USD',
        是否为试用期: isTrial,
        原始数据结构: Object.keys(data)
      }));
    
      if (cancelReason) {
      const normalizedReason = normalizeReasonDescription(cancelReason);
      console.log(`用户取消订阅，原因: ${cancelReason}，标准化描述: ${normalizedReason}`);
    }
      
      if (isRefund) {
        console.log(`检测到退款事件，金额: ${Math.abs(data.price || 0)} ${data.currency || 'USD'}`);
      }
    } else if (data.type === 'EXPIRATION') {
      // 提取过期原因
      expirationReason = data.expiration_reason;
      console.log("EXPIRATION事件详细数据:", JSON.stringify({
        事件类型: data.type,
        用户ID: app_user_id,
        产品ID: productId,
        过期原因: expirationReason,
        过期时间: data.expiration_at_ms ? new Date(data.expiration_at_ms).toISOString() : '未知',
        家庭共享: data.is_family_share ? '是' : '否',
        国家代码: data.country_code || '未知',
        币种: data.currency || '未知',
        价格: data.price || 0,
        原始数据结构: Object.keys(data)
      }));
      
      if (expirationReason) {
        const normalizedReason = normalizeReasonDescription(expirationReason);
        console.log(`用户订阅过期，原因: ${expirationReason}，标准化描述: ${normalizedReason}`);
      }
    }
    
    // 处理entitlements数据
    let entitlements = [];
    if (data.entitlement_id) {
      entitlements.push(data.entitlement_id);
    }
    if (data.entitlement_ids && Array.isArray(data.entitlement_ids)) {
      entitlements = [...new Set([...entitlements, ...data.entitlement_ids])];
    }
    console.log(`权益列表: ${entitlements.length > 0 ? entitlements.join(', ') : '无'}`);
    
    // 查找该用户的活跃订阅
    let activeSubscriptions = [];
    let subQueryError = null;
    
    try {
      if (isTrial) {
        // 对于试用期取消，我们需要特别查找is_trial=true的订阅
        const { data: trialSubscriptions, error: trialSubError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', app_user_id)
          .eq('is_trial', true)
          .in('status', ['active', 'paused']);
          
        if (!trialSubError && trialSubscriptions && trialSubscriptions.length > 0) {
          activeSubscriptions = trialSubscriptions;
          console.log(`找到 ${trialSubscriptions.length} 个活跃试用期订阅`);
        } else {
          subQueryError = trialSubError;
        }
      } else {
      // 根据user_id查询活跃订阅
      const { data: userSubscriptions, error: userSubError } = await supabase
        .from('subscriptions')
          .select('*')
          .eq('user_id', app_user_id)
        .eq('status', 'active');
        
      if (!userSubError && userSubscriptions && userSubscriptions.length > 0) {
        activeSubscriptions = userSubscriptions;
      } else if (subscription_identifier) {
        // 如果找不到用户的活跃订阅，但有交易标识符，尝试查找特定交易
        // 分别查询各种ID字段
        const { data: txnSubscription, error: txnSubError } = await supabase
          .from('subscriptions')
            .select('*')
          .eq('transaction_id', subscription_identifier)
          .eq('status', 'active')
          .maybeSingle();
          
        if (!txnSubError && txnSubscription) {
          activeSubscriptions = [txnSubscription];
        } else {
          // 检查original_transaction_id
          const { data: origTxnSubscription, error: origTxnSubError } = await supabase
            .from('subscriptions')
              .select('*')
            .eq('original_transaction_id', subscription_identifier)
            .eq('status', 'active')
            .maybeSingle();
            
          if (!origTxnSubError && origTxnSubscription) {
            activeSubscriptions = [origTxnSubscription];
          } else {
            // 检查payment_provider_subscription_id
            const { data: providerSubId, error: providerSubError } = await supabase
              .from('subscriptions')
                .select('*')
              .eq('payment_provider_subscription_id', subscription_identifier)
              .eq('status', 'active')
              .maybeSingle();
              
            if (!providerSubError && providerSubId) {
              activeSubscriptions = [providerSubId];
            } else {
              subQueryError = userSubError || txnSubError || origTxnSubError || providerSubError;
            }
          }
        }
      } else {
        subQueryError = userSubError;
        }
      }
    } catch (error) {
      console.error("查询活跃订阅出错:", error);
      subQueryError = error;
    }
    
    if (subQueryError) {
      console.error("查询活跃订阅失败:", subQueryError);
      // 继续处理，即使查询失败也尝试更新用户状态
    }
    
    if (!activeSubscriptions || activeSubscriptions.length === 0) {
      console.log(`未找到用户 ${app_user_id} 的活跃订阅`);
      
      // 即使没找到活跃订阅，也要确保用户VIP状态被设置为false
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          is_vip: false,
          vip_is_trial: false, // 确保试用标记也被清除
          vip_updated_at: now,
          updated_at: now
        })
        .eq('id', app_user_id);
      
      if (profileError) {
        console.error("更新用户VIP状态失败:", profileError);
      } else {
        console.log(`成功更新用户 ${app_user_id} 的VIP状态为false`);
      }
      
      // 如果是退款事件，但找不到活跃订阅，也要记录退款信息
      if (isRefund) {
        await recordRefund(app_user_id, null, data);
      }
      
      return;
    }
    
    // 更新所有活跃订阅状态
    for (const subscription of activeSubscriptions) {
      const subscriptionId = typeof subscription === 'object' ? subscription.id : subscription;
      const isTrialSubscription = subscription.is_trial === true;
      
      // 根据订阅类型(试用/正式)和事件类型(取消/过期)准备更新数据
      const updateData: any = {
        updated_at: now
      };
      
      if (isTrialSubscription && data.type === 'CANCELLATION') {
        // 处理试用期取消
        updateData.status = 'trial_cancelled';
        updateData.trial_cancelled = true;
        updateData.trial_cancelled_at = now;
        updateData.trial_cancellation_reason = cancelReason;
        updateData.trial_cancellation_reason_description = normalizeReasonDescription(cancelReason);
        updateData.cancel_at_period_end = false;
      } else if (data.type === 'CANCELLATION') {
        // 处理普通订阅取消
        updateData.status = 'cancelled';
        updateData.cancellation_reason = cancelReason;
        updateData.cancellation_reason_description = normalizeReasonDescription(cancelReason);
        updateData.cancel_at_period_end = false;
      } else if (data.type === 'EXPIRATION') {
        // 处理订阅过期
        updateData.status = 'expired';
        updateData.expiration_reason = expirationReason;
        updateData.expiration_reason_description = normalizeReasonDescription(expirationReason);
      }
      
      // 如果是退款事件，添加退款标记
      if (isRefund) {
        updateData.is_refunded = true;
        updateData.refunded_at = now;
        updateData.refund_amount = Math.abs(data.price || 0);
        updateData.refund_currency = data.currency;
        // 将状态更改为refunded
        updateData.status = 'refunded';
      }
      
      // 更新其他相关字段
      if (entitlements.length > 0) {
        updateData.entitlements = JSON.stringify(entitlements);
      }
      
      if (data.is_family_share !== undefined) {
        updateData.is_family_share = data.is_family_share;
      }
      
      if (data.country_code) {
        updateData.country_code = data.country_code;
      }
      
      if (data.presented_offering_id) {
        updateData.presented_offering_id = data.presented_offering_id;
      }
      
      const { error: updateError } = await supabase
        .from('subscriptions')
        .update(updateData)
        .eq('id', subscriptionId);
      
      if (updateError) {
        console.error(`更新订阅 ${subscriptionId} 状态失败:`, updateError);
      } else {
        // 确定状态描述
        let statusDescription = 'inactive';
        if (isTrialSubscription && data.type === 'CANCELLATION') {
          statusDescription = 'trial_cancelled';
        } else if (data.type === 'CANCELLATION') {
          statusDescription = 'cancelled';
        } else if (data.type === 'EXPIRATION') {
          statusDescription = 'expired';
        } else if (isRefund) {
          statusDescription = 'refunded';
        }
        
        console.log(`成功将订阅 ${subscriptionId} 状态更新为${statusDescription}`);
        
        // 记录订阅事件
        try {
          let eventType = data.type;
          if (isTrialSubscription && data.type === 'CANCELLATION') {
            eventType = 'TRIAL_CANCELLATION';
          } else if (isRefund) {
            eventType = 'REFUND';
          }
          
          await supabase
            .from('subscription_events')
            .insert({
              user_id: app_user_id,
              subscription_id: subscriptionId,
              event_type: eventType,
              event_timestamp: now,
              transaction_id: transaction_id,
              original_transaction_id: original_transaction_id,
              product_id: productId,
              event_data: {
                ...data,
                cancellation_reason: cancelReason,
                expiration_reason: expirationReason,
                is_refund: isRefund,
                is_trial: isTrial || isTrialSubscription
              }
            });
          console.log(`已记录订阅${eventType}事件`);
          
          // 如果是退款事件，记录退款信息
          if (isRefund) {
            await recordRefund(app_user_id, subscriptionId, data);
          }
        } catch (eventError) {
          console.error("记录订阅事件失败:", eventError);
        }
      }
    }
    
    // 更新用户VIP状态
    const { error: profileError } = await supabase
      .from('profiles')
      .update({
        is_vip: false,
        vip_is_trial: false, // 确保试用标记也被清除
        vip_updated_at: now,
        updated_at: now
      })
      .eq('id', app_user_id);
    
    if (profileError) {
      console.error("更新用户VIP状态失败:", profileError);
    } else {
      console.log(`成功更新用户 ${app_user_id} 的VIP状态为false`);
    }
    
    // 如果存在临时授权且有交易ID，标记临时授权验证失败
    if (transaction_id || original_transaction_id) {
      try {
        const { data: validationResult, error: validationError } = await supabase.rpc(
          'validate_temporary_entitlement',
          {
            p_transaction_id: original_transaction_id || transaction_id,
            p_is_valid: false
          }
        );
        
        if (validationError) {
          console.error("标记临时授权验证失败出错:", validationError);
        } else if (validationResult) {
          console.log(`成功标记与交易ID ${original_transaction_id || transaction_id} 关联的临时授权验证失败`);
        }
      } catch (validationError) {
        console.error("处理临时授权验证时出错:", validationError);
      }
    }
    
    console.log(`用户 ${app_user_id} 的VIP状态已更新为未激活`);
  } catch (error) {
    console.error("处理订阅失效事件失败:", error);
    // 记录错误但不中断处理
    await logError("subscription_inactive", eventData.app_user_id || eventData.event?.app_user_id, error);
  }
}

// 记录退款信息到payments表
async function recordRefund(userId: string, subscriptionId: string | null, eventData: any) {
  try {
    const refundAmount = Math.abs(eventData.price || eventData.price_in_purchased_currency || 0);
    const currency = eventData.currency || 'USD';
    const transaction_id = `${eventData.transaction_id}_refund`;
    
    console.log(`记录退款信息: 用户=${userId}, 金额=${refundAmount} ${currency}`);
    
    // 构建退款记录数据
    const refundData = {
      user_id: userId,
      subscription_id: subscriptionId,
      amount: refundAmount,
      currency: currency,
      status: 'refunded',
      payment_method: eventData.store || 'app_store',
      payment_provider: 'revenuecat',
      payment_provider_payment_id: transaction_id,
      description: `退款：${eventData.product_id || '未知产品'}`,
      metadata: {
        original_transaction_id: eventData.original_transaction_id,
        transaction_id: eventData.transaction_id,
        cancel_reason: eventData.cancel_reason || eventData.cancellation_reason,
        product_id: eventData.product_id,
        store: eventData.store,
        refund_date: new Date().toISOString()
      }
    };
    
    // 插入退款记录
    const { data, error } = await supabase
      .from('payments')
      .insert(refundData);
      
    if (error) {
      console.error("记录退款信息失败:", error);
    } else {
      console.log(`成功记录用户 ${userId} 的退款信息`);
    }
  } catch (error) {
    console.error("记录退款信息时出错:", error);
  }
}

// 修改 handleBillingIssue 函数，修复变量命名冲突并增强功能
async function handleBillingIssue(eventData: any) {
  try {
    // 向后兼容不同的数据结构
    // 确保我们始终使用正确的字段，无论数据是直接提供还是嵌套在event对象中
    const data = eventData.event ? eventData.event : eventData;
    
    const { app_user_id, transaction_id, original_transaction_id, product_id, store } = data;
    const subscription_identifier = original_transaction_id || transaction_id;
    
    if (!app_user_id) {
      console.error("缺少用户ID，无法处理账单问题");
      return;
    }
    
    // 记录账单问题但不立即更改订阅状态
    console.log(`用户 ${app_user_id} 的订阅遇到账单问题，产品ID: ${product_id || '未知'}`);
    
    // 提取更多有用信息 - 包括宽限期到期日期
    const grace_period_expires_date = data.grace_period_expires_date_ms ? 
      new Date(data.grace_period_expires_date_ms).toISOString() : null;
    const bill_retry_attempted = data.bill_retry_attempted;
    const offer_code = data.offer_code || null; // 提取优惠码
    const price = data.price || 0; // 价格
    const price_in_purchased_currency = data.price_in_purchased_currency || 0; // 本地货币价格
    const currency = data.currency || 'USD'; // 货币
    const event_timestamp = data.event_timestamp_ms ? 
      new Date(data.event_timestamp_ms).toISOString() : new Date().toISOString();
    
    // 记录完整的账单问题详情
    console.log("账单问题详情:", {
      用户ID: app_user_id,
      产品ID: product_id,
      交易ID: transaction_id,
      宽限期: grace_period_expires_date,
      已尝试重试: bill_retry_attempted ? "是" : "否",
      商店: store,
      优惠码: offer_code,
      价格: price,
      购买货币价格: price_in_purchased_currency,
      货币: currency,
      事件时间戳: event_timestamp
    });
    
    // 使用通用函数查找订阅
      const identifiers = {
        'payment_provider_subscription_id': subscription_identifier,
        'transaction_id': transaction_id,
        'original_transaction_id': original_transaction_id
      };
      
    const subscriptionId = await findSubscriptionByIdentifiers(app_user_id, identifiers, product_id);
    
    // 准备问题详情记录 - 增加更多字段
    const billingDetails = {
      product_id: product_id,
      store: store,
      grace_period_expires_date: grace_period_expires_date,
      bill_retry_attempted: bill_retry_attempted,
      offer_code: offer_code,
      price: price,
      price_in_purchased_currency: price_in_purchased_currency,
      currency: currency,
      event_timestamp: event_timestamp,
      raw_event: {
        ...data,
        // 如果有subscriber_attributes但很大，只保留关键信息
        subscriber_attributes: data.subscriber_attributes ? {
          email: data.subscriber_attributes["$email"]?.value || null,
          idfa: data.subscriber_attributes["$idfa"]?.value || null,
          appsflyerId: data.subscriber_attributes["$appsflyerId"]?.value || null
        } : null
      }
    };
    
    // 确保用户存在于数据库中
    try {
      // 尝试获取事件中的用户邮箱（如果有）
      const userEmail = data.subscriber_attributes?.["$email"]?.value || data.email || null;
      
      // 确保用户存在
      await ensureUserExists(app_user_id, userEmail);
      console.log(`用户 ${app_user_id} 确认存在或已创建`);
    } catch (userError) {
      console.error(`确保用户存在失败: ${userError}`);
      // 继续处理，因为可能是其他问题，我们不想阻止整个webhook处理
    }
    
    // 使用数据库函数记录账单问题
    if (subscriptionId) {
      // 记录订阅事件
      try {
        await supabase
          .from('subscription_events')
          .insert({
            user_id: app_user_id,
            subscription_id: subscriptionId,
            event_type: 'BILLING_ISSUE',
            event_timestamp: new Date().toISOString(),
            transaction_id: transaction_id,
            original_transaction_id: original_transaction_id,
            product_id: product_id,
            event_data: data
          });
        console.log("已记录BILLING_ISSUE事件");
      } catch (eventError) {
        console.error("记录订阅事件失败:", eventError);
      }
      
      const { data: billingIssueId, error: billingError } = await supabase.rpc('handle_billing_issue', {
        p_user_id: app_user_id,
        p_subscription_id: subscriptionId,
        p_transaction_id: transaction_id,
        p_details: billingDetails
      });
      
      if (billingError) {
        console.error("调用handle_billing_issue函数失败:", billingError);
        
        // 备用方案：直接插入记录
        console.log("使用备用方案直接创建账单问题记录");
        const billingIssueData = {
          user_id: app_user_id,
          subscription_id: subscriptionId,
          transaction_id: transaction_id,
          detected_at: new Date(),
          resolved: false,
          details: billingDetails
        };
        
        const { error: insertError } = await supabase
          .from('billing_issues')
          .insert(billingIssueData);
        
        if (insertError) {
          console.error("记录账单问题失败:", insertError);
        } else {
          console.log(`成功记录用户 ${app_user_id} 的账单问题（备用方案）`);
        }
      } else {
        console.log(`成功记录用户 ${app_user_id} 的账单问题，ID: ${billingIssueId}`);
      }
      
      // 更新订阅表中的状态 - 标记为有账单问题
      try {
        const { error: updateSubError } = await supabase
          .from('subscriptions')
          .update({
            // 不改变主状态，但添加标记和更新时间
            has_billing_issue: true,
            billing_issue_detected_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', subscriptionId);
          
        if (updateSubError) {
          console.error("更新订阅账单状态标记失败:", updateSubError);
        } else {
          console.log(`已在订阅记录 ${subscriptionId} 上标记账单问题`);
        }
      } catch (updateError) {
        console.error("更新订阅表时出错:", updateError);
      }
    } else {
      console.error(`未找到用户 ${app_user_id} 的相关订阅记录，无法记录账单问题`);
      
      // 即使没有找到订阅ID，也尝试记录账单问题
      const billingIssueData = {
        user_id: app_user_id,
        transaction_id: transaction_id,
        detected_at: new Date(),
        resolved: false,
        details: billingDetails
      };
      
      try {
        const { error: insertError } = await supabase
          .from('billing_issues')
          .insert(billingIssueData);
        
        if (insertError) {
          console.error("记录账单问题失败:", insertError);
        } else {
          console.log(`成功记录用户 ${app_user_id} 的账单问题（没有关联订阅）`);
        }
      } catch (insertError) {
        console.error("插入账单问题记录失败:", insertError);
      }
    }
  } catch (error) {
    console.error("处理账单问题事件失败:", error);
    // 记录错误但不中断处理
    await logError("billing_issue", eventData.app_user_id || eventData.event?.app_user_id, error);
  }
}

// 处理取消恢复事件
async function handleUncancellation(data: any) {
  try {
    console.log("处理订阅取消恢复事件:", {
      用户ID: data.app_user_id,
      产品ID: data.product_id,
      交易ID: data.transaction_id || data.original_transaction_id
    });
    
    // 提取用户ID
    let app_user_id = data.app_user_id;
    if (!app_user_id) {
      throw new Error("缺少app_user_id");
    }
    
    // 确保用户存在于数据库中
    await ensureUserExists(app_user_id, data.email);
    console.log(`已确认用户 ${app_user_id} 存在`);
    
    // 获取产品ID
    const productId = data.product_id;
    if (!productId) {
      throw new Error("缺少product_id");
    }
    
    const { transaction_id, original_transaction_id } = data;
    const subscription_identifier = original_transaction_id || transaction_id;
    const now = new Date().toISOString();
    
    // 提取关键数据
    const expires_at = data.expiration_at_ms ? new Date(data.expiration_at_ms) : null;
    const environment = data.environment || "PRODUCTION";
    
    if (!subscription_identifier) {
      console.error("缺少交易标识符，无法找到订阅记录");
      return;
    }
    
    // 查找被取消的订阅记录
    let subscriptionId = null;
    
    try {
      // 尝试通过多种标识符查找订阅
      const identifiers = [
        { field: 'payment_provider_subscription_id', value: subscription_identifier },
        { field: 'transaction_id', value: transaction_id },
        { field: 'original_transaction_id', value: original_transaction_id }
      ];
      
      for (const { field, value } of identifiers) {
        if (!value) continue;
        
        const { data: subscription, error } = await supabase
          .from('subscriptions')
          .select('id, status')
          .eq(field, value)
          .order('created_at', { ascending: false })
          .maybeSingle();
          
        if (!error && subscription) {
          subscriptionId = subscription.id;
          console.log(`找到订阅记录 ${subscriptionId}，当前状态：${subscription.status}`);
          break;
        }
      }
      
      // 如果找不到特定订阅，尝试基于产品ID和用户ID搜索
      if (!subscriptionId) {
        const { data: userSubscription, error: userSubError } = await supabase
          .from('subscriptions')
          .select('id, status')
          .eq('user_id', app_user_id)
          .eq('product_id', productId)
          .in('status', ['cancelled', 'expired'])  // 只查找被取消或过期的订阅
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();
          
        if (!userSubError && userSubscription) {
          subscriptionId = userSubscription.id;
          console.log(`找到用户最近的已取消订阅: ${subscriptionId}`);
        }
      }
    } catch (searchError) {
      console.error("搜索订阅记录时出错:", searchError);
    }
    
    if (!subscriptionId) {
      console.log("未找到对应的已取消订阅记录，将按新订阅处理");
      await handleSubscriptionActive(data);
      return;
    }
    
    // 更新订阅记录 - 恢复活跃状态
    const updateData: any = {
      status: 'active',
      cancel_at_period_end: false,
      cancellation_reason: null,  // 清除取消原因
      cancellation_reason_description: null,  // 清除取消原因描述
      expires_at: expires_at ? expires_at.toISOString() : null,
      environment: environment,
      updated_at: now,
      webhook_received_at: now
    };
    
    // 处理entitlements数据
    let entitlements = [];
    if (data.entitlement_id) {
      entitlements.push(data.entitlement_id);
    }
    if (data.entitlement_ids && Array.isArray(data.entitlement_ids)) {
      entitlements = [...new Set([...entitlements, ...data.entitlement_ids])];
    }
    
    // 如果有权益数据，更新到订阅记录
    if (entitlements.length > 0) {
      updateData.entitlements = JSON.stringify(entitlements);
    }
    
    // 处理其他可能存在的字段
    if (data.period_type) {
      updateData.period_type = data.period_type;
    }
    
    if (data.is_family_share !== undefined) {
      updateData.is_family_share = data.is_family_share;
    }
    
    if (data.country_code) {
      updateData.country_code = data.country_code;
    }
    
    if (data.presented_offering_id) {
      updateData.presented_offering_id = data.presented_offering_id;
    }
    
    const { error: subscriptionError } = await supabase
      .from('subscriptions')
      .update(updateData)
      .eq('id', subscriptionId);
    
    if (subscriptionError) {
      console.error(`恢复订阅 ${subscriptionId} 失败:`, subscriptionError);
    } else {
      console.log(`成功恢复订阅 ${subscriptionId}`);
      
      // 记录订阅事件
      try {
        await supabase
          .from('subscription_events')
          .insert({
            user_id: app_user_id,
            subscription_id: subscriptionId,
            event_type: 'UNCANCELLATION',
            event_timestamp: now,
            transaction_id: data.transaction_id,
            original_transaction_id: data.original_transaction_id,
            product_id: productId,
            event_data: data
          });
        console.log("已记录UNCANCELLATION事件");
      } catch (eventError) {
        console.error("记录订阅事件失败:", eventError);
      }
      
      // 更新用户VIP状态
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          is_vip: true,
          vip_product_id: productId,
          vip_expires_at: expires_at,
          vip_environment: environment,
          vip_updated_at: now,
          updated_at: now
        })
        .eq('id', app_user_id);
      
      if (profileError) {
        console.error("更新用户VIP状态失败:", profileError);
      } else {
        console.log(`成功更新用户 ${app_user_id} 的VIP状态为活跃`);
      }
    }
  } catch (error) {
    console.error("处理订阅恢复事件失败:", error);
    // 记录错误但不中断处理
    await logError("uncancellation", data.app_user_id, error);
  }
}

// 处理订阅暂停事件 (Google Play商店特有)
async function handleSubscriptionPaused(data: any) {
  try {
    console.log("处理订阅暂停事件:", {
      用户ID: data.app_user_id,
      产品ID: data.product_id,
      交易ID: data.transaction_id,
      自动恢复时间: data.auto_resume_at_ms ? new Date(data.auto_resume_at_ms).toISOString() : '未指定'
    });
    
    // 提取用户ID
    let app_user_id = data.app_user_id;
    if (!app_user_id) {
      throw new Error("缺少app_user_id");
    }
    
    // 确保用户存在于数据库中
    await ensureUserExists(app_user_id, data.email);
    console.log(`已确认用户 ${app_user_id} 存在`);
    
    // 获取产品ID
    const productId = data.product_id;
    if (!productId) {
      throw new Error("缺少product_id");
    }
    
    const { transaction_id, original_transaction_id } = data;
    const subscription_identifier = original_transaction_id || transaction_id;
    const now = new Date().toISOString();
    
    // 处理暂停时间和自动恢复时间
    const expires_at = data.expiration_at_ms ? new Date(data.expiration_at_ms) : null;
    const auto_resume_at = data.auto_resume_at_ms ? new Date(data.auto_resume_at_ms) : null;
    
    if (!subscription_identifier) {
      console.error("缺少交易标识符，无法找到订阅记录");
      return;
    }
    
    // 查找订阅记录
    let subscriptionId = null;
    let subscriptionRecord = null;
    
    try {
      // 尝试通过多种标识符查找订阅
      const identifiers = [
        { field: 'payment_provider_subscription_id', value: subscription_identifier },
        { field: 'transaction_id', value: transaction_id },
        { field: 'original_transaction_id', value: original_transaction_id }
      ];
      
      for (const { field, value } of identifiers) {
        if (!value) continue;
        
        const { data: subscription, error } = await supabase
          .from('subscriptions')
          .select('*')
          .eq(field, value)
          .order('created_at', { ascending: false })
          .maybeSingle();
          
        if (!error && subscription) {
          subscriptionId = subscription.id;
          subscriptionRecord = subscription;
          console.log(`找到订阅记录 ${subscriptionId}，当前状态：${subscription.status}`);
          break;
        }
      }
      
      // 如果找不到特定订阅，尝试基于产品ID和用户ID搜索
      if (!subscriptionId) {
        const { data: userSubscription, error: userSubError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', app_user_id)
          .eq('product_id', productId)
          .eq('status', 'active')
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();
          
        if (!userSubError && userSubscription) {
          subscriptionId = userSubscription.id;
          subscriptionRecord = userSubscription;
          console.log(`找到用户的活跃订阅: ${subscriptionId}`);
        }
      }
    } catch (searchError) {
      console.error("搜索订阅记录时出错:", searchError);
    }
    
    if (!subscriptionId) {
      console.error(`未找到用户 ${app_user_id} 的活跃订阅，无法处理暂停`);
      return;
    }
    
    // 处理订阅entitlements
    let entitlements = [];
    if (data.entitlement_id) {
      entitlements.push(data.entitlement_id);
    }
    if (data.entitlement_ids && Array.isArray(data.entitlement_ids)) {
      entitlements = [...new Set([...entitlements, ...data.entitlement_ids])];
    }
    
    // 更新订阅记录 - 设置为暂停状态
    const updateData: any = {
      status: 'paused',
      is_paused: true,
      paused_at: now,
      auto_resume_at: auto_resume_at ? auto_resume_at.toISOString() : null,
      expires_at: expires_at ? expires_at.toISOString() : null,
      updated_at: now,
      webhook_received_at: now
    };
    
    // 如果有权益数据，更新到订阅记录
    if (entitlements.length > 0) {
      updateData.entitlements = JSON.stringify(entitlements);
    }
    
    // 更新其他可能的字段
    if (data.period_type) {
      updateData.period_type = data.period_type;
    }
    
    const { error: updateError } = await supabase
      .from('subscriptions')
      .update(updateData)
      .eq('id', subscriptionId);
    
    if (updateError) {
      console.error(`更新订阅 ${subscriptionId} 状态失败:`, updateError);
    } else {
      console.log(`成功将订阅 ${subscriptionId} 状态更新为暂停`);
      
      // 记录订阅暂停事件
      try {
        await supabase
          .from('subscription_events')
          .insert({
            user_id: app_user_id,
            subscription_id: subscriptionId,
            event_type: 'SUBSCRIPTION_PAUSED',
            event_timestamp: now,
            transaction_id: transaction_id,
            original_transaction_id: original_transaction_id,
            product_id: productId,
            event_data: data
          });
        console.log("已记录订阅暂停事件");
      } catch (eventError) {
        console.error("记录订阅事件失败:", eventError);
      }
      
      // Google Play暂停订阅后用户仍然拥有已购买的权益直到过期
      // 因此我们暂时不需要改变VIP状态，除非已过期
      if (expires_at && expires_at < new Date()) {
        // 如果已过期，更新用户VIP状态为false
        const { error: profileError } = await supabase
          .from('profiles')
          .update({
            is_vip: false,
            vip_updated_at: now,
            updated_at: now
          })
          .eq('id', app_user_id);
        
        if (profileError) {
          console.error("更新用户VIP状态失败:", profileError);
        } else {
          console.log(`已将过期暂停的用户 ${app_user_id} 的VIP状态设置为false`);
        }
      } else {
        console.log(`暂停的用户 ${app_user_id} 仍然保持VIP状态，直到过期时间: ${expires_at?.toISOString() || '未知'}`);
      }
    }
  } catch (error) {
    console.error("处理订阅暂停事件失败:", error);
    // 记录错误但不中断处理
    await logError("subscription_paused", data.app_user_id, error);
  }
}

// 处理订阅转移事件
async function handleSubscriptionTransfer(eventData: any) {
  try {
    // 向后兼容不同的数据结构
    // 确保我们始终使用正确的字段，无论数据是直接提供还是嵌套在event对象中
    const data = eventData.event ? eventData.event : eventData;
    
    console.log("处理订阅转移事件:", {
      转出用户IDs: data.transferred_from,
      转入用户IDs: data.transferred_to,
      事件时间戳: data.event_timestamp_ms ? new Date(data.event_timestamp_ms).toISOString() : '未知',
      商店: data.store,
      环境: data.environment
    });
    
    // 验证必要的字段
    if (!data.transferred_from || !data.transferred_from.length || !data.transferred_to || !data.transferred_to.length) {
      throw new Error("TRANSFER事件缺少必要的transferred_from或transferred_to字段");
    }
    
    const fromUserId = data.transferred_from[0];
    let toUserId = data.transferred_to[0];
    const now = new Date().toISOString();
    
    console.log(`处理订阅转移: 从用户 ${fromUserId} 到用户 ${toUserId}`);
    
    // 尝试使用数据库函数处理订阅转移
    try {
      console.log("尝试使用数据库函数处理订阅转移");
      const { data: transferResult, error: transferError } = await supabase.rpc(
        'handle_subscription_transfer',
        {
          p_from_user_id: fromUserId,
          p_to_user_id: toUserId,
          p_event_data: data
        }
      );
      
      if (transferError) {
        console.error("数据库函数处理订阅转移失败:", transferError);
        console.log("将回退到直接处理流程");
      } else if (transferResult) {
        console.log("数据库函数处理订阅转移成功:", transferResult);
        
        // 记录事件
        try {
          if (transferResult.transferred_subscriptions && transferResult.transferred_subscriptions.length > 0) {
            // 对每个转移的订阅记录事件
            for (const subId of transferResult.transferred_subscriptions) {
              await supabase
                .from('subscription_events')
                .insert([
                  {
                    user_id: transferResult.from_user_id,
                    subscription_id: subId,
                    event_type: 'TRANSFER_OUT',
                    event_timestamp: now,
                    product_id: null, // 在数据库中已经有产品ID
                    event_data: {
                      ...data,
                      transferred_to_user_id: transferResult.to_user_id
                    }
                  }
                ]);
            }
            
            console.log("已记录订阅转出事件");
          }
        } catch (eventError) {
          console.error("记录订阅事件失败:", eventError);
        }
        
        console.log(`订阅转移处理完成: 从用户 ${fromUserId} 到用户 ${toUserId}`);
        return true;
      }
    } catch (rpcError) {
      console.error("调用数据库函数时出错:", rpcError);
      console.log("将回退到直接处理流程");
    }
    
    // 检查toUserId是否为RevenueCat匿名ID
    if (toUserId.startsWith('$RCAnonymousID:')) {
      console.log(`检测到目标用户为RevenueCat匿名ID: ${toUserId}`);
      
      // 尝试查找匿名ID对应的标准用户ID
      try {
        const { data: userMapping, error: mappingError } = await supabase
          .from('user_identifiers')
          .select('user_id')
          .eq('anonymous_id', toUserId)
          .maybeSingle();
          
        if (!mappingError && userMapping && userMapping.user_id) {
          console.log(`找到匿名ID映射的标准用户ID: ${userMapping.user_id}`);
          toUserId = userMapping.user_id;
        } else {
          // 查找第二个转入用户ID (如果存在)
          if (data.transferred_to.length > 1) {
            const alternativeUserId = data.transferred_to[1];
            if (!alternativeUserId.startsWith('$RCAnonymousID:')) {
              console.log(`使用备用用户ID: ${alternativeUserId}`);
              toUserId = alternativeUserId;
            }
          }
          console.log(`未找到匿名ID映射，将使用原始ID: ${toUserId}`);
        }
      } catch (mappingError) {
        console.error(`查询用户映射出错: ${mappingError}`);
      }
    }
    
    // 1. 确保目标用户存在
    try {
      await ensureUserExists(toUserId);
      console.log(`转入用户 ${toUserId} 确认存在或已创建`);
    } catch (userError) {
      console.error(`确保转入用户存在失败: ${userError}`);
      
      // 尝试使用rpc函数确保用户存在
      try {
        console.log("尝试使用备用方法确保用户存在");
        const { data: userData, error: rpcError } = await supabase.rpc(
          'ensure_user_exists_with_fallback',
          { 
            p_user_id: toUserId,
            p_original_id: data.transferred_to[0]
          }
        );
        
        if (rpcError) {
          console.error(`备用方法确保用户存在失败: ${rpcError}`);
          throw new Error(`无法确保转入用户 ${toUserId} 存在`);
        } else if (userData) {
          console.log(`通过备用方法确认用户存在: ${userData}`);
          if (userData !== toUserId) {
            toUserId = userData;
            console.log(`已将目标用户ID更新为: ${toUserId}`);
          }
        }
      } catch (rpcError) {
        console.error(`备用方法调用失败: ${rpcError}`);
        throw new Error(`无法确保转入用户 ${toUserId} 存在`);
      }
    }
    
    // 2. 尝试使用特殊函数查找原用户的订阅
    let subscriptionsToTransfer = [];
    
    try {
      console.log(`使用find_subscription_by_rc_id函数查找原用户的订阅`);
      const { data: foundSubscriptions, error: findError } = await supabase.rpc(
        'find_subscription_by_rc_id',
        { p_rc_id: fromUserId }
      );
      
      if (findError) {
        console.error("使用特殊函数查询订阅失败:", findError);
      } else if (foundSubscriptions && foundSubscriptions.length > 0) {
        subscriptionsToTransfer = foundSubscriptions.filter((sub: any) => sub.status === 'active');
        if (subscriptionsToTransfer.length > 0) {
          console.log(`找到 ${subscriptionsToTransfer.length} 个活跃订阅可转移`);
        } else {
          console.log(`找到 ${foundSubscriptions.length} 个订阅，但没有活跃状态的可转移`);
          // 使用最近的订阅
          subscriptionsToTransfer = [foundSubscriptions[0]];
        }
      } else {
        console.log(`未找到订阅，尝试常规查询`);
      }
    } catch (findError) {
      console.error("调用find_subscription_by_rc_id函数失败:", findError);
    }
    
    // 如果特殊函数没找到，尝试常规查询
    if (subscriptionsToTransfer.length === 0) {
      try {
        // 先尝试查询活跃订阅
        const { data: activeSubscriptions, error: subError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', fromUserId)
          .eq('status', 'active');
          
        if (subError) {
          console.error("查询原用户活跃订阅失败:", subError);
          console.log("尝试软失败继续处理");
          await logError("subscription_transfer_query", fromUserId, subError);
        } else if (activeSubscriptions && activeSubscriptions.length > 0) {
          subscriptionsToTransfer = activeSubscriptions;
          console.log(`找到 ${activeSubscriptions.length} 个活跃订阅可转移`);
        } else {
          // 如果没找到活跃订阅，尝试找任何状态的订阅
          console.log("未找到活跃订阅，尝试查找任何状态的订阅");
          const { data: anySubscriptions, error: anySubError } = await supabase
            .from('subscriptions')
            .select('*')
            .eq('user_id', fromUserId)
            .order('created_at', { ascending: false });
            
          if (!anySubError && anySubscriptions && anySubscriptions.length > 0) {
            subscriptionsToTransfer = anySubscriptions.slice(0, 1); // 只使用最新的一个
            console.log(`找到 1 个可转移的非活跃订阅`);
          } else {
            console.error(`未找到原用户 ${fromUserId} 的任何订阅`);
            await logError("subscription_transfer_no_subs", fromUserId, { message: "无可转移订阅" });
          }
        }
      } catch (queryError) {
        console.error("订阅查询过程中出错:", queryError);
      }
    }
    
    // 3. 获取原用户的VIP信息
    let fromUserProfile = null;
    try {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('is_vip, vip_product_id, vip_expires_at, vip_environment')
        .eq('id', fromUserId)
        .single();
        
      if (profileError) {
        console.error("获取原用户VIP信息失败:", profileError);
      } else {
        fromUserProfile = profile;
        if (!profile.is_vip) {
          console.log(`原用户 ${fromUserId} 不是VIP，没有VIP权益可转移`);
        }
      }
    } catch (profileError) {
      console.error("查询用户资料出错:", profileError);
    }
    
    // 4. 处理每个可转移的订阅
    let transferSuccess = false;
    
    for (const subscription of subscriptionsToTransfer) {
      try {
        // 4.1. 将原订阅标记为已转移
        const { error: updateOldSubError } = await supabase
          .from('subscriptions')
          .update({
            status: 'transferred',
            updated_at: now,
            webhook_received_at: now,
            event_type: 'TRANSFER'
          })
          .eq('id', subscription.id);
          
        if (updateOldSubError) {
          console.error(`更新原订阅 ${subscription.id} 状态失败:`, updateOldSubError);
          continue; // 继续处理其他订阅
        }
        
        console.log(`已将原订阅 ${subscription.id} 标记为已转移`);
        
        // 4.2. 为新用户创建新的订阅记录
        const newSubscription = {
          user_id: toUserId,
          product_id: subscription.product_id,
          status: 'active',
          environment: subscription.environment,
          store: subscription.store,
          transaction_id: `${subscription.transaction_id}_transferred`, // 避免唯一性冲突
          original_transaction_id: subscription.original_transaction_id,
          expires_at: subscription.expires_at,
          purchase_date: now, // 使用当前时间作为转移后的购买时间
          webhook_received_at: now,
          auto_renew_status: subscription.auto_renew_status,
          event_type: 'TRANSFER',
          last_event_at: now,
          // 复制其他相关字段
          period_type: subscription.period_type,
          entitlements: subscription.entitlements,
          is_family_share: subscription.is_family_share,
          country_code: subscription.country_code,
          presented_offering_id: subscription.presented_offering_id,
          transfer_from_user_id: fromUserId, // 添加来源用户信息
          transfer_from_subscription_id: subscription.id // 记录原订阅ID
        };
        
        const { data: newSub, error: createSubError } = await supabase
          .from('subscriptions')
          .insert(newSubscription)
          .select('id')
          .single();
          
        if (createSubError) {
          console.error("为新用户创建订阅记录失败:", createSubError);
          continue;
        }
        
        console.log(`已为用户 ${toUserId} 创建新订阅 ${newSub.id}`);
        transferSuccess = true;
        
        // 4.3. 记录转移事件
        await supabase
          .from('subscription_events')
          .insert([
            {
              user_id: fromUserId,
              subscription_id: subscription.id,
              event_type: 'TRANSFER_OUT',
              event_timestamp: now,
              transaction_id: subscription.transaction_id,
              original_transaction_id: subscription.original_transaction_id,
              product_id: subscription.product_id,
              event_data: {
                ...data,
                transferred_to_user_id: toUserId,
                transferred_to_subscription_id: newSub.id
              }
            },
            {
              user_id: toUserId,
              subscription_id: newSub.id,
              event_type: 'TRANSFER_IN',
              event_timestamp: now,
              transaction_id: subscription.transaction_id,
              original_transaction_id: subscription.original_transaction_id,
              product_id: subscription.product_id,
              event_data: {
                ...data,
                transferred_from_user_id: fromUserId,
                transferred_from_subscription_id: subscription.id
              }
            }
          ]);
          
        console.log("已记录订阅转移事件");
        
        // 4.4 保存匿名ID与标准ID的映射关系（如果适用）
        if (data.transferred_to[0].startsWith('$RCAnonymousID:') && toUserId !== data.transferred_to[0]) {
          try {
            // 使用map_rc_id_to_user_id函数
            const { data: mappingResult, error: mappingError } = await supabase.rpc(
              'map_rc_id_to_user_id',
              {
                p_rc_id: data.transferred_to[0],
                p_user_id: toUserId
              }
            );
            
            if (mappingError) {
              console.error(`使用函数保存用户ID映射失败: ${mappingError}`);
              
              // 回退到直接插入
              const { error: insertError } = await supabase
                .from('user_identifiers')
                .upsert({
                  user_id: toUserId, 
                  anonymous_id: data.transferred_to[0],
                  provider: 'revenuecat',
                  created_at: now,
                  updated_at: now
                });
                
              if (insertError) {
                console.error(`保存用户ID映射失败: ${insertError}`);
              } else {
                console.log(`已保存匿名ID与标准ID的映射关系: ${data.transferred_to[0]} -> ${toUserId}`);
              }
            } else {
              console.log(`使用函数保存ID映射成功: ${data.transferred_to[0]} -> ${toUserId}`);
            }
          } catch (mappingError) {
            console.error(`保存用户ID映射出错: ${mappingError}`);
          }
        }
        
        // 4.5 处理transferred_from中的匿名ID映射
        if (data.transferred_from.length > 1) {
          for (const fromId of data.transferred_from) {
            if (fromId.startsWith('$RCAnonymousID:') && fromId !== fromUserId) {
              try {
                await supabase.rpc(
                  'map_rc_id_to_user_id',
                  {
                    p_rc_id: fromId,
                    p_user_id: fromUserId
                  }
                );
                console.log(`已映射转出匿名ID: ${fromId} -> ${fromUserId}`);
              } catch (error) {
                console.error(`映射转出匿名ID失败: ${error}`);
              }
            }
          }
        }
      } catch (subProcessError) {
        console.error(`处理订阅 ${subscription.id} 转移时出错:`, subProcessError);
      }
    }
    
    // 如果没有成功转移任何订阅，记录日志但继续执行
    if (subscriptionsToTransfer.length > 0 && !transferSuccess) {
      console.error(`订阅转移失败: 无法成功转移任何订阅`);
      await logError("subscription_transfer_failed", fromUserId, { toUserId, message: "无法成功转移任何订阅" });
    }
    
    // 5. 更新原用户VIP状态
    if (subscriptionsToTransfer.length > 0) {
      const { error: updateFromUserError } = await supabase
        .from('profiles')
        .update({
          is_vip: false,
          vip_updated_at: now,
          updated_at: now
        })
        .eq('id', fromUserId);
        
      if (updateFromUserError) {
        console.error("更新原用户VIP状态失败:", updateFromUserError);
      } else {
        console.log(`已将原用户 ${fromUserId} 的VIP状态设置为false`);
      }
    }
    
    // 6. 更新新用户VIP状态（如果有成功转移的订阅）
    if (transferSuccess) {
      // 准备基础的VIP更新数据
      const vipUpdateData: Record<string, any> = {
        is_vip: true,
        vip_updated_at: now,
        updated_at: now
      };
      
      // 如果有原用户VIP信息，继承这些信息
      if (fromUserProfile && fromUserProfile.is_vip) {
        if (fromUserProfile.vip_product_id) {
          vipUpdateData.vip_product_id = fromUserProfile.vip_product_id;
        }
        if (fromUserProfile.vip_expires_at) {
          vipUpdateData.vip_expires_at = fromUserProfile.vip_expires_at;
        }
        if (fromUserProfile.vip_environment) {
          vipUpdateData.vip_environment = fromUserProfile.vip_environment;
        }
      }
      
      const { error: updateToUserError } = await supabase
        .from('profiles')
        .update(vipUpdateData)
        .eq('id', toUserId);
        
      if (updateToUserError) {
        console.error("更新新用户VIP状态失败:", updateToUserError);
      } else {
        console.log(`已将新用户 ${toUserId} 的VIP状态设置为true`);
      }
    }
    
    console.log(`订阅转移处理完成: 从用户 ${fromUserId} 到用户 ${toUserId}`);
    return true;
  } catch (error) {
    console.error("处理订阅转移事件失败:", error);
    // 记录错误但不中断处理
    await logError("subscription_transfer", 
      eventData.transferred_from?.[0] || eventData.event?.transferred_from?.[0], 
      error);
    return false;
  }
}

// 处理退款撤销事件
async function handleRefundReversed(eventData: any) {
  try {
    // 确保我们始终使用正确的字段，无论数据是直接提供还是嵌套在event对象中
    const data = eventData.event ? eventData.event : eventData;
    
    console.log("处理退款撤销事件:", {
      用户ID: data.app_user_id,
      产品ID: data.product_id,
      交易ID: data.transaction_id,
      原始交易ID: data.original_transaction_id,
      金额: data.price || 0,
      币种: data.currency || 'USD'
    });
    
    // 提取用户ID
    const app_user_id = data.app_user_id;
    if (!app_user_id) {
      throw new Error("缺少app_user_id");
    }
    
    // 确保用户存在于数据库中
    let user_email = null;
    if (data.subscriber_attributes && data.subscriber_attributes["$email"]) {
      user_email = data.subscriber_attributes["$email"].value;
    } else if (data.email) {
      user_email = data.email;
    }
    
    await ensureUserExists(app_user_id, user_email);
    console.log(`已确认用户 ${app_user_id} 存在`);
    
    // 获取产品ID
    const productId = data.product_id;
    if (!productId) {
      throw new Error("缺少product_id");
    }
    
    const { transaction_id, original_transaction_id } = data;
    const subscription_identifier = original_transaction_id || transaction_id;
    const now = new Date().toISOString();
    
    if (!subscription_identifier) {
      console.error("缺少交易标识符，无法找到订阅记录");
      return;
    }
    
    // 查找已退款的订阅记录
    let subscriptionId = null;
    let subscription = null;
    
    try {
      // 检查transaction_id
      const { data: refundedSub, error: refundedSubError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('transaction_id', subscription_identifier)
        .eq('is_refunded', true)
        .maybeSingle();
        
      if (!refundedSubError && refundedSub) {
        subscriptionId = refundedSub.id;
        subscription = refundedSub;
        console.log(`找到已退款的订阅记录: ${subscriptionId}`);
        } else {
        // 检查original_transaction_id
        const { data: origTxnSub, error: origTxnSubError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('original_transaction_id', subscription_identifier)
          .eq('is_refunded', true)
          .maybeSingle();
          
        if (!origTxnSubError && origTxnSub) {
          subscriptionId = origTxnSub.id;
          subscription = origTxnSub;
          console.log(`找到已退款的订阅记录(original_transaction_id): ${subscriptionId}`);
        } else {
          // 如果找不到已退款的订阅，尝试查找任何状态的相关订阅
          const { data: anySub, error: anySubError } = await supabase
            .from('subscriptions')
            .select('*')
            .eq('transaction_id', subscription_identifier)
            .order('created_at', { ascending: false })
            .maybeSingle();
            
          if (!anySubError && anySub) {
            subscriptionId = anySub.id;
            subscription = anySub;
            console.log(`找到相关订阅记录(可能非退款状态): ${subscriptionId}`);
          }
        }
      }
    } catch (searchError) {
      console.error("搜索订阅记录时出错:", searchError);
    }
    
    if (!subscriptionId) {
      console.error(`未找到用户 ${app_user_id} 的相关订阅记录，无法处理退款撤销`);
      
      // 如果找不到相关订阅，可能需要创建新订阅
      console.log("尝试按新订阅处理");
      await handleSubscriptionActive(data);
      return;
    }
    
    // 更新订阅记录 - 恢复为活跃状态
    const updateData: any = {
      status: 'active',
      is_refunded: false,  // 撤销退款标记
      refunded_at: null,   // 清除退款时间
      refund_reversed: true, // 标记为退款已撤销
      refund_reversed_at: now, // 记录退款撤销时间
      updated_at: now,
      webhook_received_at: now
    };
    
    // 设置过期时间（如果存在）
    if (data.expiration_at_ms) {
      updateData.expires_at = new Date(data.expiration_at_ms).toISOString();
    }
    
    // 处理entitlements数据
    let entitlements = [];
    if (data.entitlement_id) {
      entitlements.push(data.entitlement_id);
    }
    if (data.entitlement_ids && Array.isArray(data.entitlement_ids)) {
      entitlements = [...new Set([...entitlements, ...data.entitlement_ids])];
    }
    
    if (entitlements.length > 0) {
      updateData.entitlements = JSON.stringify(entitlements);
    }
    
    const { error: updateError } = await supabase
      .from('subscriptions')
      .update(updateData)
      .eq('id', subscriptionId);
    
    if (updateError) {
      console.error(`更新订阅 ${subscriptionId} 状态失败:`, updateError);
    } else {
      console.log(`成功将订阅 ${subscriptionId} 状态从退款恢复为活跃`);
      
      // 记录退款撤销事件
      try {
        await supabase
          .from('subscription_events')
          .insert({
            user_id: app_user_id,
            subscription_id: subscriptionId,
            event_type: 'REFUND_REVERSED',
            event_timestamp: now,
            transaction_id: transaction_id,
            original_transaction_id: original_transaction_id,
            product_id: productId,
            event_data: data
          });
        console.log("已记录REFUND_REVERSED事件");
        
        // 记录支付恢复记录
      const paymentData = {
        user_id: app_user_id,
          subscription_id: subscriptionId,
        amount: data.price || data.price_in_purchased_currency || 0,
        currency: data.currency || 'USD',
          status: 'refund_reversed',
          payment_method: data.store || 'app_store',
        payment_provider: 'revenuecat',
          payment_provider_payment_id: `${transaction_id}_refund_reversed`,
          description: `退款撤销：${data.product_id || '未知产品'}`,
        metadata: {
            original_transaction_id: data.original_transaction_id,
            transaction_id: data.transaction_id,
            product_id: data.product_id,
            store: data.store,
            refund_reversed_date: now
        }
      };
      
      const { error: paymentError } = await supabase
        .from('payments')
        .insert(paymentData);
        
      if (paymentError) {
          console.error("记录退款撤销支付信息失败:", paymentError);
      } else {
          console.log(`成功记录用户 ${app_user_id} 的退款撤销支付信息`);
      }
      } catch (eventError) {
        console.error("记录退款撤销事件失败:", eventError);
    }
    
    // 更新用户VIP状态
    const { error: profileError } = await supabase
      .from('profiles')
      .update({
        is_vip: true,
          vip_product_id: productId,
          vip_expires_at: data.expiration_at_ms ? new Date(data.expiration_at_ms).toISOString() : null,
          vip_environment: data.environment,
        vip_updated_at: now,
        updated_at: now
      })
      .eq('id', app_user_id);
    
    if (profileError) {
      console.error("更新用户VIP状态失败:", profileError);
    } else {
        console.log(`成功恢复用户 ${app_user_id} 的VIP状态为true`);
      }
    }
  } catch (error) {
    console.error("处理退款撤销事件失败:", error);
    // 记录错误但不中断处理
    await logError("refund_reversed", eventData.app_user_id || eventData.event?.app_user_id, error);
  }
}

// 处理产品变更事件
async function handleProductChange(eventData: any) {
  try {
    // 确保我们始终使用正确的字段，无论数据是直接提供还是嵌套在event对象中
    const data = eventData.event ? eventData.event : eventData;
    
    // 提取关键字段
    const app_user_id = data.app_user_id;
    const product_id = data.product_id; // 原产品ID
    const new_product_id = data.new_product_id; // 新产品ID
    
    if (!app_user_id) {
      throw new Error("缺少app_user_id");
    }
    
    if (!product_id) {
      throw new Error("缺少product_id");
    }
    
    if (!new_product_id) {
      throw new Error("缺少new_product_id，无法处理产品变更");
    }
    
    console.log(`处理产品变更事件: 用户 ${app_user_id} 从 ${product_id} 变更为 ${new_product_id}`);
    
    // 确保用户存在
    let user_email = null;
    if (data.subscriber_attributes && data.subscriber_attributes["$email"]) {
      user_email = data.subscriber_attributes["$email"].value;
    } else if (data.email) {
      user_email = data.email;
    }
    
    await ensureUserExists(app_user_id, user_email);
    console.log(`已确认用户 ${app_user_id} 存在`);
    
    // 提取其他关键数据
    const transaction_id = data.transaction_id;
    const original_transaction_id = data.original_transaction_id;
    const store = data.store;
    const environment = data.environment || "PRODUCTION";
    
    // 获取到期时间
    const expires_at = data.expiration_at_ms ? new Date(data.expiration_at_ms).toISOString() : null;
    
    // 首先尝试使用新的数据库函数处理产品变更
    try {
      console.log("尝试使用handle_product_change数据库函数处理产品变更");
      const { data: result, error: rpcError } = await supabase.rpc('handle_product_change', {
        p_user_id: app_user_id,
        p_product_id: new_product_id,
        p_expires_at: expires_at,
        p_previous_product_id: product_id,
        p_original_transaction_id: original_transaction_id,
        p_transaction_id: transaction_id,
        p_environment: environment,
        p_store: store,
        p_event_data: data
      });
      
      if (rpcError) {
        console.error("数据库函数处理产品变更失败:", rpcError);
        console.log("将回退到传统处理流程");
      } else if (result && result.success) {
        console.log(`数据库函数处理产品变更成功: ${JSON.stringify(result)}`);
        
        // 确保VIP状态同步
        await syncUserVIPStatus(app_user_id);
        
        // 记录事件到subscription_events表
        try {
          const eventPayload = {
            user_id: app_user_id,
            subscription_id: result.subscription_id,
            event_type: 'PRODUCT_CHANGE',
            event_timestamp: new Date().toISOString(),
            transaction_id: transaction_id,
            original_transaction_id: original_transaction_id,
            product_id: new_product_id,
            event_data: data
          };
          
          const { error: eventError } = await supabase
            .from('subscription_events')
            .insert([eventPayload]);
            
          if (eventError) {
            console.error("记录订阅事件失败:", eventError);
          }
        } catch (eventError) {
          console.error("记录订阅事件时出错:", eventError);
        }
        
        return true;
      }
    } catch (functionError) {
      console.error("调用数据库函数时出错:", functionError);
      console.log("将回退到传统处理流程");
    }
    
    // 以下是传统处理流程：查找原订阅记录
    console.log(`查找用户 ${app_user_id} 的原订阅记录，产品ID: ${product_id}`);
    let oldSubscriptionId = null;
    
    try {
      // 尝试通过多种标识符查找订阅
      const identifiers = {
        'transaction_id': transaction_id,
        'original_transaction_id': original_transaction_id
      };
      
      if (transaction_id && transaction_id.length > 0) {
        identifiers['payment_provider_subscription_id'] = transaction_id;
      } else if (original_transaction_id && original_transaction_id.length > 0) {
        identifiers['payment_provider_subscription_id'] = original_transaction_id;
      }
      
      oldSubscriptionId = await findSubscriptionByIdentifiers(app_user_id, identifiers, product_id);
    } catch (error) {
      console.error("查找原订阅记录失败:", error);
    }
    
    if (!oldSubscriptionId) {
      console.log(`未找到用户 ${app_user_id} 的原订阅记录，将直接创建新订阅`);
      // 如果找不到原订阅，直接调用handleSubscriptionActive处理
      await handleSubscriptionActive({
        ...data,
        product_id: new_product_id // 使用新产品ID
      });
      
      return true;
    }
    
    console.log(`找到原订阅记录: ${oldSubscriptionId}`);
    
    // 获取当前时间
    const now = new Date().toISOString();
    
    // 创建新的订阅记录
    const newSubscriptionData = {
      user_id: app_user_id,
      product_id: new_product_id,
      status: 'active',
      previous_product_id: product_id,
      product_changed_at: now,
      environment: environment,
      store: store,
      transaction_id: transaction_id,
      original_transaction_id: original_transaction_id,
      payment_provider_subscription_id: original_transaction_id || transaction_id,
      expires_at: expires_at,
      purchase_date: now,
      webhook_received_at: now,
      event_type: 'PRODUCT_CHANGE',
      upgrade_from_subscription_id: oldSubscriptionId,
      auto_renew_status: true,
      // 处理entitlements
      entitlements: JSON.stringify(
        data.entitlement_ids && Array.isArray(data.entitlement_ids) 
          ? data.entitlement_ids 
          : (data.entitlement_id ? [data.entitlement_id] : [])
      ),
      period_type: data.period_type,
      is_family_share: data.is_family_share || false,
      country_code: data.country_code,
      presented_offering_id: data.presented_offering_id
    };
    
    // 标记原订阅为已变更状态
    try {
      const { error: updateOldSubError } = await supabase
        .from('subscriptions')
        .update({
          status: 'changed',
          updated_at: now,
          webhook_received_at: now,
          event_type: 'PRODUCT_CHANGE'
        })
        .eq('id', oldSubscriptionId);
        
      if (updateOldSubError) {
        console.error(`更新原订阅状态失败: ${updateOldSubError.message}`);
      } else {
        console.log(`已将原订阅 ${oldSubscriptionId} 标记为已变更`);
      }
    } catch (updateError) {
      console.error("更新原订阅状态时出错:", updateError);
    }
    
    // 创建新订阅记录
    try {
      const { data: newSubscription, error: createSubError } = await supabase
        .from('subscriptions')
        .insert(newSubscriptionData)
        .select('id')
        .single();
        
      if (createSubError) {
        console.error("创建新订阅记录失败:", createSubError.message);
        throw new Error(`创建新订阅失败: ${createSubError.message}`);
      }
      
      console.log(`成功创建用户 ${app_user_id} 的新订阅记录，ID: ${newSubscription.id}`);
      
      // 确保VIP状态同步
      await syncUserVIPStatus(app_user_id);
      
      // 记录事件到subscription_events表
      const eventPayload = {
        user_id: app_user_id,
        subscription_id: newSubscription.id,
        event_type: 'PRODUCT_CHANGE',
        event_timestamp: now,
        transaction_id: transaction_id,
        original_transaction_id: original_transaction_id,
        product_id: new_product_id,
        event_data: data
      };
      
      await supabase
        .from('subscription_events')
        .insert([eventPayload]);
        
      return true;
    } catch (error) {
      console.error("处理产品变更的最终步骤失败:", error);
      
      // 最后尝试回退到handleSubscriptionActive
      console.log("回退到handleSubscriptionActive处理产品变更");
      await handleSubscriptionActive({
        ...data,
        product_id: new_product_id
      });
    }
    
    return true;
  } catch (error) {
    console.error("处理产品变更事件失败:", error);
    await logError("product_change", eventData?.event?.app_user_id || eventData?.app_user_id, error);
    return false;
  }
}

// 添加同步用户VIP状态的辅助函数
async function syncUserVIPStatus(userId: string) {
  try {
    console.log(`同步用户 ${userId} 的VIP状态`);
    
    // 调用fix_user_vip_status函数
    const { data: result, error } = await supabase.rpc('fix_user_vip_status', {
      p_user_id: userId
    });
    
    if (error) {
      console.error(`同步VIP状态失败: ${error.message}`);
      
      // 回退到手动查询和更新
      const { data: activeSubscription } = await supabase
        .from('subscriptions')
        .select('expires_at, product_id, environment')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();
        
      if (activeSubscription) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('is_vip, vip_expires_at')
          .eq('id', userId)
          .single();
          
        if (profile && (!profile.is_vip || !profile.vip_expires_at || 
            new Date(profile.vip_expires_at) < new Date(activeSubscription.expires_at))) {
          await supabase
            .from('profiles')
            .update({
              is_vip: true,
              vip_product_id: activeSubscription.product_id,
              vip_expires_at: activeSubscription.expires_at,
              vip_environment: activeSubscription.environment,
              vip_updated_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('id', userId);
            
          console.log(`已手动更新用户 ${userId} 的VIP状态为true，过期时间: ${activeSubscription.expires_at}`);
        }
      } else {
        console.log(`未找到用户 ${userId} 的活跃订阅，不更新VIP状态`);
      }
    } else {
      console.log(`已同步用户 ${userId} 的VIP状态，结果: ${result}`);
    }
  } catch (error) {
    console.error(`同步VIP状态时出错: ${error}`);
  }
}

// 处理订阅延长事件
async function handleSubscriptionExtended(eventData: any) {
  try {
    // 确保我们始终使用正确的字段，无论数据是直接提供还是嵌套在event对象中
    const data = eventData.event ? eventData.event : eventData;
    
    // 提取关键字段
    const app_user_id = data.app_user_id;
    const product_id = data.product_id;
    
    if (!app_user_id) {
      throw new Error("缺少app_user_id");
    }
    
    if (!product_id) {
      throw new Error("缺少product_id");
    }
    
    console.log(`处理订阅延长事件: 用户 ${app_user_id}, 产品 ${product_id}`);
    
    // 确保用户存在
    let user_email = null;
    if (data.subscriber_attributes && data.subscriber_attributes["$email"]) {
      user_email = data.subscriber_attributes["$email"].value;
    } else if (data.email) {
      user_email = data.email;
    }
    
    await ensureUserExists(app_user_id, user_email);
    console.log(`已确认用户 ${app_user_id} 存在`);
    
    // 提取其他关键数据
    const transaction_id = data.transaction_id;
    const original_transaction_id = data.original_transaction_id;
    const subscription_identifier = original_transaction_id || transaction_id;
    const now = new Date().toISOString();
    
    // 获取新的到期时间
    const new_expiration_date = data.expiration_at_ms ? 
      new Date(data.expiration_at_ms).toISOString() : null;
    
    if (!new_expiration_date) {
      console.error("订阅延长事件缺少新的到期时间");
      throw new Error("缺少新的到期时间(expiration_at_ms)");
    }
    
    // 查找要延长的订阅记录
    console.log(`查找用户 ${app_user_id} 的订阅记录，产品ID: ${product_id}`);
    let subscriptionId = null;
    let currentSubscription = null;
    
    try {
      // 尝试通过多种标识符查找订阅
      const identifiers = {
        'payment_provider_subscription_id': subscription_identifier,
        'transaction_id': transaction_id,
        'original_transaction_id': original_transaction_id
      };
      
      // 查找活跃的订阅
      const { data: activeSubscription, error: activeSubError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', app_user_id)
        .eq('product_id', product_id)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();
        
      if (!activeSubError && activeSubscription) {
        subscriptionId = activeSubscription.id;
        currentSubscription = activeSubscription;
        console.log(`找到活跃订阅记录: ${subscriptionId}`);
      } else {
        // 如果找不到活跃订阅，尝试使用通用查找函数
        subscriptionId = await findSubscriptionByIdentifiers(app_user_id, identifiers, product_id);
        
        if (subscriptionId) {
          // 获取订阅详情
          const { data: subDetails, error: subDetailsError } = await supabase
            .from('subscriptions')
            .select('*')
            .eq('id', subscriptionId)
            .single();
            
          if (!subDetailsError && subDetails) {
            currentSubscription = subDetails;
          }
        }
      }
    } catch (error) {
      console.error("查找订阅记录失败:", error);
    }
    
    if (!subscriptionId) {
      console.log(`未找到用户 ${app_user_id} 的订阅记录，创建新订阅`);
      // 如果找不到订阅，直接调用handleSubscriptionActive创建新订阅
      await handleSubscriptionActive(data);
      return;
    }
    
    console.log(`找到要延长的订阅记录: ${subscriptionId}`);
    
    // 获取当前过期时间（作为原始过期时间）
    const original_expiration_date = currentSubscription?.expires_at || now;
    
    try {
      // 尝试使用数据库函数记录延长
      const { data: extensionResult, error: extensionError } = await supabase.rpc(
        'record_subscription_extension',
        {
          p_user_id: app_user_id,
          p_subscription_id: subscriptionId,
          p_original_expiration_date: original_expiration_date,
          p_new_expiration_date: new_expiration_date,
          p_reason: 'revenuecat_webhook',
          p_details: data
        }
      );
      
      if (extensionError) {
        console.error("调用record_subscription_extension函数失败:", extensionError);
        
        // 备用方案：直接更新订阅记录
        console.log("使用备用方案直接更新订阅记录");
        
        // 获取当前延长次数
        let extension_count = currentSubscription?.extension_count || 0;
        
        const updateData = {
          extension_count: extension_count + 1,
          last_extended_at: now,
          original_expiration_date: currentSubscription?.original_expiration_date || original_expiration_date,
          expires_at: new_expiration_date,
          extension_reason: 'revenuecat_webhook',
          status: 'active', // 确保状态为活跃
          updated_at: now,
          webhook_received_at: now
        };
        
        const { error: updateError } = await supabase
          .from('subscriptions')
          .update(updateData)
          .eq('id', subscriptionId);
          
        if (updateError) {
          console.error(`更新订阅记录 ${subscriptionId} 失败:`, updateError);
        } else {
          console.log(`成功延长订阅 ${subscriptionId} 的有效期至 ${new_expiration_date}`);
          
          // 记录订阅延长历史
          await recordSubscriptionExtensionHistory(
            app_user_id,
            subscriptionId,
            original_expiration_date,
            new_expiration_date,
            'revenuecat_webhook',
            data
          );
          
          // 记录延长事件
          try {
            await supabase
              .from('subscription_events')
              .insert({
                user_id: app_user_id,
                subscription_id: subscriptionId,
                event_type: 'SUBSCRIPTION_EXTENDED',
                event_timestamp: now,
                transaction_id: transaction_id,
                original_transaction_id: original_transaction_id,
                product_id: product_id,
                event_data: {
                  ...data,
                  original_expiration_date: original_expiration_date,
                  new_expiration_date: new_expiration_date,
                  extension_count: extension_count + 1
                }
              });
            console.log("已记录SUBSCRIPTION_EXTENDED事件");
          } catch (eventError) {
            console.error("记录延长事件失败:", eventError);
          }
          
          // 手动更新用户VIP状态
          const { error: profileError } = await supabase
            .from('profiles')
            .update({
              is_vip: true,
              vip_expires_at: new_expiration_date,
              vip_updated_at: now,
              updated_at: now
            })
            .eq('id', app_user_id);
          
          if (profileError) {
            console.error("更新用户VIP状态失败:", profileError);
          } else {
            console.log(`成功更新用户 ${app_user_id} 的VIP过期时间至 ${new_expiration_date}`);
          }
        }
      } else {
        console.log(`成功使用数据库函数延长订阅 ${subscriptionId} 的有效期`);
        
        // 记录订阅延长历史
        await recordSubscriptionExtensionHistory(
          app_user_id,
          subscriptionId,
          original_expiration_date,
          new_expiration_date,
          'revenuecat_webhook',
          data
        );
      }
    } catch (error) {
      console.error("处理订阅延长出错:", error);
    }
    
    console.log(`订阅延长事件处理完成: 用户 ${app_user_id}, 产品 ${product_id}`);
  } catch (error) {
    console.error("处理订阅延长事件失败:", error);
    // 记录错误但不中断处理
    await logError("subscription_extended", eventData.app_user_id || eventData.event?.app_user_id, error);
  }
}

// 记录订阅延长历史
async function recordSubscriptionExtensionHistory(
  userId: string, 
  subscriptionId: string, 
  originalExpirationDate: string, 
  newExpirationDate: string,
  reason: string = 'webhook',
  details: any = null
) {
  try {
    console.log(`记录订阅延长历史: 用户=${userId}, 从${originalExpirationDate}延长到${newExpirationDate}`);
    
    // 计算延长的时间差（毫秒）
    const originalDate = new Date(originalExpirationDate);
    const newDate = new Date(newExpirationDate);
    const extensionMs = newDate.getTime() - originalDate.getTime();
    
    // 转换为天数（四舍五入到最接近的整数）
    const extensionDays = Math.round(extensionMs / (1000 * 60 * 60 * 24));
    
    // 准备记录数据
    const extensionData = {
      user_id: userId,
      subscription_id: subscriptionId,
      original_expiration_date: originalExpirationDate,
      new_expiration_date: newExpirationDate,
      extension_days: extensionDays,
      reason: reason,
      details: details
    };
    
    // 插入记录到订阅延长历史表
    // 注意：如果数据库中没有这个表，需要在迁移文件中创建
    try {
      const { error } = await supabase
        .from('subscription_extension_history')
        .insert(extensionData);
        
      if (error) {
        // 如果表不存在，记录到订阅事件表
        console.log("subscription_extension_history表可能不存在，记录到subscription_events表");
        await supabase
          .from('subscription_events')
          .insert({
            user_id: userId,
            subscription_id: subscriptionId,
            event_type: 'EXTENSION_HISTORY',
            event_timestamp: new Date().toISOString(),
            product_id: null,
            event_data: extensionData
          });
      } else {
        console.log(`成功记录订阅延长历史`);
      }
    } catch (insertError) {
      console.error("记录订阅延长历史失败:", insertError);
    }
    
    return true;
  } catch (error) {
    console.error("记录订阅延长历史时出错:", error);
    return false;
  }
}

// 处理试用转付费事件
async function handleTrialConversion(eventData: any) {
  try {
    // 确保我们始终使用正确的字段，无论数据是直接提供还是嵌套在event对象中
    const data = eventData.event ? eventData.event : eventData;
    
    // 提取关键字段
    const app_user_id = data.app_user_id;
    const product_id = data.product_id;
    const is_trial_conversion = data.is_trial_conversion || false;
    
    if (!app_user_id) {
      throw new Error("缺少app_user_id");
    }
    
    if (!product_id) {
      throw new Error("缺少product_id");
    }
    
    if (!is_trial_conversion) {
      console.log("此事件不是试用转付费事件，交由普通订阅处理");
      return await handleSubscriptionActive(data);
    }
    
    console.log(`处理试用转付费事件: 用户 ${app_user_id}, 产品 ${product_id}`);

    // 检查用户是否存在，如果不存在则记录孤立事件
    const userCheck = await handleUserExistenceForEvent(
      app_user_id,
      'TRIAL_CONVERSION',
      data,
      true // 跳过用户创建
    );

    if (!userCheck.shouldContinue) {
      console.log(`用户 ${app_user_id} 不存在，已记录孤立事件，停止处理`);
      return false;
    }

    console.log(`已确认用户 ${app_user_id} 存在`);
    
    // 获取其他关键信息
    const transaction_id = data.transaction_id;
    const original_transaction_id = data.original_transaction_id;
    const subscription_identifier = original_transaction_id || transaction_id;
    const now = new Date().toISOString();
    
    // 获取新的到期时间
    const new_expiration_date = data.expiration_at_ms ? 
      new Date(data.expiration_at_ms).toISOString() : null;
      
    // 获取购买时间（转付费时间）
    const conversion_date = data.purchased_at_ms ? 
      new Date(data.purchased_at_ms).toISOString() : now;
    
    // 处理entitlements数据
    let entitlements = [];
    if (data.entitlement_id) {
      entitlements.push(data.entitlement_id);
    }
    if (data.entitlement_ids && Array.isArray(data.entitlement_ids)) {
      entitlements = [...new Set([...entitlements, ...data.entitlement_ids])];
    }
    
    // 查找该用户的试用订阅
    console.log(`查找用户 ${app_user_id} 的试用订阅记录`);
    let trialSubscriptionId = null;
    let trialSubscription = null;
    
    // 查找最近的试用订阅
    const { data: trialSub, error: trialSubError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', app_user_id)
      .eq('is_trial', true)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();
      
    if (trialSubError) {
      console.error("查询试用订阅记录失败:", trialSubError);
    } else if (trialSub) {
      trialSubscriptionId = trialSub.id;
      trialSubscription = trialSub;
      console.log(`找到活跃的试用订阅记录: ${trialSubscriptionId}`);
    } else {
      // 如果找不到活跃的试用订阅，尝试查找可能的过期试用订阅
      const { data: expiredTrialSub, error: expiredTrialError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', app_user_id)
        .eq('is_trial', true)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();
        
      if (!expiredTrialError && expiredTrialSub) {
        trialSubscriptionId = expiredTrialSub.id;
        trialSubscription = expiredTrialSub;
        console.log(`找到过期的试用订阅记录: ${trialSubscriptionId}`);
      }
    }
    
    if (!trialSubscriptionId) {
      console.log(`未找到用户 ${app_user_id} 的试用订阅记录，作为普通订阅处理`);
      return await handleSubscriptionActive(data);
    }
    
    // 更新试用订阅状态为已转换
    const { error: updateTrialError } = await supabase
      .from('subscriptions')
      .update({
        trial_converted: true,
        trial_converted_at: conversion_date,
        status: 'converted', // 标记为已转换
        updated_at: now
      })
      .eq('id', trialSubscriptionId);
      
    if (updateTrialError) {
      console.error(`更新试用订阅状态失败: ${updateTrialError}`);
    } else {
      console.log(`已将试用订阅 ${trialSubscriptionId} 标记为已转换`);
    }
    
    // 创建新的付费订阅记录
    const subscriptionData: any = {
      user_id: app_user_id,
      product_id: product_id,
      status: 'active',
      period_type: data.period_type,
      entitlements: JSON.stringify(entitlements),
      environment: data.environment || "PRODUCTION",
      store: data.store,
      transaction_id: transaction_id,
      original_transaction_id: original_transaction_id,
      payment_provider: 'revenuecat',
      payment_provider_subscription_id: subscription_identifier,
      purchase_date: conversion_date,
      expires_at: new_expiration_date,
      is_trial_conversion: true,
      converted_from_trial_id: trialSubscriptionId,
      webhook_received_at: now,
      event_type: 'TRIAL_CONVERSION',
      created_at: now,
      updated_at: now
    };
    
    if (data.is_family_share !== undefined) {
      subscriptionData.is_family_share = data.is_family_share;
    }
    
    if (data.country_code) {
      subscriptionData.country_code = data.country_code;
    }
    
    if (data.presented_offering_id) {
      subscriptionData.presented_offering_id = data.presented_offering_id;
    }
    
    console.log("创建新的付费订阅记录:", subscriptionData);
    
    const { data: newSubscription, error: createSubError } = await supabase
      .from('subscriptions')
      .insert(subscriptionData)
      .select('id')
      .single();
      
    if (createSubError) {
      console.error("创建付费订阅记录失败:", createSubError);
      
      // 如果创建失败，尝试回退到普通订阅处理
      console.log("回退到普通订阅处理");
      return await handleSubscriptionActive(data);
    }
    
    console.log(`成功创建付费订阅记录，ID: ${newSubscription.id}`);
    
    // 记录试用转付费事件
    await supabase
      .from('subscription_events')
      .insert({
        user_id: app_user_id,
        subscription_id: newSubscription.id,
        event_type: 'TRIAL_CONVERSION',
        event_timestamp: now,
        transaction_id: transaction_id,
        original_transaction_id: original_transaction_id,
        product_id: product_id,
        event_data: {
          ...data,
          converted_from_trial_id: trialSubscriptionId
        }
      });
      
    console.log("已记录试用转付费事件");
    
    // 记录支付
    await createPaymentRecord(app_user_id, newSubscription.id, {
      ...data,
      is_trial_conversion: true,
      converted_from_trial_id: trialSubscriptionId
    });
    
    // 更新用户VIP信息
    const { error: profileError } = await supabase
      .from('profiles')
      .update({
        is_vip: true,
        vip_product_id: product_id,
        vip_expires_at: new_expiration_date,
        vip_is_trial: false, // 不再是试用了
        vip_environment: data.environment,
        vip_updated_at: now,
        updated_at: now
      })
      .eq('id', app_user_id);
      
    if (profileError) {
      console.error("更新用户VIP状态失败:", profileError);
    } else {
      console.log(`成功更新用户 ${app_user_id} 的VIP状态`);
    }
    
    console.log(`试用转付费事件处理完成: 用户 ${app_user_id}, 产品 ${product_id}`);
    return true;
  } catch (error) {
    console.error("处理试用转付费事件失败:", error);
    // 记录错误但不中断处理
    await logError("trial_conversion", eventData.app_user_id || eventData.event?.app_user_id, error);
    // 回退到普通订阅处理
    return await handleSubscriptionActive(eventData);
  }
}

// 处理发票开具事件
async function handleInvoiceIssuance(eventData: any) {
  try {
    // 确保我们始终使用正确的字段，无论数据是直接提供还是嵌套在event对象中
    const data = eventData.event ? eventData.event : eventData;
    
    // 提取关键字段
    const app_user_id = data.app_user_id;
    const product_id = data.product_id;
    
    if (!app_user_id) {
      throw new Error("缺少app_user_id");
    }
    
    if (!product_id) {
      throw new Error("缺少product_id");
    }
    
    console.log(`处理发票开具事件: 用户 ${app_user_id}, 产品 ${product_id}`);
    
    // 确保用户存在
    let user_email = null;
    if (data.subscriber_attributes && data.subscriber_attributes["$email"]) {
      user_email = data.subscriber_attributes["$email"].value;
    } else if (data.email) {
      user_email = data.email;
    }
    
    await ensureUserExists(app_user_id, user_email);
    console.log(`已确认用户 ${app_user_id} 存在`);
    
    // 提取其他关键数据
    const invoice_id = data.id; // 使用事件ID作为发票ID
    const store = data.store || 'RC_BILLING';
    const environment = data.environment || "PRODUCTION";
    const country_code = data.country_code;
    const amount = data.price_in_purchased_currency || 0;
    const currency = data.currency || 'USD';
    const now = new Date().toISOString();
    const issued_at = data.event_timestamp_ms ? 
      new Date(data.event_timestamp_ms).toISOString() : now;
    
    console.log(`发票详情: ID=${invoice_id}, 金额=${amount} ${currency}, 商店=${store}`);
    
    // 使用数据库函数记录发票
    try {
      const { data: invoiceId, error: invoiceError } = await supabase.rpc('record_invoice', {
        p_user_id: app_user_id,
        p_product_id: product_id,
        p_invoice_id: invoice_id,
        p_amount: amount,
        p_currency: currency,
        p_issued_at: issued_at,
        p_store: store,
        p_environment: environment,
        p_country_code: country_code,
        p_metadata: data
      });
      
      if (invoiceError) {
        console.error("调用record_invoice函数失败:", invoiceError);
        
        // 备用方案：直接插入发票记录
        console.log("使用备用方案直接创建发票记录");
        
        const invoiceData = {
          user_id: app_user_id,
          product_id: product_id,
          invoice_id: invoice_id,
          status: 'issued',
          amount: amount,
          currency: currency,
          issued_at: issued_at,
          environment: environment,
          store: store,
          country_code: country_code,
          metadata: data,
          created_at: now,
          updated_at: now
        };
        
        const { data: newInvoice, error: createError } = await supabase
          .from('invoices')
          .insert(invoiceData)
          .select('id')
          .single();
        
        if (createError) {
          console.error("直接创建发票记录失败:", createError);
        } else {
          console.log(`成功创建用户 ${app_user_id} 的发票记录，ID: ${newInvoice.id}`);
        }
      } else {
        console.log(`成功记录用户 ${app_user_id} 的发票，ID: ${invoiceId}`);
      }
      
      // 记录发票事件
      await supabase
        .from('subscription_events')
        .insert({
          user_id: app_user_id,
          event_type: 'INVOICE_ISSUANCE',
          event_timestamp: issued_at,
          product_id: product_id,
          event_data: data
        });
      
      console.log("已记录发票开具事件");
      
      return true;
    } catch (dbError) {
      console.error("记录发票信息到数据库失败:", dbError);
      return false;
    }
  } catch (error) {
    console.error("处理发票开具事件失败:", error);
    // 记录错误但不中断处理
    await logError("invoice_issuance", eventData.app_user_id || eventData.event?.app_user_id, error);
    return false;
  }
}

// 处理临时授权事件
async function handleTemporaryEntitlementGrant(eventData: any) {
  try {
    // 确保我们始终使用正确的字段，无论数据是直接提供还是嵌套在event对象中
    const data = eventData.event ? eventData.event : eventData;
    
    // 提取关键字段
    const app_user_id = data.app_user_id;
    const grant_id = data.id;
    const transaction_id = data.transaction_id;
    
    if (!app_user_id) {
      throw new Error("缺少app_user_id");
    }
    
    if (!grant_id) {
      throw new Error("缺少grant_id");
    }
    
    console.log(`处理临时授权事件: 用户 ${app_user_id}, 授权ID ${grant_id}, 交易ID ${transaction_id || '未提供'}`);

    // 检查用户是否存在，如果不存在则记录孤立事件
    const userCheck = await handleUserExistenceForEvent(
      app_user_id,
      'TEMPORARY_ENTITLEMENT_GRANT',
      data,
      true // 跳过用户创建
    );

    if (!userCheck.shouldContinue) {
      console.log(`用户 ${app_user_id} 不存在，已记录孤立事件，停止处理`);
      return;
    }
    
    // 提取授权信息
    const entitlement_ids = data.entitlement_ids || [];
    const entitlement_id = entitlement_ids.length > 0 ? entitlement_ids[0] : (data.entitlement_id || "unknown");
    
    // 计算授权时间和过期时间
    const granted_at = new Date(data.purchased_at_ms || data.event_timestamp_ms || Date.now());
    
    // 按照RevenueCat文档：临时授权最多24小时有效期
    let expires_at;
    if (data.expiration_at_ms) {
      expires_at = new Date(data.expiration_at_ms);
    } else {
      // 默认24小时有效期（而不是7天）
      expires_at = new Date(granted_at);
      expires_at.setHours(expires_at.getHours() + 24);
    }
    
    // 创建元数据，记录关键信息
    const metadata = {
      store: data.store,
      environment: data.environment || 'PRODUCTION',
      source: "revenuecat_store_outage", // 明确标记来源是商店服务中断
      transaction_id: transaction_id, // 记录transaction_id以便与后续事件匹配
      product_id: data.product_id,
      is_temporary: true,
      needs_validation: true, // 标记这个授权需要后续验证
      granted_at_ms: data.purchased_at_ms || data.event_timestamp_ms,
      expires_at_ms: data.expiration_at_ms,
      original_event: JSON.stringify(data)
    };
    
    // 调用数据库函数处理临时授权
    const result = await supabase.rpc(
      'handle_temporary_entitlement',
      {
        p_user_id: app_user_id, // 使用字符串ID而不是user.id对象
        p_entitlement_id: entitlement_id,
        p_grant_id: grant_id,
        p_granted_at: granted_at.toISOString(),
        p_expires_at: expires_at.toISOString(),
        p_reason: "store_service_outage", // 明确记录原因
        p_metadata: metadata
      }
    );
    
    if (result.error) {
      throw new Error(`处理临时授权失败: ${result.error.message}`);
    }
    
    console.log(`成功处理临时授权: ${grant_id}, 有效期至 ${expires_at.toISOString()}`);
    
    // 记录事件
    try {
      await supabase
        .from('subscription_events')
        .insert({
          user_id: app_user_id,
          event_type: 'TEMPORARY_ENTITLEMENT_GRANT',
          event_timestamp: granted_at.toISOString(),
          transaction_id: transaction_id,
          product_id: data.product_id,
          event_data: data
        });
      
      console.log("已记录临时授权事件");
    } catch (eventError: any) {
      console.error(`记录临时授权事件失败: ${eventError.message}`);
    }
    
    // 根据entitlement_id更新用户的VIP权限
    // 由于这是临时授权（最多24小时），我们只在用户当前不是VIP时才授予临时VIP
    if (entitlement_id && (
      entitlement_id.toLowerCase().includes('premium') || 
      entitlement_id.toLowerCase().includes('vip') ||
      entitlement_id.toLowerCase().includes('pro')
    )) {
      // 查询用户当前VIP状态
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('is_vip')
        .eq('id', app_user_id)
        .single();
      
      if (!profileError && profileData && !profileData.is_vip) {
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            is_vip: true,
            vip_source: 'temporary_grant',
            vip_expires_at: expires_at.toISOString(),
            vip_updated_at: granted_at.toISOString()
          })
          .eq('id', app_user_id);
        
        if (updateError) {
          console.error(`更新用户VIP状态失败: ${updateError.message}`);
        } else {
          console.log(`已为用户 ${app_user_id} 授予临时VIP权限，有效期至 ${expires_at.toISOString()}`);
        }
      } else {
        console.log(`用户 ${app_user_id} 已经是VIP，无需授予临时权限`);
      }
    }
    
    return {
      status: "success",
      message: `成功处理临时授权: ${grant_id}`,
      details: {
        grant_id: grant_id,
        user_id: app_user_id,
        entitlement_id: entitlement_id,
        expires_at: expires_at.toISOString()
      }
    };
  } catch (error: any) {
    console.error(`处理临时授权失败: ${error.message}`);
    throw error;
  }
}

// 处理退款事件
async function handleRefund(eventData: any) {
  try {
    // 向后兼容不同的数据结构
    const data = eventData.event ? eventData.event : eventData;
    
    // 确保我们使用正确的App User ID
    const appUserId = data.app_user_id;
    
    if (!appUserId) {
      throw new Error("缺少app_user_id");
    }
    
    console.log(`处理退款事件: ${appUserId}`);

    // 提取原始交易ID
    const originalTransactionId = data.original_transaction_id || data.transaction_id;

    if (!originalTransactionId) {
      console.warn(`退款事件缺少transaction_id: ${JSON.stringify(data)}`);
    }

    // 检查用户是否存在，如果不存在则记录孤立事件
    const userCheck = await handleUserExistenceForEvent(
      appUserId,
      'BILLING_ISSUE',
      data,
      true // 跳过用户创建
    );

    if (!userCheck.shouldContinue) {
      console.log(`用户 ${appUserId} 不存在，已记录孤立事件，停止处理`);
      return;
    }
    
    // 查找用户的所有订阅
    const { data: subscriptions, error: subscriptionsError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', appUserId);
    
    if (subscriptionsError) {
      throw new Error(`查找用户订阅失败: ${subscriptionsError.message}`);
    }
    
    if (!subscriptions || subscriptions.length === 0) {
      console.warn(`用户没有订阅记录: ${appUserId}`);
      // 仍然继续处理，因为可能是数据导入错误或其他异常情况
    }
    
    // 查找匹配的订阅，优先匹配transaction_id
    let matchedSubscription = null;
    if (originalTransactionId) {
      matchedSubscription = subscriptions?.find((sub: any) => 
        sub.original_transaction_id === originalTransactionId || 
        sub.transaction_id === originalTransactionId
      );
    }
    
    // 如果找不到特定订阅，则使用最近的活跃订阅
    if (!matchedSubscription && subscriptions && subscriptions.length > 0) {
      const activeSubscriptions = subscriptions.filter((sub: any) => !sub.cancelled && !sub.refunded);
      if (activeSubscriptions.length > 0) {
        // 按过期时间倒序排列，选择最新的
        matchedSubscription = activeSubscriptions.sort((a: any, b: any) => {
          const dateA = a.expires_at ? new Date(a.expires_at) : new Date(0);
          const dateB = b.expires_at ? new Date(b.expires_at) : new Date(0);
          return dateB.getTime() - dateA.getTime();
        })[0];
      } else {
        // 没有活跃订阅时，选择最近的非活跃订阅
        matchedSubscription = subscriptions.sort((a: any, b: any) => {
          const dateA = a.cancelled_at || a.expires_at || new Date(0);
          const dateB = b.cancelled_at || b.expires_at || new Date(0);
          return new Date(dateB).getTime() - new Date(dateA).getTime();
        })[0];
      }
    }
    
    // 处理退款操作
    if (matchedSubscription) {
      // 标记订阅为已退款
      const { error: updateError } = await supabase
        .from('subscriptions')
        .update({
          refunded: true,
          refunded_at: new Date().toISOString(),
          refund_reason: data.cancel_reason || data.cancellation_reason || 'user_requested',
          active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', matchedSubscription.id);
      
      if (updateError) {
        throw new Error(`更新订阅退款状态失败: ${updateError.message}`);
      }
      
      console.log(`成功标记订阅为已退款: ${matchedSubscription.id}`);
      
      // 记录退款记录
      await createRefundRecord(appUserId, matchedSubscription.id, eventData);
      
      // 如果用户没有其他活跃订阅，则移除VIP状态
      const { data: activeSubCount, error: countError } = await supabase
        .from('subscriptions')
        .select('id', { count: 'exact' })
        .eq('user_id', appUserId)
        .eq('active', true)
        .eq('refunded', false);
      
      if (!countError && (!activeSubCount || (typeof activeSubCount === 'number' && activeSubCount === 0) || (Array.isArray(activeSubCount) && activeSubCount.length === 0))) {
        const { error: profileUpdateError } = await supabase
          .from('profiles')
          .update({
            is_vip: false,
            vip_source: null,
            vip_updated_at: new Date().toISOString()
          })
          .eq('id', appUserId);
        
        if (profileUpdateError) {
          console.error(`移除用户VIP状态失败: ${profileUpdateError.message}`);
        } else {
          console.log(`已移除用户VIP状态: ${appUserId}`);
        }
      }
    } else {
      console.warn(`未找到匹配的订阅，仍然记录退款信息: ${appUserId}`);
      // 即使找不到订阅，也记录退款信息
      await createRefundRecord(appUserId, null, eventData);
    }
    
    return {
      status: "success",
      message: `成功处理退款事件: ${appUserId}`
    };
  } catch (error: any) {
    console.error(`处理退款事件失败: ${error.message}`);
    throw error;
  }
}

// 创建退款记录
async function createRefundRecord(appUserId: string, subscriptionId: string | null, eventData: any) {
  try {
    const data = eventData.event ? eventData.event : eventData;
    
    // 获取用户ID
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('id')
      .eq('app_user_id', appUserId)
      .single();
    
    if (!userProfile) {
      throw new Error(`找不到用户: ${appUserId}`);
    }
    
    // 提取金额
    let amount = 0;
    if (data.price && typeof data.price === 'number') {
      amount = Math.abs(data.price); // 确保是正数
    } else if (data.price_in_purchased_currency && typeof data.price_in_purchased_currency === 'number') {
      amount = Math.abs(data.price_in_purchased_currency);
    }
    
    // 创建退款记录
    const { error } = await supabase
      .from('refunds')
      .insert({
        user_id: userProfile.id,
        subscription_id: subscriptionId,
        transaction_id: data.transaction_id || data.original_transaction_id,
        amount: amount,
        currency: data.currency || 'USD',
        refund_reason: data.cancel_reason || data.cancellation_reason || 'user_requested',
        store: data.store,
        environment: data.environment || 'PRODUCTION',
        refunded_at: new Date(data.event_timestamp_ms || Date.now()).toISOString(),
        metadata: {
          original_event: JSON.stringify(data)
        }
      });
    
    if (error) {
      throw new Error(`创建退款记录失败: ${error.message}`);
    }
    
    console.log('成功创建退款记录');
  } catch (error: any) {
    console.error(`创建退款记录失败: ${error.message}`);
    // 非致命错误，不抛出，继续处理
  }
}

// 处理试用开始事件
async function handleTrialStarted(eventData: any) {
  try {
    // 向后兼容不同的数据结构
    const data = eventData.event ? eventData.event : eventData;
    
    // 提取关键字段
    const appUserId = data.app_user_id;
    const productId = data.product_id;
    const transactionId = data.transaction_id || data.original_transaction_id;
    
    if (!appUserId) {
      throw new Error("缺少app_user_id");
    }
    
    if (!productId) {
      throw new Error("缺少product_id");
    }
    
    console.log(`处理试用开始事件: 用户 ${appUserId}, 产品 ${productId}`);

    // 检查用户是否存在，如果不存在则记录孤立事件
    const userCheck = await handleUserExistenceForEvent(
      appUserId,
      'TRIAL_STARTED',
      data,
      true // 跳过用户创建
    );

    if (!userCheck.shouldContinue) {
      console.log(`用户 ${appUserId} 不存在，已记录孤立事件，停止处理`);
      return {
        status: "skipped",
        message: `用户不存在，已记录孤立事件: ${appUserId}`
      };
    }

    // 使用检查返回的用户ID
    const userId = userCheck.userId!;
    
    // 检查是否已存在相同的试用订阅
    if (transactionId) {
      const { data: existingSubscription, error: checkError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('transaction_id', transactionId)
        .single();
      
      if (!checkError && existingSubscription) {
        console.log(`已存在相同transaction_id的订阅记录，跳过创建: ${transactionId}`);
        return {
          status: "success",
          message: `已存在的试用订阅: ${transactionId}`
        };
      }
    }
    
    // 提取时间信息
    const purchasedAt = data.purchased_at_ms ? new Date(data.purchased_at_ms) : new Date();
    const expiresAt = data.expiration_at_ms ? new Date(data.expiration_at_ms) : null;
    
    // 提取权益信息
    const entitlementIds = data.entitlement_ids || [];
    const entitlementId = entitlementIds.length > 0 ? entitlementIds[0] : (data.entitlement_id || null);
    
    // 创建新的试用订阅记录
    const { data: newSubscription, error: createError } = await supabase
      .from('subscriptions')
      .insert({
        user_id: userId, // 使用ensureUserExists返回的用户ID
        app_user_id: appUserId,
        product_id: productId,
        transaction_id: transactionId,
        original_transaction_id: data.original_transaction_id,
        active: true,
        cancelled: false,
        period_type: 'TRIAL',
        is_trial: true,
        trial_started_at: purchasedAt.toISOString(),
        trial_expires_at: expiresAt ? expiresAt.toISOString() : null,
        entitlement_id: entitlementId,
        store: data.store,
        country_code: data.country_code,
        purchased_at: purchasedAt.toISOString(),
        expires_at: expiresAt ? expiresAt.toISOString() : null,
        environment: data.environment || 'PRODUCTION',
        is_family_share: data.is_family_share || false,
        price: data.price || 0,
        price_in_purchased_currency: data.price_in_purchased_currency || 0,
        currency: data.currency,
        metadata: {
          original_event: JSON.stringify(data)
        }
      })
      .select('*')
      .single();
    
    if (createError) {
      throw new Error(`创建试用订阅记录失败: ${createError.message}`);
    }
    
    console.log(`成功创建试用订阅记录: ${newSubscription?.id}`);
    
    // 基于产品和权益决定是否需要为用户添加VIP状态
    // 仅在entitlement_id包含premium等关键词，或产品ID包含关键词时添加VIP
    const shouldAddVip = 
      (entitlementId && /premium|vip|pro/i.test(entitlementId)) ||
      (productId && /premium|vip|pro/i.test(productId));
    
    if (shouldAddVip) {
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          is_vip: true,
          is_trial_vip: true,
          vip_source: 'trial',
          vip_updated_at: new Date().toISOString()
        })
        .eq('id', userId); // 使用ensureUserExists返回的用户ID
      
      if (updateError) {
        console.error(`更新用户VIP状态失败: ${updateError.message}`);
      } else {
        console.log(`已添加用户试用VIP状态: ${appUserId}`);
      }
    }
    
    return {
      status: "success",
      message: `成功处理试用开始事件: ${appUserId}`
    };
  } catch (error: any) {
    console.error(`处理试用开始事件失败: ${error.message}`);
    throw error;
  }
} 