# RevenueCat订阅问题排查指南

## 排查步骤

### 1. 检查RevenueCat Webhook配置
- 确保在RevenueCat后台正确配置了Webhook URL:
  - URL应为: `https://[YOUR_SUPABASE_ID].functions.supabase.co/revenuecat-webhook`
  - 确认已勾选所有相关事件类型（尤其是INITIAL_PURCHASE和RENEWAL）
  - 验证授权头设置是否与环境变量`REVENUECAT_WEBHOOK_SECRET`匹配

### 2. 检查Edge Function日志
- 在Supabase控制台 → Functions → revenuecat-webhook → Logs中查看函数执行日志
- 注意是否有错误信息，特别是关于：
  - 授权验证失败
  - 用户ID不存在
  - 数据库操作错误

### 3. 检查数据库表
- 检查`profiles`表是否正确更新了`is_vip`字段
- 检查`subscriptions`表是否存在对应的订阅记录
- 确认表结构与迁移文件定义一致

### 4. 检查客户端状态
- 在应用中使用开发者菜单查看RevenueCat订阅状态
- 检查Redux中的VIP状态是否正确
- 尝试手动恢复购买或刷新订阅状态

## 常见问题与解决方案

### 1. Webhook接收到但未处理
可能原因：
- 授权验证失败
- 事件格式解析错误
- 用户ID不匹配

解决方案：
- 检查环境变量配置
- 查看Edge Function日志以获得详细错误信息
- 更新edge function代码以增加更多日志记录

### 2. 数据库更新失败
可能原因：
- 表结构不匹配
- SQL语句错误
- RLS策略阻止更新

解决方案：
- 比较数据库表结构与迁移文件
- 检查SQL日志
- 暂时禁用RLS进行测试

### 3. 客户端状态不同步
可能原因：
- RevenueCat SDK与Supabase状态不同步
- Redux状态更新失败
- 缓存问题

解决方案：
- 实现服务器端VIP状态检查
- 添加额外的同步逻辑
- 清除缓存并重启应用

## 测试购买流程
1. 在沙盒环境进行测试购买
2. 监控RevenueCat后台事件日志
3. 检查Supabase Edge Function日志
4. 验证数据库中用户的VIP状态
5. 确认Redux中的状态更新 