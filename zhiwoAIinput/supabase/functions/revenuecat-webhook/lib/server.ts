/**
 * 直接提供serve函数的本地实现
 * 避免依赖外部网络资源
 */

/**
 * 处理HTTP请求的函数类型
 */
export type Handler = (request: Request) => Response | Promise<Response>;

/**
 * 启动HTTP服务的函数
 * @param handler 处理HTTP请求的函数
 */
export function serve(handler: Handler): void {
  // 在Deno环境中使用Deno.serve
  // @ts-ignore
  if (typeof Deno !== "undefined" && Deno.serve) {
    // @ts-ignore
    Deno.serve(handler);
  } else {
    console.error("Deno.serve not available, server cannot start");
  }
} 