# RevenueCat Webhook事件结构
RevenueCat Webhook事件通常包含以下重要字段:

## 新的基本结构(API v2)
```json
{
  "api_version": "2.0",
  "event": {
    "type": "INITIAL_PURCHASE|RENEWAL|PRODUCT_CHANGE|CANCELLATION|EXPIRATION|BILLING_ISSUE|UNCANCELLATION",
    "app_user_id": "用户ID",
    "original_app_user_id": "原始用户ID",
    "aliases": ["别名1", "别名2"],
    "original_transaction_id": "原始交易ID",
    "transaction_id": "当前交易ID",
    "product_id": "产品ID",
    "store": "app_store|play_store|amazon|...",
    "environment": "PRODUCTION|SANDBOX",
    "purchased_at_ms": 1656556800000,
    "expiration_at_ms": 1659148800000
  }
}
```

## 确保在处理时考虑:
1. `app_user_id` - 需确保此ID在数据库的profiles表中存在
2. `transaction_id` 和 `original_transaction_id` - 用于查找和更新订阅记录
3. 时间处理 - 确保正确处理UTC时间和本地时间转换
4. 错误处理 - 记录详细的错误信息
5. 调试 - 增加详细的日志记录，以追踪webhook处理流程 