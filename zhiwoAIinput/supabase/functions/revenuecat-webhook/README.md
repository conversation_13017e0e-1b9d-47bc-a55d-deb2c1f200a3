# RevenueCat Webhook 处理函数

本Edge Function用于接收和处理RevenueCat的webhook通知，确保应用内订阅状态正确同步到Supabase数据库。

## 功能概述

该函数接收来自RevenueCat的webhook通知，处理各种订阅相关的事件，并更新相应的数据库记录。支持的事件类型包括：

- 首次购买 (INITIAL_PURCHASE)
- 续订 (RENEWAL)
- 产品更改 (PRODUCT_CHANGE)
- 取消订阅 (CANCELLATION)
- 订阅到期 (EXPIRATION)
- 账单问题 (BILLING_ISSUE)
- 取消恢复 (UNCANCELLATION)
- 订阅暂停 (SUBSCRIPTION_PAUSED)

## 实现要点

- 接收并验证来自RevenueCat的webhook事件
- 解析事件数据并识别事件类型
- 根据事件类型执行相应的数据库操作
- 更新用户VIP状态、订阅记录和支付信息
- 记录账单问题以便后续处理

## 部署指南

### 前提条件

1. 已有Supabase项目
2. 已设置好相关数据库表
   - `profiles` - 包含用户资料和VIP状态
   - `subscriptions` - 存储订阅信息
   - `subscription_plans` - 存储订阅计划
   - `payments` - 记录支付信息
   - `billing_issues` - 记录账单问题

### 数据库修复和准备

在部署webhook函数前，需要执行`db_fixes.sql`脚本，该脚本会：
1. 添加`vip_since`列到`profiles`表
2. 为已有VIP用户设置初始值
3. 添加示例数据到`subscription_plans`表（如果为空）

使用Supabase SQL编辑器执行以下命令：
```sql
-- 添加vip_since列到profiles表
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS vip_since TIMESTAMP WITH TIME ZONE;

-- 为已有的VIP用户设置初始值
UPDATE profiles 
SET vip_since = COALESCE(vip_updated_at, created_at, NOW()) 
WHERE is_vip = true AND vip_since IS NULL;
```

### 部署步骤

1. 使用Supabase CLI或控制台部署该函数
   ```bash
   supabase functions deploy revenuecat-webhook --no-verify-jwt
   ```

2. 在Supabase Functions配置中禁用JWT验证（重要！RevenueCat的请求没有JWT）
   ```toml
   # config.toml
   [functions.revenuecat-webhook]
   verify_jwt = false
   ```

3. 设置环境变量
   ```bash
   # 设置RevenueCat Webhook签名密钥
   supabase secrets set REVENUECAT_WEBHOOK_SECRET=your_webhook_signing_secret
   ```

4. 在RevenueCat控制台配置webhook
   - URL: `https://[YOUR_PROJECT_REF].supabase.co/functions/v1/revenuecat-webhook`
   - 事件: 根据需求选择，通常会选择所有事件类型
   - 复制生成的签名密钥并设置为环境变量

## 测试计划

为了确保订阅系统的各个组件正常工作，建议执行以下全面测试计划：

### 准备工作

1. **测试账号准备**
   - 创建至少3个测试用户账号
   - 在RevenueCat sandbox环境配置测试设备

2. **环境配置检查**
   - 确认边缘函数部署正确
   - 验证日志记录已启用
   - 确认数据库表结构完整

### 订阅事件测试流程

1. **首次购买测试**
   - 使用测试账号购买月度套餐
   - 检查日志和数据库更新
   - 验收标准：`is_vip=true`, 新订阅记录创建

2. **续订测试**
   - 使用RevenueCat测试工具触发续订
   - 验收标准：`expires_at`更新，VIP状态不变

3. **产品变更测试**
   - 从月度升级到年度套餐
   - 验收标准：`product_id`更新，特性更新

4. **取消订阅测试**
   - 使用测试工具取消订阅
   - 验收标准：`status=expired`, `is_vip=false`

5. **订阅到期测试**
   - 触发到期事件
   - 验收标准：`status=expired`, `is_vip=false`

6. **账单问题测试**
   - 触发账单问题事件
   - 验收标准：`billing_issues`表新记录

7. **取消恢复测试**
   - 取消后恢复订阅
   - 验收标准：`status=active`, `is_vip=true`

8. **订阅暂停测试**
   - 使用RevenueCat测试工具暂停订阅
   - 验收标准：`status=paused`, `is_vip=true`

### 测试记录表

| 测试项 | 预期结果 | 实际结果 | 状态 | 备注 |
|-------|---------|---------|------|-----|
| 首次购买 | 用户状态更新为VIP | | | |
| 订阅续订 | 到期时间延长 | | | |
| 产品变更 | 产品ID更新 | | | |
| 订阅取消 | 用户VIP状态取消 | | | |
| 订阅到期 | 用户VIP状态取消 | | | |
| 账单问题 | 问题记录创建 | | | |
| 取消恢复 | VIP状态恢复 | | | |
| 订阅暂停 | VIP状态保持 | | | |

## 测试方法

### 基础测试

1. **使用RevenueCat控制台测试**
   - 登录RevenueCat开发者控制台
   - 导航到"Webhooks"设置页面
   - 使用"Send Test Webhook"功能发送测试事件
   - 选择各种事件类型进行测试，如`INITIAL_PURCHASE`、`RENEWAL`等
   - 确保在事件负载中包含有效的`app_user_id`和`product_id`

2. **通过Supabase检查日志**
   ```bash
   supabase functions logs revenuecat-webhook --filter=debug
   ```
   - 观察输出，确认事件被正确接收，并且没有错误
   - 检查授权验证是否成功
   - 确认事件类型识别和用户确认步骤正常

3. 使用cURL模拟webhook请求
   ```bash
   curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: YOUR_WEBHOOK_SECRET" \
     -d '{"event":{"app_user_id":"test_user_123","product_id":"monthly_subscription","type":"INITIAL_PURCHASE","entitlement_ids":["premium"],"expiration_at_ms":'$(date -d "+30 days" +%s000)'}}' \
     https://YOUR_PROJECT_REF.supabase.co/functions/v1/revenuecat-webhook
   ```

### 事件类型测试

针对每种事件类型，应准备相应的测试数据:

#### 1. 订阅激活测试 (INITIAL_PURCHASE/RENEWAL)
```json
{
  "event": {
    "type": "INITIAL_PURCHASE",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "transaction_id": "test_transaction_123",
    "original_transaction_id": "test_transaction_123",
    "store": "app_store",
    "environment": "SANDBOX",
    "entitlement_ids": ["premium"],
    "purchased_at_ms": 1656025200000,
    "expiration_at_ms": 1658703600000
  }
}
```

#### 2. 订阅取消测试 (CANCELLATION)
```json
{
  "event": {
    "type": "CANCELLATION",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "transaction_id": "test_transaction_123",
    "original_transaction_id": "test_transaction_123",
    "cancellation_reason": "customer_cancelled",
    "store": "app_store",
    "environment": "SANDBOX"
  }
}
```

#### 3. 订阅过期测试 (EXPIRATION)
```json
{
  "event": {
    "type": "EXPIRATION",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "transaction_id": "test_transaction_123",
    "original_transaction_id": "test_transaction_123",
    "store": "app_store",
    "environment": "SANDBOX",
    "expiration_reason": "billing_issue"
  }
}
```

#### 4. 产品变更测试 (PRODUCT_CHANGE)
```json
{
  "event": {
    "type": "PRODUCT_CHANGE",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "new_product_id": "yearly_subscription",
    "transaction_id": "test_transaction_456",
    "original_transaction_id": "test_transaction_123",
    "store": "app_store",
    "environment": "SANDBOX",
    "entitlement_ids": ["premium"]
  }
}
```

#### 5. 账单问题测试 (BILLING_ISSUE)
```json
{
  "event": {
    "type": "BILLING_ISSUE",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "transaction_id": "test_transaction_123",
    "original_transaction_id": "test_transaction_123",
    "store": "app_store",
    "environment": "SANDBOX",
    "grace_period_expires_date_ms": 1658703600000
  }
}
```

#### 6. 退款测试 (REFUND)
```json
{
  "event": {
    "type": "CANCELLATION",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "transaction_id": "test_transaction_123",
    "original_transaction_id": "test_transaction_123",
    "cancellation_reason": "refund",
    "store": "app_store",
    "environment": "SANDBOX",
    "is_refund": true,
    "price": -9.99,
    "currency": "USD"
  }
}
```

#### 7. 退款撤销测试 (REFUND_REVERSED)
```json
{
  "event": {
    "type": "REFUND_REVERSED",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "transaction_id": "test_transaction_123",
    "original_transaction_id": "test_transaction_123",
    "store": "app_store",
    "environment": "SANDBOX",
    "price": 9.99,
    "currency": "USD"
  }
}
```

#### 8. 订阅延长测试 (SUBSCRIPTION_EXTENDED)
```json
{
  "event": {
    "type": "SUBSCRIPTION_EXTENDED",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "transaction_id": "test_transaction_123",
    "original_transaction_id": "test_transaction_123",
    "store": "app_store",
    "environment": "SANDBOX",
    "expiration_at_ms": 1661382000000
  }
}
```

#### 9. 试用开始测试 (TRIAL_STARTED)
```json
{
  "event": {
    "type": "TRIAL_STARTED",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "transaction_id": "test_transaction_123",
    "original_transaction_id": "test_transaction_123",
    "store": "app_store",
    "environment": "SANDBOX",
    "period_type": "TRIAL",
    "purchased_at_ms": 1656025200000,
    "expiration_at_ms": 1658703600000,
    "entitlement_ids": ["premium"]
  }
}
```

#### 10. 试用转付费测试 (TRIAL_CONVERSION)
```json
{
  "event": {
    "type": "TRIAL_CONVERSION",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "transaction_id": "test_transaction_123",
    "original_transaction_id": "test_transaction_123",
    "store": "app_store",
    "environment": "SANDBOX",
    "is_trial_conversion": true,
    "purchased_at_ms": 1658703600000,
    "expiration_at_ms": 1661382000000,
    "entitlement_ids": ["premium"]
  }
}
```

#### 11. 试用取消测试 (TRIAL_CANCELLATION)
```json
{
  "event": {
    "type": "TRIAL_CANCELLATION",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "transaction_id": "test_transaction_123",
    "original_transaction_id": "test_transaction_123",
    "store": "app_store",
    "environment": "SANDBOX",
    "period_type": "TRIAL",
    "cancellation_reason": "trial_cancelled"
  }
}
```

#### 12. 发票开具测试 (INVOICE_ISSUANCE)
```json
{
  "event": {
    "type": "INVOICE_ISSUANCE",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "id": "invoice_123456789",
    "store": "RC_BILLING",
    "environment": "SANDBOX",
    "price_in_purchased_currency": 9.99,
    "currency": "USD",
    "event_timestamp_ms": 1656025200000,
    "country_code": "US"
  }
}
```

#### 13. 取消恢复测试 (UNCANCELLATION)
```json
{
  "event": {
    "type": "UNCANCELLATION",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "transaction_id": "test_transaction_123",
    "original_transaction_id": "test_transaction_123",
    "store": "app_store",
    "environment": "SANDBOX",
    "period_type": "NORMAL",
    "expiration_at_ms": 1661382000000,
    "entitlement_ids": ["premium"]
  }
}
```

#### 14. 订阅暂停测试 (SUBSCRIPTION_PAUSED)
```json
{
  "event": {
    "type": "SUBSCRIPTION_PAUSED",
    "app_user_id": "test_user_123",
    "product_id": "monthly_subscription",
    "transaction_id": "test_transaction_123",
    "original_transaction_id": "test_transaction_123",
    "store": "play_store",
    "environment": "SANDBOX",
    "auto_resume_at_ms": 1661382000000,
    "expiration_at_ms": 1658703600000
  }
}
```

#### 15. 订阅转移测试 (TRANSFER)
```json
{
  "event": {
    "type": "TRANSFER",
    "transferred_from": ["old_user_123"],
    "transferred_to": ["test_user_123"],
    "product_id": "monthly_subscription",
    "store": "app_store",
    "environment": "SANDBOX",
    "event_timestamp_ms": 1656025200000,
    "entitlement_ids": ["premium"]
  }
}
```

#### 16. 临时授权测试 (TEMPORARY_ENTITLEMENT_GRANT)
```json
{
  "event": {
    "type": "TEMPORARY_ENTITLEMENT_GRANT",
    "app_user_id": "test_user_123",
    "id": "grant_123456789",
    "product_id": "monthly_subscription",
    "transaction_id": "temp_transaction_123",
    "store": "app_store",
    "environment": "SANDBOX",
    "entitlement_ids": ["premium"],
    "purchased_at_ms": 1656025200000,
    "expiration_at_ms": 1656111600000
  }
}
```

### 数据库验证

测试每种事件类型后，应进行以下数据库验证:

#### 1. 订阅激活测试 (INITIAL_PURCHASE/RENEWAL) 验证
```sql
-- 验证用户VIP状态
SELECT id, is_vip, vip_product_id, vip_expires_at, vip_since 
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=true, vip_product_id='monthly_subscription', vip_expires_at应等于事件中的expiration_at_ms

-- 验证订阅记录
SELECT id, status, product_id, transaction_id, expires_at
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='active', product_id='monthly_subscription', transaction_id='test_transaction_123'

-- 验证支付记录
SELECT id, amount, currency, status, description
FROM payments
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='succeeded', 支付记录已创建

-- 验证订阅事件
SELECT id, event_type, transaction_id, product_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='INITIAL_PURCHASE'或'RENEWAL'
```

#### 2. 订阅取消测试 (CANCELLATION) 验证
```sql
-- 验证用户VIP状态
SELECT id, is_vip, vip_product_id, vip_expires_at 
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=false

-- 验证订阅记录
SELECT id, status, cancellation_reason, cancellation_reason_description
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='cancelled', cancellation_reason='customer_cancelled', cancellation_reason_description='用户主动取消'

-- 验证订阅事件
SELECT id, event_type, transaction_id, product_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='CANCELLATION'
```

#### 3. 订阅过期测试 (EXPIRATION) 验证
```sql
-- 验证用户VIP状态
SELECT id, is_vip, vip_updated_at
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=false, vip_updated_at为最近时间

-- 验证订阅记录
SELECT id, status, expiration_reason, expiration_reason_description
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='expired', expiration_reason='billing_issue'

-- 验证订阅事件
SELECT id, event_type, transaction_id, product_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='EXPIRATION'
```

#### 4. 产品变更测试 (PRODUCT_CHANGE) 验证
```sql
-- 验证用户VIP状态
SELECT id, is_vip, vip_product_id
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=true, vip_product_id='yearly_subscription'

-- 验证旧订阅记录
SELECT id, status, product_id
FROM subscriptions
WHERE user_id = 'test_user_123' AND product_id = 'monthly_subscription'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='changed'

-- 验证新订阅记录
SELECT id, status, product_id, previous_product_id, product_change_type
FROM subscriptions
WHERE user_id = 'test_user_123' AND product_id = 'yearly_subscription'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='active', product_id='yearly_subscription', previous_product_id='monthly_subscription'

-- 验证支付记录
SELECT id, amount, currency, status, description
FROM payments
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: 包含"产品变更"的描述

-- 验证订阅事件
SELECT id, event_type, transaction_id, product_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='PRODUCT_CHANGE', product_id='yearly_subscription'
```

#### 5. 账单问题测试 (BILLING_ISSUE) 验证
```sql
-- 验证订阅记录
SELECT id, status, has_billing_issue, billing_issue_detected_at
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: has_billing_issue=true, billing_issue_detected_at为最近时间

-- 验证账单问题记录
SELECT id, user_id, subscription_id, resolved, details
FROM billing_issues
WHERE user_id = 'test_user_123'
ORDER BY detected_at DESC
LIMIT 1;
-- 预期结果: resolved=false, details包含相关账单问题信息

-- 验证订阅事件
SELECT id, event_type, transaction_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='BILLING_ISSUE'

-- 注意: 账单问题不会立即影响用户VIP状态，无需验证profiles表
```

#### 6. 退款测试 (REFUND) 验证
```sql
-- 验证用户VIP状态
SELECT id, is_vip
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=false

-- 验证订阅记录
SELECT id, status, is_refunded, refunded_at, refund_amount, refund_currency
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='refunded', is_refunded=true, refund_amount=9.99, refund_currency='USD'

-- 验证退款支付记录
SELECT id, amount, currency, status, description
FROM payments
WHERE user_id = 'test_user_123' AND status = 'refunded'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: 存在refunded状态的支付记录，描述中包含"退款"

-- 验证订阅事件
SELECT id, event_type, transaction_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='REFUND'
```

#### 7. 退款撤销测试 (REFUND_REVERSED) 验证
```sql
-- 验证用户VIP状态
SELECT id, is_vip
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=true

-- 验证订阅记录
SELECT id, status, is_refunded, refund_reversed, refund_reversed_at
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='active', is_refunded=false, refund_reversed=true

-- 验证支付记录
SELECT id, amount, currency, status, description
FROM payments
WHERE user_id = 'test_user_123' AND description LIKE '%退款撤销%'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: 存在退款撤销的支付记录

-- 验证订阅事件
SELECT id, event_type, transaction_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='REFUND_REVERSED'
```

#### 8. 订阅延长测试 (SUBSCRIPTION_EXTENDED) 验证
```sql
-- 验证用户VIP状态
SELECT id, is_vip, vip_expires_at
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=true, vip_expires_at已更新为延长后的日期

-- 验证订阅记录
SELECT id, status, extension_count, last_extended_at, original_expiration_date, expires_at
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: extension_count>0, expires_at已更新为延长后的日期

-- 验证订阅延长历史
SELECT id, subscription_id, original_expiration_date, new_expiration_date, reason
FROM subscription_extensions
WHERE subscription_id IN (SELECT id FROM subscriptions WHERE user_id = 'test_user_123')
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: 存在延长记录，reason='revenuecat_webhook'

-- 验证订阅事件
SELECT id, event_type, transaction_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='SUBSCRIPTION_EXTENDED'
```

#### 9. 试用开始测试 (TRIAL_STARTED) 验证
```sql
-- 验证用户VIP状态
SELECT id, is_vip, vip_is_trial
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=true, vip_is_trial=true

-- 验证订阅记录
SELECT id, status, is_trial, trial_start_at, trial_end_at, trial_duration_days
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='active', is_trial=true, trial_duration_days>0

-- 验证订阅事件
SELECT id, event_type, transaction_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='TRIAL_STARTED'

-- 注意: 试用期通常不会产生实际支付记录，无需验证payments表
```

#### 10. 试用转付费测试 (TRIAL_CONVERSION) 验证
```sql
-- 验证用户VIP状态
SELECT id, is_vip, vip_is_trial
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=true, vip_is_trial=false

-- 验证订阅记录
SELECT id, status, is_trial, is_trial_conversion
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='active', is_trial=false, is_trial_conversion=true

-- 验证支付记录
SELECT id, amount, currency, status, description
FROM payments
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: 存在支付记录，描述中包含"试用转付费"

-- 验证订阅事件
SELECT id, event_type, transaction_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='TRIAL_CONVERSION'
```

#### 11. 试用取消测试 (TRIAL_CANCELLATION) 验证
```sql
-- 验证用户VIP状态
SELECT id, is_vip, vip_is_trial
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=false, vip_is_trial=false

-- 验证订阅记录
SELECT id, status, is_trial, trial_cancelled, trial_cancelled_at, trial_cancellation_reason
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='trial_cancelled', trial_cancelled=true, trial_cancellation_reason='trial_cancelled'

-- 验证订阅事件
SELECT id, event_type, transaction_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='TRIAL_CANCELLATION'

-- 注意: 试用取消通常不会产生支付变更，无需验证payments表
```

#### 12. 发票开具测试 (INVOICE_ISSUANCE) 验证
```sql
-- 验证发票记录
SELECT id, user_id, product_id, amount, currency, status
FROM invoices
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='pending', amount=9.99, currency='USD'

-- 注意: 发票开具本身不会影响用户VIP状态、订阅状态或创建支付记录，这些变更会在后续的支付事件中处理
-- 因此无需验证profiles、subscriptions或payments表
```

#### 13. 取消恢复测试 (UNCANCELLATION) 验证
```sql
-- 验证用户VIP状态
SELECT id, is_vip
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=true

-- 验证订阅记录
SELECT id, status, cancel_at_period_end, cancellation_reason
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='active', cancel_at_period_end=false, cancellation_reason=NULL

-- 验证订阅事件
SELECT id, event_type, transaction_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='UNCANCELLATION'

-- 注意: 取消恢复通常不会产生新的支付记录，无需验证payments表
```

#### 14. 订阅暂停测试 (SUBSCRIPTION_PAUSED) 验证
```sql
-- 验证用户VIP状态（如果过期时间未到，VIP应保持活跃）
SELECT id, is_vip, vip_expires_at
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: 取决于过期时间，如未过期则is_vip=true

-- 验证订阅记录
SELECT id, status, is_paused, paused_at, auto_resume_at
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='paused', is_paused=true, auto_resume_at有值

-- 验证订阅事件
SELECT id, event_type, transaction_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='SUBSCRIPTION_PAUSED'

-- 注意: 订阅暂停不会产生支付记录变更，无需验证payments表
```

#### 15. 订阅转移测试 (TRANSFER) 验证
```sql
-- 验证原用户VIP状态
SELECT id, is_vip
FROM profiles 
WHERE id = 'old_user_123';
-- 预期结果: is_vip=false

-- 验证新用户VIP状态
SELECT id, is_vip, vip_product_id
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=true, vip_product_id='monthly_subscription'

-- 验证原订阅记录
SELECT id, status
FROM subscriptions
WHERE user_id = 'old_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='transferred'

-- 验证新订阅记录
SELECT id, status, product_id, transfer_from_user_id
FROM subscriptions
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: status='active', transfer_from_user_id='old_user_123'

-- 验证订阅事件 - 原用户
SELECT id, event_type
FROM subscription_events
WHERE user_id = 'old_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='TRANSFER_OUT'

-- 验证订阅事件 - 新用户
SELECT id, event_type
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='TRANSFER_IN'

-- 注意: 订阅转移不会产生新的支付记录，无需验证payments表
```

#### 16. 临时授权测试 (TEMPORARY_ENTITLEMENT_GRANT) 验证
```sql
-- 验证用户VIP状态
SELECT id, is_vip, vip_expires_at
FROM profiles 
WHERE id = 'test_user_123';
-- 预期结果: is_vip=true, vip_expires_at等于临时授权到期时间

-- 验证临时授权记录
SELECT id, user_id, product_id, transaction_id, expires_at, is_validated
FROM temporary_entitlements
WHERE user_id = 'test_user_123'
ORDER BY created_at DESC
LIMIT 1;
-- 预期结果: expires_at等于临时授权到期时间, is_validated=false

-- 验证事件（可选，如果有记录事件）
SELECT id, event_type, transaction_id
FROM subscription_events
WHERE user_id = 'test_user_123'
ORDER BY event_timestamp DESC
LIMIT 1;
-- 预期结果: event_type='TEMPORARY_ENTITLEMENT_GRANT'

-- 注意: 临时授权不会产生订阅记录或支付记录，无需验证subscriptions或payments表
```

### 通用验证查询

以下查询可用于所有测试场景的通用验证:

```sql
-- 检查用户记录是否存在
SELECT id, email, is_vip 
FROM profiles 
WHERE id = 'test_user_123';

-- 检查用户最新订阅状态
SELECT id, status, product_id, created_at, updated_at 
FROM subscriptions 
WHERE user_id = 'test_user_123' 
ORDER BY created_at DESC 
LIMIT 1;

-- 检查用户最新事件记录
SELECT id, event_type, created_at 
FROM subscription_events 
WHERE user_id = 'test_user_123' 
ORDER BY event_timestamp DESC 
LIMIT 1;

-- 检查支付历史记录
SELECT id, amount, currency, status, created_at 
FROM payments 
WHERE user_id = 'test_user_123' 
ORDER BY created_at DESC;
```

### 调试技巧

1. **使用测试事件标志**
   - 在webhook请求中加入`"test": true`标志，可跳过授权验证
   - 例如：`{"event": {...}, "test": true}`

2. **检查授权验证**
   - 如果收到401错误，检查`REVENUECAT_WEBHOOK_SECRET`环境变量是否正确设置
   - 确认Authorization头与`rcWebhookSigningSecret`匹配

3. **解决数据加载问题**
   - 如果数据未正确更新，查看日志中的SQL错误
   - 检查表结构是否匹配函数中的预期字段

4. **调试常见问题**
   - 用户未找到: 检查`ensureUserExists`函数的日志
   - 订阅未更新: 检查`findSubscriptionByIdentifiers`函数的返回值
   - VIP状态不正确: 检查`profiles`表更新语句是否执行成功

## 故障排除

### Webhook处理问题

- **401 Unauthorized**: 确保已禁用JWT验证
- **400 Bad Request**: 验证签名密钥是否正确配置
- **数据库错误**: 检查表结构和字段名是否匹配代码中引用的名称

### 常见订阅问题排查

1. **用户购买成功但状态未更新**
   - 检查webhook是否成功触发 (查看RevenueCat事件历史)
   - 验证用户ID映射是否正确 (查看日志中的`app_user_id`)
   - 检查函数日志是否有错误记录

2. **订阅记录存在但VIP权限未生效**
   - 检查`update_user_vip_status`触发器是否正常工作
   - 验证`profiles`表更新是否成功
   - 检查应用端是否正确读取了用户VIP状态

3. **支付成功但订阅记录未创建**
   - 检查RevenueCat中的用户ID是否正确
   - 验证webhook是否成功接收事件
   - 检查数据库连接和权限

4. **VIP状态未正确同步到其他设备**
   - 确保应用在启动时请求最新的用户状态
   - 检查RevenueCat用户ID在不同设备上是否一致

## 注意事项

- 确保在RevenueCat购买发生后，用户ID被正确传递给RevenueCat
- 在生产环境中，实现完整的签名验证机制（当前版本已简化处理）
- 测试webhook前，可以使用RevenueCat控制台的"测试webhook"功能
- 对于生产环境，建议实现重试机制和更详细的错误日志

## 客户端应用故障排除

### React Hooks 错误

如果在应用中看到类似以下的错误:
```
Invalid hook call. Hooks can only be called inside of the body of a function component.
```

可能原因:
1. AuthProvider组件中的React Hooks使用不正确
2. React版本冲突
3. 条件渲染中使用了Hook

解决方案:
1. 确保所有Hook调用都在组件函数体的顶层，不在条件或循环中
2. 检查useRef的使用是否正确，应该使用import引入而不是React.useRef
3. 确保全局变量的检查和设置有适当的防御性代码

## 取消原因处理

RevenueCat的CANCELLATION事件中包含了用户取消订阅的原因信息，这对于改进产品和了解用户流失原因非常有价值。本系统现在支持以下功能：

1. **取消原因提取**：从不同位置提取取消原因，包括`cancellation_reason`、`cancel_reason`等多个可能的字段
2. **标准化描述**：将技术性的英文代码转换为易于理解的中文描述，例如将`customer_cancelled`转换为`用户主动取消`
3. **数据存储**：
   - `cancellation_reason`字段：存储原始取消原因代码
   - `cancellation_reason_description`字段：存储标准化后的中文描述
4. **事件日志**：在取消事件时，同时记录原始取消原因和标准化描述到`subscription_events`表

### 支持的取消原因类型

系统能够识别和分类的取消原因包括：

#### Apple常见原因
- `customer_cancelled` → 用户主动取消
- `billing_error` → 账单错误
- `price_increase` → 价格上涨
- `rejected_price_increase` → 用户拒绝价格上涨
- `declined` → 支付被拒
- `failed_billing` → 账单支付失败
- `app_deleted` → 应用被删除
- `refund` → 用户申请退款

#### Google常见原因
- `user_cancelled` → 用户主动取消
- `payment_issue` → 支付问题
- `system_cancelled` → 系统取消
- 等等

#### 通用描述
- `voluntary` → 用户自愿取消
- `involuntary` → 非自愿取消（如支付问题）

### 使用场景

通过分析取消原因，产品团队可以：
1. 了解用户流失的主要原因
2. 区分用户主动取消和支付问题导致的被动取消
3. 针对不同取消原因采取不同的挽留措施
4. 改进产品和定价策略

## 订阅恢复(UNCANCELLATION)处理

当用户在取消订阅后，又重新激活同一订阅时，RevenueCat会发送UNCANCELLATION事件。本系统对此事件的处理逻辑如下：

### 处理流程

1. **查找原订阅记录**：
   - 首先尝试通过`transaction_id`或`original_transaction_id`查找之前被取消的订阅
   - 如未找到，则查找该用户最近的已取消/已过期的相同产品订阅
   - 如仍未找到，则按新订阅处理

2. **状态转换**：
   - 将订阅状态从`cancelled`或`expired`恢复为`active`
   - 清除`cancellation_reason`和`cancellation_reason_description`字段
   - 更新`cancel_at_period_end`为`false`

3. **权益更新**：
   - 更新订阅的权益信息(`entitlements`)
   - 更新相关的属性如`period_type`、`is_family_share`等

4. **事件记录**：
   - 在`subscription_events`表中记录UNCANCELLATION事件
   - 保存完整的事件数据用于审计和分析

5. **用户状态更新**：
   - 重新激活用户的VIP状态
   - 更新VIP相关字段如到期时间、环境等

### 处理示例

对于以下UNCANCELLATION事件数据：
```json
{
    "event": {
        "event_timestamp_ms": 1663982135337,
        "product_id": "com.subscription.monthly",
        "period_type": "NORMAL",
        "entitlement_ids": ["plus"],
        "transaction_id": "123456789012345",
        "app_user_id": "1234567890",
        "type": "UNCANCELLATION"
    }
}
```

系统将：
1. 查找并更新用户ID为"1234567890"、事务ID为"123456789012345"的订阅记录
2. 将状态设置为`active`并清除取消原因
3. 更新权益为["plus"]
4. 重新激活用户的VIP状态

### 检查事项

UNCANCELLATION事件处理成功后，应确认：
- 订阅表中相关记录状态已更新为`active`
- 用户资料中`is_vip`已设置为`true`
- 取消原因字段已被清除
- 事件表中已正确记录该恢复事件

## 订阅暂停(SUBSCRIPTION_PAUSED)处理

Google Play商店允许用户暂时暂停订阅而不是完全取消，系统将在一段时间后自动恢复订阅。当用户暂停订阅时，RevenueCat会发送SUBSCRIPTION_PAUSED事件。本系统对此事件的处理逻辑如下：

### 处理流程

1. **查找订阅记录**：
   - 通过交易标识符查找相关订阅记录
   - 如未找到特定记录，则查找用户的活跃订阅

2. **状态转换**：
   - 将订阅状态从`active`更新为`paused`
   - 设置`is_paused`标志为`true`
   - 记录暂停时间(`paused_at`)和自动恢复时间(`auto_resume_at`)

3. **VIP状态处理**：
   - 依据Google Play政策，暂停期间用户保留权益直到原过期时间
   - 如果过期时间已到，则取消VIP状态
   - 否则保持VIP状态直到过期

4. **事件记录**：
   - 在`subscription_events`表记录SUBSCRIPTION_PAUSED事件
   - 包含自动恢复时间等关键信息

5. **自动恢复机制**：
   - 通过`check_paused_subscriptions`函数定期检查暂停的订阅
   - 当达到自动恢复时间时，恢复订阅状态为`active`
   - 重新激活用户VIP状态并记录AUTO_RESUME事件

### 处理示例

对于以下SUBSCRIPTION_PAUSED事件数据：
```json
{
    "event": {
        "event_timestamp_ms": 1652796516000,
        "product_id": "premium",
        "auto_resume_at_ms": 1657951448845,
        "transaction_id": "123456789012345",
        "app_user_id": "1234567890",
        "type": "SUBSCRIPTION_PAUSED"
    }
}
```

系统将：
1. 查找并更新用户ID为"1234567890"的相关订阅记录
2. 设置状态为`paused`并记录自动恢复时间
3. 保持用户VIP状态直到订阅原本的过期时间
4. 设置自动恢复检查

### 实现注意事项

- 暂停功能是Google Play商店特有的，Apple App Store不支持
- 暂停订阅的自动恢复需要定期运行`check_paused_subscriptions`函数
- 建议设置定时任务或Webhook运行自动恢复检查
- 自动恢复时间可能会因Google Play政策或用户操作而变更

### 检查事项

订阅暂停处理成功后，应确认：
- 订阅表中相关记录状态已更新为`paused`
- `is_paused`字段已设置为`true`
- `auto_resume_at`字段已正确设置
- 用户VIP状态正确反映当前暂停对权益的影响
- 已建立自动恢复机制

```jsx
// 错误示例
if (condition) {
  useEffect(() => {}, []);
}

// 正确示例
useEffect(() => {
  if (condition) {
    // 逻辑
  }
}, [condition]);
``` 