import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// 环境变量配置验证函数
function validateEnvConfig(): void {
  const requiredEnvVars = [
    'SILICONFLOW_API_KEY',
    'OPENAI_API_KEY'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !Deno.env.get(varName));
  
  if (missingVars.length > 0) {
    console.warn(`警告：以下环境变量未配置: ${missingVars.join(', ')}`);
    console.warn('某些API服务可能无法正常工作');
  }
  
  // 输出当前环境信息
  const environment = Deno.env.get('ENVIRONMENT') || 'production';
  const port = Deno.env.get('PORT') || '8000';
  console.log(`环境: ${environment}`);
  console.log(`端口: ${port}`);
}

// 验证环境配置
validateEnvConfig();

// 从环境变量获取端口，默认为8000
const PORT = parseInt(Deno.env.get('PORT') || '8000');
console.log(`统一代理服务将在端口 ${PORT} 启动`);

// 创建 Supabase 客户端用于记录日志
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// 支持的API服务配置
const API_SERVICES = {
  // SiliconFlow AI 服务
  siliconflow: {
    baseUrl: 'https://api.siliconflow.cn',
    authHeader: 'Authorization',
    authPrefix: 'Bearer ',
    envKey: 'SILICONFLOW_API_KEY'
  },
  // OpenAI 服务
  openai: {
    baseUrl: Deno.env.get('OPENAI_API_BASE_URL') || 'https://api.openai.com',
    authHeader: 'Authorization', 
    authPrefix: 'Bearer ',
    envKey: 'OPENAI_API_KEY'
  },
  // Anthropic 服务
  anthropic: {
    baseUrl: 'https://api.anthropic.com',
    authHeader: 'x-api-key',
    authPrefix: '',
    envKey: 'ANTHROPIC_API_KEY'
  },
  // ElevenLabs 服务
  elevenlabs: {
    baseUrl: 'https://api.elevenlabs.io',
    authHeader: 'xi-api-key',
    authPrefix: '',
    envKey: 'ELEVENLABS_API_KEY'
  }
};

// 通用代理请求接口
interface ProxyRequest {
  service: keyof typeof API_SERVICES;
  path: string;
  method?: string;
  headers?: Record<string, string>;
  body?: any;
}

/**
 * 获取响应大小（用于日志）
 */
function getResponseSize(response: Response): number {
  const contentLength = response.headers.get('content-length');
  return contentLength ? parseInt(contentLength, 10) : 0;
}

/**
 * 检查API密钥是否可用
 */
function isServiceAvailable(service: keyof typeof API_SERVICES): boolean {
  const serviceConfig = API_SERVICES[service];
  return !!Deno.env.get(serviceConfig.envKey);
}

/**
 * 获取用户ID从JWT令牌
 */
function getUserIdFromJWT(authHeader: string): string | null {
  try {
    if (!authHeader.startsWith('Bearer ')) return null;
    
    const token = authHeader.substring(7);
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = JSON.parse(atob(parts[1]));
    return payload.sub || null;
  } catch (error) {
    console.error('解析JWT失败:', error);
    return null;
  }
}

/**
 * 检查用户是否为VIP
 */
async function checkUserVIPStatus(userId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('is_vip')
      .eq('id', userId)
      .single();
    
    if (error || !data) {
      console.log(`无法获取用户VIP状态: ${userId}`);
      return false;
    }
    
    return data.is_vip || false;
  } catch (error) {
    console.error('检查VIP状态失败:', error);
    return false;
  }
}

/**
 * 解析AI响应以提取使用情况统计
 */
function parseAIUsage(responseText: string, model?: string): {
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
  cost_estimate?: number;
} {
  try {
    // 检查响应文本是否为空或无效
    if (!responseText || typeof responseText !== 'string') {
      console.warn('响应文本为空或无效');
      return {};
    }

    // 去除可能的BOM和空白字符
    const cleanResponseText = responseText.trim().replace(/^\uFEFF/, '');
    
    // 检查是否为JSON格式
    if (!cleanResponseText.startsWith('{') && !cleanResponseText.startsWith('[')) {
      // 对于语音转录API，响应可能是纯文本
      if (model && (model === 'whisper-1' || model === 'gpt-4o-mini-transcribe')) {
        console.log('检测到语音转录API纯文本响应，进行token估算');
        
        // 对于语音转录，估算token使用量
        const transcribedText = cleanResponseText;
        const estimatedTokens = Math.max(1, Math.ceil(transcribedText.length / 4)); // 估算：4字符≈1token
        
        // 语音转录的定价
        let costEstimate = 0;
        if (model === 'whisper-1') {
          costEstimate = 0.006; // Whisper按分钟计费，这里使用固定估算
        } else if (model === 'gpt-4o-mini-transcribe') {
          costEstimate = estimatedTokens / 1000 * 0.00015; // GPT-4o mini按token计费
        }
        
        console.log(`语音转录Token估算: 文本长度=${transcribedText.length}, 估算tokens=${estimatedTokens}, 模型=${model}`);
        
        return {
          prompt_tokens: 0, // 语音转录没有prompt tokens
          completion_tokens: estimatedTokens,
          total_tokens: estimatedTokens,
          cost_estimate: costEstimate
        };
      }
      
      console.warn('响应文本不是JSON格式，跳过AI使用情况解析');
      return {};
    }

    // 尝试解析JSON，处理可能的流式响应
    let response;
    try {
      response = JSON.parse(cleanResponseText);
    } catch (parseError) {
      // 可能是流式响应，尝试解析最后一行JSON
      const lines = cleanResponseText.split('\n').filter(line => line.trim());
      for (let i = lines.length - 1; i >= 0; i--) {
        const line = lines[i].trim();
        if (line.startsWith('{') || line.startsWith('[')) {
          try {
            response = JSON.parse(line);
            break;
          } catch (lineParseError) {
            continue;
          }
        }
      }
      
      if (!response) {
        console.warn('无法解析响应文本为JSON:', cleanResponseText.substring(0, 200) + '...');
        return {};
      }
    }
    
    // 处理标准的OpenAI文本生成API响应
    if (response.usage) {
      const usage = response.usage;
      
      // 计算成本估算
      let costEstimate = 0;
      if (model && usage.total_tokens) {
        // 更新的定价参考
        const pricingMap: { [key: string]: number } = {
          // SiliconFlow模型定价（元/千tokens）
          'Qwen/Qwen2.5-7B-Instruct': 0.0003,        // ¥0.3/千tokens
          'qwen3-8b': 0.0005,                        // ¥0.5/千tokens
          'qwen3-14b': 0.0008,                       // ¥0.8/千tokens
          'deepseek-ai/DeepSeek-V3': 0.0014,         // ¥1.4/千tokens
          // OpenAI模型定价（美元/千tokens）
          'gpt-3.5-turbo': 0.002,                    // $2/千tokens
          'gpt-4o-mini-transcribe': 0.00015,         // $0.15/千tokens
          'whisper-1': 0.006,                        // $6/小时 -> 按tokens估算
          'gpt-4': 0.03,                             // $30/千tokens
        };
        
        const pricePerThousand = pricingMap[model] || 0.001; // 默认价格
        costEstimate = (usage.total_tokens / 1000) * pricePerThousand;
      }
      
      return {
        prompt_tokens: usage.prompt_tokens,
        completion_tokens: usage.completion_tokens,
        total_tokens: usage.total_tokens,
        cost_estimate: costEstimate
      };
    }
    
    // 处理OpenAI语音转录API响应（通常只有文本，没有usage字段）
    if (model && (model === 'whisper-1' || model === 'gpt-4o-mini-transcribe') && response.text) {
      // 对于语音转录，估算token使用量
      const transcribedText = response.text || '';
      const estimatedTokens = Math.max(1, Math.ceil(transcribedText.length / 4)); // 估算：4字符≈1token
      
      // 语音转录的定价（OpenAI按分钟计费，这里按token估算）
      let costEstimate = 0;
      if (model === 'whisper-1') {
        costEstimate = 0.006; // Whisper按分钟计费，这里使用固定估算
      } else if (model === 'gpt-4o-mini-transcribe') {
        costEstimate = estimatedTokens / 1000 * 0.00015; // GPT-4o mini按token计费
      }
      
      console.log(`语音转录Token估算: 文本长度=${transcribedText.length}, 估算tokens=${estimatedTokens}, 模型=${model}`);
      
      return {
        prompt_tokens: 0, // 语音转录没有prompt tokens
        completion_tokens: estimatedTokens,
        total_tokens: estimatedTokens,
        cost_estimate: costEstimate
      };
    }
  } catch (error) {
    console.warn('解析AI使用情况失败:', error);
    if (error instanceof Error) {
      console.warn('错误详情:', error.message);
    }
  }
  
  return {};
}

/**
 * 记录代理使用日志
 */
async function logProxyUsage(
  userId: string | null,
  service: string,
  path: string,
  method: string,
  responseStatus: number,
  bytesTransferred: number,
  isVip: boolean,
  model?: string,
  aiUsage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
    cost_estimate?: number;
  }
) {
  try {
    const logData = {
      user_id: userId,
      service,
      path,
      method,
      response_status: responseStatus,
      bytes_transferred: bytesTransferred,
      is_vip: isVip,
      request_timestamp: new Date().toISOString(),
      // AI服务特定字段
      ...(model && { model }),
      ...(aiUsage?.prompt_tokens && { prompt_tokens: aiUsage.prompt_tokens }),
      ...(aiUsage?.completion_tokens && { completion_tokens: aiUsage.completion_tokens }),
      ...(aiUsage?.total_tokens && { total_tokens: aiUsage.total_tokens }),
      ...(aiUsage?.cost_estimate && { cost_estimate: aiUsage.cost_estimate })
    };
    
    const { error } = await supabase
      .from('unified_proxy_logs')
      .insert(logData);
    
    if (error) {
      console.error('记录使用日志失败:', error);
    } else {
      console.log(`记录使用日志成功: ${service} ${path}, 用户: ${userId}, VIP: ${isVip}`);
    }
  } catch (error) {
    console.error('记录使用日志异常:', error);
  }
}

console.log(`统一代理服务正在监听端口 ${PORT}...`);

// 显示可用的服务
const availableServices = Object.keys(API_SERVICES).filter(service => 
  isServiceAvailable(service as keyof typeof API_SERVICES)
);
console.log(`可用的服务: ${availableServices.join(', ')}`);

serve(async (req: Request) => {
  // 处理 CORS 预检请求
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-proxy-service, x-proxy-path',
      },
    });
  }

  let userId: string | null = null;
  let isVip = false;

  try {
    // 从Authorization头获取用户ID
    const authHeader = req.headers.get('authorization');
    console.log(`Authorization 头部: ${authHeader ? '存在' : '不存在'}`);
    
    if (authHeader) {
      userId = getUserIdFromJWT(authHeader);
      console.log(`解析用户ID结果: ${userId || '失败'}`);
      
      if (userId) {
        isVip = await checkUserVIPStatus(userId);
        console.log(`用户 ${userId} ${isVip ? '(VIP)' : '(普通)'} 发起代理请求`);
      } else {
        console.warn('JWT解析失败，无法获取用户ID');
      }
    } else {
      console.warn('请求中缺少Authorization头部');
    }

    // 解析请求参数
    let requestData: ProxyRequest;
    let isFormData = false;
    let formDataBody = null;
    
    console.log(`[请求解析] 请求方法: ${req.method}`);
    console.log(`[请求解析] Content-Type: ${req.headers.get('content-type')}`);
    
    if (req.method === 'POST' && req.headers.get('content-type')?.includes('application/json')) {
      console.log(`[请求解析] 走JSON解析分支`);
      requestData = await req.json() as ProxyRequest;
      console.log(`[请求解析] JSON解析后的requestData.body类型: ${typeof requestData.body}`);
      if (requestData.body && typeof requestData.body === 'object') {
        console.log(`[请求解析] JSON解析后的model字段: "${requestData.body.model}"`);
      }
    } else {
      console.log(`[请求解析] 走Header解析分支`);
      // 从 headers 中获取参数
      const service = req.headers.get('x-proxy-service') || 'siliconflow';
      const path = req.headers.get('x-proxy-path') || '/';
      const contentType = req.headers.get('content-type') || '';
      
      // 检查是否为FormData
      if (contentType.includes('multipart/form-data')) {
        isFormData = true;
        formDataBody = req.body;
        console.log(`FormData请求: ${service}${path}`);
        console.log(`FormData Content-Type: ${contentType}`);
        
        // 调试：尝试读取FormData内容（仅用于调试）
        if (Deno.env.get('DEBUG') === 'true') {
          try {
            const formDataClone = req.body?.tee ? req.body.tee()[0] : req.body;
            // 注意：这会消耗stream，仅用于调试
          } catch (e) {
            console.log('无法读取FormData内容用于调试:', e);
          }
        }
      }
      
      requestData = {
        service: service as keyof typeof API_SERVICES,
        path,
        method: req.headers.get('x-proxy-method') || req.method,
        body: isFormData ? undefined : (req.body ? await req.text() : undefined)
      } as ProxyRequest;
    }

    // 验证服务配置
    const serviceConfig = API_SERVICES[requestData.service];
    if (!serviceConfig) {
      return new Response('不支持的服务', { status: 400 });
    }

    // 检查服务是否可用
    if (!isServiceAvailable(requestData.service)) {
      const debugInfo = Deno.env.get('DEBUG') === 'true' ? {
        service: requestData.service,
        envKey: serviceConfig.envKey,
        hasApiKey: !!Deno.env.get(serviceConfig.envKey)
      } : {};
      
      console.error(`服务不可用: ${requestData.service}, API密钥: ${serviceConfig.envKey}`);
      
      return new Response(
        JSON.stringify({ 
          error: '服务不可用',
          message: `${requestData.service} 服务的API密钥未配置`,
          debug: debugInfo
        }),
        { 
          status: 503,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // 构建目标URL
    const targetUrl = serviceConfig.baseUrl + requestData.path;
    
    // 准备请求头
    let requestHeaders: Record<string, string> = {
      'User-Agent': 'Zhiwo-Unified-Proxy/1.0',
    };

    // 对于FormData请求，复制完整的Content-Type头部（包括boundary）
    if (isFormData) {
      const contentType = req.headers.get('content-type');
      if (contentType) {
        requestHeaders['Content-Type'] = contentType;
        console.log(`FormData Content-Type: ${contentType}`);
      }
    } else {
      requestHeaders['Content-Type'] = req.headers.get('content-type') || 'application/json';
    }

    // 添加用户自定义头部
    if (requestData.headers) {
      requestHeaders = { ...requestHeaders, ...requestData.headers };
    }

    // 添加API认证
    const apiKey = Deno.env.get(serviceConfig.envKey);
    if (apiKey) {
      requestHeaders[serviceConfig.authHeader] = serviceConfig.authPrefix + apiKey;
    } else {
      console.warn(`API密钥未配置: ${serviceConfig.envKey}`);
    }

    // 发起代理请求
    console.log(`代理请求 ${requestData.service}: ${targetUrl}`);

    // 构建请求体
    let requestBody;
    if (isFormData && formDataBody) {
      requestBody = formDataBody;
      console.log('使用FormData体');
    } else if (requestData.body) {
      requestBody = typeof requestData.body === 'string' ? requestData.body : JSON.stringify(requestData.body);
    }

    // 记录代理设置（用于调试）
    const httpProxy = Deno.env.get('HTTP_PROXY') || Deno.env.get('HTTPS_PROXY');
    if (httpProxy) {
      console.log(`检测到代理设置: ${httpProxy}`);
    }

    // 直接使用fetch，Deno会自动使用环境变量中的代理设置
    const proxyResponse = await fetch(targetUrl, {
      method: requestData.method || req.method,
      headers: requestHeaders,
      body: requestBody,
    });

    // 复制响应头（排除一些不适合的头）
    const responseHeaders = new Headers();
    proxyResponse.headers.forEach((value: string, key: string) => {
      const lowerKey = key.toLowerCase();
      if (!['host', 'connection', 'transfer-encoding', 'content-encoding'].includes(lowerKey)) {
        responseHeaders.set(key, value);
      }
    });

    // 添加CORS头
    responseHeaders.set('Access-Control-Allow-Origin', '*');
    responseHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
    responseHeaders.set('Access-Control-Allow-Headers', 'authorization, x-client-info, apikey, content-type');

    // 记录日志
    console.log(`代理响应 ${requestData.service}: ${proxyResponse.status} ${proxyResponse.statusText}`);

    // 获取响应体用于日志记录和AI使用情况解析
    const responseClone = proxyResponse.clone();
    const responseText = await responseClone.text();
    const responseSize = new Blob([responseText]).size;

    // 解析AI使用情况（仅对AI服务）
    let aiUsage = {};
    let model: string | undefined;
    
    if (requestData.service === 'siliconflow' || requestData.service === 'openai') {
      // 尝试从请求体中提取模型信息
      try {
        console.log(`[代理模型提取] 开始解析 - 服务: ${requestData.service}, 请求类型: ${isFormData ? 'FormData' : 'JSON'}`);
        console.log(`[代理模型提取] requestData.body类型: ${typeof requestData.body}`);
        
        if (requestData.body && typeof requestData.body === 'object') {
          model = requestData.body.model;
          console.log(`[代理模型提取] 从请求体对象获取模型: "${model}"`);
          console.log(`[代理模型提取] 模型字段类型: ${typeof model}, 长度: ${model?.length || '未定义'}`);
        } else if (requestData.body && typeof requestData.body === 'string') {
          console.log(`[代理模型提取] 请求体是字符串，长度: ${requestData.body.length}`);
          console.log(`[代理模型提取] 请求体内容前100字符: ${requestData.body.substring(0, 100)}`);
          
          const parsedBody = JSON.parse(requestData.body);
          model = parsedBody.model;
          console.log(`[代理模型提取] 从请求体字符串解析获取模型: "${model}"`);
          console.log(`[代理模型提取] 解析后模型字段类型: ${typeof model}, 长度: ${model?.length || '未定义'}`);
          
          // 额外检查：打印整个解析后的body结构
          console.log(`[代理模型提取] 解析后的body结构:`, Object.keys(parsedBody));
        }
        
        // 对于FormData请求（如语音转录），尝试从请求头中提取模型信息
        if (!model && isFormData) {
          try {
            // 首先尝试从自定义头部获取模型信息
            const modelFromHeader = req.headers.get('x-model') || req.headers.get('x-proxy-model');
            if (modelFromHeader) {
              model = modelFromHeader;
              console.log(`从请求头获取模型: ${model}`);
            } else if (requestData.path.includes('/audio/transcriptions') || requestData.path.includes('/audio/translations')) {
              // 如果没有指定模型，使用默认的whisper-1
              model = 'whisper-1';
              console.log('语音请求使用默认模型: whisper-1');
            }
          } catch (formDataError) {
            console.warn('从FormData中提取模型信息失败:', formDataError);
          }
        }
        
        // 添加调试信息
        if (!model) {
          console.log(`未能提取到模型信息 - 请求类型: ${isFormData ? 'FormData' : 'JSON'}, 路径: ${requestData.path}, 请求体类型: ${typeof requestData.body}`);
          if (requestData.body && typeof requestData.body === 'string') {
            const preview = requestData.body.substring(0, 200);
            console.log(`请求体预览: ${preview}...`);
          }
        }
        
        // 解析AI使用情况
        if (proxyResponse.ok && responseText) {
          // 添加调试信息
          console.log(`尝试解析AI使用情况，模型: ${model}, 响应状态: ${proxyResponse.status}, 响应长度: ${responseText.length}`);
          
          // 检查响应文本的基本格式
          if (responseText.length > 0) {
            const preview = responseText.substring(0, 100).replace(/\n/g, '\\n');
            console.log(`响应文本预览: ${preview}...`);
          }
          
          aiUsage = parseAIUsage(responseText, model);
          
          // 如果成功解析到使用情况，记录一下
          if (aiUsage && Object.keys(aiUsage).length > 0) {
            console.log(`AI使用情况解析成功: ${JSON.stringify(aiUsage)}`);
          }
        } else {
          console.log(`跳过AI使用情况解析: 响应状态=${proxyResponse.status}, 响应文本长度=${responseText?.length || 0}`);
        }
      } catch (error) {
        console.warn('解析请求体中的模型信息失败:', error);
        if (error instanceof Error) {
          console.warn('模型解析错误详情:', error.message);
        }
      }
    }

    // 记录使用日志（支持匿名用户）
    console.log(`准备记录使用日志 - 用户: ${userId || '匿名'}, 服务: ${requestData.service}, 路径: ${requestData.path}`);
    await logProxyUsage(
      userId, // 可以为null，表示匿名用户
      requestData.service,
      requestData.path,
      requestData.method || req.method,
      proxyResponse.status,
      responseSize,
      isVip,
      model,
      aiUsage
    );

    // 返回响应
    return new Response(responseText, {
      status: proxyResponse.status,
      statusText: proxyResponse.statusText,
      headers: responseHeaders,
    });

  } catch (error) {
    console.error('统一代理服务错误:', error);
    
    // 记录错误日志
    await logProxyUsage(
      userId, // 可以为null，表示匿名用户
      'unknown',
      '/',
      'POST',
      500,
      0,
      isVip
    );
    
    return new Response(
      JSON.stringify({ 
        error: '代理服务错误',
        details: error instanceof Error ? error.message : '未知错误'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}, { port: PORT }); 