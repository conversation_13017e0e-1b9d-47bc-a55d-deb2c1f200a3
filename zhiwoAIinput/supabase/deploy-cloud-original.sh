#!/bin/bash
# 知我AI输入法 Supabase云端部署脚本
# 该脚本用于将本地Supabase配置推送到云端

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 打印带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查supabase CLI是否安装
if ! command -v supabase &> /dev/null; then
    error "Supabase CLI未安装，请先安装: https://supabase.com/docs/guides/cli"
    exit 1
fi

# 检查是否登录
if ! supabase projects list &> /dev/null; then
    error "请先登录Supabase CLI: supabase login"
    exit 1
fi

# 输出当前项目信息
info "当前项目信息："
supabase projects list

# 加载.env环境变量
if [ -f ../.env ]; then
    set -o allexport
    source ../.env
    set +o allexport
else
    error "../.env文件不存在，请在项目根目录下创建并配置PROJECT_ID、REVENUECAT_WEBHOOK_SECRET等变量"
    exit 1
fi

# 检查PROJECT_ID
if [ -z "$PROJECT_ID" ]; then
    error "../.env文件中未设置PROJECT_ID"
    exit 1
fi

# 链接到项目
info "链接到项目 $PROJECT_ID..."
supabase link --project-ref "$PROJECT_ID"

if [ $? -ne 0 ]; then
    error "链接项目失败"
    exit 1
fi

# 部署SQL迁移
info "部署数据库架构..."
supabase db push

if [ $? -ne 0 ]; then
    error "部署数据库架构失败"
    exit 1
fi

# 验证新表是否已创建
info "验证订阅系统表结构..."
SQL_CHECK="SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'billing_issues') as billing_issues_exists, 
           EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'payments') as payments_exists;"

TABLES_CHECK=$(supabase db execute --single-line "$SQL_CHECK")
if [[ $TABLES_CHECK == *"billing_issues_exists|t"* && $TABLES_CHECK == *"payments_exists|t"* ]]; then
    info "账单问题表和支付记录表已成功创建"
else
    warn "账单问题表或支付记录表可能未正确创建。请检查数据库迁移日志。"
    info "手动应用billing_issues和payments表..."
    supabase db execute < ./migrations/20240820000000_add_billing_issues_and_payments.sql
    if [ $? -ne 0 ]; then
        warn "手动应用表创建脚本失败，请检查错误并手动创建这些表"
    else
        info "已手动应用表创建脚本"
    fi
fi

# 执行种子SQL文件
info "执行种子数据脚本..."
supabase db execute < ./seed.sql

if [ $? -ne 0 ]; then
    warn "执行种子数据脚本可能有问题，但将继续"
fi

# 检查函数是否存在
info "验证账单和支付处理函数..."
SQL_CHECK_FUNC="SELECT EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'create_payment_record') as payment_func_exists,
                EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'handle_billing_issue') as billing_func_exists;"

FUNC_CHECK=$(supabase db execute --single-line "$SQL_CHECK_FUNC")
if [[ $FUNC_CHECK == *"payment_func_exists|t"* && $FUNC_CHECK == *"billing_func_exists|t"* ]]; then
    info "支付和账单处理函数已成功创建"
else
    warn "支付记录或账单问题处理函数可能未正确创建。将再次应用迁移..."
    supabase db execute < ./migrations/20240820000000_add_billing_issues_and_payments.sql
    if [ $? -ne 0 ]; then
        warn "手动应用函数创建脚本失败，请检查错误并手动创建这些函数"
    else
        info "已手动应用函数创建脚本"
    fi
fi

# 验证subscriptions表的新字段
info "验证订阅表的新增字段..."
SQL_CHECK_FIELDS="SELECT 
  column_name IN ('period_type', 'entitlements', 'is_family_share', 'country_code', 
                 'presented_offering_id', 'has_billing_issue', 'billing_issue_detected_at',
                 'is_trial_conversion') as column_exists,
  column_name
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'subscriptions'
  AND column_name IN ('period_type', 'entitlements', 'is_family_share', 'country_code', 
                     'presented_offering_id', 'has_billing_issue', 'billing_issue_detected_at',
                     'is_trial_conversion');"

FIELDS_CHECK=$(supabase db execute "$SQL_CHECK_FIELDS")
MISSING_FIELDS=0

if [[ $FIELDS_CHECK == *"column_exists"* ]]; then
    info "检测到订阅表字段："
    echo "$FIELDS_CHECK" | grep column_name | awk '{print $2}'
    
    # 检查是否所有字段都存在
    REQUIRED_FIELDS=("period_type" "entitlements" "is_family_share" "country_code" "presented_offering_id" "has_billing_issue" "billing_issue_detected_at" "is_trial_conversion")
    
    for field in "${REQUIRED_FIELDS[@]}"; do
        if [[ ! $FIELDS_CHECK == *"$field"* ]]; then
            warn "缺少字段: $field"
            MISSING_FIELDS=1
        fi
    done
    
    if [ $MISSING_FIELDS -eq 1 ]; then
        warn "订阅表缺少部分新字段，将再次应用迁移..."
        supabase db execute < ./migrations/20240820000000_add_billing_issues_and_payments.sql
        if [ $? -ne 0 ]; then
            warn "手动应用字段创建脚本失败，请检查错误并手动添加这些字段"
        else
            info "已手动应用字段创建脚本"
        fi
    else
        info "订阅表的所有新字段已成功创建"
    fi
else
    warn "无法检测到任何订阅表的新字段，将再次应用迁移..."
    supabase db execute < ./migrations/20240820000000_add_billing_issues_and_payments.sql
    if [ $? -ne 0 ]; then
        warn "手动应用字段创建脚本失败，请检查错误并手动添加这些字段"
    else
        info "已手动应用字段创建脚本"
    fi
fi

# 部署Edge函数
info "部署RevenueCat Webhook函数..."
supabase functions deploy revenuecat-webhook

if [ $? -ne 0 ]; then
    error "部署RevenueCat Webhook函数失败"
    exit 1
fi

# 部署订阅检查函数
info "部署订阅状态检查函数..."
supabase functions deploy revenuecat-check

if [ $? -ne 0 ]; then
    error "部署订阅状态检查函数失败"
    exit 1
fi

# 设置RevenueCat Webhook密钥
if [ -z "$REVENUECAT_WEBHOOK_SECRET" ]; then
    warn "../.env文件中未设置REVENUECAT_WEBHOOK_SECRET，将使用现有值或默认值"
else
    info "设置RevenueCat Webhook密钥..."
    supabase secrets set REVENUECAT_WEBHOOK_SECRET="$REVENUECAT_WEBHOOK_SECRET"
    if [ $? -ne 0 ]; then
        error "设置RevenueCat Webhook密钥失败"
        exit 1
    fi
fi

# 设置订阅检查API密钥
if [ -z "$CHECK_API_SECRET" ]; then
    # 生成随机密钥并写回../.env
    CHECK_API_SECRET=$(openssl rand -base64 32)
    info "../.env中未设置CHECK_API_SECRET，已生成随机API密钥并写入../.env"
    echo "CHECK_API_SECRET=\"$CHECK_API_SECRET\"" >> ../.env
fi

info "设置订阅检查API密钥..."
supabase secrets set CHECK_API_SECRET="$CHECK_API_SECRET"

if [ $? -ne 0 ]; then
    error "设置订阅检查API密钥失败"
    exit 1
fi

# 获取并显示函数URL
WEBHOOK_URL=$(supabase functions get-url revenuecat-webhook)
CHECK_URL=$(supabase functions get-url revenuecat-check)

info "RevenueCat Webhook URL: $WEBHOOK_URL"
info "请在RevenueCat开发者控制台配置此URL作为Webhook回调地址"

info "订阅检查API URL: $CHECK_URL"
info "订阅检查API密钥: $CHECK_API_SECRET"
info "请妥善保管此密钥，用于访问订阅检查API"

info "检查订阅表中是否存在新添加的cancellation_reason_description字段..."
hasField=$(curl -s -X POST "$SUPABASE_URL/rest/v1/rpc/has_column" \
  -H "apikey: $SERVICE_KEY" \
  -H "Authorization: Bearer $SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"table_name": "subscriptions", "column_name": "cancellation_reason_description"}')

if [ "$hasField" = "true" ]; then
  echo "cancellation_reason_description字段已成功添加!"
else
  echo "警告：无法确认cancellation_reason_description字段添加成功，请手动检查。"
fi

info "检查订阅表中是否存在暂停相关字段..."
pauseFields="is_paused,paused_at,auto_resume_at"
for field in ${pauseFields//,/ }; do
  hasField=$(curl -s -X POST "$SUPABASE_URL/rest/v1/rpc/has_column" \
    -H "apikey: $SERVICE_KEY" \
    -H "Authorization: Bearer $SERVICE_KEY" \
    -H "Content-Type: application/json" \
    -d "{\"table_name\": \"subscriptions\", \"column_name\": \"$field\"}")
  
  if [ "$hasField" = "true" ]; then
    echo "订阅暂停字段 $field 已成功添加!"
  else
    echo "警告：订阅暂停字段 $field 可能未成功添加，请手动检查。"
  fi
done

# 检查暂停检查函数是否存在
SQL_CHECK_PAUSE_FUNC="SELECT EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'check_paused_subscriptions') as pause_func_exists;"
PAUSE_FUNC_CHECK=$(supabase db execute --single-line "$SQL_CHECK_PAUSE_FUNC")

if [[ $PAUSE_FUNC_CHECK == *"pause_func_exists|t"* ]]; then
  info "订阅暂停自动恢复函数已成功创建"
else
  warn "订阅暂停自动恢复函数可能未正确创建。将再次应用迁移..."
  supabase db execute < ./migrations/20240831000000_add_subscription_pause_fields.sql
  if [ $? -ne 0 ]; then
    warn "手动应用函数创建脚本失败，请检查错误并手动创建暂停恢复函数"
  else
    info "已手动应用暂停相关函数创建脚本"
  fi
fi

info "部署完成！"
info "请在RevenueCat开发者控制台配置以下产品ID映射:"
echo "--------------------------"
echo "月度订阅: monthly_subscription"
echo "年度订阅: annual_subscription"
echo "--------------------------"
info "确保RevenueCat中的产品ID与数据库中的一致！"

# 显示设置定时任务的建议
info "建议设置定时任务，定期运行订阅状态检查函数。示例cron表达式:"
echo "0 0 * * * curl -X POST \"$CHECK_URL/run-all\" -H \"Authorization: Bearer $CHECK_API_SECRET\" -H \"Content-Type: application/json\" -d '{\"dry_run\": false}'" 