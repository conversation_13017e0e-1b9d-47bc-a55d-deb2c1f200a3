#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}========== 本地 Supabase 启动脚本 ==========${NC}"

# 1. 停止当前运行的Supabase实例
echo -e "${YELLOW}停止当前运行的Supabase...${NC}"
supabase stop

# 2. 确保环境变量设置正确
echo -e "${YELLOW}配置环境变量...${NC}"
export SUPABASE_EDGE_RUNTIME_VERSION="" # 禁用Edge Functions容器

# 3. 启动Supabase，但不包括Edge函数（先启动数据库）
echo -e "${YELLOW}启动Supabase数据库和API...${NC}"
supabase start --exclude edge-runtime

# 4. 等待服务启动，使用更可靠的检查方法
echo -e "${YELLOW}等待Supabase服务启动(最多60秒)...${NC}"
MAX_WAIT=60
for i in $(seq 1 $MAX_WAIT); do
  echo -n "."
  # 尝试通过API URL检查服务状态
  if curl -s http://127.0.0.1:54321/rest/v1/ -H "Content-Type: application/json" > /dev/null 2>&1; then
    echo -e "\n${GREEN}API服务已准备就绪!${NC}"
    SERVICE_READY=true
    break
  fi
  
  # 如果API还没准备好，检查Studio页面
  if curl -s http://127.0.0.1:54323 > /dev/null 2>&1; then
    echo -e "\n${YELLOW}Studio已启动，但API可能还未就绪${NC}"
  fi
  
  sleep 1
done

# 即使检查失败，我们也继续执行，因为有些服务可能已经启动成功
echo -e "${GREEN}Supabase服务启动...${NC}"

# 5. 运行migration
echo -e "${YELLOW}运行数据库迁移...${NC}"
supabase db reset || {
  echo -e "${YELLOW}迁移执行失败，尝试继续...${NC}"
}

# 6. 显示状态信息
echo -e "${YELLOW}获取Supabase状态...${NC}"
supabase status

# 7. 提供测试Edge Function的方法
echo -e "${GREEN}========== Supabase 已成功启动 ==========${NC}"
echo -e "${YELLOW}如需测试Edge函数，可使用以下命令:${NC}"
echo -e "cd $(pwd)"
echo -e "supabase functions serve revenuecat-webhook --no-verify-jwt"

echo -e "${GREEN}========== 完成 ==========${NC}" 