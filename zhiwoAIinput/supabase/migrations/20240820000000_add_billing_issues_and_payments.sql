-- 20240820000000_add_billing_issues_and_payments.sql
-- 添加账单问题和支付记录表

-- 1. 创建账单问题记录表
CREATE TABLE IF NOT EXISTS public.billing_issues (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES public.subscriptions(id) ON DELETE CASCADE,
  transaction_id TEXT,
  detected_at TIMESTAMP WITH TIME ZONE NOT NULL,
  resolved BOOLEAN DEFAULT false,
  resolution_date TIMESTAMP WITH TIME ZONE,
  details JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

COMMENT ON TABLE public.billing_issues IS '记录订阅过程中的账单问题';
COMMENT ON COLUMN public.billing_issues.user_id IS '用户ID';
COMMENT ON COLUMN public.billing_issues.subscription_id IS '订阅记录ID';
COMMENT ON COLUMN public.billing_issues.transaction_id IS '交易ID';
COMMENT ON COLUMN public.billing_issues.detected_at IS '问题检测时间';
COMMENT ON COLUMN public.billing_issues.resolved IS '是否已解决';
COMMENT ON COLUMN public.billing_issues.resolution_date IS '解决时间';
COMMENT ON COLUMN public.billing_issues.details IS '问题详情';

-- 2. 创建支付记录表
CREATE TABLE IF NOT EXISTS public.payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES public.subscriptions(id) ON DELETE CASCADE,
  amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL,
  status TEXT NOT NULL,
  payment_method TEXT,
  payment_provider TEXT NOT NULL,
  payment_provider_payment_id TEXT,
  description TEXT,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

COMMENT ON TABLE public.payments IS '记录用户支付信息';
COMMENT ON COLUMN public.payments.user_id IS '用户ID';
COMMENT ON COLUMN public.payments.subscription_id IS '相关订阅ID';
COMMENT ON COLUMN public.payments.amount IS '支付金额';
COMMENT ON COLUMN public.payments.currency IS '货币类型';
COMMENT ON COLUMN public.payments.status IS '支付状态';
COMMENT ON COLUMN public.payments.payment_method IS '支付方式';
COMMENT ON COLUMN public.payments.payment_provider IS '支付提供商';
COMMENT ON COLUMN public.payments.payment_provider_payment_id IS '支付提供商支付ID';
COMMENT ON COLUMN public.payments.description IS '支付描述';
COMMENT ON COLUMN public.payments.metadata IS '支付元数据';

-- 3. 向subscriptions表添加新字段
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS period_type TEXT;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS entitlements JSONB DEFAULT '[]'::jsonb;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS is_family_share BOOLEAN DEFAULT false;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS country_code TEXT;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS presented_offering_id TEXT;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS has_billing_issue BOOLEAN DEFAULT false;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS billing_issue_detected_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS is_trial_conversion BOOLEAN DEFAULT false;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS transfer_from_user_id UUID REFERENCES public.profiles(id);
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS transfer_from_subscription_id UUID REFERENCES public.subscriptions(id);
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS expiration_reason TEXT;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS expiration_reason_description TEXT;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS is_refunded BOOLEAN DEFAULT false;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS refunded_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS refund_amount DECIMAL(10, 2);
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS refund_currency TEXT;

COMMENT ON COLUMN public.subscriptions.period_type IS '订阅周期类型，如NORMAL, TRIAL, INTRO等';
COMMENT ON COLUMN public.subscriptions.entitlements IS '用户订阅权益列表';
COMMENT ON COLUMN public.subscriptions.is_family_share IS '是否为家庭共享订阅';
COMMENT ON COLUMN public.subscriptions.country_code IS '购买国家/地区代码';
COMMENT ON COLUMN public.subscriptions.presented_offering_id IS '展示的优惠ID';
COMMENT ON COLUMN public.subscriptions.has_billing_issue IS '是否存在账单问题';
COMMENT ON COLUMN public.subscriptions.billing_issue_detected_at IS '账单问题检测时间';
COMMENT ON COLUMN public.subscriptions.is_trial_conversion IS '是否为试用转付费';
COMMENT ON COLUMN public.subscriptions.transfer_from_user_id IS '订阅转移来源用户ID';
COMMENT ON COLUMN public.subscriptions.transfer_from_subscription_id IS '订阅转移来源订阅ID';
COMMENT ON COLUMN public.subscriptions.expiration_reason IS '订阅过期原因';
COMMENT ON COLUMN public.subscriptions.expiration_reason_description IS '订阅过期原因描述';
COMMENT ON COLUMN public.subscriptions.is_refunded IS '是否已退款';
COMMENT ON COLUMN public.subscriptions.refunded_at IS '退款时间';
COMMENT ON COLUMN public.subscriptions.refund_amount IS '退款金额';
COMMENT ON COLUMN public.subscriptions.refund_currency IS '退款货币';

-- 4. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_billing_issues_user_id ON public.billing_issues(user_id);
CREATE INDEX IF NOT EXISTS idx_billing_issues_subscription_id ON public.billing_issues(subscription_id);
CREATE INDEX IF NOT EXISTS idx_billing_issues_transaction_id ON public.billing_issues(transaction_id);
CREATE INDEX IF NOT EXISTS idx_billing_issues_resolved ON public.billing_issues(resolved);

CREATE INDEX IF NOT EXISTS idx_payments_user_id ON public.payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_subscription_id ON public.payments(subscription_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON public.payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_payment_provider_payment_id ON public.payments(payment_provider_payment_id);

CREATE INDEX IF NOT EXISTS idx_subscriptions_period_type ON public.subscriptions(period_type);
CREATE INDEX IF NOT EXISTS idx_subscriptions_country_code ON public.subscriptions(country_code);

-- 5. 更新createPaymentRecord函数以支持新的payments表
CREATE OR REPLACE FUNCTION public.create_payment_record(
  p_user_id UUID,
  p_subscription_id UUID,
  p_amount DECIMAL,
  p_currency TEXT,
  p_payment_method TEXT,
  p_transaction_id TEXT,
  p_description TEXT,
  p_metadata JSONB
) RETURNS UUID AS $$
DECLARE
  v_payment_id UUID;
BEGIN
  -- 检查是否已存在相同交易ID的支付记录
  IF p_transaction_id IS NOT NULL THEN
    SELECT id INTO v_payment_id
    FROM public.payments
    WHERE payment_provider_payment_id = p_transaction_id;
    
    IF v_payment_id IS NOT NULL THEN
      RETURN v_payment_id; -- 返回已存在的支付记录ID
    END IF;
  END IF;
  
  -- 创建新的支付记录
  INSERT INTO public.payments (
    user_id,
    subscription_id,
    amount,
    currency,
    status,
    payment_method,
    payment_provider,
    payment_provider_payment_id,
    description,
    metadata
  ) VALUES (
    p_user_id,
    p_subscription_id,
    p_amount,
    p_currency,
    'succeeded',
    p_payment_method,
    'revenuecat',
    p_transaction_id,
    p_description,
    p_metadata
  ) RETURNING id INTO v_payment_id;
  
  RETURN v_payment_id;
END;
$$ LANGUAGE plpgsql;

-- 6. 更新处理账单问题的函数
CREATE OR REPLACE FUNCTION public.handle_billing_issue(
  p_user_id UUID,
  p_subscription_id UUID,
  p_transaction_id TEXT,
  p_details JSONB
) RETURNS UUID AS $$
DECLARE
  v_issue_id UUID;
BEGIN
  -- 检查是否已存在相同交易ID的账单问题记录
  IF p_transaction_id IS NOT NULL THEN
    SELECT id INTO v_issue_id
    FROM public.billing_issues
    WHERE transaction_id = p_transaction_id AND resolved = false;
    
    IF v_issue_id IS NOT NULL THEN
      -- 更新现有记录
      UPDATE public.billing_issues
      SET 
        details = p_details,
        updated_at = now()
      WHERE id = v_issue_id;
      
      RETURN v_issue_id;
    END IF;
  END IF;
  
  -- 创建新的账单问题记录
  INSERT INTO public.billing_issues (
    user_id,
    subscription_id,
    transaction_id,
    detected_at,
    resolved,
    details
  ) VALUES (
    p_user_id,
    p_subscription_id,
    p_transaction_id,
    now(),
    false,
    p_details
  ) RETURNING id INTO v_issue_id;
  
  RETURN v_issue_id;
END;
$$ LANGUAGE plpgsql;

-- 7. 授予适当的权限
ALTER TABLE public.billing_issues ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

-- 创建策略，允许用户查看自己的账单问题记录
CREATE POLICY billing_issues_policy_select ON public.billing_issues 
FOR SELECT USING (auth.uid() = user_id);

-- 创建策略，允许用户查看自己的支付记录
CREATE POLICY payments_policy_select ON public.payments 
FOR SELECT USING (auth.uid() = user_id);

-- 创建服务角色策略
CREATE POLICY billing_issues_service_policy ON public.billing_issues 
FOR ALL TO service_role USING (true);

CREATE POLICY payments_service_policy ON public.payments 
FOR ALL TO service_role USING (true); 