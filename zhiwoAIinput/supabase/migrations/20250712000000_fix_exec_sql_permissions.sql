-- 删除旧函数
drop function if exists public.exec_sql(text, jsonb);

-- 创建新函数
create or replace function public.exec_sql(
  query text,
  params text[] default array[]::text[]
)
returns jsonb
language plpgsql
security definer  -- 使用创建者的权限执行
set search_path = public  -- 设置搜索路径
as $$
declare
  result jsonb;
  dynamic_query text;
  i int;
begin
  -- 替换参数
  dynamic_query := query;
  if array_length(params, 1) > 0 then
    for i in 1..array_length(params, 1) loop
      dynamic_query := replace(dynamic_query, '$' || i::text, quote_literal(params[i]));
    end loop;
  end if;

  -- 执行查询
  execute dynamic_query into result;
  return coalesce(result, '{}'::jsonb);
exception
  when others then
    raise exception '执行查询时出错: %', SQLERRM;
end;
$$;

-- 设置函数权限
revoke all on function public.exec_sql(text, text[]) from public;
grant execute on function public.exec_sql(text, text[]) to service_role;  -- 只允许服务角色执行
grant execute on function public.exec_sql(text, text[]) to authenticated;  -- 允许已认证用户执行

-- 添加注释
comment on function public.exec_sql(text, text[]) is '执行动态SQL查询的辅助函数，用于维护任务'; 