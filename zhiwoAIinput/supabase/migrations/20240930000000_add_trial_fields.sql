-- 20240930000000_add_trial_fields.sql
-- 添加试用期相关字段

-- 向subscriptions表添加新字段
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS is_trial BOOLEAN DEFAULT false;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS trial_start_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS trial_end_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS trial_converted BOOLEAN DEFAULT false;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS trial_converted_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS trial_type TEXT;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS trial_duration_days INTEGER;

-- 添加字段注释
COMMENT ON COLUMN public.subscriptions.is_trial IS '是否为试用期订阅';
COMMENT ON COLUMN public.subscriptions.trial_start_at IS '试用期开始时间';
COMMENT ON COLUMN public.subscriptions.trial_end_at IS '试用期结束时间';
COMMENT ON COLUMN public.subscriptions.trial_converted IS '试用期是否已转换为付费';
COMMENT ON COLUMN public.subscriptions.trial_converted_at IS '试用期转换为付费的时间';
COMMENT ON COLUMN public.subscriptions.trial_type IS '试用类型';
COMMENT ON COLUMN public.subscriptions.trial_duration_days IS '试用期时长(天)';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_subscriptions_is_trial ON public.subscriptions(is_trial) 
  WHERE is_trial = true;
CREATE INDEX IF NOT EXISTS idx_subscriptions_trial_converted ON public.subscriptions(trial_converted) 
  WHERE trial_converted = true;

-- 创建函数用于开始试用期
CREATE OR REPLACE FUNCTION public.start_subscription_trial(
  p_user_id UUID,
  p_product_id TEXT,
  p_trial_end_date TIMESTAMP WITH TIME ZONE,
  p_entitlements JSONB DEFAULT NULL,
  p_store TEXT DEFAULT NULL,
  p_details JSONB DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  v_subscription_id UUID;
  v_trial_start_date TIMESTAMP WITH TIME ZONE := now();
  v_trial_duration_days INTEGER;
BEGIN
  -- 计算试用天数
  v_trial_duration_days := EXTRACT(DAY FROM p_trial_end_date - v_trial_start_date)::INTEGER;
  
  -- 创建新的试用订阅记录
  INSERT INTO public.subscriptions (
    user_id,
    product_id,
    status,
    is_trial,
    trial_start_at,
    trial_end_at,
    trial_duration_days,
    trial_type,
    period_type,
    entitlements,
    store,
    environment,
    transaction_id,
    original_transaction_id,
    purchase_date,
    expires_at,
    event_type,
    created_at,
    updated_at
  ) VALUES (
    p_user_id,
    p_product_id,
    'active',
    true,
    v_trial_start_date,
    p_trial_end_date,
    v_trial_duration_days,
    'standard',
    'TRIAL',
    p_entitlements,
    p_store,
    p_details->>'environment',
    p_details->>'transaction_id',
    p_details->>'original_transaction_id',
    v_trial_start_date,
    p_trial_end_date,
    'TRIAL_STARTED',
    now(),
    now()
  ) RETURNING id INTO v_subscription_id;
  
  -- 记录试用开始事件
  INSERT INTO public.subscription_events (
    user_id,
    subscription_id,
    event_type,
    event_timestamp,
    product_id,
    event_data
  ) VALUES (
    p_user_id,
    v_subscription_id,
    'TRIAL_STARTED',
    now(),
    p_product_id,
    jsonb_build_object(
      'trial_start_at', v_trial_start_date,
      'trial_end_at', p_trial_end_date,
      'trial_duration_days', v_trial_duration_days,
      'details', p_details
    )
  );
  
  -- 更新用户VIP状态(在试用期间也是VIP)
  UPDATE public.profiles
  SET 
    is_vip = true,
    vip_since = COALESCE(vip_since, v_trial_start_date),
    vip_product_id = p_product_id,
    vip_expires_at = p_trial_end_date,
    vip_is_trial = true,
    vip_updated_at = now(),
    updated_at = now()
  WHERE id = p_user_id;
  
  RETURN v_subscription_id;
END;
$$ LANGUAGE plpgsql;

-- 为profiles表添加试用期相关字段
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS vip_is_trial BOOLEAN DEFAULT false;
COMMENT ON COLUMN public.profiles.vip_is_trial IS '用户当前VIP是否为试用期'; 