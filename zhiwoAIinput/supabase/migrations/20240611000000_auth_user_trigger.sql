-- 创建用户注册触发器函数
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    email, 
    username,
    avatar_url,
    is_vip,
    custom_templates_count,
    voice_minutes_used,
    voice_minutes_limit,
    api_key_type,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.email,
    NEW.email,
    coalesce(NEW.raw_user_meta_data->>'avatar_url', ''),
    false,
    0,
    0, 
    0,
    'free',
    now(),
    now()
  )
  ON CONFLICT (id) DO NOTHING;
  
  -- 同时创建用户设置记录
  INSERT INTO public.user_settings (
    user_id,
    language,
    theme,
    default_text_model,
    default_voice_model,
    sync_enabled,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    'zh-CN',
    'light',
    'qwen2.5-7b',
    'native',
    true,
    now(),
    now()
  )
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 为auth.users表创建触发器
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 为已存在的用户创建profiles记录
INSERT INTO public.profiles (
  id, 
  email, 
  username,
  avatar_url,
  is_vip,
  custom_templates_count,
  voice_minutes_used,
  voice_minutes_limit,
  api_key_type,
  created_at,
  updated_at
)
SELECT 
  id,
  email,
  email,
  '',
  false,
  0,
  0,
  0,
  'free',
  now(),
  now()
FROM auth.users
WHERE NOT EXISTS (
  SELECT 1 FROM public.profiles WHERE profiles.id = users.id
)
ON CONFLICT (id) DO NOTHING;

-- 为已存在的用户创建user_settings记录
INSERT INTO public.user_settings (
  user_id,
  language,
  theme,
  default_text_model,
  default_voice_model,
  sync_enabled,
  created_at,
  updated_at
)
SELECT 
  id,
  'zh-CN',
  'light',
  'qwen2.5-7b',
  'native',
  true,
  now(),
  now()
FROM auth.users
WHERE NOT EXISTS (
  SELECT 1 FROM public.user_settings WHERE user_settings.user_id = users.id
)
ON CONFLICT (user_id) DO NOTHING;