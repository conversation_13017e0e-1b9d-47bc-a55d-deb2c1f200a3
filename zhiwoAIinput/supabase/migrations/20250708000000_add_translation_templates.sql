-- 添加中文翻译模板
INSERT INTO public.style_templates 
(name, description, name_key, description_key, prompt_text, 
 is_system, color, icon, position, is_vip_only, category, semantic_id)
VALUES
('中文翻译（简体）', '精准翻译各种语言为规范简体中文，保持原意并符合中文表达习惯。', 
 'template.zhCnTrans.name', 'template.zhCnTrans.description',
 '翻译成标准简体中文。保持原文含义，使用规范汉字，符合中文表达习惯。

示例：
输入：Hello, how are you today?
输出：你好，你今天怎么样？',
 true, '#DC2626', '🇨🇳', 25, true, 'translation', 'zh_cn_translation'),

('中文翻译（繁体）', '精准翻译各种语言为标准繁体中文，保持原意并符合繁体中文表达习惯。', 
 'template.zhTwTrans.name', 'template.zhTwTrans.description',
 '翻譯成標準繁體中文。保持原文含義，使用標準繁體漢字，符合繁體中文表達習慣。

示例：
輸入：Hello, how are you today?
輸出：你好，你今天怎麼樣？',
 true, '#DC2626', '🇭🇰', 26, true, 'translation', 'zh_tw_translation')
ON CONFLICT (semantic_id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  name_key = EXCLUDED.name_key,
  description_key = EXCLUDED.description_key,
  prompt_text = EXCLUDED.prompt_text,
  is_system = EXCLUDED.is_system,
  color = EXCLUDED.color,
  icon = EXCLUDED.icon,
  position = EXCLUDED.position,
  is_vip_only = EXCLUDED.is_vip_only,
  category = EXCLUDED.category,
  updated_at = now(); 