-- 用户模板偏好设置表
-- 用于存储用户对模板的个性化设置（显示/隐藏、排序等）

CREATE TABLE user_template_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  template_id VARCHAR(255) NOT NULL,
  template_type VARCHAR(20) NOT NULL CHECK (template_type IN ('system', 'user')),
  is_hidden BOOLEAN DEFAULT false,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX idx_user_template_preferences_user_id ON user_template_preferences(user_id);
CREATE INDEX idx_user_template_preferences_template_id ON user_template_preferences(template_id);
CREATE INDEX idx_user_template_preferences_user_template ON user_template_preferences(user_id, template_id);

-- 确保每个用户对每个模板只有一条偏好记录
CREATE UNIQUE INDEX idx_user_template_preferences_unique ON user_template_preferences(user_id, template_id);

-- 创建更新时间自动更新的触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_template_preferences_updated_at 
  BEFORE UPDATE ON user_template_preferences 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 设置RLS（行级安全）策略
ALTER TABLE user_template_preferences ENABLE ROW LEVEL SECURITY;

-- 用户只能访问自己的偏好设置
CREATE POLICY "用户只能访问自己的模板偏好" ON user_template_preferences
  FOR ALL USING (auth.uid() = user_id);

-- 允许认证用户插入自己的偏好设置
CREATE POLICY "用户可以创建自己的模板偏好" ON user_template_preferences
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 允许用户更新自己的偏好设置
CREATE POLICY "用户可以更新自己的模板偏好" ON user_template_preferences
  FOR UPDATE USING (auth.uid() = user_id);

-- 允许用户删除自己的偏好设置
CREATE POLICY "用户可以删除自己的模板偏好" ON user_template_preferences
  FOR DELETE USING (auth.uid() = user_id); 