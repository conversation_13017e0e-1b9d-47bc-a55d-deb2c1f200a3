-- 添加语义化ID字段到style_templates表
-- 这将替代硬编码UUID，使用可读的语义化标识符

-- 添加semantic_id字段
ALTER TABLE public.style_templates 
ADD COLUMN IF NOT EXISTS semantic_id TEXT UNIQUE;

-- 添加注释
COMMENT ON COLUMN public.style_templates.semantic_id IS '语义化ID，用于稳定地识别模板，避免UUID变动问题';

-- 为semantic_id创建索引
CREATE INDEX IF NOT EXISTS idx_style_templates_semantic_id ON public.style_templates(semantic_id);

-- 为现有的系统模板添加语义化ID（如果存在的话）
UPDATE public.style_templates 
SET semantic_id = CASE 
  WHEN name = '发邮件' THEN 'email'
  WHEN name = '日常发信息' THEN 'chat'
  WHEN name = '做notes记录' THEN 'notes'
  WHEN name = 'emoji表情' THEN 'emoji'
  WHEN name = '小红书文案' THEN 'xiaohongshu'
  WHEN name = '朋友圈文案' THEN 'moments'
  WHEN name = '英语翻译' THEN 'en_translation'
  WHEN name = '日语翻译' THEN 'ja_translation'
  WHEN name = '韩语翻译' THEN 'ko_translation'
  WHEN name = '印尼语翻译' THEN 'id_translation'
  WHEN name = '德语翻译' THEN 'de_translation'
  WHEN name = '法语翻译' THEN 'fr_translation'
  WHEN name = '西班牙语翻译' THEN 'es_translation'
  WHEN name = 'Recraft生图' THEN 'recraft'
  WHEN name = 'Midjourney V7' THEN 'midjourney_v7'
  WHEN name = 'Stable Diffusion' THEN 'stable_diffusion'
  WHEN name = '即梦AI' THEN 'jimeng_ai'
  WHEN name = '正式公文' THEN 'official'
  WHEN name = '学术论文' THEN 'academic'
  WHEN name = '新闻报道' THEN 'news'
  WHEN name = '创意文案' THEN 'creative'
  WHEN name = '口语化表达' THEN 'casual'
  ELSE NULL
END
WHERE is_system = true AND semantic_id IS NULL; 