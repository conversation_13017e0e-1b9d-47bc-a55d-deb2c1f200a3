-- 20240831000000_add_subscription_pause_fields.sql
-- 添加订阅暂停相关字段

-- 添加暂停状态相关字段到subscriptions表
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS is_paused BOOLEAN DEFAULT false;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS paused_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS auto_resume_at TIMESTAMP WITH TIME ZONE;

-- 添加字段注释
COMMENT ON COLUMN public.subscriptions.is_paused IS '订阅是否处于暂停状态(Google Play商店特有)';
COMMENT ON COLUMN public.subscriptions.paused_at IS '订阅暂停时间';
COMMENT ON COLUMN public.subscriptions.auto_resume_at IS '订阅自动恢复时间(Google Play商店特有)';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_subscriptions_is_paused ON public.subscriptions(is_paused) 
  WHERE is_paused = true;
CREATE INDEX IF NOT EXISTS idx_subscriptions_auto_resume_at ON public.subscriptions(auto_resume_at) 
  WHERE auto_resume_at IS NOT NULL;

-- 创建函数用于自动恢复暂停订阅
CREATE OR REPLACE FUNCTION public.check_paused_subscriptions()
RETURNS void AS $$
DECLARE
  r RECORD;
BEGIN
  FOR r IN 
    SELECT id, user_id, auto_resume_at 
    FROM public.subscriptions 
    WHERE is_paused = true 
      AND auto_resume_at IS NOT NULL 
      AND auto_resume_at <= now()
  LOOP
    -- 自动恢复订阅
    UPDATE public.subscriptions SET
      status = 'active',
      is_paused = false,
      updated_at = now()
    WHERE id = r.id;
    
    -- 记录自动恢复事件
    INSERT INTO public.subscription_events (
      user_id,
      subscription_id,
      event_type,
      event_timestamp,
      event_data
    ) VALUES (
      r.user_id,
      r.id,
      'AUTO_RESUME',
      now(),
      jsonb_build_object(
        'auto_resume_at', r.auto_resume_at,
        'source', 'system'
      )
    );
    
    -- 确保用户VIP状态是active
    UPDATE public.profiles SET
      is_vip = true,
      vip_updated_at = now(),
      updated_at = now()
    WHERE id = r.user_id;
    
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 示例cron调用(实际调用需要在应用中实现):
-- SELECT public.check_paused_subscriptions(); 