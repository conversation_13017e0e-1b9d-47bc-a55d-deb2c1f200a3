-- 迁移文件：添加vip_since列到profiles表
-- 版本：20250506000000

-- 检查profiles表中是否存在vip_since列
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'vip_since'
  ) THEN
    -- 添加vip_since列到profiles表
    ALTER TABLE profiles ADD COLUMN vip_since TIMESTAMP WITH TIME ZONE;
    
    -- 为已有的VIP用户设置初始值
    UPDATE profiles 
    SET vip_since = COALESCE(vip_updated_at, created_at, NOW()) 
    WHERE is_vip = true AND vip_since IS NULL;
    
    RAISE NOTICE 'vip_since列已添加到profiles表';
  ELSE
    RAISE NOTICE 'vip_since列已存在于profiles表中，跳过添加';
  END IF;
END $$; 