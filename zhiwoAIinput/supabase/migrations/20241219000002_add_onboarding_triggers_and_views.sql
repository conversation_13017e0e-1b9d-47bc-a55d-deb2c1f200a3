-- Migration: Add triggers and views for onboarding surveys
-- Created at: 2024-12-19 00:00:02
-- Description: 为onboarding问卷表添加触发器和统计视图

-- 添加触发器自动更新updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_onboarding_surveys_updated_at
    BEFORE UPDATE ON onboarding_surveys
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 创建统计视图（修复聚合函数错误）
CREATE OR REPLACE VIEW onboarding_survey_stats AS
SELECT 
  source,
  COUNT(*) as total_count,
  COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as registered_users,
  COUNT(CASE WHEN user_id IS NULL THEN 1 END) as anonymous_users,
  DATE_TRUNC('day', completed_at) as completion_date
FROM onboarding_surveys
GROUP BY source, DATE_TRUNC('day', completed_at)
ORDER BY completion_date DESC, total_count DESC;

COMMENT ON VIEW onboarding_survey_stats IS 'onboarding问卷统计视图，按来源和日期分组';

-- 创建详细的使用场景统计视图
CREATE OR REPLACE VIEW onboarding_use_cases_stats AS
SELECT 
  source,
  use_case,
  COUNT(*) as count,
  DATE_TRUNC('day', completed_at) as completion_date
FROM onboarding_surveys,
LATERAL jsonb_array_elements_text(use_cases) as use_case
GROUP BY source, use_case, DATE_TRUNC('day', completed_at)
ORDER BY completion_date DESC, count DESC;

COMMENT ON VIEW onboarding_use_cases_stats IS '使用场景统计视图，展开JSON数组';

-- 创建详细的模板选择统计视图
CREATE OR REPLACE VIEW onboarding_templates_stats AS
SELECT 
  source,
  template_id,
  COUNT(*) as count,
  DATE_TRUNC('day', completed_at) as completion_date
FROM onboarding_surveys,
LATERAL jsonb_array_elements_text(selected_templates) as template_id
GROUP BY source, template_id, DATE_TRUNC('day', completed_at)
ORDER BY completion_date DESC, count DESC;

COMMENT ON VIEW onboarding_templates_stats IS '模板选择统计视图，展开JSON数组';

-- 创建管理员查看所有数据的视图
CREATE OR REPLACE VIEW admin_onboarding_overview AS
SELECT 
  id,
  CASE 
    WHEN user_id IS NOT NULL THEN '注册用户'
    ELSE '匿名用户'
  END as user_type,
  source,
  use_cases,
  selected_templates,
  device_info->>'platform' as platform,
  device_info->>'version' as platform_version,
  completed_at,
  created_at
FROM onboarding_surveys
ORDER BY completed_at DESC;

COMMENT ON VIEW admin_onboarding_overview IS '管理员用onboarding问卷概览视图';

-- 启用视图的RLS（只有管理员可以访问）
ALTER VIEW onboarding_survey_stats OWNER TO postgres;
ALTER VIEW onboarding_use_cases_stats OWNER TO postgres;
ALTER VIEW onboarding_templates_stats OWNER TO postgres;
ALTER VIEW admin_onboarding_overview OWNER TO postgres; 