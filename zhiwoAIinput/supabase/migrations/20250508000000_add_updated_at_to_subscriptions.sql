-- 20250508000000_add_updated_at_to_subscriptions.sql
-- 添加updated_at列到subscriptions表

-- 添加updated_at列
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT now();

-- 添加列注释
COMMENT ON COLUMN public.subscriptions.updated_at IS '记录最后更新时间';

-- 为现有记录添加updated_at值
UPDATE public.subscriptions SET updated_at = created_at WHERE updated_at IS NULL;

-- 创建触发器以自动更新updated_at列
CREATE OR REPLACE FUNCTION public.update_subscriptions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 添加触发器
DROP TRIGGER IF EXISTS set_subscriptions_updated_at ON public.subscriptions;
CREATE TRIGGER set_subscriptions_updated_at
BEFORE UPDATE ON public.subscriptions
FOR EACH ROW
EXECUTE FUNCTION public.update_subscriptions_updated_at(); 