-- 20240925000000_add_subscription_extended_fields.sql
-- 添加订阅延长相关字段

-- 向subscriptions表添加新字段
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS extension_count INTEGER DEFAULT 0;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS last_extended_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS original_expiration_date TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS extension_reason TEXT;

-- 添加字段注释
COMMENT ON COLUMN public.subscriptions.extension_count IS '订阅被延长的次数';
COMMENT ON COLUMN public.subscriptions.last_extended_at IS '最近一次订阅延长时间';
COMMENT ON COLUMN public.subscriptions.original_expiration_date IS '原始过期时间(延长前)';
COMMENT ON COLUMN public.subscriptions.extension_reason IS '延长原因';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_subscriptions_extension_count ON public.subscriptions(extension_count) 
  WHERE extension_count > 0;

-- 更新函数，支持订阅延长
CREATE OR REPLACE FUNCTION public.record_subscription_extension(
  p_user_id UUID,
  p_subscription_id UUID,
  p_original_expiration_date TIMESTAMP WITH TIME ZONE,
  p_new_expiration_date TIMESTAMP WITH TIME ZONE,
  p_reason TEXT DEFAULT NULL,
  p_details JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  v_current_count INTEGER;
BEGIN
  -- 获取当前延长次数
  SELECT extension_count INTO v_current_count
  FROM public.subscriptions
  WHERE id = p_subscription_id;
  
  IF v_current_count IS NULL THEN
    v_current_count := 0;
  END IF;
  
  -- 更新订阅记录
  UPDATE public.subscriptions
  SET 
    extension_count = v_current_count + 1,
    last_extended_at = now(),
    original_expiration_date = COALESCE(original_expiration_date, p_original_expiration_date),
    expires_at = p_new_expiration_date,
    extension_reason = COALESCE(p_reason, extension_reason),
    updated_at = now()
  WHERE id = p_subscription_id;
  
  -- 记录订阅延长事件
  INSERT INTO public.subscription_events (
    user_id,
    subscription_id,
    event_type,
    event_timestamp,
    product_id,
    event_data
  ) VALUES (
    p_user_id,
    p_subscription_id,
    'SUBSCRIPTION_EXTENDED',
    now(),
    (SELECT product_id FROM public.subscriptions WHERE id = p_subscription_id),
    jsonb_build_object(
      'original_expiration_date', p_original_expiration_date,
      'new_expiration_date', p_new_expiration_date,
      'reason', p_reason,
      'extension_count', v_current_count + 1,
      'details', p_details
    )
  );
  
  -- 同步更新用户VIP信息
  UPDATE public.profiles
  SET 
    is_vip = true,
    vip_expires_at = p_new_expiration_date,
    vip_updated_at = now(),
    updated_at = now()
  WHERE id = p_user_id;
  
  RETURN true;
END;
$$ LANGUAGE plpgsql; 