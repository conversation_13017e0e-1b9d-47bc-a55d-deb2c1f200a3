-- 20241005000001_add_invoice_analysis_view.sql
-- 添加发票分析视图

-- 创建发票分析视图
CREATE OR REPLACE VIEW public.invoice_analysis AS
SELECT 
    i.user_id,
    i.product_id,
    i.status,
    i.amount,
    i.currency,
    i.issued_at,
    i.paid_at,
    i.store,
    i.environment,
    i.country_code,
    i.subscription_id,
    -- 计算从开具到支付的时间(分钟)
    CASE 
        WHEN i.paid_at IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (i.paid_at - i.issued_at)) / 60 
        ELSE NULL
    END AS minutes_to_payment,
    -- 计算发票状态转换
    CASE
        WHEN i.status = 'paid' THEN 'CONVERTED'
        WHEN i.status = 'cancelled' THEN 'ABANDONED'
        WHEN i.status = 'failed' THEN 'PAYMENT_FAILED'
        WHEN i.status = 'issued' AND i.issued_at < (now() - interval '24 hours') THEN 'EXPIRED'
        WHEN i.status = 'issued' THEN 'PENDING'
        ELSE i.status
    END AS conversion_status,
    -- 添加年月日字段用于时间分组分析
    EXTRACT(YEAR FROM i.issued_at) AS year,
    EXTRACT(MONTH FROM i.issued_at) AS month,
    EXTRACT(DAY FROM i.issued_at) AS day,
    -- 添加时间段分析
    CASE 
        WHEN EXTRACT(HOUR FROM i.issued_at) BETWEEN 0 AND 5 THEN '凌晨(0-5)'
        WHEN EXTRACT(HOUR FROM i.issued_at) BETWEEN 6 AND 11 THEN '上午(6-11)'
        WHEN EXTRACT(HOUR FROM i.issued_at) BETWEEN 12 AND 17 THEN '下午(12-17)'
        WHEN EXTRACT(HOUR FROM i.issued_at) BETWEEN 18 AND 23 THEN '晚上(18-23)'
    END AS time_period
FROM 
    public.invoices i;

-- 添加视图描述
COMMENT ON VIEW public.invoice_analysis IS '提供详细的发票分析数据，包括支付时间、转化状态和时间分布';

-- 创建发票支付转化率视图
CREATE OR REPLACE VIEW public.invoice_conversion_rate AS
SELECT
    product_id,
    environment,
    store,
    EXTRACT(YEAR FROM issued_at) AS year,
    EXTRACT(MONTH FROM issued_at) AS month,
    COUNT(*) AS total_invoices,
    SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) AS paid_invoices,
    ROUND((SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END)::NUMERIC / NULLIF(COUNT(*), 0)::NUMERIC) * 100, 2) AS conversion_rate,
    SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) AS total_revenue,
    country_code
FROM
    public.invoices
GROUP BY
    product_id,
    environment,
    store,
    year,
    month,
    country_code
ORDER BY
    year DESC,
    month DESC,
    conversion_rate DESC;

-- 添加视图描述
COMMENT ON VIEW public.invoice_conversion_rate IS '统计不同产品、环境、商店的发票支付转化率和总收入'; 