-- 20240915000000_add_refund_reversed_fields.sql
-- 添加退款撤销相关字段

-- 向subscriptions表添加新字段
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS refund_reversed BOOLEAN DEFAULT false;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS refund_reversed_at TIMESTAMP WITH TIME ZONE;

-- 添加字段注释
COMMENT ON COLUMN public.subscriptions.refund_reversed IS '退款是否已被撤销';
COMMENT ON COLUMN public.subscriptions.refund_reversed_at IS '退款撤销时间';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_subscriptions_refund_reversed ON public.subscriptions(refund_reversed) 
  WHERE refund_reversed = true;

-- 更新函数，支持退款撤销
CREATE OR REPLACE FUNCTION public.record_refund_reversed(
  p_user_id UUID,
  p_subscription_id UUID,
  p_transaction_id TEXT,
  p_amount DECIMAL,
  p_currency TEXT,
  p_details JSONB
) RETURNS UUID AS $$
DECLARE
  v_payment_id UUID;
BEGIN
  -- 检查是否已存在相同交易ID的支付记录
  IF p_transaction_id IS NOT NULL THEN
    SELECT id INTO v_payment_id
    FROM public.payments
    WHERE payment_provider_payment_id = p_transaction_id || '_refund_reversed';
    
    IF v_payment_id IS NOT NULL THEN
      RETURN v_payment_id; -- 返回已存在的支付记录ID
    END IF;
  END IF;
  
  -- 创建新的支付记录
  INSERT INTO public.payments (
    user_id,
    subscription_id,
    amount,
    currency,
    status,
    payment_method,
    payment_provider,
    payment_provider_payment_id,
    description,
    metadata
  ) VALUES (
    p_user_id,
    p_subscription_id,
    p_amount,
    p_currency,
    'refund_reversed',
    COALESCE(p_details->>'store', 'app_store'),
    'revenuecat',
    p_transaction_id || '_refund_reversed',
    '退款撤销: ' || COALESCE(p_details->>'product_id', '未知产品'),
    p_details
  ) RETURNING id INTO v_payment_id;
  
  -- 更新订阅状态
  UPDATE public.subscriptions
  SET 
    status = 'active',
    is_refunded = false,
    refunded_at = NULL,
    refund_reversed = true,
    refund_reversed_at = now(),
    updated_at = now()
  WHERE id = p_subscription_id;
  
  RETURN v_payment_id;
END;
$$ LANGUAGE plpgsql; 