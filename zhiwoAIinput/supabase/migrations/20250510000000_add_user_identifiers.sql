-- 创建用户标识符映射表，用于存储RevenueCat匿名ID和系统内用户ID的对应关系
CREATE TABLE IF NOT EXISTS public.user_identifiers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  anonymous_id TEXT NOT NULL,
  provider TEXT NOT NULL DEFAULT 'revenuecat',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  metadata JSONB
);

-- 添加索引以加快查询
CREATE INDEX IF NOT EXISTS idx_user_identifiers_user_id ON public.user_identifiers(user_id);
CREATE INDEX IF NOT EXISTS idx_user_identifiers_anonymous_id ON public.user_identifiers(anonymous_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_identifiers_anonymous_id_provider ON public.user_identifiers(anonymous_id, provider);

-- 添加检查约束确保provider值有效
ALTER TABLE public.user_identifiers ADD CONSTRAINT provider_check CHECK (provider IN ('revenuecat', 'apple', 'google', 'fb', 'firebase', 'system'));

-- 添加触发器自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_identifiers_update_updated_at
BEFORE UPDATE ON public.user_identifiers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 添加ensure_user_exists_with_fallback函数，用于处理匿名ID的情况
CREATE OR REPLACE FUNCTION public.ensure_user_exists_with_fallback(p_user_id TEXT, p_original_id TEXT)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_user_id TEXT;
  v_exists BOOLEAN;
BEGIN
  -- 首先检查用户ID是否存在于profiles表中
  SELECT EXISTS (
    SELECT 1 FROM public.profiles WHERE id = p_user_id
  ) INTO v_exists;
  
  IF v_exists THEN
    RETURN p_user_id;
  END IF;
  
  -- 如果不存在，检查是否为匿名ID并在映射表中查找
  IF p_user_id LIKE '$RCAnonymousID:%' THEN
    SELECT user_id INTO v_user_id
    FROM public.user_identifiers
    WHERE anonymous_id = p_user_id
    LIMIT 1;
    
    IF v_user_id IS NOT NULL THEN
      RETURN v_user_id;
    END IF;
  END IF;
  
  -- 检查原始ID是否与用户ID不同，且在profiles表中存在
  IF p_original_id IS NOT NULL AND p_original_id <> p_user_id THEN
    SELECT EXISTS (
      SELECT 1 FROM public.profiles WHERE id = p_original_id
    ) INTO v_exists;
    
    IF v_exists THEN
      -- 添加映射关系
      INSERT INTO public.user_identifiers (user_id, anonymous_id, provider)
      VALUES (p_original_id, p_user_id, 'revenuecat')
      ON CONFLICT (anonymous_id, provider) DO NOTHING;
      
      RETURN p_original_id;
    END IF;
  END IF;
  
  -- 最后尝试调用ensure_user_exists函数（如果存在）
  BEGIN
    SELECT ensure_user_exists(p_user_id, NULL) INTO v_user_id;
    RETURN v_user_id;
  EXCEPTION
    WHEN OTHERS THEN
      -- 如果函数调用失败，直接返回原始用户ID
      RETURN p_user_id;
  END;
END;
$$;

-- 为user_identifiers表添加权限
ALTER TABLE public.user_identifiers ENABLE ROW LEVEL SECURITY;

-- 重载schema以使更改生效
NOTIFY pgrst, 'reload schema'; 