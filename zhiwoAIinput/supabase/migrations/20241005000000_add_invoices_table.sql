-- 20241005000000_add_invoices_table.sql
-- 添加发票表和相关字段

-- 创建发票表
CREATE TABLE IF NOT EXISTS public.invoices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  product_id TEXT NOT NULL,
  invoice_id TEXT,  -- RevenueCat内部发票ID
  status TEXT DEFAULT 'issued',  -- issued, paid, cancelled, failed
  amount DECIMAL(10, 2),
  currency TEXT,
  issued_at TIMESTAMP WITH TIME ZONE,
  paid_at TIMESTAMP WITH TIME ZONE,
  environment TEXT,
  store TEXT,
  country_code TEXT,
  subscription_id UUID REFERENCES public.subscriptions(id),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 添加表注释
COMMENT ON TABLE public.invoices IS '记录RevenueCat发票信息';

-- 添加列注释
COMMENT ON COLUMN public.invoices.id IS '主键ID';
COMMENT ON COLUMN public.invoices.user_id IS '用户ID';
COMMENT ON COLUMN public.invoices.product_id IS '产品ID';
COMMENT ON COLUMN public.invoices.invoice_id IS 'RevenueCat内部发票ID';
COMMENT ON COLUMN public.invoices.status IS '发票状态(已开具/已支付/已取消/失败)';
COMMENT ON COLUMN public.invoices.amount IS '金额';
COMMENT ON COLUMN public.invoices.currency IS '货币';
COMMENT ON COLUMN public.invoices.issued_at IS '开票时间';
COMMENT ON COLUMN public.invoices.paid_at IS '支付时间';
COMMENT ON COLUMN public.invoices.environment IS '环境(PRODUCTION/SANDBOX)';
COMMENT ON COLUMN public.invoices.store IS '商店(RC_BILLING等)';
COMMENT ON COLUMN public.invoices.country_code IS '国家/地区代码';
COMMENT ON COLUMN public.invoices.subscription_id IS '相关订阅ID';
COMMENT ON COLUMN public.invoices.metadata IS '元数据';
COMMENT ON COLUMN public.invoices.created_at IS '创建时间';
COMMENT ON COLUMN public.invoices.updated_at IS '更新时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON public.invoices(user_id);
CREATE INDEX IF NOT EXISTS idx_invoices_invoice_id ON public.invoices(invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON public.invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_issued_at ON public.invoices(issued_at);

-- 创建发票状态枚举类型
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'invoice_status') THEN
        CREATE TYPE public.invoice_status AS ENUM (
            'issued',
            'paid',
            'cancelled',
            'failed'
        );
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        NULL;
END$$;

-- 创建函数用于记录发票
CREATE OR REPLACE FUNCTION public.record_invoice(
  p_user_id UUID,
  p_product_id TEXT,
  p_invoice_id TEXT,
  p_amount DECIMAL,
  p_currency TEXT,
  p_issued_at TIMESTAMP WITH TIME ZONE,
  p_store TEXT,
  p_environment TEXT,
  p_country_code TEXT,
  p_metadata JSONB
) RETURNS UUID AS $$
DECLARE
  v_invoice_id UUID;
BEGIN
  -- 检查是否已存在相同发票
  SELECT id INTO v_invoice_id 
  FROM public.invoices
  WHERE invoice_id = p_invoice_id
  LIMIT 1;
  
  -- 如果不存在，则创建新发票
  IF v_invoice_id IS NULL THEN
    INSERT INTO public.invoices (
      user_id,
      product_id,
      invoice_id,
      status,
      amount,
      currency,
      issued_at,
      environment,
      store,
      country_code,
      metadata
    ) VALUES (
      p_user_id,
      p_product_id,
      p_invoice_id,
      'issued',
      p_amount,
      p_currency,
      p_issued_at,
      p_environment,
      p_store,
      p_country_code,
      p_metadata
    ) RETURNING id INTO v_invoice_id;
  END IF;
  
  RETURN v_invoice_id;
END;
$$ LANGUAGE plpgsql; 