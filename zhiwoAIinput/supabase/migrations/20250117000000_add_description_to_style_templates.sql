-- 为style_templates表添加description字段
-- 这个字段用于存储模板的描述信息，替代硬编码的描述映射

-- 添加description字段
ALTER TABLE public.style_templates 
ADD COLUMN IF NOT EXISTS description TEXT;

-- 为字段添加注释
COMMENT ON COLUMN public.style_templates.description IS '模板描述信息，用于展示给用户的说明文字';

-- 为现有的系统模板补充描述信息
-- 这些描述信息来自原有的DESCRIPTION_MAPPING

UPDATE public.style_templates 
SET description = CASE 
  WHEN name = '邮件格式' OR name = '发送邮件模板' THEN '将文本整理成正式的商务邮件格式，包含称呼、正文、结束语等标准结构。'
  WHEN name = 'Emoji风格' OR name = '富含emoji格式' THEN '为文本添加生动有趣的表情符号，使内容更加活泼形象。'
  WHEN name = '领导汇报' OR name = '发送给领导' THEN '将内容整理成简明扼要的汇报格式，突出重点，适合向上级汇报。'
  WHEN name = '朋友聊天' OR name = '发送给朋友' THEN '将内容整理成轻松随意的聊天风格，适合与朋友分享。'
  WHEN name = '恋人表达' OR name = '发送给恋人' THEN '将内容整理成温情浪漫的表达方式，适合与恋人交流。'
  WHEN name = '学术论文' THEN '将内容整理成严谨专业的学术风格，适合用于论文和研究。'
  WHEN name = '演讲稿' THEN '将内容整理成具有感染力的演讲风格，适合公开演讲。'
  WHEN name = '新闻报道' THEN '将内容整理成客观公正的新闻风格，注重事实描述。'
  WHEN name = '故事叙述' THEN '将内容整理成生动有趣的故事风格，增强叙事感。'
  ELSE '自定义风格模板'
END
WHERE description IS NULL OR description = '';

-- 创建索引以优化查询性能（如果需要按描述搜索）
CREATE INDEX IF NOT EXISTS idx_style_templates_description ON public.style_templates(description) WHERE description IS NOT NULL; 