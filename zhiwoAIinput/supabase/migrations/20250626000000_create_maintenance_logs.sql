-- 20250626000000_create_maintenance_logs.sql
-- 创建维护任务执行日志表

-- 创建维护日志表
CREATE TABLE IF NOT EXISTS public.maintenance_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_type TEXT NOT NULL, -- 任务类型：check-vip, cleanup-expired, run-all等
    status TEXT NOT NULL DEFAULT 'running' CHECK (status IN ('running', 'success', 'failed', 'timeout')),
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    duration_seconds INTEGER,
    
    -- 执行结果统计
    users_checked INTEGER DEFAULT 0,
    users_updated INTEGER DEFAULT 0,
    subscriptions_cleaned INTEGER DEFAULT 0,
    temp_entitlements_cleaned INTEGER DEFAULT 0,
    trials_cleaned INTEGER DEFAULT 0,
    
    -- 错误信息
    error_message TEXT,
    error_details JSONB,
    
    -- 完整的执行结果
    execution_result JSONB,
    
    -- 执行环境信息
    triggered_by TEXT DEFAULT 'github_actions', -- github_actions, manual, etc.
    execution_id TEXT, -- GitHub Actions run ID或其他执行标识
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_task_type ON public.maintenance_logs(task_type);
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_status ON public.maintenance_logs(status);
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_started_at ON public.maintenance_logs(started_at DESC);
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_triggered_by ON public.maintenance_logs(triggered_by);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_maintenance_logs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    
    -- 如果状态从running变为其他状态，设置完成时间和持续时间
    IF OLD.status = 'running' AND NEW.status != 'running' AND NEW.completed_at IS NULL THEN
        NEW.completed_at = NOW();
        NEW.duration_seconds = EXTRACT(EPOCH FROM (NEW.completed_at - NEW.started_at))::INTEGER;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_maintenance_logs_updated_at
    BEFORE UPDATE ON public.maintenance_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_maintenance_logs_updated_at();

-- 设置RLS策略
ALTER TABLE public.maintenance_logs ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略：只允许服务角色访问
CREATE POLICY "Service role can manage maintenance logs" ON public.maintenance_logs
    FOR ALL USING (auth.role() = 'service_role');

-- 创建视图用于查看最近的维护状态
CREATE OR REPLACE VIEW public.maintenance_status AS
SELECT 
    task_type,
    status,
    started_at,
    completed_at,
    duration_seconds,
    users_checked,
    users_updated,
    subscriptions_cleaned + temp_entitlements_cleaned + trials_cleaned as total_cleaned,
    error_message,
    triggered_by,
    ROW_NUMBER() OVER (PARTITION BY task_type ORDER BY started_at DESC) as rn
FROM public.maintenance_logs
WHERE started_at >= NOW() - INTERVAL '30 days';

-- 只显示每种任务类型的最新状态
CREATE OR REPLACE VIEW public.latest_maintenance_status AS
SELECT 
    task_type,
    status,
    started_at,
    completed_at,
    duration_seconds,
    users_checked,
    users_updated,
    total_cleaned,
    error_message,
    triggered_by
FROM public.maintenance_status
WHERE rn = 1;

-- 创建函数用于记录维护任务开始
CREATE OR REPLACE FUNCTION start_maintenance_task(
    p_task_type TEXT,
    p_triggered_by TEXT DEFAULT 'manual',
    p_execution_id TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_log_id UUID;
BEGIN
    INSERT INTO public.maintenance_logs (
        task_type,
        triggered_by,
        execution_id,
        status
    ) VALUES (
        p_task_type,
        p_triggered_by,
        p_execution_id,
        'running'
    ) RETURNING id INTO v_log_id;
    
    RETURN v_log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建函数用于更新维护任务结果
CREATE OR REPLACE FUNCTION complete_maintenance_task(
    p_log_id UUID,
    p_status TEXT,
    p_users_checked INTEGER DEFAULT 0,
    p_users_updated INTEGER DEFAULT 0,
    p_subscriptions_cleaned INTEGER DEFAULT 0,
    p_temp_entitlements_cleaned INTEGER DEFAULT 0,
    p_trials_cleaned INTEGER DEFAULT 0,
    p_execution_result JSONB DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL,
    p_error_details JSONB DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    UPDATE public.maintenance_logs SET
        status = p_status,
        users_checked = p_users_checked,
        users_updated = p_users_updated,
        subscriptions_cleaned = p_subscriptions_cleaned,
        temp_entitlements_cleaned = p_temp_entitlements_cleaned,
        trials_cleaned = p_trials_cleaned,
        execution_result = p_execution_result,
        error_message = p_error_message,
        error_details = p_error_details
    WHERE id = p_log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON TABLE public.maintenance_logs IS '维护任务执行日志表';
COMMENT ON VIEW public.latest_maintenance_status IS '最新的维护任务状态视图';
COMMENT ON FUNCTION start_maintenance_task IS '开始维护任务并返回日志ID';
COMMENT ON FUNCTION complete_maintenance_task IS '完成维护任务并记录结果'; 