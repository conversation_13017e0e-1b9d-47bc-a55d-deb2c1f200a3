-- 20240717000000_revenuecat_fixes.sql
-- 修复RevenueCat webhook处理中的问题

-- 1. 修复订阅计划查找失败问题(类型不匹配)
-- 修改process_revenuecat_event函数，增强查找订阅计划的逻辑
CREATE OR REPLACE FUNCTION public.process_revenuecat_event(
  p_user_id UUID,
  p_event_type TEXT,
  p_product_id TEXT,
  p_transaction_id TEXT,
  p_original_transaction_id TEXT,
  p_expires_at TIMESTAMP WITH TIME ZONE,
  p_event_data JSONB
) RETURNS UUID AS $$
DECLARE
  v_subscription_id UUID;
  v_event_id UUID;
  v_plan_id TEXT;
  v_store TEXT;
  v_environment TEXT;
BEGIN
  -- 获取商店信息(apple/google)和环境信息(sandbox/production)
  v_store := COALESCE(p_event_data->>'store', 'unknown');
  v_environment := COALESCE(p_event_data->>'environment', 'production');
  
  -- 获取对应的plan_id - 增强匹配逻辑
  -- 首先尝试直接根据product_id查找plan_id
  SELECT plan_id INTO v_plan_id 
  FROM public.subscription_plans 
  WHERE apple_product_id = p_product_id OR google_product_id = p_product_id
  LIMIT 1;
  
  -- 如果找不到匹配的plan_id，尝试使用product_id作为plan_id
  IF v_plan_id IS NULL THEN
    SELECT plan_id INTO v_plan_id 
    FROM public.subscription_plans 
    WHERE plan_id = p_product_id
    LIMIT 1;
  END IF;
  
  -- 如果还是找不到，使用默认月度订阅
  IF v_plan_id IS NULL THEN
    v_plan_id := 'monthly_subscription';
    -- 记录一个警告
    RAISE WARNING '无法找到产品ID为"%"的订阅计划，使用默认月度订阅计划', p_product_id;
  END IF;
  
  -- 查找或创建订阅记录
  SELECT id INTO v_subscription_id FROM public.subscriptions 
  WHERE original_transaction_id = p_original_transaction_id OR transaction_id = p_transaction_id
  LIMIT 1;
  
  -- 如果未找到，则创建新记录
  IF v_subscription_id IS NULL THEN
    INSERT INTO public.subscriptions (
      user_id, 
      product_id, 
      status, 
      store,
      environment,
      transaction_id, 
      original_transaction_id, 
      expires_at, 
      purchase_date, 
      event_type, 
      last_event_at,
      webhook_received_at
    ) VALUES (
      p_user_id, 
      v_plan_id, 
      CASE 
        WHEN p_event_type = 'INITIAL_PURCHASE' THEN 'active'
        WHEN p_event_type = 'RENEWAL' THEN 'active'
        WHEN p_event_type = 'CANCELLATION' THEN 'cancelled'
        WHEN p_event_type = 'EXPIRATION' THEN 'expired'
        ELSE 'unknown'
      END,
      v_store,
      v_environment,
      p_transaction_id, 
      p_original_transaction_id, 
      p_expires_at, 
      now(), 
      p_event_type, 
      now(),
      now()
    ) RETURNING id INTO v_subscription_id;
  ELSE
    -- 更新现有记录
    UPDATE public.subscriptions SET
      status = CASE 
        WHEN p_event_type = 'INITIAL_PURCHASE' THEN 'active'
        WHEN p_event_type = 'RENEWAL' THEN 'active'
        WHEN p_event_type = 'CANCELLATION' THEN 'cancelled'
        WHEN p_event_type = 'UNCANCELLATION' THEN 'active'
        WHEN p_event_type = 'EXPIRATION' THEN 'expired'
        WHEN p_event_type = 'BILLING_ISSUE' THEN status
        ELSE status
      END,
      product_id = v_plan_id,
      store = COALESCE(v_store, store),
      environment = COALESCE(v_environment, environment),
      transaction_id = COALESCE(p_transaction_id, transaction_id),
      expires_at = COALESCE(p_expires_at, expires_at),
      event_type = p_event_type,
      last_event_at = now(),
      webhook_received_at = now(),
      -- 如果取消订阅，记录取消原因
      cancellation_reason = CASE 
        WHEN p_event_type = 'CANCELLATION' THEN p_event_data->>'cancellation_reason'
        ELSE cancellation_reason
      END
    WHERE id = v_subscription_id;
  END IF;
  
  -- 记录事件
  INSERT INTO public.subscription_events (
    user_id, 
    subscription_id, 
    event_type, 
    event_timestamp,
    transaction_id, 
    original_transaction_id, 
    product_id, 
    event_data
  ) VALUES (
    p_user_id, 
    v_subscription_id, 
    p_event_type, 
    now(),
    p_transaction_id, 
    p_original_transaction_id, 
    v_plan_id, 
    p_event_data
  ) RETURNING id INTO v_event_id;
  
  RETURN v_event_id;
END;
$$ LANGUAGE plpgsql;

-- 2. 修复用户在profiles表中不存在的问题
-- 确保用户注册触发器正常工作
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- 先检查用户是否已存在
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = NEW.id) THEN
    -- 插入新用户档案
    INSERT INTO public.profiles (
      id, 
      email, 
      username,
      avatar_url,
      is_vip,
      custom_templates_count,
      voice_minutes_used,
      voice_minutes_limit,
      api_key_type,
      created_at,
      updated_at
    ) VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'username', NEW.email),
      coalesce(NEW.raw_user_meta_data->>'avatar_url', ''),
      false,
      0,
      0, 
      0,
      'free',
      now(),
      now()
    );
    
    -- 创建用户设置记录
    INSERT INTO public.user_settings (
      user_id,
      language,
      theme,
      default_text_model,
      default_voice_model,
      sync_enabled,
      created_at,
      updated_at
    ) VALUES (
      NEW.id,
      'zh-CN',
      'light',
      'qwen2.5-7b',
      'native',
      true,
      now(),
      now()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. 确保触发器存在于auth.users表上
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 4. 提供一个安全的函数用于RevenueCat webhook处理
-- 该函数允许通过webhook创建用户(如果不存在)
CREATE OR REPLACE FUNCTION public.ensure_user_exists(p_user_id TEXT, p_email TEXT DEFAULT NULL)
RETURNS UUID AS $$
DECLARE
  v_uuid UUID;
  v_exists BOOLEAN;
BEGIN
  -- 尝试将用户ID转换为UUID
  BEGIN
    v_uuid := p_user_id::UUID;
  EXCEPTION WHEN OTHERS THEN
    -- 如果不是有效的UUID，则生成一个确定性UUID
    v_uuid := uuid_generate_v5(uuid_generate_v4(), p_user_id);
  END;
  
  -- 检查用户是否存在
  SELECT EXISTS (SELECT 1 FROM public.profiles WHERE id = v_uuid) INTO v_exists;
  
  -- 如果用户不存在，创建用户档案
  IF NOT v_exists THEN
    -- 插入用户档案
    INSERT INTO public.profiles (
      id, 
      email, 
      username,
      avatar_url,
      is_vip,
      custom_templates_count,
      voice_minutes_used,
      voice_minutes_limit,
      api_key_type,
      created_at,
      updated_at
    ) VALUES (
      v_uuid,
      COALESCE(p_email, p_user_id || '@example.com'),
      COALESCE(p_email, p_user_id),
      '',
      false,
      0,
      0, 
      0,
      'free',
      now(),
      now()
    );
    
    -- 创建用户设置
    INSERT INTO public.user_settings (
      user_id,
      language,
      theme,
      default_text_model,
      default_voice_model,
      sync_enabled,
      created_at,
      updated_at
    ) VALUES (
      v_uuid,
      'zh-CN',
      'light',
      'qwen2.5-7b',
      'native',
      true,
      now(),
      now()
    );
  END IF;
  
  RETURN v_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. 修复更新用户VIP状态的函数，确保字段匹配
CREATE OR REPLACE FUNCTION public.update_user_vip_status()
RETURNS TRIGGER AS $$
DECLARE
  user_has_active_sub BOOLEAN;
  v_features JSONB;
  v_voice_minutes INTEGER;
  v_api_key_type TEXT;
  v_custom_templates_limit INTEGER;
BEGIN
  -- 检查用户是否有活跃订阅
  SELECT EXISTS (
    SELECT 1 FROM public.subscriptions 
    WHERE user_id = NEW.user_id 
    AND status = 'active' 
    AND (expires_at IS NULL OR expires_at > now())
  ) INTO user_has_active_sub;
  
  -- 获取订阅计划特性
  IF user_has_active_sub THEN
    SELECT sp.features 
    INTO v_features
    FROM public.subscription_plans sp
    WHERE sp.plan_id = NEW.product_id;
    
    v_voice_minutes := (v_features->>'voice_minutes')::INTEGER;
    v_api_key_type := v_features->>'api_key_type';
    v_custom_templates_limit := (v_features->>'custom_templates_limit')::INTEGER;
  ELSE
    v_voice_minutes := 0;
    v_api_key_type := 'free';
    v_custom_templates_limit := 0;
  END IF;
  
  -- 更新用户的VIP状态
  IF user_has_active_sub AND NOT EXISTS (
    SELECT 1 FROM public.profiles WHERE id = NEW.user_id AND is_vip = true
  ) THEN
    UPDATE public.profiles 
    SET 
      is_vip = true,
      vip_product_id = NEW.product_id,
      vip_expires_at = NEW.expires_at,
      vip_environment = NEW.environment,
      vip_updated_at = now(),
      voice_minutes_limit = v_voice_minutes,
      api_key_type = v_api_key_type
    WHERE id = NEW.user_id;
  ELSIF NOT user_has_active_sub AND EXISTS (
    SELECT 1 FROM public.profiles WHERE id = NEW.user_id AND is_vip = true
  ) THEN
    UPDATE public.profiles 
    SET 
      is_vip = false,
      vip_updated_at = now(),
      voice_minutes_limit = 0,
      api_key_type = 'free'
    WHERE id = NEW.user_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建或更新索引以优化查询
-- RevenueCat相关索引
CREATE INDEX IF NOT EXISTS idx_profiles_is_vip ON public.profiles(is_vip) WHERE is_vip = true;
CREATE INDEX IF NOT EXISTS idx_subscriptions_product_id ON public.subscriptions(product_id);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_apple_product_id ON public.subscription_plans(apple_product_id) WHERE apple_product_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_subscription_plans_google_product_id ON public.subscription_plans(google_product_id) WHERE google_product_id IS NOT NULL;

-- 更新统计信息以优化查询计划
ANALYZE public.profiles;
ANALYZE public.subscriptions;
ANALYZE public.subscription_plans;
ANALYZE public.subscription_events; 