-- 为style_templates表添加多语言支持字段
-- 添加name_key和description_key字段用于国际化

-- 添加name_key字段（用于多语言模板名称）
ALTER TABLE public.style_templates 
ADD COLUMN IF NOT EXISTS name_key TEXT;

-- 添加description_key字段（用于多语言模板描述）
ALTER TABLE public.style_templates 
ADD COLUMN IF NOT EXISTS description_key TEXT;

-- 为字段添加注释
COMMENT ON COLUMN public.style_templates.name_key IS '模板名称的多语言键值，用于i18n国际化';
COMMENT ON COLUMN public.style_templates.description_key IS '模板描述的多语言键值，用于i18n国际化';

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_style_templates_name_key ON public.style_templates(name_key) WHERE name_key IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_style_templates_description_key ON public.style_templates(description_key) WHERE description_key IS NOT NULL; 