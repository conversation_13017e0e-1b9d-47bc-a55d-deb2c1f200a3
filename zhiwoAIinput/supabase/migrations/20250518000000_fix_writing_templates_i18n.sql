-- 修复写作类模板的多语言支持
-- 确保 正式公文、学术论文、新闻报道、创意文案、口语化表达 这几个模板有正确的多语言键

-- 更新这些模板的多语言键
UPDATE public.style_templates 
SET 
  name_key = CASE 
    WHEN semantic_id = 'official' THEN 'template.official.name'
    WHEN semantic_id = 'academic' THEN 'template.academic.name'
    WHEN semantic_id = 'news' THEN 'template.news.name'
    WHEN semantic_id = 'creative' THEN 'template.creative.name'
    WHEN semantic_id = 'casual' THEN 'template.casual.name'
    ELSE name_key
  END,
  description_key = CASE 
    WHEN semantic_id = 'official' THEN 'template.official.description'
    WHEN semantic_id = 'academic' THEN 'template.academic.description'
    WHEN semantic_id = 'news' THEN 'template.news.description'
    WHEN semantic_id = 'creative' THEN 'template.creative.description'
    WHEN semantic_id = 'casual' THEN 'template.casual.description'
    ELSE description_key
  END,
  updated_at = now()
WHERE semantic_id IN ('official', 'academic', 'news', 'creative', 'casual')
  AND is_system = true;

-- 输出更新结果
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE '已更新 % 个写作类模板的多语言键', updated_count;
END
$$;

-- 验证更新结果
SELECT 
    name,
    semantic_id,
    name_key,
    description_key,
    is_system
FROM public.style_templates 
WHERE semantic_id IN ('official', 'academic', 'news', 'creative', 'casual')
ORDER BY semantic_id; 