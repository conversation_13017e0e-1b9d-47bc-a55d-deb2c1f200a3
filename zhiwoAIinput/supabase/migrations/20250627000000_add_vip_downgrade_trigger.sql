-- 迁移文件: 20250124000000_add_vip_downgrade_trigger.sql
-- 描述: 当用户VIP状态从true变为false时，自动将用户的AI模型设置回退到免费版本

-- 创建VIP降级处理函数
CREATE OR REPLACE FUNCTION public.handle_vip_downgrade()
RETURNS TRIGGER AS $$
DECLARE
  v_free_text_model TEXT := 'qwen2.5-7b';
  v_free_voice_model TEXT := 'native';
  v_current_text_model TEXT;
  v_current_voice_model TEXT;
  v_text_model_is_vip BOOLEAN := false;
  v_voice_model_is_vip BOOLEAN := false;
BEGIN
  -- 只有当VIP状态从true变为false时才执行
  IF OLD.is_vip = true AND NEW.is_vip = false THEN
    
    -- 获取用户当前的模型设置
    SELECT default_text_model, default_voice_model 
    INTO v_current_text_model, v_current_voice_model
    FROM public.user_settings 
    WHERE user_id = NEW.id;
    
    -- 检查当前文本模型是否需要VIP权限
    IF v_current_text_model IS NOT NULL THEN
      SELECT is_vip_only INTO v_text_model_is_vip
      FROM public.ai_models 
      WHERE model_id = v_current_text_model 
      AND model_type = 'text' 
      AND is_active = true;
    END IF;
    
    -- 检查当前语音模型是否需要VIP权限
    IF v_current_voice_model IS NOT NULL THEN
      SELECT is_vip_only INTO v_voice_model_is_vip
      FROM public.ai_models 
      WHERE model_id = v_current_voice_model 
      AND model_type = 'voice' 
      AND is_active = true;
    END IF;
    
    -- 如果用户当前使用的是VIP模型，则需要回退
    IF v_text_model_is_vip = true OR v_voice_model_is_vip = true THEN
      
      -- 更新用户设置，将VIP模型回退到免费模型
      UPDATE public.user_settings 
      SET 
        default_text_model = CASE 
          WHEN v_text_model_is_vip = true THEN v_free_text_model
          ELSE default_text_model
        END,
        default_voice_model = CASE 
          WHEN v_voice_model_is_vip = true THEN v_free_voice_model
          ELSE default_voice_model
        END,
        updated_at = now()
      WHERE user_id = NEW.id;
      
      -- 记录日志（可选，用于调试）
      INSERT INTO public.subscription_events (
        user_id,
        event_type,
        event_timestamp,
        event_data
      ) VALUES (
        NEW.id,
        'VIP_MODEL_DOWNGRADE',
        now(),
        jsonb_build_object(
          'previous_text_model', v_current_text_model,
          'previous_voice_model', v_current_voice_model,
          'new_text_model', CASE WHEN v_text_model_is_vip = true THEN v_free_text_model ELSE v_current_text_model END,
          'new_voice_model', CASE WHEN v_voice_model_is_vip = true THEN v_free_voice_model ELSE v_current_voice_model END,
          'reason', 'vip_expiration'
        )
      );
      
    END IF;
    
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器，在profiles表的is_vip字段更新时执行
DROP TRIGGER IF EXISTS vip_downgrade_trigger ON public.profiles;

CREATE TRIGGER vip_downgrade_trigger
  AFTER UPDATE OF is_vip ON public.profiles
  FOR EACH ROW
  WHEN (OLD.is_vip IS DISTINCT FROM NEW.is_vip)
  EXECUTE FUNCTION public.handle_vip_downgrade();

-- 添加注释说明
COMMENT ON FUNCTION public.handle_vip_downgrade() IS '当用户VIP状态变为false时，自动将用户的AI模型设置回退到免费版本';
COMMENT ON TRIGGER vip_downgrade_trigger ON public.profiles IS '监控VIP状态变化，自动处理模型降级';

-- 创建一个函数，用于手动检查和修复所有当前非VIP用户的模型设置
CREATE OR REPLACE FUNCTION public.fix_non_vip_user_models()
RETURNS TABLE(user_id UUID, fixed_text_model TEXT, fixed_voice_model TEXT) AS $$
DECLARE
  v_free_text_model TEXT := 'qwen2.5-7b';
  v_free_voice_model TEXT := 'native';
  r RECORD;
BEGIN
  -- 查找所有非VIP用户但使用VIP模型的情况
  FOR r IN 
    SELECT 
      p.id as profile_id,
      us.default_text_model,
      us.default_voice_model,
      tm.is_vip_only as text_is_vip,
      vm.is_vip_only as voice_is_vip
    FROM public.profiles p
    JOIN public.user_settings us ON p.id = us.user_id
    LEFT JOIN public.ai_models tm ON us.default_text_model = tm.model_id AND tm.model_type = 'text'
    LEFT JOIN public.ai_models vm ON us.default_voice_model = vm.model_id AND vm.model_type = 'voice'
    WHERE p.is_vip = false 
    AND (tm.is_vip_only = true OR vm.is_vip_only = true)
  LOOP
    
    -- 更新用户设置
    UPDATE public.user_settings 
    SET 
      default_text_model = CASE 
        WHEN r.text_is_vip = true THEN v_free_text_model
        ELSE default_text_model
      END,
      default_voice_model = CASE 
        WHEN r.voice_is_vip = true THEN v_free_voice_model
        ELSE default_voice_model
      END,
      updated_at = now()
    WHERE user_id = r.profile_id;
    
    -- 返回修复结果
    RETURN QUERY SELECT 
      r.profile_id,
      CASE WHEN r.text_is_vip = true THEN v_free_text_model ELSE r.default_text_model END,
      CASE WHEN r.voice_is_vip = true THEN v_free_voice_model ELSE r.default_voice_model END;
    
  END LOOP;
  
  RETURN;
END;
$$ LANGUAGE plpgsql;

-- 添加函数注释
COMMENT ON FUNCTION public.fix_non_vip_user_models() IS '手动检查并修复所有非VIP用户的VIP模型设置，将其回退到免费模型';

-- 立即执行一次修复，处理现有的数据不一致问题
-- SELECT * FROM public.fix_non_vip_user_models(); 