-- 20250126000000_support_anonymous_proxy_logs.sql
-- 支持匿名用户的统一代理日志记录

-- 1. 修改user_id字段，允许为null以支持匿名用户
ALTER TABLE unified_proxy_logs 
    ALTER COLUMN user_id DROP NOT NULL,
    DROP CONSTRAINT IF EXISTS unified_proxy_logs_user_id_fkey;

-- 2. 添加新的外键约束，允许null值
ALTER TABLE unified_proxy_logs 
    ADD CONSTRAINT unified_proxy_logs_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;

-- 3. 添加匿名用户标识字段
ALTER TABLE unified_proxy_logs 
    ADD COLUMN IF NOT EXISTS is_anonymous BOOLEAN DEFAULT false;

-- 4. 创建触发器函数，自动设置匿名标识
CREATE OR REPLACE FUNCTION set_anonymous_flag()
RETURNS TRIGGER AS $$
BEGIN
    -- 如果user_id为null，标记为匿名用户
    NEW.is_anonymous = (NEW.user_id IS NULL);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. 创建触发器
DROP TRIGGER IF EXISTS tr_set_anonymous_flag ON unified_proxy_logs;
CREATE TRIGGER tr_set_anonymous_flag
    BEFORE INSERT ON unified_proxy_logs
    FOR EACH ROW
    EXECUTE FUNCTION set_anonymous_flag();

-- 6. 更新现有记录的匿名标识
UPDATE unified_proxy_logs SET is_anonymous = (user_id IS NULL);

-- 7. 创建匿名用户专用索引
CREATE INDEX IF NOT EXISTS idx_unified_proxy_logs_anonymous ON unified_proxy_logs(is_anonymous, request_timestamp);
CREATE INDEX IF NOT EXISTS idx_unified_proxy_logs_anonymous_service ON unified_proxy_logs(is_anonymous, service, request_timestamp);

-- 8. 更新统计视图，包含匿名用户统计
DROP VIEW IF EXISTS unified_proxy_stats;
CREATE VIEW unified_proxy_stats AS
SELECT 
    user_id,
    is_anonymous,
    service,
    DATE_TRUNC('hour', request_timestamp) as hour,
    DATE_TRUNC('day', request_timestamp) as day,
    COUNT(*) as request_count,
    SUM(bytes_transferred) as total_bytes,
    AVG(CASE WHEN response_status BETWEEN 200 AND 299 THEN 1 ELSE 0 END) as success_rate,
    -- AI 统计
    SUM(COALESCE(prompt_tokens, 0)) as total_prompt_tokens,
    SUM(COALESCE(completion_tokens, 0)) as total_completion_tokens,
    SUM(COALESCE(total_tokens, 0)) as total_tokens_used,
    SUM(COALESCE(cost_estimate, 0)) as total_cost
FROM unified_proxy_logs
GROUP BY user_id, is_anonymous, service, hour, day;

-- 9. 创建匿名用户专用统计视图
CREATE OR REPLACE VIEW anonymous_usage_stats AS
SELECT 
    service,
    model,
    DATE_TRUNC('day', request_timestamp) as day,
    COUNT(*) as request_count,
    SUM(prompt_tokens) as total_prompt_tokens,
    SUM(completion_tokens) as total_completion_tokens,
    SUM(total_tokens) as total_tokens,
    SUM(cost_estimate) as total_cost,
    AVG(cost_estimate) as avg_cost_per_request
FROM unified_proxy_logs
WHERE is_anonymous = true AND model IS NOT NULL
GROUP BY service, model, day;

-- 10. 更新AI服务专用视图
DROP VIEW IF EXISTS ai_usage_stats;
CREATE VIEW ai_usage_stats AS
SELECT 
    user_id,
    is_anonymous,
    service,
    model,
    DATE_TRUNC('day', request_timestamp) as day,
    COUNT(*) as request_count,
    SUM(prompt_tokens) as total_prompt_tokens,
    SUM(completion_tokens) as total_completion_tokens,
    SUM(total_tokens) as total_tokens,
    SUM(cost_estimate) as total_cost,
    AVG(cost_estimate) as avg_cost_per_request
FROM unified_proxy_logs
WHERE model IS NOT NULL
GROUP BY user_id, is_anonymous, service, model, day;

-- 11. 更新RLS策略，允许查看匿名用户日志统计
CREATE POLICY "Users can view anonymous usage stats" ON unified_proxy_logs
    FOR SELECT USING (is_anonymous = true AND user_id IS NULL);

-- 12. 添加注释
COMMENT ON COLUMN unified_proxy_logs.is_anonymous IS '是否为匿名用户请求';
COMMENT ON VIEW anonymous_usage_stats IS '匿名用户使用统计视图';

-- 13. 创建获取使用统计的函数
CREATE OR REPLACE FUNCTION get_usage_summary(
    start_date TIMESTAMPTZ DEFAULT NOW() - INTERVAL '30 days',
    end_date TIMESTAMPTZ DEFAULT NOW()
) RETURNS TABLE (
    authenticated_users BIGINT,
    anonymous_requests BIGINT,
    total_requests BIGINT,
    total_tokens BIGINT,
    total_cost DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(DISTINCT CASE WHEN is_anonymous = false THEN user_id END) as authenticated_users,
        COUNT(CASE WHEN is_anonymous = true THEN 1 END) as anonymous_requests,
        COUNT(*) as total_requests,
        SUM(COALESCE(total_tokens, 0)) as total_tokens,
        SUM(COALESCE(cost_estimate, 0)) as total_cost
    FROM unified_proxy_logs
    WHERE request_timestamp BETWEEN start_date AND end_date;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION get_usage_summary IS '获取指定时间范围内的使用情况摘要'; 