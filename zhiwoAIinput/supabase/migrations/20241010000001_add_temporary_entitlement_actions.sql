-- 20241010000001_add_temporary_entitlement_actions.sql
-- 添加临时授权管理操作

-- 创建一个函数用于撤销临时授权
CREATE OR REPLACE FUNCTION public.revoke_temporary_entitlement(
  p_grant_id TEXT,
  p_reason TEXT DEFAULT 'manual_revocation'
) RETURNS BOOLEAN AS $$
DECLARE
  v_user_id UUID;
  v_entitlement_id TEXT;
BEGIN
  -- 更新临时授权状态为已撤销
  UPDATE public.temporary_entitlements
  SET 
    status = 'revoked',
    revoked_at = now(),
    metadata = jsonb_set(
      COALESCE(metadata, '{}'::jsonb),
      '{revocation_reason}',
      to_jsonb(p_reason)
    ),
    updated_at = now()
  WHERE 
    grant_id = p_grant_id
    AND status = 'active'
  RETURNING user_id, entitlement_id INTO v_user_id, v_entitlement_id;
  
  -- 如果找到并撤销了授权，检查是否需要取消VIP状态
  IF v_user_id IS NOT NULL AND v_entitlement_id IS NOT NULL THEN
    -- 检查此用户的其他活跃授权
    IF NOT EXISTS (
      SELECT 1 FROM public.temporary_entitlements
      WHERE 
        user_id = v_user_id 
        AND status = 'active'
        AND entitlement_id = v_entitlement_id
    ) AND
    -- 检查是否没有活跃的付费订阅
    NOT EXISTS (
      SELECT 1 FROM public.subscriptions s
      JOIN public.profiles p ON p.id = s.user_id
      WHERE 
        p.id = v_user_id
        AND s.active = true
        AND (s.period_type != 'TRIAL' OR s.period_type IS NULL)
    ) THEN
      -- 如果没有其他活跃授权或付费订阅，且VIP来源是temporary_grant，则移除VIP
      UPDATE public.profiles
      SET 
        is_vip = false,
        vip_source = null,
        vip_updated_at = now()
      WHERE 
        id = v_user_id
        AND vip_source = 'temporary_grant';
    END IF;
    
    RETURN TRUE;
  END IF;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- 创建一个函数用于批量撤销用户的所有临时授权
CREATE OR REPLACE FUNCTION public.revoke_all_user_temporary_entitlements(
  p_user_id UUID,
  p_reason TEXT DEFAULT 'bulk_revocation'
) RETURNS INTEGER AS $$
DECLARE
  v_count INTEGER;
BEGIN
  -- 更新用户的所有活跃临时授权为已撤销
  WITH revoked_grants AS (
    UPDATE public.temporary_entitlements
    SET 
      status = 'revoked',
      revoked_at = now(),
      metadata = jsonb_set(
        COALESCE(metadata, '{}'::jsonb),
        '{revocation_reason}',
        to_jsonb(p_reason)
      ),
      updated_at = now()
    WHERE 
      user_id = p_user_id
      AND status = 'active'
    RETURNING id
  )
  SELECT COUNT(*) INTO v_count FROM revoked_grants;
  
  -- 如果撤销了任何授权，更新用户的VIP状态
  IF v_count > 0 THEN
    -- 检查是否没有活跃的付费订阅
    IF NOT EXISTS (
      SELECT 1 FROM public.subscriptions
      WHERE 
        user_id = p_user_id
        AND active = true
        AND (period_type != 'TRIAL' OR period_type IS NULL)
    ) THEN
      -- 如果没有活跃付费订阅，且VIP来源是temporary_grant，则移除VIP
      UPDATE public.profiles
      SET 
        is_vip = false,
        vip_source = null,
        vip_updated_at = now()
      WHERE 
        id = p_user_id
        AND vip_source = 'temporary_grant';
    END IF;
  END IF;
  
  RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- 创建一个函数用于延长临时授权有效期
CREATE OR REPLACE FUNCTION public.extend_temporary_entitlement(
  p_grant_id TEXT,
  p_extension_days INTEGER,
  p_reason TEXT DEFAULT 'manual_extension'
) RETURNS BOOLEAN AS $$
DECLARE
  v_current_expiry TIMESTAMP WITH TIME ZONE;
  v_new_expiry TIMESTAMP WITH TIME ZONE;
BEGIN
  -- 首先获取当前过期时间
  SELECT expires_at INTO v_current_expiry
  FROM public.temporary_entitlements
  WHERE grant_id = p_grant_id AND status = 'active';
  
  -- 如果找不到活跃的授权，返回失败
  IF v_current_expiry IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- 计算新的过期时间
  v_new_expiry := v_current_expiry + (p_extension_days || ' days')::interval;
  
  -- 更新临时授权延长过期时间
  UPDATE public.temporary_entitlements
  SET 
    expires_at = v_new_expiry,
    metadata = jsonb_set(
      COALESCE(metadata, '{}'::jsonb),
      '{extension_history}',
      COALESCE(
        metadata->'extension_history', 
        '[]'::jsonb
      ) || jsonb_build_object(
        'extension_date', now(),
        'days_added', p_extension_days,
        'previous_expiry', v_current_expiry,
        'new_expiry', v_new_expiry,
        'reason', p_reason
      )::jsonb
    ),
    updated_at = now()
  WHERE 
    grant_id = p_grant_id
    AND status = 'active';
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql; 