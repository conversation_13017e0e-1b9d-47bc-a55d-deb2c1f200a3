-- 迁移文件：为profiles表添加软删除支持
-- 版本：20250116000000

-- 为profiles表添加软删除相关字段
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS is_deleted_by_user BOOLEAN DEFAULT false;

ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- 添加字段注释
COMMENT ON COLUMN public.profiles.is_deleted_by_user IS '用户是否主动注销账号（软删除标志）';
COMMENT ON COLUMN public.profiles.deleted_at IS '用户注销账号的时间';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_profiles_is_deleted_by_user 
ON public.profiles(is_deleted_by_user) 
WHERE is_deleted_by_user = true;

-- 更新RLS策略，确保已删除的用户无法访问数据
-- 修改现有的查看自己档案的策略
DROP POLICY IF EXISTS "用户可以查看自己的档案" ON public.profiles;
CREATE POLICY "用户可以查看自己的档案"
  ON public.profiles FOR SELECT
  USING (auth.uid() = id AND (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL));

-- 修改现有的更新自己档案的策略
DROP POLICY IF EXISTS "用户可以更新自己的档案" ON public.profiles;
CREATE POLICY "用户可以更新自己的档案"
  ON public.profiles FOR UPDATE
  USING (auth.uid() = id AND (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL));

-- 为订阅相关表添加类似的软删除支持
ALTER TABLE public.subscriptions 
ADD COLUMN IF NOT EXISTS is_deleted_by_user BOOLEAN DEFAULT false;

ALTER TABLE public.subscription_events 
ADD COLUMN IF NOT EXISTS is_deleted_by_user BOOLEAN DEFAULT false;

-- 添加注释
COMMENT ON COLUMN public.subscriptions.is_deleted_by_user IS '用户注销时标记相关订阅为已删除';
COMMENT ON COLUMN public.subscription_events.is_deleted_by_user IS '用户注销时标记相关事件为已删除';

-- 更新订阅相关的RLS策略
DROP POLICY IF EXISTS "用户可以查看自己的订阅" ON public.subscriptions;
CREATE POLICY "用户可以查看自己的订阅"
  ON public.subscriptions FOR SELECT
  USING (auth.uid() = user_id AND (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL));

DROP POLICY IF EXISTS "用户可以查看自己的订阅事件" ON public.subscription_events;
CREATE POLICY "用户可以查看自己的订阅事件"
  ON public.subscription_events FOR SELECT
  USING (auth.uid() = user_id AND (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL));

-- 创建函数用于软删除用户及其相关数据
CREATE OR REPLACE FUNCTION public.soft_delete_user(target_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  affected_rows INTEGER;
BEGIN
  -- 检查用户是否存在且未被删除
  SELECT COUNT(*) INTO affected_rows
  FROM public.profiles
  WHERE id = target_user_id 
    AND (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL);
  
  IF affected_rows = 0 THEN
    RETURN FALSE; -- 用户不存在或已被删除
  END IF;
  
  -- 软删除用户档案
  UPDATE public.profiles
  SET 
    is_deleted_by_user = true,
    deleted_at = now(),
    -- 清空敏感信息
    email = NULL,
    username = NULL,
    avatar_url = NULL,
    updated_at = now()
  WHERE id = target_user_id;
  
  -- 软删除相关订阅
  UPDATE public.subscriptions
  SET is_deleted_by_user = true
  WHERE user_id = target_user_id;
  
  -- 软删除相关订阅事件
  UPDATE public.subscription_events
  SET is_deleted_by_user = true
  WHERE user_id = target_user_id;
  
  -- 软删除用户设置
  DELETE FROM public.user_settings
  WHERE user_id = target_user_id;
  
  -- 软删除自定义模板
  DELETE FROM public.style_templates
  WHERE user_id = target_user_id AND is_system = false;
  
  -- 软删除会话记录
  DELETE FROM public.sessions
  WHERE user_id = target_user_id;
  
  RETURN TRUE;
END;
$$;

-- 添加函数注释
COMMENT ON FUNCTION public.soft_delete_user IS '软删除用户及其相关数据，保留核心记录用于数据分析';

-- 创建视图用于查看活跃（未删除）用户
CREATE OR REPLACE VIEW public.active_users AS
SELECT *
FROM public.profiles
WHERE (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL);

-- 添加视图注释
COMMENT ON VIEW public.active_users IS '查看所有活跃（未被用户注销）的用户';

-- 确保触发器在软删除后仍正常工作
CREATE OR REPLACE FUNCTION public.update_user_vip_status()
RETURNS TRIGGER AS $$
DECLARE
  user_has_active_sub BOOLEAN;
  v_features JSONB;
  v_voice_minutes INTEGER;
  v_api_key_type TEXT;
  v_custom_templates_limit INTEGER;
BEGIN
  -- 如果用户已被软删除，跳过VIP状态更新
  IF EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = NEW.user_id AND is_deleted_by_user = true
  ) THEN
    RETURN NEW;
  END IF;
  
  -- 检查用户是否有活跃订阅
  SELECT EXISTS (
    SELECT 1 FROM public.subscriptions 
    WHERE user_id = NEW.user_id 
    AND status = 'active' 
    AND (expires_at IS NULL OR expires_at > now())
    AND (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL)
  ) INTO user_has_active_sub;
  
  -- 获取订阅计划特性
  IF user_has_active_sub THEN
    SELECT sp.features 
    INTO v_features
    FROM public.subscription_plans sp
    WHERE sp.plan_id = NEW.product_id;
    
    v_voice_minutes := (v_features->>'voice_minutes')::INTEGER;
    v_api_key_type := v_features->>'api_key_type';
    v_custom_templates_limit := (v_features->>'custom_templates_limit')::INTEGER;
  ELSE
    v_voice_minutes := 0;
    v_api_key_type := 'free';
    v_custom_templates_limit := 0;
  END IF;
  
  -- 更新用户的VIP状态
  IF user_has_active_sub AND NOT EXISTS (
    SELECT 1 FROM public.profiles WHERE id = NEW.user_id AND is_vip = true
  ) THEN
    UPDATE public.profiles 
    SET 
      is_vip = true,
      vip_product_id = NEW.product_id,
      vip_expires_at = NEW.expires_at,
      vip_environment = NEW.environment,
      vip_updated_at = now(),
      voice_minutes_limit = v_voice_minutes,
      api_key_type = v_api_key_type
    WHERE id = NEW.user_id
      AND (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL);
  ELSIF NOT user_has_active_sub AND EXISTS (
    SELECT 1 FROM public.profiles WHERE id = NEW.user_id AND is_vip = true
  ) THEN
    UPDATE public.profiles 
    SET 
      is_vip = false,
      vip_updated_at = now(),
      voice_minutes_limit = 0,
      api_key_type = 'free'
    WHERE id = NEW.user_id
      AND (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql; 