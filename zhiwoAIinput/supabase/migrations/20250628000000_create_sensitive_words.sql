-- 创建敏感词表
CREATE TABLE IF NOT EXISTS sensitive_words (
    id BIGSERIAL PRIMARY KEY,
    word TEXT NOT NULL UNIQUE,
    category TEXT DEFAULT 'whisper_sensitive_words',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_sensitive_words_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_sensitive_words_updated_at
    BEFORE UPDATE ON sensitive_words
    FOR EACH ROW
    EXECUTE FUNCTION update_sensitive_words_updated_at();

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_sensitive_words_active ON sensitive_words(is_active);
CREATE INDEX IF NOT EXISTS idx_sensitive_words_category ON sensitive_words(category);
CREATE INDEX IF NOT EXISTS idx_sensitive_words_word ON sensitive_words(word);

-- 设置RLS策略
ALTER TABLE sensitive_words ENABLE ROW LEVEL SECURITY;

-- 允许所有认证用户读取敏感词（用于客户端过滤）
CREATE POLICY "Allow authenticated users to read sensitive words" ON sensitive_words
    FOR SELECT USING (auth.role() = 'authenticated');

-- 只允许service_role写入（管理员操作）
CREATE POLICY "Allow service_role to manage sensitive words" ON sensitive_words
    FOR ALL USING (auth.role() = 'service_role');

-- 插入初始敏感词数据
INSERT INTO sensitive_words (word, category) VALUES
    ('明镜与点点', 'whisper_sensitive_words'),
    ('字幕由Amara.org社群提供', 'whisper_sensitive_words'),
    ('Amara.org', 'whisper_sensitive_words'),
    ('Amara. org', 'whisper_sensitive_words'),
    ('《明镜》与《点点》', 'whisper_sensitive_words'),
    ('请不吝点赞 订阅 转发 打赏', 'whisper_sensitive_words'),
    ('欢迎订阅', 'whisper_sensitive_words'),
    ('明镜', 'whisper_sensitive_words'),
    ('点点', 'whisper_sensitive_words'),
    ('明鏡與點點', 'whisper_sensitive_words'),
    ('字幕由Amara.org社群提供', 'whisper_sensitive_words'),
    ('Amara.org', 'whisper_sensitive_words'),
    ('Amara. org', 'whisper_sensitive_words'),
    ('《明鏡》與《點點》', 'whisper_sensitive_words'),
    ('請不吝點讚 訂閱 轉發 打賞', 'whisper_sensitive_words'),
    ('歡迎訂閱', 'whisper_sensitive_words'),
    ('明鏡', 'whisper_sensitive_words'),
    ('點點', 'whisper_sensitive_words'),
    ('PissedConsumer.com', 'whisper_sensitive_words'),
    ('mingjing', 'whisper_sensitive_words'),
    ('diandian', 'whisper_sensitive_words')
ON CONFLICT (word) DO NOTHING;

-- 创建获取敏感词的函数（可被客户端调用）
CREATE OR REPLACE FUNCTION get_active_sensitive_words()
RETURNS TABLE (
    word TEXT,
    category TEXT,
    updated_at TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT sw.word, sw.category, sw.updated_at
    FROM sensitive_words sw
    WHERE sw.is_active = true
    ORDER BY sw.word;
END;
$$;

-- 创建繁简体转换映射表
CREATE TABLE IF NOT EXISTS chinese_char_mapping (
    simplified CHAR(1) PRIMARY KEY,
    traditional CHAR(1) NOT NULL
);

-- 创建繁体转简体函数
CREATE OR REPLACE FUNCTION to_simplified(text_input TEXT)
RETURNS TEXT
LANGUAGE plpgsql
IMMUTABLE
AS $$
DECLARE
    result TEXT := text_input;
    char_record RECORD;
BEGIN
    FOR char_record IN SELECT traditional, simplified FROM chinese_char_mapping
    LOOP
        result := REPLACE(result, char_record.traditional, char_record.simplified);
    END LOOP;
    RETURN result;
END;
$$;

-- 创建简体转繁体函数
CREATE OR REPLACE FUNCTION to_traditional(text_input TEXT)
RETURNS TEXT
LANGUAGE plpgsql
IMMUTABLE
AS $$
DECLARE
    result TEXT := text_input;
    char_record RECORD;
BEGIN
    FOR char_record IN SELECT traditional, simplified FROM chinese_char_mapping
    LOOP
        result := REPLACE(result, char_record.simplified, char_record.traditional);
    END LOOP;
    RETURN result;
END;
$$;

-- 插入常见的繁简体映射
INSERT INTO chinese_char_mapping (simplified, traditional) VALUES
    ('镜', '鏡'),
    ('点', '點'),
    ('与', '與'),
    ('赞', '讚'),
    ('订', '訂'),
    ('阅', '閱'),
    ('转', '轉'),
    ('发', '發'),
    ('请', '請'),
    ('欢', '歡'),
    ('迎', '迎'),
    ('录', '錄'),
    ('视', '視'),
    ('频', '頻'),
    ('评', '評'),
    ('论', '論'),
    ('观', '觀'),
    ('看', '看'),
    ('说', '說'),
    ('话', '話')
ON CONFLICT DO NOTHING;

-- 更新检查文本是否包含敏感词的函数（支持繁简体）
CREATE OR REPLACE FUNCTION check_text_for_sensitive_words(input_text TEXT)
RETURNS TABLE (
    contains_sensitive_word BOOLEAN,
    detected_words TEXT[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    sensitive_word_record RECORD;
    detected_words_array TEXT[] := '{}';
    contains_sensitive BOOLEAN := false;
    normalized_input TEXT;
    simplified_input TEXT;
    traditional_input TEXT;
BEGIN
    -- 规范化输入文本（移除多余空格，转换为小写）
    normalized_input := LOWER(REGEXP_REPLACE(input_text, '\s+', ' ', 'g'));
    
    -- 转换输入文本为简体和繁体版本
    simplified_input := to_simplified(normalized_input);
    traditional_input := to_traditional(normalized_input);
    
    -- 遍历所有活跃的敏感词
    FOR sensitive_word_record IN 
        SELECT word, 
               to_simplified(word) as simplified_word,
               to_traditional(word) as traditional_word
        FROM sensitive_words 
        WHERE is_active = true
    LOOP
        -- 规范化敏感词
        DECLARE
            normalized_word TEXT := LOWER(REGEXP_REPLACE(sensitive_word_record.word, '\s+', ' ', 'g'));
            normalized_simplified TEXT := LOWER(REGEXP_REPLACE(sensitive_word_record.simplified_word, '\s+', ' ', 'g'));
            normalized_traditional TEXT := LOWER(REGEXP_REPLACE(sensitive_word_record.traditional_word, '\s+', ' ', 'g'));
        BEGIN
            -- 检查原始文本、简体版本和繁体版本
            IF simplified_input ~ ('\m' || REGEXP_REPLACE(QUOTE_LITERAL(normalized_word), '([[\]{}()*+?.\\^$|])', '\\\1', 'g') || '\M')
               OR simplified_input ~ ('\m' || REGEXP_REPLACE(QUOTE_LITERAL(normalized_simplified), '([[\]{}()*+?.\\^$|])', '\\\1', 'g') || '\M')
               OR simplified_input ~ ('\m' || REGEXP_REPLACE(QUOTE_LITERAL(normalized_traditional), '([[\]{}()*+?.\\^$|])', '\\\1', 'g') || '\M')
               OR traditional_input ~ ('\m' || REGEXP_REPLACE(QUOTE_LITERAL(normalized_word), '([[\]{}()*+?.\\^$|])', '\\\1', 'g') || '\M')
               OR traditional_input ~ ('\m' || REGEXP_REPLACE(QUOTE_LITERAL(normalized_simplified), '([[\]{}()*+?.\\^$|])', '\\\1', 'g') || '\M')
               OR traditional_input ~ ('\m' || REGEXP_REPLACE(QUOTE_LITERAL(normalized_traditional), '([[\]{}()*+?.\\^$|])', '\\\1', 'g') || '\M')
            THEN
                contains_sensitive := true;
                detected_words_array := array_append(detected_words_array, sensitive_word_record.word);
            END IF;
        END;
    END LOOP;
    
    RETURN QUERY SELECT contains_sensitive, detected_words_array;
END;
$$; 