-- Migration: Create onboarding surveys table
-- Created at: 2024-12-19 00:00:01
-- Description: 创建用户onboarding问卷调查数据表

-- 创建onboarding问卷数据表
CREATE TABLE IF NOT EXISTS onboarding_surveys (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  -- 用户来源
  source TEXT NOT NULL,
  -- 使用场景 (JSON数组)
  use_cases JSONB NOT NULL DEFAULT '[]',
  -- 选择的模板 (JSON数组)
  selected_templates JSONB NOT NULL DEFAULT '[]',
  -- 设备信息
  device_info JSONB DEFAULT '{}',
  -- 完成时间
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_onboarding_surveys_user_id ON onboarding_surveys(user_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_surveys_source ON onboarding_surveys(source);
CREATE INDEX IF NOT EXISTS idx_onboarding_surveys_completed_at ON onboarding_surveys(completed_at);

-- 启用RLS
ALTER TABLE onboarding_surveys ENABLE ROW LEVEL SECURITY;

-- 用户只能查看和创建自己的问卷记录
CREATE POLICY "Users can view own surveys" ON onboarding_surveys
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own surveys" ON onboarding_surveys
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 允许匿名用户插入问卷（但会记录为null user_id）
CREATE POLICY "Anonymous users can insert surveys" ON onboarding_surveys
  FOR INSERT WITH CHECK (auth.uid() IS NULL AND user_id IS NULL);

-- 管理员可以查看所有问卷数据（用于统计分析）
CREATE POLICY "Admin can view all surveys" ON onboarding_surveys
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.email = '<EMAIL>'
    )
  );

-- 添加表和列的注释
COMMENT ON TABLE onboarding_surveys IS '用户onboarding问卷调查数据';
COMMENT ON COLUMN onboarding_surveys.id IS '主键ID';
COMMENT ON COLUMN onboarding_surveys.user_id IS '用户ID，可为空（匿名用户）';
COMMENT ON COLUMN onboarding_surveys.source IS '用户了解应用的来源';
COMMENT ON COLUMN onboarding_surveys.use_cases IS '用户选择的使用场景数组';
COMMENT ON COLUMN onboarding_surveys.selected_templates IS '用户选择的风格模板ID数组';
COMMENT ON COLUMN onboarding_surveys.device_info IS '设备信息（操作系统、版本等）';
COMMENT ON COLUMN onboarding_surveys.completed_at IS '问卷完成时间';
COMMENT ON COLUMN onboarding_surveys.created_at IS '记录创建时间';
COMMENT ON COLUMN onboarding_surveys.updated_at IS '记录更新时间'; 