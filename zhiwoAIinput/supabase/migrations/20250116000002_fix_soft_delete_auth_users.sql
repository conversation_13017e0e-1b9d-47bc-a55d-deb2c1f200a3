-- 修复软删除用户函数 - 使用auth.users的deleted_at字段而不是修改邮箱
-- 这样用户注销后可以用相同邮箱重新注册

-- 删除现有函数
DROP FUNCTION IF EXISTS soft_delete_user(UUID);

-- 重新创建软删除函数，正确使用auth.users的deleted_at字段
CREATE OR REPLACE FUNCTION soft_delete_user(target_user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- 1. 软删除profiles表记录（不修改邮箱，保持原样用于数据分析）
    UPDATE profiles SET 
        is_deleted_by_user = true,
        deleted_at = NOW(),
        updated_at = NOW()
    WHERE id = target_user_id;
    
    -- 2. 软删除订阅记录
    UPDATE subscriptions SET 
        is_deleted_by_user = true,
        deleted_at = NOW()
    WHERE user_id = target_user_id;
    
    -- 3. 软删除订阅事件记录
    UPDATE subscription_events SET 
        is_deleted_by_user = true,
        deleted_at = NOW()
    WHERE user_id = target_user_id;
    
    -- 4. 修改user_identifiers记录避免冲突
    UPDATE public.user_identifiers SET 
        user_id = 'deleted_' || extract(epoch from now())::bigint || '_' || user_id,
        anonymous_id = 'deleted_' || extract(epoch from now())::bigint || '_' || anonymous_id,
        updated_at = NOW()
    WHERE user_id = target_user_id::TEXT;
    
    -- 5. 使用auth.users的deleted_at字段进行软删除（不修改邮箱）
    UPDATE auth.users SET 
        deleted_at = NOW(),
        raw_user_meta_data = jsonb_build_object(
            'deleted_by_user', true,
            'deleted_at', now()::text,
            'original_email', COALESCE(email, ''),
            'original_data', COALESCE(raw_user_meta_data, '{}')
        )
    WHERE id = target_user_id;
    
    -- 6. 删除用户的实时数据（这些可以完全删除）
    DELETE FROM public.user_settings WHERE user_id = target_user_id;
    DELETE FROM public.style_templates WHERE user_id = target_user_id AND is_system = false;
    DELETE FROM public.sessions WHERE user_id = target_user_id;
    
    -- 7. 记录操作日志
    INSERT INTO public.user_deletion_logs (user_id, deleted_at, deletion_type)
    VALUES (target_user_id, NOW(), 'user_requested')
    ON CONFLICT DO NOTHING;
    
EXCEPTION
    WHEN OTHERS THEN
        -- 记录错误但不回滚，确保至少profiles表被标记为删除
        INSERT INTO public.error_logs (user_id, error_message, occurred_at)
        VALUES (target_user_id, SQLERRM, NOW())
        ON CONFLICT DO NOTHING;
        RAISE;
END;
$$;

-- 为函数设置权限
GRANT EXECUTE ON FUNCTION soft_delete_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION soft_delete_user(UUID) TO anon;

-- 添加函数注释
COMMENT ON FUNCTION soft_delete_user IS '软删除用户，使用auth.users的deleted_at字段，允许相同邮箱重新注册'; 