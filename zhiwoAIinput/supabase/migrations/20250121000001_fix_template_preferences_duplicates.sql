-- 修复模板偏好设置表中可能存在的重复数据
-- 这个脚本会保留每个用户每个模板的最新记录，删除重复记录

-- 创建临时函数来清理重复数据
CREATE OR REPLACE FUNCTION clean_duplicate_template_preferences()
RETURNS void AS $$
DECLARE
  duplicate_record RECORD;
  records_to_delete uuid[];
BEGIN
  -- 查找重复记录（同一用户同一模板有多条记录）
  FOR duplicate_record IN
    SELECT user_id, template_id, array_agg(id ORDER BY updated_at DESC) as ids
    FROM user_template_preferences
    GROUP BY user_id, template_id
    HAVING COUNT(*) > 1
  LOOP
    -- 保留最新的记录（数组第一个），删除其他的
    records_to_delete := duplicate_record.ids[2:];
    
    -- 删除重复记录
    DELETE FROM user_template_preferences 
    WHERE id = ANY(records_to_delete);
    
    RAISE NOTICE '清理用户 % 模板 % 的 % 条重复记录', 
      duplicate_record.user_id, 
      duplicate_record.template_id, 
      array_length(records_to_delete, 1);
  END LOOP;
  
  RAISE NOTICE '重复数据清理完成';
END;
$$ LANGUAGE plpgsql;

-- 执行清理函数
SELECT clean_duplicate_template_preferences();

-- 删除临时函数
DROP FUNCTION clean_duplicate_template_preferences();

-- 验证清理结果
DO $$
DECLARE
  duplicate_count integer;
BEGIN
  SELECT COUNT(*) INTO duplicate_count
  FROM (
    SELECT user_id, template_id, COUNT(*) as cnt
    FROM user_template_preferences
    GROUP BY user_id, template_id
    HAVING COUNT(*) > 1
  ) duplicates;
  
  IF duplicate_count > 0 THEN
    RAISE WARNING '仍然存在 % 组重复数据，请检查', duplicate_count;
  ELSE
    RAISE NOTICE '所有重复数据已清理完成';
  END IF;
END;
$$; 