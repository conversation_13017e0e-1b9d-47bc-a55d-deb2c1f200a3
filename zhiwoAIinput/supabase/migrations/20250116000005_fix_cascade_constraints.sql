-- 修复外键约束，避免删除auth.users时级联删除我们想保留的数据

-- 1. 修改profiles表的外键约束
-- 先删除现有的级联约束
ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_id_fkey;

-- 重新创建约束，改为SET NULL（但我们的id是主键，不能为NULL）
-- 所以我们需要完全移除这个外键约束，因为我们有业务逻辑来管理

-- 数据一致性（通过软删除函数）

-- 为安全起见，先备份profiles表的id列不能为空的约束
ALTER TABLE public.profiles ALTER COLUMN id SET NOT NULL;

-- 2. 修改sessions表的外键约束
ALTER TABLE public.sessions DROP CONSTRAINT IF EXISTS sessions_user_id_fkey;

-- 重新创建为SET NULL约束，允许保留孤儿记录
ALTER TABLE public.sessions ALTER COLUMN user_id DROP NOT NULL;
ALTER TABLE public.sessions ADD CONSTRAINT sessions_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;

-- 3. 修改subscription_extension_history表的外键约束
ALTER TABLE public.subscription_extension_history DROP CONSTRAINT IF EXISTS subscription_extension_history_user_id_fkey;

-- 重新创建为SET NULL约束
ALTER TABLE public.subscription_extension_history ALTER COLUMN user_id DROP NOT NULL;
ALTER TABLE public.subscription_extension_history ADD CONSTRAINT subscription_extension_history_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;

-- 4. 检查其他可能的CASCADE约束
-- 查看subscriptions表（从profiles表引用）
ALTER TABLE public.subscriptions DROP CONSTRAINT IF EXISTS subscriptions_user_id_fkey;
-- 重新创建，引用profiles表而不是auth.users
ALTER TABLE public.subscriptions ADD CONSTRAINT subscriptions_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- 5. 检查subscription_events表
ALTER TABLE public.subscription_events DROP CONSTRAINT IF EXISTS subscription_events_user_id_fkey;
-- 重新创建，引用profiles表
ALTER TABLE public.subscription_events ADD CONSTRAINT subscription_events_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- 6. 检查temporary_entitlements表（如果存在）
ALTER TABLE public.temporary_entitlements DROP CONSTRAINT IF EXISTS temporary_entitlements_user_id_fkey;
-- 重新创建，引用profiles表
ALTER TABLE public.temporary_entitlements ADD CONSTRAINT temporary_entitlements_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- 7. 检查user_settings表
ALTER TABLE public.user_settings DROP CONSTRAINT IF EXISTS user_settings_user_id_fkey;
-- user_settings在软删除时会被完全删除，所以保持原来的逻辑
ALTER TABLE public.user_settings ADD CONSTRAINT user_settings_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- 8. 检查style_templates表
ALTER TABLE public.style_templates DROP CONSTRAINT IF EXISTS style_templates_user_id_fkey;
-- style_templates的非系统模板在软删除时会被删除
ALTER TABLE public.style_templates ADD CONSTRAINT style_templates_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- 添加注释说明新的约束逻辑
COMMENT ON TABLE public.profiles IS '用户档案表 - 软删除时保留记录，不再直接引用auth.users以避免级联删除';
COMMENT ON TABLE public.sessions IS '用户会话表 - auth.users删除时user_id设为NULL，通过软删除标记来过滤';
COMMENT ON TABLE public.subscription_extension_history IS '订阅延期历史 - auth.users删除时user_id设为NULL，保留历史记录'; 