-- 迁移文件：允许孤立的订阅事件记录
-- 版本：20250719000000
-- 目的：支持在没有对应用户记录的情况下记录RevenueCat订阅事件

-- 1. 修改subscription_events表结构
-- 添加app_user_id字段存储原始的RevenueCat用户ID
ALTER TABLE public.subscription_events 
ADD COLUMN IF NOT EXISTS app_user_id TEXT;

-- 将user_id字段改为可空，允许孤立的事件记录
ALTER TABLE public.subscription_events 
ALTER COLUMN user_id DROP NOT NULL;

-- 添加字段注释
COMMENT ON COLUMN public.subscription_events.app_user_id IS '原始的RevenueCat用户ID，用于追溯和后续用户匹配';
COMMENT ON COLUMN public.subscription_events.user_id IS '系统内用户ID，可为空（孤立事件记录）';

-- 2. 修改subscriptions表结构
-- 添加app_user_id字段
ALTER TABLE public.subscriptions 
ADD COLUMN IF NOT EXISTS app_user_id TEXT;

-- 将user_id字段改为可空
ALTER TABLE public.subscriptions 
ALTER COLUMN user_id DROP NOT NULL;

-- 添加字段注释
COMMENT ON COLUMN public.subscriptions.app_user_id IS '原始的RevenueCat用户ID，用于追溯和后续用户匹配';
COMMENT ON COLUMN public.subscriptions.user_id IS '系统内用户ID，可为空（孤立订阅记录）';

-- 3. 为现有记录填充app_user_id字段
-- 对于已有的记录，直接使用user_id作为app_user_id
UPDATE public.subscription_events
SET app_user_id = user_id::text
WHERE app_user_id IS NULL AND user_id IS NOT NULL;

UPDATE public.subscriptions
SET app_user_id = user_id::text
WHERE app_user_id IS NULL AND user_id IS NOT NULL;

-- 4. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_subscription_events_app_user_id 
ON public.subscription_events(app_user_id) 
WHERE app_user_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_subscriptions_app_user_id 
ON public.subscriptions(app_user_id) 
WHERE app_user_id IS NOT NULL;

-- 创建复合索引用于孤立记录查询
CREATE INDEX IF NOT EXISTS idx_subscription_events_orphaned 
ON public.subscription_events(app_user_id, event_timestamp) 
WHERE user_id IS NULL AND app_user_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_subscriptions_orphaned 
ON public.subscriptions(app_user_id, created_at) 
WHERE user_id IS NULL AND app_user_id IS NOT NULL;

-- 5. 创建辅助函数：检查用户是否存在（不自动创建）
CREATE OR REPLACE FUNCTION public.check_user_exists(p_user_id TEXT)
RETURNS UUID AS $$
DECLARE
  v_user_uuid UUID;
BEGIN
  -- 尝试将字符串转换为UUID
  BEGIN
    v_user_uuid := p_user_id::UUID;
  EXCEPTION WHEN invalid_text_representation THEN
    -- 如果不是有效的UUID格式，返回NULL
    RETURN NULL;
  END;
  
  -- 检查用户是否存在于profiles表中
  SELECT id INTO v_user_uuid
  FROM public.profiles 
  WHERE id = v_user_uuid 
    AND (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL);
  
  RETURN v_user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 添加函数注释
COMMENT ON FUNCTION public.check_user_exists(TEXT) IS '检查用户是否存在，不自动创建用户记录';

-- 6. 创建函数：记录孤立的订阅事件
CREATE OR REPLACE FUNCTION public.record_orphaned_subscription_event(
  p_app_user_id TEXT,
  p_event_type TEXT,
  p_event_timestamp TIMESTAMP WITH TIME ZONE,
  p_transaction_id TEXT DEFAULT NULL,
  p_original_transaction_id TEXT DEFAULT NULL,
  p_product_id TEXT DEFAULT NULL,
  p_event_data JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID AS $$
DECLARE
  v_event_id UUID;
BEGIN
  -- 插入孤立的订阅事件记录
  INSERT INTO public.subscription_events (
    user_id,
    app_user_id,
    subscription_id,
    event_type,
    event_timestamp,
    transaction_id,
    original_transaction_id,
    product_id,
    event_data,
    created_at
  ) VALUES (
    NULL, -- user_id为空，表示孤立记录
    p_app_user_id,
    NULL, -- subscription_id为空，因为没有对应的订阅记录
    p_event_type,
    p_event_timestamp,
    p_transaction_id,
    p_original_transaction_id,
    p_product_id,
    p_event_data,
    now()
  )
  RETURNING id INTO v_event_id;
  
  RETURN v_event_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 添加函数注释
COMMENT ON FUNCTION public.record_orphaned_subscription_event IS '记录孤立的订阅事件（无对应用户记录）';

-- 7. 创建视图：查看孤立的订阅记录
CREATE OR REPLACE VIEW public.orphaned_subscription_events AS
SELECT
  se.id,
  se.app_user_id,
  se.event_type,
  se.event_timestamp,
  se.transaction_id,
  se.original_transaction_id,
  se.product_id,
  se.event_data,
  se.created_at,
  -- 尝试匹配可能的用户（基于email或UUID格式）
  p.id as potential_user_id,
  p.email as potential_user_email
FROM public.subscription_events se
LEFT JOIN public.profiles p ON (
  -- 尝试通过email匹配（如果app_user_id看起来像email）
  se.app_user_id LIKE '%@%' AND p.email = se.app_user_id
  OR
  -- 尝试通过UUID格式匹配（如果app_user_id是有效的UUID）
  (se.app_user_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
   AND p.id::text = se.app_user_id)
)
WHERE se.user_id IS NULL
  AND se.app_user_id IS NOT NULL
ORDER BY se.event_timestamp DESC;

-- 添加视图注释
COMMENT ON VIEW public.orphaned_subscription_events IS '显示所有孤立的订阅事件及可能的用户匹配';

-- 8. 更新RLS策略
-- 由于subscription_events现在可能没有user_id，需要更新安全策略
DROP POLICY IF EXISTS "用户可以查看自己的订阅事件" ON public.subscription_events;
CREATE POLICY "用户可以查看自己的订阅事件"
  ON public.subscription_events FOR SELECT
  USING (
    auth.uid() = user_id
    OR
    -- 允许查看与自己相关的孤立事件（通过app_user_id匹配）
    (user_id IS NULL AND EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid()
        AND (id::text = app_user_id OR email = app_user_id)
    ))
  );

-- 对subscriptions表应用类似的策略更新
DROP POLICY IF EXISTS "用户可以查看自己的订阅" ON public.subscriptions;
CREATE POLICY "用户可以查看自己的订阅"
  ON public.subscriptions FOR SELECT
  USING (
    auth.uid() = user_id
    OR
    -- 允许查看与自己相关的孤立订阅（通过app_user_id匹配）
    (user_id IS NULL AND EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid()
        AND (id::text = app_user_id OR email = app_user_id)
    ))
  );

-- 9. 创建清理函数：将孤立记录关联到现有用户
CREATE OR REPLACE FUNCTION public.link_orphaned_records_to_user(
  p_app_user_id TEXT,
  p_user_id UUID
)
RETURNS INTEGER AS $$
DECLARE
  v_updated_count INTEGER := 0;
  v_temp_count INTEGER;
BEGIN
  -- 验证用户存在
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = p_user_id) THEN
    RAISE EXCEPTION '用户不存在: %', p_user_id;
  END IF;

  -- 更新孤立的订阅事件记录
  UPDATE public.subscription_events
  SET user_id = p_user_id,
      updated_at = now()
  WHERE user_id IS NULL
    AND app_user_id = p_app_user_id;

  GET DIAGNOSTICS v_temp_count = ROW_COUNT;
  v_updated_count := v_updated_count + v_temp_count;

  -- 更新孤立的订阅记录
  UPDATE public.subscriptions
  SET user_id = p_user_id,
      updated_at = now()
  WHERE user_id IS NULL
    AND app_user_id = p_app_user_id;

  GET DIAGNOSTICS v_temp_count = ROW_COUNT;
  v_updated_count := v_updated_count + v_temp_count;

  RETURN v_updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 添加函数注释
COMMENT ON FUNCTION public.link_orphaned_records_to_user IS '将孤立的订阅记录关联到现有用户';

-- 10. 添加约束确保数据完整性
-- 确保app_user_id不为空字符串
ALTER TABLE public.subscription_events 
ADD CONSTRAINT check_app_user_id_not_empty 
CHECK (app_user_id IS NULL OR length(trim(app_user_id)) > 0);

ALTER TABLE public.subscriptions 
ADD CONSTRAINT check_app_user_id_not_empty 
CHECK (app_user_id IS NULL OR length(trim(app_user_id)) > 0);

-- 确保至少有一个用户标识（user_id或app_user_id）
ALTER TABLE public.subscription_events 
ADD CONSTRAINT check_has_user_identifier 
CHECK (user_id IS NOT NULL OR app_user_id IS NOT NULL);

ALTER TABLE public.subscriptions
ADD CONSTRAINT check_has_user_identifier
CHECK (user_id IS NOT NULL OR app_user_id IS NOT NULL);

-- 11. 创建统计视图：孤立记录统计
CREATE OR REPLACE VIEW public.orphaned_records_stats AS
SELECT
  'subscription_events' as table_name,
  COUNT(*) as orphaned_count,
  COUNT(DISTINCT app_user_id) as unique_app_users,
  MIN(event_timestamp) as earliest_event,
  MAX(event_timestamp) as latest_event
FROM public.subscription_events
WHERE user_id IS NULL AND app_user_id IS NOT NULL

UNION ALL

SELECT
  'subscriptions' as table_name,
  COUNT(*) as orphaned_count,
  COUNT(DISTINCT app_user_id) as unique_app_users,
  MIN(created_at) as earliest_event,
  MAX(created_at) as latest_event
FROM public.subscriptions
WHERE user_id IS NULL AND app_user_id IS NOT NULL;

-- 添加视图注释
COMMENT ON VIEW public.orphaned_records_stats IS '孤立记录统计信息';

-- 12. 创建匹配建议函数：基于各种条件建议可能的用户匹配
CREATE OR REPLACE FUNCTION public.suggest_user_matches_for_orphaned_records()
RETURNS TABLE (
  app_user_id TEXT,
  suggested_user_id UUID,
  suggested_user_email TEXT,
  match_reason TEXT,
  confidence_score INTEGER
) AS $$
BEGIN
  RETURN QUERY
  -- 基于email完全匹配
  SELECT DISTINCT
    se.app_user_id,
    p.id as suggested_user_id,
    p.email as suggested_user_email,
    'email_exact_match' as match_reason,
    100 as confidence_score
  FROM public.subscription_events se
  JOIN public.profiles p ON p.email = se.app_user_id
  WHERE se.user_id IS NULL
    AND se.app_user_id LIKE '%@%'
    AND p.email IS NOT NULL

  UNION ALL

  -- 基于UUID格式匹配（如果app_user_id是有效的UUID）
  SELECT DISTINCT
    se.app_user_id,
    p.id as suggested_user_id,
    p.email as suggested_user_email,
    'uuid_format_match' as match_reason,
    95 as confidence_score
  FROM public.subscription_events se
  JOIN public.profiles p ON p.id::text = se.app_user_id
  WHERE se.user_id IS NULL
    AND se.app_user_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'

  ORDER BY confidence_score DESC, app_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 添加函数注释
COMMENT ON FUNCTION public.suggest_user_matches_for_orphaned_records IS '为孤立记录建议可能的用户匹配';

-- 13. 创建批量关联函数：批量处理高置信度的匹配
CREATE OR REPLACE FUNCTION public.auto_link_high_confidence_matches(
  p_min_confidence_score INTEGER DEFAULT 95
)
RETURNS TABLE (
  app_user_id TEXT,
  linked_user_id UUID,
  records_updated INTEGER
) AS $$
DECLARE
  match_record RECORD;
  updated_count INTEGER;
BEGIN
  -- 遍历高置信度的匹配建议
  FOR match_record IN
    SELECT DISTINCT
      m.app_user_id,
      m.suggested_user_id
    FROM public.suggest_user_matches_for_orphaned_records() m
    WHERE m.confidence_score >= p_min_confidence_score
  LOOP
    -- 关联孤立记录到用户
    SELECT public.link_orphaned_records_to_user(
      match_record.app_user_id,
      match_record.suggested_user_id
    ) INTO updated_count;

    -- 返回结果
    app_user_id := match_record.app_user_id;
    linked_user_id := match_record.suggested_user_id;
    records_updated := updated_count;

    RETURN NEXT;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 添加函数注释
COMMENT ON FUNCTION public.auto_link_high_confidence_matches IS '自动关联高置信度的孤立记录匹配';
