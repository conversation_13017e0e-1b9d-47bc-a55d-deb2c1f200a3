-- 为 service_role 授予必要的权限
GRANT USAGE ON SCHEMA public TO service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- 为 authenticated 角色授予必要的权限
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- 为 anon 角色授予最小必要权限
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT ON TABLE public.subscription_plans TO anon;
GRANT SELECT ON TABLE public.ai_models TO anon;

-- 启用 RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.style_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略
CREATE POLICY "允许服务角色完全访问" ON public.profiles
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "允许服务角色完全访问" ON public.subscriptions
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "允许服务角色完全访问" ON public.subscription_events
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "允许服务角色完全访问" ON public.sessions
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "允许服务角色完全访问" ON public.style_templates
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "允许服务角色完全访问" ON public.user_settings
  FOR ALL USING (auth.role() = 'service_role');

-- 为 exec_sql 函数设置特殊权限
ALTER FUNCTION public.exec_sql(text, text[]) OWNER TO postgres;
REVOKE ALL ON FUNCTION public.exec_sql(text, text[]) FROM PUBLIC;
GRANT EXECUTE ON FUNCTION public.exec_sql(text, text[]) TO service_role;

-- 确保 Edge Function 可以访问必要的系统表
GRANT USAGE ON SCHEMA pg_catalog TO service_role;
GRANT SELECT ON pg_catalog.pg_namespace TO service_role;
GRANT SELECT ON pg_catalog.pg_class TO service_role;
GRANT SELECT ON pg_catalog.pg_attribute TO service_role;

-- 添加注释
COMMENT ON SCHEMA public IS '知我AI输入法应用的主要数据模式'; 