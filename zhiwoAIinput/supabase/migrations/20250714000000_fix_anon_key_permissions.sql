-- 修复anon_key无法访问模板和模型列表的问题
-- 授予anon角色SELECT权限，并添加RLS策略允许anon读取style_templates和ai_models表

-- 1. 授予anon角色SELECT权限
GRANT SELECT ON TABLE public.style_templates TO anon;
GRANT SELECT ON TABLE public.ai_models TO anon;

-- 2. 为style_templates添加RLS策略，允许anon读取所有模板（如需更细粒度可加条件）
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'style_templates' AND policyname = 'Allow anon read style_templates'
  ) THEN
    CREATE POLICY "Allow anon read style_templates" ON public.style_templates
      FOR SELECT
      TO anon
      USING (true);
  END IF;
END$$;

-- 3. 为ai_models添加RLS策略，允许anon读取所有模型
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'ai_models' AND policyname = 'Allow anon read ai_models'
  ) THEN
    CREATE POLICY "Allow anon read ai_models" ON public.ai_models
      FOR SELECT
      TO anon
      USING (true);
  END IF;
END$$; 