-- 更新RLS策略，确保新用户可以正常创建profiles记录

-- 删除旧的INSERT策略（如果存在）
DROP POLICY IF EXISTS "用户可以创建自己的档案" ON public.profiles;

-- 创建新的INSERT策略，允许新用户创建档案
CREATE POLICY "用户可以创建自己的档案"
  ON public.profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- 确保触发器函数有足够权限
GRANT INSERT ON public.profiles TO service_role;
GRANT UPDATE ON public.profiles TO service_role;

-- 为已删除用户的邮箱重复使用添加特殊处理
-- 修改profiles表的email唯一约束，允许已删除用户的邮箱被重复使用

-- 先删除email的唯一约束，然后删除相关索引
ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_email_key;
DROP INDEX IF EXISTS profiles_email_key;

-- 创建部分唯一索引，只对未删除的用户强制邮箱唯一性
CREATE UNIQUE INDEX IF NOT EXISTS profiles_email_unique_active 
ON public.profiles (email) 
WHERE (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL);

-- 为用户名创建类似的部分唯一索引
ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_username_key;
DROP INDEX IF EXISTS profiles_username_key;
CREATE UNIQUE INDEX IF NOT EXISTS profiles_username_unique_active 
ON public.profiles (username) 
WHERE (is_deleted_by_user IS FALSE OR is_deleted_by_user IS NULL) AND username IS NOT NULL;

-- 添加注释说明
COMMENT ON INDEX profiles_email_unique_active IS '只对未删除用户强制邮箱唯一性，允许已删除用户邮箱被重复使用';
COMMENT ON INDEX profiles_username_unique_active IS '只对未删除用户强制用户名唯一性，允许已删除用户用户名被重复使用'; 