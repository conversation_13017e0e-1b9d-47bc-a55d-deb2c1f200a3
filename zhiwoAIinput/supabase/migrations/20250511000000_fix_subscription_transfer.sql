-- 添加函数用于处理匿名ID的映射和查询
CREATE OR REPLACE FUNCTION public.find_subscription_by_rc_id(
  p_rc_id TEXT
)
RETURNS SETOF subscriptions
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_user_id TEXT;
  v_standard_id TEXT;
BEGIN
  -- 尝试查找与RC匿名ID关联的标准用户ID
  IF p_rc_id LIKE '$RCAnonymousID:%' THEN
    SELECT user_id INTO v_standard_id
    FROM user_identifiers
    WHERE anonymous_id = p_rc_id
    LIMIT 1;
  END IF;
  
  -- 如果找到了映射关系，用标准ID查询
  IF v_standard_id IS NOT NULL THEN
    RETURN QUERY 
    SELECT * FROM subscriptions 
    WHERE user_id = v_standard_id;
    RETURN;
  END IF;
  
  -- 尝试直接使用匿名ID查询
  -- 这里使用TEXT类型比较而不是UUID比较
  RETURN QUERY
  SELECT * FROM subscriptions
  WHERE user_id::TEXT = p_rc_id;
  
  -- 如果前面都没找到，尝试在profiles中查找相关信息
  IF NOT FOUND THEN
    -- 检查是否在profiles表中有这个匿名ID的记录
    SELECT id INTO v_user_id
    FROM profiles
    WHERE anonymous_id = p_rc_id
    LIMIT 1;
    
    IF v_user_id IS NOT NULL THEN
      RETURN QUERY
      SELECT * FROM subscriptions
      WHERE user_id = v_user_id;
    END IF;
  END IF;
  
  RETURN;
END;
$$;

-- 添加函数用于创建匿名ID到标准ID的映射
CREATE OR REPLACE FUNCTION public.map_rc_id_to_user_id(
  p_rc_id TEXT,
  p_user_id TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- 跳过非匿名ID
  IF p_rc_id NOT LIKE '$RCAnonymousID:%' THEN
    RETURN FALSE;
  END IF;
  
  -- 插入/更新映射关系
  INSERT INTO user_identifiers (user_id, anonymous_id, provider)
  VALUES (p_user_id, p_rc_id, 'revenuecat')
  ON CONFLICT (anonymous_id, provider) 
  DO UPDATE SET 
    user_id = p_user_id,
    updated_at = now();
    
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- 添加RPC函数，用于处理TRANSFER事件中的RevenueCat匿名ID
CREATE OR REPLACE FUNCTION public.handle_subscription_transfer(
  p_from_user_id TEXT,
  p_to_user_id TEXT,
  p_event_data JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_from_std_id TEXT;
  v_to_std_id TEXT;
  v_subs RECORD;
  v_sub_ids TEXT[] := '{}'::TEXT[];
  v_now TIMESTAMPTZ := now();
  v_result JSONB;
BEGIN
  -- 处理用户ID
  -- 检查from_user_id是否为匿名ID
  IF p_from_user_id LIKE '$RCAnonymousID:%' THEN
    -- 查找匿名ID对应的标准ID
    SELECT user_id INTO v_from_std_id
    FROM user_identifiers
    WHERE anonymous_id = p_from_user_id
    LIMIT 1;
    
    -- 如果找不到，尝试在profiles表中查找
    IF v_from_std_id IS NULL THEN
      SELECT id INTO v_from_std_id
      FROM profiles
      WHERE anonymous_id = p_from_user_id
      LIMIT 1;
    END IF;
  ELSE
    v_from_std_id := p_from_user_id;
  END IF;
  
  -- 处理to_user_id
  v_to_std_id := p_to_user_id;
  
  -- 查找要转移的订阅
  FOR v_subs IN
    SELECT * FROM find_subscription_by_rc_id(p_from_user_id)
    WHERE status = 'active'
  LOOP
    -- 将订阅标记为已转移
    UPDATE subscriptions
    SET 
      status = 'transferred',
      updated_at = v_now,
      event_type = 'TRANSFER'
    WHERE id = v_subs.id;
    
    -- 记录转移的订阅ID
    v_sub_ids := array_append(v_sub_ids, v_subs.id::TEXT);
    
    -- 创建新的订阅记录
    INSERT INTO subscriptions (
      user_id, 
      product_id,
      status,
      environment,
      store,
      transaction_id,
      original_transaction_id,
      expires_at,
      purchase_date,
      event_type,
      period_type,
      entitlements,
      is_family_share,
      country_code,
      transfer_from_user_id,
      transfer_from_subscription_id
    )
    VALUES (
      v_to_std_id,
      v_subs.product_id,
      'active',
      v_subs.environment,
      v_subs.store,
      v_subs.transaction_id || '_transferred',
      v_subs.original_transaction_id,
      v_subs.expires_at,
      v_now,
      'TRANSFER',
      v_subs.period_type,
      v_subs.entitlements,
      v_subs.is_family_share,
      v_subs.country_code,
      v_from_std_id,
      v_subs.id
    );
  END LOOP;
  
  -- 如果有订阅被转移
  IF array_length(v_sub_ids, 1) > 0 THEN
    -- 更新用户VIP状态
    -- 原用户
    UPDATE profiles
    SET 
      is_vip = FALSE,
      vip_updated_at = v_now,
      updated_at = v_now
    WHERE id = v_from_std_id;
    
    -- 新用户
    UPDATE profiles
    SET 
      is_vip = TRUE,
      vip_updated_at = v_now,
      updated_at = v_now
    WHERE id = v_to_std_id;
    
    -- 如果from_user_id是匿名ID，创建映射
    IF p_from_user_id LIKE '$RCAnonymousID:%' AND v_from_std_id IS NOT NULL THEN
      PERFORM map_rc_id_to_user_id(p_from_user_id, v_from_std_id);
    END IF;
    
    -- 如果存在另一个匿名ID在转入用户列表中，也创建映射
    IF p_event_data->'transferred_to' IS NOT NULL AND jsonb_array_length(p_event_data->'transferred_to') > 1 THEN
      FOR i IN 0..jsonb_array_length(p_event_data->'transferred_to')-1 LOOP
        IF jsonb_extract_path_text(p_event_data, 'transferred_to', i::TEXT) LIKE '$RCAnonymousID:%' THEN
          PERFORM map_rc_id_to_user_id(
            jsonb_extract_path_text(p_event_data, 'transferred_to', i::TEXT),
            v_to_std_id
          );
        END IF;
      END LOOP;
    END IF;
  END IF;
  
  -- 返回结果
  v_result := jsonb_build_object(
    'success', array_length(v_sub_ids, 1) > 0,
    'transferred_subscriptions', v_sub_ids,
    'from_user_id', v_from_std_id,
    'to_user_id', v_to_std_id
  );
  
  RETURN v_result;
END;
$$;

-- 确保types表存在用于格式化
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM pg_type 
    WHERE typname = 'subscription_status'
  ) THEN
    CREATE TYPE subscription_status AS ENUM (
      'active', 
      'paused', 
      'cancelled', 
      'expired', 
      'refunded',
      'transferred',
      'converted', 
      'trial_cancelled'
    );
  END IF;
EXCEPTION 
  WHEN OTHERS THEN
    NULL;
END$$;

-- 重载schema以使更改生效
NOTIFY pgrst, 'reload schema'; 