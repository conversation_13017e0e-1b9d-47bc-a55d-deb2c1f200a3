-- 更新软删除用户函数，同时删除auth.users记录
-- 这样用户注销后可以用相同邮箱重新注册

-- 首先为subscriptions和subscription_events表添加缺失的deleted_at字段
ALTER TABLE public.subscriptions 
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

ALTER TABLE public.subscription_events 
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- 添加字段注释
COMMENT ON COLUMN public.subscriptions.deleted_at IS '订阅删除时间';
COMMENT ON COLUMN public.subscription_events.deleted_at IS '订阅事件删除时间';

-- 删除现有函数
DROP FUNCTION IF EXISTS soft_delete_user(UUID);

-- 重新创建软删除函数，包含auth.users删除
CREATE OR REPLACE FUNCTION soft_delete_user(target_user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- 1. 软删除profiles表记录并修改邮箱和用户名避免冲突
    UPDATE profiles SET 
        is_deleted_by_user = true,
        deleted_at = NOW(),
        email = CASE 
            WHEN email IS NOT NULL 
            THEN 'deleted_' || extract(epoch from now())::bigint || '_' || email 
            ELSE NULL 
        END,
        username = CASE 
            WHEN username IS NOT NULL 
            THEN 'deleted_' || extract(epoch from now())::bigint || '_' || username 
            ELSE NULL 
        END
    WHERE id = target_user_id;
    
    -- 2. 软删除订阅记录（如果表存在且有相关字段）
    UPDATE subscriptions SET 
        is_deleted_by_user = true,
        deleted_at = NOW()
    WHERE user_id = target_user_id
      AND EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'subscriptions' 
                  AND column_name = 'user_id' 
                  AND table_schema = 'public');
    
    -- 3. 软删除订阅事件记录（如果表存在且有相关字段）
    UPDATE subscription_events SET 
        is_deleted_by_user = true,
        deleted_at = NOW()
    WHERE user_id = target_user_id
      AND EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'subscription_events' 
                  AND column_name = 'user_id' 
                  AND table_schema = 'public');
    
    -- 4. 软删除user_identifiers记录（修改可能冲突的字段）
    UPDATE public.user_identifiers SET 
        user_id = CASE 
            WHEN user_id IS NOT NULL 
            THEN 'deleted_' || extract(epoch from now())::bigint || '_' || user_id 
            ELSE NULL 
        END,
        anonymous_id = CASE 
            WHEN anonymous_id IS NOT NULL 
            THEN 'deleted_' || extract(epoch from now())::bigint || '_' || anonymous_id 
            ELSE NULL 
        END,
        updated_at = NOW()
    WHERE user_id = target_user_id::TEXT
      AND EXISTS (SELECT 1 FROM information_schema.tables 
                  WHERE table_name = 'user_identifiers' 
                  AND table_schema = 'public');
    
    -- 5. 软删除auth.users记录（只修改确实存在的字段）
    UPDATE auth.users SET 
        email = CASE 
            WHEN email IS NOT NULL 
            THEN 'deleted_' || extract(epoch from now())::bigint || '_' || email 
            ELSE NULL 
        END,
        raw_user_meta_data = jsonb_build_object(
            'deleted_by_user', true,
            'deleted_at', now()::text,
            'original_email', COALESCE(email, ''),
            'original_data', COALESCE(raw_user_meta_data, '{}')
        )
    WHERE id = target_user_id;
    
    -- 6. 记录操作日志（如果表创建成功）
    INSERT INTO public.user_deletion_logs (user_id, deleted_at, deletion_type)
    VALUES (target_user_id, NOW(), 'user_requested');
    
EXCEPTION
    WHEN OTHERS THEN
        -- 记录错误但不回滚，确保至少profiles表被标记为删除
        INSERT INTO public.error_logs (user_id, error_message, occurred_at)
        VALUES (target_user_id, SQLERRM, NOW());
        RAISE;
END;
$$;

-- 创建用户删除日志表（如果不存在）
CREATE TABLE IF NOT EXISTS public.user_deletion_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE NOT NULL,
    deletion_type TEXT NOT NULL DEFAULT 'user_requested',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建错误日志表（如果不存在）
CREATE TABLE IF NOT EXISTS public.error_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID,
    error_message TEXT NOT NULL,
    occurred_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 设置RLS策略
ALTER TABLE public.user_deletion_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.error_logs ENABLE ROW LEVEL SECURITY;

-- 只允许超级管理员查看这些日志
CREATE POLICY "Only superuser can access deletion logs" ON public.user_deletion_logs
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Only superuser can access error logs" ON public.error_logs
    FOR ALL USING (auth.role() = 'service_role');

-- 为函数设置权限
GRANT EXECUTE ON FUNCTION soft_delete_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION soft_delete_user(UUID) TO anon; 