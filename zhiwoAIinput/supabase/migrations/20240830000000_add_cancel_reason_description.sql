-- 20240830000000_add_cancel_reason_description.sql
-- 添加订阅取消原因描述字段

-- 添加取消原因描述字段到subscriptions表
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS cancellation_reason_description TEXT;

-- 添加字段注释
COMMENT ON COLUMN public.subscriptions.cancellation_reason_description IS '标准化的订阅取消原因描述';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_subscriptions_cancellation_reason ON public.subscriptions(cancellation_reason) 
  WHERE cancellation_reason IS NOT NULL;

-- 创建RPC函数用于检查列是否存在
CREATE OR REPLACE FUNCTION public.has_column(table_name TEXT, column_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  result BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_schema = 'public'
      AND table_name = $1
      AND column_name = $2
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 批量更新现有记录的取消原因描述
CREATE OR REPLACE FUNCTION public.update_cancellation_descriptions() RETURNS void AS $$
DECLARE
  r RECORD;
BEGIN
  FOR r IN 
    SELECT id, cancellation_reason 
    FROM public.subscriptions 
    WHERE cancellation_reason IS NOT NULL 
      AND (cancellation_reason_description IS NULL OR cancellation_reason_description = '')
  LOOP
    -- 简单的映射，可以根据实际情况扩展
    UPDATE public.subscriptions SET
      cancellation_reason_description = CASE 
        -- Apple常见原因
        WHEN r.cancellation_reason ILIKE '%customer_cancelled%' THEN '用户主动取消'
        WHEN r.cancellation_reason ILIKE '%billing_error%' THEN '账单错误'
        WHEN r.cancellation_reason ILIKE '%price_increase%' THEN '价格上涨'
        WHEN r.cancellation_reason ILIKE '%declined%' THEN '支付被拒'
        WHEN r.cancellation_reason ILIKE '%failed_billing%' THEN '账单支付失败'
        WHEN r.cancellation_reason ILIKE '%app_deleted%' THEN '应用被删除'
        WHEN r.cancellation_reason ILIKE '%refund%' THEN '用户申请退款'
        
        -- Google常见原因
        WHEN r.cancellation_reason ILIKE '%user_cancelled%' THEN '用户主动取消'
        WHEN r.cancellation_reason ILIKE '%payment_issue%' THEN '支付问题'
        WHEN r.cancellation_reason ILIKE '%system_cancelled%' THEN '系统取消'
        
        -- 通用描述
        WHEN r.cancellation_reason ILIKE '%voluntary%' THEN '用户自愿取消'
        WHEN r.cancellation_reason ILIKE '%involuntary%' THEN '非自愿取消（如支付问题）'
        ELSE r.cancellation_reason
      END
    WHERE id = r.id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 执行更新函数
SELECT public.update_cancellation_descriptions();

-- 删除临时函数
DROP FUNCTION public.update_cancellation_descriptions(); 