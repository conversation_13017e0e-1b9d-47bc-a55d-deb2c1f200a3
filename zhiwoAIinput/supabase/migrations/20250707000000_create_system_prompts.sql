-- 创建系统提示词表
CREATE TABLE IF NOT EXISTS public.system_prompts (
  id varchar(50) PRIMARY KEY,
  name varchar(100) NOT NULL,
  description text,
  prompt_content text NOT NULL,
  version integer DEFAULT 1,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 添加RLS策略（只允许读取）
ALTER TABLE public.system_prompts ENABLE ROW LEVEL SECURITY;

-- 允许所有用户读取活跃的系统提示词
CREATE POLICY "Allow read access to active system prompts" ON public.system_prompts
  FOR SELECT USING (is_active = true);

-- 插入默认的系统提示词
INSERT INTO public.system_prompts (id, name, description, prompt_content, version, is_active)
VALUES (
  'default_system_prompt',
  '默认系统提示词',
  '用于文本优化的默认系统提示词',
  '你是文本优化专家。将用户的文字优化为流畅、准确、符合语法的内容。

核心要求：
- 保持原意和语气不变
- 修正错别字和语法错误  
- 优化标点符号（中文全角，英文半角）
- 删除无意义重复词语
- 保持原始语言不变，除非明确要求翻译，否则不要改变用户输入的语言
- 直接返回优化结果，不要解释

风格要求：
${templatePrompt}

请严格按照风格要求优化文本。',
  3,
  true
) ON CONFLICT (id) DO UPDATE 
SET 
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  prompt_content = EXCLUDED.prompt_content,
  version = EXCLUDED.version,
  is_active = EXCLUDED.is_active,
  updated_at = timezone('utc'::text, now());

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_system_prompts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_system_prompts_updated_at
  BEFORE UPDATE ON public.system_prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_system_prompts_updated_at(); 