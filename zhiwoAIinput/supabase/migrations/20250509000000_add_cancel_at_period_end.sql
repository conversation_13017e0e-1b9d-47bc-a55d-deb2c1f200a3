-- 添加缺失的 cancel_at_period_end 字段
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS cancel_at_period_end BOOLEAN DEFAULT false;

-- 添加字段注释
COMMENT ON COLUMN public.subscriptions.cancel_at_period_end IS '是否在当前订阅期结束时取消';

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_subscriptions_cancel_at_period_end ON public.subscriptions(cancel_at_period_end)
WHERE cancel_at_period_end = true;

-- 更新缓存
NOTIFY pgrst, 'reload schema'; 