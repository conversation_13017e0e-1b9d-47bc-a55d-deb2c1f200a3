-- 创建统一代理使用日志表
CREATE TABLE IF NOT EXISTS unified_proxy_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    service TEXT NOT NULL,
    path TEXT NOT NULL,
    method TEXT NOT NULL DEFAULT 'POST',
    request_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    is_vip BOOLEAN NOT NULL DEFAULT false,
    response_status INTEGER,
    bytes_transferred BIGINT DEFAULT 0,
    -- AI 服务特定字段
    model TEXT,
    prompt_tokens INTEGER,
    completion_tokens INTEGER,
    total_tokens INTEGER,
    cost_estimate DECIMAL(10,6),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_unified_proxy_logs_user_id ON unified_proxy_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_unified_proxy_logs_user_timestamp ON unified_proxy_logs(user_id, request_timestamp);
CREATE INDEX IF NOT EXISTS idx_unified_proxy_logs_service ON unified_proxy_logs(service);
CREATE INDEX IF NOT EXISTS idx_unified_proxy_logs_user_service_timestamp ON unified_proxy_logs(user_id, service, request_timestamp);
CREATE INDEX IF NOT EXISTS idx_unified_proxy_logs_model ON unified_proxy_logs(model) WHERE model IS NOT NULL;

-- 创建视图用于统计分析
CREATE OR REPLACE VIEW unified_proxy_stats AS
SELECT 
    user_id,
    service,
    DATE_TRUNC('hour', request_timestamp) as hour,
    DATE_TRUNC('day', request_timestamp) as day,
    COUNT(*) as request_count,
    SUM(bytes_transferred) as total_bytes,
    AVG(CASE WHEN response_status BETWEEN 200 AND 299 THEN 1 ELSE 0 END) as success_rate,
    -- AI 统计
    SUM(COALESCE(prompt_tokens, 0)) as total_prompt_tokens,
    SUM(COALESCE(completion_tokens, 0)) as total_completion_tokens,
    SUM(COALESCE(total_tokens, 0)) as total_tokens_used,
    SUM(COALESCE(cost_estimate, 0)) as total_cost
FROM unified_proxy_logs
GROUP BY user_id, service, hour, day;

-- 创建AI服务专用视图
CREATE OR REPLACE VIEW ai_usage_stats AS
SELECT 
    user_id,
    service,
    model,
    DATE_TRUNC('day', request_timestamp) as day,
    COUNT(*) as request_count,
    SUM(prompt_tokens) as total_prompt_tokens,
    SUM(completion_tokens) as total_completion_tokens,
    SUM(total_tokens) as total_tokens,
    SUM(cost_estimate) as total_cost,
    AVG(cost_estimate) as avg_cost_per_request
FROM unified_proxy_logs
WHERE model IS NOT NULL
GROUP BY user_id, service, model, day;

-- 添加RLS (Row Level Security)
ALTER TABLE unified_proxy_logs ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略：用户只能查看自己的日志
CREATE POLICY "Users can view own proxy logs" ON unified_proxy_logs
    FOR SELECT USING (auth.uid() = user_id);

-- 创建RLS策略：服务角色可以插入日志
CREATE POLICY "Service role can insert proxy logs" ON unified_proxy_logs
    FOR INSERT WITH CHECK (true);

-- 创建RLS策略：服务角色可以查看所有日志
CREATE POLICY "Service role can view all proxy logs" ON unified_proxy_logs
    FOR SELECT USING (auth.role() = 'service_role');

-- 添加注释
COMMENT ON TABLE unified_proxy_logs IS '统一代理服务使用日志表';
COMMENT ON COLUMN unified_proxy_logs.user_id IS '用户ID';
COMMENT ON COLUMN unified_proxy_logs.service IS '使用的服务类型 (siliconflow, openai, anthropic, elevenlabs, generic)';
COMMENT ON COLUMN unified_proxy_logs.path IS '请求路径';
COMMENT ON COLUMN unified_proxy_logs.method IS 'HTTP方法';
COMMENT ON COLUMN unified_proxy_logs.request_timestamp IS '请求时间戳';
COMMENT ON COLUMN unified_proxy_logs.is_vip IS '是否为VIP用户';
COMMENT ON COLUMN unified_proxy_logs.response_status IS 'HTTP响应状态码';
COMMENT ON COLUMN unified_proxy_logs.bytes_transferred IS '传输字节数';
COMMENT ON COLUMN unified_proxy_logs.model IS 'AI模型名称（仅AI服务）';
COMMENT ON COLUMN unified_proxy_logs.prompt_tokens IS '输入tokens数量（仅AI服务）';
COMMENT ON COLUMN unified_proxy_logs.completion_tokens IS '输出tokens数量（仅AI服务）';
COMMENT ON COLUMN unified_proxy_logs.total_tokens IS '总tokens数量（仅AI服务）';
COMMENT ON COLUMN unified_proxy_logs.cost_estimate IS '预估成本（仅AI服务）'; 