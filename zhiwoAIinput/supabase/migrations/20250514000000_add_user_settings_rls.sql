-- 迁移文件: 20250514000000_add_user_settings_rls.sql
-- 描述: 为user_settings表添加RLS策略，确保用户只能访问和修改自己的设置

-- 准备工作: 确保启用了RLS (虽然在初始模式中已经启用，但为了保险起见再次设置)
ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;

-- 添加用户可以查询自己设置的策略
-- 这确保用户只能查看自己的设置，而不能查看其他用户的设置
CREATE POLICY "用户可以查看自己的设置"
ON public.user_settings
FOR SELECT
USING (auth.uid() = user_id);

-- 添加用户可以更新自己设置的策略
-- 这确保用户只能更新自己的设置，而不能更新其他用户的设置
CREATE POLICY "用户可以更新自己的设置"
ON public.user_settings
FOR UPDATE
USING (auth.uid() = user_id);

-- 添加用户可以插入自己设置的策略
-- 这确保用户只能为自己创建设置记录
CREATE POLICY "用户可以创建自己的设置"
ON public.user_settings
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- 添加用户可以删除自己设置的策略（可选）
-- 通常不需要，因为用户不应删除自己的设置，但为了完整性添加
CREATE POLICY "用户可以删除自己的设置"
ON public.user_settings
FOR DELETE
USING (auth.uid() = user_id);

-- 提供一些元数据信息，说明迁移的目的
COMMENT ON POLICY "用户可以查看自己的设置" ON public.user_settings IS '确保用户只能查看自己的应用设置';
COMMENT ON POLICY "用户可以更新自己的设置" ON public.user_settings IS '确保用户只能更新自己的应用设置';
COMMENT ON POLICY "用户可以创建自己的设置" ON public.user_settings IS '确保用户只能为自己创建应用设置';
COMMENT ON POLICY "用户可以删除自己的设置" ON public.user_settings IS '确保用户只能删除自己的应用设置';

-- 为服务角色添加额外的全局策略，确保服务级操作不受限制
CREATE POLICY "服务角色完全访问user_settings"
ON public.user_settings
FOR ALL
USING (auth.role() = 'service_role'::text)
WITH CHECK (auth.role() = 'service_role'::text);

COMMENT ON POLICY "服务角色完全访问user_settings" ON public.user_settings IS '允许服务角色完全访问user_settings表，用于后端API操作';

-- 完成迁移 