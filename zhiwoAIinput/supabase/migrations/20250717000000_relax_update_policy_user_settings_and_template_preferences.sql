-- 放宽user_settings和user_template_preferences表的UPDATE权限策略
-- 只要auth.uid() = user_id即可，无需body带user_id字段
-- INSERT策略仍保留WITH CHECK，防止伪造

-- user_settings
DROP POLICY IF EXISTS "Users can update own settings" ON public.user_settings;
CREATE POLICY "Users can update own settings" ON public.user_settings
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id);

-- user_template_preferences
DROP POLICY IF EXISTS "Users can update own template preferences" ON public.user_template_preferences;
CREATE POLICY "Users can update own template preferences" ON public.user_template_preferences
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id); 