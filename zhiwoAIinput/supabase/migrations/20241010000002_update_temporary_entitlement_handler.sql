-- 20241010000002_update_temporary_entitlement_handler.sql
-- 更新临时授权处理函数以支持字符串用户ID

-- 修改函数handle_temporary_entitlement支持字符串用户ID
CREATE OR REPLACE FUNCTION public.handle_temporary_entitlement(
  p_user_id TEXT,  -- 改为TEXT类型以兼容字符串ID
  p_entitlement_id TEXT,
  p_grant_id TEXT,
  p_granted_at TIMESTAMP WITH TIME ZONE,
  p_expires_at TIMESTAMP WITH TIME ZONE,
  p_reason TEXT,
  p_metadata JSONB
) RETURNS UUID AS $$
DECLARE
  v_grant_id UUID;
  v_uuid_user_id UUID;
BEGIN
  -- 尝试将用户ID转换为UUID
  BEGIN
    v_uuid_user_id := p_user_id::UUID;
  EXCEPTION WHEN OTHERS THEN
    -- 如果无法转换为UUID，尝试从profiles表查找
    SELECT id INTO v_uuid_user_id
    FROM public.profiles
    WHERE app_user_id = p_user_id
    LIMIT 1;
    
    -- 如果仍找不到用户ID，则返回错误
    IF v_uuid_user_id IS NULL THEN
      RAISE EXCEPTION '找不到用户ID: %', p_user_id;
    END IF;
  END;
  
  -- 检查是否已存在相同授权
  SELECT id INTO v_grant_id 
  FROM public.temporary_entitlements
  WHERE grant_id = p_grant_id
  LIMIT 1;
  
  -- 如果已存在则更新
  IF v_grant_id IS NOT NULL THEN
    UPDATE public.temporary_entitlements
    SET
      entitlement_id = p_entitlement_id,
      status = 'active',
      granted_at = p_granted_at,
      expires_at = p_expires_at,
      reason = p_reason,
      metadata = p_metadata,
      updated_at = now()
    WHERE id = v_grant_id;
  ELSE
    -- 否则创建新记录
    INSERT INTO public.temporary_entitlements (
      user_id,
      entitlement_id,
      grant_id,
      status,
      granted_at,
      expires_at,
      reason,
      metadata
    ) VALUES (
      v_uuid_user_id,
      p_entitlement_id,
      p_grant_id,
      'active',
      p_granted_at,
      p_expires_at,
      p_reason,
      p_metadata
    ) RETURNING id INTO v_grant_id;
  END IF;
  
  RETURN v_grant_id;
END;
$$ LANGUAGE plpgsql;

-- 创建一个函数用于查找并处理等待验证的临时授权
CREATE OR REPLACE FUNCTION public.validate_temporary_entitlement(
  p_transaction_id TEXT,
  p_is_valid BOOLEAN,
  p_subscription_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  v_grant_id TEXT;
  v_user_id UUID;
  v_entitlement_id TEXT;
BEGIN
  -- 查找匹配transaction_id的临时授权
  SELECT te.grant_id, te.user_id, te.entitlement_id
  INTO v_grant_id, v_user_id, v_entitlement_id
  FROM public.temporary_entitlements te
  WHERE 
    te.metadata->>'transaction_id' = p_transaction_id
    AND te.metadata->>'needs_validation' = 'true'
    AND te.status = 'active'
  ORDER BY te.granted_at DESC
  LIMIT 1;
  
  -- 如果找不到匹配的授权，返回false
  IF v_grant_id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- 根据验证结果更新临时授权
  IF p_is_valid THEN
    -- 购买验证成功，更新状态为已验证
    UPDATE public.temporary_entitlements
    SET 
      metadata = jsonb_set(
        jsonb_set(
          COALESCE(metadata, '{}'::jsonb),
          '{needs_validation}',
          'false'::jsonb
        ),
        '{validated_at}',
        to_jsonb(now())
      ),
      status = 'validated',
      updated_at = now()
    WHERE grant_id = v_grant_id;
    
    -- 如果提供了订阅ID，关联到该订阅
    IF p_subscription_id IS NOT NULL THEN
      UPDATE public.temporary_entitlements
      SET 
        metadata = jsonb_set(
          COALESCE(metadata, '{}'::jsonb),
          '{subscription_id}',
          to_jsonb(p_subscription_id)
        )
      WHERE grant_id = v_grant_id;
    END IF;
  ELSE
    -- 购买验证失败，标记为过期
    UPDATE public.temporary_entitlements
    SET 
      status = 'expired',
      metadata = jsonb_set(
        jsonb_set(
          COALESCE(metadata, '{}'::jsonb),
          '{needs_validation}',
          'false'::jsonb
        ),
        '{validation_failed}',
        'true'::jsonb
      ),
      updated_at = now()
    WHERE grant_id = v_grant_id;
    
    -- 如果用户VIP来源是临时授权且没有其他活跃授权，移除VIP状态
    IF NOT EXISTS (
      SELECT 1 FROM public.temporary_entitlements
      WHERE 
        user_id = v_user_id 
        AND grant_id != v_grant_id
        AND status = 'active'
    ) THEN
      UPDATE public.profiles
      SET 
        is_vip = false,
        vip_source = NULL,
        vip_updated_at = now()
      WHERE 
        id = v_user_id
        AND vip_source = 'temporary_grant';
    END IF;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 增加验证状态字段
ALTER TABLE public.temporary_entitlements ADD COLUMN IF NOT EXISTS is_validated BOOLEAN DEFAULT false;
ALTER TABLE public.temporary_entitlements ADD COLUMN IF NOT EXISTS validated_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.temporary_entitlements ADD COLUMN IF NOT EXISTS validation_subscription_id UUID REFERENCES public.subscriptions(id);
ALTER TABLE public.temporary_entitlements ADD COLUMN IF NOT EXISTS transaction_id TEXT;
ALTER TABLE public.temporary_entitlements ADD COLUMN IF NOT EXISTS product_id TEXT;

-- 添加字段注释
COMMENT ON COLUMN public.temporary_entitlements.is_validated IS '临时授权是否已验证(转为正式购买)';
COMMENT ON COLUMN public.temporary_entitlements.validated_at IS '临时授权验证时间';
COMMENT ON COLUMN public.temporary_entitlements.validation_subscription_id IS '验证关联的订阅ID';
COMMENT ON COLUMN public.temporary_entitlements.transaction_id IS '交易ID';
COMMENT ON COLUMN public.temporary_entitlements.product_id IS '产品ID'; 