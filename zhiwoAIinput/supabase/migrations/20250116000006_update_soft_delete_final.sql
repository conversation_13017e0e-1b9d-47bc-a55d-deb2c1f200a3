-- 最终版本的软删除函数，适配新的外键约束结构

-- 删除现有函数
DROP FUNCTION IF EXISTS soft_delete_user(UUID);

-- 重新创建最终版本的软删除函数
CREATE OR REPLACE FUNCTION soft_delete_user(target_user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- 1. 软删除profiles表记录（保持原邮箱，用于数据分析）
    UPDATE profiles SET 
        is_deleted_by_user = true,
        deleted_at = NOW(),
        updated_at = NOW()
    WHERE id = target_user_id;
    
    -- 2. 软删除订阅记录（这些现在引用profiles表，会保留）
    UPDATE subscriptions SET 
        is_deleted_by_user = true,
        deleted_at = NOW()
    WHERE user_id = target_user_id;
    
    -- 3. 软删除订阅事件记录（这些现在引用profiles表，会保留）
    UPDATE subscription_events SET 
        is_deleted_by_user = true,
        deleted_at = NOW()
    WHERE user_id = target_user_id;
    
    -- 4. 修改user_identifiers记录避免冲突
    UPDATE public.user_identifiers SET 
        user_id = 'deleted_' || extract(epoch from now())::bigint || '_' || user_id,
        anonymous_id = 'deleted_' || extract(epoch from now())::bigint || '_' || anonymous_id,
        updated_at = NOW()
    WHERE user_id = target_user_id::TEXT;
    
    -- 5. 删除用户的实时数据（这些表有CASCADE约束，会被自动删除）
    -- user_settings, style_templates会通过CASCADE自动删除
    -- 但我们先手动删除以确保逻辑清晰
    DELETE FROM public.user_settings WHERE user_id = target_user_id;
    DELETE FROM public.style_templates WHERE user_id = target_user_id AND is_system = false;
    
    -- 6. 记录操作日志
    INSERT INTO public.user_deletion_logs (user_id, deleted_at, deletion_type)
    VALUES (target_user_id, NOW(), 'user_requested')
    ON CONFLICT DO NOTHING;
    
    -- 7. 最后删除auth.users记录
    -- sessions和subscription_extension_history的user_id会被设为NULL
    -- profiles记录会保留（因为我们移除了外键约束）
    DELETE FROM auth.users WHERE id = target_user_id;
    
EXCEPTION
    WHEN OTHERS THEN
        -- 记录错误但不回滚，确保至少profiles表被标记为删除
        INSERT INTO public.error_logs (user_id, error_message, occurred_at)
        VALUES (target_user_id, SQLERRM, NOW())
        ON CONFLICT DO NOTHING;
        RAISE;
END;
$$;

-- 为函数设置权限
GRANT EXECUTE ON FUNCTION soft_delete_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION soft_delete_user(UUID) TO anon;

-- 添加函数注释
COMMENT ON FUNCTION soft_delete_user IS '软删除用户：删除认证记录但保留分析数据，支持邮箱重新注册';

-- 创建视图用于查询有效用户数据（排除已删除用户）
CREATE OR REPLACE VIEW active_user_sessions AS
SELECT s.* 
FROM sessions s
JOIN profiles p ON s.user_id = p.id
WHERE (p.is_deleted_by_user IS FALSE OR p.is_deleted_by_user IS NULL);

CREATE OR REPLACE VIEW active_user_subscriptions AS
SELECT s.* 
FROM subscriptions s
JOIN profiles p ON s.user_id = p.id
WHERE (p.is_deleted_by_user IS FALSE OR p.is_deleted_by_user IS NULL)
  AND (s.is_deleted_by_user IS FALSE OR s.is_deleted_by_user IS NULL);

-- 为视图添加注释
COMMENT ON VIEW active_user_sessions IS '活跃用户的会话记录（排除已删除用户）';
COMMENT ON VIEW active_user_subscriptions IS '活跃用户的订阅记录（排除已删除用户）'; 