-- 更新active_subscribers视图，确保每个用户只有一个活跃订阅
DROP VIEW IF EXISTS public.active_subscribers;

CREATE VIEW public.active_subscribers AS
SELECT 
  p.id,
  p.email,
  s.product_id,
  s.expires_at,
  s.store,
  sp.name AS plan_name,
  sp.features
FROM 
  (
    SELECT DISTINCT ON (user_id) 
      id, user_id, product_id, expires_at, store, status
    FROM 
      subscriptions
    WHERE 
      status = 'active'
    ORDER BY 
      user_id, created_at DESC
  ) s
JOIN 
  profiles p ON p.id = s.user_id
LEFT JOIN 
  subscription_plans sp ON s.product_id = sp.plan_id
WHERE 
  p.is_vip = true;

-- 添加函数：在新增活跃订阅时，将用户已有的活跃订阅标记为历史
CREATE OR REPLACE FUNCTION public.update_subscription_status()
RETURNS TRIGGER AS $$
BEGIN
  -- 如果新插入的订阅状态为active，则将同一用户的其他active订阅更新为历史
  IF NEW.status = 'active' THEN
    -- 将同一用户的旧的active订阅更新为历史
    UPDATE subscriptions
    SET 
      status = 'historical',
      updated_at = NOW()
    WHERE 
      user_id = NEW.user_id AND 
      id != NEW.id AND
      status = 'active';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 确保subscription_status类型包含'historical'状态
DO $$
BEGIN
  -- 检查是否有subscription_status类型
  IF EXISTS (
    SELECT FROM pg_type 
    WHERE typname = 'subscription_status'
  ) THEN
    -- 检查类型中是否已包含'historical'
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE typname = 'subscription_status' 
      AND enumlabel = 'historical'
    ) THEN
      -- 添加'historical'到enum
      ALTER TYPE subscription_status ADD VALUE 'historical';
    END IF;
  ELSE
    -- 创建新的subscription_status类型
    CREATE TYPE subscription_status AS ENUM (
      'active', 
      'paused', 
      'cancelled', 
      'expired', 
      'refunded',
      'transferred',
      'converted', 
      'trial_cancelled',
      'historical'
    );
  END IF;
EXCEPTION 
  WHEN OTHERS THEN
    -- 如果失败，忽略错误继续执行
    NULL;
END
$$;

-- 在subscriptions表中创建触发器
DROP TRIGGER IF EXISTS tr_update_subscription_status ON subscriptions;

CREATE TRIGGER tr_update_subscription_status
AFTER INSERT ON subscriptions
FOR EACH ROW
EXECUTE FUNCTION update_subscription_status();

-- 修复现有数据：为每个用户保留最新的活跃订阅，将其他活跃订阅标记为历史
DO $$
DECLARE
  v_user_id TEXT;
  v_latest_sub_id UUID;
BEGIN
  -- 遍历每个拥有多个活跃订阅的用户
  FOR v_user_id IN (
    SELECT user_id 
    FROM subscriptions 
    WHERE status = 'active' 
    GROUP BY user_id 
    HAVING COUNT(*) > 1
  )
  LOOP
    -- 获取该用户最新的活跃订阅ID
    SELECT id INTO v_latest_sub_id
    FROM subscriptions
    WHERE user_id = v_user_id AND status = 'active'
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- 将该用户其他的活跃订阅标记为历史
    UPDATE subscriptions
    SET status = 'historical', updated_at = NOW()
    WHERE user_id = v_user_id 
      AND status = 'active' 
      AND id != v_latest_sub_id;
      
    RAISE NOTICE '用户 % 的旧订阅已标记为历史，保留最新订阅 %', v_user_id, v_latest_sub_id;
  END LOOP;
END
$$;

-- 更新缓存
NOTIFY pgrst, 'reload schema'; 