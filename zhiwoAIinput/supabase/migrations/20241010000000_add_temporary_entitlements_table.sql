-- 20241010000000_add_temporary_entitlements_table.sql
-- 添加临时授权记录表

-- 创建临时授权表
CREATE TABLE IF NOT EXISTS public.temporary_entitlements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  entitlement_id TEXT,
  grant_id TEXT,
  status TEXT DEFAULT 'active',  -- active, expired, revoked
  granted_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  revoked_at TIMESTAMP WITH TIME ZONE,
  reason TEXT,
  source TEXT DEFAULT 'revenuecat',
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 添加表注释
COMMENT ON TABLE public.temporary_entitlements IS '记录临时授予用户的权益信息';

-- 添加列注释
COMMENT ON COLUMN public.temporary_entitlements.id IS '主键ID';
COMMENT ON COLUMN public.temporary_entitlements.user_id IS '用户ID';
COMMENT ON COLUMN public.temporary_entitlements.entitlement_id IS '权益ID';
COMMENT ON COLUMN public.temporary_entitlements.grant_id IS '授权ID';
COMMENT ON COLUMN public.temporary_entitlements.status IS '状态(活跃/过期/撤销)';
COMMENT ON COLUMN public.temporary_entitlements.granted_at IS '授权时间';
COMMENT ON COLUMN public.temporary_entitlements.expires_at IS '过期时间';
COMMENT ON COLUMN public.temporary_entitlements.revoked_at IS '撤销时间';
COMMENT ON COLUMN public.temporary_entitlements.reason IS '授权原因';
COMMENT ON COLUMN public.temporary_entitlements.source IS '授权来源';
COMMENT ON COLUMN public.temporary_entitlements.metadata IS '元数据';
COMMENT ON COLUMN public.temporary_entitlements.created_at IS '创建时间';
COMMENT ON COLUMN public.temporary_entitlements.updated_at IS '更新时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_temporary_entitlements_user_id ON public.temporary_entitlements(user_id);
CREATE INDEX IF NOT EXISTS idx_temporary_entitlements_status ON public.temporary_entitlements(status);
CREATE INDEX IF NOT EXISTS idx_temporary_entitlements_expires_at ON public.temporary_entitlements(expires_at);
CREATE INDEX IF NOT EXISTS idx_temporary_entitlements_grant_id ON public.temporary_entitlements(grant_id);

-- 创建函数用于处理临时授权
CREATE OR REPLACE FUNCTION public.handle_temporary_entitlement(
  p_user_id UUID,
  p_entitlement_id TEXT,
  p_grant_id TEXT,
  p_granted_at TIMESTAMP WITH TIME ZONE,
  p_expires_at TIMESTAMP WITH TIME ZONE,
  p_reason TEXT,
  p_metadata JSONB
) RETURNS UUID AS $$
DECLARE
  v_grant_id UUID;
BEGIN
  -- 检查是否已存在相同授权
  SELECT id INTO v_grant_id 
  FROM public.temporary_entitlements
  WHERE grant_id = p_grant_id
  LIMIT 1;
  
  -- 如果已存在则更新
  IF v_grant_id IS NOT NULL THEN
    UPDATE public.temporary_entitlements
    SET
      entitlement_id = p_entitlement_id,
      status = 'active',
      granted_at = p_granted_at,
      expires_at = p_expires_at,
      reason = p_reason,
      metadata = p_metadata,
      updated_at = now()
    WHERE id = v_grant_id;
  ELSE
    -- 否则创建新记录
    INSERT INTO public.temporary_entitlements (
      user_id,
      entitlement_id,
      grant_id,
      status,
      granted_at,
      expires_at,
      reason,
      metadata
    ) VALUES (
      p_user_id,
      p_entitlement_id,
      p_grant_id,
      'active',
      p_granted_at,
      p_expires_at,
      p_reason,
      p_metadata
    ) RETURNING id INTO v_grant_id;
  END IF;
  
  RETURN v_grant_id;
END;
$$ LANGUAGE plpgsql;

-- 创建函数用于自动处理过期的临时授权
CREATE OR REPLACE FUNCTION public.expire_temporary_entitlements() RETURNS INTEGER AS $$
DECLARE
  v_count INTEGER;
BEGIN
  -- 更新已过期但状态仍为活跃的临时授权
  WITH expired_grants AS (
    UPDATE public.temporary_entitlements
    SET 
      status = 'expired',
      updated_at = now()
    WHERE 
      status = 'active'
      AND expires_at < now()
    RETURNING id
  )
  SELECT COUNT(*) INTO v_count FROM expired_grants;
  
  RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- 创建一个视图用于分析临时授权使用情况
CREATE OR REPLACE VIEW public.temporary_entitlement_analysis AS
SELECT
  user_id,
  entitlement_id,
  status,
  granted_at,
  expires_at,
  EXTRACT(EPOCH FROM (expires_at - granted_at)) / 86400 AS duration_days,
  reason,
  source,
  CASE
    WHEN expires_at > now() AND status = 'active' THEN true
    ELSE false
  END AS is_active,
  CASE
    WHEN status = 'expired' THEN 'EXPIRED'
    WHEN status = 'revoked' THEN 'REVOKED'
    WHEN expires_at < now() THEN 'EXPIRED'
    ELSE 'ACTIVE'
  END AS current_status
FROM
  public.temporary_entitlements;

-- 定期清理过期的临时授权
DO $$
DECLARE
  cron_exists BOOLEAN;
BEGIN
  -- 检查pg_cron扩展是否存在
  SELECT EXISTS (
    SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
  ) INTO cron_exists;
  
  IF cron_exists THEN
    -- 创建定时任务，每小时执行一次
    PERFORM cron.schedule(
      'expire-temporary-entitlements',
      '0 * * * *',
      'SELECT public.expire_temporary_entitlements()'
    );
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'pg_cron扩展不存在，跳过定时任务创建';
END
$$; 