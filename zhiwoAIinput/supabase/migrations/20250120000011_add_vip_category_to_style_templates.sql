-- 为style_templates表添加VIP专用和分类字段
-- 添加is_vip_only字段用于标识VIP专用模板
-- 添加category字段用于模板分类

-- 添加is_vip_only字段（是否为VIP专用模板）
ALTER TABLE public.style_templates 
ADD COLUMN IF NOT EXISTS is_vip_only BOOLEAN NOT NULL DEFAULT false;

-- 添加category字段（模板分类）
ALTER TABLE public.style_templates 
ADD COLUMN IF NOT EXISTS category TEXT;

-- 为字段添加注释
COMMENT ON COLUMN public.style_templates.is_vip_only IS '是否为VIP专用模板，true表示仅VIP用户可使用';
COMMENT ON COLUMN public.style_templates.category IS '模板分类，如：工作沟通、日常聊天、情感表达、专业写作、创意写作等';

-- 为现有的系统模板更新VIP标志和分类
UPDATE public.style_templates 
SET 
  is_vip_only = CASE 
    WHEN name IN ('小红书文案', '朋友圈文案', '日语翻译', '韩语翻译', '印尼语翻译', '德语翻译', '法语翻译', '西班牙语翻译', 'Recraft生图', 'Midjourney V7', 'Stable Diffusion', '即梦AI', '创意文案') THEN true
    ELSE false
  END,
  category = CASE 
    WHEN name IN ('发邮件', '做notes记录', '正式公文', '学术论文', '新闻报道') THEN 'scenario'
    WHEN name IN ('日常发信息', 'emoji表情', '口语化表达') THEN 'scenario'
    WHEN name IN ('小红书文案', '朋友圈文案') THEN 'scenario'
    WHEN name IN ('英语翻译', '日语翻译', '韩语翻译', '印尼语翻译', '德语翻译', '法语翻译', '西班牙语翻译') THEN 'translation'
    WHEN name IN ('Recraft生图', 'Midjourney V7', 'Stable Diffusion', '即梦AI') THEN 'ai_prompt'
    WHEN name IN ('正式公文', '学术论文', '新闻报道', '创意文案', '口语化表达') THEN 'writing'
    ELSE 'other'
  END
WHERE is_system = true;

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_style_templates_is_vip_only ON public.style_templates(is_vip_only);
CREATE INDEX IF NOT EXISTS idx_style_templates_category ON public.style_templates(category) WHERE category IS NOT NULL; 