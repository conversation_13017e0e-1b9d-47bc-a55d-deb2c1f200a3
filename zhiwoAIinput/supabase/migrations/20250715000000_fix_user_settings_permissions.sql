-- 修复user_settings表的权限问题
-- 确保authenticated和anon用户可以正确访问和修改自己的设置

-- 1. 确保authenticated角色有user_settings表的基本权限
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.user_settings TO authenticated;

-- 2. 启用RLS（如果未启用）
ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;

-- 3. 创建RLS策略
-- 允许authenticated用户查看自己的设置
DROP POLICY IF EXISTS "Users can view own settings" ON public.user_settings;
CREATE POLICY "Users can view own settings" ON public.user_settings
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

-- 允许authenticated用户更新自己的设置
DROP POLICY IF EXISTS "Users can update own settings" ON public.user_settings;
CREATE POLICY "Users can update own settings" ON public.user_settings
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- 允许authenticated用户插入自己的设置
DROP POLICY IF EXISTS "Users can insert own settings" ON public.user_settings;
CREATE POLICY "Users can insert own settings" ON public.user_settings
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- 允许authenticated用户删除自己的设置
DROP POLICY IF EXISTS "Users can delete own settings" ON public.user_settings;
CREATE POLICY "Users can delete own settings" ON public.user_settings
    FOR DELETE TO authenticated
    USING (auth.uid() = user_id);

-- 4. 允许anon角色查看user_settings（如果需要未登录用户查看某些设置）
GRANT SELECT ON TABLE public.user_settings TO anon;

-- 5. anon角色的RLS策略（如果需要）
DROP POLICY IF EXISTS "Anyone can view public settings" ON public.user_settings;
CREATE POLICY "Anyone can view public settings" ON public.user_settings
    FOR SELECT TO anon
    USING (true);  -- 或者根据需要添加条件限制只能查看某些字段 