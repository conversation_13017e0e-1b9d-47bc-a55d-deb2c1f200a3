-- Create system_configs table
CREATE TABLE IF NOT EXISTS public.system_configs (
    key TEXT PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add RLS policies for system_configs
ALTER TABLE public.system_configs ENABLE ROW LEVEL SECURITY;

-- Only allow authenticated users to read system configs
CREATE POLICY "Allow authenticated users to read system configs" ON public.system_configs
    FOR SELECT TO authenticated
    USING (true);

-- Only allow service role to modify system configs
CREATE POLICY "Allow service role to modify system configs" ON public.system_configs
    FOR ALL TO service_role
    USING (true)
    WITH CHECK (true);

-- Insert initial whisper limit config
INSERT INTO public.system_configs (key, value, description)
VALUES (
    'daily_whisper_limit',
    '{"free_user_limit": 5}'::jsonb,
    'Daily usage limit for Whisper model by free users'
);

-- Add trigger for updating updated_at
CREATE OR REPLACE FUNCTION update_system_configs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_system_configs_update_updated_at
    BEFORE UPDATE ON public.system_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_system_configs_updated_at();

-- Add comments
COMMENT ON TABLE public.system_configs IS '系统配置表，用于存储各种系统级配置';
COMMENT ON COLUMN public.system_configs.key IS '配置键名';
COMMENT ON COLUMN public.system_configs.value IS '配置值（JSONB格式）';
COMMENT ON COLUMN public.system_configs.description IS '配置说明'; 