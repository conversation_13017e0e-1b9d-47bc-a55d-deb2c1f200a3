-- 启用必要的PostgreSQL扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_graphql";
CREATE EXTENSION IF NOT EXISTS "pgjwt";
CREATE EXTENSION IF NOT EXISTS "vector";

-- 创建auth相关schema (如果使用Supabase auth)
-- 通常Supabase会自动创建auth schema，此处仅为确保

-- 创建用户档案表
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  username TEXT UNIQUE,
  email TEXT UNIQUE,
  avatar_url TEXT,
  is_vip BOOLEAN DEFAULT false,
  vip_product_id TEXT,
  vip_expires_at TIMESTAMP WITH TIME ZONE,
  vip_environment TEXT,
  vip_updated_at TIMESTAMP WITH TIME ZONE,
  custom_templates_count INTEGER DEFAULT 0,
  voice_minutes_used INTEGER DEFAULT 0,
  voice_minutes_limit INTEGER DEFAULT 0,
  api_key_type TEXT DEFAULT 'free',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 添加表注释
COMMENT ON TABLE public.profiles IS '存储用户个人信息、VIP状态和使用限制';

-- 创建订阅计划表
CREATE TABLE IF NOT EXISTS public.subscription_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  plan_id TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  price_usd DECIMAL(10, 2),
  duration_days INTEGER,
  features JSONB DEFAULT '{}'::jsonb,
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  apple_product_id TEXT,
  google_product_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 添加表注释
COMMENT ON TABLE public.subscription_plans IS '定义应用内可购买的订阅计划及其特性';

-- 创建订阅记录表
CREATE TABLE IF NOT EXISTS public.subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  product_id TEXT NOT NULL,
  status TEXT NOT NULL,
  environment TEXT,
  store TEXT NOT NULL,
  transaction_id TEXT UNIQUE,
  original_transaction_id TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  purchase_date TIMESTAMP WITH TIME ZONE,
  webhook_received_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  auto_renew_status BOOLEAN DEFAULT true,
  cancellation_reason TEXT,
  event_type TEXT,
  last_event_at TIMESTAMP WITH TIME ZONE
);

-- 添加表注释
COMMENT ON TABLE public.subscriptions IS '记录用户的订阅信息，包括状态、过期时间和交易细节';

-- 创建交易事件记录表
CREATE TABLE IF NOT EXISTS public.subscription_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES public.subscriptions(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  event_timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  transaction_id TEXT,
  original_transaction_id TEXT,
  product_id TEXT,
  event_data JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 添加表注释
COMMENT ON TABLE public.subscription_events IS '记录订阅相关的所有事件，如购买、续订、取消等';

-- 创建AI模型管理表
CREATE TABLE IF NOT EXISTS public.ai_models (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  model_id TEXT UNIQUE NOT NULL,
  provider TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  is_vip_only BOOLEAN DEFAULT true,
  model_type TEXT NOT NULL, -- text, voice
  api_key_type TEXT NOT NULL, -- free, premium
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 添加表注释
COMMENT ON TABLE public.ai_models IS '存储应用支持的AI模型信息，包括文本和语音模型';

-- 创建风格模板表
CREATE TABLE IF NOT EXISTS public.style_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  prompt_text TEXT NOT NULL,
  is_system BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  color TEXT DEFAULT 'blue',
  icon TEXT,
  position INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 添加表注释
COMMENT ON TABLE public.style_templates IS '存储系统预设和用户自定义的风格模板';

-- 创建用户会话表
CREATE TABLE IF NOT EXISTS public.sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  template_id UUID REFERENCES public.style_templates(id),
  source_text TEXT,
  processed_text TEXT,
  audio_duration INTEGER,
  word_count INTEGER,
  is_favorite BOOLEAN DEFAULT false,
  audio_file_path TEXT,
  is_synced BOOLEAN DEFAULT false, -- 是否已同步到云端
  voice_model_id TEXT,
  text_model_id TEXT
);

-- 添加表注释
COMMENT ON TABLE public.sessions IS '记录用户的文本处理会话，包括文本内容和音频生成结果';

-- 创建用户设置表
CREATE TABLE IF NOT EXISTS public.user_settings (
  user_id UUID PRIMARY KEY REFERENCES public.profiles(id) ON DELETE CASCADE,
  language TEXT DEFAULT 'zh-CN',
  theme TEXT DEFAULT 'light',
  default_text_model TEXT,
  default_voice_model TEXT,
  sync_enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 添加表注释
COMMENT ON TABLE public.user_settings IS '存储用户的应用偏好设置，如语言、主题和默认模型选择';

-- 创建触发器用于自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为需要自动更新updated_at的表创建触发器
CREATE TRIGGER update_profiles_modtime
BEFORE UPDATE ON public.profiles
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_subscription_plans_modtime
BEFORE UPDATE ON public.subscription_plans
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_style_templates_modtime
BEFORE UPDATE ON public.style_templates
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

CREATE TRIGGER update_user_settings_modtime
BEFORE UPDATE ON public.user_settings
FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- 创建或更新VIP状态处理函数
CREATE OR REPLACE FUNCTION public.update_user_vip_status()
RETURNS TRIGGER AS $$
DECLARE
  user_has_active_sub BOOLEAN;
  v_features JSONB;
  v_voice_minutes INTEGER;
  v_api_key_type TEXT;
BEGIN
  -- 检查用户是否有活跃订阅
  SELECT EXISTS (
    SELECT 1 FROM public.subscriptions 
    WHERE user_id = NEW.user_id 
    AND status = 'active' 
    AND (expires_at IS NULL OR expires_at > now())
  ) INTO user_has_active_sub;
  
  -- 获取订阅计划特性
  IF user_has_active_sub THEN
    SELECT sp.features 
    INTO v_features
    FROM public.subscription_plans sp
    WHERE sp.plan_id = NEW.product_id;
    
    v_voice_minutes := (v_features->>'voice_minutes')::INTEGER;
    v_api_key_type := v_features->>'api_key_type';
  ELSE
    v_voice_minutes := 0;
    v_api_key_type := 'free';
  END IF;
  
  -- 更新用户的VIP状态
  IF user_has_active_sub AND NOT EXISTS (
    SELECT 1 FROM public.profiles WHERE id = NEW.user_id AND is_vip = true
  ) THEN
    UPDATE public.profiles 
    SET 
      is_vip = true,
      vip_product_id = NEW.product_id,
      vip_expires_at = NEW.expires_at,
      vip_environment = NEW.environment,
      vip_updated_at = now(),
      voice_minutes_limit = v_voice_minutes,
      api_key_type = v_api_key_type
    WHERE id = NEW.user_id;
  ELSIF NOT user_has_active_sub AND EXISTS (
    SELECT 1 FROM public.profiles WHERE id = NEW.user_id AND is_vip = true
  ) THEN
    UPDATE public.profiles 
    SET 
      is_vip = false,
      vip_updated_at = now(),
      voice_minutes_limit = 0,
      api_key_type = 'free'
    WHERE id = NEW.user_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为订阅表创建触发器
CREATE TRIGGER update_vip_status_on_subscription_change
AFTER INSERT OR UPDATE ON public.subscriptions
FOR EACH ROW EXECUTE FUNCTION public.update_user_vip_status();

-- 处理RevenueCat事件的函数
CREATE OR REPLACE FUNCTION public.process_revenuecat_event(
  p_user_id UUID,
  p_event_type TEXT,
  p_product_id TEXT,
  p_transaction_id TEXT,
  p_original_transaction_id TEXT,
  p_expires_at TIMESTAMP WITH TIME ZONE,
  p_event_data JSONB
) RETURNS UUID AS $$
DECLARE
  v_subscription_id UUID;
  v_event_id UUID;
  v_plan_id TEXT;
BEGIN
  -- 获取对应的plan_id
  SELECT plan_id INTO v_plan_id 
  FROM public.subscription_plans 
  WHERE apple_product_id = p_product_id OR google_product_id = p_product_id
  LIMIT 1;
  
  -- 查找或创建订阅记录
  SELECT id INTO v_subscription_id FROM public.subscriptions 
  WHERE original_transaction_id = p_original_transaction_id OR transaction_id = p_transaction_id
  LIMIT 1;
  
  -- 如果未找到，则创建新记录
  IF v_subscription_id IS NULL THEN
    INSERT INTO public.subscriptions (
      user_id, product_id, status, transaction_id, original_transaction_id, 
      expires_at, purchase_date, event_type, last_event_at
    ) VALUES (
      p_user_id, COALESCE(v_plan_id, p_product_id), 
      CASE 
        WHEN p_event_type = 'INITIAL_PURCHASE' THEN 'active'
        WHEN p_event_type = 'CANCELLATION' THEN 'cancelled'
        WHEN p_event_type = 'EXPIRATION' THEN 'expired'
        ELSE 'unknown'
      END,
      p_transaction_id, p_original_transaction_id, 
      p_expires_at, now(), p_event_type, now()
    ) RETURNING id INTO v_subscription_id;
  ELSE
    -- 更新现有记录
    UPDATE public.subscriptions SET
      status = CASE 
        WHEN p_event_type = 'INITIAL_PURCHASE' THEN 'active'
        WHEN p_event_type = 'RENEWAL' THEN 'active'
        WHEN p_event_type = 'CANCELLATION' THEN 'cancelled'
        WHEN p_event_type = 'UNCANCELLATION' THEN 'active'
        WHEN p_event_type = 'EXPIRATION' THEN 'expired'
        WHEN p_event_type = 'BILLING_ISSUE' THEN status
        ELSE status
      END,
      product_id = COALESCE(v_plan_id, p_product_id, product_id),
      transaction_id = COALESCE(p_transaction_id, transaction_id),
      expires_at = COALESCE(p_expires_at, expires_at),
      event_type = p_event_type,
      last_event_at = now(),
      -- 如果取消订阅，记录取消原因
      cancellation_reason = CASE 
        WHEN p_event_type = 'CANCELLATION' THEN p_event_data->>'cancellation_reason'
        ELSE cancellation_reason
      END
    WHERE id = v_subscription_id;
  END IF;
  
  -- 记录事件
  INSERT INTO public.subscription_events (
    user_id, subscription_id, event_type, event_timestamp,
    transaction_id, original_transaction_id, product_id, event_data
  ) VALUES (
    p_user_id, v_subscription_id, p_event_type, now(),
    p_transaction_id, p_original_transaction_id, COALESCE(v_plan_id, p_product_id), p_event_data
  ) RETURNING id INTO v_event_id;
  
  RETURN v_event_id;
END;
$$ LANGUAGE plpgsql;

-- 检查用户是否可以创建自定义模板
CREATE OR REPLACE FUNCTION public.can_create_custom_template(user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_is_vip BOOLEAN;
  v_template_limit INTEGER;
  v_current_count INTEGER;
BEGIN
  -- 检查用户是否为VIP
  SELECT is_vip, custom_templates_count INTO v_is_vip, v_current_count
  FROM public.profiles WHERE id = user_id;
  
  -- 获取模板限制
  IF v_is_vip THEN
    RETURN true; -- VIP用户无限制
  ELSE
    -- 非VIP用户检查限制
    SELECT (features->>'custom_templates_limit')::INTEGER INTO v_template_limit 
    FROM public.subscription_plans WHERE plan_id = 'free';
    
    RETURN v_current_count < COALESCE(v_template_limit, 0);
  END IF;
END;
$$;

-- 更新用户语音使用时间函数
CREATE OR REPLACE FUNCTION public.update_voice_minutes_used(
  p_user_id UUID,
  p_minutes_used INTEGER
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_is_vip BOOLEAN;
  v_minutes_limit INTEGER;
  v_current_used INTEGER;
BEGIN
  -- 获取用户当前状态
  SELECT 
    is_vip,
    voice_minutes_limit,
    voice_minutes_used
  INTO 
    v_is_vip,
    v_minutes_limit,
    v_current_used
  FROM public.profiles
  WHERE id = p_user_id;
  
  -- 更新使用时间
  UPDATE public.profiles
  SET voice_minutes_used = voice_minutes_used + p_minutes_used
  WHERE id = p_user_id;
  
  -- 检查是否超出限制
  IF v_is_vip AND v_minutes_limit > 0 THEN
    -- VIP用户有限制
    RETURN (v_current_used + p_minutes_used) <= v_minutes_limit;
  ELSIF NOT v_is_vip THEN
    -- 非VIP用户不能使用高级语音功能
    RETURN false;
  ELSE
    -- VIP用户无限制
    RETURN true;
  END IF;
END;
$$;

-- 创建行级安全策略（RLS）
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.style_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_models ENABLE ROW LEVEL SECURITY;

-- 创建基本的安全策略
CREATE POLICY "用户可以查看自己的档案"
  ON public.profiles FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "用户可以更新自己的档案"
  ON public.profiles FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "任何人可以查看订阅计划"
  ON public.subscription_plans FOR SELECT
  USING (true);

CREATE POLICY "用户可以查看自己的订阅"
  ON public.subscriptions FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "用户可以查看自己的订阅事件"
  ON public.subscription_events FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "用户可以查看系统和自己的风格模板"
  ON public.style_templates FOR SELECT
  USING (is_system OR auth.uid() = user_id);

CREATE POLICY "用户可以创建自己的风格模板"
  ON public.style_templates FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "用户可以更新自己的风格模板"
  ON public.style_templates FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "用户可以删除自己的风格模板"
  ON public.style_templates FOR DELETE
  USING (auth.uid() = user_id AND NOT is_system);

CREATE POLICY "用户可以查看自己的会话"
  ON public.sessions FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "用户可以创建会话"
  ON public.sessions FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "用户可以更新自己的会话"
  ON public.sessions FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "用户可以删除自己的会话"
  ON public.sessions FOR DELETE
  USING (auth.uid() = user_id);

CREATE POLICY "任何人可以查看AI模型"
  ON public.ai_models FOR SELECT
  USING (true);

-- 创建视图用于查看活跃订阅的用户
CREATE OR REPLACE VIEW public.active_subscribers AS
SELECT 
  p.id,
  p.email,
  s.product_id,
  s.expires_at,
  s.store,
  sp.name as plan_name,
  sp.features
FROM 
  public.profiles p
JOIN 
  public.subscriptions s ON p.id = s.user_id
LEFT JOIN
  public.subscription_plans sp ON s.product_id = sp.plan_id
WHERE 
  p.is_vip = true AND 
  s.status = 'active';

-- 添加索引以优化查询性能
CREATE INDEX IF NOT EXISTS sessions_user_id_idx ON public.sessions(user_id);
CREATE INDEX IF NOT EXISTS sessions_template_id_idx ON public.sessions(template_id);
CREATE INDEX IF NOT EXISTS sessions_created_at_idx ON public.sessions(created_at);
CREATE INDEX IF NOT EXISTS sessions_is_favorite_idx ON public.sessions(is_favorite) WHERE is_favorite = true;
CREATE INDEX IF NOT EXISTS sessions_is_synced_idx ON public.sessions(is_synced) WHERE is_synced = false;

CREATE INDEX IF NOT EXISTS subscriptions_user_id_idx ON public.subscriptions(user_id);
CREATE INDEX IF NOT EXISTS subscriptions_status_idx ON public.subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_transaction_id ON public.subscriptions(transaction_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_original_transaction_id ON public.subscriptions(original_transaction_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_expires_at ON public.subscriptions(expires_at);

CREATE INDEX IF NOT EXISTS style_templates_user_id_idx ON public.style_templates(user_id);
CREATE INDEX IF NOT EXISTS style_templates_is_system_idx ON public.style_templates(is_system);
CREATE INDEX IF NOT EXISTS style_templates_is_active_idx ON public.style_templates(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS subscription_events_user_id_idx ON public.subscription_events(user_id);
CREATE INDEX IF NOT EXISTS subscription_events_event_type_idx ON public.subscription_events(event_type);

CREATE INDEX IF NOT EXISTS ai_models_is_active_idx ON public.ai_models(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS ai_models_is_vip_only_idx ON public.ai_models(is_vip_only);
CREATE INDEX IF NOT EXISTS ai_models_model_type_idx ON public.ai_models(model_type);
CREATE INDEX IF NOT EXISTS ai_models_api_key_type_idx ON public.ai_models(api_key_type); 