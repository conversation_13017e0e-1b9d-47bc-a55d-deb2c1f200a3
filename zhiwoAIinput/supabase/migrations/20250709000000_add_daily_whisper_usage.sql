-- 创建每日whisper使用记录表
-- 该表用于记录免费用户的whisper模型每日使用次数限制
-- 免费用户每日使用次数由system_configs表的daily_whisper_limit配置决定

CREATE TABLE IF NOT EXISTS public.daily_whisper_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  usage_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- 确保每个用户每天只有一条记录
  UNIQUE(user_id, date)
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_daily_whisper_usage_user_id ON public.daily_whisper_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_daily_whisper_usage_date ON public.daily_whisper_usage(date);
CREATE INDEX IF NOT EXISTS idx_daily_whisper_usage_user_date ON public.daily_whisper_usage(user_id, date);

-- 添加触发器自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_daily_whisper_usage_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_daily_whisper_usage_update_updated_at
    BEFORE UPDATE ON public.daily_whisper_usage
    FOR EACH ROW
    EXECUTE FUNCTION update_daily_whisper_usage_updated_at();

-- 创建函数：获取用户当日whisper使用次数
CREATE OR REPLACE FUNCTION public.get_user_daily_whisper_usage(
    p_user_id UUID,
    p_date DATE DEFAULT CURRENT_DATE
) RETURNS INTEGER AS $$
DECLARE
    v_usage_count INTEGER := 0;
BEGIN
    SELECT usage_count INTO v_usage_count
    FROM public.daily_whisper_usage
    WHERE user_id = p_user_id AND date = p_date;
    
    RETURN COALESCE(v_usage_count, 0);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建函数：增加用户当日whisper使用次数
CREATE OR REPLACE FUNCTION public.increment_user_daily_whisper_usage(
    p_user_id UUID,
    p_date DATE DEFAULT CURRENT_DATE
) RETURNS INTEGER AS $$
DECLARE
    v_new_count INTEGER;
BEGIN
    -- 使用UPSERT操作：如果记录不存在则创建，存在则增加计数
    INSERT INTO public.daily_whisper_usage (user_id, date, usage_count)
    VALUES (p_user_id, p_date, 1)
    ON CONFLICT (user_id, date)
    DO UPDATE SET 
        usage_count = daily_whisper_usage.usage_count + 1,
        updated_at = NOW()
    RETURNING usage_count INTO v_new_count;
    
    RETURN v_new_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建函数：检查用户是否超出每日whisper使用限制（修改为TABLE格式，使用system_configs）
CREATE OR REPLACE FUNCTION public.check_user_daily_whisper_limit(
    p_user_id UUID,
    p_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    "canUse" BOOLEAN,
    "isVip" BOOLEAN,
    "currentUsage" INTEGER,
    "dailyLimit" INTEGER,
    "remaining" INTEGER,
    "message" TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_is_vip BOOLEAN;
    v_current_usage INTEGER;
    v_daily_limit INTEGER;
    v_remaining INTEGER;
    v_can_use BOOLEAN;
    v_message TEXT;
BEGIN
    -- 获取配置中的每日限制
    SELECT (value->>'free_user_limit')::INTEGER
    INTO v_daily_limit
    FROM system_configs
    WHERE key = 'daily_whisper_limit';

    -- 如果没有找到配置，使用默认值
    IF v_daily_limit IS NULL THEN
        v_daily_limit := 5;
    END IF;

    -- 检查用户是否为VIP（使用profiles表的is_vip字段）
    SELECT p.is_vip INTO v_is_vip
    FROM profiles p
    WHERE p.id = p_user_id;

    -- 如果用户不存在，默认为非VIP
    IF v_is_vip IS NULL THEN
        v_is_vip := false;
    END IF;

    -- 获取当前使用次数
    SELECT COALESCE(usage_count, 0)
    INTO v_current_usage
    FROM daily_whisper_usage
    WHERE user_id = p_user_id AND date = p_date;

    -- 如果没有记录，设置为0
    IF v_current_usage IS NULL THEN
        v_current_usage := 0;
    END IF;

    -- 计算剩余使用次数
    IF v_is_vip THEN
        v_remaining := -1; -- -1 表示无限制
        v_can_use := true;
        v_message := 'VIP'; -- 状态码：VIP用户
    ELSE
        v_remaining := GREATEST(0, v_daily_limit - v_current_usage);
        v_can_use := v_current_usage < v_daily_limit;
        
        IF v_can_use THEN
            v_message := 'FREE_REMAINING'; -- 状态码：免费用户还有剩余次数
        ELSE
            v_message := 'FREE_LIMIT_REACHED'; -- 状态码：免费用户已达上限
        END IF;
    END IF;

    -- 返回结果时确保所有字段都有值
    RETURN QUERY
    SELECT 
        COALESCE(v_can_use, true) as "canUse",
        COALESCE(v_is_vip, false) as "isVip",
        COALESCE(v_current_usage, 0) as "currentUsage",
        COALESCE(v_daily_limit, 5) as "dailyLimit",
        COALESCE(v_remaining, v_daily_limit) as "remaining",
        COALESCE(v_message, '系统未配置使用限制') as "message";
END;
$$;

-- 启用RLS
ALTER TABLE public.daily_whisper_usage ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略：用户只能查看和修改自己的使用记录
CREATE POLICY "Users can view own whisper usage" ON public.daily_whisper_usage
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own whisper usage" ON public.daily_whisper_usage
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own whisper usage" ON public.daily_whisper_usage
    FOR UPDATE USING (auth.uid() = user_id);

-- 服务角色可以完全访问（用于统计和管理）
CREATE POLICY "Service role can access all whisper usage" ON public.daily_whisper_usage
    FOR ALL USING (auth.role() = 'service_role');

-- 添加表和字段注释
COMMENT ON TABLE public.daily_whisper_usage IS '记录用户每日whisper模型使用次数，用于免费用户限制';
COMMENT ON COLUMN public.daily_whisper_usage.user_id IS '用户ID';
COMMENT ON COLUMN public.daily_whisper_usage.date IS '使用日期';
COMMENT ON COLUMN public.daily_whisper_usage.usage_count IS '当日使用次数';
COMMENT ON COLUMN public.daily_whisper_usage.created_at IS '记录创建时间';
COMMENT ON COLUMN public.daily_whisper_usage.updated_at IS '记录更新时间';

COMMENT ON FUNCTION public.get_user_daily_whisper_usage IS '获取用户指定日期的whisper使用次数';
COMMENT ON FUNCTION public.increment_user_daily_whisper_usage IS '增加用户当日whisper使用次数';
COMMENT ON FUNCTION public.check_user_daily_whisper_limit IS '检查用户是否超出每日whisper使用限制';

-- 创建清理过期记录的函数（保留30天的记录）
CREATE OR REPLACE FUNCTION public.cleanup_old_whisper_usage()
RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    DELETE FROM public.daily_whisper_usage
    WHERE date < CURRENT_DATE - INTERVAL '30 days'
    AND created_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    
    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.cleanup_old_whisper_usage IS '清理30天前的whisper使用记录';

-- 创建视图用于统计分析
CREATE OR REPLACE VIEW public.whisper_usage_stats AS
WITH whisper_limit AS (
    SELECT (value->>'free_user_limit')::INTEGER as limit_value
    FROM public.system_configs
    WHERE key = 'daily_whisper_limit'
)
SELECT 
    date,
    COUNT(DISTINCT user_id) as active_users,
    SUM(usage_count) as total_usage,
    AVG(usage_count) as avg_usage_per_user,
    COUNT(CASE WHEN usage_count >= COALESCE((SELECT limit_value FROM whisper_limit), 5) THEN 1 END) as users_hit_limit,
    ROUND(
        COUNT(CASE WHEN usage_count >= COALESCE((SELECT limit_value FROM whisper_limit), 5) THEN 1 END)::NUMERIC / 
        COUNT(DISTINCT user_id)::NUMERIC * 100, 2
    ) as limit_hit_percentage
FROM public.daily_whisper_usage
WHERE date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY date
ORDER BY date DESC;

COMMENT ON VIEW public.whisper_usage_stats IS 'whisper使用统计视图，用于分析用户使用模式，使用system_configs中配置的使用限制';

-- 添加初始配置
INSERT INTO public.system_configs (key, value, description, created_at, updated_at)
VALUES (
    'daily_whisper_limit',
    jsonb_build_object(
        'free_user_limit', 5,
        'description', '免费用户每日Whisper使用次数限制'
    ),
    '配置免费用户每日可使用Whisper的次数限制',
    now(),
    now()
) ON CONFLICT (key) DO UPDATE
SET 
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    updated_at = now();

-- 确保system_configs表存在
CREATE TABLE IF NOT EXISTS public.system_configs (
    key TEXT PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 添加RLS策略
ALTER TABLE public.system_configs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow public read access to system_configs" ON public.system_configs
    FOR SELECT
    USING (true);

CREATE POLICY "Only service_role can modify system_configs" ON public.system_configs
    FOR ALL
    USING (auth.role() = 'service_role');

-- 添加注释
COMMENT ON TABLE public.system_configs IS '系统配置表，用于存储各种系统级配置';
COMMENT ON COLUMN public.system_configs.key IS '配置键名';
COMMENT ON COLUMN public.system_configs.value IS '配置值（JSONB格式）';
COMMENT ON COLUMN public.system_configs.description IS '配置说明';

-- 创建更新触发器
CREATE OR REPLACE FUNCTION update_system_configs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_system_configs_timestamp
    BEFORE UPDATE ON public.system_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_system_configs_updated_at(); 