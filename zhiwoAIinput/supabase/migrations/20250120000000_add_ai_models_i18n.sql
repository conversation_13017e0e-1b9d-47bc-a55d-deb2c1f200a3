-- 迁移文件: 20250120000000_add_ai_models_i18n.sql
-- 描述: 为AI模型表添加多语言支持

-- 1. 添加新的多语言字段
ALTER TABLE public.ai_models 
  ADD COLUMN name_key TEXT,
  ADD COLUMN description_key TEXT;

-- 2. 更新现有数据，将中文name和description转换为多语言key
UPDATE public.ai_models SET 
  name_key = 'aiModels.' || model_id || '.name',
  description_key = 'aiModels.' || model_id || '.description'
WHERE name_key IS NULL OR description_key IS NULL;

-- 3. 为新字段添加索引以优化查询
CREATE INDEX IF NOT EXISTS ai_models_name_key_idx ON public.ai_models(name_key);
CREATE INDEX IF NOT EXISTS ai_models_description_key_idx ON public.ai_models(description_key);

-- 4. 添加注释说明
COMMENT ON COLUMN public.ai_models.name_key IS 'AI模型名称的多语言key，用于i18n翻译';
COMMENT ON COLUMN public.ai_models.description_key IS 'AI模型描述的多语言key，用于i18n翻译';

-- 5. 保留原有的name和description字段作为fallback，但更新注释
COMMENT ON COLUMN public.ai_models.name IS 'AI模型名称（已废弃，使用name_key进行多语言支持）';
COMMENT ON COLUMN public.ai_models.description IS 'AI模型描述（已废弃，使用description_key进行多语言支持）'; 