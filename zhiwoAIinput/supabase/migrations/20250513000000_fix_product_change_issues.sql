-- 修复产品变更相关问题
-- 1. 添加缺失的payment_provider_subscription_id字段
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS payment_provider_subscription_id TEXT;
CREATE INDEX IF NOT EXISTS idx_subscriptions_payment_provider_subscription_id ON public.subscriptions(payment_provider_subscription_id) WHERE payment_provider_subscription_id IS NOT NULL;

COMMENT ON COLUMN public.subscriptions.payment_provider_subscription_id IS '支付提供商订阅ID，用于查找特定支付提供商的订阅';

-- 2. 创建检查和修复VIP状态的函数
CREATE OR REPLACE FUNCTION public.fix_user_vip_status(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_subscription RECORD;
  v_latest_expires_at TIMESTAMPTZ;
  v_now TIMESTAMPTZ := now();
  v_fixed BOOLEAN := false;
BEGIN
  -- 查找用户最新的活跃订阅
  SELECT * INTO v_subscription 
  FROM subscriptions 
  WHERE user_id = p_user_id AND status = 'active'
  ORDER BY created_at DESC 
  LIMIT 1;
  
  -- 如果找到活跃订阅
  IF FOUND THEN
    v_latest_expires_at := v_subscription.expires_at;
    
    -- 检查VIP状态与订阅是否一致
    IF (
      SELECT 
        (profiles.is_vip = false OR 
         profiles.vip_expires_at IS NULL OR 
         profiles.vip_expires_at < v_latest_expires_at)
      FROM 
        profiles
      WHERE 
        id = p_user_id
    ) THEN
      -- 更新用户VIP状态
      UPDATE profiles
      SET 
        is_vip = true,
        vip_product_id = v_subscription.product_id,
        vip_expires_at = v_latest_expires_at,
        vip_environment = v_subscription.environment,
        vip_updated_at = v_now,
        updated_at = v_now
      WHERE id = p_user_id;
      
      v_fixed := true;
    END IF;
  ELSE
    -- 检查用户没有活跃订阅但标记为VIP的情况
    IF (
      SELECT is_vip 
      FROM profiles 
      WHERE id = p_user_id
    ) THEN
      -- 更新用户为非VIP
      UPDATE profiles
      SET 
        is_vip = false,
        vip_updated_at = v_now,
        updated_at = v_now
      WHERE id = p_user_id;
      
      v_fixed := true;
    END IF;
  END IF;
  
  RETURN v_fixed;
END;
$$;

-- 3. 创建触发器，当订阅状态更改时自动更新VIP状态
CREATE OR REPLACE FUNCTION public.update_vip_on_subscription_change()
RETURNS TRIGGER AS $$
BEGIN
  -- 仅当状态变更为active或从active变更时
  IF NEW.status = 'active' OR OLD.status = 'active' THEN
    PERFORM fix_user_vip_status(NEW.user_id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 如果触发器不存在则创建
DROP TRIGGER IF EXISTS tr_update_vip_on_subscription_change ON subscriptions;

CREATE TRIGGER tr_update_vip_on_subscription_change
AFTER INSERT OR UPDATE OF status ON subscriptions
FOR EACH ROW
EXECUTE FUNCTION update_vip_on_subscription_change();

-- 4. 优化处理产品变更事件的函数
CREATE OR REPLACE FUNCTION public.handle_product_change(
  p_user_id TEXT,
  p_product_id TEXT,
  p_expires_at TIMESTAMPTZ,
  p_previous_product_id TEXT,
  p_original_transaction_id TEXT,
  p_transaction_id TEXT,
  p_environment TEXT,
  p_store TEXT,
  p_event_data JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_now TIMESTAMPTZ := now();
  v_subscription_id UUID;
  v_result JSONB;
  v_user_uuid UUID;
BEGIN
  -- 验证用户ID是否为UUID
  BEGIN
    v_user_uuid := p_user_id::UUID;
  EXCEPTION
    WHEN OTHERS THEN
      -- 尝试从user_identifiers表中查找
      SELECT user_id::UUID INTO v_user_uuid
      FROM user_identifiers
      WHERE anonymous_id = p_user_id
      LIMIT 1;
      
      IF v_user_uuid IS NULL THEN
        RAISE EXCEPTION '无效的用户ID: %', p_user_id;
      END IF;
  END;
  
  -- 查找要更新的现有订阅
  SELECT id INTO v_subscription_id
  FROM subscriptions
  WHERE 
    user_id = v_user_uuid AND 
    (
      original_transaction_id = p_original_transaction_id OR
      transaction_id = p_transaction_id OR
      (status = 'active' AND product_id = p_previous_product_id)
    )
  ORDER BY 
    CASE 
      WHEN original_transaction_id = p_original_transaction_id THEN 1
      WHEN transaction_id = p_transaction_id THEN 2
      ELSE 3
    END,
    created_at DESC
  LIMIT 1;
  
  -- 如果找到订阅，更新状态
  IF v_subscription_id IS NOT NULL THEN
    UPDATE subscriptions
    SET 
      previous_product_id = product_id,
      product_id = p_product_id,
      expires_at = p_expires_at,
      transaction_id = p_transaction_id,
      product_changed_at = v_now,
      updated_at = v_now,
      event_type = 'PRODUCT_CHANGE',
      last_event_at = v_now
    WHERE id = v_subscription_id;
    
    -- 更新用户VIP状态
    PERFORM fix_user_vip_status(v_user_uuid);
    
    v_result := jsonb_build_object(
      'success', true,
      'subscription_id', v_subscription_id,
      'action', 'updated',
      'product_id', p_product_id,
      'previous_product_id', p_previous_product_id
    );
  ELSE
    -- 如果没找到订阅，创建新的
    INSERT INTO subscriptions (
      user_id,
      product_id,
      previous_product_id,
      status,
      environment,
      store,
      transaction_id,
      original_transaction_id,
      expires_at,
      purchase_date,
      event_type,
      product_changed_at,
      payment_provider_subscription_id
    ) VALUES (
      v_user_uuid,
      p_product_id,
      p_previous_product_id,
      'active',
      p_environment,
      p_store,
      p_transaction_id,
      p_original_transaction_id,
      p_expires_at,
      v_now,
      'PRODUCT_CHANGE',
      v_now,
      p_original_transaction_id
    )
    RETURNING id INTO v_subscription_id;
    
    -- 更新用户VIP状态
    PERFORM fix_user_vip_status(v_user_uuid);
    
    v_result := jsonb_build_object(
      'success', true,
      'subscription_id', v_subscription_id,
      'action', 'created',
      'product_id', p_product_id,
      'previous_product_id', p_previous_product_id
    );
  END IF;
  
  RETURN v_result;
END;
$$;

-- 5. 修复现有数据中的VIP状态
DO $$
DECLARE
  v_user_id UUID;
  v_fixed BOOLEAN;
BEGIN
  -- 查找所有有活跃订阅的用户
  FOR v_user_id IN (
    SELECT DISTINCT user_id 
    FROM subscriptions 
    WHERE status = 'active'
  )
  LOOP
    SELECT fix_user_vip_status(v_user_id) INTO v_fixed;
    
    IF v_fixed THEN
      RAISE NOTICE '已修复用户 % 的VIP状态', v_user_id;
    END IF;
  END LOOP;
  
  -- 查找所有标记为VIP但没有活跃订阅的用户
  FOR v_user_id IN (
    SELECT p.id
    FROM profiles p
    LEFT JOIN (
      SELECT user_id
      FROM subscriptions
      WHERE status = 'active'
    ) s ON p.id = s.user_id
    WHERE p.is_vip = true AND s.user_id IS NULL
  )
  LOOP
    UPDATE profiles
    SET 
      is_vip = false,
      vip_updated_at = now(),
      updated_at = now()
    WHERE id = v_user_id;
    
    RAISE NOTICE '已将无活跃订阅的用户 % 的VIP状态设为false', v_user_id;
  END LOOP;
END
$$;

-- 更新缓存
NOTIFY pgrst, 'reload schema'; 