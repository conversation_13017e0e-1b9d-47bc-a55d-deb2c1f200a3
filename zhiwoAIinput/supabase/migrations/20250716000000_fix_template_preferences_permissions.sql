-- 修复user_template_preferences表的权限问题
-- 确保authenticated和anon用户可以正确访问和修改自己的模板偏好设置

-- 1. 确保authenticated角色有user_template_preferences表的基本权限
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.user_template_preferences TO authenticated;

-- 2. 启用RLS（如果未启用）
ALTER TABLE public.user_template_preferences ENABLE ROW LEVEL SECURITY;

-- 3. 创建RLS策略
-- 允许authenticated用户查看自己的模板偏好
DROP POLICY IF EXISTS "Users can view own template preferences" ON public.user_template_preferences;
CREATE POLICY "Users can view own template preferences" ON public.user_template_preferences
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

-- 允许authenticated用户更新自己的模板偏好
DROP POLICY IF EXISTS "Users can update own template preferences" ON public.user_template_preferences;
CREATE POLICY "Users can update own template preferences" ON public.user_template_preferences
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- 允许authenticated用户插入自己的模板偏好
DROP POLICY IF EXISTS "Users can insert own template preferences" ON public.user_template_preferences;
CREATE POLICY "Users can insert own template preferences" ON public.user_template_preferences
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- 允许authenticated用户删除自己的模板偏好
DROP POLICY IF EXISTS "Users can delete own template preferences" ON public.user_template_preferences;
CREATE POLICY "Users can delete own template preferences" ON public.user_template_preferences
    FOR DELETE TO authenticated
    USING (auth.uid() = user_id);

-- 4. 允许anon角色查看user_template_preferences（因为未登录用户也需要看到默认模板）
GRANT SELECT ON TABLE public.user_template_preferences TO anon;

-- 5. anon角色的RLS策略
DROP POLICY IF EXISTS "Anyone can view template preferences" ON public.user_template_preferences;
CREATE POLICY "Anyone can view template preferences" ON public.user_template_preferences
    FOR SELECT TO anon
    USING (template_type = 'system');  -- 匿名用户只能查看系统模板的偏好设置 