-- 创建语音提示词表
CREATE TABLE IF NOT EXISTS speech_prompts (
  id TEXT PRIMARY KEY,
  prompt_type TEXT NOT NULL, -- 'chinese' 或 'multilingual'
  model_type TEXT NOT NULL, -- 'gpt-4o-mini-transcribe' 或 'whisper-1'
  prompt_content TEXT NOT NULL,
  version INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_speech_prompts_type_model ON speech_prompts(prompt_type, model_type);
CREATE INDEX IF NOT EXISTS idx_speech_prompts_version ON speech_prompts(version);

-- 插入默认的语音提示词
INSERT INTO speech_prompts (id, prompt_type, model_type, prompt_content, version) VALUES
(
  'chinese_gpt4o_mini_transcribe',
  'chinese',
  'gpt-4o-mini-transcribe',
  '请准确转录语音内容，保持原始语言，添加标点符号。',
  2
),
(
  'chinese_whisper',
  'chinese',
  'whisper-1',
  '请准确转录语音内容，保持原始语言，添加标点符号。',
  2
),
(
  'multilingual_common',
  'multilingual',
  'common',
  'Please transcribe accurately, keep original language, add punctuation.',
  2
);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_speech_prompts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_speech_prompts_updated_at
  BEFORE UPDATE ON speech_prompts
  FOR EACH ROW
  EXECUTE FUNCTION update_speech_prompts_updated_at(); 