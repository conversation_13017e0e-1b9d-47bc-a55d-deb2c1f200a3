-- 20241001000000_add_trial_cancellation_fields.sql
-- 添加试用期取消相关字段

-- 向subscriptions表添加试用期取消相关字段
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS trial_cancelled BOOLEAN DEFAULT false;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS trial_cancelled_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS trial_cancellation_reason TEXT;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS trial_cancellation_reason_description TEXT;

-- 添加字段注释
COMMENT ON COLUMN public.subscriptions.trial_cancelled IS '试用期是否已取消';
COMMENT ON COLUMN public.subscriptions.trial_cancelled_at IS '试用期取消时间';
COMMENT ON COLUMN public.subscriptions.trial_cancellation_reason IS '试用期取消原因';
COMMENT ON COLUMN public.subscriptions.trial_cancellation_reason_description IS '试用期取消原因描述';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_subscriptions_trial_cancelled ON public.subscriptions(trial_cancelled) 
  WHERE trial_cancelled = true;

-- 为订阅状态添加'trial_cancelled'值
DO $$
BEGIN
    -- 检查是否subscription_status类型存在
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'subscription_status') THEN
        -- 检查是否已经有'trial_cancelled'值
        IF NOT EXISTS (
            SELECT 1 
            FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'subscription_status')
            AND enumlabel = 'trial_cancelled'
        ) THEN
            -- 添加'trial_cancelled'值到枚举类型
            ALTER TYPE public.subscription_status ADD VALUE 'trial_cancelled';
        END IF;
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        NULL;
END$$;

-- 创建试用期取消分析视图
CREATE OR REPLACE VIEW public.trial_cancellation_analysis AS
SELECT 
    s.user_id,
    s.id AS subscription_id,
    s.product_id,
    s.trial_start_at,
    s.trial_end_at,
    s.trial_cancelled_at,
    s.trial_cancellation_reason,
    s.trial_cancellation_reason_description,
    -- 计算试用期持续时间(天)
    CASE 
        WHEN s.trial_cancelled_at IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (s.trial_cancelled_at - s.trial_start_at)) / 86400 
        ELSE
            EXTRACT(EPOCH FROM (s.trial_end_at - s.trial_start_at)) / 86400
    END AS trial_duration_days,
    -- 计算试用期使用比例(0-100%)
    CASE 
        WHEN s.trial_cancelled_at IS NOT NULL AND s.trial_start_at IS NOT NULL AND s.trial_end_at IS NOT NULL THEN 
            ROUND((EXTRACT(EPOCH FROM (s.trial_cancelled_at - s.trial_start_at)) / 
                  EXTRACT(EPOCH FROM (s.trial_end_at - s.trial_start_at))) * 100)
        ELSE NULL
    END AS trial_usage_percentage,
    -- 判断是取消前转化还是直接取消
    CASE
        WHEN s.trial_converted = true AND s.trial_converted_at < s.trial_cancelled_at THEN 'CONVERTED_THEN_CANCELLED'
        WHEN s.trial_cancelled = true THEN 'CANCELLED_WITHOUT_CONVERSION'
        ELSE 'UNKNOWN'
    END AS cancellation_pattern
FROM 
    public.subscriptions s
WHERE 
    s.is_trial = true 
    AND s.trial_cancelled = true; 