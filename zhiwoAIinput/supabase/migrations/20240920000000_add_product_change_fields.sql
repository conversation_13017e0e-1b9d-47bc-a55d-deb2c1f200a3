-- 20240920000000_add_product_change_fields.sql
-- 添加产品变更相关字段

-- 向subscriptions表添加新字段
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS previous_product_id TEXT;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS product_changed_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS product_change_from TEXT;
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS upgrade_from_subscription_id UUID REFERENCES public.subscriptions(id);

-- 添加字段注释
COMMENT ON COLUMN public.subscriptions.previous_product_id IS '变更前的产品ID';
COMMENT ON COLUMN public.subscriptions.product_changed_at IS '产品变更时间';
COMMENT ON COLUMN public.subscriptions.product_change_from IS '变更来源(升级/降级)';
COMMENT ON COLUMN public.subscriptions.upgrade_from_subscription_id IS '升级前的订阅ID';

-- 创建产品变更类型枚举
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'product_change_type') THEN
        CREATE TYPE public.product_change_type AS ENUM ('upgrade', 'downgrade', 'crossgrade');
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        NULL;
END$$;

-- 添加产品变更类型字段
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS product_change_type public.product_change_type;
COMMENT ON COLUMN public.subscriptions.product_change_type IS '产品变更类型(升级/降级/平级变更)';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_subscriptions_previous_product_id ON public.subscriptions(previous_product_id) 
  WHERE previous_product_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_subscriptions_upgrade_from_subscription_id ON public.subscriptions(upgrade_from_subscription_id) 
  WHERE upgrade_from_subscription_id IS NOT NULL;

-- 更新函数，支持产品变更
CREATE OR REPLACE FUNCTION public.record_product_change(
  p_user_id UUID,
  p_old_subscription_id UUID,
  p_new_product_id TEXT,
  p_previous_product_id TEXT,
  p_change_type public.product_change_type,
  p_details JSONB
) RETURNS UUID AS $$
DECLARE
  v_new_subscription_id UUID;
  v_event_id UUID;
BEGIN
  -- 记录旧订阅为已变更
  UPDATE public.subscriptions
  SET 
    status = 'changed',
    updated_at = now()
  WHERE id = p_old_subscription_id;
  
  -- 创建新的订阅记录
  INSERT INTO public.subscriptions (
    user_id,
    product_id,
    status,
    previous_product_id,
    product_changed_at,
    product_change_type,
    upgrade_from_subscription_id,
    period_type,
    store,
    environment,
    entitlements,
    transaction_id,
    original_transaction_id,
    expires_at,
    purchase_date,
    event_type
  ) VALUES (
    p_user_id,
    p_new_product_id,
    'active',
    p_previous_product_id,
    now(),
    p_change_type,
    p_old_subscription_id,
    p_details->>'period_type',
    p_details->>'store',
    p_details->>'environment',
    CASE WHEN p_details->'entitlement_ids' IS NOT NULL THEN 
      jsonb_build_array(p_details->'entitlement_ids') 
    ELSE 
      '[]'::jsonb 
    END,
    p_details->>'transaction_id',
    p_details->>'original_transaction_id',
    CASE WHEN (p_details->>'expiration_at_ms') IS NOT NULL THEN 
      to_timestamp((p_details->>'expiration_at_ms')::bigint / 1000) 
    ELSE 
      NULL 
    END,
    now(),
    'PRODUCT_CHANGE'
  ) RETURNING id INTO v_new_subscription_id;
  
  -- 记录产品变更事件
  INSERT INTO public.subscription_events (
    user_id,
    subscription_id,
    event_type,
    event_timestamp,
    transaction_id,
    original_transaction_id,
    product_id,
    event_data
  ) VALUES (
    p_user_id,
    v_new_subscription_id,
    'PRODUCT_CHANGE',
    now(),
    p_details->>'transaction_id',
    p_details->>'original_transaction_id',
    p_new_product_id,
    p_details
  ) RETURNING id INTO v_event_id;
  
  RETURN v_new_subscription_id;
END;
$$ LANGUAGE plpgsql; 