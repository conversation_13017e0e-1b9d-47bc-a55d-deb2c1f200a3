-- 20241005000002_add_invoice_update_functions.sql
-- 添加更新发票状态的函数

-- 创建更新发票状态为已支付的函数
CREATE OR REPLACE FUNCTION public.update_invoice_to_paid(
  p_invoice_id TEXT,
  p_subscription_id UUID,
  p_payment_date TIMESTAMP WITH TIME ZONE DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  v_paid_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- 使用提供的支付日期或当前时间
  v_paid_at := COALESCE(p_payment_date, now());
  
  -- 更新发票状态为已支付
  UPDATE public.invoices
  SET 
    status = 'paid',
    paid_at = v_paid_at,
    subscription_id = p_subscription_id,
    updated_at = now()
  WHERE 
    invoice_id = p_invoice_id
    AND status = 'issued';
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- 创建更新发票状态为已取消的函数
CREATE OR REPLACE FUNCTION public.update_invoice_to_cancelled(
  p_invoice_id TEXT,
  p_reason TEXT DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
  -- 更新发票状态为已取消
  UPDATE public.invoices
  SET 
    status = 'cancelled',
    metadata = jsonb_set(
      COALESCE(metadata, '{}'::jsonb),
      '{cancellation_reason}',
      to_jsonb(p_reason)
    ),
    updated_at = now()
  WHERE 
    invoice_id = p_invoice_id
    AND status = 'issued';
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- 创建更新发票状态为支付失败的函数
CREATE OR REPLACE FUNCTION public.update_invoice_to_failed(
  p_invoice_id TEXT,
  p_error_details TEXT DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
  -- 更新发票状态为支付失败
  UPDATE public.invoices
  SET 
    status = 'failed',
    metadata = jsonb_set(
      COALESCE(metadata, '{}'::jsonb),
      '{error_details}',
      to_jsonb(p_error_details)
    ),
    updated_at = now()
  WHERE 
    invoice_id = p_invoice_id
    AND status = 'issued';
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- 创建过期未支付发票的函数
CREATE OR REPLACE FUNCTION public.expire_old_invoices(
  p_hours INTEGER DEFAULT 24
) RETURNS INTEGER AS $$
DECLARE
  v_count INTEGER;
BEGIN
  -- 标记过期的发票
  WITH expired_invoices AS (
    UPDATE public.invoices
    SET 
      status = 'cancelled',
      metadata = jsonb_set(
        COALESCE(metadata, '{}'::jsonb),
        '{expiration_reason}',
        '"TIMEOUT"'
      ),
      updated_at = now()
    WHERE 
      status = 'issued'
      AND issued_at < (now() - (p_hours || ' hours')::interval)
    RETURNING id
  )
  SELECT COUNT(*) INTO v_count FROM expired_invoices;
  
  RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- 创建一个定时任务来自动清理旧的未支付发票
-- 注意: 这需要pg_cron扩展，如果没有该扩展，可以跳过此步骤
DO $$
DECLARE
  cron_exists BOOLEAN;
BEGIN
  -- 检查pg_cron扩展是否存在
  SELECT EXISTS (
    SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
  ) INTO cron_exists;
  
  IF cron_exists THEN
    -- 创建定时任务，每天凌晨2点执行一次
    PERFORM cron.schedule(
      'expire-old-invoices',
      '0 2 * * *',
      'SELECT public.expire_old_invoices(24)'
    );
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    -- 忽略错误，可能是pg_cron扩展不存在
    RAISE NOTICE 'pg_cron扩展不存在，跳过定时任务创建';
END
$$; 