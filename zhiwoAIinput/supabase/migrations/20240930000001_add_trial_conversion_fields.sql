-- 20240930000001_add_trial_conversion_fields.sql
-- 添加试用转付费相关字段

-- 向subscriptions表添加字段支持试用转付费
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS converted_from_trial_id UUID REFERENCES public.subscriptions(id);
ALTER TABLE public.subscriptions ADD COLUMN IF NOT EXISTS conversion_date TIMESTAMP WITH TIME ZONE;

-- 添加字段注释
COMMENT ON COLUMN public.subscriptions.converted_from_trial_id IS '从哪个试用订阅转换而来';
COMMENT ON COLUMN public.subscriptions.conversion_date IS '转换为付费的日期';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_subscriptions_converted_from_trial_id ON public.subscriptions(converted_from_trial_id) 
  WHERE converted_from_trial_id IS NOT NULL;

-- 为订阅状态字段添加'converted'值
DO $$
BEGIN
    -- 检查是否subscription_status类型存在
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'subscription_status') THEN
        -- 检查是否已经有'converted'值
        IF NOT EXISTS (
            SELECT 1 
            FROM pg_enum 
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'subscription_status')
            AND enumlabel = 'converted'
        ) THEN
            -- 添加'converted'值到枚举类型
            ALTER TYPE public.subscription_status ADD VALUE 'converted';
        END IF;
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        NULL;
END$$;

-- 创建函数用于处理试用转付费
CREATE OR REPLACE FUNCTION public.convert_trial_to_paid(
  p_trial_subscription_id UUID,
  p_new_expiration_date TIMESTAMP WITH TIME ZONE,
  p_details JSONB DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  v_user_id UUID;
  v_product_id TEXT;
  v_new_subscription_id UUID;
  v_conversion_date TIMESTAMP WITH TIME ZONE := now();
BEGIN
  -- 获取试用订阅的信息
  SELECT 
    user_id, 
    product_id 
  INTO 
    v_user_id, 
    v_product_id
  FROM 
    public.subscriptions
  WHERE 
    id = p_trial_subscription_id;
  
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION '找不到试用订阅记录: %', p_trial_subscription_id;
  END IF;
  
  -- 更新试用订阅为已转换状态
  UPDATE public.subscriptions
  SET 
    status = 'converted',
    trial_converted = true,
    trial_converted_at = v_conversion_date,
    updated_at = now()
  WHERE id = p_trial_subscription_id;
  
  -- 创建新的付费订阅记录
  INSERT INTO public.subscriptions (
    user_id,
    product_id,
    status,
    is_trial,
    is_trial_conversion,
    converted_from_trial_id,
    conversion_date,
    expires_at,
    environment,
    purchase_date,
    event_type,
    created_at,
    updated_at
  ) VALUES (
    v_user_id,
    v_product_id,
    'active',
    false,
    true,
    p_trial_subscription_id,
    v_conversion_date,
    p_new_expiration_date,
    (SELECT environment FROM public.subscriptions WHERE id = p_trial_subscription_id),
    v_conversion_date,
    'TRIAL_CONVERSION',
    now(),
    now()
  ) RETURNING id INTO v_new_subscription_id;
  
  -- 记录试用转付费事件
  INSERT INTO public.subscription_events (
    user_id,
    subscription_id,
    event_type,
    event_timestamp,
    product_id,
    event_data
  ) VALUES (
    v_user_id,
    v_new_subscription_id,
    'TRIAL_CONVERSION',
    now(),
    v_product_id,
    jsonb_build_object(
      'trial_subscription_id', p_trial_subscription_id,
      'conversion_date', v_conversion_date,
      'new_expiration_date', p_new_expiration_date,
      'details', p_details
    )
  );
  
  -- 更新用户VIP状态
  UPDATE public.profiles
  SET 
    is_vip = true,
    vip_is_trial = false,
    vip_expires_at = p_new_expiration_date,
    vip_updated_at = now(),
    updated_at = now()
  WHERE id = v_user_id;
  
  RETURN v_new_subscription_id;
END;
$$ LANGUAGE plpgsql;

-- 创建视图，用于展示试用转付费分析
CREATE OR REPLACE VIEW public.trial_conversion_analysis AS
SELECT 
  s1.user_id,
  s1.id AS trial_subscription_id,
  s1.trial_duration_days,
  s1.trial_start_at,
  s1.trial_end_at,
  s1.trial_converted,
  s1.trial_converted_at,
  s2.id AS paid_subscription_id,
  s2.conversion_date,
  s2.product_id,
  -- 计算转化率相关字段
  CASE 
    WHEN s1.trial_converted THEN 1 
    ELSE 0 
  END AS is_converted,
  -- 计算转化时间(天)
  CASE 
    WHEN s1.trial_converted THEN 
      EXTRACT(EPOCH FROM (s1.trial_converted_at - s1.trial_start_at)) / 86400 
    ELSE NULL 
  END AS days_to_conversion,
  -- 计算试用期使用比例(0-100%)
  CASE 
    WHEN s1.trial_converted AND s1.trial_duration_days > 0 THEN 
      ROUND((EXTRACT(EPOCH FROM (s1.trial_converted_at - s1.trial_start_at)) / 
            EXTRACT(EPOCH FROM (s1.trial_end_at - s1.trial_start_at))) * 100)
    ELSE NULL
  END AS trial_usage_percentage
FROM 
  public.subscriptions s1
LEFT JOIN 
  public.subscriptions s2 ON s1.id = s2.converted_from_trial_id
WHERE 
  s1.is_trial = true; 