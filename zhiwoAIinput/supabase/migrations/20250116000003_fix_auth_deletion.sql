-- 修复软删除函数 - 真正删除auth.users记录，允许相同邮箱重新注册
-- 在public表中保留软删除记录用于数据分析

-- 删除现有函数
DROP FUNCTION IF EXISTS soft_delete_user(UUID);

-- 重新创建软删除函数，删除auth.users记录
CREATE OR REPLACE FUNCTION soft_delete_user(target_user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- 1. 软删除profiles表记录（保持原邮箱，用于数据分析）
    UPDATE profiles SET 
        is_deleted_by_user = true,
        deleted_at = NOW(),
        updated_at = NOW()
    WHERE id = target_user_id;
    
    -- 2. 软删除订阅记录
    UPDATE subscriptions SET 
        is_deleted_by_user = true,
        deleted_at = NOW()
    WHERE user_id = target_user_id;
    
    -- 3. 软删除订阅事件记录
    UPDATE subscription_events SET 
        is_deleted_by_user = true,
        deleted_at = NOW()
    WHERE user_id = target_user_id;
    
    -- 4. 修改user_identifiers记录避免冲突
    UPDATE public.user_identifiers SET 
        user_id = 'deleted_' || extract(epoch from now())::bigint || '_' || user_id,
        anonymous_id = 'deleted_' || extract(epoch from now())::bigint || '_' || anonymous_id,
        updated_at = NOW()
    WHERE user_id = target_user_id::TEXT;
    
    -- 5. 删除用户的实时数据
    DELETE FROM public.user_settings WHERE user_id = target_user_id;
    DELETE FROM public.style_templates WHERE user_id = target_user_id AND is_system = false;
    DELETE FROM public.sessions WHERE user_id = target_user_id;
    
    -- 6. 记录操作日志
    INSERT INTO public.user_deletion_logs (user_id, deleted_at, deletion_type)
    VALUES (target_user_id, NOW(), 'user_requested')
    ON CONFLICT DO NOTHING;
    
    -- 7. 真正删除auth.users记录（这样用户无法再登录，相同邮箱可以重新注册）
    DELETE FROM auth.users WHERE id = target_user_id;
    
EXCEPTION
    WHEN OTHERS THEN
        -- 记录错误但不回滚，确保至少profiles表被标记为删除
        INSERT INTO public.error_logs (user_id, error_message, occurred_at)
        VALUES (target_user_id, SQLERRM, NOW())
        ON CONFLICT DO NOTHING;
        RAISE;
END;
$$;

-- 为函数设置权限
GRANT EXECUTE ON FUNCTION soft_delete_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION soft_delete_user(UUID) TO anon;

-- 添加函数注释
COMMENT ON FUNCTION soft_delete_user IS '软删除用户数据但真正删除认证记录，允许相同邮箱重新注册';

-- 更新用户注册触发器，确保已删除用户不会干扰新用户注册
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- 检查是否存在已删除的用户记录，如果有则跳过（避免冲突）
  IF EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE email = NEW.email 
    AND is_deleted_by_user = true
  ) THEN
    -- 如果存在已删除的同邮箱用户，为新用户创建全新的记录
    INSERT INTO public.profiles (
      id, 
      email, 
      username,
      avatar_url,
      is_vip,
      custom_templates_count,
      voice_minutes_used,
      voice_minutes_limit,
      api_key_type,
      created_at,
      updated_at
    ) VALUES (
      NEW.id,
      NEW.email,
      NEW.email,
      coalesce(NEW.raw_user_meta_data->>'avatar_url', ''),
      false,
      0,
      0, 
      0,
      'free',
      now(),
      now()
    );
  ELSE
    -- 正常插入用户记录
    INSERT INTO public.profiles (
      id, 
      email, 
      username,
      avatar_url,
      is_vip,
      custom_templates_count,
      voice_minutes_used,
      voice_minutes_limit,
      api_key_type,
      created_at,
      updated_at
    ) VALUES (
      NEW.id,
      NEW.email,
      NEW.email,
      coalesce(NEW.raw_user_meta_data->>'avatar_url', ''),
      false,
      0,
      0, 
      0,
      'free',
      now(),
      now()
    )
    ON CONFLICT (id) DO NOTHING;
  END IF;
  
  -- 创建用户设置记录
  INSERT INTO public.user_settings (
    user_id,
    language,
    theme,
    default_text_model,
    default_voice_model,
    sync_enabled,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    'zh-CN',
    'light',
    'qwen2.5-7b',
    'native',
    true,
    now(),
    now()
  )
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 