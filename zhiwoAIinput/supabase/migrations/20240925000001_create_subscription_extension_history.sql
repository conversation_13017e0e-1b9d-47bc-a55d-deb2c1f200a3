-- 20240925000001_create_subscription_extension_history.sql
-- 创建订阅延长历史表

-- 创建表记录订阅延长历史
CREATE TABLE IF NOT EXISTS public.subscription_extension_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription_id UUID NOT NULL REFERENCES public.subscriptions(id) ON DELETE CASCADE,
  original_expiration_date TIMESTAMP WITH TIME ZONE NOT NULL,
  new_expiration_date TIMESTAMP WITH TIME ZONE NOT NULL,
  extension_days INTEGER NOT NULL,
  reason TEXT,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- 添加表注释
COMMENT ON TABLE public.subscription_extension_history IS '记录订阅延长历史，包括延长时间、原因等信息';

-- 添加列注释
COMMENT ON COLUMN public.subscription_extension_history.id IS '主键ID';
COMMENT ON COLUMN public.subscription_extension_history.user_id IS '用户ID';
COMMENT ON COLUMN public.subscription_extension_history.subscription_id IS '订阅ID';
COMMENT ON COLUMN public.subscription_extension_history.original_expiration_date IS '原始过期时间';
COMMENT ON COLUMN public.subscription_extension_history.new_expiration_date IS '新的过期时间';
COMMENT ON COLUMN public.subscription_extension_history.extension_days IS '延长天数';
COMMENT ON COLUMN public.subscription_extension_history.reason IS '延长原因';
COMMENT ON COLUMN public.subscription_extension_history.details IS '延长详细信息';
COMMENT ON COLUMN public.subscription_extension_history.created_at IS '创建时间';
COMMENT ON COLUMN public.subscription_extension_history.updated_at IS '更新时间';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_subscription_extension_history_user_id ON public.subscription_extension_history(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_extension_history_subscription_id ON public.subscription_extension_history(subscription_id);

-- 添加RLS策略
ALTER TABLE public.subscription_extension_history ENABLE ROW LEVEL SECURITY;

-- 创建访问策略：允许管理员和服务角色完全访问
CREATE POLICY "管理员完全访问" ON public.subscription_extension_history
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- 创建访问策略：允许用户查看自己的订阅延长历史
CREATE POLICY "用户查看自己的订阅延长历史" ON public.subscription_extension_history
  FOR SELECT
  USING (auth.uid() = user_id);

-- 添加触发器以自动更新updated_at字段
CREATE TRIGGER update_subscription_extension_history_updated_at
  BEFORE UPDATE ON public.subscription_extension_history
  FOR EACH ROW
  EXECUTE FUNCTION public.update_modified_column();

-- 创建视图，用于展示用户订阅延长摘要
CREATE OR REPLACE VIEW public.user_subscription_extensions AS
SELECT 
  user_id,
  COUNT(*) AS total_extensions,
  SUM(extension_days) AS total_extension_days,
  MAX(new_expiration_date) AS latest_expiration_date,
  jsonb_agg(
    jsonb_build_object(
      'extension_days', extension_days,
      'reason', reason,
      'original_date', original_expiration_date,
      'new_date', new_expiration_date
    ) ORDER BY created_at DESC
  ) AS recent_extensions
FROM public.subscription_extension_history
GROUP BY user_id; 