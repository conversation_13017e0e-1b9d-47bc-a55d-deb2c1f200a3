-- 确保subscription_plans数据存在
INSERT INTO public.subscription_plans (plan_id, name, description, price_usd, duration_days, features, sort_order, apple_product_id, google_product_id)
VALUES 
  ('free', 'Free', '免费基础版', 0, NULL, '{"custom_templates_limit": 1, "ai_model": "basic", "history_sync": false, "voice_minutes": 0, "api_key_type": "free"}'::jsonb, 1, NULL, NULL),
  ('monthly_subscription', '月度VIP', '每月订阅VIP版本', 18, 30, '{"custom_templates_limit": -1, "ai_model": "premium", "history_sync": true, "voice_minutes": 300, "api_key_type": "premium"}'::jsonb, 2, 'monthly_subscription', 'monthly_subscription'),
  ('annual_subscription', '年度VIP', '每年订阅VIP版本', 168, 365, '{"custom_templates_limit": -1, "ai_model": "premium", "history_sync": true, "voice_minutes": 3600, "api_key_type": "premium"}'::jsonb, 3, 'annual_subscription', 'annual_subscription')
ON CONFLICT (plan_id) DO NOTHING;

-- 确保ai_models数据存在 (包含多语言支持)
INSERT INTO public.ai_models (model_id, provider, name, description, name_key, description_key, is_vip_only, model_type, api_key_type, sort_order, is_active)
VALUES 
  -- 语言模型 (文本优化模型)
  ('qwen2.5-7b', 'siliconflow', 'QWen 2.5-7B', '阿里通义千问大语言模型，基础免费模型', 'aiModels.qwen2.5-7b.name', 'aiModels.qwen2.5-7b.description', false, 'text', 'free', 1, true),
  ('qwen3-8b', 'siliconflow', 'QWen 3-8B', '通义千问第三代中型模型，性能更优', 'aiModels.qwen3-8b.name', 'aiModels.qwen3-8b.description', true, 'text', 'premium', 2, true),
  ('qwen3-14b', 'siliconflow', 'QWen 3-14B', '通义千问第三代高性能模型，适合复杂文本', 'aiModels.qwen3-14b.name', 'aiModels.qwen3-14b.description', true, 'text', 'premium', 3, true),
  ('deepseek-v3', 'siliconflow', 'DeepSeek V3', '顶级大语言模型，适合复杂场景的理解和创作', 'aiModels.deepseek-v3.name', 'aiModels.deepseek-v3.description', true, 'text', 'premium', 4, true),
  
  -- 语音转写模型
  ('native', 'device', '原生转写', '设备内置语音识别服务，Android和iOS自带', 'aiModels.native.name', 'aiModels.native.description', false, 'voice', 'free', 1, true),
  ('whisper-1', 'openai', 'Whisper', 'OpenAI Whisper 语音识别，准确度高', 'aiModels.whisper-1.name', 'aiModels.whisper-1.description', false, 'voice', 'free', 2, true),
  ('gpt-4o-mini-transcribe', 'openai', 'GPT-4o Mini', '高精度智能转写，支持复杂场景和多语言', 'aiModels.gpt-4o-mini-transcribe.name', 'aiModels.gpt-4o-mini-transcribe.description', true, 'voice', 'premium', 3, true)
ON CONFLICT (model_id) DO UPDATE 
SET provider = EXCLUDED.provider,
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    name_key = EXCLUDED.name_key,
    description_key = EXCLUDED.description_key,
    is_vip_only = EXCLUDED.is_vip_only,
    model_type = EXCLUDED.model_type,
    api_key_type = EXCLUDED.api_key_type,
    sort_order = EXCLUDED.sort_order,
    is_active = EXCLUDED.is_active;

-- 确保用户设置表结构正确
DO $$
BEGIN
    -- 检查是否需要添加default_text_model和default_voice_model列
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'user_settings' 
        AND column_name = 'default_text_model'
    ) THEN
        ALTER TABLE public.user_settings ADD COLUMN default_text_model varchar(50) DEFAULT 'qwen2.5-7b';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'user_settings' 
        AND column_name = 'default_voice_model'
    ) THEN
        ALTER TABLE public.user_settings ADD COLUMN default_voice_model varchar(50) DEFAULT 'native';
    END IF;
END
$$;

-- 删除现有的系统模板数据（只删除系统模板，保留用户自定义模板）
DELETE FROM public.style_templates WHERE is_system = true;

-- 插入风格模板数据
INSERT INTO style_templates (
  name, description, name_key, description_key, prompt_text, 
  is_system, color, icon, position, is_vip_only, category, semantic_id
) VALUES
-- 使用场景类模板
('发邮件', '一键生成专业得体的商务邮件，自动适配收件人身份和邮件场景。', 
 'template.email.name', 'template.email.description',
 '写成正式邮件格式。开头用"尊敬的"或"您好"，结尾加署名。语言正式礼貌，每段不超过3行。

示例：
输入：李经理 项目报告周五前交 会议室改B栋201
输出：
尊敬的李经理：
您好！本周项目汇报调整至周五，地点变更为B栋201会议室。请准备项目进度报告参会。
期待您的出席。

此致
敬礼！
[你的姓名]',
 true, '#1E40AF', '📧', 1, false, 'scenario', 'email'),

('日常发信息', '让闲聊消息更自然生动，自动匹配好友亲密度和对话场景。', 
 'template.chat.name', 'template.chat.description',
 '转换成自然的聊天语气。用短句，口语化表达，适当添加表情符号。

示例：
输入：晚上聚餐地点改到万象城5楼那家川菜馆七点半不见不散
输出：
今晚聚餐换地方啦！🌶️
万象城5楼川菜馆
7:30集合，不见不散哦！🙌',
 true, '#059669', '💬', 2, false, 'scenario', 'chat'),

('做notes记录', '将零散信息转化为结构化的纯文本笔记，支持学习/会议/灵感记录场景。', 
 'template.notes.name', 'template.notes.description',
 '整理成结构化的纯文本笔记。用段落分层，序号标记，重要信息用【】高亮，用→标示要点。

示例：
输入：客户需求UI界面要简洁支付流程三步完成增加夜间模式
输出：
客户需求文档

核心功能：
1. UI设计 → 【极简风格】要求  
2. 支付流程 → 必须【3步内完成】
3. 视觉模式 → 【增加暗黑主题】功能

→ 优先级：支付流程优化最重要',
 true, '#7C3AED', '📝', 3, false, 'scenario', 'notes'),

('markdown格式的要点记录', '使用markdown格式生成结构化要点记录，支持标题层级和格式化。', 
 'template.markdown_notes.name', 'template.markdown_notes.description',
 '整理成markdown格式的结构化笔记。用标题、列表、要点分层。关键信息加粗，行动项用→标记。

示例：
输入：客户需求UI界面要简洁支付流程三步完成增加夜间模式
输出：
## 客户需求文档
### 核心功能
• **UI设计** → 极简风格
• **支付流程** → 3步内完成  
• **视觉模式** → 增加暗黑主题',
 true, '#8B5CF6', '📋', 23, true, 'scenario', 'markdown_notes'),

('原文优化', '保持原文基础上修正错误、删除重复和语气词，优化段落结构。', 
 'template.text_optimize.name', 'template.text_optimize.description',
 '仅做必要的文本优化。修正错误，删除重复词汇和语气词，优化段落呈现，尽可能保持原文表达。

示例：
输入：这个这个方案呢其实还是很不错的嗯我觉得可以考虑一下但是呢还需要再优化优化一下
输出：
这个方案很不错，可以考虑，但还需要进一步优化。',
 true, '#059669', '✨', 24, false, 'scenario', 'text_optimize'),

('emoji表情', '智能添加表情符号增强情绪表达，避免滥用尴尬场景。', 
 'template.emoji.name', 'template.emoji.description',
 '添加合适的emoji表情。每10-15字加1个emoji，匹配情绪色彩。

示例：
输入：会议推迟到明天上午十点请重新安排日程
输出：
会议改明天10点啦！📅
记得调整日程哟~ ⏰
别跑空！⚠️',
 true, '#EA580C', '😀', 4, false, 'scenario', 'emoji'),

('小红书文案', '打造爆款种草文案，自动生成分段标题+标签+emoji组合拳。', 
 'template.xhs.name', 'template.xhs.description',
 '写成小红书风格。标题加吸睛符号，正文短段落，每句配emoji，核心卖点用【】标注，结尾加3个话题标签。

示例：
输入：防晒霜测评SPF50+不油腻成膜快适合油皮
输出：
💥油皮天菜防晒！通勤党锁死这支！
【SPF50+】暴晒都不黑🌞
质地水感💦成膜快不搓泥
👉戳图看28天实测对比

⚠️户外补涂别偷懒
#防晒测评 #油皮护肤 #夏日必备',
 true, '#BE185D', '📱', 5, true, 'scenario', 'xiaohongshu'),

('朋友圈文案', '创作高互动率朋友圈，自动平衡真实感和精致度。', 
 'template.moment.name', 'template.moment.description',
 '写成朋友圈风格。开头用钩子，配场景emoji，结尾互动提问。

示例：
输入：加班完成年度提案希望客户认可
输出：
谁懂啊...凌晨三点的写字楼🌃
提案改完第8版真的掏空心思了💡
求锦鲤🙏 你觉得能过吗？',
 true, '#0891B2', '📲', 6, true, 'scenario', 'moments'),

-- 翻译类模板
('英语翻译', '母语级英译优化，智能处理俚语和文化隐喻。', 
 'template.enTrans.name', 'template.enTrans.description',
 '翻译成地道英语。正式文本用标准语法，口语内容用自然表达。

示例：
输入：这个方案需要优化细节
输出：This proposal requires finer tuning.',
 true, '#1E3A8A', '🇺🇸', 7, false, 'translation', 'en_translation'),

('日语翻译', '适配日本敬语体系，区分商务/动漫/日常场景。', 
 'template.jaTrans.name', 'template.jaTrans.description',
 '翻译成日语。客户用敬语，同事用丁宁语，朋友用常体。

示例：
输入：部长，请确认会议时间
输出：部長、会議の時間をご確認いただけますでしょうか。',
 true, '#EC4899', '🇯🇵', 8, true, 'translation', 'ja_translation'),

('韩语翻译', '精准转换韩语敬语与半语，自动区分商务与K-pop流行语境。', 
 'template.koTrans.name', 'template.koTrans.description',
 '翻译成韩语。商务用합쇼체，长辈用해요体，朋友用해체。

示例：
输入：经理，请批准这份报告
输出：매니저님, 이 보고서 승인 부탁드립니다.',
 true, '#7C3AED', '🇰🇷', 9, true, 'translation', 'ko_translation'),

('印尼语翻译', '适配东南亚多方言场景，智能处理宗教与文化敏感词。', 
 'template.idTrans.name', 'template.idTrans.description',
 '翻译成印尼语。男士用Pak，女士用Bu，口语加ya等粒子词。

示例：
输入：王先生，请确认订单
输出：Pak Wang, mohon konfirmasi pesanannya ya.',
 true, '#EA580C', '🇮🇩', 10, true, 'translation', 'id_translation'),

('德语翻译', '严谨处理德语格与词性，自动适配法律/工程/日常场景。', 
 'template.deTrans.name', 'template.deTrans.description',
 '翻译成德语。名词首字母大写，长句拆分，使用DIN标准术语。

示例：
输入：技术文档更新通知
输出：Aktualisierung der Technischen Dokumentation',
 true, '#374151', '🇩🇪', 11, true, 'translation', 'de_translation'),

('法语翻译', '优雅处理法语阴阳性与变位，强化文学性与商务正式感。', 
 'template.frTrans.name', 'template.frTrans.description',
 '翻译成法语。注意阴阳性配合，使用优雅表达，引号用« »。

示例：
输入：这个决定很重要
输出：Cette décision est importante.',
 true, '#3B82F6', '🇫🇷', 12, true, 'translation', 'fr_translation'),

('西班牙语翻译', '灵活转换拉丁美洲/西班牙变体，智能处理倒装句结构。', 
 'template.esTrans.name', 'template.esTrans.description',
 '翻译成西班牙语。疑问句前置动词，感叹句用¡!包围。

示例：
输入：你好，会议在哪里？
输出：¡Hola! ¿Dónde está la reunión?',
 true, '#DC2626', '🇪🇸', 13, true, 'translation', 'es_translation'),

-- AI提示词类模板 - 生图类
('Recraft生图', '生成矢量艺术专用提示词，自动匹配平面设计规范。', 
 'template.recraft.name', 'template.recraft.description',
 '转换成Recraft生图提示词。结构：[对象], [风格], [色彩], [构图]。必须包含vector graphic。

示例：
输入：科技公司logo设计
输出：Modern tech company logo, vector graphic, minimal flat design, geometric shapes, blue gradient color scheme, centered composition, scalable vector format',
 true, '#6366F1', '🎨', 14, true, 'ai_prompt', 'recraft'),

('Midjourney V7', '适配V7最新参数，强化细节控制与风格混合能力。', 
 'template.midjourney.name', 'template.midjourney.description',
 '转换成MJ V7提示词。格式：/imagine prompt: [主体], [场景] [风格] --v 7.0 --style raw。

示例：
输入：赛博朋克城市
输出：/imagine prompt: neon-drenched cyberpunk cityscape, holographic ads::blade runner style --v 7.0 --style raw --stylize 700',
 true, '#8B5CF6', '🖼️', 15, true, 'ai_prompt', 'midjourney_v7'),

('Stable Diffusion', '专业控制SDXL模型，优化负面提示与LORA触发词。', 
 'template.stableDiffusion.name', 'template.stableDiffusion.description',
 '转换成SD提示词。正面：[主体]+[质量词]+[光影]，必含masterpiece, best quality, 8k。

示例：
输入：古风美女肖像
输出：
Positive: ancient chinese beauty portrait, masterpiece, best quality, 8k, soft lighting, traditional hanfu dress, elegant pose
Negative: ugly, blurry, low quality, distorted',
 true, '#10B981', '⚡', 16, true, 'ai_prompt', 'stable_diffusion'),

('即梦AI', '专为中文图像生成优化，智能转换古诗文与成语意境。', 
 'template.dreamAI.name', 'template.dreamAI.description',
 '转换成即梦AI中文提示词。用古风雅致描述，强调意境美感。

示例：
输入：山水画
输出：水墨山水，烟波浩渺，远山如黛，近水含烟，古风意境，水墨晕染，留白艺术',
 true, '#6B7280', '🌸', 17, true, 'ai_prompt', 'jimeng_ai'),

-- 写作风格类模板  
('正式公文', '符合机关单位标准的公文写作规范，严肃准确。', 
 'template.official.name', 'template.official.description',
 '写成正式公文。用标准格式，语言严谨，逻辑清晰，事实准确。

示例：
输入：通知大家明天开会
输出：
关于召开部门例会的通知

各位同事：
兹定于明日上午9时召开部门例会，请准时参加。
特此通知。

办公室
年月日',
 true, '#94A3B8', '📄', 18, false, 'writing', 'official'),

('学术论文', '严谨的学术写作风格，符合期刊投稿标准。', 
 'template.academic.name', 'template.academic.description',
 '写成学术论文风格。逻辑严密，用词准确，客观中性，有理有据。

示例：
输入：这个方法很有效
输出：实验结果表明，该方法在测试数据集上取得了显著的性能提升（p<0.05），相较于基线方法提高了15.3%的准确率。',
 true, '#94A3B8', '🎓', 19, false, 'writing', 'academic'),

('新闻报道', '客观中立的新闻写作，倒金字塔结构。', 
 'template.news.name', 'template.news.description',
 '写成新闻报道。重要信息前置，客观中立，事实准确，时间地点人物清晰。

示例：
输入：公司推出新产品
输出：本报讯 XX科技公司今日发布新一代智能产品，该产品采用先进技术，预计将于下月正式上市。公司负责人表示，新产品将显著提升用户体验。',
 true, '#1E40AF', '📰', 20, false, 'writing', 'news'),

('创意文案', '充满创意和感染力的营销文案，吸引眼球。', 
 'template.creative.name', 'template.creative.description',
 '写成创意文案。语言生动，富有感染力，突出核心卖点，引发情感共鸣。

示例：
输入：咖啡店开业
输出：☕ 香气唤醒的不只是味蕾
更是对美好生活的向往
新店开业，与你共享每一个温暖瞬间
#遇见美好 从这杯咖啡开始',
 true, '#F59E0B', '✨', 21, true, 'writing', 'creative'),

('口语化表达', '将正式语言转换为自然口语，亲切易懂。', 
 'template.casual.name', 'template.casual.description',
 '转换成口语化表达。用简单词汇，自然语气，就像和朋友聊天。

示例：
输入：由于天气原因，活动取消
输出：因为下雨，活动取消了，大家别白跑一趟哈！',
 true, '#10B981', '💬', 22, false, 'writing', 'casual'),

('中文翻译（简体）', '精准翻译各种语言为规范简体中文，保持原意并符合中文表达习惯。', 
 'template.zhCnTrans.name', 'template.zhCnTrans.description',
 '翻译成标准简体中文。保持原文含义，使用规范汉字，符合中文表达习惯。

示例：
输入：Hello, how are you today?
输出：你好，你今天怎么样？',
 true, '#DC2626', '🇨🇳', 25, true, 'translation', 'zh_cn_translation'),

('中文翻译（繁体）', '精准翻译各种语言为标准繁体中文，保持原意并符合繁体中文表达习惯。', 
 'template.zhTwTrans.name', 'template.zhTwTrans.description',
 '翻译成標準繁體中文。保持原文含義，使用標準繁體漢字，符合繁體中文表達習慣。

示例：
輸入：Hello, how are you today?
輸出：你好，你今天怎麼樣？',
 true, '#DC2626', '🇭🇰', 26, true, 'translation', 'zh_tw_translation')
ON CONFLICT (semantic_id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  name_key = EXCLUDED.name_key,
  description_key = EXCLUDED.description_key,
  prompt_text = EXCLUDED.prompt_text,
  is_system = EXCLUDED.is_system,
  color = EXCLUDED.color,
  icon = EXCLUDED.icon,
  position = EXCLUDED.position,
  is_vip_only = EXCLUDED.is_vip_only,
  category = EXCLUDED.category,
  updated_at = now();

-- 检查是否有默认语言模型数据，如果没有则设置默认值
DO $$
BEGIN
    -- 检查是否有设置为true的默认语言模型
    IF NOT EXISTS (
        SELECT 1 FROM public.ai_models 
        WHERE model_type = 'text' 
        AND is_active = true 
        AND is_vip_only = false
        LIMIT 1
    ) THEN
        -- 如果没有可用的免费文本模型，记录一个警告
        RAISE NOTICE '警告：没有找到可用的免费文本模型';
    END IF;

    -- 检查是否有可用的免费语音模型
    IF NOT EXISTS (
        SELECT 1 FROM public.ai_models 
        WHERE model_type = 'voice' 
        AND is_active = true 
        AND is_vip_only = false
        LIMIT 1
    ) THEN
        -- 如果没有可用的免费语音模型，记录一个警告
        RAISE NOTICE '警告：没有找到可用的免费语音模型';
    END IF;
END
$$; 