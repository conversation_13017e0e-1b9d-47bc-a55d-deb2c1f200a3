#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}========== 直接使用Deno运行函数 ==========${NC}"

# 1. 检查参数
FUNCTION_NAME=${1:-"revenuecat-webhook"}
PORT=${2:-"8000"}
echo -e "${YELLOW}将运行函数: ${FUNCTION_NAME} 在端口: ${PORT}${NC}"

# 支持的函数列表
SUPPORTED_FUNCTIONS=("revenuecat-webhook" "revenuecat-check" "unified-proxy")
if [[ ! " ${SUPPORTED_FUNCTIONS[@]} " =~ " ${FUNCTION_NAME} " ]]; then
    echo -e "${YELLOW}支持的函数: ${SUPPORTED_FUNCTIONS[*]}${NC}"
fi

# 2. 设置环境变量
echo -e "${YELLOW}设置环境变量...${NC}"
# 确保Deno可用
export PATH="$HOME/.deno/bin:$PATH"
# 设置端口环境变量
export PORT="$PORT"
# Supabase URL和密钥
export SUPABASE_URL="http://127.0.0.1:54321"
export SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
export SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"
# RevenueCat webhook 测试密钥
export REVENUECAT_WEBHOOK_SECRET="test_webhook_secret"

# 加载 .env 文件中的额外环境变量（如果存在）
# 首先尝试加载 supabase 目录下的 .env.local 文件（边缘函数专用）
if [[ -f "./.env.local" ]]; then
    echo -e "${YELLOW}加载 ./.env.local 文件中的环境变量...${NC}"
    set -o allexport
    source "./.env.local"
    set +o allexport
fi

# 然后尝试加载项目根目录的 .env 文件
ENV_PATHS=("../.env" "./.env" "../../.env")
for env_path in "${ENV_PATHS[@]}"; do
    if [[ -f "$env_path" ]]; then
        echo -e "${YELLOW}加载 $env_path 文件中的环境变量...${NC}"
        set -o allexport
        source "$env_path"
        set +o allexport
        break
    fi
done

# 如果没有找到 .env 文件，显示提示信息
if [[ -z "${SILICONFLOW_API_KEY:-}" && -z "${OPENAI_API_KEY:-}" ]]; then
    echo -e "${RED}警告: 未找到 .env 文件或API密钥未设置${NC}"
    echo -e "${YELLOW}请在项目根目录创建 .env 文件并设置以下变量:${NC}"
    echo -e "${YELLOW}SILICONFLOW_API_KEY=你的SiliconFlow密钥${NC}"
    echo -e "${YELLOW}OPENAI_API_KEY=你的OpenAI密钥${NC}"
    echo ""
fi

# 统一代理函数特定的环境变量
if [[ "$FUNCTION_NAME" == "unified-proxy" ]]; then
    if [[ -z "${SILICONFLOW_API_KEY:-}" ]]; then
        echo -e "${RED}警告: SILICONFLOW_API_KEY 未设置，AI 代理功能可能无法正常工作${NC}"
    fi
    if [[ -z "${OPENAI_API_KEY:-}" ]]; then
        echo -e "${YELLOW}注意: OPENAI_API_KEY 未设置，将仅使用 SiliconFlow API${NC}"
    fi
fi

# 设置缓存目录
export DENO_DIR="$(pwd)/.deno_cache"
mkdir -p $DENO_DIR

# 3. 切换到函数目录
cd "$(dirname $0)/../supabase/functions/$FUNCTION_NAME"
echo -e "${YELLOW}当前目录: $(pwd)${NC}"

# 4. 运行函数
echo -e "${YELLOW}使用本地Deno启动函数...${NC}"
echo -e "${YELLOW}按Ctrl+C终止服务器${NC}"
echo -e "${GREEN}-----------------------------------${NC}"

# 使用参数来确保网络可用，并访问环境变量
deno run --allow-net --allow-env --allow-read --import-map=./import_map.json index.ts

echo -e "${GREEN}========== 执行完成 ==========${NC}" 