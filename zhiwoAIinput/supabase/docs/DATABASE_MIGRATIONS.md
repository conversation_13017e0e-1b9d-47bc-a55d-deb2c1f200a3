# Supabase数据库迁移和版本管理最佳实践

本文档提供了"知我AI输入法"项目中Supabase数据库迁移和版本管理的最佳实践指南。

## 迁移文件命名规范

迁移文件应使用以下命名格式：`YYYYMMDDHHMMSS_descriptive_name.sql`

- `YYYYMMDDHHMMSS`: 年月日时分秒的时间戳，确保迁移按照创建顺序执行
- `descriptive_name`: 描述迁移内容的简短名称，使用下划线分隔单词

例如：
- `20240511000000_initial_schema.sql` - 初始数据库模式创建
- `20240611000000_auth_user_trigger.sql` - 添加认证用户触发器
- `20240717000000_revenuecat_fixes.sql` - 修复RevenueCat webhook相关问题

## 创建新的迁移文件

### 1. 自动生成迁移文件（推荐）

使用Supabase CLI创建新的迁移文件：

```bash
# 安装Supabase CLI (如果尚未安装)
npm install -g supabase

# 生成新的迁移文件
supabase migration new descriptive_name
```

这将在`supabase/migrations`目录下创建一个带有当前时间戳的新迁移文件。

### 2. 手动创建迁移文件

如果需要手动创建，请确保遵循命名规范：

```bash
# 使用当前时间创建文件名
TIMESTAMP=$(date +%Y%m%d%H%M%S)
FILENAME="${TIMESTAMP}_descriptive_name.sql"
touch "supabase/migrations/$FILENAME"
```

## 迁移文件内容结构

每个迁移文件应该：

1. 开始部分包含注释，描述迁移的目的
2. 包含可以重复执行的SQL语句（幂等性）
3. 使用`IF EXISTS`/`IF NOT EXISTS`等条件语句，避免执行错误
4. 尽可能使用事务包装相关操作

示例结构：

```sql
-- 迁移文件：20240717000000_add_new_feature.sql
-- 描述：添加新功能X所需的数据库结构

-- 创建新表
CREATE TABLE IF NOT EXISTS public.new_table (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_new_table_name ON public.new_table(name);

-- 添加权限
ALTER TABLE public.new_table ENABLE ROW LEVEL SECURITY;
CREATE POLICY "用户可以查看公开记录" ON public.new_table 
  FOR SELECT USING (is_public = true OR auth.uid() = user_id);
```

## 应用迁移

### 本地开发环境

在本地开发环境中应用迁移：

```bash
# 启动本地Supabase服务
supabase start

# 应用所有迁移
supabase db reset

# 或仅应用新迁移
supabase db push
```

### 生产环境

对于生产环境，可以使用以下方法应用迁移：

```bash
# 链接到远程项目
supabase link --project-ref your-project-ref

# 推送迁移到远程数据库
supabase db push
```

或者通过Supabase Dashboard的SQL编辑器手动执行迁移脚本。

## 迁移版本管理最佳实践

1. **增量变更**：每个迁移文件应包含小的、独立的变更，便于理解和回滚
2. **不删除旧迁移**：旧迁移文件是数据库演变的历史记录，不应被删除
3. **测试迁移**：在应用到生产环境前，在测试环境中验证迁移脚本
4. **版本控制**：所有迁移文件应纳入版本控制系统
5. **备份数据**：在执行重要迁移前备份数据库
6. **向前兼容**：设计迁移时考虑向前兼容性，避免破坏现有功能

## 处理敏感变更

对于删除列、表或重命名等敏感操作，建议分多个迁移步骤完成：

1. 添加新结构
2. 更新代码使用新结构
3. 在确认旧结构不再使用后，创建新的迁移删除旧结构

## 常见问题解决

### 迁移冲突

如果遇到迁移冲突，可以尝试：

```bash
# 重置本地数据库
supabase db reset

# 或在生产环境中，查看迁移历史
supabase db remote commit
```

### 回滚迁移

Supabase目前不直接支持迁移回滚。建议的做法是：

1. 创建新的"向前"迁移，撤销之前迁移的更改
2. 对于紧急情况，可以从备份恢复数据库

## 种子数据管理

使用`seed.sql`文件管理测试或初始数据：

```bash
# 应用种子数据
supabase db seed
```

`seed.sql`文件应包含`ON CONFLICT DO NOTHING`或`ON CONFLICT DO UPDATE`子句，以确保可以重复执行。

## 结论

遵循这些最佳实践可以帮助维护一个健康、可维护的数据库架构，简化团队协作，并减少生产环境中的问题。定期审查和更新这些实践，以适应项目的增长和变化。 