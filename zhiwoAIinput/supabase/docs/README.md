# 知我AI输入法 - Supabase数据库服务

本文档说明了"知我AI输入法"项目中Supabase数据库结构、功能和使用方法，特别是针对RevenueCat的支付和VIP订阅管理系统。

## 数据库结构概览

我们实现了一个全面的数据库系统，支持以下核心功能：

### 1. 用户管理相关
- **profiles**：用户档案表，存储用户基本信息及VIP状态
- **auth_user_trigger**：自动创建用户相关数据的触发器

### 2. 订阅管理相关
- **subscription_plans**：订阅计划表，存储不同的VIP订阅方案
- **subscriptions**：用户订阅记录表，记录每个用户的订阅状态
- **subscription_events**：订阅事件记录表，记录所有订阅相关事件
- **subscription_extension_history**：订阅延期历史记录表
- **user_subscription_extensions**：用户订阅延期统计视图
- **temporary_entitlements**：临时授权记录表，用于处理商店服务中断等情况
- **active_subscribers**：活跃订阅用户视图

### 3. 支付和账单相关
- **payments**：支付记录表，记录所有支付事件
- **billing_issues**：账单问题记录表，记录账单失败事件
- **invoices**：发票记录表，记录RevenueCat发票信息

### 4. 分析视图
- **trial_conversion_analysis**：试用转付费分析视图
- **trial_cancellation_analysis**：试用取消分析视图
- **invoice_analysis**：发票分析视图
- **invoice_conversion_rate**：发票转化率视图
- **temporary_entitlement_analysis**：临时授权分析视图

### 5. 功能特性相关
- **ai_models**：AI模型管理表，区分免费和VIP用户可用的模型
- **style_templates**：风格模板表，包括系统预设和用户自定义模板
- **sessions**：用户会话表，记录用户的输入和AI生成内容
- **user_settings**：用户设置表，存储用户偏好

## 最新数据库功能

### 1. 完善的VIP订阅管理

我们实现了多层次的VIP订阅管理系统：

- **订阅生命周期管理**：包括创建、续订、取消、恢复、过期等
- **试用期管理**：支持免费试用及其转付费分析
- **订阅计划变更**：支持升级、降级和平级变更
- **账单问题处理**：自动处理及记录账单失败事件
- **退款处理**：支持全额和部分退款流程

### 2. 临时授权系统

为应对商店服务中断等特殊情况，我们实现了临时授权系统：

- **临时VIP权益授予**：当商店支付服务中断时提供临时VIP权益
- **授权验证机制**：支持后续验证及转为正式订阅
- **授权管理功能**：支持延长、撤销和批量处理临时授权

### 3. 订阅延期功能

为提高用户留存，我们实现了订阅延期系统：

- **延期记录追踪**：记录所有延期历史及原因
- **延期聚合视图**：统计分析用户延期情况

### 4. 高级分析视图

为产品决策提供数据支持：

- **试用转化分析**：分析试用用户转付费情况
- **账单分析**：提供账单成功率和地区分布数据
- **用户行为追踪**：关联订阅状态与产品使用行为

## 数据库迁移文件

我们的数据库结构通过以下主要迁移文件演进：

1. **20240511000000_initial_schema.sql**：初始数据库架构
2. **20240611000000_auth_user_trigger.sql**：用户认证触发器
3. **20240717000000_revenuecat_fixes.sql**：RevenueCat集成修复
4. **20240820000000_add_billing_issues_and_payments.sql**：账单和支付表
5. **20240920000000_add_product_change_fields.sql**：产品变更相关字段
6. **20240925000001_create_subscription_extension_history.sql**：订阅延期历史
7. **20240930000000_add_trial_fields.sql**：试用期相关字段
8. **20240930000001_add_trial_conversion_fields.sql**：试用转付费字段
9. **20241005000000_add_invoices_table.sql**：发票表及相关功能
10. **20241010000000_add_temporary_entitlements_table.sql**：临时授权表
11. **20241010000001_add_temporary_entitlement_actions.sql**：临时授权管理操作
12. **20241010000002_update_temporary_entitlement_handler.sql**：临时授权处理优化

## RevenueCat Webhook处理优化

我们对RevenueCat webhook处理进行了全面优化：

1. **增强错误处理**：添加详细错误日志及异常处理逻辑
2. **用户存在性验证**：确保用户存在于数据库中
3. **多重复原机制**：实现多种失败情况的恢复策略
4. **临时授权处理**：支持商店服务中断时的授权机制
5. **试用转付费处理**：优化试用期转为付费订阅的流程

## 使用指南

### 部署数据库迁移

1. 本地开发环境：
```bash
supabase db reset
# 或
supabase db push
```

2. 生产环境：
```bash
supabase link --project-ref your-project-ref
supabase db push
```

### 配置RevenueCat集成

1. 在RevenueCat控制台配置Webhook：
   - URL: `https://[YOUR_PROJECT_REF].supabase.co/functions/v1/revenuecat-webhook`
   - Secret: 与环境变量REVENUECAT_WEBHOOK_SECRET匹配
   - 事件类型: 建议全选以确保所有事件都能正确处理

2. 确保产品ID映射正确：
   - Supabase中的subscription_plans表的apple_product_id和google_product_id
   - 必须与RevenueCat中配置的产品ID一致

### 数据验证查询

```sql
-- 检查用户VIP状态
SELECT id, is_vip, vip_expires_at, vip_is_trial FROM profiles WHERE id = '[USER_ID]';

-- 检查用户活跃订阅
SELECT * FROM subscriptions WHERE user_id = '[USER_ID]' AND active = true;

-- 查看用户的临时授权情况
SELECT * FROM temporary_entitlements WHERE user_id = '[USER_ID]';

-- 分析试用转付费情况
SELECT * FROM trial_conversion_analysis WHERE user_id = '[USER_ID]';
```

## 故障排除

- **订阅状态异常**：
  - 检查webhook日志找出事件处理错误
  - 验证subscription_events表中的原始事件数据
  - 使用RevenueCat控制台测试事件重放

- **临时授权问题**：
  - 确认临时授权记录是否正确创建
  - 检查validation_subscription_id是否正确关联到订阅

- **多设备同步问题**：
  - 确保用户标识符在多个设备间保持一致
  - 检查profiles表中的device_tokens字段是否正确更新

# 知我AI输入法 Supabase 后端

本目录包含知我AI输入法应用的Supabase后端配置，包括数据库架构、Edge Functions和部署脚本。

## 项目结构

```
supabase/
├── migrations/             # 数据库迁移文件
│   ├── 20240511000000_initial_schema.sql      # 初始数据库架构
│   ├── 20240611000000_auth_user_trigger.sql   # 用户认证触发器
│   ├── 20240717000000_revenuecat_fixes.sql    # RevenueCat集成修复
│   ├── 20240820000000_add_billing_issues_and_payments.sql  # 账单和支付表
│   ├── 20240920000000_add_product_change_fields.sql        # 产品变更字段
│   ├── 20240925000001_create_subscription_extension_history.sql  # 订阅延期
│   ├── 20240930000000_add_trial_fields.sql                 # 试用期字段
│   ├── 20240930000001_add_trial_conversion_fields.sql      # 试用转付费字段
│   ├── 20241005000000_add_invoices_table.sql               # 发票表
│   ├── 20241010000000_add_temporary_entitlements_table.sql # 临时授权表
│   ├── 20241010000001_add_temporary_entitlement_actions.sql # 临时授权操作
│   └── 20241010000002_update_temporary_entitlement_handler.sql # 临时授权处理
├── functions/              # Edge Functions
│   ├── revenuecat-webhook/ # RevenueCat Webhook处理函数
│   └── revenuecat-check/   # 订阅状态检查函数
├── seed.sql                # 初始数据填充脚本
├── deploy-cloud.sh         # 云端部署脚本
├── run-local.sh            # 本地开发脚本
├── test-function.sh        # 函数测试脚本
├── SUBSCRIPTION_GUIDE.md   # 订阅系统实现指南
└── README.md               # 本说明文件
```

## 主要功能

1. **用户管理**：基于Supabase Auth的用户认证与资料管理
2. **模板存储**：存储和管理用户自定义模板与系统预设模板
3. **订阅系统**：基于RevenueCat的应用内订阅全生命周期管理
4. **临时授权**：特殊情况下的临时VIP权益授予系统
5. **账单管理**：处理支付记录、发票和账单问题
6. **数据分析**：提供多种分析视图支持决策

## 数据库表结构

### 核心表

- `profiles`: 用户资料及VIP权限状态
- `style_templates`: 风格模板与用户自定义模板
- `sessions`: 用户会话记录
- `subscription_plans`: 订阅计划定义
- `subscriptions`: 用户订阅状态记录
- `subscription_events`: 订阅事件记录
- `subscription_extension_history`: 订阅延期记录
- `temporary_entitlements`: 临时授权记录
- `payments`: 支付记录
- `billing_issues`: 账单问题记录
- `invoices`: 发票记录
- `ai_models`: AI模型配置
- `user_settings`: 用户设置

查看完整数据库设计文档：`SUBSCRIPTION_GUIDE.md`

## 部署说明

### 前提条件

1. 安装Supabase CLI：https://supabase.com/docs/guides/cli
2. 创建Supabase项目
3. 配置环境变量文件：在项目根目录创建.env

```
PROJECT_ID="your_supabase_project_id"
REVENUECAT_WEBHOOK_SECRET="your_webhook_secret"
CHECK_API_SECRET="your_api_secret"
```

### 部署命令

```bash
# 确保脚本可执行
chmod +x deploy-cloud.sh

# 部署到云端
./deploy-cloud.sh
```

## RevenueCat 集成

1. 在RevenueCat控制台创建相应的产品：
   - 月度订阅: `monthly_subscription`
   - 年度订阅: `annual_subscription`
   - 试用订阅: `trial_subscription`

2. 配置Webhook：
   - URL: `https://[YOUR_PROJECT_REF].supabase.co/functions/v1/revenuecat-webhook`
   - 事件类型: 全选以确保所有事件都能被处理
   - 设置Webhook密钥并在环境变量中配置

## 订阅系统测试

遵循以下步骤测试订阅系统：

1. **准备工作**
   - 确认部署脚本成功运行
   - 验证数据库表结构完整
   - 准备测试账号与测试设备

2. **测试核心订阅流程**
   - 首次购买流程测试
   - 订阅续订测试
   - 订阅取消与恢复测试
   - 跨平台订阅同步测试

3. **测试高级功能**
   - 试用转付费流程测试
   - 订阅升级/降级测试
   - 临时授权创建与验证测试
   - 账单问题处理与自动恢复测试
   - 详细测试计划请参考`SUBSCRIPTION_GUIDE.md`

## 本地开发

```bash
# 启动本地开发环境
./run-local.sh

# 测试函数
./test-function.sh revenuecat-webhook
```

## 常见问题

- **RevenueCat与后端不同步**: 确认webhook是否正常接收事件
- **用户权益未正确更新**: 检查profiles表和subscriptions表状态
- **订阅升级/降级失败**: 检查product_change_fields相关处理
- **临时授权未正确创建**: 检查webhook函数中的handleTemporaryEntitlementGrant函数
- **分析视图数据不准确**: 检查原始事件数据与处理函数逻辑 