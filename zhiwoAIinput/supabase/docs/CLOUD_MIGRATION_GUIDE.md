# 知我AI输入法 - Supabase云端迁移指南

## 🎯 迁移概述

本指南将帮助你将本地Supabase环境迁移到云端，建立完整的开发、测试、生产环境。

## 📋 前置要求

### 1. 安装必要工具
```bash
# 安装Supabase CLI
npm install -g supabase
# 或使用brew (macOS)
brew install supabase/tap/supabase
```

### 2. 创建云端项目
1. 访问 [Supabase Dashboard](https://supabase.com/dashboard)
2. 点击 "New Project"
3. 选择组织和区域（建议选择离用户最近的区域）
4. 创建项目并记录项目ID

### 3. 配置环境变量
创建 `.env` 文件（根目录下）：
```bash
# Supabase 云端项目配置
PROJECT_ID=your-supabase-project-id-here
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_KEY=your-service-key-here

# RevenueCat 配置
REVENUECAT_WEBHOOK_SECRET=your-revenuecat-webhook-secret-here

# 订阅检查API密钥（自动生成或手动设置）
CHECK_API_SECRET=your-check-api-secret-here

# AI服务配置
SILICONFLOW_API_KEY=your-siliconflow-api-key-here

# 环境标识
ENVIRONMENT=staging
```

## 🚀 迁移步骤

### 步骤1: 登录Supabase CLI
```bash
cd zhiwoAIinput/supabase
supabase login
```

### 步骤2: 执行迁移脚本
```bash
# 确保在supabase目录下
chmod +x deploy-cloud.sh
./deploy-cloud.sh
```

### 步骤3: 验证迁移结果
脚本会自动验证：
- 数据库结构是否正确创建
- Edge函数是否部署成功
- 环境变量是否设置正确

## 🔄 持续开发流程

### 开发新功能时：
1. **本地开发**
   ```bash
   cd zhiwoAIinput/supabase
   ./run-local.sh  # 启动本地环境
   ```

2. **数据库变更**
   ```bash
   # 创建新的迁移文件
   supabase migration new feature_name
   # 编辑 supabase/migrations/YYYYMMDDHHMMSS_feature_name.sql
   ```

3. **本地测试**
   ```bash
   supabase db reset  # 重置本地数据库并应用所有迁移
   ```

4. **推送到云端**
   ```bash
   ./deploy-cloud.sh  # 将变更推送到云端
   ```

### 数据库迁移最佳实践：
- ✅ **只能添加新的迁移文件**
- ✅ **迁移文件按时间戳命名**
- ❌ **永远不要修改已部署的迁移文件**
- ❌ **永远不要删除已部署的迁移文件**

## 🏗️ 环境架构建议

### 三环境模式
```
本地开发环境 → 云端测试环境 → 云端生产环境
    ↓              ↓              ↓
  开发测试    →    集成测试    →    正式发布
```

### 环境配置
1. **本地环境**: 完全离线开发
2. **测试环境**: 使用当前迁移的云端项目
3. **生产环境**: 另创建一个独立的云端项目

## 📊 备份策略

### 自动备份
- Supabase提供每日自动备份
- 免费计划保留7天，Pro计划保留30天

### 手动备份
```bash
# 导出数据库结构和数据
supabase db dump --data-only > backup.sql
supabase db dump --schema-only > schema.sql
```

### 恢复备份
```bash
# 从备份恢复
supabase db reset --with-data backup.sql
```

## 🔐 安全注意事项

1. **环境变量管理**
   - 使用不同的API密钥为不同环境
   - 定期轮换敏感密钥
   - 确保 `.env` 文件在 `.gitignore` 中

2. **数据库访问**
   - 生产环境启用行级安全(RLS)
   - 限制服务密钥的使用范围
   - 定期审查访问权限

## 🐛 常见问题排查

### 迁移失败
```bash
# 检查迁移状态
supabase migration list

# 手动应用特定迁移
supabase db execute < migrations/YYYYMMDDHHMMSS_migration_name.sql
```

### 函数部署失败
```bash
# 重新部署函数
supabase functions deploy function-name

# 查看函数日志
supabase functions logs function-name
```

### 环境变量问题
```bash
# 查看当前设置的密钥
supabase secrets list

# 更新密钥
supabase secrets set KEY_NAME=new_value
```

## 📞 支持

如遇到问题：
1. 查看Supabase官方文档
2. 检查项目的GitHub Issues
3. 联系开发团队 