# Supabase边缘函数环境变量管理指南

本指南详细说明如何在Supabase边缘函数中管理环境变量，包括本地开发和云端部署两种场景。

## 📋 概述

本项目将Supabase边缘函数的环境变量与React Native应用的环境变量分开管理：

- **React Native应用**: 使用项目根目录的 `.env` 文件
- **Supabase边缘函数**: 使用 `supabase/` 目录下的独立环境变量配置

## 📁 文件结构

```
zhiwoAIinput/
├── .env                          # React Native应用环境变量
├── supabase/
│   ├── config.toml              # Supabase配置文件
│   ├── env.template             # 环境变量模板文件
│   ├── .env.local               # 本地开发环境变量（需要创建）
│   ├── env-setup.sh             # 环境变量管理脚本
│   └── functions/
│       └── unified-proxy/
│           └── index.ts         # 边缘函数代码
```

## 🚀 快速开始

### 1. 设置本地开发环境

```bash
# 进入supabase目录
cd supabase

# 运行环境变量设置脚本
./env-setup.sh local
```

脚本会：
- 从 `env.template` 创建 `.env.local` 文件
- 验证环境变量配置
- 提示需要填写的API密钥

### 2. 配置API密钥

编辑 `supabase/.env.local` 文件，填入真实的API密钥：

```bash
# AI服务API密钥
SILICONFLOW_API_KEY="sk-your-actual-siliconflow-key"
OPENAI_API_KEY="sk-your-actual-openai-key"
ANTHROPIC_API_KEY="sk-ant-your-actual-anthropic-key"
ELEVENLABS_API_KEY="your-actual-elevenlabs-key"
```

### 3. 本地测试

```bash
# 启动本地Supabase服务
supabase start

# 运行边缘函数
supabase functions serve unified-proxy --env-file .env.local
```

### 4. 部署到云端

```bash
# 设置云端环境变量
./env-setup.sh cloud

# 部署边缘函数
supabase functions deploy unified-proxy
```

## 🔧 详细配置

### 本地开发环境变量加载

在本地开发时，环境变量按以下优先级加载：

1. **命令行参数**: `--env-file .env.local`
2. **config.toml**: `[function_env]` 部分
3. **系统环境变量**: `export VAR=value`

### 云端环境变量管理

云端环境变量通过Supabase Secrets管理：

```bash
# 设置单个secret
supabase secrets set SILICONFLOW_API_KEY="your-key"

# 查看所有secrets
supabase secrets list

# 删除secret
supabase secrets unset SILICONFLOW_API_KEY
```

## 📝 环境变量说明

### 必需变量

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `SILICONFLOW_API_KEY` | SiliconFlow AI服务密钥 | `sk-...` |
| `OPENAI_API_KEY` | OpenAI服务密钥 | `sk-...` |

### 可选变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `ANTHROPIC_API_KEY` | Anthropic服务密钥 | - |
| `ELEVENLABS_API_KEY` | ElevenLabs服务密钥 | - |
| `PORT` | 服务端口 | `8000` |
| `ENVIRONMENT` | 运行环境 | `production` |
| `DEBUG` | 调试模式 | `false` |

## 🛠️ 管理脚本使用

### 环境变量管理脚本

```bash
# 设置本地环境
./env-setup.sh local

# 设置云端环境
./env-setup.sh cloud

# 查看云端配置
./env-setup.sh list

# 显示帮助
./env-setup.sh help
```

### 验证配置

边缘函数启动时会自动验证环境变量：

```
[INFO] 环境: development
[INFO] 端口: 8000
[WARNING] 以下环境变量未配置: ANTHROPIC_API_KEY, ELEVENLABS_API_KEY
[INFO] 可用的服务: siliconflow, openai
```

## 🔐 安全最佳实践

### 1. 文件权限

```bash
# 设置适当的文件权限
chmod 600 .env.local
chmod +x env-setup.sh
```

### 2. Git忽略

确保 `.gitignore` 包含：

```gitignore
# 环境变量文件
.env
.env.local
.env*.local
```

### 3. API密钥轮换

定期更换API密钥并更新配置：

```bash
# 更新本地配置
vim .env.local

# 更新云端配置
./env-setup.sh cloud
```

## 🐛 故障排查

### 常见问题

1. **环境变量未加载**
   ```bash
   # 检查文件是否存在
   ls -la .env.local
   
   # 检查环境变量语法
   source .env.local && echo $SILICONFLOW_API_KEY
   ```

2. **云端密钥未设置**
   ```bash
   # 检查是否登录
   supabase projects list
   
   # 查看云端secrets
   supabase secrets list
   ```

3. **服务不可用**
   ```bash
   # 查看边缘函数日志
   supabase functions logs unified-proxy
   ```

### 调试模式

启用调试模式获取更多信息：

```bash
# 本地调试
DEBUG=true supabase functions serve unified-proxy --env-file .env.local

# 云端调试
supabase secrets set DEBUG="true"
```

## 📚 相关文档

- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- [Supabase CLI](https://supabase.com/docs/guides/cli)
- [Environment Variables](https://supabase.com/docs/guides/functions/secrets)

## 🆘 获取帮助

如果遇到问题，请：

1. 查看边缘函数日志
2. 检查环境变量配置
3. 验证API密钥有效性
4. 参考故障排查部分 