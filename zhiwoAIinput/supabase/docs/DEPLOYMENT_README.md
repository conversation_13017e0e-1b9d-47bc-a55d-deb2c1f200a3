# 知我AI输入法 - 边缘函数部署指南

## 🎯 快速开始

### 一键部署脚本（推荐）
使用统一部署脚本，支持本地测试和云端部署：

```bash
# 本地测试统一代理函数
./deploy-unified.sh local-test unified-proxy

# 本地公网测试（使用 ngrok）
./deploy-unified.sh local-public unified-proxy 8001

# 部署到云端
./deploy-unified.sh cloud
```

## 📁 项目结构

```
supabase/
├── functions/                    # 边缘函数
│   ├── revenuecat-webhook/      # RevenueCat 支付回调
│   ├── revenuecat-check/        # 订阅状态检查
│   └── unified-proxy/           # 统一代理服务 (AI + 通用代理)
├── migrations/                  # 数据库迁移文件
├── deploy-unified.sh            # 统一部署脚本 (推荐)
├── deploy-cloud.sh              # 云端部署脚本
├── direct-run.sh                # 本地直接运行
└── direct-run-public.sh         # 本地公网运行
```

## 🔧 环境配置

### 必需的环境变量

```bash
# .env 文件
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# 云端部署
PROJECT_ID=your_project_id
SUPABASE_ACCESS_TOKEN=your_access_token

# AI 代理服务
SILICONFLOW_API_KEY=your_siliconflow_key     # 必需
OPENAI_API_KEY=your_openai_key               # 可选
ANTHROPIC_API_KEY=your_anthropic_key         # 可选
ELEVENLABS_API_KEY=your_elevenlabs_key       # 可选

# RevenueCat
REVENUECAT_WEBHOOK_SECRET=your_webhook_secret
CHECK_API_SECRET=your_api_secret
```

## 🚀 部署方式

### 方式一：统一部署脚本（推荐）

```bash
# 设置执行权限
chmod +x deploy-unified.sh

# 本地测试
./deploy-unified.sh local-test unified-proxy

# 云端部署到 staging 环境
./deploy-unified.sh cloud

# 云端部署到生产环境
./deploy-unified.sh cloud -e production -f
```

### 方式二：独立脚本

```bash
# 本地开发测试
./direct-run.sh unified-proxy

# 本地公网测试
./direct-run-public.sh unified-proxy 8001

# 云端部署
./deploy-cloud.sh
```

## 📋 功能说明

### unified-proxy 统一代理服务

**功能特性：**
- 🤖 **AI 聊天代理**：支持 SiliconFlow、OpenAI、Anthropic
- 🌐 **通用 HTTP 代理**：绕过网络限制访问各种 API
- 🔒 **安全控制**：用户认证、VIP 权限、速率限制
- 📊 **使用统计**：详细的请求日志和成本分析
- 🔑 **密钥管理**：服务端安全存储 API 密钥

**API 接口：**

1. **AI 聊天模式**
```bash
curl -X POST https://your-project.supabase.co/functions/v1/unified-proxy \
  -H "Authorization: Bearer $USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://api.siliconflow.cn/v1/chat/completions",
    "method": "POST",
    "body": {
      "model": "deepseek-chat",
      "messages": [{"role": "user", "content": "Hello"}]
    }
  }'
```

2. **通用代理模式**
```bash
curl -X POST https://your-project.supabase.co/functions/v1/unified-proxy \
  -H "Authorization: Bearer $USER_TOKEN" \
  -H "x-target-url: https://api.example.com/endpoint" \
  -H "Content-Type: application/json" \
  -d '{"data": "your data"}'
```

### revenuecat-webhook
处理 RevenueCat 支付回调，自动更新用户订阅状态

### revenuecat-check  
提供订阅状态查询 API，支持客户端主动检查

## 🔍 本地测试

### 测试统一代理函数

```bash
# 启动本地服务
./deploy-unified.sh local-test unified-proxy

# 测试 AI 聊天（新终端）
curl -X POST http://localhost:54321/functions/v1/unified-proxy \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://api.siliconflow.cn/v1/chat/completions",
    "method": "POST",
    "body": {
      "model": "deepseek-chat", 
      "messages": [{"role": "user", "content": "你好"}]
    }
  }'

# 测试通用代理
curl -X GET http://localhost:54321/functions/v1/unified-proxy \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "x-target-url: https://httpbin.org/json"
```

## 🔒 安全注意事项

1. **API 密钥管理**
   - 所有 API 密钥存储在 Supabase Secrets 中
   - 客户端无需直接处理敏感密钥
   - 支持密钥轮换和管理

2. **用户认证**
   - 所有请求需要有效的用户 JWT Token
   - 支持 VIP 用户权限分级
   - 实现速率限制防止滥用

3. **网络安全**
   - URL 白名单验证
   - 请求大小限制
   - 错误信息脱敏

## 📊 监控和日志

- **数据库表**：`unified_proxy_logs` 记录所有请求
- **字段包含**：用户ID、请求URL、响应状态、处理时间、成本估算
- **可视化**：可在 Supabase Dashboard 中查看使用统计

## 🆘 故障排除

### 常见问题

1. **函数部署失败**
   ```bash
   # 检查函数语法
   deno check functions/unified-proxy/index.ts
   
   # 重新部署
   ./deploy-unified.sh cloud -f
   ```

2. **API 密钥未生效**
   ```bash
   # 检查 Secrets 配置
   supabase secrets list
   
   # 重新设置密钥
   supabase secrets set SILICONFLOW_API_KEY=your_key
   ```

3. **权限错误**
   - 确保用户已认证且 JWT Token 有效
   - 检查 VIP 状态和权限配置

4. **网络问题**
   - 检查目标 URL 是否在白名单中
   - 验证网络连接和防火墙设置

## 📞 支持

如遇问题，请检查：
1. 环境变量配置是否正确
2. 数据库迁移是否执行成功  
3. 函数部署状态和日志
4. 用户认证和权限设置 