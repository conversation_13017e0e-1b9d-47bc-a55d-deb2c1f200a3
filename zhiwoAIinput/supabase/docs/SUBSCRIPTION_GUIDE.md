# 知我AI输入法 订阅系统指南

本文档详细介绍了知我AI输入法应用中的订阅系统实现，包括数据库设计、RevenueCat集成以及订阅管理。

## 数据库设计

### 主要表结构

1. **subscription_plans** - 订阅计划定义
   ```sql
   CREATE TABLE IF NOT EXISTS public.subscription_plans (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     plan_id TEXT UNIQUE NOT NULL,
     name TEXT NOT NULL,
     description TEXT,
     price_usd DECIMAL(10, 2),
     duration_days INTEGER,
     features JSONB DEFAULT '{}'::jsonb,
     is_active BOOLEAN DEFAULT true,
     sort_order INTEGER DEFAULT 0,
     apple_product_id TEXT,
     google_product_id TEXT,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
   );
   ```

2. **subscriptions** - 用户订阅记录
   ```sql
   CREATE TABLE IF NOT EXISTS public.subscriptions (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
     product_id TEXT NOT NULL,
     status TEXT NOT NULL,
     environment TEXT,
     store TEXT NOT NULL,
     transaction_id TEXT UNIQUE,
     original_transaction_id TEXT,
     expires_at TIMESTAMP WITH TIME ZONE,
     purchase_date TIMESTAMP WITH TIME ZONE,
     webhook_received_at TIMESTAMP WITH TIME ZONE,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
     auto_renew_status BOOLEAN DEFAULT true,
     cancellation_reason TEXT,
     event_type TEXT,
     last_event_at TIMESTAMP WITH TIME ZONE,
     
     -- 试用期相关字段
     is_trial BOOLEAN DEFAULT false,
     trial_start_at TIMESTAMP WITH TIME ZONE,
     trial_end_at TIMESTAMP WITH TIME ZONE,
     trial_converted BOOLEAN DEFAULT false,
     trial_converted_at TIMESTAMP WITH TIME ZONE,
     trial_type TEXT,
     trial_duration_days INTEGER,
     
     -- 产品变更相关字段
     previous_product_id TEXT,
     product_changed_at TIMESTAMP WITH TIME ZONE,
     product_change_from TEXT,
     product_change_type public.product_change_type,
     upgrade_from_subscription_id UUID,
     
     -- 额外信息字段
     period_type TEXT,
     entitlements JSONB DEFAULT '[]'::jsonb,
     is_family_share BOOLEAN DEFAULT false,
     country_code TEXT,
     presented_offering_id TEXT,
     has_billing_issue BOOLEAN DEFAULT false,
     billing_issue_detected_at TIMESTAMP WITH TIME ZONE,
     is_trial_conversion BOOLEAN DEFAULT false,
     transfer_from_user_id UUID,
     transfer_from_subscription_id UUID,
     expiration_reason TEXT,
     expiration_reason_description TEXT,
     is_refunded BOOLEAN DEFAULT false,
     refunded_at TIMESTAMP WITH TIME ZONE,
     refund_amount DECIMAL(10, 2),
     refund_currency TEXT,
     converted_from_trial_id UUID,
     conversion_date TIMESTAMP WITH TIME ZONE
   );
   ```

3. **subscription_events** - 订阅事件记录
   ```sql
   CREATE TABLE IF NOT EXISTS public.subscription_events (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
     subscription_id UUID REFERENCES public.subscriptions(id) ON DELETE CASCADE,
     event_type TEXT NOT NULL,
     event_timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
     transaction_id TEXT,
     original_transaction_id TEXT,
     product_id TEXT,
     event_data JSONB DEFAULT '{}'::jsonb,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
   );
   ```

4. **profiles** - 用户档案（包含VIP状态）
   ```sql
   CREATE TABLE IF NOT EXISTS public.profiles (
     id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
     username TEXT UNIQUE,
     email TEXT UNIQUE,
     avatar_url TEXT,
     is_vip BOOLEAN DEFAULT false,
     vip_product_id TEXT,
     vip_expires_at TIMESTAMP WITH TIME ZONE,
     vip_environment TEXT,
     vip_updated_at TIMESTAMP WITH TIME ZONE,
     vip_is_trial BOOLEAN DEFAULT false,
     vip_source TEXT,
     custom_templates_count INTEGER DEFAULT 0,
     voice_minutes_used INTEGER DEFAULT 0,
     voice_minutes_limit INTEGER DEFAULT 0,
     api_key_type TEXT DEFAULT 'free',
     created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
   );
   ```

5. **temporary_entitlements** - 临时授权记录表
   ```sql
   CREATE TABLE IF NOT EXISTS public.temporary_entitlements (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
     entitlement_id TEXT,
     grant_id TEXT,
     status TEXT DEFAULT 'active',  -- active, expired, revoked, validated
     granted_at TIMESTAMP WITH TIME ZONE,
     expires_at TIMESTAMP WITH TIME ZONE,
     revoked_at TIMESTAMP WITH TIME ZONE,
     reason TEXT,
     source TEXT DEFAULT 'revenuecat',
     metadata JSONB,
     is_validated BOOLEAN DEFAULT false,
     validated_at TIMESTAMP WITH TIME ZONE,
     validation_subscription_id UUID REFERENCES public.subscriptions(id),
     transaction_id TEXT,
     product_id TEXT,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
   );
   ```

6. **subscription_extension_history** - 订阅延期历史
   ```sql
   CREATE TABLE IF NOT EXISTS public.subscription_extension_history (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
     subscription_id UUID NOT NULL REFERENCES public.subscriptions(id) ON DELETE CASCADE,
     original_expiration_date TIMESTAMP WITH TIME ZONE NOT NULL,
     new_expiration_date TIMESTAMP WITH TIME ZONE NOT NULL,
     extension_days INTEGER NOT NULL,
     reason TEXT,
     details JSONB,
     created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
     updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
   );
   ```

7. **invoices** - 发票记录表
   ```sql
   CREATE TABLE IF NOT EXISTS public.invoices (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
     product_id TEXT NOT NULL,
     invoice_id TEXT,  -- RevenueCat内部发票ID
     status TEXT DEFAULT 'issued',  -- issued, paid, cancelled, failed
     amount DECIMAL(10, 2),
     currency TEXT,
     issued_at TIMESTAMP WITH TIME ZONE,
     paid_at TIMESTAMP WITH TIME ZONE,
     environment TEXT,
     store TEXT,
     country_code TEXT,
     subscription_id UUID REFERENCES public.subscriptions(id),
     metadata JSONB,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
   );
   ```

8. **billing_issues** - 账单问题记录表
   ```sql
   CREATE TABLE IF NOT EXISTS public.billing_issues (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
     subscription_id UUID REFERENCES public.subscriptions(id) ON DELETE CASCADE,
     transaction_id TEXT,
     detected_at TIMESTAMP WITH TIME ZONE NOT NULL,
     resolved BOOLEAN DEFAULT false,
     resolution_date TIMESTAMP WITH TIME ZONE,
     details JSONB DEFAULT '{}'::jsonb,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
   );
   ```

9. **payments** - 支付记录表
   ```sql
   CREATE TABLE IF NOT EXISTS public.payments (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
     subscription_id UUID REFERENCES public.subscriptions(id) ON DELETE CASCADE,
     amount DECIMAL(10, 2) NOT NULL,
     currency TEXT NOT NULL,
     status TEXT NOT NULL,
     payment_method TEXT,
     payment_provider TEXT NOT NULL,
     payment_provider_payment_id TEXT,
     description TEXT,
     metadata JSONB DEFAULT '{}'::jsonb,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
   );
   ```

### 重要索引

以下索引已配置以提高查询性能：

```sql
CREATE INDEX IF NOT EXISTS idx_profiles_is_vip ON public.profiles(is_vip) WHERE is_vip = true;
CREATE INDEX IF NOT EXISTS idx_subscriptions_product_id ON public.subscriptions(product_id);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_apple_product_id ON public.subscription_plans(apple_product_id) WHERE apple_product_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_subscription_plans_google_product_id ON public.subscription_plans(google_product_id) WHERE google_product_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS subscriptions_user_id_idx ON public.subscriptions(user_id);
CREATE INDEX IF NOT EXISTS subscriptions_status_idx ON public.subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_transaction_id ON public.subscriptions(transaction_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_original_transaction_id ON public.subscriptions(original_transaction_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_expires_at ON public.subscriptions(expires_at);
CREATE INDEX IF NOT EXISTS subscription_events_user_id_idx ON public.subscription_events(user_id);
CREATE INDEX IF NOT EXISTS subscription_events_event_type_idx ON public.subscription_events(event_type);
```

## RevenueCat集成

### 产品ID映射

为确保系统正常运行，RevenueCat中的产品ID必须与数据库中的配置一致：

| 计划ID | 描述 | Apple产品ID | Google产品ID |
|-------|-----|------------|-------------|
| `monthly_subscription` | 月度VIP | `monthly_subscription` | `monthly_subscription` |
| `annual_subscription` | 年度VIP | `annual_subscription` | `annual_subscription` |
| `trial_subscription` | 试用期 | `trial_subscription` | `trial_subscription` |

### Webhook处理流程

1. RevenueCat事件通过Webhook发送到Supabase Edge Function (`revenuecat-webhook`)
2. Edge Function验证请求签名并解析事件数据
3. 通过`ensureUserExists`函数确保用户在数据库中存在
4. 根据事件类型调用相应处理函数:
   - `handleSubscriptionActive`: 处理新购买、续订和产品变更
   - `handleSubscriptionInactive`: 处理取消和过期
   - `handleBillingIssue`: 处理计费问题
   - `handleUncancellation`: 处理恢复订阅
   - `handleInvoiceIssuance`: 处理发票开具
   - `handleTemporaryEntitlementGrant`: 处理临时授权

5. 每个事件都会:
   - 更新相应表中的记录
   - 添加事件记录到`subscription_events`表
   - 触发必要的状态更新函数

### VIP状态更新

当订阅状态改变时，`update_user_vip_status`触发器会:

1. 检查用户是否有活跃订阅
2. 从订阅计划中获取相关特性（语音分钟数、API密钥类型等）
3. 更新`profiles`表中的VIP相关字段

```sql
CREATE OR REPLACE FUNCTION public.update_user_vip_status()
RETURNS TRIGGER AS $$
DECLARE
  user_has_active_sub BOOLEAN;
  v_features JSONB;
  v_voice_minutes INTEGER;
  v_api_key_type TEXT;
  v_custom_templates_limit INTEGER;
BEGIN
  -- 检查用户是否有活跃订阅
  SELECT EXISTS (
    SELECT 1 FROM public.subscriptions 
    WHERE user_id = NEW.user_id 
    AND status = 'active' 
    AND (expires_at IS NULL OR expires_at > now())
  ) INTO user_has_active_sub;
  
  -- 获取订阅计划特性并更新用户状态
  IF user_has_active_sub THEN
    -- 设置用户为VIP
  ELSE
    -- 取消用户VIP状态
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## 高级功能

### 1. 临时授权系统

临时授权系统允许在特殊情况下（如商店服务中断）为用户临时授予VIP权益。

#### 工作流程

1. 当检测到商店服务中断时，系统会创建临时授权记录
2. 临时授权通常有效期为特定时长（例如24小时）
3. 一旦商店服务恢复，临时授权可以被验证并转为正式订阅
4. 如果验证失败，临时授权会被撤销

#### 主要函数

- `handle_temporary_entitlement` - 创建或更新临时授权
- `revoke_temporary_entitlement` - 撤销临时授权
- `extend_temporary_entitlement` - 延长临时授权有效期
- `validate_temporary_entitlement` - 验证临时授权

#### 使用示例

```typescript
// 授予临时权益
async function grantTemporaryEntitlement(userId, entitlementId, expiresInHours = 24) {
  const grantId = uuidv4();
  const granted_at = new Date();
  const expires_at = new Date(granted_at.getTime() + expiresInHours * 60 * 60 * 1000);
  
  await supabase.rpc('handle_temporary_entitlement', {
    p_user_id: userId,
    p_entitlement_id: entitlementId,
    p_grant_id: grantId,
    p_granted_at: granted_at.toISOString(),
    p_expires_at: expires_at.toISOString(),
    p_reason: "app_compensation",
    p_metadata: { source: "customer_service" }
  });
}

// 撤销临时权益
async function revokeTemporaryEntitlement(grantId) {
  await supabase.rpc('revoke_temporary_entitlement', {
    p_grant_id: grantId,
    p_reason: "policy_violation"
  });
}
```

### 2. 试用期管理

试用期功能允许用户在付费前免费体验VIP服务。

#### 工作流程

1. 用户开始试用时，创建一个`is_trial=true`的订阅记录
2. 试用记录包含试用开始和结束时间
3. 用户转为付费用户时，将`trial_converted`标记为true，并创建新的付费订阅
4. 试用转化分析视图提供试用转付费数据

#### 主要函数

- `start_subscription_trial` - 开始试用期
- `convert_trial_to_paid` - 试用期转为付费

#### 使用示例

```typescript
// 开始试用期
async function startTrial(userId, productId, durationDays = 7) {
  const trialEndDate = new Date();
  trialEndDate.setDate(trialEndDate.getDate() + durationDays);
  
  await supabase.rpc('start_subscription_trial', {
    p_user_id: userId,
    p_product_id: productId,
    p_trial_end_date: trialEndDate.toISOString(),
    p_store: 'APP_STORE'
  });
}

// 转换试用为付费
async function convertTrialToPaid(trialSubscriptionId, newExpirationDate) {
  await supabase.rpc('convert_trial_to_paid', {
    p_trial_subscription_id: trialSubscriptionId,
    p_new_expiration_date: newExpirationDate.toISOString()
  });
}
```

### 3. 订阅延期系统

订阅延期系统允许延长用户的订阅有效期，例如作为补偿或促销活动。

#### 工作流程

1. 当需要延长订阅时，记录原始到期日和新到期日
2. 系统计算延长天数并更新用户订阅到期时间
3. 所有延期历史记录保存在`subscription_extension_history`表中

#### 使用示例

```typescript
// 延长用户订阅
async function extendSubscription(userId, subscriptionId, extensionDays, reason) {
  // 查询当前订阅到期时间
  const { data: subscription } = await supabase
    .from('subscriptions')
    .select('expires_at')
    .eq('id', subscriptionId)
    .single();
    
  if (!subscription) return false;
  
  const originalDate = new Date(subscription.expires_at);
  const newDate = new Date(originalDate.getTime() + extensionDays * 86400000);
  
  // 记录延期历史
  await supabase.from('subscription_extension_history').insert({
    user_id: userId,
    subscription_id: subscriptionId,
    original_expiration_date: originalDate.toISOString(),
    new_expiration_date: newDate.toISOString(),
    extension_days: extensionDays,
    reason: reason
  });
  
  // 更新订阅到期时间
  await supabase
    .from('subscriptions')
    .update({ expires_at: newDate.toISOString() })
    .eq('id', subscriptionId);
    
  return true;
}
```

### 4. 发票系统

发票系统记录所有订阅和支付的发票信息。

#### 工作流程

1. 当新订阅或续订事件发生时，系统创建发票记录
2. 支付成功时，发票状态更新为"已支付"
3. 取消或失败时，状态相应更新
4. 发票分析视图提供转化率和收入数据

#### 主要函数

- `record_invoice` - 记录新发票
- `update_invoice_to_paid` - 更新发票为已支付
- `update_invoice_to_cancelled` - 更新发票为已取消
- `update_invoice_to_failed` - 更新发票为支付失败

## 订阅计划配置

系统中定义了以下订阅计划:

1. **免费版**
   - 特性: 最多1个自定义模板，基础AI模型，无历史同步，0语音分钟
   - API密钥类型: free

2. **月度VIP**
   - 价格: $9.9/月
   - 期限: 30天
   - 特性: 无限自定义模板，高级AI模型，历史同步，300语音分钟/月
   - API密钥类型: premium

3. **年度VIP**
   - 价格: $99.99/年
   - 期限: 365天
   - 特性: 无限自定义模板，高级AI模型，历史同步，3600语音分钟/年
   - API密钥类型: premium

4. **试用期**
   - 价格: 免费
   - 期限: 7天
   - 特性: 与月度VIP相同，但有时间限制
   - API密钥类型: premium

## 测试计划

为了确保订阅系统各个组件的正确性，以下是一个全面的测试计划：

### 准备工作

1. **环境设置**
   - 确认RevenueCat项目配置正确
   - 确认Supabase Edge Function部署成功
   - 验证环境变量（特别是`REVENUECAT_WEBHOOK_SECRET`）已设置

2. **测试账号准备**
   - 准备至少3个测试用户账号
   - 在RevenueCat后台配置这些测试账号
   - 确保设备处于sandbox测试模式

### 核心功能测试

1. **首次购买测试**
   ```
   目标: 验证用户首次订阅时VIP状态正确设置
   步骤:
   1. 使用测试账号购买月度订阅
   2. 检查webhook响应状态
   3. 验证数据库中用户VIP状态为true
   4. 确认subscriptions表中创建了相应记录
   ```

2. **订阅续订测试**
   ```
   目标: 验证续订时到期日正确更新
   步骤:
   1. 使用RevenueCat测试工具触发续订事件
   2. 检查数据库中的expires_at是否更新
   3. 确认用户VIP状态保持为true
   ```

3. **订阅变更测试**
   ```
   目标: 验证套餐变更时产品ID正确更新
   步骤:
   1. 将测试账号从月度套餐升级到年度套餐
   2. 检查webhook响应状态
   3. 验证数据库中产品ID已更新
   4. 确认用户权益已相应调整
   ```

### 高级功能测试

1. **试用期转付费测试**
   ```
   目标: 验证试用期正确转换为付费订阅
   步骤:
   1. 开始用户试用期
   2. 验证用户获得临时VIP权益
   3. 触发试用转付费事件
   4. 验证新订阅正确创建且关联到试用记录
   5. 确认trial_conversion_analysis视图正确显示转化
   ```

2. **临时授权测试**
   ```
   目标: 验证临时授权功能正常工作
   步骤:
   1. 创建临时授权记录
   2. 验证用户获得临时VIP权益
   3. 测试延长临时授权
   4. 测试撤销临时授权
   5. 测试验证临时授权并转为正式订阅
   ```

3. **发票系统测试**
   ```
   目标: 验证发票记录和状态更新
   步骤:
   1. 触发发票创建事件
   2. 验证发票状态为"已开具"
   3. 触发支付成功事件
   4. 验证发票状态更新为"已支付"
   5. 检查invoice_analysis视图数据
   ```

4. **订阅延期测试**
   ```
   目标: 验证订阅延期功能
   步骤:
   1. 选择一个活跃订阅
   2. 延长订阅30天
   3. 验证新的到期日正确设置
   4. 检查subscription_extension_history表记录
   5. 检查user_subscription_extensions视图显示
   ```

## 常见问题排查

### Webhook问题

1. **Webhook未触发**
   - 检查RevenueCat控制台中webhook配置
   - 确认URL格式正确: `https://[YOUR_PROJECT_REF].supabase.co/functions/v1/revenuecat-webhook`
   - 验证webhook密钥已正确设置
   - 检查函数部署状态和日志

2. **Webhook触发但处理失败**
   - 检查Supabase Function日志
   - 验证环境变量配置
   - 检查数据库连接和表结构

### 临时授权问题

1. **临时授权未正确授予**
   - 检查`handle_temporary_entitlement`函数是否正常工作
   - 验证grant_id是否唯一
   - 检查用户ID格式是否正确

2. **临时授权验证失败**
   - 检查transaction_id是否一致
   - 验证`validate_temporary_entitlement`函数参数

### 试用期问题

1. **试用期未正确启动**
   - 检查`start_subscription_trial`函数
   - 验证用户ID是否有效
   - 确认trial_end_date格式是否正确

2. **试用转付费失败**
   - 检查`convert_trial_to_paid`函数
   - 验证trial_subscription_id是否正确
   - 确认试用记录状态为active

### 调试查询

以下是一些有用的调试查询:

```sql
-- 检查用户VIP状态
SELECT id, is_vip, vip_expires_at, vip_product_id, vip_is_trial, vip_source 
FROM profiles WHERE id = '[USER_ID]';

-- 检查用户订阅记录
SELECT * FROM subscriptions 
WHERE user_id = '[USER_ID]' 
ORDER BY created_at DESC;

-- 检查用户的临时授权
SELECT * FROM temporary_entitlements 
WHERE user_id = '[USER_ID]' 
ORDER BY granted_at DESC;

-- 检查用户的发票记录
SELECT * FROM invoices 
WHERE user_id = '[USER_ID]' 
ORDER BY issued_at DESC;

-- 查看试用转付费情况
SELECT * FROM trial_conversion_analysis 
WHERE user_id = '[USER_ID]';

-- 检查用户的订阅延期记录
SELECT * FROM subscription_extension_history 
WHERE user_id = '[USER_ID]' 
ORDER BY created_at DESC;

-- 检查触发器是否正常工作
SELECT event_id, trigger_name, trigger_schema, trigger_table, action_statement 
FROM information_schema.triggers 
WHERE event_object_table IN ('subscriptions', 'temporary_entitlements');
```

## 部署指南

使用`deploy-cloud.sh`脚本将本地Supabase配置部署到云端:

```bash
chmod +x deploy-cloud.sh
./deploy-cloud.sh
```

该脚本将:
1. 部署数据库架构
2. 执行种子数据脚本
3. 部署RevenueCat Webhook函数
4. 设置必要的环境变量

部署后，在RevenueCat开发者控制台中配置Webhook URL和密钥。 