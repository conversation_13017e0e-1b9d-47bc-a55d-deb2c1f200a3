# 孤立订阅事件处理指南

## 概述

本文档说明了如何处理RevenueCat webhook中没有对应用户记录的订阅事件。这种情况通常发生在数据库重启、用户数据清理或其他异常情况下。

## 问题背景

当数据库重启或用户数据被清空后，RevenueCat仍然会发送订阅事件，但系统中找不到对应的用户记录。之前的处理方式是自动创建匿名用户记录，这会导致：

1. 创建虚假的用户数据
2. 用户数据来源不一致
3. 数据库中出现无效的用户记录

## 解决方案

通过数据库迁移 `20250719000000_allow_orphaned_subscription_events.sql`，我们实现了以下功能：

### 1. 表结构修改

- **subscription_events表**：
  - 添加 `app_user_id` 字段存储原始RevenueCat用户ID
  - `user_id` 字段改为可空
  
- **subscriptions表**：
  - 添加 `app_user_id` 字段存储原始RevenueCat用户ID
  - `user_id` 字段改为可空

### 2. 新增函数

#### `check_user_exists(p_user_id TEXT)`
检查用户是否存在，不自动创建用户记录。

```sql
SELECT public.check_user_exists('6aaa2bc0-a24b-4bd8-92eb-081bb3fec46b');
```

#### `record_orphaned_subscription_event(...)`
记录孤立的订阅事件（无对应用户记录）。

```sql
SELECT public.record_orphaned_subscription_event(
  '6aaa2bc0-a24b-4bd8-92eb-081bb3fec46b',
  'RENEWAL',
  '2025-07-17 10:00:00+00',
  '2000000963574150',
  '2000000951989701',
  'annual_subscription',
  '{"price": 96, "currency": "USD"}'::jsonb
);
```

#### `link_orphaned_records_to_user(p_app_user_id TEXT, p_user_id UUID)`
将孤立记录关联到现有用户。

```sql
SELECT public.link_orphaned_records_to_user(
  '6aaa2bc0-a24b-4bd8-92eb-081bb3fec46b',
  'real-user-uuid-here'
);
```

### 3. 视图和统计

#### `orphaned_subscription_events` 视图
显示所有孤立的订阅事件及可能的用户匹配。

```sql
SELECT * FROM public.orphaned_subscription_events;
```

#### `orphaned_records_stats` 视图
显示孤立记录的统计信息。

```sql
SELECT * FROM public.orphaned_records_stats;
```

### 4. 智能匹配功能

#### `suggest_user_matches_for_orphaned_records()`
基于各种条件建议可能的用户匹配。

```sql
SELECT * FROM public.suggest_user_matches_for_orphaned_records();
```

#### `auto_link_high_confidence_matches(p_min_confidence_score INTEGER)`
自动关联高置信度的匹配。

```sql
SELECT * FROM public.auto_link_high_confidence_matches(95);
```

## 边缘函数修改建议

在RevenueCat webhook处理函数中，建议采用以下逻辑：

```typescript
// 检查用户是否存在（不自动创建）
const { data: existingUserId } = await supabase.rpc('check_user_exists', {
  p_user_id: app_user_id
});

if (existingUserId) {
  // 用户存在，正常处理
  await ensureUserExists(app_user_id, userEmail);
  // ... 正常的订阅处理逻辑
} else {
  // 用户不存在，只记录事件
  console.log(`用户 ${app_user_id} 不存在，记录孤立事件`);
  
  await supabase.rpc('record_orphaned_subscription_event', {
    p_app_user_id: app_user_id,
    p_event_type: eventData.type,
    p_event_timestamp: new Date(eventData.event_timestamp_ms).toISOString(),
    p_transaction_id: eventData.transaction_id,
    p_original_transaction_id: eventData.original_transaction_id,
    p_product_id: eventData.product_id,
    p_event_data: eventData
  });
  
  // 不创建用户记录，不处理订阅状态
  return;
}
```

## 运维操作

### 查看孤立记录

```sql
-- 查看所有孤立的订阅事件
SELECT * FROM public.orphaned_subscription_events;

-- 查看孤立记录统计
SELECT * FROM public.orphaned_records_stats;
```

### 手动关联记录

```sql
-- 将特定app_user_id的孤立记录关联到用户
SELECT public.link_orphaned_records_to_user(
  '6aaa2bc0-a24b-4bd8-92eb-081bb3fec46b',
  'real-user-uuid-here'
);
```

### 批量处理高置信度匹配

```sql
-- 自动处理置信度95%以上的匹配
SELECT * FROM public.auto_link_high_confidence_matches(95);
```

### 查看匹配建议

```sql
-- 查看所有匹配建议
SELECT * FROM public.suggest_user_matches_for_orphaned_records()
ORDER BY confidence_score DESC;
```

## 数据完整性保证

1. **约束检查**：确保每条记录至少有一个用户标识（user_id或app_user_id）
2. **索引优化**：为app_user_id字段创建索引，提高查询性能
3. **RLS策略**：更新行级安全策略，支持孤立记录的访问控制

## 监控和告警

建议设置监控来跟踪孤立记录的数量：

```sql
-- 监控查询：检查孤立记录数量
SELECT 
  table_name,
  orphaned_count,
  unique_app_users
FROM public.orphaned_records_stats;
```

如果孤立记录数量异常增长，可能表示：
1. 用户数据被意外删除
2. RevenueCat配置问题
3. 系统集成问题

## 最佳实践

1. **定期检查**：定期查看孤立记录统计，及时发现问题
2. **匹配处理**：定期运行匹配建议，处理可以关联的记录
3. **数据备份**：在进行批量关联操作前，确保数据已备份
4. **日志记录**：在边缘函数中记录详细日志，便于问题排查

## 注意事项

1. 孤立记录不会影响正常的订阅功能
2. 用户重新注册后，可以通过匹配功能关联历史记录
3. 孤立记录保留了完整的事件信息，不会丢失数据
4. 系统保证用户数据创建来源的唯一性
