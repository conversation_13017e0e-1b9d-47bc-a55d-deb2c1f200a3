#!/bin/bash
# 知我AI输入法 - 增强版Supabase云端部署脚本
# 版本: 2.0
# 支持多环境、备份、回滚功能

set -euo pipefail  # 严格错误处理

# 颜色定义
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly RED='\033[0;31m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# 全局变量
ENVIRONMENT=${ENVIRONMENT:-staging}
BACKUP_ENABLED=${BACKUP_ENABLED:-true}
DRY_RUN=${DRY_RUN:-false}
FORCE_DEPLOY=${FORCE_DEPLOY:-false}

# 打印函数
info() { echo -e "${GREEN}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
debug() { echo -e "${BLUE}[DEBUG]${NC} $1"; }

# 显示使用帮助
show_help() {
    cat << EOF
知我AI输入法 - Supabase云端部署脚本 v2.0

用法:
    $0 [选项]

选项:
    -e, --environment ENV    部署环境 (staging/production) [默认: staging]
    -d, --dry-run           预演模式，不执行实际部署
    -f, --force             强制部署，跳过确认
    -b, --no-backup         跳过备份步骤
    -h, --help              显示此帮助信息

示例:
    $0                           # 部署到staging环境
    $0 -e production             # 部署到生产环境
    $0 -d                        # 预演模式
    $0 -e production -f          # 强制部署到生产环境

环境变量:
    PROJECT_ID                   Supabase项目ID
    SUPABASE_ACCESS_TOKEN        Supabase访问令牌
    REVENUECAT_WEBHOOK_SECRET    RevenueCat Webhook密钥
    CHECK_API_SECRET             订阅检查API密钥
    
    AI服务相关:
    SILICONFLOW_API_KEY          SiliconFlow API密钥 (必需)
    OPENAI_API_KEY               OpenAI API密钥 (可选)
    ANTHROPIC_API_KEY            Anthropic API密钥 (可选)
    ELEVENLABS_API_KEY           ElevenLabs API密钥 (可选)

部署的Edge函数:
    - revenuecat-webhook         RevenueCat支付回调处理
    - revenuecat-check          订阅状态检查
    - unified-proxy             通用代理 (仅VIP用户)
EOF
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -f|--force)
                FORCE_DEPLOY=true
                shift
                ;;
            -b|--no-backup)
                BACKUP_ENABLED=false
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查前置条件
check_prerequisites() {
    info "检查前置条件..."
    
    # 检查supabase CLI
    if ! command -v supabase &> /dev/null; then
        error "Supabase CLI未安装，请安装: npm install -g supabase"
        exit 1
    fi
    
    # 检查是否登录
    if ! supabase projects list &> /dev/null; then
        error "请先登录Supabase CLI: supabase login"
        exit 1
    fi
    
    # 检查环境文件
    local env_file="../.env.${ENVIRONMENT}"
    if [ ! -f "$env_file" ]; then
        error "$env_file 文件不存在，请创建并配置必要的环境变量"
        exit 1
    fi
    
    # 加载环境变量
    set -o allexport
    source "$env_file"
    set +o allexport
    
    info "✅ 已加载环境配置: $env_file"
    
    # 检查必要的环境变量
    if [ -z "${PROJECT_ID:-}" ]; then
        error "PROJECT_ID环境变量未设置"
        exit 1
    fi
    
    info "✅ 前置条件检查完成"
}

# 创建备份
create_backup() {
    if [ "$BACKUP_ENABLED" != "true" ]; then
        warn "跳过备份步骤"
        return 0
    fi
    
    info "创建数据库备份..."
    
    local backup_dir="../backups"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="${backup_dir}/backup_${ENVIRONMENT}_${timestamp}.sql"
    
    mkdir -p "$backup_dir"
    
    if [ "$DRY_RUN" = "true" ]; then
        info "[DRY RUN] 将创建备份文件: $backup_file"
        return 0
    fi
    
    # 创建数据备份
    if supabase db dump --data-only > "$backup_file"; then
        info "✅ 备份已创建: $backup_file"
        echo "LAST_BACKUP=$backup_file" > "${backup_dir}/.last_backup"
    else
        warn "⚠️ 备份创建失败，但将继续部署"
    fi
}

# 验证数据库状态
validate_database() {
    info "验证数据库状态..."
    
    if [ "$DRY_RUN" = "true" ]; then
        info "[DRY RUN] 将验证数据库状态"
        return 0
    fi
    
    # 简化验证：基于迁移状态确认
    info "检查迁移状态..."
    if supabase migration list | grep -q "20250122000001"; then
        info "✅ 最新迁移已应用"
        info "✅ 数据库状态验证完成"
    else
        warn "⚠️ 部分迁移可能未应用，但继续执行"
    fi
}

# 部署数据库迁移
deploy_migrations() {
    info "部署数据库迁移..."
    
    if [ "$DRY_RUN" = "true" ]; then
        info "[DRY RUN] 将应用以下迁移:"
        supabase migration list --local
        return 0
    fi
    
    # 显示待应用的迁移
    info "待应用的迁移:"
    supabase migration list --local
    
    # 应用迁移
    if supabase db push; then
        info "✅ 数据库迁移部署成功"
    else
        error "数据库迁移部署失败"
        return 1
    fi
}

# 部署Edge函数
deploy_functions() {
    info "部署Edge函数..."
    
    if [ "$DRY_RUN" = "true" ]; then
        info "[DRY RUN] 将部署Edge函数"
        return 0
    fi
    
    local functions=("revenuecat-webhook" "revenuecat-check" "unified-proxy")
    
    for func in "${functions[@]}"; do
        if [ -d "functions/$func" ]; then
            info "部署函数: $func"
            if supabase functions deploy "$func"; then
                info "✅ 函数 $func 部署成功"
            else
                error "函数 $func 部署失败"
                return 1
            fi
        else
            warn "函数目录不存在: functions/$func"
        fi
    done
}

# 设置环境变量
set_secrets() {
    info "设置环境变量..."
    
    if [ "$DRY_RUN" = "true" ]; then
        info "[DRY RUN] 将设置环境变量"
        return 0
    fi
    
    # 设置RevenueCat Webhook密钥
    if [ -n "${REVENUECAT_WEBHOOK_SECRET:-}" ]; then
        supabase secrets set REVENUECAT_WEBHOOK_SECRET="$REVENUECAT_WEBHOOK_SECRET"
        info "✅ REVENUECAT_WEBHOOK_SECRET 设置完成"
    fi
    
    # 设置或生成订阅检查API密钥
    if [ -z "${CHECK_API_SECRET:-}" ]; then
        CHECK_API_SECRET=$(openssl rand -base64 32)
        local env_file="../.env.${ENVIRONMENT}"
        echo "CHECK_API_SECRET=\"$CHECK_API_SECRET\"" >> "$env_file"
        info "✅ 生成并设置 CHECK_API_SECRET 到 $env_file"
    fi
    
    supabase secrets set CHECK_API_SECRET="$CHECK_API_SECRET"
    info "✅ CHECK_API_SECRET 设置完成"
    
    # 设置AI相关的环境变量
    # 注意：AI密钥通常不在客户端配置文件中，而是直接在Supabase云端管理
    if [ -n "${SILICONFLOW_API_KEY:-}" ]; then
        supabase secrets set SILICONFLOW_API_KEY="$SILICONFLOW_API_KEY"
        info "✅ SILICONFLOW_API_KEY 设置完成"
    else
        info "ℹ️ SILICONFLOW_API_KEY 未在本地配置文件中找到"
        info "   请确保已在Supabase Dashboard或通过CLI手动设置该密钥"
        info "   这是正常的，AI密钥应该直接在云端管理以确保安全"
    fi
    
    if [ -n "${OPENAI_API_KEY:-}" ]; then
        supabase secrets set OPENAI_API_KEY="$OPENAI_API_KEY"
        info "✅ OPENAI_API_KEY 设置完成"
    else
        warn "⚠️ OPENAI_API_KEY 未设置，将仅使用 SiliconFlow API"
    fi
    
    if [ -n "${ANTHROPIC_API_KEY:-}" ]; then
        supabase secrets set ANTHROPIC_API_KEY="$ANTHROPIC_API_KEY"
        info "✅ ANTHROPIC_API_KEY 设置完成"
    fi
    
    if [ -n "${ELEVENLABS_API_KEY:-}" ]; then
        supabase secrets set ELEVENLABS_API_KEY="$ELEVENLABS_API_KEY"
        info "✅ ELEVENLABS_API_KEY 设置完成"
    fi
}

# 执行种子数据
apply_seed_data() {
    info "应用种子数据..."
    
    if [ "$DRY_RUN" = "true" ]; then
        info "[DRY RUN] 将应用种子数据"
        return 0
    fi
    
    if [ -f "./seed.sql" ]; then
        if supabase db execute < ./seed.sql; then
            info "✅ 种子数据应用成功"
        else
            warn "⚠️ 种子数据应用可能有问题，但将继续"
        fi
    else
        warn "种子数据文件不存在: ./seed.sql"
    fi
}

# 部署后验证
post_deploy_validation() {
    info "执行部署后验证..."
    
    if [ "$DRY_RUN" = "true" ]; then
        info "[DRY RUN] 将执行部署后验证"
        return 0
    fi
    
    # 获取项目URL信息
    local project_url="https://supabase.com/dashboard/project/$PROJECT_ID/functions"
    
    info "✅ 函数部署完成，可以在Dashboard中查看:"
    info "   $project_url"
    
    # 简化验证：检查函数是否部署成功
    info "验证函数部署状态..."
    if supabase functions list --project-ref "$PROJECT_ID" 2>/dev/null | grep -q "revenuecat-webhook"; then
        info "✅ revenuecat-webhook 函数部署成功"
    else
        warn "⚠️ revenuecat-webhook 函数可能未部署成功"
    fi
    
    if supabase functions list --project-ref "$PROJECT_ID" 2>/dev/null | grep -q "revenuecat-check"; then
        info "✅ revenuecat-check 函数部署成功"
    else
        warn "⚠️ revenuecat-check 函数可能未部署成功"
    fi
    
    if supabase functions list --project-ref "$PROJECT_ID" 2>/dev/null | grep -q "unified-proxy"; then
        info "✅ unified-proxy 函数部署成功"
    else
        warn "⚠️ unified-proxy 函数可能未部署成功"
    fi
    
    # 验证迁移状态
    info "验证迁移状态..."
    if supabase migration list | grep -q "Local.*Remote"; then
        info "✅ 迁移状态正常"
        validate_database
        info "✅ 部署后验证通过"
    else
        warn "⚠️ 无法验证迁移状态，但部署已完成"
    fi
}

# 显示部署摘要
show_summary() {
    info "部署摘要:"
    echo "------------------------"
    echo "环境: $ENVIRONMENT"
    echo "项目ID: $PROJECT_ID"
    echo "DRY RUN: $DRY_RUN"
    echo "备份: $BACKUP_ENABLED"
    echo "------------------------"
    
    if [ "$DRY_RUN" != "true" ]; then
        local webhook_url=$(supabase functions get-url revenuecat-webhook 2>/dev/null || echo "未获取到")
        local check_url=$(supabase functions get-url revenuecat-check 2>/dev/null || echo "未获取到")
        local unified_proxy_url=$(supabase functions get-url unified-proxy 2>/dev/null || echo "未获取到")
        
        echo "RevenueCat Webhook URL: $webhook_url"
        echo "订阅检查API URL: $check_url"
        echo "通用代理API URL: $unified_proxy_url"
        echo "API密钥: ${CHECK_API_SECRET:-未设置}"
        echo ""
        echo "=== AI服务配置 ==="
        echo "SiliconFlow API: ${SILICONFLOW_API_KEY:+已配置}"
        echo "OpenAI API: ${OPENAI_API_KEY:+已配置}"
        echo "Anthropic API: ${ANTHROPIC_API_KEY:+已配置}"
        echo "ElevenLabs API: ${ELEVENLABS_API_KEY:+已配置}"
        echo ""
        echo "=== 代理服务功能 ==="
        echo "1. AI代理: 专门用于AI服务调用 (ChatGPT, Claude等)"
        echo "2. 通用代理: 可代理任意HTTP API请求 (仅VIP用户)"
        echo "3. 网络加速: 利用Supabase CDN绕过地域限制"
        echo "4. 安全控制: 用户认证、速率限制、使用监控"
        echo ""
        echo "=== 安全提示 ==="
        echo "1. API 密钥已安全存储在 Supabase Secrets 中"
        echo "2. 边缘函数会自动验证用户身份"
        echo "3. 速率限制已启用（免费用户: 10次/小时，VIP用户: 100次/小时）"
        echo "4. 所有 API 调用都会被记录和监控"
        echo "5. 通用代理功能仅对VIP用户开放，且有安全限制"
    fi
}

# 确认部署
confirm_deployment() {
    if [ "$FORCE_DEPLOY" = "true" ] || [ "$DRY_RUN" = "true" ]; then
        return 0
    fi
    
    echo
    warn "您即将部署到 $ENVIRONMENT 环境"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        info "部署已取消"
        exit 0
    fi
}

# 主函数
main() {
    parse_arguments "$@"
    
    info "开始部署到 $ENVIRONMENT 环境..."
    
    if [ "$DRY_RUN" = "true" ]; then
        warn "这是预演模式，不会执行实际操作"
    fi
    
    check_prerequisites
    confirm_deployment
    
    # 链接到项目
    info "链接到项目 $PROJECT_ID..."
    if [ "$DRY_RUN" != "true" ]; then
        supabase link --project-ref "$PROJECT_ID"
    fi
    
    # 执行部署步骤
    create_backup
    deploy_migrations
    apply_seed_data
    deploy_functions
    set_secrets
    post_deploy_validation
    
    show_summary
    
    if [ "$DRY_RUN" = "true" ]; then
        info "🎯 预演完成！使用 --force 参数执行实际部署"
    else
        info "🎉 部署完成！"
    fi
}

# 错误处理
trap 'error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@" 