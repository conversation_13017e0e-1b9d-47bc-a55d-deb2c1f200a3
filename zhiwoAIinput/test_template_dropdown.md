# 模板下拉菜单功能测试

## 功能说明

1. **下拉菜单宽度优化**
   - 菜单最小宽度：200px
   - 菜单最大宽度：280px
   - 比按钮宽度多100px，确保能显示更多模板文字信息

2. **历史记录逻辑修复**
   - **相同模板重新生成**：合并到一条记录，添加到optimizedResults数组
   - **不同模板重新生成**：生成新的独立记录

## 测试步骤

### 测试1：相同模板重新生成
1. 录制一段语音，选择"原文优化"模板
2. 点击结果页面右上角的模板下拉按钮
3. 再次选择"原文优化"模板
4. **预期结果**：应该合并到同一条历史记录，不产生新记录

### 测试2：不同模板重新生成
1. 录制一段语音，选择"原文优化"模板
2. 点击结果页面右上角的模板下拉按钮
3. 选择"朋友聊天"模板
4. **预期结果**：应该生成一条新的历史记录

### 测试3：下拉菜单显示
1. 进入结果页面
2. 点击右上角的模板名称（现在是按钮）
3. **预期结果**：
   - 显示下拉菜单
   - 菜单宽度足够显示模板名称
   - 显示前6个模板
   - 每个模板显示图标+名称
   - 当前选中的模板有选中标记

## 修改的文件

1. `components/input/TemplateDropdownMenu.tsx` - 新增下拉菜单组件
2. `app/(tabs)/index.tsx` - 集成下拉菜单，修改模板选择逻辑
3. `store/slices/recordingSlice.ts` - 修复历史记录保存逻辑
4. `services/historyService.ts` - 修复历史记录合并逻辑
5. `components/ui/IconSymbol.tsx` - 添加checkmark和chevron.down图标映射

## 关键逻辑修复

### 🔍 发现的根本问题
**实际使用的是 `newHistoryService`（SQLite），而不是 `historyService`（AsyncStorage）**
- `USE_SQLITE_SERVICE = true`，所以调用链是：
  1. `recordingSlice.ts` → `HistoryService.saveHistoryRecord`
  2. `storageService.ts` → `newHistoryService.saveHistoryRecord`
  3. **`newHistoryService` 重新应用了自己的合并逻辑，覆盖了我们的修改**

### 🔧 最终修复的问题
1. **重新生成按钮没有传递 `isSameTemplate` 参数** - ✅ 已修复
2. **首次优化和重新生成逻辑混乱** - ✅ 已修复
3. **钩子函数参数类型缺少 `isSameTemplate`** - ✅ 已修复
4. **🔥 storageService 返回记录缺少关键字段** - ✅ 已修复
5. **🔥 newHistoryService 重复应用合并逻辑** - ✅ 已修复

### 🎯 关键修复点
1. **`storageService.ts`**：添加缺失的 `sourceRecordId` 和 `optimizedResults` 字段
2. **`newHistoryService.ts`**：简化逻辑，让 `recordingSlice` 负责所有合并逻辑
3. **完全禁用 `newHistoryService` 的自动合并**：只负责保存，不重新处理

### 修复后的历史记录判断逻辑
```typescript
// 情况1：相同模板的重新生成 - 合并记录
if (isRegenerate && isSameTemplate) {
  // 查找相同audioUri、originalText和templateId的记录
  // 如果找到，添加到optimizedResults数组
  // 如果没找到，创建新记录
}

// 情况2：不同模板的重新生成 - 创建新记录
else if (isRegenerate && !isSameTemplate) {
  // 总是创建新记录
}

// 情况3：首次优化 - 按模板ID查找
else if (audioUri && !sourceRecordId && !isRegenerate) {
  // 查找相同audioUri、originalText和templateId的记录
  // 如果找到相同模板，合并
  // 如果没找到或模板不同，创建新记录
}

// 情况4：其他情况（二次优化、无音频等）
else {
  // 总是创建新记录
}
```

### newHistoryService.ts 的关键修复
```typescript
// 1. 添加模板ID检查
const existingIndex = records.findIndex(r =>
  r.audioUri === processedAudioUri &&
  r.templateId === record.templateId && // 🔥 新增：确保模板ID相同
  !r.sourceRecordId
);

// 2. 添加更新操作检测
const existingRecordById = records.find(r => r.id === record.id);
const isUpdateOperation = !!existingRecordById;

// 3. 只有在不是更新操作时才应用合并逻辑
if (processedAudioUri && !record.sourceRecordId && !isUpdateOperation) {
  // 应用合并逻辑
}
```

### 调用场景对应关系
1. **首次点击优化按钮**：`isRegenerate=undefined` → 走情况3（首次优化）
2. **点击重新生成按钮**：`isRegenerate=true, isSameTemplate=true` → 走情况1（合并记录）
3. **下拉菜单选择相同模板**：`isRegenerate=true, isSameTemplate=true` → 走情况1（合并记录）
4. **下拉菜单选择不同模板**：`isRegenerate=true, isSameTemplate=false` → 走情况2（新记录）
