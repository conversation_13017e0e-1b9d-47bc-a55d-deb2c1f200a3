#!/bin/bash

# 知我AI输入法 - 环境变量快速迁移脚本
# 一键完成从旧架构到新架构的迁移

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 打印函数
print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 主标题
echo -e "${GREEN}
=======================================================
   知我AI输入法 - 环境变量架构快速迁移
=======================================================
${NC}"

print_info "开始环境变量架构迁移..."

# 步骤1: 备份现有配置
print_info "步骤1: 备份现有配置"
if [[ -f ".env" ]]; then
    cp .env .env.backup
    print_success "已备份 .env 文件"
else
    print_warning ".env 文件不存在，跳过备份"
fi

if [[ -f "supabase/.env.local" ]]; then
    cp supabase/.env.local supabase/.env.local.backup
    print_success "已备份 supabase/.env.local 文件"
else
    print_warning "supabase/.env.local 文件不存在，跳过备份"
fi

# 步骤2: 初始化开发环境配置
print_info "步骤2: 初始化开发环境配置"
if [[ ! -x "scripts/setup-env.sh" ]]; then
    chmod +x scripts/setup-env.sh
    print_success "已设置脚本执行权限"
fi

# 从旧.env文件迁移配置
if [[ -f ".env.backup" ]]; then
    print_info "从旧配置文件迁移内容..."
    scripts/setup-env.sh init development
    
    # 如果存在.env.development，提示用户手动编辑
    if [[ -f ".env.development" ]]; then
        print_warning "请编辑 .env.development 文件："
        print_info "1. 将 .env.backup 中的配置复制到 .env.development"
        print_info "2. 移除AI服务密钥（这些将在Supabase中配置）"
        print_info "3. 确保 APP_ENV=development"
        
        read -p "是否现在打开编辑器？ (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            ${EDITOR:-nano} .env.development
        fi
    fi
else
    print_info "没有旧配置文件，创建新的开发环境配置..."
    scripts/setup-env.sh init development
fi

# 步骤3: 检查配置状态
print_info "步骤3: 检查当前配置状态"
scripts/setup-env.sh check

# 步骤4: 提示用户配置云端服务
print_info "步骤4: 配置云端服务"
print_warning "接下来需要配置云端服务，请按照以下步骤操作："

echo -e "${YELLOW}
4.1 配置Supabase边缘函数密钥：
${NC}   ./scripts/setup-env.sh setup-supabase
   
   这将设置以下密钥到Supabase云端：
   - SILICONFLOW_API_KEY
   - OPENAI_API_KEY (可选)
   - ANTHROPIC_API_KEY (可选)
   - ELEVENLABS_API_KEY (可选)

${YELLOW}4.2 配置EAS环境变量：
${NC}   ./scripts/setup-env.sh setup-eas staging
   ./scripts/setup-env.sh setup-eas production
   
   然后在EAS控制台中设置环境变量：
   https://expo.dev/accounts/[your-account]/projects/knowmetype/environment-variables"

# 步骤5: 测试新配置
print_info "步骤5: 测试建议"
echo -e "${BLUE}
测试步骤：
1. 本地开发测试：
   npm start
   
2. EAS构建测试：
   eas build --profile staging
   
3. AI服务测试：
   在应用中测试AI文本优化功能

${NC}"

# 步骤6: 清理旧文件（可选）
print_warning "步骤6: 清理旧文件（可选）"
if [[ -f ".env.backup" ]]; then
    read -p "迁移完成后是否删除备份文件？ (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -f .env.backup
        rm -f supabase/.env.local.backup
        print_success "已删除备份文件"
    else
        print_info "备份文件保留在 .env.backup 和 supabase/.env.local.backup"
    fi
fi

# 完成消息
echo -e "${GREEN}
=======================================================
                    迁移完成！
=======================================================

✅ 新架构优势：
   - EAS构建支持环境变量
   - API密钥安全存储在云端
   - 开发/测试/生产环境分离
   - 统一的配置管理

📋 下一步操作：
   1. 配置Supabase边缘函数密钥
   2. 设置EAS环境变量
   3. 测试本地开发和构建
   4. 部署到测试/生产环境

📚 相关文档：
   - ENV_MIGRATION_GUIDE.md
   - services/envConfigService.ts
   - supabase/ENV_MANAGEMENT_GUIDE.md

${NC}"

print_success "环境变量架构迁移完成！" 