# 知我AI输入法 - 环境变量配置模板 (已简化)
# 使用说明：
# 1. 开发环境：复制为 .env.development，使用本地Supabase配置
# 2. 测试环境：复制为 .env.staging，使用云端Supabase配置
# 3. 生产环境：复制为 .env.production，使用云端Supabase配置

# ===========================================
# 环境标识 (必须)
# ===========================================
APP_ENV=development  # development | staging | production

# ===========================================
# Supabase 配置 (直接配置该环境的具体值)
# ===========================================
# 📝 新架构说明：
# - 开发环境(.env.development): 直接使用本地Supabase配置
# - 测试环境(.env.staging): 直接使用云端Supabase配置  
# - 生产环境(.env.production): 直接使用云端Supabase配置

# 根据环境填入对应的值：
# 开发环境: 
#   SUPABASE_URL=http://127.0.0.1:54321
#   SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
#   SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
# 
# 生产环境:
#   SUPABASE_URL=https://yenwfmoubflrluhqsyim.supabase.co
#   SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InllbndmbW91YmZscmx1aHFzeWltIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MjM2NDMsImV4cCI6MjA1ODk5OTY0M30.P9t2dkOhfgPnt53WpDB2gQ6OLWJNvfPNFMqmihmTzXQ
#   SUPABASE_SERVICE_ROLE_KEY=你的云端service_role_key

SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# ===========================================
# 基础环境配置
# ===========================================
IS_DEV_ENV=true
SHOW_DEV_LOGIN=false

# ===========================================
# 应用商店配置
# ===========================================
IOS_APP_ID=6744967748
ANDROID_PACKAGE_NAME=com.mindpowerhk.knowmetype

# ===========================================
# RevenueCat 配置
# ===========================================
REVENUE_CAT_IOS_API_KEY=your_revenuecat_ios_key_here
REVENUE_CAT_ANDROID_API_KEY=your_revenuecat_android_key_here

# ===========================================
# 重要说明：以下密钥不应在客户端配置
# ===========================================
# AI服务密钥应该统一在 Supabase 云端配置，通过边缘函数代理访问
# 客户端只需要配置代理服务的URL，不需要直接存储API密钥

# 如果需要在本地测试AI功能，请在 supabase/.env.local 中配置
# SILICONFLOW_API_KEY=sk-your-key-here  # 仅用于本地测试
# OPENAI_API_KEY=sk-your-key-here       # 仅用于本地测试 