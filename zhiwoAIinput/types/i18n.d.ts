import 'i18next';

declare module 'i18next' {
  interface CustomTypeOptions {
    defaultNS: 'translation';
    resources: {
      translation: {
        loginGuide: {
          title: string;
          desc: string;
          skip: string;
          login: string;
        };
        common: Record<string, string>;
        home: {
          whisperModelEnabled: string;
          title: string;
          inputTitle: string;
          inputSubtitle: string;
          startRecording: string;
          stopRecording: string;
          processing: string;
          optimize: string;
          copy: string;
          share: string;
          clear: string;
          selectTemplate: string;
          noTemplate: string;
          optimizedText: string;
          originalText: string;
          editHint: string;
          recording: string;
          transcribing: string;
          selectStyle: string;
          optimizationResult: string;
          aiOptimizing: string;
          vipExclusive: string;
          vipHint: string;
          regenerate: string;
          selectTemplateHint: string;
          moreStyles: string;
          noVoiceDetected: string;
          recordingError: string;
          recordingErrorMsg: string;
          recordingTimeout: string;
          recordingTimeoutMsg: string;
          stopRecordingBtn: string;
          continueRecordingBtn: string;
          speakNow: string;
          tapToPause: string;
          clickToStartRecording: string;
          recordingHint: string;
          inputOrEditText: string;
          newVoiceInput: string;
          finish: string;
          pause: string;
          continue: string;
          newInput: string;
          upgradeNow: string;
          upgradeToVIP: string;
          vipUpgradeDescription: string;
          optimizingText: string;
          retryFailed: string;
          audioFileCorrupted: string;
          audioFileNotFound: string;
          retryTranscriptionFailed: string;
          retryTranscriptionFailedMsg: string;
          retryFailedWithError: string;
          featureTags: {
            smartReworking: string;
            sceneAdaptation: string;
            personalizedTemplates: string;
          };
          textDisplay: Record<string, string>;
          networkError: Record<string, string>;
          whisperLimit: {
            loginPrompt: string;
            switchToNative: string;
            title: string;
            message: string;
            upgradeOption: string;
            switchOption: string;
            remainingCount: string;
            vipUnlimited: string;
            usageStats: string;
            tip1: string;
            tip2: string;
            tip3: string;
          };
        };
        history: {
          detail: {
            jumpToResult: string;
            sourceRecordDeleted: string;
            sourceRecordDeletedDesc: string;
            title: string;
            backToList: string;
            originalContent: string;
            optimizedContent: string;
            optimizationSuccess: string;
            optimizationSuccessDesc: string;
            optimizationFailed: string;
            optimizationFailedDesc: string;
            reoptimize: string;
            share: string;
            noOriginalContent: string;
            noOptimizedContent: string;
            optimizationHistory: string;
            copy: string;
            copySpecificResult: string;
            reoptimizedInfo: {
              title: string;
              description: string;
              viewOriginal: string;
            };
            sections: {
              originalAudio: string;
              aiOptimized: string;
            };
            actions: {
              copyText: string;
              reoptimize: string;
              shareResult: string;
            };
            templateModal: {
              title: string;
              sourceText: string;
              optimizedText: string;
              originalText: string;
              optimizeButton: string;
              cancelButton: string;
              optimizing: string;
              selectTemplate: string;
              startOptimize: string;
            };
            toast: {
              copySuccess: string;
              selectSingleResult: string;
              optimizationSuccess: string;
              optimizationSuccessMessage: string;
              optimizationFailed: string;
              optimizationFailedMessage: string;
              viewHistoryList: string;
            };
            errors: {
              templateNotFound: string;
            };
          };
          today: string;
          yesterday: string;
          dayBeforeYesterday: string;
          thisWeek: string;
          thisMonth: string;
          earlier: string;
          expand: string;
          collapse: string;
          share: string;
          delete: string;
          copy: string;
          all: string;
          workRelated: string;
          email: string;
          meetingNotes: string;
          chat: string;
          important: string;
          personal: string;
          draft: string;
          archived: string;
          title: string;
          noRecords: string;
          deleteConfirm: string;
          deleteConfirmMultiple: string;
          deleteConfirmTitle: string;
          deleteSuccess: string;
          deleteSuccessMultiple: string;
          copySuccess: string;
          shareSuccess: string;
          searchPlaceholder: string;
          selectAll: string;
          deselectAll: string;
          select: string;
          cancel: string;
          vipAllRecords: string;
          freeUserLimitedRecords: string;
          freeUserAllRecords: string;
          deleteSelected: string;
          noRecordsFound: string;
          loadMore: string;
          loading: string;
          pullToRefresh: string;
          releaseToRefresh: string;
          refreshing: string;
          emptyStateTitle: string;
          emptyStateSubtitle: string;
          startRecording: string;
          selectMode: string;
          selectedCount: string;
          vipUpgrade: {
            title: string;
            subtitle: string;
            upgradeButton: string;
          };
        };
        settings: {
          appName: string;
          languageChangeFailed: string;
          wechatLogin: string;
          appleLogin: string;
          googleLogin: string;
          emailLogin: string;
          phoneLogin: string;
          title: string;
          account: string;
          appName: string;
          selectTranscribeModel: string;
          selectLanguageModel: string;
          uiLanguage: string;
          selectLanguage: string;
          themeStyle: string;
          selectThemeStyle: string;
          themeSystemStyle: string;
          themeSystemStyleDesc: string;
          themeDarkStyle: string;
          themeDarkStyleDesc: string;
          themeLightStyle: string;
          themeLightStyleDesc: string;
          fastMode: string;
          fastModeDescription: string;
          styleTemplate: string;
          templatePersonalization: string;
          templatePersonalizationSubtitle: string;
          templatePersonalizationPortal: string;
          templateManagerInstructions: string;
          templateManagerLoading: string;
          templateManagerSaving: string;
          templateManagerLoadError: string;
          templateManagerSaveError: string;
          templateManagerSystemTag: string;
          templateManagerCustomTag: string;
          templateManagerDefaultTag: string;
          templateManagerDeletedTemplate: string;
          templateManagerDeletedTemplateDesc: string;
          templateManagerOrderExplanation: string;
          templateManagerDragHint: string;
          transcribeModel: string;
          languageModel: string;
          theme: string;
          about: string;
          feedback: string;
          rateUs: string;
          rateUsDescription: string;
          rateInAppStore: string;
          rateInGooglePlay: string;
          thankYouForRating: string;
          ratingNotAvailable: string;
          openStoreError: string;
          privacyPolicy: string;
          termsOfService: string;
          logout: string;
          logoutConfirm: string;
          switchingLanguage: string;
          deleteAllData: string;
          dataManagement: string;
          deleteAllDataConfirm: string;
          deleteAllDataSuccess: string;
          deleteAllDataSuccessDesc: string;
          version: string;
          versionUpdate: string;
          vipExclusiveFeature: string;
          onlyVIPUsersCanUseThisModel: string;
          upgradeToUseVipModel: string;
          upgradeNow: string;
          vipBadgeText: string;
          vipCardTitle: string;
          vipCardSubtitle: string;
          unlimitedCustomTemplate: string;
          advancedAIModel: string;
          priorityRequest: string;
          fullHistoryRecord: string;
          vipSubscribed: string;
          checkVipDetail: string;
          vipSubscribedCardTitle: string;
          vipSubscribedCardSubtitle: string;
          loginGuideTitle: string;
          loginGuideSubtitle: string;
          loginGuideButtonText: string;
          toast: {
            settingsSaved: string;
            settingsSaveSuccess: string;
            settingsSaveFailed: string;
            localSettingsSaveFailed: string;
            cloudSyncFailed: string;
            settingsSavedWithIssue: string;
            languageChangeFailed: string;
          };
          vipFeatures: {
            title: string;
            message: string;
            aiModelSettingTitle: string;
            aiModelSettingMessage: string;
          };
          languageNames: Record<string, string>;
          subscriptionPlans: {
            monthly: string;
            yearly: string;
            vip: string;
          };
          authProviders: {
            unknown: string;
          };
          validityInfo: {
            notAvailable: string;
          };
          models: {
            nativeTranscribe: {
              ios: string;
              android: string;
            };
          };
          prompts: {
            upgradeVipTitle: string;
            upgradeVipMessage: string;
            modelSettingsTitle: string;
            modelSettingsMessage: string;
          };
          feedbackEmailSubject: string;
          feedbackEmailBodyLine1: string;
          feedbackEmailBodyLine2: string;
          feedbackEmailBodyLine3: string;
          feedbackEmailBodyLine4: string;
          feedbackEmailBodyLine5: string;
          feedbackEmailBodyLine6: string;
        };
        dataManagement: Record<string, string>;
        tabs: {
          input: string;
          history: string;
          settings: string;
        };
        templates: Record<string, string>;
        errors: Record<string, string>;
        auth: Record<string, string>;
        vipDetail: {
          title: string;
          vipStatus: string;
          validUntil: string;
          featuresTitle: string;
          changePlan: string;
          validityNotAvailable: string;
          plans: {
            monthly: string;
            yearly: string;
            default: string;
          };
          features: {
            unlimitedTemplates: {
              title: string;
              description: string;
            };
            advancedModel: {
              title: string;
              description: string;
            };
            priorityProcessing: {
              title: string;
              description: string;
            };
            cloudSync: {
              title: string;
              description: string;
            };
          };
        };
        payment: {
          currentPackage: string;
          title: string;
          priceHint: string;
          getVip: string;
          unlockPremium: string;
          award2025: string;
          awardApp: string;
          vipBenefits: string;
          userReviews: string;
          subscriptionOptions: string;
          monthly: string;
          yearly: string;
          billedAs: string;
          trialBilling: string;
          subscribeNow: string;
          startTrial: string;
          restorePurchases: string;
          termsOfService: string;
          privacyPolicy: string;
          subscriptionTerms: string;
          subscriptionTermsIos: string;
          subscriptionTermsAndroid: string;
          paymentInfoIos: string;
          paymentInfoAndroid: string;
          benefits: {
            unlimitedTemplates: {
              title: string;
              description: string;
            };
            advancedModel: {
              title: string;
              description: string;
            };
            priorityRequest: {
              title: string;
              description: string;
            };
            cloudSync: {
              title: string;
              description: string;
            };
          };
          reviews: Record<string, {
            title: string;
            content: string;
            author: string;
          }>;
          errors: {
            purchaseFailed: string;
            purchaseError: string;
            restoreFailed: string;
            restoreError: string;
            noSubscriptionFound: string;
            noActiveSubscription: string;
            loginRequired: string;
            loginRequiredMessage: string;
            subscriptionError: string;
            noSubscriptionAvailable: string;
          };
          success: {
            purchaseSuccess: string;
            purchaseSuccessMessage: string;
            restoreSuccess: string;
            restoreSuccessMessage: string;
          };
          loading: {
            loading: string;
            processing: string;
          };
          periodUnit: {
            month: string;
            year: string;
          };
        };
        login: Record<string, string>;
        aiModels: Record<string, {
          name: string;
          description: string;
        }>;
        template: Record<string, {
          name: string;
          description: string;
        }>;
        onboarding: {
          languageSelection: {
            title: string;
            subtitle: string;
          };
          welcome: {
            title: string;
            subtitle: string;
            changeLanguage: string;
          };
          source: {
            title: string;
            options: Record<string, string>;
          };
          useCases: {
            title: string;
            subtitle: string;
            options: Record<string, string>;
          };
          templates: {
            title: string;
            subtitle: string;
            vipRequired: string;
            loadError: string;
            loadErrorDesc: string;
            noTemplates: string;
            loading: string;
          };
          navigation: {
            next: string;
            back: string;
            complete: string;
            saving: string;
            skip: string;
          };
          progress: string;
        };
      };
    };
  }
}
