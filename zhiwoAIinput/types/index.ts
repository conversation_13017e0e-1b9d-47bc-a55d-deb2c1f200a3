export interface User {
  id: string;
  email: string;
  profile?: any;
  // 登录方式
  providerType?: 'wechat' | 'apple' | 'google' | 'email' | 'phone' | string;
  // 用户头像URL
  avatarUrl?: string;
  // 显示名称
  displayName?: string;
  // 用户名
  username?: string;
}

// 定义订阅计划类型
export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
}

// 定义订阅状态类型
export interface Subscription {
  id: string;
  userId: string;
  planId: string;
  status: 'active' | 'canceled' | 'expired';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  plan?: SubscriptionPlan;
}

// 定义支付记录类型
export interface Payment {
  id: string;
  userId: string;
  amount: number;
  currency: string;
  status: 'succeeded' | 'pending' | 'failed';
  createdAt: string;
  description: string;
} 