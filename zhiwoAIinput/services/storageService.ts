/**
 * 存储服务
 * 处理本地数据持久化，包括历史记录和用户设置
 */
import AsyncStorage from '@react-native-async-storage/async-storage';
import { log, error as logError, warn } from '@/services/logService';
import { extractRelativeAudioPath } from '@/utils/inputUtils';
import { newHistoryService, HistoryRecordCompat } from './newHistoryService';
import { userIdService } from './userIdService';

// 存储键名
const STORAGE_KEYS = {
  HISTORY_RECORDS: 'knowme_history_records',
  HISTORY_LAST_UPDATED: 'knowme_history_last_updated',
  USER_TEMPLATES: 'knowme_user_templates',
  USER_SETTINGS: 'knowme_user_settings',
  VIP_STATUS: 'knowme_vip_status',
  USE_ADVANCED_TRANSCRIPTION: 'knowme_use_advanced_transcription',
};

// 优化结果接口（保持向后兼容）
export interface OptimizedResult {
  text: string;
  timestamp: number;
  templateId: string;
  templateName: string;
}

// 历史记录项接口（保持向后兼容）
export interface HistoryRecord {
  id: string;
  timestamp: number;
  originalText: string;
  optimizedText: string;
  templateId: string; // 统一使用string类型
  templateName: string;
  audioUri?: string; // 可选的音频文件URI
  sourceRecordId?: string; // 二次优化的原始记录ID
  optimizedResults?: OptimizedResult[]; // 多次优化结果数组
}

// 是否使用新的SQLite服务（可以通过环境变量或配置控制）
const USE_SQLITE_SERVICE = true;

// 重新导出新服务中的类型，保持向后兼容
export type { HistoryGroup, HistoryRecordWithUI } from './newHistoryService';

// 用户设置接口
export interface UserSettings {
  defaultTemplateId?: number | string;
  preferredModel?: string;
  language?: string;
  theme?: 'light' | 'dark' | 'system';
  autoSave?: boolean;
  transcribeModel?: 'native' | 'whisper-1' | 'gpt-4o-mini-transcribe';
  languageModel?: 'qwen2.5-7b' | 'qwen3-8b' | 'qwen3-14b' | 'deepseek-v3';
  fastMode?: boolean;
  styleOptimization?: boolean; // 风格模板优化开关，默认开启
  appLockEnabled?: boolean; // 应用加密开关，默认关闭
}

// VIP状态接口
export interface VipStatus {
  isVip: boolean;
  expiryDate?: number; // 到期时间戳
  purchaseDate?: number; // 购买时间戳
}

/**
 * 保存历史记录
 * @param record 历史记录对象
 */
export const saveHistoryRecord = async (record: HistoryRecord): Promise<boolean> => {
  try {
    if (USE_SQLITE_SERVICE) {
      // 使用新的SQLite服务
      await userIdService.initialize();

      // 转换为兼容格式
      const compatRecord: HistoryRecordCompat = {
        id: record.id,
        timestamp: record.timestamp,
        originalText: record.originalText,
        optimizedText: record.optimizedText,
        templateId: String(record.templateId),
        templateName: record.templateName,
        audioUri: record.audioUri,
        sourceRecordId: record.sourceRecordId, // 修复：添加缺失的 sourceRecordId 字段
        optimizedResults: record.optimizedResults, // 修复：添加缺失的 optimizedResults 字段
      };

      const success = await newHistoryService.saveHistoryRecord(compatRecord);
      if (success) {
        log('历史记录已保存到SQLite数据库:', record.id);
      }
      return success;
    } else {
      // 使用原有的AsyncStorage逻辑
      // 确保音频URI格式正确，只保存相对路径
      if (record.audioUri) {
        record.audioUri = extractRelativeAudioPath(record.audioUri);
        log('保存带音频的历史记录（相对路径）:', record.audioUri);
      }

      // 获取现有历史记录
      const existingRecords = await getHistoryRecords();

      // 检查是否已存在相同ID的记录
      const existingIndex = existingRecords.findIndex(r => r.id === record.id);

      // 创建新的记录列表
      let updatedRecords;
      if (existingIndex >= 0) {
        // 如果记录已存在，更新该记录
        log('更新已存在的历史记录:', record.id);
        updatedRecords = [...existingRecords];
        updatedRecords[existingIndex] = record;
      } else {
        // 如果记录不存在，添加到开头
        log('添加新的历史记录:', record.id);
        updatedRecords = [record, ...existingRecords];
      }

      // 存储更新后的记录
      await AsyncStorage.setItem(
        STORAGE_KEYS.HISTORY_RECORDS,
        JSON.stringify(updatedRecords)
      );

      log('历史记录已保存到AsyncStorage:', record.id);
      return true;
    }
  } catch (error) {
    logError('保存历史记录失败:', error);
    return false;
  }
};

/**
 * 获取所有历史记录
 * @returns 历史记录数组
 */
export const getHistoryRecords = async (): Promise<HistoryRecord[]> => {
  try {
    if (USE_SQLITE_SERVICE) {
      // 使用新的SQLite服务
      await userIdService.initialize();
      const compatRecords = await newHistoryService.getHistoryRecords();

      // 转换为原有格式
      return compatRecords.map(record => ({
        id: record.id,
        timestamp: record.timestamp,
        originalText: record.originalText,
        optimizedText: record.optimizedText,
        templateId: record.templateId,
        templateName: record.templateName,
        audioUri: record.audioUri,
        sourceRecordId: record.sourceRecordId, // 修复：添加缺失的字段
        optimizedResults: record.optimizedResults, // 修复：添加缺失的字段
      }));
    } else {
      // 使用原有的AsyncStorage逻辑
      const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.HISTORY_RECORDS);
      return jsonValue ? JSON.parse(jsonValue) : [];
    }
  } catch (error) {
    logError('获取历史记录失败:', error);
    return [];
  }
};

/**
 * 获取指定ID的历史记录
 * @param id 历史记录ID
 * @returns 历史记录对象，如果不存在则返回null
 */
export const getHistoryRecordById = async (id: string): Promise<HistoryRecord | null> => {
  try {
    if (USE_SQLITE_SERVICE) {
      // 使用新的SQLite服务
      await userIdService.initialize();
      const compatRecord = await newHistoryService.getHistoryRecordById(id);

      if (!compatRecord) {
        return null;
      }

      // 转换为原有格式
      return {
        id: compatRecord.id,
        timestamp: compatRecord.timestamp,
        originalText: compatRecord.originalText,
        optimizedText: compatRecord.optimizedText,
        templateId: compatRecord.templateId,
        templateName: compatRecord.templateName,
        audioUri: compatRecord.audioUri,
        sourceRecordId: compatRecord.sourceRecordId, // 修复：添加缺失的 sourceRecordId 字段
        optimizedResults: compatRecord.optimizedResults, // 修复：添加缺失的 optimizedResults 字段
      };
    } else {
      // 使用原有的AsyncStorage逻辑
      const records = await getHistoryRecords();
      return records.find(record => record.id === id) || null;
    }
  } catch (error) {
    logError(`获取历史记录ID=${id}失败:`, error);
    return null;
  }
};

/**
 * 删除历史记录
 * @param id 要删除的历史记录ID
 */
export const deleteHistoryRecord = async (id: string): Promise<boolean> => {
  try {
    if (USE_SQLITE_SERVICE) {
      // 使用新的SQLite服务
      await userIdService.initialize();
      await newHistoryService.deleteHistoryRecord(id);
      log('历史记录已从SQLite删除:', id);
      return true;
    } else {
      // 使用原有的AsyncStorage逻辑
      const existingRecords = await getHistoryRecords();
      const updatedRecords = existingRecords.filter(record => record.id !== id);
      await AsyncStorage.setItem(
        STORAGE_KEYS.HISTORY_RECORDS,
        JSON.stringify(updatedRecords)
      );
      log('历史记录已从AsyncStorage删除:', id);
      return true;
    }
  } catch (error) {
    logError(`删除历史记录ID=${id}失败:`, error);
    return false;
  }
};

/**
 * 清空所有历史记录
 */
export const clearAllHistoryRecords = async (): Promise<boolean> => {
  try {
    if (USE_SQLITE_SERVICE) {
      // 使用新的SQLite服务
      await userIdService.initialize();
      const success = await newHistoryService.clearAllHistoryRecords();
      if (success) {
        log('所有历史记录已从SQLite清空');
      }
      return success;
    } else {
      // 使用原有的AsyncStorage逻辑
      await AsyncStorage.setItem(STORAGE_KEYS.HISTORY_RECORDS, JSON.stringify([]));
      log('所有历史记录已从AsyncStorage清空');
      return true;
    }
  } catch (error) {
    logError('清空历史记录失败:', error);
    return false;
  }
};

/**
 * 保存用户设置
 * @param settings 用户设置对象
 */
export const saveUserSettings = async (settings: UserSettings): Promise<void> => {
  try {
    // 获取现有设置
    const existingSettings = await getUserSettings();
    
    // 合并设置
    const updatedSettings = { ...existingSettings, ...settings };
    
    // 保存更新后的设置
    await AsyncStorage.setItem(
      STORAGE_KEYS.USER_SETTINGS,
      JSON.stringify(updatedSettings)
    );
    
    log('用户设置已保存');
  } catch (error) {
    logError('保存用户设置失败:', error);
    throw error;
  }
};

/**
 * 获取用户设置
 * @returns 用户设置对象
 */
export const getUserSettings = async (): Promise<UserSettings> => {
  try {
    log('[LoginGuide] getUserSettings被调用');
    const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.USER_SETTINGS);
    log('[LoginGuide] getUserSettings返回:', jsonValue);
    
    // 默认设置
    const defaultSettings: UserSettings = {
      transcribeModel: 'native',  // 默认使用原生转写模型
      languageModel: 'qwen2.5-7b', // 默认语言模型
      theme: 'system',            // 默认跟随系统主题
      autoSave: true,             // 默认启用自动保存
      fastMode: false,            // 默认关闭极速模式
      styleOptimization: true,    // 默认开启风格模板优化
      appLockEnabled: false       // 默认关闭应用加密
    };
    
    if (jsonValue) {
      const settings = JSON.parse(jsonValue);
      log("解析后的用户设置对象:", settings);
      log("语言模型设置:", settings.languageModel);
      log("语言模型设置类型:", typeof settings.languageModel);
      
      // 合并默认设置和用户设置，确保所有必要字段都有值
      return { ...defaultSettings, ...settings };
    } else {
      log("未找到用户设置，返回默认设置");
      return defaultSettings;
    }
  } catch (error) {
    logError('获取用户设置失败:', error);
    logError('错误详情:', JSON.stringify(error, null, 2));
    return {};
  }
};

/**
 * 获取VIP状态
 * @returns VIP状态对象
 */
export const getVipStatus = async (): Promise<VipStatus> => {
  try {
    const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.VIP_STATUS);
    return jsonValue ? JSON.parse(jsonValue) : { isVip: false };
  } catch (error) {
    logError('获取VIP状态失败:', error);
    return { isVip: false };
  }
};

/**
 * 保存VIP状态
 * @param status VIP状态对象
 */
export const saveVipStatus = async (status: VipStatus): Promise<void> => {
  try {
    await AsyncStorage.setItem(
      STORAGE_KEYS.VIP_STATUS,
      JSON.stringify(status)
    );
    log('VIP状态已保存:', status.isVip);
  } catch (error) {
    logError('保存VIP状态失败:', error);
    throw error;
  }
};

/**
 * 获取是否使用高级转写设置
 * 只有VIP用户能够使用高级转写功能
 * @returns 是否启用高级转写
 */
export const getUseAdvancedTranscription = async (): Promise<boolean> => {
  try {
    // 先检查VIP状态
    const vipStatus = await getVipStatus();
    
    // 如果不是VIP用户，直接返回false，不允许使用高级转写
    if (!vipStatus.isVip) {
      log('用户不是VIP，不允许使用高级转写功能');
      
      // 更新存储值确保一致性
      await setUseAdvancedTranscription(false);
      
      return false;
    }
    
    // 如果是VIP用户，查询用户设置值
    const value = await AsyncStorage.getItem(STORAGE_KEYS.USE_ADVANCED_TRANSCRIPTION);
    // 默认不启用高级转写
    return value === 'true';
  } catch (error) {
    logError('获取高级转写设置失败:', error);
    return false;
  }
};

/**
 * 设置是否使用高级转写
 * @param useAdvanced 是否启用高级转写
 */
export const setUseAdvancedTranscription = async (useAdvanced: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(
      STORAGE_KEYS.USE_ADVANCED_TRANSCRIPTION,
      JSON.stringify(useAdvanced)
    );
    log('高级转写设置已更新:', useAdvanced);
  } catch (error) {
    logError('保存高级转写设置失败:', error);
    throw error;
  }
};

/**
 * 重置用户设置为默认值
 * 在用户登出时调用，确保非登录用户使用 native 转写模型
 */
export const resetUserSettings = async (): Promise<void> => {
  try {
    // 获取当前设置
    const currentSettings = await getUserSettings();
    
    // 重置转写模型为 native
    const defaultSettings: UserSettings = {
      ...currentSettings,
      transcribeModel: 'native',
    };
    
    // 保存重置后的设置
    await saveUserSettings(defaultSettings);
    log('用户设置已重置为默认值');
  } catch (error) {
    logError('重置用户设置失败:', error);
    throw error;
  }
};

// ==================== 历史记录相关方法（代理到新服务） ====================

/**
 * 获取按日期分组的历史记录
 * @param limit 限制加载的记录条数，默认不限制
 * @param t 翻译函数，用于多语言支持
 * @returns 按日期分组的历史记录数组
 */
export const getGroupedHistoryRecords = async (limit?: number, t?: (key: string) => string): Promise<any[]> => {
  try {
    if (USE_SQLITE_SERVICE) {
      await userIdService.initialize();
      return await newHistoryService.getGroupedHistoryRecords(limit, t);
    } else {
      // 回退到原有的historyService（如果需要的话）
      warn('StorageService: 使用AsyncStorage模式，某些功能可能不可用');
      return [];
    }
  } catch (error) {
    logError('获取分组历史记录失败:', error);
    return [];
  }
};

/**
 * 根据模板名称获取分组的历史记录
 * @param templateName 模板名称，如果为空则返回所有记录
 * @param limit 限制加载的记录条数
 * @param t 翻译函数，用于多语言支持
 * @returns 按日期分组的历史记录数组
 */
export const getGroupedHistoryRecordsByTemplate = async (
  templateName?: string,
  limit?: number,
  t?: (key: string) => string
): Promise<any[]> => {
  try {
    if (USE_SQLITE_SERVICE) {
      await userIdService.initialize();
      return await newHistoryService.getGroupedHistoryRecordsByTemplate(templateName, limit, t);
    } else {
      warn('StorageService: 使用AsyncStorage模式，某些功能可能不可用');
      return [];
    }
  } catch (error) {
    logError('根据模板获取分组历史记录失败:', error);
    return [];
  }
};

/**
 * 获取真实的历史记录总数（不受VIP限制）
 * @returns 真实的历史记录总数
 */
export const getRealHistoryRecordsCount = async (): Promise<number> => {
  try {
    if (USE_SQLITE_SERVICE) {
      await userIdService.initialize();
      return await newHistoryService.getRealHistoryRecordsCount();
    } else {
      const records = await getHistoryRecords();
      return records.length;
    }
  } catch (error) {
    logError('获取真实历史记录总数失败:', error);
    return 0;
  }
};

/**
 * 获取用户可见的历史记录总数（考虑VIP限制）
 * @returns 用户可见的历史记录总数
 */
export const getVisibleHistoryRecordsCount = async (): Promise<number> => {
  try {
    if (USE_SQLITE_SERVICE) {
      await userIdService.initialize();
      return await newHistoryService.getVisibleHistoryRecordsCount();
    } else {
      const records = await getHistoryRecords();
      const vipStatus = await getVipStatus();
      if (!vipStatus.isVip) {
        return Math.min(records.length, 50);
      } else {
        return records.length;
      }
    }
  } catch (error) {
    logError('获取可见历史记录总数失败:', error);
    return 0;
  }
};

/**
 * 获取所有历史记录中使用过的模板名称（用于标签筛选）
 * @returns 模板名称数组
 */
export const getAllTemplateNamesFromHistory = async (): Promise<string[]> => {
  try {
    if (USE_SQLITE_SERVICE) {
      await userIdService.initialize();
      return await newHistoryService.getAllTemplateNamesFromHistory();
    } else {
      const records = await getHistoryRecords();
      const templateNames = new Set<string>();
      records.forEach(record => {
        if (record.templateName) {
          templateNames.add(record.templateName);
        }
      });
      return Array.from(templateNames).sort();
    }
  } catch (error) {
    logError('获取历史记录模板名称失败:', error);
    return [];
  }
};

/**
 * 获取在前50条记录范围内可用的模板名称（仅非VIP用户需要）
 * @returns 在前50条记录中存在的模板名称数组
 */
export const getAvailableTemplateNamesInVisibleRange = async (): Promise<string[]> => {
  try {
    if (USE_SQLITE_SERVICE) {
      await userIdService.initialize();
      return await newHistoryService.getAvailableTemplateNamesInVisibleRange();
    } else {
      const allRecords = await getHistoryRecords();
      const vipStatus = await getVipStatus();

      let visibleRecords = allRecords;
      if (!vipStatus.isVip) {
        visibleRecords = allRecords.slice(0, 50);
      }

      const templateNames = new Set<string>();
      visibleRecords.forEach(record => {
        if (record.templateName) {
          templateNames.add(record.templateName);
        }
      });
      return Array.from(templateNames).sort();
    }
  } catch (error) {
    logError('获取可见范围内的模板名称失败:', error);
    return [];
  }
};

/**
 * 获取指定模板的可见历史记录总数（考虑VIP限制）
 * @param templateName 模板名称，如果为空则返回所有记录数
 * @returns 指定模板的可见历史记录总数
 */
export const getVisibleHistoryRecordsCountByTemplate = async (templateName?: string): Promise<number> => {
  try {
    if (USE_SQLITE_SERVICE) {
      await userIdService.initialize();
      return await newHistoryService.getVisibleHistoryRecordsCountByTemplate(templateName);
    } else {
      const allRecords = await getHistoryRecords();
      const vipStatus = await getVipStatus();

      let visibleRecords = allRecords;
      if (!vipStatus.isVip) {
        visibleRecords = allRecords.slice(0, 50);
      }

      if (templateName && templateName !== "全部") {
        visibleRecords = visibleRecords.filter(record => record.templateName === templateName);
      }

      return visibleRecords.length;
    }
  } catch (error) {
    logError('获取指定模板的可见历史记录总数失败:', error);
    return 0;
  }
};

/**
 * 获取指定模板的真实历史记录总数（不受VIP限制）
 * @param templateName 模板名称，如果为空则返回所有记录数
 * @returns 指定模板的真实历史记录总数
 */
export const getRealHistoryRecordsCountByTemplate = async (templateName?: string): Promise<number> => {
  try {
    if (USE_SQLITE_SERVICE) {
      await userIdService.initialize();
      return await newHistoryService.getRealHistoryRecordsCountByTemplate(templateName);
    } else {
      const allRecords = await getHistoryRecords();

      let filteredRecords = allRecords;
      if (templateName && templateName !== "全部") {
        filteredRecords = allRecords.filter(record => record.templateName === templateName);
      }

      return filteredRecords.length;
    }
  } catch (error) {
    logError('获取指定模板的真实历史记录总数失败:', error);
    return 0;
  }
};

/**
 * 检查指定模板是否有被VIP限制隐藏的记录
 * @param templateName 模板名称，如果为空则检查所有记录
 * @returns 隐藏的记录数量
 */
export const getHiddenRecordsCountByTemplate = async (templateName?: string): Promise<number> => {
  try {
    if (USE_SQLITE_SERVICE) {
      await userIdService.initialize();
      return await newHistoryService.getHiddenRecordsCountByTemplate(templateName);
    } else {
      const allRecords = await getHistoryRecords();
      const vipStatus = await getVipStatus();

      if (vipStatus.isVip) {
        return 0;
      }

      let filteredRecords = allRecords;
      if (templateName && templateName !== "全部") {
        filteredRecords = allRecords.filter(record => record.templateName === templateName);
      }

      const visibleRecords = allRecords.slice(0, 50);
      let visibleFilteredRecords = visibleRecords;
      if (templateName && templateName !== "全部") {
        visibleFilteredRecords = visibleRecords.filter(record => record.templateName === templateName);
      }

      return Math.max(0, filteredRecords.length - visibleFilteredRecords.length);
    }
  } catch (error) {
    logError('获取指定模板的隐藏记录总数失败:', error);
    return 0;
  }
};

/**
 * 获取历史记录最后更新时间戳
 * @returns 最后更新时间戳
 */
export const getLastUpdatedTimestamp = async (): Promise<number> => {
  try {
    if (USE_SQLITE_SERVICE) {
      // 对于SQLite服务，我们可以从最新的记录中获取时间戳
      await userIdService.initialize();
      const records = await newHistoryService.getHistoryRecords(undefined, 1, 0);
      if (records.length > 0) {
        return records[0].timestamp;
      }
      return 0;
    } else {
      // 使用原有的AsyncStorage逻辑
      const timestampStr = await AsyncStorage.getItem(STORAGE_KEYS.HISTORY_LAST_UPDATED);
      if (timestampStr) {
        return parseInt(timestampStr, 10);
      }
      return 0;
    }
  } catch (error) {
    logError('获取最后更新时间戳失败:', error);
    return 0;
  }
};