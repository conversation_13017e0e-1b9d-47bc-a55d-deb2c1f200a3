/**
 * SQLite数据库服务
 * 提供本地数据库的初始化、管理和操作功能
 */
import * as SQLite from 'expo-sqlite';
import * as FileSystem from 'expo-file-system';
import { log, error as logError, warn } from '@/services/logService';

// 数据库配置
const DB_VERSION = 1;

// 历史记录表结构
export interface HistoryRecord {
  id: string;
  user_id: string;
  timestamp: number;
  original_text: string;
  optimized_text: string;
  template_id: string;
  template_name: string;
  audio_uri?: string;
  source_record_id?: string;
  optimized_results?: string; // JSON字符串
  created_at: number;
  updated_at: number;
}

// 录音文件记录表结构
export interface RecordingFileRecord {
  id: string;
  user_id: string;
  file_path: string; // 完整的绝对路径
  relative_path: string; // 相对路径部分
  file_name: string;
  file_size?: number;
  duration?: number;
  created_at: number;
  updated_at: number;
}

/**
 * SQLite数据库服务类
 */
class SQLiteService {
  private databases: Map<string, SQLite.SQLiteDatabase> = new Map();

  /**
   * 获取用户专属的数据库实例
   * @param userId 用户ID
   * @returns 数据库实例
   */
  async getDatabase(userId: string): Promise<SQLite.SQLiteDatabase> {
    if (this.databases.has(userId)) {
      return this.databases.get(userId)!;
    }

    try {
      const dbName = `knowmetype.db.${userId}`;
      log(`SQLiteService: 打开数据库 ${dbName}`);
      
      const db = await SQLite.openDatabaseAsync(dbName);
      
      // 初始化数据库表
      await this.initializeTables(db);
      
      this.databases.set(userId, db);
      log(`SQLiteService: 数据库 ${dbName} 初始化完成`);
      
      return db;
    } catch (error) {
      logError(`SQLiteService: 打开数据库失败 (userId: ${userId}):`, error);
      throw error;
    }
  }

  /**
   * 初始化数据库表
   * @param db 数据库实例
   */
  private async initializeTables(db: SQLite.SQLiteDatabase): Promise<void> {
    try {
      // 创建历史记录表
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS history_records (
          id TEXT PRIMARY KEY,
          user_id TEXT NOT NULL,
          timestamp INTEGER NOT NULL,
          original_text TEXT NOT NULL,
          optimized_text TEXT NOT NULL,
          template_id TEXT NOT NULL,
          template_name TEXT NOT NULL,
          audio_uri TEXT,
          source_record_id TEXT,
          optimized_results TEXT,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL
        );
      `);

      // 创建录音文件记录表
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS recording_files (
          id TEXT PRIMARY KEY,
          user_id TEXT NOT NULL,
          file_path TEXT NOT NULL,
          relative_path TEXT NOT NULL,
          file_name TEXT NOT NULL,
          file_size INTEGER,
          duration INTEGER,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL
        );
      `);

      // 创建索引
      await db.execAsync(`
        CREATE INDEX IF NOT EXISTS idx_history_records_user_id ON history_records(user_id);
        CREATE INDEX IF NOT EXISTS idx_history_records_timestamp ON history_records(timestamp);
        CREATE INDEX IF NOT EXISTS idx_recording_files_user_id ON recording_files(user_id);
        CREATE INDEX IF NOT EXISTS idx_recording_files_file_path ON recording_files(file_path);
      `);

      log('SQLiteService: 数据库表初始化完成');
    } catch (error) {
      logError('SQLiteService: 初始化数据库表失败:', error);
      throw error;
    }
  }

  /**
   * 保存历史记录
   * @param userId 用户ID
   * @param record 历史记录
   */
  async saveHistoryRecord(userId: string, record: Omit<HistoryRecord, 'user_id' | 'created_at' | 'updated_at'>): Promise<void> {
    try {
      const db = await this.getDatabase(userId);
      const now = Date.now();
      
      const fullRecord: HistoryRecord = {
        ...record,
        user_id: userId,
        created_at: now,
        updated_at: now
      };

      await db.runAsync(
        `INSERT OR REPLACE INTO history_records 
         (id, user_id, timestamp, original_text, optimized_text, template_id, template_name, 
          audio_uri, source_record_id, optimized_results, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          fullRecord.id,
          fullRecord.user_id,
          fullRecord.timestamp,
          fullRecord.original_text,
          fullRecord.optimized_text,
          fullRecord.template_id,
          fullRecord.template_name,
          fullRecord.audio_uri || null,
          fullRecord.source_record_id || null,
          fullRecord.optimized_results || null,
          fullRecord.created_at,
          fullRecord.updated_at
        ]
      );

      log(`SQLiteService: 保存历史记录成功 (userId: ${userId}, recordId: ${record.id})`);
    } catch (error) {
      logError(`SQLiteService: 保存历史记录失败 (userId: ${userId}):`, error);
      throw error;
    }
  }

  /**
   * 获取历史记录列表
   * @param userId 用户ID
   * @param limit 限制数量
   * @param offset 偏移量
   * @returns 历史记录列表
   */
  async getHistoryRecords(userId: string, limit: number = 50, offset: number = 0): Promise<HistoryRecord[]> {
    try {
      const db = await this.getDatabase(userId);
      
      const result = await db.getAllAsync(
        `SELECT * FROM history_records 
         WHERE user_id = ? 
         ORDER BY timestamp DESC 
         LIMIT ? OFFSET ?`,
        [userId, limit, offset]
      );

      log(`SQLiteService: 获取历史记录成功 (userId: ${userId}, count: ${result.length})`);
      return result as HistoryRecord[];
    } catch (error) {
      logError(`SQLiteService: 获取历史记录失败 (userId: ${userId}):`, error);
      return [];
    }
  }

  /**
   * 根据ID获取历史记录
   * @param userId 用户ID
   * @param recordId 记录ID
   * @returns 历史记录或null
   */
  async getHistoryRecordById(userId: string, recordId: string): Promise<HistoryRecord | null> {
    try {
      const db = await this.getDatabase(userId);
      
      const result = await db.getFirstAsync(
        `SELECT * FROM history_records WHERE user_id = ? AND id = ?`,
        [userId, recordId]
      );

      return result as HistoryRecord | null;
    } catch (error) {
      logError(`SQLiteService: 获取历史记录失败 (userId: ${userId}, recordId: ${recordId}):`, error);
      return null;
    }
  }

  /**
   * 删除历史记录
   * @param userId 用户ID
   * @param recordId 记录ID
   */
  async deleteHistoryRecord(userId: string, recordId: string): Promise<void> {
    try {
      const db = await this.getDatabase(userId);
      
      await db.runAsync(
        `DELETE FROM history_records WHERE user_id = ? AND id = ?`,
        [userId, recordId]
      );

      log(`SQLiteService: 删除历史记录成功 (userId: ${userId}, recordId: ${recordId})`);
    } catch (error) {
      logError(`SQLiteService: 删除历史记录失败 (userId: ${userId}, recordId: ${recordId}):`, error);
      throw error;
    }
  }

  /**
   * 保存录音文件记录
   * @param userId 用户ID
   * @param record 录音文件记录
   */
  async saveRecordingFile(userId: string, record: Omit<RecordingFileRecord, 'user_id' | 'created_at' | 'updated_at'>): Promise<void> {
    try {
      const db = await this.getDatabase(userId);
      const now = Date.now();
      
      const fullRecord: RecordingFileRecord = {
        ...record,
        user_id: userId,
        created_at: now,
        updated_at: now
      };

      await db.runAsync(
        `INSERT OR REPLACE INTO recording_files 
         (id, user_id, file_path, relative_path, file_name, file_size, duration, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          fullRecord.id,
          fullRecord.user_id,
          fullRecord.file_path,
          fullRecord.relative_path,
          fullRecord.file_name,
          fullRecord.file_size || null,
          fullRecord.duration || null,
          fullRecord.created_at,
          fullRecord.updated_at
        ]
      );

      log(`SQLiteService: 保存录音文件记录成功 (userId: ${userId}, fileId: ${record.id})`);
    } catch (error) {
      logError(`SQLiteService: 保存录音文件记录失败 (userId: ${userId}):`, error);
      throw error;
    }
  }

  /**
   * 获取录音文件记录
   * @param userId 用户ID
   * @param filePath 文件路径
   * @returns 录音文件记录或null
   */
  async getRecordingFileByPath(userId: string, filePath: string): Promise<RecordingFileRecord | null> {
    try {
      const db = await this.getDatabase(userId);
      
      const result = await db.getFirstAsync(
        `SELECT * FROM recording_files WHERE user_id = ? AND file_path = ?`,
        [userId, filePath]
      );

      return result as RecordingFileRecord | null;
    } catch (error) {
      logError(`SQLiteService: 获取录音文件记录失败 (userId: ${userId}, filePath: ${filePath}):`, error);
      return null;
    }
  }

  /**
   * 删除录音文件记录
   * @param userId 用户ID
   * @param filePath 文件路径
   */
  async deleteRecordingFile(userId: string, filePath: string): Promise<void> {
    try {
      const db = await this.getDatabase(userId);

      await db.runAsync(
        `DELETE FROM recording_files WHERE user_id = ? AND file_path = ?`,
        [userId, filePath]
      );

      log(`SQLiteService: 删除录音文件记录成功 (userId: ${userId}, filePath: ${filePath})`);
    } catch (error) {
      logError(`SQLiteService: 删除录音文件记录失败 (userId: ${userId}, filePath: ${filePath}):`, error);
      throw error;
    }
  }

  /**
   * 关闭数据库连接
   * @param userId 用户ID
   */
  async closeDatabase(userId: string): Promise<void> {
    try {
      const db = this.databases.get(userId);
      if (db) {
        await db.closeAsync();
        this.databases.delete(userId);
        log(`SQLiteService: 关闭数据库连接 (userId: ${userId})`);
      }
    } catch (error) {
      logError(`SQLiteService: 关闭数据库连接失败 (userId: ${userId}):`, error);
    }
  }

  /**
   * 迁移历史记录从一个用户到另一个用户
   * @param fromUserId 源用户ID
   * @param toUserId 目标用户ID
   * @returns 迁移的记录数量
   */
  async migrateHistoryRecords(fromUserId: string, toUserId: string): Promise<number> {
    try {
      const fromDb = await this.getDatabase(fromUserId);
      const toDb = await this.getDatabase(toUserId);

      // 获取源用户的所有历史记录
      const records = await fromDb.getAllAsync(
        `SELECT * FROM history_records WHERE user_id = ?`,
        [fromUserId]
      ) as HistoryRecord[];

      if (records.length === 0) {
        log(`SQLiteService: 没有需要迁移的历史记录 (fromUserId: ${fromUserId})`);
        return 0;
      }

      // 将记录插入到目标用户数据库
      for (const record of records) {
        const updatedRecord = {
          ...record,
          user_id: toUserId,
          updated_at: Date.now()
        };

        await toDb.runAsync(
          `INSERT OR REPLACE INTO history_records
           (id, user_id, timestamp, original_text, optimized_text, template_id, template_name,
            audio_uri, source_record_id, optimized_results, created_at, updated_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            updatedRecord.id,
            updatedRecord.user_id,
            updatedRecord.timestamp,
            updatedRecord.original_text,
            updatedRecord.optimized_text,
            updatedRecord.template_id,
            updatedRecord.template_name,
            updatedRecord.audio_uri || null,
            updatedRecord.source_record_id || null,
            updatedRecord.optimized_results || null,
            updatedRecord.created_at,
            updatedRecord.updated_at
          ]
        );
      }

      // 删除源用户的历史记录
      await fromDb.runAsync(
        `DELETE FROM history_records WHERE user_id = ?`,
        [fromUserId]
      );

      log(`SQLiteService: 迁移历史记录成功 (${fromUserId} -> ${toUserId}), 共 ${records.length} 条记录`);
      return records.length;
    } catch (error) {
      logError(`SQLiteService: 迁移历史记录失败 (${fromUserId} -> ${toUserId}):`, error);
      throw error;
    }
  }

  /**
   * 关闭所有数据库连接
   */
  async closeAllDatabases(): Promise<void> {
    try {
      for (const [userId, db] of this.databases.entries()) {
        await db.closeAsync();
        log(`SQLiteService: 关闭数据库连接 (userId: ${userId})`);
      }
      this.databases.clear();
      log('SQLiteService: 所有数据库连接已关闭');
    } catch (error) {
      logError('SQLiteService: 关闭所有数据库连接失败:', error);
    }
  }
}

// 导出单例实例
export const sqliteService = new SQLiteService();
