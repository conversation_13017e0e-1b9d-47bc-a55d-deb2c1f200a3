/**
 * 模板偏好设置服务
 * 处理用户对模板的个性化设置（显示/隐藏、排序等）
 */

import { log, error as logError, warn } from '@/services/logService';
import { supabase, authService } from './supabaseService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 模板偏好设置接口
export interface TemplatePreference {
  id: string;
  userId: string;
  templateId: string;
  templateType: 'system' | 'user';
  isHidden: boolean;
  displayOrder: number;
  createdAt: string;
  updatedAt: string;
}

// 本地偏好设置接口（简化版）
export interface LocalTemplatePreference {
  templateId: string;
  templateType: 'system' | 'user';
  isHidden: boolean;
  displayOrder: number;
}

// 存储键名
const STORAGE_KEYS = {
  TEMPLATE_PREFERENCES: 'knowme_template_preferences',
  PREFERENCES_LAST_SYNC: 'knowme_preferences_last_sync',
};

/**
 * 获取用户的模板偏好设置
 * @returns 用户的模板偏好设置数组
 */
export const getUserTemplatePreferences = async (): Promise<LocalTemplatePreference[]> => {
  try {
    // 优先从云端获取（如果用户已登录）
    const { data: userData } = await authService.getCurrentUser();
    if (userData.user) {
      try {
        const cloudPreferences = await fetchUserPreferencesFromCloud();
        if (cloudPreferences.length > 0) {
          // 保存到本地缓存
          await savePreferencesToLocal(cloudPreferences);
          return cloudPreferences;
        }
      } catch (cloudError) {
        warn('[TemplatePreference] 从云端获取偏好设置失败，使用本地缓存:', cloudError);
      }
    }

    // 从本地存储获取
    return await getPreferencesFromLocal();
  } catch (error) {
    logError('[TemplatePreference] 获取用户模板偏好设置失败:', error);
    return [];
  }
};

/**
 * 保存模板偏好设置
 * @param preferences 偏好设置数组
 */
export const saveUserTemplatePreferences = async (preferences: LocalTemplatePreference[]): Promise<boolean> => {
  try {
    // 保存到本地
    await savePreferencesToLocal(preferences);

    // 如果用户已登录，同步到云端
    const { data: userData } = await authService.getCurrentUser();
    if (userData.user) {
      try {
        await saveUserPreferencesToCloud(preferences);
        log('[TemplatePreference] 偏好设置已同步到云端');
      } catch (cloudError) {
        warn('[TemplatePreference] 同步偏好设置到云端失败:', cloudError);
        // 本地保存成功，云端失败不影响用户体验
      }
    }

    return true;
  } catch (error) {
    logError('[TemplatePreference] 保存模板偏好设置失败:', error);
    return false;
  }
};

/**
 * 隐藏模板（优化版本）
 * @param templateId 模板ID
 * @param templateType 模板类型
 */
export const hideTemplate = async (templateId: string, templateType: 'system' | 'user'): Promise<boolean> => {
  try {
    // 检查是否为默认模板
    const { getDefaultTemplateId } = await import('./templateService');
    const defaultTemplateId = await getDefaultTemplateId();
    
    if (templateId === defaultTemplateId) {
      warn('[TemplatePreference] 不能隐藏默认模板:', templateId);
      return false;
    }

    const preferences = await getUserTemplatePreferences();
    const existingIndex = preferences.findIndex(p => p.templateId === templateId);

    let updatedPreference: LocalTemplatePreference;
    
    if (existingIndex >= 0) {
      // 更新现有偏好设置
      updatedPreference = {
        ...preferences[existingIndex],
        isHidden: true
      };
    } else {
      // 创建新的偏好设置
      updatedPreference = {
        templateId,
        templateType,
        isHidden: true,
        displayOrder: preferences.length, // 默认排在最后
      };
    }

    // 使用增量更新
    return await updateTemplatePreferencesIncremental([updatedPreference]);
  } catch (error) {
    logError('[TemplatePreference] 隐藏模板失败:', error);
    return false;
  }
};

/**
 * 显示模板（优化版本）
 * @param templateId 模板ID
 * @param templateType 模板类型
 */
export const showTemplate = async (templateId: string, templateType: 'system' | 'user'): Promise<boolean> => {
  try {
    const preferences = await getUserTemplatePreferences();
    const existingIndex = preferences.findIndex(p => p.templateId === templateId);

    let updatedPreference: LocalTemplatePreference;

    if (existingIndex >= 0) {
      // 更新现有偏好设置
      updatedPreference = {
        ...preferences[existingIndex],
        isHidden: false
      };
    } else {
      // 创建新的偏好设置
      updatedPreference = {
        templateId,
        templateType,
        isHidden: false,
        displayOrder: preferences.length, // 默认排在最后
      };
    }

    // 使用增量更新
    return await updateTemplatePreferencesIncremental([updatedPreference]);
  } catch (error) {
    logError('[TemplatePreference] 显示模板失败:', error);
    return false;
  }
};

/**
 * 更新模板显示顺序（优化版本，只更新变化的项）
 * 确保默认模板始终保持在第一位
 * @param templateOrder 模板ID按显示顺序排列的数组
 */
export const updateTemplateDisplayOrder = async (templateOrder: Array<{templateId: string, templateType: 'system' | 'user'}>): Promise<boolean> => {
  try {
    // 获取默认模板ID
    const { getDefaultTemplateId } = await import('./templateService');
    const defaultTemplateId = await getDefaultTemplateId();
    
    const preferences = await getUserTemplatePreferences();
    
    // 创建一个映射，便于快速查找现有偏好设置
    const preferenceMap = new Map(preferences.map(p => [p.templateId, p]));

    // 找出需要更新的项（显示顺序发生变化的）
    const changedPreferences: LocalTemplatePreference[] = [];
    const newPreferences: LocalTemplatePreference[] = [];

    templateOrder.forEach((item, index) => {
      // 如果是默认模板，强制设置为显示顺序0
      const actualDisplayOrder = item.templateId === defaultTemplateId ? 0 : index;
      
      const existing = preferenceMap.get(item.templateId);
      const newPreference: LocalTemplatePreference = {
        templateId: item.templateId,
        templateType: item.templateType,
        isHidden: existing?.isHidden || false,
        displayOrder: actualDisplayOrder,
      };

      if (!existing) {
        // 新的偏好设置
        newPreferences.push(newPreference);
      } else if (existing.displayOrder !== actualDisplayOrder) {
        // 显示顺序发生变化
        changedPreferences.push(newPreference);
      }
    });

    log(`[TemplatePreference] 排序更新分析: 新增 ${newPreferences.length} 个，变化 ${changedPreferences.length} 个, 默认模板ID: ${defaultTemplateId}`);

    // 如果没有变化，直接返回成功
    if (changedPreferences.length === 0 && newPreferences.length === 0) {
      log('[TemplatePreference] 没有排序变化，跳过更新');
      return true;
    }

    // 使用增量更新
    return await updateTemplatePreferencesIncremental([...changedPreferences, ...newPreferences]);
  } catch (error) {
    logError('[TemplatePreference] 更新模板显示顺序失败:', error);
    return false;
  }
};

/**
 * 增量更新模板偏好设置（只更新变化的项）
 * @param changedPreferences 需要更新的偏好设置数组
 */
const updateTemplatePreferencesIncremental = async (changedPreferences: LocalTemplatePreference[]): Promise<boolean> => {
  try {
    // 先更新本地存储 - 只更新变化的项
    const allPreferences = await getPreferencesFromLocal();
    const preferenceMap = new Map(allPreferences.map(p => [p.templateId, p]));
    
    // 应用变更
    changedPreferences.forEach(changed => {
      preferenceMap.set(changed.templateId, changed);
    });
    
    const updatedPreferences = Array.from(preferenceMap.values());
    await savePreferencesToLocal(updatedPreferences);

    // 如果用户已登录，增量同步到云端
    const { data: userData } = await authService.getCurrentUser();
    if (userData.user) {
      try {
        await saveChangedPreferencesToCloud(changedPreferences);
        log(`[TemplatePreference] 成功增量同步 ${changedPreferences.length} 个偏好设置到云端`);
      } catch (cloudError) {
        warn('[TemplatePreference] 增量同步偏好设置到云端失败:', cloudError);
        // 本地保存成功，云端失败不影响用户体验
      }
    }

    return true;
  } catch (error) {
    logError('[TemplatePreference] 增量更新模板偏好设置失败:', error);
    return false;
  }
};

/**
 * 批量更新变化的偏好设置到云端（优化版本）
 * @param changedPreferences 需要更新的偏好设置数组
 */
const saveChangedPreferencesToCloud = async (changedPreferences: LocalTemplatePreference[]): Promise<void> => {
  if (changedPreferences.length === 0) {
    return;
  }

  try {
    const { data: userData } = await authService.getCurrentUser();
    if (!userData.user) {
      log('[TemplatePreference] 用户未登录，跳过云端偏好设置保存');
      return;
    }

    const userId = userData.user.id;

    // 使用批量UPSERT提高性能
    const upsertData = changedPreferences.map(pref => ({
      user_id: userId,
      template_id: pref.templateId,
      template_type: pref.templateType,
      is_hidden: pref.isHidden,
      display_order: pref.displayOrder,
      updated_at: new Date().toISOString()
    }));

    log(`[TemplatePreference] 开始批量更新 ${upsertData.length} 个偏好设置到云端`);

    // 使用Supabase的upsert功能进行批量操作
    const { error } = await supabase
      .from('user_template_preferences')
      .upsert(upsertData, {
        onConflict: 'user_id,template_id',
        ignoreDuplicates: false
      });

    if (error) {
      logError('[TemplatePreference] 批量更新偏好设置失败:', error);
      throw error;
    }

    log(`[TemplatePreference] 成功批量更新 ${upsertData.length} 个偏好设置到云端`);
  } catch (error) {
    logError('[TemplatePreference] 批量更新偏好设置到云端失败:', error);
    throw error;
  }
};

/**
 * 从云端获取用户偏好设置
 */
const fetchUserPreferencesFromCloud = async (): Promise<LocalTemplatePreference[]> => {
  try {
    const { data: userData } = await authService.getCurrentUser();
    if (!userData.user) {
      log('[TemplatePreference] 用户未登录，跳过云端偏好设置获取');
      return [];
    }

    const { data, error } = await supabase
      .from('user_template_preferences')
      .select('*')
      .eq('user_id', userData.user.id)
      .order('display_order', { ascending: true });

    if (error) {
      logError('[TemplatePreference] 从云端获取偏好设置失败:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      return [];
    }

    // 转换为本地格式
    return data.map((item: any) => ({
      templateId: item.template_id,
      templateType: item.template_type as 'system' | 'user',
      isHidden: item.is_hidden,
      displayOrder: item.display_order,
    }));
  } catch (error) {
    logError('[TemplatePreference] 从云端获取偏好设置失败:', error);
    throw error;
  }
};

/**
 * 保存用户偏好设置到云端
 */
const saveUserPreferencesToCloud = async (preferences: LocalTemplatePreference[]): Promise<void> => {
  try {
    const { data: userData } = await authService.getCurrentUser();
    if (!userData.user) {
      log('[TemplatePreference] 用户未登录，跳过云端偏好设置保存');
      return;
    }

    const userId = userData.user.id;

    // 使用逐个UPSERT操作来避免唯一约束冲突
    for (const pref of preferences) {
      try {
        // 先尝试更新
        const { data: updateData, error: updateError } = await supabase
          .from('user_template_preferences')
          .update({
            template_type: pref.templateType,
            is_hidden: pref.isHidden,
            display_order: pref.displayOrder,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId)
          .eq('template_id', pref.templateId)
          .select();

        if (updateError && updateError.code !== 'PGRST116') {
          // PGRST116 表示没有找到记录，这种情况下我们需要插入
          logError('[TemplatePreference] 更新偏好设置失败:', updateError);
          throw updateError;
        }

        // 如果没有找到记录（updateData为空或null），则插入新记录
        if (!updateData || updateData.length === 0) {
          const { error: insertError } = await supabase
            .from('user_template_preferences')
            .insert({
              user_id: userId,
              template_id: pref.templateId,
              template_type: pref.templateType,
              is_hidden: pref.isHidden,
              display_order: pref.displayOrder,
            });

          if (insertError) {
            // 如果插入时遇到唯一约束冲突，可能是并发操作导致，忽略这种错误
            if (insertError.code === '23505') {
              warn(`[TemplatePreference] 偏好设置已存在，跳过插入: ${pref.templateId}`);
            } else {
              logError('[TemplatePreference] 插入偏好设置失败:', insertError);
              throw insertError;
            }
          }
        }
      } catch (prefError) {
        logError(`[TemplatePreference] 处理偏好设置 ${pref.templateId} 失败:`, prefError);
        // 继续处理其他偏好设置，不中断整个过程
      }
    }

    log(`[TemplatePreference] 成功处理 ${preferences.length} 个偏好设置到云端`);
  } catch (error) {
    logError('[TemplatePreference] 保存偏好设置到云端失败:', error);
    throw error;
  }
};

/**
 * 从本地存储获取偏好设置
 */
const getPreferencesFromLocal = async (): Promise<LocalTemplatePreference[]> => {
  try {
    const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.TEMPLATE_PREFERENCES);
    return jsonValue ? JSON.parse(jsonValue) : [];
  } catch (error) {
    logError('[TemplatePreference] 从本地获取偏好设置失败:', error);
    return [];
  }
};

/**
 * 保存偏好设置到本地存储
 */
const savePreferencesToLocal = async (preferences: LocalTemplatePreference[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.TEMPLATE_PREFERENCES, JSON.stringify(preferences));
    await AsyncStorage.setItem(STORAGE_KEYS.PREFERENCES_LAST_SYNC, Date.now().toString());
  } catch (error) {
    logError('[TemplatePreference] 保存偏好设置到本地失败:', error);
    throw error;
  }
};

// 防止重复同步的标志
let syncInProgress = false;

/**
 * 登录后同步偏好设置
 */
export const syncUserPreferencesOnLogin = async (): Promise<void> => {
  // 防止重复同步
  if (syncInProgress) {
    log('[TemplatePreference] 偏好设置同步正在进行中，跳过');
    return;
  }

  try {
    syncInProgress = true;
    log('[TemplatePreference] 开始登录后偏好设置同步...');
    
    // 获取本地偏好设置
    const localPreferences = await getPreferencesFromLocal();
    
    // 获取云端偏好设置
    let cloudPreferences: LocalTemplatePreference[] = [];
    try {
      cloudPreferences = await fetchUserPreferencesFromCloud();
    } catch (cloudError) {
      warn('[TemplatePreference] 获取云端偏好设置失败，将使用本地设置:', cloudError);
    }
    
    if (cloudPreferences.length === 0 && localPreferences.length > 0) {
      // 云端没有，本地有 → 上传本地设置到云端
      try {
        await saveUserPreferencesToCloud(localPreferences);
        log('[TemplatePreference] 本地偏好设置已上传到云端');
      } catch (uploadError) {
        warn('[TemplatePreference] 上传本地偏好设置到云端失败:', uploadError);
      }
    } else if (cloudPreferences.length > 0) {
      // 云端有 → 使用云端设置覆盖本地
      try {
        await savePreferencesToLocal(cloudPreferences);
        log('[TemplatePreference] 云端偏好设置已同步到本地');
      } catch (saveError) {
        warn('[TemplatePreference] 保存云端偏好设置到本地失败:', saveError);
      }
    }
    
    log('[TemplatePreference] 偏好设置同步完成');
  } catch (error) {
    logError('[TemplatePreference] 登录后偏好设置同步失败:', error);
  } finally {
    syncInProgress = false;
  }
}; 