/**
 * 最终验证脚本
 * 综合验证整个录音文件保存和读取逻辑升级的完成情况
 */
import { validateUpgrade } from './upgradeValidation';
import { validateServiceIntegration, generateMigrationCompletionReport } from './integrationValidation';
import { testRecordingSaveFlow } from './recordingSaveTest';
import { testAudioFilePaths } from './audioPathTest';
import { testRecordingFilePath } from './recordingPathTest';
import { testRecordingUriConsistency } from './recordingUriTest';
import { testTranscriptionUriSelection } from './transcriptionUriTest';
import { testSmartPathFixLogic } from './pathFixTest';
import { dataMigrationService } from './dataMigrationService';
import { log, error as logError } from '@/services/logService';

/**
 * 运行最终的综合验证
 * @returns 验证结果
 */
export async function runFinalValidation(): Promise<{
  success: boolean;
  upgradeValidation: Awaited<ReturnType<typeof validateUpgrade>>;
  integrationValidation: Awaited<ReturnType<typeof validateServiceIntegration>>;
  recordingSaveTest: Awaited<ReturnType<typeof testRecordingSaveFlow>>;
  audioPathTest: Awaited<ReturnType<typeof testAudioFilePaths>>;
  recordingPathTest: Awaited<ReturnType<typeof testRecordingFilePath>>;
  recordingUriTest: Awaited<ReturnType<typeof testRecordingUriConsistency>>;
  transcriptionUriTest: ReturnType<typeof testTranscriptionUriSelection>;
  pathFixTest: ReturnType<typeof testSmartPathFixLogic>;
  migrationStatus: Awaited<ReturnType<typeof dataMigrationService.getMigrationStatus>>;
  completionReport: string;
}> {
  try {
    log('🚀 开始最终综合验证...');
    
    // 1. 运行升级验证
    log('📋 步骤 1: 运行升级验证');
    const upgradeValidation = await validateUpgrade();
    
    // 2. 运行集成验证
    log('📋 步骤 2: 运行服务集成验证');
    const integrationValidation = await validateServiceIntegration();

    // 3. 运行录音保存流程测试
    log('📋 步骤 3: 运行录音保存流程测试');
    const recordingSaveTest = await testRecordingSaveFlow();

    // 4. 运行录音文件路径测试
    log('📋 步骤 4: 运行录音文件路径测试');
    const audioPathTest = await testAudioFilePaths();

    // 5. 运行录音路径配置测试
    log('📋 步骤 5: 运行录音路径配置测试');
    const recordingPathTest = await testRecordingFilePath();

    // 6. 运行录音URI一致性测试
    log('📋 步骤 6: 运行录音URI一致性测试');
    const recordingUriTest = await testRecordingUriConsistency();

    // 7. 运行转写URI选择逻辑测试
    log('📋 步骤 7: 运行转写URI选择逻辑测试');
    const transcriptionUriTest = testTranscriptionUriSelection();

    // 8. 运行智能路径修复逻辑测试
    log('📋 步骤 8: 运行智能路径修复逻辑测试');
    const pathFixTest = testSmartPathFixLogic();

    // 9. 检查数据迁移状态
    log('📋 步骤 9: 检查数据迁移状态');
    const migrationStatus = await dataMigrationService.getMigrationStatus();
    
    // 10. 生成完成报告
    log('📋 步骤 10: 生成完成报告');
    const completionReport = await generateMigrationCompletionReport();

    // 11. 综合评估
    const success = upgradeValidation.success && integrationValidation.success && recordingSaveTest.success && audioPathTest.success && recordingPathTest.success && recordingUriTest.success && transcriptionUriTest.success && pathFixTest.success;
    
    log(`🎯 最终验证结果: ${success ? '✅ 成功' : '❌ 失败'}`);
    
    return {
      success,
      upgradeValidation,
      integrationValidation,
      recordingSaveTest,
      audioPathTest,
      recordingPathTest,
      recordingUriTest,
      transcriptionUriTest,
      pathFixTest,
      migrationStatus,
      completionReport,
    };
    
  } catch (error) {
    logError('最终验证过程出现错误:', error);
    throw error;
  }
}

/**
 * 打印最终验证报告
 * @param result 验证结果
 */
export function printFinalValidationReport(result: Awaited<ReturnType<typeof runFinalValidation>>): void {
  console.log('\n🎉 录音文件保存和读取逻辑整体升级 - 最终验证报告');
  console.log('=' .repeat(80));
  
  // 总体状态
  console.log(`\n🎯 总体状态: ${result.success ? '✅ 升级成功完成' : '❌ 升级未完全成功'}`);
  
  // 各项验证结果
  console.log('\n📊 详细验证结果:');
  console.log(`  1. 升级验证: ${result.upgradeValidation.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  2. 服务集成验证: ${result.integrationValidation.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  3. 录音保存测试: ${result.recordingSaveTest.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  4. 录音文件路径测试: ${result.audioPathTest.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  5. 录音路径配置测试: ${result.recordingPathTest.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  6. 录音URI一致性测试: ${result.recordingUriTest.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  7. 转写URI选择测试: ${result.transcriptionUriTest.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`  8. 数据迁移: ${result.migrationStatus.migrationCompleted ? '✅ 已完成' : '⚠️ 未完成'}`);
  
  // 数据统计
  console.log('\n📈 数据统计:');
  console.log(`  当前用户ID: ${result.upgradeValidation.summary.currentUserId}`);
  console.log(`  历史记录数量: ${result.upgradeValidation.summary.recordCount}`);
  console.log(`  SQLite记录数: ${result.migrationStatus.sqliteRecordsCount}`);
  console.log(`  遗留记录数: ${result.migrationStatus.legacyRecordsCount}`);
  
  // 服务状态
  console.log('\n🔧 服务状态:');
  console.log(`  用户ID服务: ${result.upgradeValidation.summary.userIdServiceReady ? '✅' : '❌'}`);
  console.log(`  历史记录服务: ${result.upgradeValidation.summary.historyServiceReady ? '✅' : '❌'}`);
  console.log(`  storageService: ${result.integrationValidation.summary.storageServiceReady ? '✅' : '❌'}`);
  console.log(`  页面集成: ${result.integrationValidation.summary.historyServiceMigrated ? '✅' : '❌'}`);
  console.log(`  类型一致性: ${result.integrationValidation.summary.typesConsistent ? '✅' : '❌'}`);
  
  // 错误和警告
  if (result.upgradeValidation.errors.length > 0) {
    console.log('\n❌ 升级验证错误:');
    result.upgradeValidation.errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
  }
  
  if (result.integrationValidation.issues.length > 0) {
    console.log('\n❌ 集成验证问题:');
    result.integrationValidation.issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
  }

  if (result.recordingSaveTest.issues.length > 0) {
    console.log('\n❌ 录音保存测试问题:');
    result.recordingSaveTest.issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
  }

  if (result.audioPathTest.issues.length > 0) {
    console.log('\n❌ 录音文件路径测试问题:');
    result.audioPathTest.issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
  }

  if (result.recordingPathTest.issues.length > 0) {
    console.log('\n❌ 录音路径配置测试问题:');
    result.recordingPathTest.issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
  }
  
  if (result.upgradeValidation.warnings.length > 0) {
    console.log('\n⚠️ 警告信息:');
    result.upgradeValidation.warnings.forEach((warning, index) => {
      console.log(`  ${index + 1}. ${warning}`);
    });
  }
  
  // 完成报告
  console.log(result.completionReport);
  
  console.log('=' .repeat(80));
  
  if (result.success) {
    console.log('🎉 恭喜！录音文件保存和读取逻辑整体升级已成功完成！');
    console.log('\n✨ 升级带来的改进:');
    console.log('  • 📱 覆盖安装后录音文件仍可播放');
    console.log('  • 👥 不同用户的录音文件完全隔离');
    console.log('  • 🗄️ 使用SQLite数据库提升性能');
    console.log('  • 🔒 保留完整的VIP权限控制');
    console.log('  • 🔄 自动数据迁移，无需用户操作');
    console.log('  • 🔧 现有代码无需修改即可享受新功能');
    console.log('\n🚀 系统已准备就绪，用户可以正常使用所有功能！');
  } else {
    console.log('⚠️ 升级过程中发现问题，请检查上述错误信息并进行修复。');
    console.log('💡 大部分功能应该仍可正常工作，建议优先修复关键问题。');
  }
}

/**
 * 运行最终验证并打印详细报告
 */
export async function runFinalValidationWithReport(): Promise<boolean> {
  try {
    const result = await runFinalValidation();
    printFinalValidationReport(result);
    return result.success;
  } catch (error) {
    console.error('最终验证过程出现异常:', error);
    return false;
  }
}

/**
 * 快速状态检查（用于开发调试）
 */
export async function quickStatusCheck(): Promise<void> {
  try {
    console.log('🔍 快速状态检查...');
    
    // 检查关键服务
    const storageService = await import('@/services/storageService');
    console.log('✅ storageService 可用');
    
    const newHistoryService = await import('@/services/newHistoryService');
    console.log('✅ newHistoryService 可用');
    
    const userIdService = await import('@/services/userIdService');
    console.log('✅ userIdService 可用');
    
    // 检查数据迁移状态
    const migrationStatus = await dataMigrationService.getMigrationStatus();
    console.log(`📊 迁移状态: ${migrationStatus.migrationCompleted ? '已完成' : '未完成'}`);
    console.log(`📊 SQLite记录: ${migrationStatus.sqliteRecordsCount}`);
    console.log(`📊 遗留记录: ${migrationStatus.legacyRecordsCount}`);
    
    console.log('✅ 快速状态检查完成');
  } catch (error) {
    console.error('❌ 快速状态检查失败:', error);
  }
}
