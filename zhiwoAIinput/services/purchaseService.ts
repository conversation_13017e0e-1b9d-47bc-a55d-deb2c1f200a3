/**
 * 购买服务 - RevenueCat集成
 * 处理应用内购买、订阅和VIP状态管理
 */
import { Platform } from 'react-native';
import Purchases, { 
  PurchasesPackage, 
  CustomerInfo, 
  PURCHASE_TYPE
} from 'react-native-purchases';
import { setVIPStatus } from '@/store/slices/authSlice';
import { store } from '@/store';
import Constants from 'expo-constants';
import { supabase } from './supabaseService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { saveVipStatus } from './storageService';
import { log, warn, error as logError }  from '@/services/logService';

// RevenueCat API 密钥
// 从环境变量或Expo常量中获取
const REVENUECAT_API_KEYS = {
  ios: Constants.expoConfig?.extra?.revenueCatIosApiKey || process.env.REVENUE_CAT_IOS_API_KEY || '',
  android: Constants.expoConfig?.extra?.revenueCatAndroidApiKey || process.env.REVENUE_CAT_ANDROID_API_KEY || '',
};

// 订阅包ID
export const SUBSCRIPTION_PACKAGES = {
  MONTHLY: 'monthly_vip',  // 月度VIP订阅
  YEARLY: 'yearly_vip',    // 年度VIP订阅
};

// 订阅类型
export const SUBSCRIPTION_TYPE = {
  MONTHLY: 'monthly',
  YEARLY: 'yearly'
};

// 初始化标志
let isInitialized = false;

/**
 * 购买服务
 */
export const purchaseService = {
  /**
   * 初始化RevenueCat SDK
   * 每次应用启动时应该调用此方法
   * @param userId 当前登录用户ID (可选)
   */
  initialize: async (userId?: string) => {
    try {
      if (isInitialized) {
        log('[PurchaseService] SDK已初始化，跳过');
        return;
      }

      // 获取对应平台的API密钥
      const apiKey = Platform.select({
        ios: REVENUECAT_API_KEYS.ios,
        android: REVENUECAT_API_KEYS.android,
        default: REVENUECAT_API_KEYS.ios,
      });
      
      if (!apiKey) {
        warn('[PurchaseService] RevenueCat API密钥未配置，跳过初始化（开发环境可选）');
        // 在开发环境中，如果没有API密钥，设为已初始化状态但不执行实际初始化
        // 这样可以避免重复尝试初始化
        isInitialized = true;
        return false;
      }
      
      // 配置RevenueCat SDK
      Purchases.configure({
        apiKey,
        appUserID: userId, // 如果有用户ID就使用，否则使用匿名ID
      });
      
      isInitialized = true;
      log('[PurchaseService] RevenueCat SDK初始化成功');
      
      // 如果有用户ID，设置用户标识
      if (userId) {
        await purchaseService.identifyUser(userId);
      }

      // 同步当前用户的购买状态
      await purchaseService.syncPurchases();
      
      return true;
    } catch (error) {
      logError('[PurchaseService] 初始化RevenueCat SDK失败:', error);
      return false;
    }
  },
  
  /**
   * 设置用户标识
   * 用户登录后调用此方法，将匿名购买转移到已登录用户
   * @param userId 用户ID
   */
  identifyUser: async (userId: string) => {
    try {
      if (!isInitialized) {
        const initialized = await purchaseService.initialize(userId);
        if (!initialized) {
          log('[PurchaseService] RevenueCat未初始化，跳过用户标识设置');
          return null;
        }
      }

      // 检查API密钥是否可用
      const apiKey = Platform.select({
        ios: REVENUECAT_API_KEYS.ios,
        android: REVENUECAT_API_KEYS.android,
        default: REVENUECAT_API_KEYS.ios,
      });
      
      if (!apiKey) {
        log('[PurchaseService] RevenueCat API密钥未配置，跳过用户标识设置');
        return null;
      }

      log('[PurchaseService] 设置用户标识:', userId);
      
      // 设置RevenueCat用户标识
      const { customerInfo } = await Purchases.logIn(userId);
      
      // 更新Redux中的VIP状态
      purchaseService.updateVIPStatus(customerInfo);
      
      // 从服务器获取VIP状态，确保前后端一致
      await purchaseService.getServerVIPStatus();
      
      return customerInfo;
    } catch (error) {
      logError('[PurchaseService] 设置用户标识失败:', error);
      throw error;
    }
  },
  
  /**
   * 重置用户标识
   * 用户登出时调用此方法
   */
  resetUser: async () => {
    try {
      log('[PurchaseService] 重置用户标识');
      await Purchases.logOut();
      
      // 清除Redux中的VIP状态
      store.dispatch(setVIPStatus(false));
      
      return true;
    } catch (error) {
      logError('[PurchaseService] 重置用户标识失败:', error);
      return false;
    }
  },
  
  /**
   * 同步购买
   * 每次应用启动或恢复前台时调用此方法
   */
  syncPurchases: async () => {
    try {
      log('[PurchaseService] 同步购买状态');
      
      // 首先检查用户登录状态
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        log('[PurchaseService] 用户未登录，重置VIP状态为false');
        store.dispatch(setVIPStatus(false));
        return null;
      }
      
      // 检查用户在数据库中是否存在
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', user.id)
        .single();
        
      if (profileError && (profileError.code === 'PGRST116' || profileError.message?.includes('no rows'))) {
        log('[PurchaseService] 用户在数据库中不存在，重置VIP状态为false');
        store.dispatch(setVIPStatus(false));
        
        // 如果用户在Supabase中不存在，可能需要登出
        // 但不在此函数中处理，而是交给checkSession去处理
        return null;
      }
      
      // 获取最新的订阅信息
      const customerInfo = await Purchases.getCustomerInfo();
      
      // 更新VIP状态 - 先使用客户端状态
      const clientVipStatus = purchaseService.checkIsVIP(customerInfo);
      
      // 同时从服务器获取VIP状态，以确保客户端和服务器一致
      const serverVipStatus = await purchaseService.getServerVIPStatus();
      
      // 如果客户端和服务器状态不一致，优先使用服务器状态
      if (clientVipStatus !== serverVipStatus) {
        log('[PurchaseService] 客户端VIP状态与服务器不一致，使用服务器状态');
        store.dispatch(setVIPStatus(serverVipStatus));
      }
      
      return customerInfo;
    } catch (error) {
      logError('[PurchaseService] 同步购买失败:', error);
      // 出错时，默认设置为非VIP状态
      store.dispatch(setVIPStatus(false));
      throw error;
    }
  },
  
  /**
   * 更新VIP状态
   * @param customerInfo 客户信息
   */
  updateVIPStatus: async (customerInfo: CustomerInfo) => {
    try {
      // 检查是否有活跃的VIP权益
      const isVIP = purchaseService.checkIsVIP(customerInfo);
      
      // 更新Redux状态
      store.dispatch(setVIPStatus(isVIP));
      
      // 将交易信息同步到Supabase数据库
      await purchaseService.syncTransactionToServer(customerInfo);
      
      // 如果客户端检测到VIP权益，还应查询服务器以确保一致性
      if (isVIP) {
        // 获取当前用户ID
        const { data: { user } } = await supabase.auth.getUser();
        
        if (user) {
          log('[PurchaseService] 从服务器获取VIP状态以确保同步');
          
          try {
            // 查询用户资料
            const { data: profile } = await supabase
              .from('profiles')
              .select('is_vip')
              .eq('id', user.id)
              .single();
              
            if (profile && profile.is_vip === false) {
              log('[PurchaseService] 服务器VIP状态为false，但客户端检测为true，需要同步');
              
              // 如果服务器不一致，则重新同步
              await purchaseService.syncTransactionToServer(customerInfo, true);
            }
          } catch (err) {
            logError('[PurchaseService] 获取服务器VIP状态出错:', err);
          }
        }
      }
      
      log('[PurchaseService] VIP状态已更新:', isVIP);
      
      return isVIP;
    } catch (error) {
      logError('[PurchaseService] 更新VIP状态失败:', error);
      return false;
    }
  },
  
  /**
   * 检查用户是否为VIP
   * @param customerInfo 客户信息
   */
  checkIsVIP: (customerInfo: CustomerInfo): boolean => {
    // 获取所有活跃权益的名称
    const activeEntitlements = Object.keys(customerInfo?.entitlements?.active || {});
    
    // 检查是否有'vip'或'VIP'等不同大小写形式的权益
    const hasVipEntitlement = activeEntitlements.some(
      entitlement => entitlement.toLowerCase() === 'vip'
    );
    
    // 增加日志，记录更详细的VIP状态信息
    log('[PurchaseService] 检查VIP权益详情:', {
      activeEntitlements,
      entitlementInfo: customerInfo?.entitlements?.active?.VIP || customerInfo?.entitlements?.active?.vip,
      hasVipEntitlement
    });
    
    return hasVipEntitlement;
  },
  
  /**
   * 将交易信息同步到服务器
   * @param customerInfo 客户信息
   * @param force 是否强制同步
   */
  syncTransactionToServer: async (customerInfo: CustomerInfo, force: boolean = false) => {
    try {
      // 获取当前用户ID
      const { data: { user } } = await supabase.auth.getUser();
      
      // 如果用户未登录，跳过同步
      if (!user) {
        log('[PurchaseService] 用户未登录，跳过同步交易信息');
        return;
      }
      
      // 获取活跃的权益
      const activeEntitlements = customerInfo?.entitlements?.active || {};
      
      // 尝试获取VIP权益，不区分大小写
      const activeEntitlementNames = Object.keys(activeEntitlements);
      const vipEntitlementName = activeEntitlementNames.find(name => name.toLowerCase() === 'vip');
      const vipEntitlement = vipEntitlementName ? activeEntitlements[vipEntitlementName] : null;
      
      // 如果有活跃的VIP权益，将交易信息同步到服务器
      if (vipEntitlement || force) {
        const productId = vipEntitlement?.productIdentifier || '';
        
        // 获取原始交易ID和交易时间
        const originalAppUserId = customerInfo.originalAppUserId || '';
        const latestTransactionInfo = vipEntitlement?.latestPurchaseDate ? 
          new Date(vipEntitlement.latestPurchaseDate).toISOString() : new Date().toISOString();
        
        log('[PurchaseService] 准备同步交易信息:', {
          userId: user.id,
          productId,
          originalAppUserId,
          latestPurchaseDate: latestTransactionInfo,
          hasVipEntitlement: !!vipEntitlement
        });
        
        // 构建交易数据
        const transactionData = {
          user_id: user.id,
          product_id: productId,
          store: Platform.OS === 'ios' ? 'app_store' : 'play_store',
          // 从RevenueCat API获取的唯一标识符，用作交易ID
          transaction_id: originalAppUserId,
          original_transaction_id: originalAppUserId,
          environment: __DEV__ ? 'sandbox' : 'production',
          status: 'active',
          // 使用权益的过期时间
          expires_at: vipEntitlement?.expirationDate ? new Date(vipEntitlement.expirationDate).toISOString() : null,
          purchase_date: latestTransactionInfo,
        };
        
        try {
          // 首先检查是否已存在相同transaction_id的记录
          const { data: existingSubscription, error: queryError } = await supabase
            .from('subscriptions')
            .select('id')
            .eq('transaction_id', originalAppUserId)
            .maybeSingle();

          if (queryError) {
            // 如果是权限错误，我们可以忽略它并继续，尝试直接更新profiles表
            log('[PurchaseService] 查询现有订阅记录失败:', queryError);
            log('[PurchaseService] 跳过订阅记录更新，仅更新用户资料');
          } else if (existingSubscription) {
            // 如果已存在记录，则更新它
            try {
              const { error } = await supabase
                .from('subscriptions')
                .update(transactionData)
                .eq('id', existingSubscription.id);
              
              if (error) {
                log('[PurchaseService] 更新订阅记录失败:', error);
              } else {
                log('[PurchaseService] 交易信息已同步到服务器');
              }
            } catch (updateError) {
              log('[PurchaseService] 更新订阅操作失败:', updateError);
            }
          } else {
            // 否则创建新记录
            try {
              const { error } = await supabase
                .from('subscriptions')
                .insert(transactionData);

              if (error) {
                log('[PurchaseService] 创建订阅记录失败:', error);
              } else {
                log('[PurchaseService] 交易信息已创建并同步到服务器');
              }
            } catch (insertError) {
              log('[PurchaseService] 创建订阅操作失败:', insertError);
            }
          }
        } catch (dbAccessError) {
          log('[PurchaseService] 访问数据库失败，可能是权限问题:', dbAccessError);
        }
        
        // 无论订阅表操作是否成功，都尝试更新用户资料的VIP状态
        try {
          const { error: profileError } = await supabase
            .from('profiles')
            .update({
              is_vip: true,
              vip_product_id: productId,
              vip_expires_at: vipEntitlement?.expirationDate ? new Date(vipEntitlement.expirationDate).toISOString() : null,
              vip_updated_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id);
            
          if (profileError) {
            log('[PurchaseService] 更新用户VIP状态失败:', profileError);
          } else {
            log('[PurchaseService] 用户VIP状态已更新');
          }
        } catch (profileUpdateError) {
          log('[PurchaseService] 更新用户资料操作失败:', profileUpdateError);
        }
      }
    } catch (error) {
      log('[PurchaseService] 同步交易信息失败:', error);
    }
  },
  
  /**
   * 获取可用的订阅套餐
   * 在显示支付墙时调用
   */
  getOfferings: async () => {
    try {
      const offerings = await Purchases.getOfferings();
      log('[PurchaseService] 获取订阅套餐成功:', offerings);
      return offerings;
    } catch (error) {
      logError('[PurchaseService] 获取订阅套餐失败:', error);
      throw error;
    }
  },
  
  /**
   * 购买套餐
   * @param packageToPurchase 要购买的套餐
   */
  purchasePackage: async (packageToPurchase: PurchasesPackage) => {
    try {
      // API返回结果类型可能与声明的类型不完全匹配，使用any避免类型错误
      const purchaseResult = await Purchases.purchasePackage(packageToPurchase) as any;
      log('[PurchaseService] 购买成功:', purchaseResult);
      
      // 确保我们有customerInfo
      if (purchaseResult && purchaseResult.customerInfo) {
        // 更新VIP状态
        purchaseService.updateVIPStatus(purchaseResult.customerInfo);
        
        // 等待1秒钟后再次从服务器获取状态，确保webhook已经处理完成
        setTimeout(async () => {
          await purchaseService.getServerVIPStatus();
        }, 1000);
        
        return purchaseResult.customerInfo;
      } else {
        warn('[PurchaseService] 购买成功但未获取到customerInfo');
        
        // 从服务器获取VIP状态
        await purchaseService.getServerVIPStatus();
        
        return await Purchases.getCustomerInfo();
      }
    } catch (error) {
      logError('[PurchaseService] 购买失败:', error);
      throw error;
    }
  },
  
  /**
   * 恢复购买
   * 用户可以恢复之前在不同设备上购买的订阅
   */
  restorePurchases: async () => {
    try {
      const customerInfo = await Purchases.restorePurchases();
      log('[PurchaseService] 恢复购买成功:', customerInfo);
      
      // 更新VIP状态
      purchaseService.updateVIPStatus(customerInfo);
      
      return customerInfo;
    } catch (error) {
      logError('[PurchaseService] 恢复购买失败:', error);
      throw error;
    }
  },
  
  /**
   * 获取当前用户的订阅信息
   */
  getCustomerInfo: async () => {
    try {
      const customerInfo = await Purchases.getCustomerInfo();
      log('[PurchaseService] 获取用户订阅信息成功');
      
      // 更新VIP状态
      purchaseService.updateVIPStatus(customerInfo);
      
      return customerInfo;
    } catch (error) {
      logError('[PurchaseService] 获取用户订阅信息失败:', error);
      throw error;
    }
  },
  
  /**
   * 直接从服务器获取用户的VIP状态
   * 这是一个备用机制，确保即使RevenueCat客户端检查失败，也能获取正确的VIP状态
   */
  getServerVIPStatus: async () => {
    try {
      // 获取当前用户ID
      const { data: { user } } = await supabase.auth.getUser();
      
      // 如果用户未登录，跳过检查
      if (!user) {
        log('[PurchaseService] 用户未登录，跳过服务器VIP状态检查');
        store.dispatch(setVIPStatus(false));
        return false;
      }
      
      log('[PurchaseService] 从服务器获取VIP状态');
      
      try {
        // 查询用户资料
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('is_vip, vip_expires_at')
          .eq('id', user.id)
          .single();
        
        if (error) {
          log('[PurchaseService] 获取服务器VIP状态出错:', error);
          
          // 检查是否是因为找不到记录导致的错误
          if (error.code === 'PGRST116' || error.message?.includes('no rows')) {
            log('[PurchaseService] 用户资料不存在，尝试创建默认资料');
            
            // 创建默认用户资料
            const { error: insertError } = await supabase
              .from('profiles')
              .insert({
                id: user.id,
                email: user.email,
                is_vip: false,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              });
              
            if (insertError) {
              logError('[PurchaseService] 创建用户资料失败:', insertError);
              // 即使创建失败，也应将VIP状态设为false，因为用户资料不存在
              store.dispatch(setVIPStatus(false));
              return false;
            } else {
              log('[PurchaseService] 已创建默认用户资料');
              // 创建成功后返回默认VIP状态(false)
              store.dispatch(setVIPStatus(false));
              return false;
            }
          }
          
          // 如果是其他数据库错误（非找不到记录），考虑是否真的无法连接到服务器
          if (error.code && ['PGRST', 'PGRES', '08'].some(prefix => error.code?.startsWith(prefix))) {
            log('[PurchaseService] 数据库连接错误，可能是暂时性问题，使用客户端状态作为备用');
            // 只有在确认是数据库连接问题时才使用客户端状态
            return store.getState().auth.isVIP || false;
          }
          
          // 其他类型的错误，认为用户不是VIP
          log('[PurchaseService] 其他类型错误，重置VIP状态为false');
          store.dispatch(setVIPStatus(false));
          return false;
        }
        
        const isVip = profile?.is_vip || false;
        
        // 检查VIP是否过期
        const isExpired = profile?.vip_expires_at 
          ? new Date(profile.vip_expires_at) < new Date() 
          : false;
        
        const finalVipStatus = isVip && !isExpired;
        
        log('[PurchaseService] 服务器VIP状态:', {
          isVip,
          vipExpiresAt: profile?.vip_expires_at,
          isExpired,
          finalStatus: finalVipStatus
        });
        
        // 更新Redux状态
        store.dispatch(setVIPStatus(finalVipStatus));
        
        // 同时更新本地存储的VIP状态，确保包含过期时间
        try {
          const vipStatusForStorage = {
            isVip: finalVipStatus,
            expiryDate: profile?.vip_expires_at ? new Date(profile.vip_expires_at).getTime() : undefined
          };
          await saveVipStatus(vipStatusForStorage);
          log('[PurchaseService] 已更新本地存储的VIP状态', vipStatusForStorage);
        } catch (storageError) {
          logError('[PurchaseService] 更新本地VIP状态失败:', storageError);
        }
        
        return finalVipStatus;
      } catch (dbError) {
        log('[PurchaseService] 数据库操作失败:', dbError);
        // 如果是网络或连接问题，回退到客户端状态
        if (dbError instanceof Error && 
            (dbError.message.includes('network') || 
             dbError.message.includes('connection') || 
             dbError.message.includes('timeout'))) {
          log('[PurchaseService] 确认为网络连接问题，暂时使用客户端状态');
          return store.getState().auth.isVIP || false;
        }
        
        // 其他未知错误，重置为非VIP
        log('[PurchaseService] 未知错误，重置VIP状态为false');
        store.dispatch(setVIPStatus(false));
        return false;
      }
    } catch (error) {
      log('[PurchaseService] 获取服务器VIP状态失败:', error);
      // 只有在确认是网络问题时才使用客户端状态
      if (error instanceof Error && 
          (error.message.includes('network') || 
           error.message.includes('connection') || 
           error.message.includes('timeout'))) {
        return store.getState().auth.isVIP || false;
      }
      
      // 其他错误，重置为非VIP
      store.dispatch(setVIPStatus(false));
      return false;
    }
  },
};

export default purchaseService; 