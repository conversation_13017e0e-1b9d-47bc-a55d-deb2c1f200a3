/**
 * 安全的 AI 服务
 * 通过 Supabase 边缘函数调用 AI API，避免在客户端暴露 API 密钥
 */
import { log, error as logError } from '@/services/logService';
import { supabase } from './supabaseService';
import Constants from 'expo-constants';

// 边缘函数 URL
const EDGE_FUNCTION_URL = `${Constants.expoConfig?.extra?.supabaseUrl}/functions/v1/ai-proxy`;

// 请求接口定义
interface SecureAIRequest {
  model: string;
  messages: {
    role: string;
    content: string;
  }[];
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  stream?: boolean;
  n?: number;
  response_format?: {
    type: string;
  };
}

// 响应接口定义
interface SecureAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
      reasoning_content?: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  system_fingerprint?: string;
}

// 错误响应接口
interface AIErrorResponse {
  error: string;
  details?: string;
}

/**
 * 通过边缘函数安全地调用 AI API
 * @param request AI 请求参数
 * @param abortSignal 取消信号
 * @returns AI 响应结果
 */
export const callSecureAI = async (
  request: SecureAIRequest,
  abortSignal?: AbortSignal
): Promise<{ text: string; usage?: any; error?: Error }> => {
  try {
    // 获取当前用户的会话
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    
    if (authError || !session) {
      throw new Error('用户未登录，无法调用 AI 服务');
    }

    log('=== 安全 AI 服务调试信息 ===');
    log('边缘函数 URL:', EDGE_FUNCTION_URL);
    log('使用模型:', request.model);
    log('请求消息数量:', request.messages.length);
    log('用户 ID:', session.user.id);

    // 调用边缘函数
    const response = await fetch(EDGE_FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      },
      body: JSON.stringify(request),
      signal: abortSignal,
    });

    log('边缘函数响应状态:', response.status);

    if (!response.ok) {
      let errorMessage = `API 调用失败: ${response.status}`;
      
      try {
        const errorData: AIErrorResponse = await response.json();
        errorMessage = errorData.error || errorMessage;
        
        // 处理特定错误类型
        if (response.status === 401) {
          errorMessage = '身份验证失败，请重新登录';
        } else if (response.status === 403) {
          errorMessage = errorData.error || '权限不足';
        } else if (response.status === 429) {
          errorMessage = errorData.error || 'API 调用频率超限，请稍后再试';
        } else if (response.status === 503) {
          errorMessage = 'AI 服务暂时不可用，请稍后再试';
        }
        
        logError('边缘函数错误响应:', errorData);
      } catch (parseError) {
        logError('解析错误响应失败:', parseError);
      }

      throw new Error(errorMessage);
    }

    // 解析成功响应
    const responseData: SecureAIResponse = await response.json();

    log('AI 调用成功，tokens 使用情况:', responseData.usage);

    // 检查响应数据是否有效
    if (!responseData.choices || responseData.choices.length === 0) {
      throw new Error('AI 响应数据无效');
    }

    return {
      text: responseData.choices[0].message.content.trim(),
      usage: responseData.usage
    };

  } catch (error) {
    logError('安全 AI 服务调用失败:', error);

    // 如果是取消请求导致的错误，直接抛出
    if (error instanceof Error && 
        (error.name === 'AbortError' || error.message.includes('abort'))) {
      throw error;
    }

    // 返回错误信息
    return {
      text: '',
      error: error instanceof Error ? error : new Error('未知错误')
    };
  }
};

/**
 * 使用安全 AI 服务优化文本
 * @param text 用户输入的文本
 * @param templatePrompt 模板提示词
 * @param model 使用的AI模型
 * @param abortSignal 可选的AbortSignal，用于取消请求
 * @param requestId 请求ID，用于跟踪特定请求
 * @returns 优化后的文本和请求ID
 */
export const optimizeTextSecurely = async (
  text: string,
  templatePrompt: string,
  model: string = 'Qwen/Qwen2.5-7B-Instruct',
  abortSignal?: AbortSignal,
  requestId?: string
): Promise<{ text: string, requestId?: string }> => {
  try {
    log(`安全 AI 优化开始，请求 ID: ${requestId || '未设置'}`);
    log(`使用模型: ${model}`);
    log(`文本长度: ${text.length}`);
    log(`模板提示词长度: ${templatePrompt.length}`);

    // 构建请求数据
    const requestData: SecureAIRequest = {
      model,
      messages: [
        {
          role: "system",
          content: templatePrompt
        },
        {
          role: "user",
          content: text
        }
      ],
      temperature: 0.7,
      max_tokens: 2048,
      top_p: 0.9,
      stream: false,
      n: 1,
      response_format: {
        type: "text"
      }
    };

    // 调用安全 AI 服务
    const result = await callSecureAI(requestData, abortSignal);

    if (result.error) {
      logError('安全 AI 优化失败:', result.error);
      throw result.error;
    }

    log(`安全 AI 优化成功，请求 ID: ${requestId || '未设置'}`);
    log('优化后文本长度:', result.text.length);

    return {
      text: result.text,
      requestId
    };

  } catch (error) {
    logError(`安全 AI 文本优化失败，请求 ID: ${requestId || '未设置'}`, error);

    // 如果是取消请求导致的错误，直接抛出
    if (error instanceof Error &&
        (error.name === 'AbortError' || error.message.includes('abort'))) {
      throw error;
    }

    // 直接抛出API错误
    throw error;
  }
};

/**
 * 获取用户的 AI 使用统计
 * @param limit 获取记录的数量限制
 * @returns 使用统计数据
 */
export const getUserAIUsageStats = async (limit: number = 100) => {
  try {
    const { data, error } = await supabase
      .from('unified_proxy_logs')
      .select('*')
      .not('model', 'is', null) // 只获取 AI 相关的记录（有模型信息的）
      .order('request_timestamp', { ascending: false })
      .limit(limit);

    if (error) {
      logError('获取 AI 使用统计失败:', error);
      return null;
    }

    return data;
  } catch (error) {
    logError('获取 AI 使用统计异常:', error);
    return null;
  }
};

/**
 * 获取用户今日的 AI 使用情况
 * @returns 今日使用统计
 */
export const getTodayUsageStats = async () => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayISO = today.toISOString();

    const { data, error } = await supabase
      .from('unified_proxy_logs')
      .select('*')
      .not('model', 'is', null) // 只获取 AI 相关的记录
      .gte('request_timestamp', todayISO);

    if (error) {
      logError('获取今日使用统计失败:', error);
      return null;
    }

    // 计算统计信息
    const totalRequests = data.length;
    const totalTokens = data.reduce((sum, record) => sum + (record.total_tokens || 0), 0);
    const totalCost = data.reduce((sum, record) => sum + (record.cost_estimate || 0), 0);

    return {
      totalRequests,
      totalTokens,
      totalCost,
      records: data
    };
  } catch (error) {
    logError('获取今日使用统计异常:', error);
    return null;
  }
}; 