/**
 * 语音提示词服务
 * 用于管理语音转录API的提示词，支持云端获取和本地缓存机制
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { speechPromptService } from './supabaseService';
import { log, error as logError } from '@/services/logService';

// 缓存键名
const CACHE_KEYS = {
  CHINESE_GPT4O_MINI: 'knowme_speech_prompt_chinese_gpt4o_mini',
  CHINESE_WHISPER: 'knowme_speech_prompt_chinese_whisper',
  MULTILINGUAL_COMMON: 'knowme_speech_prompt_multilingual_common',
  VERSION_CHINESE_GPT4O_MINI: 'knowme_speech_prompt_version_chinese_gpt4o_mini',
  VERSION_CHINESE_WHISPER: 'knowme_speech_prompt_version_chinese_whisper',
  VERSION_MULTILINGUAL_COMMON: 'knowme_speech_prompt_version_multilingual_common',
  CACHE_TIME_CHINESE_GPT4O_MINI: 'knowme_speech_prompt_cache_time_chinese_gpt4o_mini',
  CACHE_TIME_CHINESE_WHISPER: 'knowme_speech_prompt_cache_time_chinese_whisper',
  CACHE_TIME_MULTILINGUAL_COMMON: 'knowme_speech_prompt_cache_time_multilingual_common',
};

// Fallback提示词
const FALLBACK_PROMPTS = {
  chinese_gpt4o_mini: '请准确转录语音内容，保持原始语言，添加标点符号。',
  chinese_whisper: '请准确转录语音内容，保持原始语言，添加标点符号。',
  multilingual_common: 'Please transcribe accurately, keep original language, add punctuation.'
};

// 缓存有效期（1小时）
const CACHE_DURATION = 60 * 60 * 1000;

/**
 * 语音提示词类型定义
 */
export interface SpeechPrompt {
  id: string;
  prompt_type: string;
  model_type: string;
  prompt_content: string;
  version: number;
  created_at: string;
  updated_at: string;
}

/**
 * 根据语言和模型获取提示词Key
 */
const getPromptKey = (language: string, model: string): keyof typeof FALLBACK_PROMPTS => {
  if (language === 'zh') {
    if (model === 'gpt-4o-mini-transcribe') {
      return 'chinese_gpt4o_mini';
    } else {
      return 'chinese_whisper';
    }
  } else {
    return 'multilingual_common';
  }
};

/**
 * 获取缓存相关的键名
 */
const getCacheKeys = (promptKey: string) => {
  switch (promptKey) {
    case 'chinese_gpt4o_mini':
      return {
        content: CACHE_KEYS.CHINESE_GPT4O_MINI,
        version: CACHE_KEYS.VERSION_CHINESE_GPT4O_MINI,
        time: CACHE_KEYS.CACHE_TIME_CHINESE_GPT4O_MINI
      };
    case 'chinese_whisper':
      return {
        content: CACHE_KEYS.CHINESE_WHISPER,
        version: CACHE_KEYS.VERSION_CHINESE_WHISPER,
        time: CACHE_KEYS.CACHE_TIME_CHINESE_WHISPER
      };
    case 'multilingual_common':
      return {
        content: CACHE_KEYS.MULTILINGUAL_COMMON,
        version: CACHE_KEYS.VERSION_MULTILINGUAL_COMMON,
        time: CACHE_KEYS.CACHE_TIME_MULTILINGUAL_COMMON
      };
    default:
      throw new Error(`未知的提示词类型: ${promptKey}`);
  }
};

/**
 * 从本地缓存获取语音提示词
 */
const getSpeechPromptFromCache = async (promptKey: string): Promise<string | null> => {
  try {
    const keys = getCacheKeys(promptKey);
    const [content, version, cacheTime] = await AsyncStorage.multiGet([
      keys.content,
      keys.version,
      keys.time
    ]);

    const promptContent = content[1];
    const promptVersion = version[1];
    const cacheTiming = cacheTime[1];

    if (!promptContent || !cacheTiming) {
      return null;
    }

    // 检查缓存是否过期
    const cacheTimestamp = parseInt(cacheTiming);
    const isExpired = Date.now() - cacheTimestamp > CACHE_DURATION;

    if (isExpired) {
      log(`[SpeechPrompt] 缓存已过期: ${promptKey}`);
      return null;
    }

    log(`[SpeechPrompt] 使用缓存的语音提示词: ${promptKey}, 版本: ${promptVersion}`);
    return promptContent;
  } catch (error) {
    logError('[SpeechPrompt] 获取缓存失败:', error);
    return null;
  }
};

/**
 * 保存语音提示词到本地缓存
 */
const saveSpeechPromptToCache = async (promptKey: string, speechPrompt: SpeechPrompt): Promise<void> => {
  try {
    const keys = getCacheKeys(promptKey);
    await AsyncStorage.multiSet([
      [keys.content, speechPrompt.prompt_content],
      [keys.version, speechPrompt.version.toString()],
      [keys.time, Date.now().toString()]
    ]);

    log(`[SpeechPrompt] 语音提示词已缓存: ${promptKey}, 版本: ${speechPrompt.version}`);
  } catch (error) {
    logError('[SpeechPrompt] 保存缓存失败:', error);
  }
};

/**
 * 从云端获取语音提示词
 */
const fetchSpeechPromptFromCloud = async (promptKey: string): Promise<SpeechPrompt | null> => {
  try {
    // 将 promptKey 转换为数据库中的 ID 格式
    let promptId: string;
    switch (promptKey) {
      case 'chinese_gpt4o_mini':
        promptId = 'chinese_gpt4o_mini_transcribe';
        break;
      case 'chinese_whisper':
        promptId = 'chinese_whisper';
        break;
      case 'multilingual_common':
        promptId = 'multilingual_common';
        break;
      default:
        promptId = promptKey;
    }
    
    const speechPrompt = await speechPromptService.getSpeechPrompt(promptId);
    
    if (speechPrompt) {
      log(`[SpeechPrompt] 从云端获取语音提示词成功: ${promptKey}, 版本: ${speechPrompt.version}`);
      return speechPrompt;
    }

    return null;
  } catch (error) {
    logError('[SpeechPrompt] 从云端获取语音提示词失败:', error);
    return null;
  }
};

/**
 * 清除语音提示词缓存
 */
export const clearSpeechPromptCache = async (promptKey?: string): Promise<void> => {
  try {
    if (promptKey) {
      // 清除特定提示词的缓存
      const keys = getCacheKeys(promptKey);
      await AsyncStorage.multiRemove([keys.content, keys.version, keys.time]);
      log(`[SpeechPrompt] 已清除缓存: ${promptKey}`);
    } else {
      // 清除所有语音提示词缓存
      const allKeys = Object.values(CACHE_KEYS);
      await AsyncStorage.multiRemove(allKeys);
      log('[SpeechPrompt] 已清除所有语音提示词缓存');
    }
  } catch (error) {
    logError('[SpeechPrompt] 清除缓存失败:', error);
  }
};

/**
 * 刷新语音提示词缓存
 */
export const refreshSpeechPromptCache = async (promptKey?: string): Promise<boolean> => {
  try {
    if (promptKey) {
      // 刷新特定提示词
      const cloudPrompt = await fetchSpeechPromptFromCloud(promptKey);
      if (cloudPrompt) {
        await saveSpeechPromptToCache(promptKey, cloudPrompt);
        return true;
      }
    } else {
      // 刷新所有提示词
      const keys = ['chinese_gpt4o_mini', 'chinese_whisper', 'multilingual_common'];
      let success = true;
      
      for (const key of keys) {
        try {
          const cloudPrompt = await fetchSpeechPromptFromCloud(key);
          if (cloudPrompt) {
            await saveSpeechPromptToCache(key, cloudPrompt);
          } else {
            success = false;
          }
        } catch (error) {
          logError(`[SpeechPrompt] 刷新 ${key} 失败:`, error);
          success = false;
        }
      }
      
      return success;
    }

    return false;
  } catch (error) {
    logError('[SpeechPrompt] 刷新缓存失败:', error);
    return false;
  }
};

/**
 * 获取语音提示词（优先从缓存，再从云端，最后使用fallback）
 * @param language 语言代码 ('zh' 或其他)
 * @param model 模型名称 ('gpt-4o-mini-transcribe' 或 'whisper-1')
 * @returns 语音提示词
 */
export const getSpeechPrompt = async (language: string, model: string): Promise<string> => {
  try {
    const promptKey = getPromptKey(language, model);
    
    // 首先尝试从本地缓存获取
    const cachedPrompt = await getSpeechPromptFromCache(promptKey);
    if (cachedPrompt) {
      log(`[SpeechPrompt] 使用缓存的语音提示词: ${promptKey}`);
      return cachedPrompt;
    }

    // 从云端获取
    const cloudPrompt = await fetchSpeechPromptFromCloud(promptKey);
    if (cloudPrompt) {
      // 保存到缓存
      await saveSpeechPromptToCache(promptKey, cloudPrompt);
      log(`[SpeechPrompt] 使用云端语音提示词: ${promptKey}`);
      return cloudPrompt.prompt_content;
    }

    // 使用fallback
    log(`[SpeechPrompt] 使用fallback语音提示词: ${promptKey}`);
    return FALLBACK_PROMPTS[promptKey];
  } catch (error) {
    logError('[SpeechPrompt] 获取语音提示词失败，使用fallback:', error);
    const promptKey = getPromptKey(language, model);
    return FALLBACK_PROMPTS[promptKey];
  }
};

/**
 * 获取缓存的语音提示词信息
 */
export const getCachedSpeechPromptInfo = async (promptKey?: string) => {
  try {
    if (promptKey) {
      const keys = getCacheKeys(promptKey);
      const [content, version, cacheTime] = await AsyncStorage.multiGet([
        keys.content,
        keys.version,
        keys.time
      ]);

      const promptContent = content[1];
      const promptVersion = version[1];
      const cacheTiming = cacheTime[1];

      if (!promptContent || !cacheTiming) {
        return null;
      }

      const cacheTimestamp = parseInt(cacheTiming);
      const isExpired = Date.now() - cacheTimestamp > CACHE_DURATION;

      return {
        hasCache: !!promptContent,
        version: promptVersion ? parseInt(promptVersion) : undefined,
        cacheTime: new Date(cacheTimestamp),
        isExpired,
        content: promptContent
      };
    } else {
      // 获取所有提示词的缓存信息
      const keys = ['chinese_gpt4o_mini', 'chinese_whisper', 'multilingual_common'];
      const result: Record<string, any> = {};
      
      for (const key of keys) {
        result[key] = await getCachedSpeechPromptInfo(key);
      }
      
      return result;
    }
  } catch (error) {
    logError('[SpeechPrompt] 获取缓存信息失败:', error);
    return null;
  }
};

export default {
  getSpeechPrompt,
  clearSpeechPromptCache,
  refreshSpeechPromptCache,
  getCachedSpeechPromptInfo
}; 