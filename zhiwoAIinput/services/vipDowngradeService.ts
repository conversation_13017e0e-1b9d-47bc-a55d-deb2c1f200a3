import { supabase } from './supabaseService';
import { log, error as logError } from './logService';
import { store } from '@/store';
import { setVIPStatus } from '@/store/slices/authSlice';

/**
 * VIP降级服务
 * 处理VIP状态变化时的模型设置同步
 */
export class VipDowngradeService {
  
  /**
   * 检查用户VIP状态并同步模型设置
   * @param userId 用户ID
   * @returns 是否发生了模型降级
   */
  static async checkAndSyncUserModels(userId: string): Promise<boolean> {
    try {
      log('[VIP降级服务] 开始检查用户VIP状态和模型设置:', userId);
      
      // 1. 获取用户当前的VIP状态和模型设置
      const { data: userProfile, error: profileError } = await supabase
        .from('profiles')
        .select('is_vip, vip_expires_at')
        .eq('id', userId)
        .single();
      
      if (profileError) {
        logError('[VIP降级服务] 获取用户资料失败:', profileError);
        return false;
      }
      
      const { data: userSettings, error: settingsError } = await supabase
        .from('user_settings')
        .select('default_text_model, default_voice_model')
        .eq('user_id', userId)
        .single();
      
      if (settingsError) {
        logError('[VIP降级服务] 获取用户设置失败:', settingsError);
        return false;
      }
      
      // 2. 检查VIP状态是否过期
      const isVipExpired = userProfile.vip_expires_at 
        ? new Date(userProfile.vip_expires_at) < new Date() 
        : false;
      
      const actualVipStatus = userProfile.is_vip && !isVipExpired;
      
      log('[VIP降级服务] 用户状态检查结果:', {
        profileVip: userProfile.is_vip,
        vipExpiresAt: userProfile.vip_expires_at,
        isExpired: isVipExpired,
        actualVipStatus,
        currentTextModel: userSettings.default_text_model,
        currentVoiceModel: userSettings.default_voice_model
      });
      
      // 3. 如果用户不是VIP，检查是否使用了VIP模型
      if (!actualVipStatus) {
        const modelsToCheck = [];
        
        if (userSettings.default_text_model) {
          modelsToCheck.push({
            model_id: userSettings.default_text_model,
            type: 'text'
          });
        }
        
        if (userSettings.default_voice_model) {
          modelsToCheck.push({
            model_id: userSettings.default_voice_model,
            type: 'voice'
          });
        }
        
        let hasVipModels = false;
        const modelUpdates: { default_text_model?: string; default_voice_model?: string } = {};
        
        // 检查每个模型是否需要VIP权限
        for (const modelCheck of modelsToCheck) {
          const { data: modelInfo, error: modelError } = await supabase
            .from('ai_models')
            .select('is_vip_only')
            .eq('model_id', modelCheck.model_id)
            .eq('model_type', modelCheck.type)
            .eq('is_active', true)
            .single();
          
          if (!modelError && modelInfo?.is_vip_only) {
            hasVipModels = true;
            
            if (modelCheck.type === 'text') {
              modelUpdates.default_text_model = 'qwen2.5-7b';
              log('[VIP降级服务] 检测到VIP文本模型，需要降级:', modelCheck.model_id);
            } else if (modelCheck.type === 'voice') {
              modelUpdates.default_voice_model = 'native';
              log('[VIP降级服务] 检测到VIP语音模型，需要降级:', modelCheck.model_id);
            }
          }
        }
        
        // 4. 如果发现VIP模型，执行降级
        if (hasVipModels) {
          const { error: updateError } = await supabase
            .from('user_settings')
            .update({
              ...modelUpdates,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', userId);
          
          if (updateError) {
            logError('[VIP降级服务] 更新用户模型设置失败:', updateError);
            return false;
          }
          
          log('[VIP降级服务] 成功降级用户模型设置:', modelUpdates);
          
          // 记录降级事件
          await this.logDowngradeEvent(userId, userSettings, modelUpdates);
          
          return true;
        }
      }
      
      return false;
    } catch (error) {
      logError('[VIP降级服务] 检查和同步用户模型失败:', error);
      return false;
    }
  }
  
  /**
   * 获取用户当前的模型设置（服务器端）
   * @param userId 用户ID
   */
  static async getUserServerModelSettings(userId: string) {
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .select('default_text_model, default_voice_model, updated_at')
        .eq('user_id', userId)
        .single();
      
      if (error) {
        logError('[VIP降级服务] 获取服务器模型设置失败:', error);
        return null;
      }
      
      return data;
    } catch (error) {
      logError('[VIP降级服务] 获取服务器模型设置出错:', error);
      return null;
    }
  }
  
  /**
   * 监听VIP状态变化，自动同步模型设置
   * @param userId 用户ID
   * @param callback 模型设置更新回调
   */
  static setupVipStatusListener(
    userId: string, 
    callback: (settings: { default_text_model: string; default_voice_model: string }) => void
  ) {
    // 监听profiles表的变化
    const profileSubscription = supabase
      .channel('vip-status-changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'profiles',
          filter: `id=eq.${userId}`,
        },
        async (payload) => {
          log('[VIP降级服务] 检测到用户资料变化:', payload);
          
          const newData = payload.new as any;
          const oldData = payload.old as any;
          
          // 如果VIP状态从true变为false，检查并同步模型设置
          if (oldData.is_vip === true && newData.is_vip === false) {
            log('[VIP降级服务] 检测到VIP状态降级，开始同步模型设置');
            
            // 等待一小段时间让数据库触发器完成
            setTimeout(async () => {
              const settings = await this.getUserServerModelSettings(userId);
              if (settings) {
                log('[VIP降级服务] 同步新的模型设置到客户端:', settings);
                callback(settings);
              }
            }, 1000);
          }
        }
      )
      .subscribe();
    
    // 监听user_settings表的变化
    const settingsSubscription = supabase
      .channel('model-settings-changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'user_settings',
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          log('[VIP降级服务] 检测到用户设置变化:', payload);
          
          const newData = payload.new as any;
          if (newData.default_text_model || newData.default_voice_model) {
            callback({
              default_text_model: newData.default_text_model,
              default_voice_model: newData.default_voice_model
            });
          }
        }
      )
      .subscribe();
    
    return {
      profileSubscription,
      settingsSubscription,
      unsubscribe: () => {
        profileSubscription.unsubscribe();
        settingsSubscription.unsubscribe();
      }
    };
  }
  
  /**
   * 记录降级事件
   */
  private static async logDowngradeEvent(
    userId: string, 
    originalSettings: any, 
    newSettings: any
  ) {
    try {
      await supabase
        .from('subscription_events')
        .insert({
          user_id: userId,
          event_type: 'VIP_MODEL_DOWNGRADE_CLIENT',
          event_timestamp: new Date().toISOString(),
          event_data: {
            original_text_model: originalSettings.default_text_model,
            original_voice_model: originalSettings.default_voice_model,
            new_text_model: newSettings.default_text_model,
            new_voice_model: newSettings.default_voice_model,
            reason: 'client_sync',
            timestamp: new Date().toISOString()
          }
        });
    } catch (error) {
      logError('[VIP降级服务] 记录降级事件失败:', error);
    }
  }
  
  /**
   * 手动触发模型同步检查
   * @param userId 用户ID
   */
  static async manualSync(userId: string): Promise<boolean> {
    log('[VIP降级服务] 手动触发模型同步检查');
    return await this.checkAndSyncUserModels(userId);
  }
} 