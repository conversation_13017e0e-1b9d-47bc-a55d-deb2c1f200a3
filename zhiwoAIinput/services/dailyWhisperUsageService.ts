/**
 * 每日Whisper使用次数服务
 * 管理免费用户的whisper模型每日使用次数限制
 * VIP用户无限制
 */

import { supabase } from './supabaseService';
import { log, error as logError } from './logService';

// 默认免费用户每日whisper使用限制（当无法获取配置时使用）
const DEFAULT_FREE_USER_DAILY_WHISPER_LIMIT = 5;

// 使用限制检查结果接口
export interface WhisperUsageLimitResult {
  canUse: boolean;
  isVip: boolean;
  currentUsage: number;
  dailyLimit: number;
  remaining: number;
  message: string;
}

/**
 * 获取当前的每日使用限制配置
 * @returns 使用限制次数
 */
export const getDailyWhisperLimit = async (): Promise<number> => {
  try {
    log('[WhisperUsage] 获取每日使用限制配置');
    
    const { data, error } = await supabase
      .from('system_configs')
      .select('value')
      .eq('key', 'daily_whisper_limit')
      .single();

    if (error) {
      logError('[WhisperUsage] 获取使用限制配置失败:', error);
      return DEFAULT_FREE_USER_DAILY_WHISPER_LIMIT;
    }

    const limit = data?.value?.free_user_limit;
    if (typeof limit !== 'number' || limit < 0) {
      logError('[WhisperUsage] 使用限制配置无效:', limit);
      return DEFAULT_FREE_USER_DAILY_WHISPER_LIMIT;
    }

    log('[WhisperUsage] 当前每日使用限制:', limit);
    return limit;
  } catch (error) {
    logError('[WhisperUsage] 获取使用限制配置异常:', error);
    return DEFAULT_FREE_USER_DAILY_WHISPER_LIMIT;
  }
};

/**
 * 检查用户当日whisper使用限制
 * @param userId 用户ID
 * @returns 使用限制检查结果
 */
export const checkUserDailyWhisperLimit = async (userId: string): Promise<WhisperUsageLimitResult> => {
  try {
    log('[WhisperUsage] 检查用户每日使用限制:', userId);
    
    // 获取当前日期
    const currentDate = new Date().toISOString().split('T')[0];
    
    // 调用数据库函数检查使用限制
    const { data, error } = await supabase.rpc('check_user_daily_whisper_limit', {
      p_user_id: userId,
      p_date: currentDate
    });

    if (error) {
      logError('[WhisperUsage] 检查使用限制失败:', error);
      // 出错时默认允许使用，避免影响用户体验
      return {
        canUse: true,
        isVip: false,
        currentUsage: 0,
        dailyLimit: await getDailyWhisperLimit(),
        remaining: await getDailyWhisperLimit(),
        message: '检查使用限制时出错，默认允许使用'
      };
    }

    const result = data[0] as WhisperUsageLimitResult;
    
    log('[WhisperUsage] 使用限制检查结果:', {
      canUse: result.canUse,
      isVip: result.isVip,
      currentUsage: result.currentUsage,
      dailyLimit: result.dailyLimit,
      remaining: result.remaining
    });

    return result;
  } catch (error) {
    logError('[WhisperUsage] 检查使用限制异常:', error);
    // 异常时默认允许使用
    return {
      canUse: true,
      isVip: false,
      currentUsage: 0,
      dailyLimit: await getDailyWhisperLimit(),
      remaining: await getDailyWhisperLimit(),
      message: '检查使用限制异常，默认允许使用'
    };
  }
};

/**
 * 增加用户当日whisper使用次数
 * @param userId 用户ID
 * @returns 新的使用次数
 */
export const incrementUserDailyWhisperUsage = async (userId: string): Promise<number> => {
  try {
    log('[WhisperUsage] 增加用户当日使用次数:', userId);
    
    // 调用数据库函数增加使用次数
    const { data, error } = await supabase.rpc('increment_user_daily_whisper_usage', {
      p_user_id: userId,
      p_date: new Date().toISOString().split('T')[0] // 当前日期 YYYY-MM-DD
    });

    if (error) {
      logError('[WhisperUsage] 增加使用次数失败:', error);
      throw error;
    }

    const newCount = data as number;
    log('[WhisperUsage] 使用次数已更新为:', newCount);

    return newCount;
  } catch (error) {
    logError('[WhisperUsage] 增加使用次数异常:', error);
    throw error;
  }
};

/**
 * 获取用户当日whisper使用次数
 * @param userId 用户ID
 * @param date 日期（可选，默认今天）
 * @returns 使用次数
 */
export const getUserDailyWhisperUsage = async (userId: string, date?: string): Promise<number> => {
  try {
    const targetDate = date || new Date().toISOString().split('T')[0];
    log('[WhisperUsage] 获取用户使用次数:', { userId, date: targetDate });
    
    // 调用数据库函数获取使用次数
    const { data, error } = await supabase.rpc('get_user_daily_whisper_usage', {
      p_user_id: userId,
      p_date: targetDate
    });

    if (error) {
      logError('[WhisperUsage] 获取使用次数失败:', error);
      return 0;
    }

    const usageCount = data as number;
    log('[WhisperUsage] 用户当日使用次数:', usageCount);

    return usageCount;
  } catch (error) {
    logError('[WhisperUsage] 获取使用次数异常:', error);
    return 0;
  }
};

/**
 * 检查并可能增加whisper使用次数
 * 这是主要的入口函数，在调用whisper前使用
 * @param userId 用户ID
 * @returns 检查结果，如果canUse为true，使用次数已自动增加
 */
export const checkAndIncrementWhisperUsage = async (userId: string): Promise<WhisperUsageLimitResult> => {
  try {
    log('[WhisperUsage] 检查并增加whisper使用次数:', userId);
    
    // 1. 先检查是否可以使用
    const limitCheck = await checkUserDailyWhisperLimit(userId);
    
    // 2. 如果不能使用，直接返回
    if (!limitCheck.canUse) {
      log('[WhisperUsage] 用户已达到使用限制，不允许使用');
      return limitCheck;
    }
    
    // 3. 如果可以使用，增加使用次数
    try {
      const newCount = await incrementUserDailyWhisperUsage(userId);
      
      // 4. 更新返回结果中的使用次数
      const updatedResult: WhisperUsageLimitResult = {
        ...limitCheck,
        currentUsage: newCount,
        remaining: limitCheck.isVip ? -1 : Math.max(0, limitCheck.dailyLimit - newCount),
        message: limitCheck.isVip 
          ? 'VIP用户无使用限制' 
          : `使用成功，今日还可使用${Math.max(0, limitCheck.dailyLimit - newCount)}次`
      };
      
      log('[WhisperUsage] 使用次数检查和增加完成:', {
        newCount,
        remaining: updatedResult.remaining
      });
      
      return updatedResult;
    } catch (incrementError) {
      logError('[WhisperUsage] 增加使用次数失败:', incrementError);
      // 增加失败时，返回不允许使用
      return {
        ...limitCheck,
        canUse: false,
        message: '记录使用次数失败，请稍后再试'
      };
    }
  } catch (error) {
    logError('[WhisperUsage] 检查并增加使用次数异常:', error);
    // 异常时默认允许使用，避免影响用户体验
    return {
      canUse: true,
      isVip: false,
      currentUsage: 0,
      dailyLimit: await getDailyWhisperLimit(),
      remaining: await getDailyWhisperLimit(),
      message: '检查使用限制异常，默认允许使用'
    };
  }
};

/**
 * 获取用户whisper使用统计
 * @param userId 用户ID
 * @param days 获取最近几天的数据（默认7天）
 * @returns 使用统计
 */
export const getUserWhisperUsageStats = async (userId: string, days: number = 7) => {
  try {
    log('[WhisperUsage] 获取用户使用统计:', { userId, days });
    
    const { data, error } = await supabase
      .from('daily_whisper_usage')
      .select('date, usage_count')
      .eq('user_id', userId)
      .gte('date', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
      .order('date', { ascending: false });

    if (error) {
      logError('[WhisperUsage] 获取使用统计失败:', error);
      return [];
    }

    log('[WhisperUsage] 用户使用统计:', data);
    return data || [];
  } catch (error) {
    logError('[WhisperUsage] 获取使用统计异常:', error);
    return [];
  }
};

/**
 * 清理过期的使用记录（管理员功能）
 * @returns 清理的记录数
 */
export const cleanupOldWhisperUsage = async (): Promise<number> => {
  try {
    log('[WhisperUsage] 开始清理过期使用记录');
    
    const { data, error } = await supabase.rpc('cleanup_old_whisper_usage');

    if (error) {
      logError('[WhisperUsage] 清理过期记录失败:', error);
      return 0;
    }

    const deletedCount = data as number;
    log('[WhisperUsage] 清理完成，删除记录数:', deletedCount);

    return deletedCount;
  } catch (error) {
    logError('[WhisperUsage] 清理过期记录异常:', error);
    return 0;
  }
};

// 默认导出服务对象
export const dailyWhisperUsageService = {
  getDailyWhisperLimit,
  checkUserDailyWhisperLimit,
  incrementUserDailyWhisperUsage,
  getUserDailyWhisperUsage,
  checkAndIncrementWhisperUsage,
  getUserWhisperUsageStats,
  cleanupOldWhisperUsage
};

export default dailyWhisperUsageService; 