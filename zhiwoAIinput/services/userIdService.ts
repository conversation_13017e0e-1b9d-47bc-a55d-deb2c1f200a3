/**
 * 用户ID管理服务
 * 处理匿名用户ID生成、登录用户ID获取、用户状态变化等
 */
import AsyncStorage from '@react-native-async-storage/async-storage';
import { nanoid } from 'nanoid/non-secure';
import { log, error as logError, warn } from '@/services/logService';
import { supabase } from './supabaseService';

// 存储键名
const STORAGE_KEYS = {
  ANONYMOUS_USER_ID: 'knowme_anonymous_user_id',
  LAST_LOGGED_USER_ID: 'knowme_last_logged_user_id',
};

/**
 * 用户ID管理服务类
 */
class UserIdService {
  private currentUserId: string | null = null;
  private anonymousUserId: string | null = null;
  private isInitialized: boolean = false;

  /**
   * 初始化用户ID服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // 获取或生成匿名用户ID
      this.anonymousUserId = await this.getOrCreateAnonymousUserId();
      
      // 检查当前登录状态
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        this.currentUserId = user.id;
        log(`UserIdService: 检测到已登录用户 ${user.id}`);
      } else {
        this.currentUserId = this.anonymousUserId;
        log(`UserIdService: 使用匿名用户ID ${this.anonymousUserId}`);
      }

      this.isInitialized = true;
      log('UserIdService: 初始化完成');
    } catch (error) {
      logError('UserIdService: 初始化失败:', error);
      // 即使初始化失败，也要设置一个默认的匿名ID
      this.anonymousUserId = this.generateAnonymousUserId();
      this.currentUserId = this.anonymousUserId;
      this.isInitialized = true;
    }
  }

  /**
   * 获取当前用户ID
   * @returns 当前用户ID（登录用户的supabase ID或匿名用户ID）
   */
  async getCurrentUserId(): Promise<string> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    return this.currentUserId!;
  }

  /**
   * 获取匿名用户ID
   * @returns 匿名用户ID
   */
  async getAnonymousUserId(): Promise<string> {
    if (!this.anonymousUserId) {
      this.anonymousUserId = await this.getOrCreateAnonymousUserId();
    }
    return this.anonymousUserId;
  }

  /**
   * 检查当前是否为匿名用户
   * @returns 是否为匿名用户
   */
  async isAnonymousUser(): Promise<boolean> {
    const currentId = await this.getCurrentUserId();
    const anonymousId = await this.getAnonymousUserId();
    return currentId === anonymousId;
  }

  /**
   * 获取登录用户ID（如果已登录）
   * @returns 登录用户ID或null
   */
  async getLoggedUserId(): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      return user?.id || null;
    } catch (error) {
      logError('UserIdService: 获取登录用户ID失败:', error);
      return null;
    }
  }

  /**
   * 处理用户登录
   * @param userId 登录用户的supabase ID
   */
  async handleUserLogin(userId: string): Promise<void> {
    try {
      log(`UserIdService: 处理用户登录 ${userId}`);
      
      // 保存上一个用户ID（用于数据迁移）
      const previousUserId = this.currentUserId;
      
      // 更新当前用户ID
      this.currentUserId = userId;
      
      // 保存最后登录的用户ID
      await AsyncStorage.setItem(STORAGE_KEYS.LAST_LOGGED_USER_ID, userId);
      
      // 如果之前是匿名用户，触发数据迁移
      if (previousUserId && previousUserId === this.anonymousUserId) {
        log(`UserIdService: 需要从匿名用户 ${previousUserId} 迁移数据到登录用户 ${userId}`);
        // 这里会在后续的文件迁移服务中处理
      }
      
      log(`UserIdService: 用户登录处理完成 ${userId}`);
    } catch (error) {
      logError(`UserIdService: 处理用户登录失败 (userId: ${userId}):`, error);
      throw error;
    }
  }

  /**
   * 处理用户退出登录
   */
  async handleUserLogout(): Promise<void> {
    try {
      log('UserIdService: 处理用户退出登录');
      
      // 切换回匿名用户ID
      this.currentUserId = this.anonymousUserId;
      
      log(`UserIdService: 已切换到匿名用户 ${this.anonymousUserId}`);
    } catch (error) {
      logError('UserIdService: 处理用户退出登录失败:', error);
      throw error;
    }
  }

  /**
   * 获取或创建匿名用户ID
   * @returns 匿名用户ID
   */
  private async getOrCreateAnonymousUserId(): Promise<string> {
    try {
      // 尝试从存储中获取现有的匿名用户ID
      const existingId = await AsyncStorage.getItem(STORAGE_KEYS.ANONYMOUS_USER_ID);
      if (existingId) {
        log(`UserIdService: 使用现有匿名用户ID ${existingId}`);
        return existingId;
      }

      // 生成新的匿名用户ID
      const newId = this.generateAnonymousUserId();
      await AsyncStorage.setItem(STORAGE_KEYS.ANONYMOUS_USER_ID, newId);
      log(`UserIdService: 生成新匿名用户ID ${newId}`);
      
      return newId;
    } catch (error) {
      logError('UserIdService: 获取或创建匿名用户ID失败:', error);
      // 如果存储失败，返回一个临时ID
      return this.generateAnonymousUserId();
    }
  }

  /**
   * 生成匿名用户ID
   * @returns 匿名用户ID
   */
  private generateAnonymousUserId(): string {
    return `anonymous_${nanoid(12)}`;
  }

  /**
   * 获取上次登录的用户ID
   * @returns 上次登录的用户ID或null
   */
  async getLastLoggedUserId(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.LAST_LOGGED_USER_ID);
    } catch (error) {
      logError('UserIdService: 获取上次登录用户ID失败:', error);
      return null;
    }
  }

  /**
   * 清除用户数据（用于测试或重置）
   */
  async clearUserData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.ANONYMOUS_USER_ID,
        STORAGE_KEYS.LAST_LOGGED_USER_ID,
      ]);
      
      this.currentUserId = null;
      this.anonymousUserId = null;
      this.isInitialized = false;
      
      log('UserIdService: 用户数据已清除');
    } catch (error) {
      logError('UserIdService: 清除用户数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户信息摘要（用于调试）
   */
  async getUserSummary(): Promise<{
    currentUserId: string | null;
    anonymousUserId: string | null;
    isAnonymous: boolean;
    isInitialized: boolean;
  }> {
    return {
      currentUserId: this.currentUserId,
      anonymousUserId: this.anonymousUserId,
      isAnonymous: await this.isAnonymousUser(),
      isInitialized: this.isInitialized,
    };
  }
}

// 导出单例实例
export const userIdService = new UserIdService();
