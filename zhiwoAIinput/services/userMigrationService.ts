/**
 * 用户迁移服务
 * 处理用户登录状态变化时的数据迁移逻辑
 */
import { userIdService } from './userIdService';
import { newHistoryService } from './newHistoryService';
import { recordingFileService } from './recordingFileService';
import { sqliteService } from './sqliteService';
import { log, error as logError, warn } from '@/services/logService';

/**
 * 用户迁移服务类
 */
class UserMigrationService {
  private isMigrating: boolean = false;

  /**
   * 处理用户登录时的数据迁移
   * @param loggedUserId 登录用户的supabase ID
   */
  async handleUserLogin(loggedUserId: string): Promise<void> {
    if (this.isMigrating) {
      warn('UserMigrationService: 迁移正在进行中，跳过重复操作');
      return;
    }

    try {
      this.isMigrating = true;
      log(`UserMigrationService: 开始处理用户登录迁移 ${loggedUserId}`);

      // 获取匿名用户ID
      const anonymousUserId = await userIdService.getAnonymousUserId();
      
      // 更新用户ID服务状态
      await userIdService.handleUserLogin(loggedUserId);
      
      // 检查是否需要迁移数据（从匿名用户到登录用户）
      if (anonymousUserId && anonymousUserId !== loggedUserId) {
        await this.migrateUserData(anonymousUserId, loggedUserId);
      }
      
      log(`UserMigrationService: 用户登录迁移完成 ${loggedUserId}`);
    } catch (error) {
      logError(`UserMigrationService: 处理用户登录迁移失败 (userId: ${loggedUserId}):`, error);
      throw error;
    } finally {
      this.isMigrating = false;
    }
  }

  /**
   * 处理用户退出登录时的状态切换
   */
  async handleUserLogout(): Promise<void> {
    try {
      log('UserMigrationService: 开始处理用户退出登录');
      
      // 更新用户ID服务状态，切换回匿名用户
      await userIdService.handleUserLogout();
      
      log('UserMigrationService: 用户退出登录处理完成');
    } catch (error) {
      logError('UserMigrationService: 处理用户退出登录失败:', error);
      throw error;
    }
  }

  /**
   * 处理用户再次登录时的数据迁移
   * @param newLoggedUserId 新登录用户的supabase ID
   */
  async handleUserReLogin(newLoggedUserId: string): Promise<void> {
    if (this.isMigrating) {
      warn('UserMigrationService: 迁移正在进行中，跳过重复操作');
      return;
    }

    try {
      this.isMigrating = true;
      log(`UserMigrationService: 开始处理用户再次登录迁移 ${newLoggedUserId}`);

      // 获取匿名用户ID
      const anonymousUserId = await userIdService.getAnonymousUserId();
      
      // 检查是否与上次登录的用户相同
      const lastLoggedUserId = await userIdService.getLastLoggedUserId();
      if (lastLoggedUserId === newLoggedUserId) {
        log(`UserMigrationService: 用户重新登录相同账号，无需迁移数据 ${newLoggedUserId}`);
        await userIdService.handleUserLogin(newLoggedUserId);
        return;
      }
      
      // 更新用户ID服务状态
      await userIdService.handleUserLogin(newLoggedUserId);
      
      // 迁移匿名用户数据到新登录用户
      if (anonymousUserId && anonymousUserId !== newLoggedUserId) {
        await this.migrateUserData(anonymousUserId, newLoggedUserId);
      }
      
      log(`UserMigrationService: 用户再次登录迁移完成 ${newLoggedUserId}`);
    } catch (error) {
      logError(`UserMigrationService: 处理用户再次登录迁移失败 (userId: ${newLoggedUserId}):`, error);
      throw error;
    } finally {
      this.isMigrating = false;
    }
  }

  /**
   * 迁移用户数据
   * @param fromUserId 源用户ID
   * @param toUserId 目标用户ID
   */
  private async migrateUserData(fromUserId: string, toUserId: string): Promise<void> {
    try {
      log(`UserMigrationService: 开始迁移用户数据 ${fromUserId} -> ${toUserId}`);
      
      // 检查源用户是否有数据需要迁移
      const sourceHistoryRecords = await newHistoryService.getHistoryRecords(fromUserId, 10, 0);
      const sourceRecordingFiles = await recordingFileService.getUserRecordingFiles(fromUserId);
      
      if (sourceHistoryRecords.length === 0 && sourceRecordingFiles.length === 0) {
        log(`UserMigrationService: 源用户无数据需要迁移 ${fromUserId}`);
        return;
      }
      
      log(`UserMigrationService: 发现需要迁移的数据 - 历史记录: ${sourceHistoryRecords.length}, 录音文件: ${sourceRecordingFiles.length}`);
      
      // 迁移历史记录和录音文件
      const migratedRecordsCount = await newHistoryService.migrateHistoryRecords(fromUserId, toUserId);
      
      log(`UserMigrationService: 数据迁移完成 ${fromUserId} -> ${toUserId}, 迁移记录数: ${migratedRecordsCount}`);
    } catch (error) {
      logError(`UserMigrationService: 迁移用户数据失败 (${fromUserId} -> ${toUserId}):`, error);
      throw error;
    }
  }

  /**
   * 清理用户数据（用于测试或重置）
   * @param userId 用户ID
   */
  async cleanupUserData(userId: string): Promise<void> {
    try {
      log(`UserMigrationService: 开始清理用户数据 ${userId}`);
      
      // 清理历史记录
      await newHistoryService.clearAllHistoryRecords(userId);
      
      // 清理录音文件
      const recordingFiles = await recordingFileService.getUserRecordingFiles(userId);
      for (const filePath of recordingFiles) {
        try {
          await recordingFileService.deleteRecordingFile(filePath, userId);
        } catch (fileError) {
          warn(`UserMigrationService: 删除录音文件失败 ${filePath}:`, fileError);
        }
      }
      
      // 关闭数据库连接
      await sqliteService.closeDatabase(userId);
      
      log(`UserMigrationService: 用户数据清理完成 ${userId}`);
    } catch (error) {
      logError(`UserMigrationService: 清理用户数据失败 (userId: ${userId}):`, error);
      throw error;
    }
  }

  /**
   * 获取迁移状态
   * @returns 是否正在迁移
   */
  isMigrationInProgress(): boolean {
    return this.isMigrating;
  }

  /**
   * 强制停止迁移（紧急情况使用）
   */
  forceStopMigration(): void {
    this.isMigrating = false;
    warn('UserMigrationService: 强制停止迁移操作');
  }

  /**
   * 获取用户数据统计
   * @param userId 用户ID
   * @returns 用户数据统计信息
   */
  async getUserDataStats(userId: string): Promise<{
    historyRecordsCount: number;
    recordingFilesCount: number;
    totalSize: number;
  }> {
    try {
      // 获取历史记录数量
      const historyRecords = await newHistoryService.getHistoryRecords(userId, 1000, 0);
      const historyRecordsCount = historyRecords.length;
      
      // 获取录音文件数量和大小
      const recordingFiles = await recordingFileService.getUserRecordingFiles(userId);
      const recordingFilesCount = recordingFiles.length;
      
      let totalSize = 0;
      for (const filePath of recordingFiles) {
        try {
          const fileInfo = await require('expo-file-system').getInfoAsync(filePath);
          if (fileInfo.exists && fileInfo.size) {
            totalSize += fileInfo.size;
          }
        } catch (sizeError) {
          warn(`UserMigrationService: 获取文件大小失败 ${filePath}:`, sizeError);
        }
      }
      
      return {
        historyRecordsCount,
        recordingFilesCount,
        totalSize,
      };
    } catch (error) {
      logError(`UserMigrationService: 获取用户数据统计失败 (userId: ${userId}):`, error);
      return {
        historyRecordsCount: 0,
        recordingFilesCount: 0,
        totalSize: 0,
      };
    }
  }
}

// 导出单例实例
export const userMigrationService = new UserMigrationService();
