import Share from 'react-native-share';
import { Alert } from 'react-native';
import { log, error as logError, warn } from '@/services/logService';

export interface ShareOptions {
  message: string;
  title?: string;
  url?: string;
}

export class ShareService {
  /**
   * 通用分享方法 - 使用系统分享面板
   * 用户可以在分享面板中选择微信进行分享
   * @param options 分享选项
   */
  static async shareText(options: ShareOptions): Promise<void> {
    try {
      const shareOptions = {
        title: options.title || '知我AI输入法',
        message: options.message,
        subject: options.title || '来自知我AI输入法的内容',
        url: options.url,
        filename: options.title || '分享内容', // 如果有文件需要分享
      };

      const result = await Share.open(shareOptions);
      log('分享成功:', result);
    } catch (error: any) {
      logError('分享失败:', error);
      
      // 处理用户取消分享的情况
      if (error.message === 'User did not share' || error.message.includes('cancelled')) {
        // 用户主动取消分享，不显示错误提示
        return;
      }
      
      // 其他错误显示提示
      Alert.alert('分享失败', '请稍后重试或选择其他分享方式');
    }
  }

  /**
   * 分享到微信的便捷方法
   * 注意：这会打开系统分享面板，用户需要选择微信进行分享
   * 如需直接分享到微信，请参考注释中的微信SDK集成方案
   * @param options 分享选项
   */
  static async shareToWeChat(options: ShareOptions): Promise<void> {
    try {
      // 使用系统分享面板，用户可以选择微信
      await this.shareText({
        ...options,
        title: options.title || '来自知我AI输入法',
      });
    } catch (error) {
      logError('分享到微信失败:', error);
      throw error;
    }
  }

  /**
   * 检查是否可以分享到微信
   * 注意：这只是检查系统分享面板是否可用
   */
  static async isWeChatShareAvailable(): Promise<boolean> {
    try {
      const result = await Share.isPackageInstalled('com.tencent.mm'); // Android包名
      return result.isInstalled || false;
    } catch (error) {
      warn('检查微信安装状态失败:', error);
      return false; // 假设不可用
    }
  }
}

/*
=== 如需直接分享到微信，需要集成微信SDK ===

推荐使用的微信SDK库：
1. react-native-wechat-lib (推荐)
2. @uiw/react-native-wechat

安装步骤：
1. npm install react-native-wechat-lib
2. 在微信开放平台注册应用：https://open.weixin.qq.com/
3. 获取App ID
4. 配置iOS Universal Link和Android签名

配置示例：
// 初始化微信SDK
import * as WeChat from 'react-native-wechat-lib';

WeChat.registerApp('your_wechat_app_id', 'your_universal_link')
  .then(registered => {
    log('微信SDK注册成功:', registered);
  });

// 直接分享到微信
WeChat.shareText({
  text: '分享内容',
  scene: 0, // 0-微信好友，1-朋友圈
}).then(result => {
  log('分享成功');
}).catch(error => {
  logError('分享失败:', error);
});

注意事项：
1. 需要在微信开放平台注册应用并通过审核
2. iOS需要配置Universal Link
3. Android需要正确的应用签名
4. 需要在应用中正确配置URL Scheme
*/ 