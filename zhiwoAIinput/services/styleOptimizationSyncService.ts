import { log, error as logError } from '@/services/logService';
import supabaseService from './supabaseService';
import { getUserSettings, saveUserSettings } from './storageService';

interface SyncQueueItem {
  userId: string;
  styleOptimization: boolean;
  timestamp: number;
  retryCount: number;
}

class StyleOptimizationSyncService {
  private syncQueue: SyncQueueItem[] = [];
  private isSyncing = false;
  private maxRetries = 3;
  private retryDelay = 2000; // 2秒

  /**
   * 添加同步任务到队列
   */
  async queueSync(userId: string, styleOptimization: boolean) {
    // 移除相同用户的旧任务，保留最新的设置
    this.syncQueue = this.syncQueue.filter(item => item.userId !== userId);
    
    // 添加新任务
    this.syncQueue.push({
      userId,
      styleOptimization,
      timestamp: Date.now(),
      retryCount: 0
    });

    log(`[StyleOptimizationSync] 已将风格模板优化同步任务添加到队列: ${styleOptimization} (用户: ${userId})`);
    
    // 启动同步处理
    this.processSyncQueue();
  }

  /**
   * 处理同步队列
   */
  private async processSyncQueue() {
    if (this.isSyncing || this.syncQueue.length === 0) {
      return;
    }

    this.isSyncing = true;

    while (this.syncQueue.length > 0) {
      const item = this.syncQueue.shift()!;
      
      try {
        const success = await this.syncToCloud(item);
        
        if (!success) {
          // 同步失败，检查是否需要重试
          if (item.retryCount < this.maxRetries) {
            item.retryCount++;
            log(`[StyleOptimizationSync] 同步失败，将重试 (${item.retryCount}/${this.maxRetries})`);
            
            // 等待一段时间后重试
            setTimeout(() => {
              this.syncQueue.unshift(item);
              this.processSyncQueue();
            }, this.retryDelay * item.retryCount);
          } else {
            logError(`[StyleOptimizationSync] 同步失败，已达到最大重试次数`);
          }
        }
      } catch (error) {
        logError(`[StyleOptimizationSync] 同步过程中出错:`, error);
      }
    }

    this.isSyncing = false;
  }

  /**
   * 同步到云端
   */
  private async syncToCloud(item: SyncQueueItem): Promise<boolean> {
    try {
      const { data: sessionData } = await supabaseService.supabase.auth.getSession();
      
      if (!sessionData?.session?.user?.id) {
        log(`[StyleOptimizationSync] 用户未登录，跳过云端同步`);
        return true; // 未登录不算失败
      }

      if (sessionData.session.user.id !== item.userId) {
        log(`[StyleOptimizationSync] 用户已切换，跳过旧用户的同步任务`);
        return true; // 用户切换不算失败
      }

      await supabaseService.modelService.saveUserModelSettings(
        item.userId,
        { style_optimization_enabled: item.styleOptimization }
      );

      log(`[StyleOptimizationSync] 风格模板优化设置已成功同步到云端: ${item.styleOptimization}`);
      return true;
    } catch (error) {
      logError(`[StyleOptimizationSync] 云端同步失败:`, error);
      return false;
    }
  }

  /**
   * 强制将本地设置同步到云端（用于登录后）
   */
  async forceSyncFromLocal(): Promise<void> {
    try {
      const { data: sessionData } = await supabaseService.supabase.auth.getSession();
      if (!sessionData?.session?.user?.id) {
        log(`[StyleOptimizationSync] 用户未登录，跳过强制同步`);
        return;
      }

      const localSettings = await getUserSettings();
      const styleOptimization = localSettings.styleOptimization !== undefined ? 
        localSettings.styleOptimization : true; // 默认开启

      await this.queueSync(sessionData.session.user.id, styleOptimization);
      log(`[StyleOptimizationSync] 已强制将本地风格模板优化设置同步到云端: ${styleOptimization}`);
    } catch (error) {
      logError(`[StyleOptimizationSync] 强制同步失败:`, error);
    }
  }

  /**
   * 从云端同步到本地（仅在必要时使用）
   */
  async syncFromCloud(): Promise<boolean> {
    try {
      const { data: sessionData } = await supabaseService.supabase.auth.getSession();
      if (!sessionData?.session?.user?.id) {
        return false;
      }

      const { data: cloudSettings } = await supabaseService.modelService.getUserModelSettings(
        sessionData.session.user.id
      );

      if (cloudSettings?.style_optimization_enabled !== undefined) {
        const localSettings = await getUserSettings();
        
        // 只有当云端设置与本地不同时才更新本地
        if (localSettings.styleOptimization !== cloudSettings.style_optimization_enabled) {
          await saveUserSettings({ styleOptimization: cloudSettings.style_optimization_enabled });
          log(`[StyleOptimizationSync] 已从云端同步风格模板优化设置到本地: ${cloudSettings.style_optimization_enabled}`);
          return true;
        }
      }

      return false;
    } catch (error) {
      logError(`[StyleOptimizationSync] 从云端同步失败:`, error);
      return false;
    }
  }

  /**
   * 清除队列中的所有任务
   */
  clearQueue() {
    this.syncQueue = [];
    log(`[StyleOptimizationSync] 已清除同步队列`);
  }

  /**
   * 获取当前队列状态
   */
  getQueueStatus() {
    return {
      queueLength: this.syncQueue.length,
      isSyncing: this.isSyncing
    };
  }
}

// 创建并导出服务实例
const styleOptimizationSyncService = new StyleOptimizationSyncService();
export default styleOptimizationSyncService; 