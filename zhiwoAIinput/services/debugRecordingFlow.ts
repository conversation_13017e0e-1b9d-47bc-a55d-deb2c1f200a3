/**
 * 录音流程调试脚本
 * 用于调试录音完成后历史记录不显示的问题
 */
import { log, error as logError, warn } from '@/services/logService';

/**
 * 调试录音保存流程
 */
export async function debugRecordingFlow(): Promise<void> {
  try {
    console.log('\n🔍 开始调试录音保存流程...');
    console.log('=' .repeat(60));

    // 1. 检查服务导入
    console.log('\n📦 检查服务导入...');
    
    try {
      const storageService = await import('@/services/storageService');
      console.log('✅ storageService 导入成功');
      console.log('  - saveHistoryRecord:', typeof storageService.saveHistoryRecord);
      console.log('  - getHistoryRecords:', typeof storageService.getHistoryRecords);
    } catch (error) {
      console.log('❌ storageService 导入失败:', error);
    }

    try {
      const newHistoryService = await import('@/services/newHistoryService');
      console.log('✅ newHistoryService 导入成功');
    } catch (error) {
      console.log('❌ newHistoryService 导入失败:', error);
    }

    try {
      const userIdService = await import('@/services/userIdService');
      console.log('✅ userIdService 导入成功');
    } catch (error) {
      console.log('❌ userIdService 导入失败:', error);
    }

    try {
      const sqliteService = await import('@/services/sqliteService');
      console.log('✅ sqliteService 导入成功');
    } catch (error) {
      console.log('❌ sqliteService 导入失败:', error);
    }

    // 2. 检查用户ID服务状态
    console.log('\n👤 检查用户ID服务状态...');
    
    try {
      const { userIdService } = await import('@/services/userIdService');
      await userIdService.initialize();
      const currentUserId = await userIdService.getCurrentUserId();
      const isAnonymous = await userIdService.isAnonymousUser();
      
      console.log('✅ 用户ID服务正常');
      console.log(`  - 当前用户ID: ${currentUserId}`);
      console.log(`  - 是否匿名: ${isAnonymous}`);
    } catch (error) {
      console.log('❌ 用户ID服务异常:', error);
    }

    // 3. 检查数据库状态
    console.log('\n🗄️ 检查数据库状态...');
    
    try {
      const { sqliteService } = await import('@/services/sqliteService');
      const { userIdService } = await import('@/services/userIdService');
      
      const currentUserId = await userIdService.getCurrentUserId();
      const records = await sqliteService.getHistoryRecords(currentUserId, 10, 0);
      
      console.log('✅ 数据库连接正常');
      console.log(`  - 当前用户记录数: ${records.length}`);
    } catch (error) {
      console.log('❌ 数据库连接异常:', error);
    }

    // 4. 检查数据迁移状态
    console.log('\n🔄 检查数据迁移状态...');
    
    try {
      const { dataMigrationService } = await import('@/services/dataMigrationService');
      const migrationStatus = await dataMigrationService.getMigrationStatus();
      
      console.log('✅ 数据迁移服务正常');
      console.log(`  - 迁移完成: ${migrationStatus.migrationCompleted}`);
      console.log(`  - SQLite记录数: ${migrationStatus.sqliteRecordsCount}`);
      console.log(`  - 遗留记录数: ${migrationStatus.legacyRecordsCount}`);
    } catch (error) {
      console.log('❌ 数据迁移服务异常:', error);
    }

    // 5. 测试保存流程
    console.log('\n💾 测试保存流程...');
    
    try {
      const storageService = await import('@/services/storageService');
      const { generateId } = await import('@/utils/helpers');
      
      const testRecord = {
        id: generateId(),
        timestamp: Date.now(),
        originalText: '调试测试原始文本',
        optimizedText: '调试测试优化文本',
        templateId: 'debug_template',
        templateName: '调试模板',
        audioUri: '/debug/test/recording.m4a',
      };

      console.log('📝 创建测试记录:', testRecord.id);
      
      const saveResult = await storageService.saveHistoryRecord(testRecord);
      console.log(`💾 保存结果: ${saveResult ? '成功' : '失败'}`);
      
      if (saveResult) {
        // 尝试读取记录
        const retrievedRecord = await storageService.getHistoryRecordById(testRecord.id);
        console.log(`📖 读取结果: ${retrievedRecord ? '成功' : '失败'}`);
        
        if (retrievedRecord) {
          console.log('  - 记录ID:', retrievedRecord.id);
          console.log('  - 原始文本:', retrievedRecord.originalText.substring(0, 20) + '...');
          console.log('  - 优化文本:', retrievedRecord.optimizedText.substring(0, 20) + '...');
        }
        
        // 清理测试记录
        await storageService.deleteHistoryRecord(testRecord.id);
        console.log('🧹 测试记录已清理');
      }
    } catch (error) {
      console.log('❌ 保存流程测试失败:', error);
    }

    // 6. 检查历史记录获取
    console.log('\n📋 检查历史记录获取...');
    
    try {
      const storageService = await import('@/services/storageService');
      
      const allRecords = await storageService.getHistoryRecords();
      console.log(`📊 总记录数: ${allRecords.length}`);
      
      const groupedRecords = await storageService.getGroupedHistoryRecords(10);
      console.log(`📊 分组记录数: ${groupedRecords.length} 组`);
      
      if (groupedRecords.length > 0) {
        const totalInGroups = groupedRecords.reduce((sum, group) => sum + group.data.length, 0);
        console.log(`📊 分组中的记录总数: ${totalInGroups}`);
      }
    } catch (error) {
      console.log('❌ 历史记录获取失败:', error);
    }

    // 7. 检查Redux状态
    console.log('\n🔄 检查Redux状态...');
    
    try {
      // 这里需要在实际应用中运行，因为需要Redux store
      console.log('ℹ️ Redux状态检查需要在应用运行时进行');
    } catch (error) {
      console.log('❌ Redux状态检查失败:', error);
    }

    console.log('\n' + '=' .repeat(60));
    console.log('🎯 调试完成！请检查上述输出中的任何错误或异常。');
    console.log('💡 如果所有检查都通过但历史记录仍不显示，问题可能在UI层面。');

  } catch (error) {
    console.error('调试过程出现异常:', error);
  }
}

/**
 * 检查特定的录音保存调用链
 */
export async function traceRecordingSaveCall(): Promise<void> {
  try {
    console.log('\n🔍 追踪录音保存调用链...');
    
    // 模拟录音完成后的保存调用
    const { generateId } = await import('@/utils/helpers');
    const storageService = await import('@/services/storageService');
    
    const testRecord = {
      id: generateId(),
      timestamp: Date.now(),
      originalText: '追踪测试原始文本',
      optimizedText: '追踪测试优化文本',
      templateId: 'trace_template',
      templateName: '追踪模板',
      audioUri: '/trace/test/recording.m4a',
    };

    console.log('1️⃣ 调用 storageService.saveHistoryRecord...');
    console.log('   记录ID:', testRecord.id);
    
    // 添加详细的日志追踪
    const originalLog = console.log;
    const logs: string[] = [];
    
    console.log = (...args) => {
      const message = args.join(' ');
      logs.push(message);
      originalLog(...args);
    };

    try {
      const result = await storageService.saveHistoryRecord(testRecord);
      console.log = originalLog;
      
      console.log('2️⃣ 保存结果:', result);
      console.log('3️⃣ 调用过程中的日志:');
      logs.forEach((logMsg, index) => {
        if (logMsg.includes('SQLite') || logMsg.includes('NewHistory') || logMsg.includes('保存')) {
          console.log(`   ${index + 1}. ${logMsg}`);
        }
      });
      
      // 验证保存结果
      if (result) {
        const saved = await storageService.getHistoryRecordById(testRecord.id);
        console.log('4️⃣ 验证保存:', saved ? '成功找到记录' : '未找到记录');
        
        // 清理
        await storageService.deleteHistoryRecord(testRecord.id);
      }
      
    } catch (error) {
      console.log = originalLog;
      console.log('❌ 保存过程出错:', error);
    }

  } catch (error) {
    console.error('追踪过程出现异常:', error);
  }
}

/**
 * 检查UI组件的历史记录获取
 */
export async function checkUIHistoryFetch(): Promise<void> {
  try {
    console.log('\n🖥️ 检查UI组件的历史记录获取...');
    
    // 模拟历史记录页面的数据获取
    const storageService = await import('@/services/storageService');
    
    console.log('1️⃣ 模拟历史记录页面数据获取...');
    
    // 获取分组历史记录（历史记录页面使用的方法）
    const groupedRecords = await storageService.getGroupedHistoryRecordsByTemplate(undefined, 15);
    console.log(`📊 获取到 ${groupedRecords.length} 个分组`);
    
    if (groupedRecords.length > 0) {
      groupedRecords.forEach((group, index) => {
        console.log(`   分组 ${index + 1}: ${group.title} (${group.data.length} 条记录)`);
      });
    } else {
      console.log('⚠️ 没有获取到任何分组记录');
    }

    // 获取可见记录数量
    const visibleCount = await storageService.getVisibleHistoryRecordsCount();
    console.log(`📊 可见记录数量: ${visibleCount}`);
    
    // 获取真实记录数量
    const realCount = await storageService.getRealHistoryRecordsCount();
    console.log(`📊 真实记录数量: ${realCount}`);
    
    if (visibleCount === 0 && realCount === 0) {
      console.log('⚠️ 数据库中没有任何记录，这可能是问题所在');
    } else if (visibleCount === 0 && realCount > 0) {
      console.log('⚠️ 有记录但不可见，可能是VIP权限问题');
    }

  } catch (error) {
    console.error('UI历史记录获取检查失败:', error);
  }
}

/**
 * 运行完整的调试流程
 */
export async function runFullDebug(): Promise<void> {
  await debugRecordingFlow();
  await traceRecordingSaveCall();
  await checkUIHistoryFetch();
}
