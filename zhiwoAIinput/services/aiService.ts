/**
 * AI文本优化服务
 * 处理与SiliconFlow API的交互
 */
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getVipStatus, getUserSettings, saveUserSettings } from './storageService';
import Constants from 'expo-constants';
import { defaultApiKey } from '@/utils/config';
import { getApiModelName, DEFAULT_MODEL_ID, getModelInfo } from '@/utils/modelUtils';
import { supabase, modelService } from './supabaseService';
import { getSystemPrompt } from './systemPromptService';
import { callSiliconFlowAPI } from './unifiedProxyService';
import { log, error as logError, warn } from '@/services/logService';

// 存储键名
const STORAGE_KEYS = {
  API_KEY: 'knowme_api_key',
};

// SiliconFlow API 地址
const API_URL = 'https://api.siliconflow.cn/v1/chat/completions';

// 定义API调用请求接口
interface OptimizeTextRequest {
  model: string;
  messages: {
    role: string;
    content: string;
  }[];
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  stream: boolean;
  n: number;
  response_format: {
    type: string;
  };
}

// 定义API调用响应接口
interface OptimizeTextResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
      reasoning_content?: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  system_fingerprint?: string;
};

// 默认模型
const DEFAULT_MODEL = DEFAULT_MODEL_ID;

/**
 * 获取API密钥
 * @returns API密钥
 */
export const getApiKey = async (): Promise<string> => {
  try {
    // 使用统一代理服务时，客户端不需要API密钥
    // 这个函数保留是为了兼容性和fallback使用
    log('=== API密钥调试信息 ===');
    log('使用统一代理服务，客户端API密钥仅用于fallback');
    log('defaultApiKey长度:', defaultApiKey?.length || 0);
    
    // 如果没有API密钥，使用统一代理时这不是问题
    if (!defaultApiKey || defaultApiKey.trim() === '') {
      log('客户端API密钥为空，但使用统一代理服务时这是正常的');
      return ''; // 返回空字符串，不报错
    }
    
    return defaultApiKey;
  } catch (error) {
    logError('获取API密钥失败:', error);
    return defaultApiKey || '';
  }
};

/**
 * 保存API密钥
 * @param apiKey API密钥
 */
export const saveApiKey = async (apiKey: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.API_KEY, apiKey);
    log('API密钥已保存');
  } catch (error) {
    logError('保存API密钥失败:', error);
    throw error;
  }
};

/**
 * 获取可用的AI模型列表
 * @returns 模型列表
 */
export const getAvailableModels = async (): Promise<{ id: string; name: string; isPremium: boolean }[]> => {
  // 检查用户是否为VIP
  const vipStatus = await getVipStatus();

  // 从模型工具获取模型信息
  const defaultModelInfo = getModelInfo(DEFAULT_MODEL);

  // 基础模型列表
  const models = [
    { id: defaultModelInfo.id, name: defaultModelInfo.displayName, isPremium: false }
  ];

  // 如果是VIP用户，添加高级模型
  if (vipStatus.isVip) {
    // 获取DeepSeek-V3模型信息
    const deepSeekModelInfo = getModelInfo('deepseek-v3');
    models.push(
      { id: deepSeekModelInfo.id, name: deepSeekModelInfo.displayName, isPremium: true }
    );
  }

  return models;
};

/**
 * 从本地存储中获取当前使用的语言模型
 * 优化版本：直接从本地存储获取，避免数据库连接问题
 */
export const getCurrentLanguageModel = async (): Promise<string> => {
  try {
    log("getCurrentLanguageModel开始执行（优化版本）");

    // 首先检查用户是否为VIP
    const vipStatus = await getVipStatus();
    log("当前VIP状态:", vipStatus);

    // 直接从本地存储获取用户设置
    log("从本地存储获取用户设置");
    const userSettings = await getUserSettings();
    log("获取到的本地用户设置:", JSON.stringify(userSettings, null, 2));

    // 如果用户设置了语言模型，则使用用户设置的模型
    if (userSettings.languageModel) {
      // 检查当前模型是否需要VIP权限
      const modelInfo = getModelInfo(userSettings.languageModel);

      if (modelInfo.isPremium && !vipStatus.isVip) {
        log("用户不是VIP，但本地存储使用高级模型，强制切换到默认模型");

        // 更新本地存储为默认模型
        await saveUserSettings({
          languageModel: DEFAULT_MODEL
        });

        log("已将本地存储的语言模型更新为默认模型:", DEFAULT_MODEL);
        return DEFAULT_MODEL;
      }

      log("使用本地存储的语言模型:", userSettings.languageModel);
      log("模型类型:", typeof userSettings.languageModel);
      return userSettings.languageModel;
    }

    // 如果未设置，则使用默认模型并保存到本地存储
    log("未找到用户设置的语言模型，使用默认模型:", DEFAULT_MODEL);

    // 保存默认设置到本地存储
    await saveUserSettings({
      languageModel: DEFAULT_MODEL
    });

    return DEFAULT_MODEL;
  } catch (error) {
    logError('获取当前语言模型失败:', error);
    logError('错误详情:', JSON.stringify(error, null, 2));
    // 出错时使用默认模型
    log("出错，返回默认模型:", DEFAULT_MODEL);
    return DEFAULT_MODEL;
  }
};

/**
 * 从本地存储中获取当前使用的转写模型
 */
export const getCurrentTranscribeModel = async (): Promise<string> => {
  try {
    log("getCurrentTranscribeModel开始执行");

    // 首先检查用户是否为VIP
    const vipStatus = await getVipStatus();
    log("当前VIP状态:", vipStatus);

    // 直接从本地存储获取用户设置
    log("从本地存储获取用户设置");
    const userSettings = await getUserSettings();
    log("获取到的本地用户设置:", JSON.stringify(userSettings, null, 2));

    // 如果用户设置了转写模型，则使用用户设置的模型
    if (userSettings.transcribeModel) {
      log("使用本地存储的转写模型:", userSettings.transcribeModel);
      return userSettings.transcribeModel;
    }

    // 如果未设置，则使用默认转写模型并保存到本地存储
    const defaultTranscribeModel = 'native';
    log("未找到用户设置的转写模型，使用默认模型:", defaultTranscribeModel);

    // 保存默认设置到本地存储
    await saveUserSettings({
      transcribeModel: defaultTranscribeModel
    });

    return defaultTranscribeModel;
  } catch (error) {
    logError('获取当前转写模型失败:', error);
    logError('错误详情:', JSON.stringify(error, null, 2));
    // 出错时使用默认模型
    const defaultTranscribeModel = 'native';
    log("出错，返回默认转写模型:", defaultTranscribeModel);
    return defaultTranscribeModel;
  }
};

/**
 * 处理用户输入文本的AI优化
 * @param messages 消息数组
 * @param options 选项，包括模型选择
 * @returns 优化后的内容
 */
export const processWithAI = async (
  messages: { role: string; content: string }[],
  options?: { model?: string; temperature?: number; apiKey?: string }
): Promise<{ text: string; error?: Error }> => {
  try {
    // 如果没有指定模型，从用户设置获取当前使用的模型
    const modelId = options?.model || await getCurrentLanguageModel();

    // 获取API模型名称
    const apiModelName = getApiModelName(modelId);

    // 构建请求参数
    const apiKey = options?.apiKey || await getApiKey();
    const temperature = options?.temperature || 0.7;

    // 构建请求体
    const data = {
      model: apiModelName,
      messages,
      temperature,
      max_tokens: 4000
    };

    // 通过统一代理服务调用
    const { response } = await callSiliconFlowAPI('/v1/chat/completions', data, apiKey);

    // 检查响应状态
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API请求失败: ${response.status} ${errorText}`);
    }

    // 解析响应
    const result = await response.json();

    // 返回生成的文本
    return {
      text: result.choices[0].message.content
    };
  } catch (error) {
    logError('AI处理失败:', error);
    return {
      text: '',
      error: error as Error
    };
  }
};

/**
 * 使用AI优化文本
 * @param text 用户输入的文本
 * @param templatePrompt 模板提示词
 * @param model 使用的AI模型 (VIP用户可选择高级模型)
 * @param abortSignal 可选的AbortSignal，用于取消请求
 * @param requestId 请求ID，用于跟踪特定请求
 * @returns 优化后的文本和请求ID
 */
export const optimizeText = async (
  text: string,
  templatePrompt: string,
  model: string = DEFAULT_MODEL,
  abortSignal?: AbortSignal,
  requestId?: string
): Promise<{ text: string, requestId?: string }> => {
  try {
    // 调试：打印传入的model参数
    log(`[模型调试] optimizeText被调用，传入model参数: "${model}"`);
    log(`[模型调试] model参数类型: ${typeof model}`);
    
    // 如果已经中止，立即结束
    if (abortSignal?.aborted) {
      log(`请求已被中止，ID:${requestId || '未知'}`);
      throw new Error('请求已被中止');
    }

    // 添加AbortController状态检查，防止使用已被重置的controller
    if (abortSignal) {
      // 检查AbortSignal是否处于可用状态
      try {
        // 尝试添加一个临时监听器来验证信号是否正常
        const testHandler = () => {};
        abortSignal.addEventListener('abort', testHandler);
        abortSignal.removeEventListener('abort', testHandler);
      } catch (signalError) {
        warn('AbortSignal状态异常，可能已被重置:', signalError);
        throw new Error('请求状态异常，请重试');
      }
    }

    log('使用统一代理服务调用 AI 服务');

    // 获取API密钥（仅用于fallback，统一代理服务不需要客户端API密钥）
    const apiKey = await getApiKey();

    // 对于统一代理服务，客户端不需要验证API密钥
    // API密钥验证由代理服务负责
    log('通过统一代理调用，客户端无需验证API密钥');

    // 检查是否为VIP用户
    const vipStatus = await getVipStatus();

    // 获取模型信息
    const modelInfo = getModelInfo(model);

    // 如果不是VIP用户但选择了高级模型，则使用默认模型
    if (!vipStatus.isVip && modelInfo.isPremium) {
      log('非VIP用户尝试使用高级模型，自动切换到默认模型');
      model = DEFAULT_MODEL;
    }

    // 获取API请求使用的实际模型名称
    const apiModelName = getApiModelName(model);
    log(`使用模型ID: ${model}, 对应API模型名称: ${apiModelName}`);

    // 获取系统提示词
    const systemPromptContent = await getSystemPrompt(templatePrompt);

    // 构建请求数据
    const requestData: OptimizeTextRequest = {
      model: apiModelName,
      messages: [
        {
          role: "system",
          content: systemPromptContent
        },
        {
          role: "user",
          content: text
        }
      ],
      temperature: 0.7,
      max_tokens: 2048,
      top_p: 0.9,
      // 添加SiliconFlow API所需的必要参数
      stream: false,
      n: 1,
      response_format: {
        type: "text"
      }
    };

    // 调用统一代理服务
    log(`正在通过统一代理调用AI优化服务... 模型:${apiModelName}, 请求ID:${requestId || '未设置'}`);
    log(`[客户端请求数据检查] model字段值: "${requestData.model}"`);
    log(`[客户端请求数据检查] model字段长度: ${requestData.model.length}`);
    log(`[客户端请求数据检查] model字段类型: ${typeof requestData.model}`);
    log(`[完整系统提示词] ${requestData.messages[0].content}`);
    log(`[用户输入] ${requestData.messages[1].content}`);

    // 通过统一代理服务调用API
    const { response, isProxied } = await callSiliconFlowAPI(
      '/v1/chat/completions', 
      requestData, 
      apiKey, 
      abortSignal
    );

    log(`AI服务调用${isProxied ? '通过代理' : '直接调用'}完成`);

    // 如果请求已中止，抛出错误
    if (abortSignal?.aborted) {
      log(`请求已被中止，ID:${requestId || '未知'}`);
      throw new Error('请求已被中止');
    }

    log(`收到响应状态码: ${response.status}`);

    // 检查响应状态
    if (!response.ok) {
      let errorMessage = `API响应错误: ${response.status} ${response.statusText}`;

      try {
        const errorData = await response.json();
        logError('AI服务响应错误:', errorData);

        // 根据错误类型返回不同的错误信息
        if (response.status === 401) {
          errorMessage = 'API密钥无效，请联系管理员';
        } else if (response.status === 429) {
          errorMessage = 'API请求次数超限，请稍后再试';
        } else if (errorData.error?.message) {
          errorMessage = `API错误: ${errorData.error.message}`;
        }
      } catch (parseError) {
        logError('解析错误响应失败:', parseError);
      }

      throw new Error(errorMessage);
    }

    // 解析响应数据
    const responseData: OptimizeTextResponse = await response.json();

    log('AI响应数据解析成功');
    log('响应中的选择数量:', responseData.choices?.length || 0);

    // 验证响应数据结构
    if (!responseData.choices || responseData.choices.length === 0) {
      logError('AI响应数据无效: 没有choices字段');
      throw new Error('AI响应数据格式错误');
    }

    if (!responseData.choices[0]?.message?.content) {
      logError('AI响应数据无效: message.content为空');
      throw new Error('AI返回内容为空');
    }

    // 提取优化后的文本
    const optimizedText = responseData.choices[0].message.content.trim();

    log(`AI文本优化完成，原始长度:${text.length}, 优化后长度:${optimizedText.length}`);
    log(`请求ID:${requestId || '未设置'}`);

    // 添加使用统计记录
    if (responseData.usage) {
      log('Token使用情况:', responseData.usage);
    }

    return {
      text: optimizedText,
      requestId
    };

  } catch (error) {
    logError('AI文本优化失败:', error);

    // 如果是取消请求的错误，直接抛出
    if (error instanceof Error && 
        (error.name === 'AbortError' || error.message.includes('abort') || error.message.includes('中止'))) {
      throw error;
    }

    // 返回用户友好的错误信息
    let userFriendlyMessage = '文本优化失败，请稍后再试';

    if (error instanceof Error) {
      if (error.message.includes('密钥')) {
        userFriendlyMessage = 'API密钥配置错误，请联系管理员';
      } else if (error.message.includes('网络') || error.message.includes('网络连接')) {
        userFriendlyMessage = '网络连接失败，请检查网络设置';
      } else if (error.message.includes('超限') || error.message.includes('429')) {
        userFriendlyMessage = 'API调用频率超限，请稍后再试';
      } else if (error.message.includes('服务') || error.message.includes('503')) {
        userFriendlyMessage = 'AI服务暂时不可用，请稍后再试';
      }
    }

    throw new Error(userFriendlyMessage);
  }
}; 