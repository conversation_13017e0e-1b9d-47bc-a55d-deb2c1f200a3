/**
 * 权限管理服务
 * 统一处理麦克风和语音识别权限的检查、请求和引导
 */
import { Platform } from 'react-native';
import { Audio } from 'expo-av';
import { ExpoSpeechRecognitionModule } from 'expo-speech-recognition';
import { log, error as logError, warn } from '@/services/logService';

export type PermissionType = 'microphone' | 'speech' | 'both';

export interface PermissionStatus {
  microphone: boolean;
  speech: boolean;
}

export interface PermissionCheckResult {
  granted: boolean;
  status: PermissionStatus;
  needsGuide: boolean;
  permissionType: PermissionType;
}

// 权限引导回调类型
export type PermissionGuideCallback = (permissionType: PermissionType) => void;

// 全局权限引导回调
let permissionGuideCallback: PermissionGuideCallback | null = null;

/**
 * 设置权限引导回调
 */
export const setPermissionGuideCallback = (callback: PermissionGuideCallback | null) => {
  permissionGuideCallback = callback;
};

/**
 * 检查权限状态
 */
export const checkPermissionStatus = async (): Promise<PermissionStatus> => {
  try {
    log('[权限服务] 检查权限状态');
    
    // 检查麦克风权限
    const audioPermission = await Audio.getPermissionsAsync();
    const microphoneGranted = audioPermission.status === 'granted';
    
    // 检查语音识别权限
    let speechGranted = false;
    try {
      const speechPermission = await ExpoSpeechRecognitionModule.getPermissionsAsync();
      speechGranted = speechPermission.granted;
    } catch (error) {
      logError('[权限服务] 获取语音识别权限状态失败:', error);
      speechGranted = false;
    }
    
    const status = {
      microphone: microphoneGranted,
      speech: speechGranted,
    };
    
    log('[权限服务] 权限状态检查结果:', status);
    return status;
  } catch (error) {
    logError('[权限服务] 检查权限状态失败:', error);
    return {
      microphone: false,
      speech: false,
    };
  }
};

/**
 * 请求麦克风权限
 */
export const requestMicrophonePermission = async (): Promise<boolean> => {
  try {
    log('[权限服务] 请求麦克风权限');
    
    // Web平台处理
    if (Platform.OS === 'web') {
      try {
        if (!navigator.mediaDevices) {
          logError('[权限服务] Web平台不支持麦克风访问');
          return false;
        }
        
        const mediaPermission = await navigator.mediaDevices.getUserMedia({ 
          audio: true,
          video: false
        });
        
        if (mediaPermission) {
          log('[权限服务] Web麦克风权限已获取');
          // 停止所有媒体轨道，防止麦克风一直被占用
          mediaPermission.getTracks().forEach(track => track.stop());
          return true;
        }
      } catch (error) {
        logError('[权限服务] Web麦克风权限请求失败:', error);
        return false;
      }
    }

    // 移动平台处理
    const permission = await Audio.requestPermissionsAsync();
    const granted = permission.status === 'granted';
    
    log('[权限服务] 麦克风权限请求结果:', granted);
    return granted;
  } catch (error) {
    logError('[权限服务] 请求麦克风权限失败:', error);
    return false;
  }
};

/**
 * 请求语音识别权限
 */
export const requestSpeechRecognitionPermission = async (): Promise<boolean> => {
  try {
    log('[权限服务] 请求语音识别权限');
    
    // 检查语音识别服务是否可用
    const recognitionAvailable = await ExpoSpeechRecognitionModule.isRecognitionAvailable();
    if (!recognitionAvailable) {
      warn('[权限服务] 语音识别服务不可用');
      return false;
    }
    
    // 请求语音识别权限
    const permissions = await ExpoSpeechRecognitionModule.requestPermissionsAsync();
    const granted = permissions.granted;
    
    log('[权限服务] 语音识别权限请求结果:', granted);
    return granted;
  } catch (error) {
    logError('[权限服务] 请求语音识别权限失败:', error);
    return false;
  }
};

/**
 * 检查并请求指定类型的权限
 */
export const checkAndRequestPermissions = async (type: PermissionType): Promise<PermissionCheckResult> => {
  try {
    log(`[权限服务] 检查并请求权限类型: ${type}`);
    
    // 首先检查当前权限状态
    let status = await checkPermissionStatus();
    
    // 根据类型检查是否已有权限
    const hasRequiredPermissions = 
      (type === 'microphone' && status.microphone) ||
      (type === 'speech' && status.speech) ||
      (type === 'both' && status.microphone && status.speech);
    
    if (hasRequiredPermissions) {
      log('[权限服务] 所需权限已授予');
      return {
        granted: true,
        status,
        needsGuide: false,
        permissionType: type,
      };
    }
    
    log('[权限服务] 权限未授予，尝试请求权限');
    
    // 请求所需权限
    let microphoneGranted = status.microphone;
    let speechGranted = status.speech;
    
    if ((type === 'microphone' || type === 'both') && !status.microphone) {
      microphoneGranted = await requestMicrophonePermission();
    }
    
    if ((type === 'speech' || type === 'both') && !status.speech) {
      speechGranted = await requestSpeechRecognitionPermission();
    }
    
    // 更新状态
    status = {
      microphone: microphoneGranted,
      speech: speechGranted,
    };
    
    // 检查是否获得了所需权限
    const finalHasRequiredPermissions = 
      (type === 'microphone' && status.microphone) ||
      (type === 'speech' && status.speech) ||
      (type === 'both' && status.microphone && status.speech);
    
    if (finalHasRequiredPermissions) {
      log('[权限服务] 权限请求成功');
      return {
        granted: true,
        status,
        needsGuide: false,
        permissionType: type,
      };
    }
    
    // 权限被拒绝，需要引导用户到设置
    log('[权限服务] 权限被拒绝，需要引导用户到设置');
    
    // 确定具体需要引导的权限类型
    let guideType: PermissionType = type;
    if (type === 'both') {
      if (!status.microphone && !status.speech) {
        guideType = 'both';
      } else if (!status.microphone) {
        guideType = 'microphone';
      } else {
        guideType = 'speech';
      }
    }
    
    return {
      granted: false,
      status,
      needsGuide: true,
      permissionType: guideType,
    };
    
  } catch (error) {
    logError('[权限服务] 检查和请求权限失败:', error);
    
    const status = await checkPermissionStatus();
    return {
      granted: false,
      status,
      needsGuide: true,
      permissionType: type,
    };
  }
};

/**
 * 检查并处理权限，如需要会自动显示引导
 */
export const ensurePermissions = async (type: PermissionType): Promise<boolean> => {
  try {
    log(`[权限服务] 确保权限: ${type}`);
    
    const result = await checkAndRequestPermissions(type);
    
    if (result.granted) {
      log('[权限服务] 权限已授予');
      return true;
    }
    
    if (result.needsGuide && permissionGuideCallback) {
      log('[权限服务] 显示权限引导');
      permissionGuideCallback(result.permissionType);
    }
    
    return false;
  } catch (error) {
    logError('[权限服务] 确保权限失败:', error);
    return false;
  }
};

/**
 * 快速检查权限（不请求，只检查状态）
 */
export const quickCheckPermissions = async (type: PermissionType): Promise<boolean> => {
  try {
    const status = await checkPermissionStatus();
    
    switch (type) {
      case 'microphone':
        return status.microphone;
      case 'speech':
        return status.speech;
      case 'both':
        return status.microphone && status.speech;
      default:
        return false;
    }
  } catch (error) {
    logError('[权限服务] 快速检查权限失败:', error);
    return false;
  }
};

 