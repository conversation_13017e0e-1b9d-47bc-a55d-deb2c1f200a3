/**
 * 统一代理服务客户端
 * 提供统一的API调用接口，支持fallback机制
 */
import Constants from 'expo-constants';
import { log, warn, error as logError } from '@/services/logService';

// 代理服务配置 - 开发环境使用本地IP地址
const getProxyBaseUrl = () => {
  // 如果是开发环境，使用本地IP地址而不是localhost
  if (__DEV__ || Constants.expoConfig?.extra?.isDevEnv === 'true') {
    // 从环境配置获取本地IP，如果没有则使用默认IP
    const localIp = Constants.expoConfig?.extra?.localIp || '************';
    return `http://${localIp}:8001`;
  }
  
  // 生产环境使用Supabase的边缘函数
  return Constants.expoConfig?.extra?.supabaseUrl 
    ? `${Constants.expoConfig.extra.supabaseUrl}/functions/v1/unified-proxy`
    : 'http://localhost:54321/functions/v1/unified-proxy';
};

const PROXY_BASE_URL = getProxyBaseUrl();

log(`🔗 代理服务URL配置: ${PROXY_BASE_URL}`);
log(`🔧 开发环境: ${__DEV__}`);
log(`🔧 配置环境: ${Constants.expoConfig?.extra?.isDevEnv}`);

// 代理请求接口
interface ProxyRequest {
  service: 'siliconflow' | 'openai';
  path: string;
  method?: string;
  headers?: Record<string, string>;
  body?: any;
}

// 代理响应接口
interface ProxyResponse {
  success: boolean;
  data?: any;
  error?: string;
}

/**
 * 通过统一代理调用API（简化版本，移除fallback机制）
 * @param request 代理请求参数
 * @param abortSignal 取消信号
 * @param retryCount 重试次数，默认3次
 * @returns API响应
 */
export const callWithProxy = async (
  request: ProxyRequest,
  abortSignal?: AbortSignal,
  retryCount: number = 3
): Promise<{ response: Response; isProxied: boolean }> => {
  let lastError: Error | null = null;
  
  for (let attempt = 1; attempt <= retryCount; attempt++) {
    try {
      log(`尝试通过代理调用 ${request.service} API... (第${attempt}/${retryCount}次)`);

      // 获取用户认证token
      const { supabase } = await import('./supabaseService');
      const { data: { session } } = await supabase.auth.getSession();
      
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      // 添加用户认证头部（如果用户已登录）
      if (session?.access_token) {
        headers['Authorization'] = `Bearer ${session.access_token}`;
        log('添加用户认证头部');
      } else {
        warn('用户未登录，将匿名调用代理服务');
      }

      const proxyResponse = await fetch(PROXY_BASE_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
        signal: abortSignal,
      });

      if (proxyResponse.ok) {
        log(`代理调用成功: ${request.service}`);
        return { response: proxyResponse, isProxied: true };
      } else {
        const errorText = await proxyResponse.text();
        const error = new Error(`代理调用失败: ${proxyResponse.status} ${proxyResponse.statusText} - ${errorText}`);
        warn(error.message);
        lastError = error;
        
        // 如果是客户端错误（4xx），不要重试
        if (proxyResponse.status >= 400 && proxyResponse.status < 500) {
          throw error;
        }
        
        // 服务器错误或网络错误，继续重试
        if (attempt < retryCount) {
          log(`将在1秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // 递增延迟
        }
      }
    } catch (error) {
      warn(`代理调用失败 (第${attempt}/${retryCount}次):`, error);
      lastError = error as Error;
      
      // 如果是取消信号，立即抛出
      if (abortSignal?.aborted) {
        throw new Error('请求已取消');
      }
      
      // 如果还有重试机会，继续重试
      if (attempt < retryCount) {
        log(`将在1秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // 递增延迟
      }
    }
  }
  
  // 所有重试都失败
  throw lastError || new Error('代理调用失败，已达到最大重试次数');
};

/**
 * 调用 SiliconFlow AI API
 * @param path API路径
 * @param body 请求体
 * @param apiKey API密钥（已弃用，保留用于兼容性）
 * @param abortSignal 取消信号
 * @returns API响应
 */
export const callSiliconFlowAPI = async (
  path: string,
  body: any,
  apiKey?: string,
  abortSignal?: AbortSignal
): Promise<{ response: Response; isProxied: boolean }> => {
  const request: ProxyRequest = {
    service: 'siliconflow',
    path,
    method: 'POST',
    body
  };

  // 使用统一代理，不再需要fallback
  return await callWithProxy(request, abortSignal);
};

/**
 * 调用 OpenAI API
 * @param path API路径
 * @param body 请求体（对于FormData，直接传递）
 * @param apiKey API密钥（已弃用，保留用于兼容性）
 * @param abortSignal 取消信号
 * @param isFormData 是否为FormData请求
 * @returns API响应
 */
export const callOpenAIAPI = async (
  path: string,
  body: any,
  apiKey?: string,
  abortSignal?: AbortSignal,
  isFormData: boolean = false
): Promise<{ response: Response; isProxied: boolean }> => {
  if (isFormData) {
    // 对于FormData请求（如语音转文字），使用特殊的代理调用方式
    log('FormData请求，尝试通过代理调用OpenAI API');
    
    try {
      // 获取用户认证token
      const { supabase } = await import('./supabaseService');
      const { data: { session } } = await supabase.auth.getSession();
      
      const headers: Record<string, string> = {
        'x-proxy-service': 'openai',
        'x-proxy-path': path,
        'x-proxy-method': 'POST'
        // 注意：不要设置Content-Type，让浏览器自动设置multipart/form-data的boundary
      };
      
      // 尝试从FormData中提取模型信息并设置到头部
      if (body instanceof FormData) {
        const modelValue = body.get('model');
        if (modelValue && typeof modelValue === 'string') {
          headers['x-model'] = modelValue;
          log(`FormData请求包含模型: ${modelValue}`);
        }
      }
      
      // 添加用户认证头部（如果用户已登录）
      if (session?.access_token) {
        headers['Authorization'] = `Bearer ${session.access_token}`;
        log('FormData请求添加用户认证头部');
      } else {
        warn('FormData请求：用户未登录，将匿名调用代理服务');
      }
      
      // 尝试通过代理发送FormData
      const proxyResponse = await fetch(PROXY_BASE_URL, {
        method: 'POST',
        headers,
        body,
        signal: abortSignal,
      });

      if (proxyResponse.ok) {
        log('OpenAI代理调用成功');
        return { response: proxyResponse, isProxied: true };
      } else {
        warn(`FormData代理调用失败: ${proxyResponse.status}`);
        throw new Error(`代理调用失败: ${proxyResponse.status}`);
      }
    } catch (proxyError) {
      logError('FormData代理调用失败:', proxyError);
      throw proxyError;
    }
  }

  // 普通JSON请求通过代理
  const request: ProxyRequest = {
    service: 'openai',
    path,
    method: 'POST',
    body
  };

  // 使用统一代理，不再需要fallback
  return await callWithProxy(request, abortSignal);
};

/**
 * 检查代理服务是否可用
 * @returns 代理服务状态
 */
export const checkProxyHealth = async (): Promise<boolean> => {
  try {
    const response = await fetch(PROXY_BASE_URL, {
      method: 'OPTIONS',
    });
    return response.ok;
  } catch (error) {
    warn('代理服务健康检查失败:', error);
    return false;
  }
};

/**
 * 测试代理服务连接
 * @returns 测试结果
 */
export const testProxyConnection = async (): Promise<{
  success: boolean;
  url: string;
  message: string;
  details?: any;
}> => {
  log(`🧪 测试代理服务连接: ${PROXY_BASE_URL}`);
  
  try {
    // 测试OPTIONS请求
    const healthResponse = await fetch(PROXY_BASE_URL, {
      method: 'OPTIONS',
    });
    
    if (!healthResponse.ok) {
      return {
        success: false,
        url: PROXY_BASE_URL,
        message: `健康检查失败: ${healthResponse.status}`,
        details: { status: healthResponse.status, statusText: healthResponse.statusText }
      };
    }
    
    // 测试SiliconFlow代理
    const testRequest = {
      service: 'siliconflow' as const,
      path: '/v1/chat/completions',
      method: 'POST',
      body: {
        model: 'Qwen/Qwen2.5-7B-Instruct',
        messages: [{ role: 'user', content: '测试' }],
        max_tokens: 50
      }
    };
    
    const testResponse = await fetch(PROXY_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testRequest),
    });
    
    const result = await testResponse.text();
    
    return {
      success: testResponse.ok,
      url: PROXY_BASE_URL,
      message: testResponse.ok ? '代理服务正常' : `测试失败: ${testResponse.status}`,
      details: {
        status: testResponse.status,
        statusText: testResponse.statusText,
        response: result.substring(0, 200) + (result.length > 200 ? '...' : '')
      }
    };
    
  } catch (error) {
    return {
      success: false,
      url: PROXY_BASE_URL,
      message: `连接失败: ${error instanceof Error ? error.message : '未知错误'}`,
      details: { error: error }
    };
  }
};

export default {
  callWithProxy,
  callSiliconFlowAPI,
  callOpenAIAPI,
  checkProxyHealth,
  testProxyConnection
}; 