/**
 * 历史记录服务
 * 处理历史对话记录的增删查改
 */
import { getVipStatus } from './storageService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { generateId, formatDate } from '@/utils/helpers';
import * as FileSystem from 'expo-file-system';
import { log, error as logError } from '@/services/logService';

// 存储键名
const STORAGE_KEYS = {
  HISTORY_RECORDS: 'knowme_history_records',
  HISTORY_LAST_UPDATED: 'knowme_history_last_updated',
};

// 历史记录接口
export interface HistoryRecord {
  id: string;
  timestamp: number;
  originalText: string;
  optimizedText: string;
  templateId: string;
  templateName: string;
  audioUri?: string;
  sourceRecordId?: string; // 二次优化的原始记录ID，仅二次优化记录有此字段
  optimizedResults?: OptimizedResult[]; // 添加多次优化结果数组
}

// 新增：优化结果接口
export interface OptimizedResult {
  text: string;
  timestamp: number;
  templateId: string;
  templateName: string;
}

// 添加一些扩展属性，用于UI显示
export interface HistoryRecordWithUI extends HistoryRecord {
  formattedDate: string; // 格式化后的日期
  displayOriginalText: string; // 用于显示的原文文本（可能被截断）
  displayOptimizedText: string; // 用于显示的优化文本（可能被截断）
}

// 历史记录分组接口
export interface HistoryGroup {
  title: string; // 分组标题，如"今天"、"昨天"、"2023-11-10"
  data: HistoryRecordWithUI[]; // 分组下的历史记录
}

/**
 * 保存历史记录
 * @param record 历史记录对象
 * @returns 保存是否成功
 */
export const saveHistoryRecord = async (record: HistoryRecord): Promise<boolean> => {
  try {
    // 确保记录有ID
    if (!record.id) {
      record.id = generateId();
    }
    
    // 确保时间戳存在
    if (!record.timestamp) {
      record.timestamp = Date.now();
    }
    
    // 获取现有历史记录
    const records = await getHistoryRecords();
    
    // 处理原始优化记录（来自语音输入）
    if (record.audioUri && !record.sourceRecordId) {
      // 检查记录ID是否已经存在于记录列表中
      const existingRecordIndex = records.findIndex(r => r.id === record.id);

      if (existingRecordIndex >= 0) {
        // 如果记录ID已存在，说明这是optimizeText中决定要合并的记录，直接更新
        log('更新已存在的记录(historyService)，记录ID:', record.id);
        log('原始记录是否有优化结果数组:', !!records[existingRecordIndex].optimizedResults);

        // 直接替换整个记录，保持optimizeText中构建的完整结构
        records[existingRecordIndex] = record;
        log('记录已更新，优化结果数量:', record.optimizedResults?.length || 0);
      } else {
        // 如果记录ID不存在，说明这是新记录，直接添加
        log('添加新的语音历史记录(historyService):', record.id, 'templateId:', record.templateId);

        // 确保优化结果数组存在
        if (!record.optimizedResults) {
          record.optimizedResults = [{
            text: record.optimizedText,
            timestamp: record.timestamp,
            templateId: record.templateId,
            templateName: record.templateName
          }];
        }

        // 添加到记录列表的开头
        records.unshift(record);
      }
    }
    // 处理二次优化记录
    else if (record.sourceRecordId) {
      // 这是一个二次优化记录，将其作为独立记录保存
      log('将二次优化结果作为独立记录保存:', record.id);
      
      // 通过ID查找是否已存在相同的二次优化记录
      const existingIndex = records.findIndex(r => r.id === record.id);
      
      // 初始化优化结果数组
      record.optimizedResults = [{
        text: record.optimizedText,
        timestamp: record.timestamp,
        templateId: record.templateId,
        templateName: record.templateName
      }];
      
      // 如果已存在相同ID的记录则更新，否则添加新记录
      if (existingIndex >= 0) {
        records[existingIndex] = record;
      } else {
        records.unshift(record);
      }
    } else {
      // 没有音频也没有源记录ID的情况（可能是导入的记录）
      log('保存没有音频的历史记录:', record.id);
      
      // 通过ID查找是否已存在
      const existingIndex = records.findIndex(r => r.id === record.id);
      
      // 初始化优化结果数组
      record.optimizedResults = [{
        text: record.optimizedText,
        timestamp: record.timestamp,
        templateId: record.templateId,
        templateName: record.templateName
      }];
      
      if (existingIndex >= 0) {
        records[existingIndex] = record;
      } else {
        records.unshift(record);
      }
    }
    
    // 注意：不再在保存时删除记录，所有记录都保存到数据库
    // VIP限制只在UI显示层面处理，确保用户升级VIP后能看到历史记录
    log('保存历史记录，当前总数:', records.length);
    
    // 保存历史记录
    await AsyncStorage.setItem(STORAGE_KEYS.HISTORY_RECORDS, JSON.stringify(records));
    
    // 添加日志，验证保存后的记录是否包含optimizedResults
    const savedJson = await AsyncStorage.getItem(STORAGE_KEYS.HISTORY_RECORDS);
    if (savedJson) {
      const savedRecords = JSON.parse(savedJson);
      const indexInSaved = savedRecords.findIndex((r: HistoryRecord) => r.id === record.id);
      if (indexInSaved >= 0) {
        log('验证保存的记录:', {
          id: savedRecords[indexInSaved].id,
          有优化结果数组: !!savedRecords[indexInSaved].optimizedResults,
          优化结果数量: savedRecords[indexInSaved].optimizedResults?.length || 0
        });
      }
    }
    
    // 更新最后更新时间戳
    const timestamp = Date.now();
    await AsyncStorage.setItem(STORAGE_KEYS.HISTORY_LAST_UPDATED, timestamp.toString());
    
    return true;
  } catch (error) {
    logError('保存历史记录失败:', error);
    return false;
  }
};

/**
 * 获取所有历史记录
 * @returns 历史记录数组
 */
export const getHistoryRecords = async (): Promise<HistoryRecord[]> => {
  try {
    const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.HISTORY_RECORDS);
    return jsonValue ? JSON.parse(jsonValue) : [];
  } catch (error) {
    logError('获取历史记录失败:', error);
    return [];
  }
};

/**
 * 根据ID获取历史记录
 * @param id 历史记录ID
 * @returns 历史记录或null
 */
export const getHistoryRecordById = async (id: string): Promise<HistoryRecord | null> => {
  try {
    // 直接从AsyncStorage获取记录，确保数据最新
    const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.HISTORY_RECORDS);
    const records = jsonValue ? JSON.parse(jsonValue) : [];
    const record = records.find((record: HistoryRecord) => record.id === id) || null;
    
    // 添加详细日志
    log(`通过ID(${id})获取历史记录:`, {
      找到记录: !!record,
      原始文本长度: record?.originalText?.length,
      优化文本长度: record?.optimizedText?.length,
      有优化结果数组: !!record?.optimizedResults,
      优化结果数量: record?.optimizedResults?.length || 0,
      时间戳: record?.timestamp
    });
    
    if (record?.optimizedResults) {
      log('优化结果数组详情:', record.optimizedResults.map((r: OptimizedResult) => ({
        文本长度: r.text.length,
        模板名称: r.templateName,
        时间戳: r.timestamp,
        日期时间: new Date(r.timestamp).toLocaleString()
      })));
    }
    
    return record;
  } catch (error) {
    logError('根据ID获取历史记录失败:', error);
    return null;
  }
};

/**
 * 删除历史记录
 * @param id 要删除的历史记录ID
 * @returns 删除是否成功
 */
export const deleteHistoryRecord = async (id: string): Promise<boolean> => {
  try {
    // 获取所有记录
    const records = await getHistoryRecords();
    
    // 查找要删除的记录
    const recordToDelete = records.find(record => record.id === id);
    
    if (!recordToDelete) {
      return false; // 记录不存在
    }
    
    // 如果有音频文件，也删除它
    if (recordToDelete.audioUri) {
      try {
        const fileInfo = await FileSystem.getInfoAsync(recordToDelete.audioUri);
        if (fileInfo.exists) {
          await FileSystem.deleteAsync(recordToDelete.audioUri);
        }
      } catch (fileError) {
        console.warn('删除音频文件失败:', fileError);
        // 继续删除记录，即使文件删除失败
      }
    }
    
    // 过滤掉要删除的记录
    const updatedRecords = records.filter(record => record.id !== id);
    
    // 保存更新后的记录
    await AsyncStorage.setItem(STORAGE_KEYS.HISTORY_RECORDS, JSON.stringify(updatedRecords));
    
    // 更新最后更新时间戳
    const timestamp = Date.now();
    await AsyncStorage.setItem(STORAGE_KEYS.HISTORY_LAST_UPDATED, timestamp.toString());
    
    return true;
  } catch (error) {
    logError('删除历史记录失败:', error);
    return false;
  }
};

/**
 * 清空所有历史记录
 * @returns 清空是否成功
 */
export const clearAllHistoryRecords = async (): Promise<boolean> => {
  try {
    // 获取所有记录以删除音频文件
    const records = await getHistoryRecords();
    
    // 删除所有音频文件
    for (const record of records) {
      if (record.audioUri) {
        try {
          const fileInfo = await FileSystem.getInfoAsync(record.audioUri);
          if (fileInfo.exists) {
            await FileSystem.deleteAsync(record.audioUri);
          }
        } catch (error) {
          console.warn('删除音频文件失败:', error);
          // 继续处理其他文件
        }
      }
    }
    
    // 清空历史记录
    await AsyncStorage.setItem(STORAGE_KEYS.HISTORY_RECORDS, JSON.stringify([]));
    
    // 更新最后更新时间戳
    const timestamp = Date.now();
    await AsyncStorage.setItem(STORAGE_KEYS.HISTORY_LAST_UPDATED, timestamp.toString());
    
    return true;
  } catch (error) {
    logError('清空历史记录失败:', error);
    return false;
  }
};

/**
 * 获取按日期分组的历史记录
 * @param limit 限制加载的记录条数，默认不限制
 * @param t 翻译函数，用于多语言支持
 * @returns 按日期分组的历史记录数组
 */
export const getGroupedHistoryRecords = async (limit?: number, t?: (key: string) => string): Promise<HistoryGroup[]> => {
  try {
    // 获取全部历史记录
    const records = await getHistoryRecords();
    
    // 如果限制了条数，则只返回指定条数的记录
    let filteredRecords = records;
    if (limit && limit > 0) {
      // 检查VIP状态，非VIP用户最多只能看到50条记录
      const vipStatus = await getVipStatus();
      let actualLimit = limit;
      
      if (!vipStatus.isVip) {
        // 非VIP用户限制最多50条
        actualLimit = Math.min(limit, 50);
        log(`非VIP用户，限制显示最多50条记录: 共${records.length}条，请求${limit}条，实际返回${actualLimit}条`);
      } else {
        log(`VIP用户，按请求加载: 共${records.length}条，加载前${limit}条`);
      }
      
      filteredRecords = records.slice(0, actualLimit);
    }
    
    // 按日期分组
    const groups: { [key: string]: HistoryRecordWithUI[] } = {};
    
    // 当前日期
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const twoDaysAgo = new Date();
    twoDaysAgo.setDate(today.getDate() - 2);

    // 获取多语言日期标签
    const todayLabel = t ? t("history.today") : "今天";
    const yesterdayLabel = t ? t("history.yesterday") : "昨天";
    const twoDaysAgoLabel = t ? t("history.dayBeforeYesterday") : "前天";
    
    // 转换为UI显示格式并按日期分组
    filteredRecords.forEach(record => {
      // 创建日期对象
      const recordDate = new Date(record.timestamp);
      
      // 格式化日期，传递翻译函数
      let dateKey = formatDate(record.timestamp, t);
      
      // 分组的日期键和日期显示保持一致，都使用formatDate函数的结果（今天/昨天/前天/具体日期）
      // 注意：formatDate已经返回正确的日期标签，不需要手动替换
      
      // 创建带UI属性的记录
      const recordWithUI: HistoryRecordWithUI = {
        ...record,
        formattedDate: dateKey,
        // 截断长文本以提高显示效率
        displayOriginalText: record.originalText.length > 100 
          ? record.originalText.substring(0, 100) + "..." 
          : record.originalText,
        displayOptimizedText: record.optimizedText.length > 100 
          ? record.optimizedText.substring(0, 100) + "..." 
          : record.optimizedText
      };
      
      // 添加到相应分组
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(recordWithUI);
    });
    
    // 将分组对象转换为数组格式并按时间倒序排序
    return Object.entries(groups)
      .map(([title, data]) => ({ title, data }))
      .sort((a, b) => {
        // 特殊处理多语言日期分组
        if (a.title === todayLabel) return -1;
        if (b.title === todayLabel) return 1;
        if (a.title === yesterdayLabel) return -1;
        if (b.title === yesterdayLabel) return 1;
        if (a.title === twoDaysAgoLabel) return -1;
        if (b.title === twoDaysAgoLabel) return 1;
        // 其他情况按日期倒序排序
        return new Date(b.title).getTime() - new Date(a.title).getTime();
      });
  } catch (error) {
    logError('获取分组历史记录失败:', error);
    return [];
  }
};

/**
 * 获取最后更新时间戳
 * @returns 最后更新的时间戳，如果没有记录则返回0
 */
export const getLastUpdatedTimestamp = async (): Promise<number> => {
  try {
    const timestampStr = await AsyncStorage.getItem(STORAGE_KEYS.HISTORY_LAST_UPDATED);
    if (timestampStr) {
      return parseInt(timestampStr, 10);
    }
    return 0;
  } catch (error) {
    logError('获取最后更新时间戳失败:', error);
    return 0;
  }
};

/**
 * 获取真实的历史记录总数（不受VIP限制）
 * @returns 真实的历史记录总数
 */
export const getRealHistoryRecordsCount = async (): Promise<number> => {
  try {
    const records = await getHistoryRecords();
    return records.length;
  } catch (error) {
    logError('获取真实历史记录总数失败:', error);
    return 0;
  }
};

/**
 * 获取所有历史记录中使用过的模板名称（用于标签筛选）
 * @returns 模板名称数组
 */
export const getAllTemplateNamesFromHistory = async (): Promise<string[]> => {
  try {
    const records = await getHistoryRecords();
    const templateNames = new Set<string>();
    
    records.forEach(record => {
      if (record.templateName) {
        templateNames.add(record.templateName);
      }
    });
    
    return Array.from(templateNames).sort();
  } catch (error) {
    logError('获取历史记录模板名称失败:', error);
    return [];
  }
};

/**
 * 根据模板名称获取分组的历史记录
 * @param templateName 模板名称，如果为空则返回所有记录
 * @param limit 限制加载的记录条数
 * @param t 翻译函数，用于多语言支持
 * @returns 按日期分组的历史记录数组
 */
export const getGroupedHistoryRecordsByTemplate = async (
  templateName?: string, 
  limit?: number,
  t?: (key: string) => string
): Promise<HistoryGroup[]> => {
  try {
    // 获取全部历史记录
    const allRecords = await getHistoryRecords();
    
    // 检查VIP状态
    const vipStatus = await getVipStatus();
    
    // 首先根据VIP状态限制可见记录范围
    let visibleRecords = allRecords;
    if (!vipStatus.isVip) {
      // 非VIP用户只能看最近的50条记录
      visibleRecords = allRecords.slice(0, 50);
      log(`非VIP用户，限制在最近50条记录范围内: 总共${allRecords.length}条，可见${visibleRecords.length}条`);
    } else {
      log(`VIP用户，可查看所有${allRecords.length}条记录`);
    }
    
    // 然后在可见记录范围内根据模板名称筛选
    let filteredRecords = visibleRecords;
    if (templateName && templateName !== "全部") {
      filteredRecords = visibleRecords.filter(record => record.templateName === templateName);
      log(`在可见记录中筛选模板"${templateName}": 找到${filteredRecords.length}条记录`);
    }
    
    // 最后应用分页限制
    if (limit && limit > 0) {
      filteredRecords = filteredRecords.slice(0, limit);
      log(`应用分页限制: 最终返回${filteredRecords.length}条记录`);
    }
    
    // 按日期分组
    const groups: { [key: string]: HistoryRecordWithUI[] } = {};
    
    // 当前日期
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const twoDaysAgo = new Date();
    twoDaysAgo.setDate(today.getDate() - 2);

    // 获取多语言日期标签
    const todayLabel = t ? t("history.today") : "今天";
    const yesterdayLabel = t ? t("history.yesterday") : "昨天";
    const twoDaysAgoLabel = t ? t("history.dayBeforeYesterday") : "前天";
    
    // 转换为UI显示格式并按日期分组
    filteredRecords.forEach(record => {
      // 创建日期对象
      const recordDate = new Date(record.timestamp);
      
      // 格式化日期，传递翻译函数
      let dateKey = formatDate(record.timestamp, t);
      
      // 创建带UI属性的记录
      const recordWithUI: HistoryRecordWithUI = {
        ...record,
        formattedDate: dateKey,
        // 截断长文本以提高显示效率
        displayOriginalText: record.originalText.length > 100 
          ? record.originalText.substring(0, 100) + "..." 
          : record.originalText,
        displayOptimizedText: record.optimizedText.length > 100 
          ? record.optimizedText.substring(0, 100) + "..." 
          : record.optimizedText
      };
      
      // 添加到相应分组
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(recordWithUI);
    });
    
    // 将分组对象转换为数组格式并按时间倒序排序
    return Object.entries(groups)
      .map(([title, data]) => ({ title, data }))
      .sort((a, b) => {
        // 特殊处理多语言日期分组
        if (a.title === todayLabel) return -1;
        if (b.title === todayLabel) return 1;
        if (a.title === yesterdayLabel) return -1;
        if (b.title === yesterdayLabel) return 1;
        if (a.title === twoDaysAgoLabel) return -1;
        if (b.title === twoDaysAgoLabel) return 1;
        // 其他情况按日期倒序排序
        return new Date(b.title).getTime() - new Date(a.title).getTime();
      });
  } catch (error) {
    logError('根据模板获取分组历史记录失败:', error);
    return [];
  }
};

/**
 * 获取指定模板的可见历史记录总数（考虑VIP限制）
 * @param templateName 模板名称，如果为空则返回所有记录数
 * @returns 指定模板的可见历史记录总数
 */
export const getVisibleHistoryRecordsCountByTemplate = async (templateName?: string): Promise<number> => {
  try {
    const allRecords = await getHistoryRecords();
    const vipStatus = await getVipStatus();
    
    // 首先根据VIP状态限制可见记录范围
    let visibleRecords = allRecords;
    if (!vipStatus.isVip) {
      // 非VIP用户只能看最近的50条记录
      visibleRecords = allRecords.slice(0, 50);
    }
    
    // 然后在可见记录范围内根据模板名称筛选
    if (templateName && templateName !== "全部") {
      visibleRecords = visibleRecords.filter(record => record.templateName === templateName);
    }
    
    return visibleRecords.length;
  } catch (error) {
    logError('获取指定模板的可见历史记录总数失败:', error);
    return 0;
  }
};

/**
 * 获取指定模板的真实历史记录总数（不受VIP限制）
 * @param templateName 模板名称，如果为空则返回所有记录数
 * @returns 指定模板的真实历史记录总数
 */
export const getRealHistoryRecordsCountByTemplate = async (templateName?: string): Promise<number> => {
  try {
    const allRecords = await getHistoryRecords();
    
    // 如果指定了模板名称，则筛选对应的记录
    let filteredRecords = allRecords;
    if (templateName && templateName !== "全部") {
      filteredRecords = allRecords.filter(record => record.templateName === templateName);
    }
    
    return filteredRecords.length;
  } catch (error) {
    logError('获取指定模板的真实历史记录总数失败:', error);
    return 0;
  }
};

/**
 * 获取用户可见的历史记录总数（考虑VIP限制）
 * @returns 用户可见的历史记录总数
 */
export const getVisibleHistoryRecordsCount = async (): Promise<number> => {
  try {
    const records = await getHistoryRecords();
    const vipStatus = await getVipStatus();
    
    if (!vipStatus.isVip) {
      // 非VIP用户最多只能看到50条
      return Math.min(records.length, 50);
    } else {
      // VIP用户可以看到所有记录
      return records.length;
    }
  } catch (error) {
    logError('获取可见历史记录总数失败:', error);
    return 0;
  }
};

/**
 * 检查指定模板是否有被VIP限制隐藏的记录
 * @param templateName 模板名称，如果为空则检查所有记录
 * @returns 隐藏的记录数量
 */
export const getHiddenRecordsCountByTemplate = async (templateName?: string): Promise<number> => {
  try {
    const allRecords = await getHistoryRecords();
    const vipStatus = await getVipStatus();
    
    // VIP用户没有隐藏记录
    if (vipStatus.isVip) {
      return 0;
    }
    
    // 筛选指定模板的所有记录
    let filteredRecords = allRecords;
    if (templateName && templateName !== "全部") {
      filteredRecords = allRecords.filter(record => record.templateName === templateName);
    }
    
    // 获取前50条记录中该模板的记录数
    const visibleRecords = allRecords.slice(0, 50);
    let visibleFilteredRecords = visibleRecords;
    if (templateName && templateName !== "全部") {
      visibleFilteredRecords = visibleRecords.filter(record => record.templateName === templateName);
    }
    
    // 返回隐藏的记录数量
    return Math.max(0, filteredRecords.length - visibleFilteredRecords.length);
  } catch (error) {
    logError('获取指定模板的隐藏记录总数失败:', error);
    return 0;
  }
};

/**
 * 获取在前50条记录范围内可用的模板名称（仅非VIP用户需要）
 * @returns 在前50条记录中存在的模板名称数组
 */
export const getAvailableTemplateNamesInVisibleRange = async (): Promise<string[]> => {
  try {
    const allRecords = await getHistoryRecords();
    const vipStatus = await getVipStatus();
    
    // VIP用户看到所有模板
    if (vipStatus.isVip) {
      const templateNames = new Set<string>();
      allRecords.forEach(record => {
        if (record.templateName) {
          templateNames.add(record.templateName);
        }
      });
      return Array.from(templateNames).sort();
    }
    
    // 非VIP用户只看前50条记录中的模板
    const visibleRecords = allRecords.slice(0, 50);
    const templateNames = new Set<string>();
    
    visibleRecords.forEach(record => {
      if (record.templateName) {
        templateNames.add(record.templateName);
      }
    });
    
    return Array.from(templateNames).sort();
  } catch (error) {
    logError('获取可见范围内的模板名称失败:', error);
    return [];
  }
}; 