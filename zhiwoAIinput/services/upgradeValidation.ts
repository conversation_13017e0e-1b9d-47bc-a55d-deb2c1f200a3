/**
 * 录音文件保存和读取逻辑升级验证脚本
 * 用于快速验证升级是否成功完成
 */
import { userIdService } from './userIdService';
import { newHistoryService } from './newHistoryService';
import { dataMigrationService } from './dataMigrationService';
import { log, error as logError } from '@/services/logService';

/**
 * 快速验证升级是否成功
 * @returns 验证结果
 */
export async function validateUpgrade(): Promise<{
  success: boolean;
  errors: string[];
  warnings: string[];
  summary: {
    userIdServiceReady: boolean;
    historyServiceReady: boolean;
    migrationCompleted: boolean;
    currentUserId: string;
    recordCount: number;
  };
}> {
  const errors: string[] = [];
  const warnings: string[] = [];
  let userIdServiceReady = false;
  let historyServiceReady = false;
  let migrationCompleted = false;
  let currentUserId = '';
  let recordCount = 0;

  try {
    log('🔍 开始验证录音文件保存和读取逻辑升级...');

    // 1. 验证用户ID服务
    try {
      await userIdService.initialize();
      currentUserId = await userIdService.getCurrentUserId();
      const isAnonymous = await userIdService.isAnonymousUser();
      
      if (currentUserId) {
        userIdServiceReady = true;
        log(`✅ 用户ID服务正常 - 当前用户: ${currentUserId} (匿名: ${isAnonymous})`);
      } else {
        errors.push('用户ID服务返回空的用户ID');
      }
    } catch (error) {
      errors.push(`用户ID服务初始化失败: ${error}`);
      logError('用户ID服务验证失败:', error);
    }

    // 2. 验证历史记录服务
    try {
      // 测试获取历史记录
      const records = await newHistoryService.getHistoryRecords(currentUserId, 10, 0);
      recordCount = await newHistoryService.getRealHistoryRecordsCount(currentUserId);
      
      historyServiceReady = true;
      log(`✅ 历史记录服务正常 - 当前记录数: ${recordCount}`);
      
      // 测试VIP权限控制
      const visibleCount = await newHistoryService.getVisibleHistoryRecordsCount(currentUserId);
      log(`📊 可见记录数: ${visibleCount} (总记录数: ${recordCount})`);
      
    } catch (error) {
      errors.push(`历史记录服务测试失败: ${error}`);
      logError('历史记录服务验证失败:', error);
    }

    // 3. 验证数据迁移状态
    try {
      const migrationStatus = await dataMigrationService.getMigrationStatus();
      migrationCompleted = migrationStatus.migrationCompleted;
      
      if (migrationCompleted) {
        log(`✅ 数据迁移已完成 - SQLite记录: ${migrationStatus.sqliteRecordsCount}, 遗留记录: ${migrationStatus.legacyRecordsCount}`);
      } else {
        if (migrationStatus.legacyRecordsCount > 0) {
          warnings.push(`发现 ${migrationStatus.legacyRecordsCount} 条遗留记录未迁移`);
        } else {
          log('ℹ️ 无需数据迁移（没有遗留数据）');
          migrationCompleted = true; // 没有遗留数据也算迁移完成
        }
      }
    } catch (error) {
      errors.push(`数据迁移状态检查失败: ${error}`);
      logError('数据迁移验证失败:', error);
    }

    // 4. 验证基本功能
    try {
      // 测试保存一条记录
      const testRecord = {
        id: `test_${Date.now()}`,
        timestamp: Date.now(),
        originalText: '升级验证测试',
        optimizedText: '这是一条用于验证升级的测试记录',
        templateId: 'test_template',
        templateName: '测试模板',
      };

      const saveSuccess = await newHistoryService.saveHistoryRecord(testRecord, currentUserId);
      if (saveSuccess) {
        log('✅ 记录保存功能正常');
        
        // 立即删除测试记录
        await newHistoryService.deleteHistoryRecord(testRecord.id, currentUserId);
        log('✅ 记录删除功能正常');
      } else {
        warnings.push('记录保存功能可能存在问题');
      }
    } catch (error) {
      warnings.push(`基本功能测试失败: ${error}`);
    }

    // 5. 生成验证结果
    const success = errors.length === 0 && userIdServiceReady && historyServiceReady;
    
    if (success) {
      log('🎉 升级验证成功！所有核心功能正常工作');
    } else {
      log('⚠️ 升级验证发现问题，请检查错误信息');
    }

    if (warnings.length > 0) {
      log('⚠️ 发现以下警告:');
      warnings.forEach(warning => log(`  - ${warning}`));
    }

    return {
      success,
      errors,
      warnings,
      summary: {
        userIdServiceReady,
        historyServiceReady,
        migrationCompleted,
        currentUserId,
        recordCount,
      },
    };

  } catch (error) {
    const errorMsg = `验证过程出现未预期的错误: ${error}`;
    errors.push(errorMsg);
    logError('升级验证失败:', error);
    
    return {
      success: false,
      errors,
      warnings,
      summary: {
        userIdServiceReady,
        historyServiceReady,
        migrationCompleted,
        currentUserId,
        recordCount,
      },
    };
  }
}

/**
 * 打印验证结果的详细报告
 * @param result 验证结果
 */
export function printValidationReport(result: Awaited<ReturnType<typeof validateUpgrade>>): void {
  console.log('\n📋 录音文件保存和读取逻辑升级验证报告');
  console.log('=' .repeat(50));
  
  console.log(`\n🎯 总体状态: ${result.success ? '✅ 成功' : '❌ 失败'}`);
  
  console.log('\n📊 服务状态:');
  console.log(`  用户ID服务: ${result.summary.userIdServiceReady ? '✅' : '❌'}`);
  console.log(`  历史记录服务: ${result.summary.historyServiceReady ? '✅' : '❌'}`);
  console.log(`  数据迁移: ${result.summary.migrationCompleted ? '✅' : '❌'}`);
  
  console.log('\n📈 数据统计:');
  console.log(`  当前用户ID: ${result.summary.currentUserId}`);
  console.log(`  历史记录数量: ${result.summary.recordCount}`);
  
  if (result.errors.length > 0) {
    console.log('\n❌ 错误信息:');
    result.errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
  }
  
  if (result.warnings.length > 0) {
    console.log('\n⚠️ 警告信息:');
    result.warnings.forEach((warning, index) => {
      console.log(`  ${index + 1}. ${warning}`);
    });
  }
  
  console.log('\n' + '=' .repeat(50));
  
  if (result.success) {
    console.log('🎉 升级验证完成！系统已准备就绪。');
  } else {
    console.log('💡 建议检查上述错误信息并重新运行验证。');
  }
}

/**
 * 运行验证并打印报告
 */
export async function runValidationWithReport(): Promise<boolean> {
  try {
    const result = await validateUpgrade();
    printValidationReport(result);
    return result.success;
  } catch (error) {
    console.error('验证过程出现异常:', error);
    return false;
  }
}
