/**
 * 生物识别认证服务
 * 提供指纹识别和人脸识别功能，用于应用加密
 */

import * as LocalAuthentication from 'expo-local-authentication';
import { Platform, Alert, AppState, AppStateStatus } from 'react-native';
import { log, error as logError, warn } from './logService';
import { getUserSettings } from './storageService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 生物识别认证类型
export enum BiometricType {
  FINGERPRINT = 'fingerprint',
  FACE_ID = 'faceId',
  IRIS = 'iris',
  NONE = 'none'
}

// 认证结果接口
export interface BiometricAuthResult {
  success: boolean;
  error?: string;
  biometricType?: BiometricType;
}

// 设备生物识别能力接口
export interface BiometricCapability {
  isAvailable: boolean;
  supportedTypes: BiometricType[];
  isEnrolled: boolean;
}

// 认证会话接口
export interface AuthSession {
  isAuthenticated: boolean;
  authenticatedAt: number;
  expiresAt: number;
}

// 认证会话配置
const AUTH_SESSION_CONFIG = {
  // 认证会话有效期（毫秒）- 默认5分钟
  SESSION_DURATION: 5 * 60 * 1000,
  // 应用后台超时时间（毫秒）- 超过此时间需要重新认证，默认30秒
  BACKGROUND_TIMEOUT: 30 * 1000,
  // 存储键
  STORAGE_KEY: 'biometric_auth_session',
  BACKGROUND_TIME_KEY: 'app_background_time'
};

/**
 * 检查设备是否支持生物识别认证
 * @returns 设备生物识别能力信息
 */
export const checkBiometricCapability = async (): Promise<BiometricCapability> => {
  try {
    log('[BiometricService] 检查设备生物识别能力');
    
    // 检查设备是否支持生物识别
    const isAvailable = await LocalAuthentication.hasHardwareAsync();
    if (!isAvailable) {
      log('[BiometricService] 设备不支持生物识别硬件');
      return {
        isAvailable: false,
        supportedTypes: [],
        isEnrolled: false
      };
    }
    
    // 检查是否已注册生物识别信息
    const isEnrolled = await LocalAuthentication.isEnrolledAsync();
    if (!isEnrolled) {
      log('[BiometricService] 设备未注册生物识别信息');
      return {
        isAvailable: true,
        supportedTypes: [],
        isEnrolled: false
      };
    }
    
    // 获取支持的生物识别类型
    const supportedAuthTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
    const supportedTypes: BiometricType[] = [];
    
    supportedAuthTypes.forEach(type => {
      switch (type) {
        case LocalAuthentication.AuthenticationType.FINGERPRINT:
          supportedTypes.push(BiometricType.FINGERPRINT);
          break;
        case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
          supportedTypes.push(BiometricType.FACE_ID);
          break;
        case LocalAuthentication.AuthenticationType.IRIS:
          supportedTypes.push(BiometricType.IRIS);
          break;
      }
    });
    
    log('[BiometricService] 设备生物识别能力检查完成', {
      isAvailable,
      isEnrolled,
      supportedTypes
    });
    
    return {
      isAvailable: true,
      supportedTypes,
      isEnrolled: true
    };
  } catch (error) {
    logError('[BiometricService] 检查生物识别能力失败:', error);
    return {
      isAvailable: false,
      supportedTypes: [],
      isEnrolled: false
    };
  }
};

/**
 * 执行生物识别认证
 * @param promptMessage 认证提示信息
 * @param fallbackLabel 备用认证方式标签（Android）
 * @returns 认证结果
 */
export const authenticateWithBiometrics = async (
  promptMessage: string = '请验证您的身份',
  fallbackLabel: string = '使用密码'
): Promise<BiometricAuthResult> => {
  try {
    log('[BiometricService] 开始生物识别认证');
    
    // 首先检查设备能力
    const capability = await checkBiometricCapability();
    if (!capability.isAvailable) {
      return {
        success: false,
        error: '设备不支持生物识别认证'
      };
    }
    
    if (!capability.isEnrolled) {
      return {
        success: false,
        error: '请先在系统设置中注册指纹或面容ID'
      };
    }
    
    if (capability.supportedTypes.length === 0) {
      return {
        success: false,
        error: '设备未配置可用的生物识别方式'
      };
    }
    
    // 执行认证
    const authResult = await LocalAuthentication.authenticateAsync({
      promptMessage,
      fallbackLabel: Platform.OS === 'android' ? fallbackLabel : undefined,
      disableDeviceFallback: false, // 允许设备备用认证方式
      cancelLabel: '取消'
    });
    
    if (authResult.success) {
      log('[BiometricService] 生物识别认证成功');
      return {
        success: true,
        biometricType: capability.supportedTypes[0] // 返回主要的生物识别类型
      };
    } else {
      const errorMessage = getAuthErrorMessage(authResult.error);
      warn('[BiometricService] 生物识别认证失败:', errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    }
  } catch (error) {
    logError('[BiometricService] 生物识别认证异常:', error);
    return {
      success: false,
      error: '认证过程中发生错误，请稍后重试'
    };
  }
};

/**
 * 获取当前认证会话
 * @returns 认证会话信息
 */
const getCurrentAuthSession = async (): Promise<AuthSession | null> => {
  try {
    const sessionData = await AsyncStorage.getItem(AUTH_SESSION_CONFIG.STORAGE_KEY);
    if (!sessionData) {
      return null;
    }

    const session: AuthSession = JSON.parse(sessionData);
    const now = Date.now();

    // 检查会话是否过期
    if (now > session.expiresAt) {
      log('[BiometricService] 认证会话已过期');
      await clearAuthSession();
      return null;
    }

    return session;
  } catch (error) {
    logError('[BiometricService] 获取认证会话失败:', error);
    return null;
  }
};

/**
 * 保存认证会话
 * @param session 认证会话信息
 */
const saveAuthSession = async (session: AuthSession): Promise<void> => {
  try {
    await AsyncStorage.setItem(AUTH_SESSION_CONFIG.STORAGE_KEY, JSON.stringify(session));
    log('[BiometricService] 认证会话已保存', {
      authenticatedAt: new Date(session.authenticatedAt).toISOString(),
      expiresAt: new Date(session.expiresAt).toISOString()
    });
  } catch (error) {
    logError('[BiometricService] 保存认证会话失败:', error);
  }
};

/**
 * 清除认证会话
 */
const clearAuthSession = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(AUTH_SESSION_CONFIG.STORAGE_KEY);
    log('[BiometricService] 认证会话已清除');
  } catch (error) {
    logError('[BiometricService] 清除认证会话失败:', error);
  }
};

/**
 * 记录应用进入后台的时间
 */
const recordBackgroundTime = async (): Promise<void> => {
  try {
    const backgroundTime = Date.now().toString();
    await AsyncStorage.setItem(AUTH_SESSION_CONFIG.BACKGROUND_TIME_KEY, backgroundTime);
    log('[BiometricService] 已记录应用后台时间');
  } catch (error) {
    logError('[BiometricService] 记录后台时间失败:', error);
  }
};

/**
 * 检查应用是否在后台停留过久
 * @returns 是否需要重新认证
 */
const checkBackgroundTimeout = async (): Promise<boolean> => {
  try {
    const backgroundTimeStr = await AsyncStorage.getItem(AUTH_SESSION_CONFIG.BACKGROUND_TIME_KEY);
    if (!backgroundTimeStr) {
      return false; // 没有后台时间记录，不需要重新认证
    }

    const backgroundTime = parseInt(backgroundTimeStr);
    const now = Date.now();
    const timeDiff = now - backgroundTime;

    log('[BiometricService] 检查后台时间', {
      backgroundTime: new Date(backgroundTime).toISOString(),
      timeDiff: `${timeDiff}ms`,
      timeout: `${AUTH_SESSION_CONFIG.BACKGROUND_TIMEOUT}ms`,
      needsReauth: timeDiff > AUTH_SESSION_CONFIG.BACKGROUND_TIMEOUT
    });

    // 清除后台时间记录
    await AsyncStorage.removeItem(AUTH_SESSION_CONFIG.BACKGROUND_TIME_KEY);

    return timeDiff > AUTH_SESSION_CONFIG.BACKGROUND_TIMEOUT;
  } catch (error) {
    logError('[BiometricService] 检查后台超时失败:', error);
    return true; // 出错时保守处理，要求重新认证
  }
};

/**
 * 检查应用是否启用了加密功能
 * @returns 是否启用应用加密
 */
export const isAppLockEnabled = async (): Promise<boolean> => {
  try {
    const settings = await getUserSettings();
    return settings.appLockEnabled || false;
  } catch (error) {
    logError('[BiometricService] 获取应用加密设置失败:', error);
    return false;
  }
};

/**
 * 智能检查是否需要生物识别认证
 * 考虑会话状态、应用后台时间等因素
 * @param forceCheck 是否强制检查（忽略会话）
 * @returns 是否需要认证
 */
export const shouldRequireAuthentication = async (forceCheck: boolean = false): Promise<boolean> => {
  try {
    // 首先检查是否启用了应用加密
    const appLockEnabled = await isAppLockEnabled();
    if (!appLockEnabled) {
      log('[BiometricService] 应用加密未启用，无需认证');
      return false;
    }

    // 如果强制检查，直接返回需要认证
    if (forceCheck) {
      log('[BiometricService] 强制检查，需要认证');
      return true;
    }

    // 检查当前认证会话
    const currentSession = await getCurrentAuthSession();
    if (!currentSession || !currentSession.isAuthenticated) {
      log('[BiometricService] 无有效认证会话，需要认证');
      return true;
    }

    // 检查应用是否在后台停留过久
    const backgroundTimeout = await checkBackgroundTimeout();
    if (backgroundTimeout) {
      log('[BiometricService] 应用后台时间过长，需要重新认证');
      await clearAuthSession(); // 清除过期会话
      return true;
    }

    log('[BiometricService] 认证会话有效，无需重新认证');
    return false;
  } catch (error) {
    logError('[BiometricService] 检查认证需求失败:', error);
    return true; // 出错时保守处理，要求认证
  }
};

/**
 * 执行生物识别认证并管理会话
 * @param promptMessage 认证提示信息
 * @param fallbackLabel 备用认证方式标签（Android）
 * @param forceAuth 是否强制认证（忽略现有会话）
 * @returns 认证结果
 */
export const authenticateWithSession = async (
  promptMessage: string = '请验证您的身份',
  fallbackLabel: string = '使用密码',
  forceAuth: boolean = false
): Promise<BiometricAuthResult> => {
  try {
    // 检查是否需要认证
    const needsAuth = await shouldRequireAuthentication(forceAuth);
    if (!needsAuth) {
      log('[BiometricService] 无需重新认证，使用现有会话');
      return {
        success: true,
        biometricType: BiometricType.FINGERPRINT // 默认类型，实际类型可能不同
      };
    }

    // 执行生物识别认证
    const authResult = await authenticateWithBiometrics(promptMessage, fallbackLabel);

    if (authResult.success) {
      // 认证成功，创建新会话
      const now = Date.now();
      const session: AuthSession = {
        isAuthenticated: true,
        authenticatedAt: now,
        expiresAt: now + AUTH_SESSION_CONFIG.SESSION_DURATION
      };

      await saveAuthSession(session);
      log('[BiometricService] 认证成功，已创建新会话');
    }

    return authResult;
  } catch (error) {
    logError('[BiometricService] 会话认证失败:', error);
    return {
      success: false,
      error: '认证过程中发生错误，请稍后重试'
    };
  }
};

/**
 * 显示生物识别设置引导
 * @param onSetupComplete 设置完成回调
 */
export const showBiometricSetupGuide = (onSetupComplete?: () => void) => {
  Alert.alert(
    '启用生物识别认证',
    '为了保护您的隐私，建议启用指纹或面容ID认证。请前往系统设置完成配置。',
    [
      {
        text: '稍后设置',
        style: 'cancel'
      },
      {
        text: '前往设置',
        onPress: () => {
          // 在实际应用中，这里可以打开系统设置
          if (Platform.OS === 'ios') {
            // iOS 可以使用 Linking.openURL('App-Prefs:root=TOUCHID_PASSCODE')
            // 但这个API可能不被App Store接受，所以这里只是提示
            Alert.alert('提示', '请前往"设置" > "面容ID与密码"或"触控ID与密码"进行配置');
          } else {
            // Android
            Alert.alert('提示', '请前往"设置" > "安全"或"生物识别"进行配置');
          }
          onSetupComplete?.();
        }
      }
    ]
  );
};

/**
 * 获取认证错误的友好提示信息
 * @param error 认证错误
 * @returns 友好的错误提示
 */
const getAuthErrorMessage = (error: string): string => {
  switch (error) {
    case 'UserCancel':
      return '用户取消了认证';
    case 'UserFallback':
      return '用户选择了备用认证方式';
    case 'SystemCancel':
      return '系统取消了认证';
    case 'PasscodeNotSet':
      return '设备未设置密码';
    case 'BiometryNotAvailable':
      return '生物识别功能不可用';
    case 'BiometryNotEnrolled':
      return '未注册生物识别信息';
    case 'BiometryLockout':
      return '生物识别被锁定，请稍后重试';
    case 'AuthenticationFailed':
      return '认证失败，请重试';
    default:
      return '认证失败，请重试';
  }
};

/**
 * 应用生命周期监听器管理
 */
let appStateListener: any = null;

/**
 * 初始化应用生命周期监听
 * 监听应用进入后台，记录时间用于判断是否需要重新认证
 */
export const initializeAppStateListener = (): void => {
  // 避免重复注册
  if (appStateListener) {
    return;
  }

  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    log('[BiometricService] 应用状态变化:', nextAppState);

    if (nextAppState === 'background' || nextAppState === 'inactive') {
      // 应用进入后台或非活跃状态，记录时间
      recordBackgroundTime();
    }
  };

  appStateListener = AppState.addEventListener('change', handleAppStateChange);
  log('[BiometricService] 应用状态监听器已初始化');
};

/**
 * 清理应用生命周期监听
 */
export const cleanupAppStateListener = (): void => {
  if (appStateListener) {
    appStateListener.remove();
    appStateListener = null;
    log('[BiometricService] 应用状态监听器已清理');
  }
};

/**
 * 手动清除认证会话
 * 用于用户登出或关闭应用加密时
 */
export const clearAuthenticationSession = async (): Promise<void> => {
  await clearAuthSession();
  await AsyncStorage.removeItem(AUTH_SESSION_CONFIG.BACKGROUND_TIME_KEY);
  log('[BiometricService] 认证会话已手动清除');
};

/**
 * 获取认证会话信息（用于调试）
 * @returns 当前会话状态
 */
export const getAuthSessionInfo = async (): Promise<{
  hasSession: boolean;
  isValid: boolean;
  expiresAt?: string;
  timeRemaining?: number;
}> => {
  try {
    const session = await getCurrentAuthSession();
    if (!session) {
      return { hasSession: false, isValid: false };
    }

    const now = Date.now();
    const timeRemaining = session.expiresAt - now;

    return {
      hasSession: true,
      isValid: session.isAuthenticated && now < session.expiresAt,
      expiresAt: new Date(session.expiresAt).toISOString(),
      timeRemaining: Math.max(0, timeRemaining)
    };
  } catch (error) {
    logError('[BiometricService] 获取会话信息失败:', error);
    return { hasSession: false, isValid: false };
  }
};

/**
 * 获取生物识别类型的显示名称
 * @param type 生物识别类型
 * @returns 显示名称
 */
export const getBiometricTypeName = (type: BiometricType): string => {
  switch (type) {
    case BiometricType.FINGERPRINT:
      return '指纹识别';
    case BiometricType.FACE_ID:
      return Platform.OS === 'ios' ? '面容ID' : '人脸识别';
    case BiometricType.IRIS:
      return '虹膜识别';
    default:
      return '生物识别';
  }
};
