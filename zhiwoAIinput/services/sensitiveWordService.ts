import { supabase } from './supabaseService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { log, warn, error as logError } from './logService';

// 敏感词接口定义
export interface SensitiveWord {
  word: string;
  category: string;
  updated_at: string;
}

// 过滤结果接口
export interface FilterResult {
  isFiltered: boolean;
  detectedWords: string[];
  originalText: string;
}

// 缓存键名
const CACHE_KEYS = {
  SENSITIVE_WORDS: 'sensitive_words_cache',
  LAST_UPDATE: 'sensitive_words_last_update',
  CACHE_VERSION: 'sensitive_words_cache_version',
} as const;

// 缓存版本，用于强制更新缓存
const CURRENT_CACHE_VERSION = '1.0.0';

// 缓存有效期（小时）
const CACHE_EXPIRY_HOURS = 24;

// 自动更新间隔（小时）
const AUTO_UPDATE_INTERVAL_HOURS = 6;

/**
 * 敏感词过滤服务
 * 提供敏感词的获取、缓存、更新和过滤功能
 */
class SensitiveWordService {
  private sensitiveWords: SensitiveWord[] = [];
  private isInitialized = false;
  private updateTimer: NodeJS.Timeout | null = null;
  private isUpdating = false;

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      log('[SensitiveWordService] 初始化敏感词服务...');
      
      // 检查缓存版本
      await this.checkCacheVersion();
      
      // 尝试从缓存加载
      const cachedWords = await this.loadFromCache();
      
      if (cachedWords && cachedWords.length > 0) {
        log('[SensitiveWordService] 从缓存加载敏感词:', cachedWords.length, '个');
        this.sensitiveWords = cachedWords;
      } else {
        log('[SensitiveWordService] 缓存为空，从服务端获取...');
        await this.updateFromServer();
      }

      // 检查是否需要更新
      if (await this.shouldUpdate()) {
        log('[SensitiveWordService] 缓存已过期，更新敏感词...');
        // 异步更新，不阻塞初始化
        this.updateFromServer().catch(error => {
          warn('[SensitiveWordService] 后台更新敏感词失败:', error);
        });
      }

      // 启动定时更新
      this.startAutoUpdate();
      
      this.isInitialized = true;
      log('[SensitiveWordService] 敏感词服务初始化完成，词汇数量:', this.sensitiveWords.length);
    } catch (error) {
      logError('[SensitiveWordService] 初始化失败:', error);
      // 即使初始化失败，也设置为已初始化，避免重复尝试
      this.isInitialized = true;
    }
  }

  /**
   * 检查缓存版本，如果版本不匹配则清空缓存
   */
  private async checkCacheVersion(): Promise<void> {
    try {
      const cachedVersion = await AsyncStorage.getItem(CACHE_KEYS.CACHE_VERSION);
      if (cachedVersion !== CURRENT_CACHE_VERSION) {
        log('[SensitiveWordService] 缓存版本不匹配，清空缓存');
        await this.clearCache();
        await AsyncStorage.setItem(CACHE_KEYS.CACHE_VERSION, CURRENT_CACHE_VERSION);
      }
    } catch (error) {
      warn('[SensitiveWordService] 检查缓存版本失败:', error);
    }
  }

  /**
   * 从服务端更新敏感词
   */
  async updateFromServer(): Promise<boolean> {
    if (this.isUpdating) {
      log('[SensitiveWordService] 正在更新中，跳过重复请求');
      return false;
    }

    this.isUpdating = true;
    
    try {
      log('[SensitiveWordService] 从服务端获取敏感词...');
      
      const { data, error } = await supabase.rpc('get_active_sensitive_words');
      
      if (error) {
        logError('[SensitiveWordService] 获取敏感词失败:', error);
        return false;
      }

      if (!data || !Array.isArray(data)) {
        warn('[SensitiveWordService] 服务端返回数据格式无效');
        return false;
      }

      // 更新内存中的敏感词
      this.sensitiveWords = data.map(item => ({
        word: item.word,
        category: item.category,
        updated_at: item.updated_at,
      }));

      // 保存到缓存
      await this.saveToCache(this.sensitiveWords);
      
      log('[SensitiveWordService] 敏感词更新成功，数量:', this.sensitiveWords.length);
      return true;
    } catch (error) {
      logError('[SensitiveWordService] 更新敏感词异常:', error);
      return false;
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * 保存敏感词到缓存
   */
  private async saveToCache(words: SensitiveWord[]): Promise<void> {
    try {
      const now = new Date().toISOString();
      await Promise.all([
        AsyncStorage.setItem(CACHE_KEYS.SENSITIVE_WORDS, JSON.stringify(words)),
        AsyncStorage.setItem(CACHE_KEYS.LAST_UPDATE, now),
      ]);
      log('[SensitiveWordService] 敏感词已保存到缓存');
    } catch (error) {
      warn('[SensitiveWordService] 保存缓存失败:', error);
    }
  }

  /**
   * 从缓存加载敏感词
   */
  private async loadFromCache(): Promise<SensitiveWord[] | null> {
    try {
      const cachedData = await AsyncStorage.getItem(CACHE_KEYS.SENSITIVE_WORDS);
      if (!cachedData) {
        return null;
      }

      const words = JSON.parse(cachedData) as SensitiveWord[];
      return Array.isArray(words) ? words : null;
    } catch (error) {
      warn('[SensitiveWordService] 加载缓存失败:', error);
      return null;
    }
  }

  /**
   * 检查是否需要更新缓存
   */
  private async shouldUpdate(): Promise<boolean> {
    try {
      const lastUpdateStr = await AsyncStorage.getItem(CACHE_KEYS.LAST_UPDATE);
      if (!lastUpdateStr) {
        return true;
      }

      const lastUpdate = new Date(lastUpdateStr);
      const now = new Date();
      const hoursDiff = (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60);
      
      return hoursDiff >= CACHE_EXPIRY_HOURS;
    } catch (error) {
      warn('[SensitiveWordService] 检查更新时间失败:', error);
      return true;
    }
  }

  /**
   * 启动自动更新定时器
   */
  private startAutoUpdate(): void {
    // 清除现有定时器
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
    }

    // 设置新的定时器
    this.updateTimer = setInterval(async () => {
      log('[SensitiveWordService] 定时更新敏感词...');
      await this.updateFromServer();
    }, AUTO_UPDATE_INTERVAL_HOURS * 60 * 60 * 1000);

    log('[SensitiveWordService] 自动更新定时器已启动，间隔:', AUTO_UPDATE_INTERVAL_HOURS, '小时');
  }

  /**
   * 停止自动更新
   */
  stopAutoUpdate(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
      log('[SensitiveWordService] 自动更新定时器已停止');
    }
  }

  /**
   * 清空缓存
   */
  private async clearCache(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(CACHE_KEYS.SENSITIVE_WORDS),
        AsyncStorage.removeItem(CACHE_KEYS.LAST_UPDATE),
      ]);
      log('[SensitiveWordService] 缓存已清空');
    } catch (error) {
      warn('[SensitiveWordService] 清空缓存失败:', error);
    }
  }

  /**
   * 强制更新敏感词
   */
  async forceUpdate(): Promise<boolean> {
    log('[SensitiveWordService] 强制更新敏感词...');
    return await this.updateFromServer();
  }

  /**
   * 过滤文本中的敏感词
   */
  async filterText(text: string): Promise<FilterResult> {
    // 确保服务已初始化
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!text || typeof text !== 'string') {
      return {
        isFiltered: false,
        detectedWords: [],
        originalText: text || '',
      };
    }

    const detectedWords: string[] = [];
    const lowerText = text.toLowerCase();

    // 检查每个敏感词
    for (const sensitiveWord of this.sensitiveWords) {
      const lowerWord = sensitiveWord.word.toLowerCase();
      if (lowerText.includes(lowerWord)) {
        detectedWords.push(sensitiveWord.word);
        log('[SensitiveWordService] 检测到敏感词:', sensitiveWord.word, '类别:', sensitiveWord.category);
      }
    }

    const isFiltered = detectedWords.length > 0;
    
    if (isFiltered) {
      log('[SensitiveWordService] 文本被过滤，检测到', detectedWords.length, '个敏感词:', detectedWords);
    }

    return {
      isFiltered,
      detectedWords,
      originalText: text,
    };
  }

  /**
   * 获取当前敏感词列表（用于调试）
   */
  getCurrentWords(): SensitiveWord[] {
    return [...this.sensitiveWords];
  }

  /**
   * 获取敏感词统计信息
   */
  getStats(): {
    totalWords: number;
    categories: string[];
    lastUpdate: string | null;
    isInitialized: boolean;
  } {
    const categories = [...new Set(this.sensitiveWords.map(w => w.category))];
    
    return {
      totalWords: this.sensitiveWords.length,
      categories,
      lastUpdate: this.sensitiveWords.length > 0 ? 
        Math.max(...this.sensitiveWords.map(w => new Date(w.updated_at).getTime())).toString() : null,
      isInitialized: this.isInitialized,
    };
  }

  /**
   * 销毁服务实例
   */
  destroy(): void {
    this.stopAutoUpdate();
    this.sensitiveWords = [];
    this.isInitialized = false;
    log('[SensitiveWordService] 服务已销毁');
  }
}

// 导出单例实例
export const sensitiveWordService = new SensitiveWordService();

// 导出便捷方法
export const filterSensitiveWords = (text: string): Promise<FilterResult> => {
  return sensitiveWordService.filterText(text);
};

export const initializeSensitiveWordService = (): Promise<void> => {
  return sensitiveWordService.initialize();
};

export const updateSensitiveWords = (): Promise<boolean> => {
  return sensitiveWordService.forceUpdate();
}; 