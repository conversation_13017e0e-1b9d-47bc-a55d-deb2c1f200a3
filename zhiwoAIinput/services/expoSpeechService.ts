/**
 * 语音识别服务 (使用 expo-speech-recognition)
 * 使用expo-speech-recognition处理语音输入和转文本功能
 * 
 * 这是对原有 speechService.ts 的重构版本，使用 expo-speech-recognition 代替 react-native-voice 库
 * 对外暴露的API与原有服务保持一致，便于在应用中无缝替换
 * 
 * 使用方法：
 * 1. 安装依赖：npm install expo-speech-recognition 或 yarn add expo-speech-recognition
 * 2. 在 app.json 中添加插件配置：
 *    {
 *      "expo": {
 *        "plugins": [
 *          [
 *            "expo-speech-recognition",
 *            {
 *              "microphonePermission": "允许应用访问您的麦克风进行语音识别",
 *              "speechRecognitionPermission": "允许应用进行语音识别"
 *            }
 *          ]
 *        ]
 *      }
 *    }
 * 3. 重新生成原生代码：
 *    - npx expo prebuild --clean
 *    - 或者 npx expo run:ios / npx expo run:android
 * 
 * @see https://github.com/jamsch/expo-speech-recognition
 */
import { Audio } from 'expo-av';
import { InterruptionModeIOS, InterruptionModeAndroid } from 'expo-av/build/Audio.types';
import * as FileSystem from 'expo-file-system';
import { Platform, Alert, NativeModules, PermissionsAndroid } from 'react-native';
// 使用expo-speech-recognition库进行语音识别
import {
  ExpoSpeechRecognitionModule,
  useSpeechRecognitionEvent,
  ExpoSpeechRecognitionErrorEvent,
  ExpoSpeechRecognitionResultEvent,
  AVAudioSessionCategory,
  AVAudioSessionCategoryOptions,
  AVAudioSessionMode,
} from 'expo-speech-recognition';
import * as OpenAISpeechService from './openaiSpeechService';
import * as storageService from './storageService';
import { recordingFileService } from './recordingFileService';
import { userIdService } from './userIdService';
import i18n from '../utils/i18n';
import { log, error as logError, warn, info } from '@/services/logService';

// 录音会话
let recording: Audio.Recording | null = null;
// 当前录音文件路径
let currentRecordingUri: string | null = null;
// 是否正在进行语音识别
let isRecognizing: boolean = false;
// 当前识别文本
let currentRecognizedText: string = '';
// 备份的多段文本 - 存储所有段落的完整文本
let fullRecognizedText: string = '';
// 每次暂停时的文本
let pausedRecognizedText: string = '';
// 添加这个变量来存储暂停点
let recordingPauseMarkers: number[] = [];
// 当前录音状态
let currentRecordingStatus: RecordingStatus = 'idle';
// 录音开始时间
let recordingStartTime: number = 0;
// 录音时长更新定时器
let updateRecordingTimeInterval: NodeJS.Timeout | null = null;
// 当前激活的回调函数
let activeCallback: SpeechRecognitionCallback | null = null;
// 事件监听器
let startListener: ReturnType<typeof ExpoSpeechRecognitionModule.addListener> | null = null;
let resultListener: ReturnType<typeof ExpoSpeechRecognitionModule.addListener> | null = null;
let errorListener: ReturnType<typeof ExpoSpeechRecognitionModule.addListener> | null = null;
let endListener: ReturnType<typeof ExpoSpeechRecognitionModule.addListener> | null = null;
let volumeListener: ReturnType<typeof ExpoSpeechRecognitionModule.addListener> | null = null;

// 添加额外状态来追踪语音识别活跃度
let lastSpeechActivityTimestamp = 0;
let recognitionWatchdog: NodeJS.Timeout | null = null;
// 延长超时检测时间，从8秒改为20秒，给用户足够的思考时间
const RECOGNITION_ACTIVITY_TIMEOUT = 20000; // 20秒无活动则检查


// 添加API调用锁机制，防止重复调用API
let isWhisperApiCallInProgress = false;
let lastWhisperApiCallTimestamp = 0;
let isProcessingTranscription = false; // 添加转写处理锁
const API_CALL_COOLDOWN = 1000; // 1秒内不允许重复调用API

// 添加任务跟踪机制，用于检查任务是否已被取消
interface TranscriptionTask {
  id: string;
  isCancelled: boolean;
  timestamp: number;
}

// 当前转录任务
let currentTranscriptionTask: TranscriptionTask | null = null;

// 创建新的转录任务
export const createTranscriptionTask = (taskId: string): string => {
  currentTranscriptionTask = {
    id: taskId,
    isCancelled: false,
    timestamp: Date.now()
  };
  log(`[任务跟踪] 创建新转录任务: ${taskId}`);
  return taskId;
};

// 取消转录任务
export const cancelTranscriptionTask = (taskId: string): boolean => {
  if (currentTranscriptionTask && currentTranscriptionTask.id === taskId) {
    currentTranscriptionTask.isCancelled = true;
    log(`[任务跟踪] 取消转录任务: ${taskId}`);
    return true;
  }
  return false;
};

// 取消当前转录任务
export const cancelCurrentTranscriptionTask = (): boolean => {
  if (currentTranscriptionTask) {
    currentTranscriptionTask.isCancelled = true;
    log(`[任务跟踪] 取消当前转录任务: ${currentTranscriptionTask.id}`);
    return true;
  }
  return false;
};

// 检查转录任务是否已被取消
export const isTranscriptionTaskCancelled = (): boolean => {
  return !currentTranscriptionTask || currentTranscriptionTask.isCancelled;
};
// 添加重试次数计数
let restartAttemptCount = 0;
const MAX_RESTART_ATTEMPTS = 3; // 在一次录音会话中最多重试3次
// 添加用户通知状态
let hasNotifiedTimeout = false;
// 添加上次重启时间记录
let lastRestartTimestamp = 0;
const MIN_RESTART_INTERVAL = 3000; // 最小重启间隔为3秒
// 添加无语音计数器
let noSpeechCount = 0;
const MAX_NO_SPEECH_BEFORE_RESTART = 2; // 连续2次无语音才考虑重启

// 录音状态类型
export type RecordingStatus = 'idle' | 'recording' | 'paused' | 'stopped' | 'error';

// 语音识别状态监听回调类型
export type SpeechRecognitionCallback = {
  onStart?: () => void;
  onRecognizing?: (text: string) => void;
  onResult?: (text: string) => void;
  onError?: (error: Error) => void;
  onAudioLevel?: (level: number) => void; // 0-1范围的音量级别
};

/**
 * 生成唯一ID，不依赖crypto
 */
const generateId = (): string => {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
};

/**
 * 检查麦克风权限
 * @returns 是否已获得权限
 */
export const checkPermission = async (): Promise<boolean> => {
  try {
    log('检查麦克风权限');

    // 针对Web平台添加特殊处理
    if (Platform.OS === 'web') {
      log('Web环境，请求麦克风权限');
      try {
        // 检查navigator.mediaDevices是否可用
        if (!navigator.mediaDevices) {
          logError('navigator.mediaDevices不可用，可能是非安全上下文或浏览器不支持');
          Alert.alert(
            '麦克风权限请求失败',
            '您的浏览器不支持麦克风访问，请使用Chrome、Firefox或Safari等现代浏览器，并确保使用HTTPS连接'
          );
          return false;
        }
        
        // 在Web环境中，使用标准的浏览器API请求权限
        const mediaPermission = await navigator.mediaDevices.getUserMedia({ 
          audio: true,
          video: false  // 明确指定不需要视频，避免额外的权限请求
        });
        
        // 如果获取成功，则认为已有权限
        if (mediaPermission) {
          log('Web麦克风权限已获取');
          
          // 停止所有媒体轨道，防止麦克风一直被占用
          mediaPermission.getTracks().forEach(track => track.stop());
          
          return true;
        }
      } catch (error) {
        logError('Web麦克风权限请求失败:', error);
        
        // 更详细的错误信息
        if (error instanceof DOMException) {
          if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
            logError('用户拒绝了麦克风访问权限');
            Alert.alert(
              '麦克风权限被拒绝',
              '请在浏览器设置中允许访问麦克风，以便使用语音识别功能'
            );
          } else if (error.name === 'NotFoundError') {
            logError('未找到麦克风设备');
            Alert.alert(
              '麦克风不可用',
              '未检测到麦克风设备，请确保您的设备有麦克风并正常工作'
            );
          } else {
            logError(`Web麦克风权限错误: ${error.name}`);
            Alert.alert(
              '麦克风权限错误',
              `无法访问麦克风: ${error.message}`
            );
          }
        } else {
          Alert.alert(
            '麦克风权限请求失败',
            '请在浏览器设置中允许访问麦克风，以便使用语音识别功能'
          );
        }
        return false;
      }
    }

    // 为iOS和Android平台请求权限
    const permission = await Audio.requestPermissionsAsync();
    const permissionResponse = permission.status === 'granted';
    
    if (!permissionResponse) {
      warn('麦克风权限被拒绝');
      
      // 显示权限请求失败的提示
      Alert.alert(
        '需要麦克风权限',
        '请在设备设置中允许应用访问麦克风，以便使用语音识别功能'
      );
    }
    
    return permissionResponse;
  } catch (error) {
    logError('检查权限出错:', error);
    return false;
  }
};

/**
 * 请求语音识别权限
 * @returns 是否获得权限
 */
export const requestSpeechRecognitionPermission = async (): Promise<boolean> => {
  try {
    log('请求语音识别权限...');
    
    // 检查语音识别服务是否可用
    const recognitionAvailable = await ExpoSpeechRecognitionModule.isRecognitionAvailable();
    if (!recognitionAvailable) {
      warn('语音识别服务不可用');
      return false;
    }
    log('语音识别服务可用');
    
    // 请求麦克风权限
    if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        {
          title: '需要麦克风权限',
          message: '为了进行语音识别，应用需要访问您的麦克风',
          buttonPositive: '授权',
          buttonNegative: '取消',
        }
      );
      
      if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
        log('Android麦克风权限被拒绝');
        return false;
      }
      log('Android麦克风权限已授权');
    }
    
    // 请求语音识别权限
    const permissions = await ExpoSpeechRecognitionModule.requestPermissionsAsync();
    if (!permissions.granted) {
      log('语音识别权限被拒绝');
      return false;
    }
    
    log('语音识别权限已授权');
    return true;
  } catch (error) {
    logError('请求语音识别权限完全失败:', error);
    return false;
  }
};

/**
 * 请求音频录制权限
 * @returns 是否获得权限
 */
export const requestAudioPermission = async (): Promise<boolean> => {
  try {
    log('请求音频录制权限...');
    
    // 在iOS上，先初始化音频会话，确保使用兼容的设置
    if (Platform.OS === 'ios') {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true, // 必须为true，才能与DuckOthers兼容
        staysActiveInBackground: false,
        interruptionModeIOS: InterruptionModeIOS.DuckOthers,
        interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
      });
      
      // 设置iOS音频会话类别
      // 使用 ExpoSpeechRecognitionModule 的方法设置iOS音频会话类别
      if (Platform.OS === 'ios') {
        // 注: 在实际使用中，会自动在语音识别开始时设置合适的音频会话类别
        log('iOS平台音频会话已设置');
      }
    }
    
    // 请求权限
    const permission = await Audio.requestPermissionsAsync();
    const granted = permission.status === 'granted';
    
    log('音频录制权限状态:', permission.status);
    
    // 如果权限被拒绝，输出详细信息以便调试
    if (!granted) {
      warn('音频录制权限被拒绝，状态:', permission);
    }
    
    return granted;
  } catch (error) {
    logError('请求音频权限失败:', error);
    return false;
  }
};

/**
 * 初始化音频录制
 */
export const initAudioRecording = async (): Promise<void> => {
  try {
    log('开始初始化音频录制...');
    
    // 先检查并请求音频权限
    const hasPermission = await requestAudioPermission();
    if (!hasPermission) {
      throw new Error('未获得录音权限，无法初始化录音功能');
    }
    
    log('音频权限已获取，设置音频模式...');
    
    // 在iOS上，需要先设置音频模式才能录音
    if (Platform.OS === 'ios') {
      log('iOS平台，设置专用音频会话模式');
    await Audio.setAudioModeAsync({
      allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      staysActiveInBackground: false,
      interruptionModeIOS: InterruptionModeIOS.DuckOthers,
      interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
        shouldDuckAndroid: true,
      });
    } else {
      // Android平台保持原有设置
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        staysActiveInBackground: false,
        interruptionModeIOS: InterruptionModeIOS.DuckOthers,
        interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
        shouldDuckAndroid: true,
    });
    }
    
    log('音频录制模式已初始化');
  } catch (error) {
    logError('初始化音频录制失败:', error);
    throw error;
  }
};

/**
 * 获取录音文件保存目录
 */
const getRecordingsDirectory = async (): Promise<string> => {
  const dir = `${FileSystem.documentDirectory}recordings/`;
  const dirInfo = await FileSystem.getInfoAsync(dir);
  if (!dirInfo.exists) {
    await FileSystem.makeDirectoryAsync(dir, { intermediates: true });
  }
  return dir;
};

/**
 * 生成唯一的录音文件名
 */
const generateRecordingFilename = (): string => {
  return `recording_${generateId()}.m4a`;
};

/**
 * 设置语音识别的事件监听器
 * @param callback 语音识别回调函数
 */
const setupSpeechRecognitionListeners = (callback: SpeechRecognitionCallback) => {
  // 保存回调函数的引用以便在其他地方使用
  if (callback) {
    activeCallback = callback;
  } else {
    warn('设置监听器时未提供回调函数，使用默认空回调');
    // 创建一个空回调防止空引用调用
    activeCallback = {
      onStart: () => {},
      onRecognizing: () => {},
      onResult: () => {},
      onError: () => {},
      onAudioLevel: () => {}
    };
  }
  
  log('正在初始化语音识别监听器...');
  
  // 清除之前的所有事件监听器
  removeAllListeners();
  
  // 设置语音识别开始事件
  startListener = ExpoSpeechRecognitionModule.addListener('start', () => {
    try {
      log('语音识别已开始');
      isRecognizing = true;
      lastSpeechActivityTimestamp = Date.now(); // 初始化活动时间戳
      noSpeechCount = 0; // 重置无语音计数
      activeCallback?.onStart?.();
      
      // 启动监视器检查识别活跃度
      if (recognitionWatchdog) clearInterval(recognitionWatchdog);
      recognitionWatchdog = setInterval(checkRecognitionActivity, 5000); // 每5秒检查一次
    } catch (e) {
      warn('onStart回调执行错误:', e);
    }
  });
  
  // 设置语音识别结果事件
  resultListener = ExpoSpeechRecognitionModule.addListener('result', (event: ExpoSpeechRecognitionResultEvent) => {
    try {
      // 更新最后活动时间戳
      lastSpeechActivityTimestamp = Date.now();
      
      if (event.results && event.results.length > 0) {
        const result = event.results[0];
        
        // 更新当前段落的识别文本
        const text = result.transcript || '';
        currentRecognizedText = text;
        
        // 创建完整的文本内容（已存文本 + 当前段落）
        const completeText = constructCompleteText();
        
        if (event.isFinal) {
          // 最终结果
          log('识别完成, 文本长度:', completeText.length);
          
          // 在最终结果时，更新完整文本以便后续使用
          if (completeText && completeText.trim().length > 0) {
            fullRecognizedText = completeText.trim();
            log('更新完整识别文本:', fullRecognizedText);
          }
          
          // 调用回调函数返回最终文本
          activeCallback?.onResult?.(completeText);
        } else {
          // 部分结果
          log('正在识别, 文本长度:', text.length);
          
          // 调用回调函数，传递完整的文本内容
          activeCallback?.onRecognizing?.(completeText);
        }
        
        // 重置无语音计数，因为有识别结果
        noSpeechCount = 0;
      }
    } catch (e) {
      warn('onResult回调执行错误:', e);
    }
  });
  
  // 设置语音识别结束事件
  endListener = ExpoSpeechRecognitionModule.addListener('end', () => {
    try {
      log('语音识别已结束');
      
      // 如果是在活跃录音状态，但识别服务意外结束，尝试重新启动
      if (isRecognizing && recording && recordingPauseMarkers.length % 2 === 0) {
        // 检查是否可以重启
        const now = Date.now();
        const timeSinceLastRestart = now - lastRestartTimestamp;
        
        if (timeSinceLastRestart >= MIN_RESTART_INTERVAL) {
          log('语音识别意外结束，尝试自动重新启动...');
          
          // 更新最后重启时间
          lastRestartTimestamp = now;
          
          // 延迟一小段时间后重新启动
          setTimeout(async () => {
            try {
              if (isRecognizing && recording) {
                log('重新启动语音识别...');
                await ExpoSpeechRecognitionModule.start({
                  lang: 'zh-CN',
                  interimResults: true,
                  addsPunctuation: true
                });
              }
            } catch (restartError) {
              logError('自动重新启动语音识别失败:', restartError);
            }
          }, 800);
        } else {
          log(`距离上次重启时间过短(${timeSinceLastRestart}ms)，跳过本次重启`);
          // 标记为仍在识别状态，等待下次检查
          isRecognizing = true;
        }
      } else {
        isRecognizing = false;
      }
    } catch (e) {
      warn('onEnd回调执行错误:', e);
    }
  });
  
  // 设置语音识别错误事件
  errorListener = ExpoSpeechRecognitionModule.addListener('error', (event: ExpoSpeechRecognitionErrorEvent) => {
    try {
      // 更新最后活动时间戳，即使是错误也算活动
      lastSpeechActivityTimestamp = Date.now();
      
      // 根据错误类型处理
      if (event.error === 'not-allowed') {
        warn('语音识别错误:', event);
        activeCallback?.onError?.(new Error('语音识别权限被拒绝，请在设置中授予麦克风访问权限'));
      } else if (event.error === 'no-speech') {
        // no-speech不是真正的错误，而是正常的无语音状态
        noSpeechCount++;
        log(`未检测到语音(${noSpeechCount}/${MAX_NO_SPEECH_BEFORE_RESTART})，继续监听...`);
        
        // 只有连续多次无语音时才考虑重启
        if (noSpeechCount >= MAX_NO_SPEECH_BEFORE_RESTART) {
          // 检查是否可以重启
          const now = Date.now();
          const timeSinceLastRestart = now - lastRestartTimestamp;
          
          if (timeSinceLastRestart >= MIN_RESTART_INTERVAL) {
            log('多次未检测到语音，尝试重新激活...');
            
            // 更新最后重启时间
            lastRestartTimestamp = now;
            
            // 延迟处理，避免频繁重启
            setTimeout(() => {
              if (isRecognizing && recordingPauseMarkers.length % 2 === 0) {
                restartSpeechRecognition().catch(e => 
                  warn('重新激活语音识别失败:', e));
              }
            }, 1000);
            
            // 重置无语音计数
            noSpeechCount = 0;
          } else {
            log(`距离上次重启时间过短(${timeSinceLastRestart}ms)，跳过本次重启`);
          }
        }
      } else if (event.error === 'network') {
        warn('语音识别错误:', event);
        activeCallback?.onError?.(new Error('网络连接错误，无法进行语音识别'));
        
        // 网络错误时尝试重新启动
        const now = Date.now();
        const timeSinceLastRestart = now - lastRestartTimestamp;
        
        if (timeSinceLastRestart >= MIN_RESTART_INTERVAL) {
          // 更新最后重启时间
          lastRestartTimestamp = now;
          
          setTimeout(() => {
            if (isRecognizing && recordingPauseMarkers.length % 2 === 0) {
              restartSpeechRecognition().catch(e => 
                warn('网络错误后重新激活语音识别失败:', e));
            }
          }, 2000);
        }
      } else if (event.error === 'aborted') {
        // 被中止通常是我们调用了stop方法
        log('语音识别被中止');
      } else {
        warn('语音识别错误:', event);
        activeCallback?.onError?.(new Error(`语音识别错误: ${event.error || '未知错误'}`));
        
        // 对于其他错误，也尝试重新启动
        const now = Date.now();
        const timeSinceLastRestart = now - lastRestartTimestamp;
        
        if (timeSinceLastRestart >= MIN_RESTART_INTERVAL) {
          // 更新最后重启时间
          lastRestartTimestamp = now;
          
          setTimeout(() => {
            if (isRecognizing && recordingPauseMarkers.length % 2 === 0) {
              restartSpeechRecognition().catch(e => 
                warn('错误后重新激活语音识别失败:', e));
            }
          }, 2000);
        }
      }
    } catch (e) {
      warn('onError回调执行错误:', e);
    }
  });
  
  // 设置语音识别音量事件 (如果支持)
  volumeListener = ExpoSpeechRecognitionModule.addListener('volumechange', (event: any) => {
    try {
      // 更新最后活动时间戳
      lastSpeechActivityTimestamp = Date.now();
      
      if (typeof event.value === 'number') {
        // 标准化音量值到0-1范围
        const volume = Math.min(Math.max((event.value + 2) / 12, 0), 1);
        activeCallback?.onAudioLevel?.(volume);
      } else {
        // 如果没有有效的音量值，提供默认值
        activeCallback?.onAudioLevel?.(0.5);
      }
    } catch (e) {
      warn('onVolumeChange回调执行错误:', e);
    }
  });
  
  log('语音识别监听器初始化完成');
};

/**
 * 移除所有事件监听器
 */
const removeAllListeners = () => {
  startListener?.remove();
  resultListener?.remove();
  errorListener?.remove();
  endListener?.remove();
  volumeListener?.remove();
  
  startListener = null;
  resultListener = null;
  errorListener = null;
  endListener = null;
  volumeListener = null;
  
  // 清理监视器定时器
  if (recognitionWatchdog) {
    clearInterval(recognitionWatchdog);
    recognitionWatchdog = null;
  }
};

/**
 * 构建完整的文本内容，合并已保存的文本和当前正在识别的文本
 * @returns 完整的文本内容
 */
const constructCompleteText = (): string => {
  let completeText = '';
  
  // 如果有保存的完整文本，以它为基础
  if (fullRecognizedText && fullRecognizedText.trim()) {
    completeText = fullRecognizedText;
    
    // 如果有新的识别内容，附加到完整文本后
    if (currentRecognizedText && currentRecognizedText.trim() && 
        !fullRecognizedText.includes(currentRecognizedText.trim()) &&
        !fullRecognizedText.endsWith(currentRecognizedText.trim())) {
      // 确保中间有合适的分隔
      if (!fullRecognizedText.endsWith(' ') && 
          !fullRecognizedText.endsWith('。') && 
          !fullRecognizedText.endsWith('！') && 
          !fullRecognizedText.endsWith('？') && 
          !fullRecognizedText.endsWith('.') && 
          !fullRecognizedText.endsWith('!') && 
          !fullRecognizedText.endsWith('?')) {
        completeText += ' ';
      }
      completeText += currentRecognizedText.trim();
    }
  } else {
    // 没有保存的完整文本，直接使用当前识别内容
    completeText = currentRecognizedText || '';
  }
  
  // 更新最后活动时间戳
  lastSpeechActivityTimestamp = Date.now();
  
  return completeText.trim();
};

/**
 * 检查语音识别是否还在活跃工作
 */
const checkRecognitionActivity = async () => {
  if (!isRecognizing || recordingPauseMarkers.length % 2 !== 0) {
    // 如果不在识别状态或处于暂停状态，不做处理
    return;
  }
  
  const now = Date.now();
  const timeSinceLastActivity = now - lastSpeechActivityTimestamp;
  
  // 只有超过超时时间且未达到最大重试次数时才尝试重启
  if (timeSinceLastActivity > RECOGNITION_ACTIVITY_TIMEOUT && restartAttemptCount < MAX_RESTART_ATTEMPTS) {
    log(`语音识别已${timeSinceLastActivity/1000}秒无活动，尝试重新激活...，这是第${restartAttemptCount + 1}次重试`);
    
    try {
      // 增加重试计数
      restartAttemptCount++;
      
      // 尝试重新激活语音识别
      const success = await restartSpeechRecognition();
      
      // 重置活动时间戳，避免频繁重试
      lastSpeechActivityTimestamp = Date.now();
      
      // 如果最后一次重试也失败了，通知用户
      if (!success && restartAttemptCount >= MAX_RESTART_ATTEMPTS && !hasNotifiedTimeout) {
        notifyRecognitionTimeout();
      }
    } catch (error) {
      logError('重新激活语音识别失败:', error);
      
      // 即使重试失败也重置时间戳，避免无限重试
      lastSpeechActivityTimestamp = Date.now();
      
      // 如果是最后一次重试失败，通知用户
      if (restartAttemptCount >= MAX_RESTART_ATTEMPTS && !hasNotifiedTimeout) {
        notifyRecognitionTimeout();
      }
    }
  }
  // 特殊情况：已经达到最大重试次数且长时间无活动，但还未通知用户
  else if (timeSinceLastActivity > RECOGNITION_ACTIVITY_TIMEOUT * 1.5 && 
           restartAttemptCount >= MAX_RESTART_ATTEMPTS && 
           !hasNotifiedTimeout) {
    log('语音识别已多次重试失败且长时间无活动，通知用户');
    notifyRecognitionTimeout();
  }
};

/**
 * 通知语音识别超时
 */
const notifyRecognitionTimeout = () => {
  log('通知用户语音识别超时');
  hasNotifiedTimeout = true;
  
  // 通过回调通知应用
  if (activeCallback) {
    // 不使用error回调，避免显示错误提示
    // 而是使用特殊的结果回调，包含特定标记
    activeCallback.onResult?.('__recognition_timeout__');
  }
};

/**
 * 重新启动语音识别
 */
const restartSpeechRecognition = async () => {
  if (!isRecognizing) return;
  
  try {
    log('准备重新启动语音识别...');
    
    // 先停止当前识别
    await ExpoSpeechRecognitionModule.stop();
    
    // 确保停止后有短暂延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 短暂延迟后重新启动
    if (isRecognizing) {
      log('正在重新启动语音识别...');
      await ExpoSpeechRecognitionModule.start({
        lang: 'zh-CN',
        interimResults: true,
        addsPunctuation: true
      });
      log('语音识别已重新启动');
      
      // 通知回调识别已恢复
      if (activeCallback) {
        // 这里不是错误，而是通知
        const currentText = constructCompleteText();
        if (currentText) {
          activeCallback.onRecognizing?.(currentText);
        } else {
          // 如果没有文本，通知正在聆听
          activeCallback.onRecognizing?.('');
        }
      }
      
      // 重置活动时间戳
      lastSpeechActivityTimestamp = Date.now();
      
      // 返回成功
      return true;
    }
  } catch (error) {
    logError('重新启动语音识别失败:', error);
    
    // 发生错误时返回失败
    return false;
  }
};

/**
 * 检测设备上可用的语音识别服务
 * @returns 是否有可用的语音识别服务
 */
export const checkAvailableSpeechRecognizers = async (): Promise<boolean> => {
  try {
    // 使用expo-speech-recognition提供的方法检查是否支持语音识别
    const recognitionAvailable = await ExpoSpeechRecognitionModule.isRecognitionAvailable();
    log('语音识别服务可用状态:', recognitionAvailable);
    return recognitionAvailable;
  } catch (error) {
    warn('检查语音识别服务可用性失败:', error);
    // 如果检查过程失败，我们假设可能是可用的
    return true;
  }
};

/**
 * 检查是否处于中国大陆网络环境
 * 这个方法可以通过各种方式实现，这里使用一个简单的检测方法
 * @returns 是否可能在中国大陆
 */
export const isLikelyInChina = async (): Promise<boolean> => {
  try {
    // 这里可以改进为检查设备语言、时区或IP地理位置
    // 简单实现：检查设备语言
    let deviceLanguage = 'en'; // 默认为英语
    
    if (Platform.OS === 'ios') {
      // iOS上安全地访问设置
      try {
        const settingsManager = NativeModules.SettingsManager;
        if (!settingsManager) {
          log('SettingsManager不可用，使用默认语言');
          return false;
        }
        
        if (settingsManager.settings && settingsManager.settings.AppleLocale) {
          deviceLanguage = settingsManager.settings.AppleLocale;
        } else if (settingsManager.settings && 
                  settingsManager.settings.AppleLanguages && 
                  Array.isArray(settingsManager.settings.AppleLanguages) && 
                  settingsManager.settings.AppleLanguages.length > 0) {
          deviceLanguage = settingsManager.settings.AppleLanguages[0];
        }
      } catch (iosError) {
        warn('访问iOS语言设置失败:', iosError);
        // 发生错误时使用默认设置，不影响录音功能
        return false;
      }
    } else if (Platform.OS === 'android') {
      // Android上安全地访问设置
      try {
        if (NativeModules.I18nManager && NativeModules.I18nManager.localeIdentifier) {
          deviceLanguage = NativeModules.I18nManager.localeIdentifier;
        }
      } catch (androidError) {
        warn('访问Android语言设置失败:', androidError);
        return false;
      }
    }
    
    // 将deviceLanguage转换为字符串确保可以调用字符串方法
    const langStr = String(deviceLanguage || 'en');
    
    // 如果设备语言是简体中文，可能在中国大陆
    const isChineseLanguage = langStr.startsWith('zh') && 
      (langStr.includes('CN') || langStr.includes('Hans'));
    
    log('设备语言:', langStr, '可能在中国大陆:', isChineseLanguage);
    return isChineseLanguage;
  } catch (error) {
    warn('检查区域失败:', error);
    // 如果无法确定，假设不在中国大陆，优先使用默认服务
    return false;
  }
};



/**
 * 开始录音并进行实时语音识别
 * @param callback 语音识别回调函数
 * @returns 是否成功启动录音
 */
export const startRecording = async (callback: SpeechRecognitionCallback): Promise<boolean> => {
  try {
    log('开始录音');
    
    // 重置暂停状态标记
    isPausedRecording = false;
    
    // 确保先释放之前的资源
    await releaseAudioResources();
    log('开始新录音前已释放之前的所有资源');
    

    // 重置重试计数
    restartAttemptCount = 0;
    // 重置超时通知标记
    hasNotifiedTimeout = false;
    // 初始化重启时间
    lastRestartTimestamp = Date.now();
    // 重置无语音计数
    noSpeechCount = 0;
    
    // 获取用户选择的转写模型
    const { isVip } = await storageService.getVipStatus();
    const settings = await storageService.getUserSettings();
    
    // 获取用户设置中的转写模型
    let selectedModel = settings.transcribeModel || 'native';
    
    // 额外的安全检查：如果用户不是VIP且选择了需要VIP的模型，回退到whisper-1
    // 注意：whisper-1 模型开放给所有用户使用，只有 gpt-4o-mini-transcribe 需要VIP
    if (!isVip && selectedModel === 'gpt-4o-mini-transcribe') {
      log(`[模型检查] 非VIP用户尝试使用${selectedModel}模型，回退到whisper-1模型`);
      selectedModel = 'whisper-1'; // 回退到whisper-1而不是native
      
      // 同时更新存储中的设置，确保一致性
      try {
        await storageService.saveUserSettings({
          transcribeModel: 'whisper-1'
        });
        log('[模型检查] 已将非VIP用户的转写模型调整为whisper-1');
      } catch (saveError) {
        logError('[模型检查] 保存模型设置失败:', saveError);
      }
    }
    
    // 打印调试信息
    log(`[语音模型] 当前使用的转写模型: ${selectedModel}, 用户VIP状态: ${isVip}`);
    log(`[语音模型] 用户设置详情:`, JSON.stringify(settings));
    
    // 设置活动回调
    activeCallback = callback;
    
    // 重置识别文本
    currentRecognizedText = '';
    fullRecognizedText = '';
    pausedRecognizedText = '';
    recordingPauseMarkers = [];
    
    // 初始化活动时间戳
    lastSpeechActivityTimestamp = Date.now();
    
    // 检查如果使用的是whisper或gpt-4o-mini-transcribe模型，则不启动本地语音识别
    const useWebApiModel = selectedModel === 'whisper-1' || selectedModel === 'gpt-4o-mini-transcribe';
    
    if (useWebApiModel) {
      log(`[${selectedModel}模式] 使用${selectedModel}模型，跳过本地语音识别初始化`);
      
      // 确保清理任何可能存在的旧录音对象
      if (recording) {
        try {
          log(`[${selectedModel}模式] 清理旧的录音实例`);
          await recording.stopAndUnloadAsync();
        } catch (cleanupError) {
          log(`[${selectedModel}模式] 清理旧的录音实例时出错，忽略并继续:`, cleanupError);
        } finally {
          recording = null; // 确保清除旧实例引用
        }
      }
      
      // 立即调用开始回调，先让UI响应
      callback?.onStart?.();
      currentRecordingStatus = 'recording';
      recordingStartTime = Date.now();
      log(`[${selectedModel}模式] UI已更新为录音状态，开始准备录音器`);
      
      // 并行启动录音和其他初始化
      const startRecordingPromise = (async () => {
        try {
          await initAudioRecording();
          isRecognizing = false;
          await startAudioRecording();
          log(`[${selectedModel}模式] 录音器启动完成`);
        } catch (error) {
          logError(`[${selectedModel}模式] 录音器启动失败:`, error);
          throw error;
        }
      })();
      
      // 立即开始录音时间计时器
      updateRecordingTimeInterval = setInterval(() => {
        // 更新录音时长
          const elapsedTime = Math.floor((Date.now() - recordingStartTime) / 1000);
          if (callback?.onRecognizing) {
            // 添加特殊前缀标记，表示这是状态更新而不是实际转写内容
            callback.onRecognizing(`__status_update__:正在聆听中... 已录制${elapsedTime}秒`);
          }
          log(`录音进行中，当前时长：${elapsedTime}秒`);
      }, 1000);
      
      // 等待录音启动完成（但UI已经响应）
      try {
        await startRecordingPromise;
        log(`[${selectedModel}模式] 录音准备完成，使用一次性转写`);
      } catch (error) {
        logError(`[${selectedModel}模式] 录音启动失败:`, error);
        if (updateRecordingTimeInterval) {
          clearInterval(updateRecordingTimeInterval);
          updateRecordingTimeInterval = null;
        }
        callback?.onError?.(new Error(`录音启动失败: ${error}`));
        return false;
      }
      
      return true;
    }
    
    log('[Native模式] 使用本地语音识别');
    // 设置语音识别回调
    setupSpeechRecognitionListeners(callback);
    
    // Web平台特殊处理
    if (Platform.OS === 'web') {
      log('Web平台，使用Web Speech API');
      
      // 确保在安全上下文中运行
      if (typeof window === 'undefined' || !window.isSecureContext) {
        logError('Web Speech API需要安全上下文(HTTPS)');
        callback?.onError?.(new Error('语音识别需要在HTTPS环境中使用，当前环境不安全'));
        return false;
      }
      
      // 检查浏览器是否支持语音识别API
      // @ts-ignore - 此变量在浏览器中可用
      const webSpeechAPI = window.SpeechRecognition || window.webkitSpeechRecognition;
      
      if (!webSpeechAPI) {
        logError('该浏览器不支持语音识别API');
        callback?.onError?.(new Error('您的浏览器不支持语音识别功能，请使用Chrome、Edge或Safari浏览器'));
        return false;
      }
      
      try {
        // 先确保已获得麦克风权限，再开始语音识别
        // 这是一个额外的检查，防止用户已拒绝权限但我们没有捕获到
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true, video: false });
          // 立即释放麦克风，避免占用资源
          stream.getTracks().forEach(track => track.stop());
        } catch (permissionError) {
          logError('获取麦克风权限失败:', permissionError);
          callback?.onError?.(new Error('麦克风权限被拒绝，请在浏览器设置中允许访问麦克风'));
          return false;
        }
        
        // 创建语音识别实例
        const recognition = new webSpeechAPI();
        
        // 配置
        recognition.lang = 'zh-CN';
        recognition.continuous = true;
        recognition.interimResults = true;
        
        // 设置事件监听器
        recognition.onstart = () => {
          log('Web语音识别已开始');
          isRecognizing = true;
          activeCallback?.onStart?.();
        };
        
        recognition.onresult = (event: any) => {
          let interimTranscript = '';
          let finalTranscript = '';
          
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
              finalTranscript += transcript;
            } else {
              interimTranscript += transcript;
            }
          }
          
          // 更新当前识别文本
          const text = finalTranscript || interimTranscript;
          currentRecognizedText = text;
          
          // 构建完整文本
          const completeText = constructCompleteText();
          
          log('Web语音识别，文本长度:', completeText.length);
          activeCallback?.onRecognizing?.(completeText);
          
          // 模拟音量变化 - 随机值
          const randomLevel = Math.random() * 0.5 + 0.3; // 0.3-0.8的随机值
          activeCallback?.onAudioLevel?.(randomLevel);
        };
        
        recognition.onerror = (event: any) => {
          warn('Web语音识别错误:', event.error);
          
          // 根据错误类型提供更有用的错误信息
          let errorMessage = `语音识别错误: ${event.error}`;
          
          switch (event.error) {
            case 'not-allowed':
              errorMessage = '麦克风访问被拒绝，请在浏览器设置中允许访问麦克风';
              break;
            case 'no-speech':
              errorMessage = '未检测到语音，请确保麦克风正常工作并尝试说话';
              log('未检测到语音，继续监听...');
              // 这种情况可能不需要向用户报错
              return;
            case 'audio-capture':
              errorMessage = '无法捕获音频，请检查麦克风是否正常连接';
              break;
            case 'network':
              errorMessage = '网络连接错误，无法进行语音识别';
              break;
            case 'aborted':
              log('语音识别被中止');
              // 这通常是由于我们自己调用了stop()，不需要报错
              return;
          }
          
          activeCallback?.onError?.(new Error(errorMessage));
        };
        
        recognition.onend = () => {
          log('Web语音识别已结束');
          isRecognizing = false;
        };
        
        // 启动识别
        recognition.start();
        
        // 保存到全局变量以便在停止时访问
        // @ts-ignore
        window.webSpeechRecognition = recognition;
        
        // 由于Web环境不能真正录制音频文件，我们创建一个虚拟路径
        currentRecordingUri = 'web_recording_' + Date.now() + '.webm';
        
        return true;
      } catch (error) {
        logError('启动Web语音识别失败:', error);
        activeCallback?.onError?.(new Error('启动语音识别失败，请检查浏览器权限设置'));
        return false;
      }
    }
    
    // 移动平台处理 - 调整启动顺序优化
    log('[Native模式] 移动平台，优化录音和识别启动顺序');
    
    try {
      // 首先初始化音频录制系统
      log('[Native模式] 初始化音频录制系统');
      await initAudioRecording();
      
      // 检查语音识别服务可用性
      const isRecognizerAvailable = await checkAvailableSpeechRecognizers();
      if (!isRecognizerAvailable) {
        warn('设备不支持语音识别服务或服务不可用');
        if (Platform.OS === 'android') {
          Alert.alert(
            '语音识别不可用',
            '您的设备似乎没有语音识别服务，这可能导致语音转文字功能无法正常工作。'
          );
        }
      }
      
      // 确认监听器已经设置完成
      log('[Native模式] 确认语音识别监听器已准备就绪');
      if (!activeCallback) {
        warn('[Native模式] 活动回调未设置，重新设置');
        activeCallback = callback;
      }
      
      // 第一步：先启动语音识别，让它准备好监听
      log('[Native模式] 启动语音识别服务');
      isRecognizing = true;
      
      await ExpoSpeechRecognitionModule.start({
        lang: 'zh-CN',
        interimResults: true, // 获取实时的部分结果
        addsPunctuation: true // 启用标点符号功能（iOS 和 Android 13+ 支持）
      });
      
      log('[Native模式] 语音识别启动成功，等待服务完全就绪');
      
      // 等待足够时间确保语音识别服务完全启动并准备好接收音频
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 第二步：启动录音，此时语音识别已经准备好了
      log('[Native模式] 语音识别已就绪，现在启动录音');
      
      // 等待状态验证后再通知UI，避免过早显示"正在监听"状态
      await startAudioRecording();
      await waitForRecordingToStart();
      
      log('[Native模式] 录音已启动并验证完成');
      
      // 录音真正开始后才调用开始回调
      callback?.onStart?.();
      
      // 设置当前录音状态
      currentRecordingStatus = 'recording';
      recordingStartTime = Date.now();
      
      log('[Native模式] 启动序列完成，系统已准备好接收语音输入');
      
      return true;
    } catch (error) {
      logError('[Native模式] 录音或语音识别启动失败:', error);
      
      // 尝试清理资源
      await releaseAudioResources(true);
      
      // 设置错误消息
      activeCallback?.onError?.(error instanceof Error ? error : new Error('录音启动失败'));
      return false;
    }
  } catch (error) {
    logError('开始录音过程出错:', error);
    activeCallback?.onError?.(error instanceof Error ? error : new Error('录音过程出错'));
    return false;
  }
};

// 添加一个状态变量来跟踪是否处于暂停状态
let isPausedRecording = false;

/**
 * 暂停录音
 * @returns 录音状态
 */
export const pauseRecording = async (): Promise<RecordingStatus> => {
  try {
    log('暂停录音');
    
    // 设置暂停状态标记
    isPausedRecording = true;
    
    // 获取用户选择的转写模型
    const settings = await storageService.getUserSettings();
    const selectedModel = settings.transcribeModel || 'native';
    const useWebApiModel = selectedModel === 'whisper-1' || selectedModel === 'gpt-4o-mini-transcribe';
    
    // 暂停语音识别
    if (isRecognizing && !useWebApiModel) {
      try {
        await ExpoSpeechRecognitionModule.stop();
        isRecognizing = false;
      } catch (error) {
        logError('暂停语音识别出错:', error);
      }
    }
    
    // 在暂停时，将当前识别的内容合并到完整文本中
    const currentCompleteText = constructCompleteText();
    if (currentCompleteText && currentCompleteText.trim()) {
      fullRecognizedText = currentCompleteText.trim();
      log('暂停时保存的完整文本:', fullRecognizedText);
    }
    
    // 记录暂停前的文本，确保恢复时不会重复
    pausedRecognizedText = fullRecognizedText;
    
    // 向暂停标记数组添加当前位置索引
    recordingPauseMarkers.push(pausedRecognizedText.length);
    
    // 清空当前识别文本，为下一段录音做准备
    currentRecognizedText = '';
    
    // 暂停录音
    if (recording) {
      try {
        const status = await recording.getStatusAsync();
        if (status.isRecording) {
          await recording.pauseAsync();
          log('录音已暂停');
        }
      } catch (error) {
        logError('暂停录音出错:', error);
      }
    }
    
    // 设置录音状态
    currentRecordingStatus = 'paused';
    return 'paused';
  } catch (error) {
    logError('暂停录音过程出错:', error);
    isPausedRecording = false; // 出错时重置状态
    return 'error';
  }
};

/**
 * 恢复录音
 * @returns 录音状态
 */
export const resumeRecording = async (): Promise<RecordingStatus> => {
  try {
    log('恢复录音，当前保存的文本:', pausedRecognizedText);
    
    // 获取用户选择的转写模型
    const settings = await storageService.getUserSettings();
    const selectedModel = settings.transcribeModel || 'native';
    const useWebApiModel = selectedModel === 'whisper-1' || selectedModel === 'gpt-4o-mini-transcribe';
    
    // 如果录音对象不存在，尝试重新创建录音会话
    if (!recording) {
      log('录音对象不存在，尝试重新创建录音会话...');
      
      try {
        // 使用统一的录音创建函数
        await startAudioRecording();
        log('录音会话已重新创建并开始录音');
        
      } catch (recreateError) {
        logError('重新创建录音会话失败:', recreateError);
        isPausedRecording = false;
        return 'error';
      }
    } else {
      // 录音对象存在，尝试恢复暂停的录音
      try {
        const recordingStatus = await recording.getStatusAsync();
        
        // 如果录音被暂停，则恢复录音
        if (!recordingStatus.isRecording) {
          await recording.startAsync();
          log('已恢复暂停的录音');
        } else {
          log('录音已在进行中，无需恢复');
        }
      } catch (error) {
        logError('恢复录音失败:', error);
        
        // 如果恢复失败，尝试重新创建
        log('恢复失败，尝试重新创建录音...');
        try {
          recording = null; // 清除失效的对象
          
          // 使用统一的录音创建函数
          await startAudioRecording();
          log('录音会话已重新创建');
        } catch (fallbackError) {
          logError('重新创建录音也失败:', fallbackError);
          isPausedRecording = false;
          return 'error';
        }
      }
    }
    
    // 清除暂停状态标记
    isPausedRecording = false;
    
    // 记录暂停标记，表示开始新的段落
    recordingPauseMarkers.push(pausedRecognizedText.length);
    
    // 重新启动语音识别（如果不是使用Whisper模式）
    if (!useWebApiModel && activeCallback) {
      try {
        log('恢复录音：优先启动语音识别服务');
        
        // 重新设置监听器
        setupSpeechRecognitionListeners(activeCallback);
        
        // 先启动语音识别服务
        await ExpoSpeechRecognitionModule.start({
          lang: 'zh-CN',
          interimResults: true,
          addsPunctuation: true
        });
        
        isRecognizing = true;
        log('恢复录音：语音识别已启动，等待服务就绪');
        
        // 等待语音识别服务完全启动
        await new Promise(resolve => setTimeout(resolve, 200));
        
        log('恢复录音：语音识别服务已就绪');
        
        // 立即显示之前保存的文本
        if (activeCallback.onRecognizing && pausedRecognizedText && pausedRecognizedText.trim()) {
          log('显示恢复前的文本:', pausedRecognizedText);
          activeCallback.onRecognizing(pausedRecognizedText);
        }
      } catch (error) {
        logError('恢复语音识别失败:', error);
      }
    } else if (useWebApiModel && activeCallback) {
      // WebAPI模式下也需要显示之前的文本
      if (activeCallback.onRecognizing && pausedRecognizedText && pausedRecognizedText.trim()) {
        log('WebAPI模式显示恢复前的文本:', pausedRecognizedText);
        activeCallback.onRecognizing(pausedRecognizedText);
      }
    }
    
    // 更新录音状态
    currentRecordingStatus = 'recording';
    return 'recording';
  } catch (error) {
    logError('恢复录音过程出错:', error);
    isPausedRecording = false;
    return 'error';
  }
};

/**
 * 获取录音暂停标记点
 * @returns 暂停标记点数组(毫秒)
 */
export const getRecordingPauseMarkers = (): number[] => {
  return [...recordingPauseMarkers];
};

/**
 * 获取当前录音文件的URI
 * @returns 录音文件URI或null
 */
export const getCurrentRecordingUri = (): string | null => {
  return currentRecordingUri;
};

/**
 * 释放音频资源
 * @param force 是否强制释放，即使看起来不在录音状态
 */
export const releaseAudioResources = async (force: boolean = false): Promise<void> => {
  try {
    log('释放所有音频和语音识别资源', { force, isPausedRecording });
    
    // 如果正在暂停状态且不是强制释放，则跳过录音资源的释放
    if (isPausedRecording && !force) {
      log('录音处于暂停状态，跳过录音资源释放，仅释放语音识别资源');
      
      // 仍然需要停止语音识别相关资源
      if (recognitionWatchdog) {
        clearInterval(recognitionWatchdog);
        recognitionWatchdog = null;
      }
      
      if (isRecognizing) {
        try {
          log('正在停止语音识别...');
          await ExpoSpeechRecognitionModule.stop();
          log('语音识别已停止');
        } catch (err) {
          warn('停止语音识别失败，忽略并继续:', err);
        } finally {
          isRecognizing = false;
        }
      }
      
      // 移除所有监听器
      removeAllListeners();
      
      // 但保留录音对象和相关状态
      log('暂停状态下，保留录音对象以便后续恢复');
      return;
    }
    
    // 停止任何监视器和定时器
    if (recognitionWatchdog) {
      clearTimeout(recognitionWatchdog);
      recognitionWatchdog = null;
      log('识别监控定时器已清除');
    }
    
    // 清除录音时间更新定时器
    if (updateRecordingTimeInterval) {
      clearInterval(updateRecordingTimeInterval);
      updateRecordingTimeInterval = null;
      log('录音时间更新定时器已清除');
    }
    
    // 清除任何可能存在的Whisper流式处理定时器
    if ((global as any).whisperStreamingInterval) {
      clearInterval((global as any).whisperStreamingInterval);
      (global as any).whisperStreamingInterval = null;
      log('Whisper流式处理定时器已清除');
    }
    
    // 停止语音识别
    if (isRecognizing || force) {
      try {
        log('正在停止语音识别...');
        await ExpoSpeechRecognitionModule.stop();
        log('语音识别已停止');
      } catch (err) {
        warn('停止语音识别失败，忽略并继续:', err);
      } finally {
        isRecognizing = false;
      }
    }
    
    // 移除所有监听器
    removeAllListeners();
    
    // 停止录音
    if (recording || force) {
      try {
        // 检查录音状态
        try {
          const status = await recording?.getStatusAsync();
          if (!recording || status?.isRecording || status?.isDoneRecording) {
            await recording?.stopAndUnloadAsync().catch(e => {
              warn('停止录音时出错，尝试强制释放:', e);
            });
            log('录音资源已停止并释放');
          } else {
            log('录音已经处于停止状态，无需再次停止');
          }
        } catch (statusError) {
          warn('获取录音状态失败，尝试直接停止:', statusError);
          try {
            await recording?.stopAndUnloadAsync().catch(e => {
              warn('直接停止录音失败，忽略:', e);
            });
          } catch (innerError) {
            warn('直接停止录音时出错，忽略:', innerError);
          }
        }
      } catch (err) {
        warn('释放录音资源过程中出错，强制清理:', err);
      } finally {
        // 无论发生什么错误，都确保录音对象被释放
        recording = null;
        currentRecordingUri = null;
        
        // Android平台：清除全局引用
        if (Platform.OS === 'android') {
          (global as any).__currentRecording = null;
        }
        
        log('录音对象已置为null');
      }
    }
    
    try {
      // 重置音频模式，以便下次录音能正常初始化
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: false,
        staysActiveInBackground: false,
        // 保持其他默认值
        interruptionModeIOS: InterruptionModeIOS.MixWithOthers,
        interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
      });
      log('音频模式已重置');
    } catch (audioModeError) {
      warn('重置音频模式失败，忽略:', audioModeError);
    }
    
    // 重置所有状态
    currentRecognizedText = '';
    fullRecognizedText = '';
    pausedRecognizedText = '';
    lastSpeechActivityTimestamp = 0;
    recordingPauseMarkers = [];
    activeCallback = null;
    restartAttemptCount = 0;
    hasNotifiedTimeout = false;
    currentRecordingStatus = 'idle';
    isProcessingTranscription = false; // 重置API调用锁机制
    isWhisperApiCallInProgress = false;
    lastWhisperApiCallTimestamp = 0;
    
    // 仅在完全释放时重置暂停状态
    if (force || currentRecordingStatus === 'idle') {
      isPausedRecording = false;
    }
    
    log('所有音频资源已释放');
  } catch (error) {
    logError('释放音频资源时出错，强制重置所有状态:', error);
    // 即使在最外层出错，也确保所有状态被重置
    recording = null;
    isRecognizing = false;
    currentRecognizedText = '';
    fullRecognizedText = '';
    pausedRecognizedText = '';
    lastSpeechActivityTimestamp = 0;
    recordingPauseMarkers = [];
    activeCallback = null;
    currentRecordingStatus = 'idle';
    isPausedRecording = false;
  }
};

/**
 * 停止录音
 * @param callback 语音识别回调函数
 * @returns 录音状态
 */
export const stopRecording = async (callback: SpeechRecognitionCallback): Promise<RecordingStatus> => {
  try {
    log('停止录音');
    
    // 重置暂停状态标记
    isPausedRecording = false;
    
    // 先检查并清除Whisper流式处理定时器
    if ((global as any).whisperStreamingInterval) {
      log('[Whisper模式] 清除Whisper流式处理定时器');
      clearInterval((global as any).whisperStreamingInterval);
      (global as any).whisperStreamingInterval = null;
    }
    
    // 检查当前录音状态
    if (!recording) {
      log('没有正在进行的录音');
      return 'stopped';
    }
    
    // 获取设置中选择的转写模型
    const settings = await storageService.getUserSettings();
    const selectedModel = settings.transcribeModel || 'native';
    const useWebApiModel = selectedModel === 'whisper-1' || selectedModel === 'gpt-4o-mini-transcribe';
    
    log(`[语音模型] 停止录音时使用的转写模型: ${selectedModel}, Web API模式: ${useWebApiModel}`);
    
    // 停止语音识别
    if (isRecognizing) {
      if (useWebApiModel) {
        log(`[${selectedModel}模式] 停止录音，准备转写`);
      } else {
        log('[Native模式] 停止本地语音识别');
        try {
          await ExpoSpeechRecognitionModule.stop();
          // 对于native模式，处理识别结果但不立即返回，让后续文件处理逻辑执行
          if (selectedModel === 'native') {
            log('[Native模式] 本地语音识别已停止，处理识别结果');
            
            // 确保在停止时也将当前识别内容合并到完整文本中
            const currentCompleteText = constructCompleteText();
            let finalText = currentCompleteText;
            
            // 如果有暂停前保存的文本，需要进行拼接
            if (pausedRecognizedText && pausedRecognizedText.trim().length > 0) {
              log('[Native模式] 发现暂停前的文本，进行拼接。之前文本:', pausedRecognizedText);
              log('[Native模式] 当前识别文本:', currentCompleteText);
              
              // 检查当前文本是否已经包含了暂停前的文本
              if (!currentCompleteText.includes(pausedRecognizedText.trim())) {
                // 检查是否需要添加分隔符
                if (!pausedRecognizedText.trim().endsWith('。') && 
                    !pausedRecognizedText.trim().endsWith('！') && 
                    !pausedRecognizedText.trim().endsWith('？') && 
                    !pausedRecognizedText.trim().endsWith('.') && 
                    !pausedRecognizedText.trim().endsWith('!') && 
                    !pausedRecognizedText.trim().endsWith('?')) {
                  // 如果之前的文本没有以标点符号结尾，添加空格分隔
                  finalText = pausedRecognizedText.trim() + ' ' + currentCompleteText.trim();
                } else {
                  // 如果已有标点符号，直接拼接
                  finalText = pausedRecognizedText.trim() + currentCompleteText.trim();
                }
                
                log('[Native模式] 拼接后的最终文本:', finalText);
              } else {
                log('[Native模式] 当前文本已包含暂停前文本，无需额外拼接');
              }
            } else if (!currentCompleteText || currentCompleteText.trim().length === 0) {
              // 如果当前没有新识别内容但有暂停前的文本，返回暂停前的文本
              if (pausedRecognizedText && pausedRecognizedText.trim().length > 0) {
                finalText = pausedRecognizedText.trim();
                log('[Native模式] 无新识别内容，返回暂停前的文本:', finalText);
              }
            }
            
            // 更新全局状态
            fullRecognizedText = finalText;
            currentRecognizedText = finalText;
            
            if (finalText && finalText.trim().length > 0) {
              log('[Native模式] 处理识别结果，文本长度:', finalText.length);
              // 先保存识别结果，但不立即调用callback，让后续逻辑处理录音文件
              (global as any).nativeRecognitionResult = finalText;
            } else {
              log('[Native模式] 没有识别到有效文本');
              (global as any).nativeRecognitionResult = null;
            }
            
            // 设置标志表示native模式已处理完识别部分，但不立即返回
            (global as any).isNativeModeProcessed = true;
            
            log('[Native模式] 识别结果已处理，继续执行录音文件处理逻辑');
          }
        } catch (stopError) {
          warn('停止语音识别出错:', stopError);
        }
      }
      
      isRecognizing = false;
    }
    
    // 录音完成前，获取录音对象的URI(适用于iOS)
    let recordingUri: string | null = null;
    try {
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        // 尝试获取录音对象的URI
        if (recording) {
        recordingUri = (recording as any)._uri || null;
        if (recordingUri) {
          log('从录音对象获取到URI:', recordingUri);
          }
        } else {
          log('录音对象已为null，尝试使用已保存的URI:', currentRecordingUri);
          // 如果录音对象已经被释放但我们已经保存了URI，使用保存的URI
          if (currentRecordingUri) {
            recordingUri = currentRecordingUri;
            log('使用已保存的URI继续处理:', recordingUri);
          }
        }
      }
    } catch (getUriError) {
      warn('从录音对象获取URI失败，尝试使用已保存的URI:', getUriError);
      // 在出错时尝试使用已保存的URI
      if (currentRecordingUri) {
        recordingUri = currentRecordingUri;
        log('使用已保存的URI继续处理:', recordingUri);
      }
    }
    
    // 首先停止所有定时器和清理状态
    if (updateRecordingTimeInterval) {
      clearInterval(updateRecordingTimeInterval);
      updateRecordingTimeInterval = null;
      log('停止录音时已清除录音时间定时器');
    }
    
    // 清除所有可能存在的定时器
    if (recognitionWatchdog) {
      clearTimeout(recognitionWatchdog);
      recognitionWatchdog = null;
      log('停止录音时已清除识别监控定时器');
    }
    
    // 创建一个本地变量保存录音对象引用，然后将全局引用置空
    // 这样可以防止其他地方同时访问并停止录音器
    const localRecording = recording;
    recording = null; // 立即置空全局引用，防止其他地方访问
    
    // 更新录音状态
    currentRecordingStatus = 'stopped';
    
    // 获取当前录音状态
    let status: Audio.RecordingStatus = { 
      canRecord: false, 
      isRecording: false, 
      isDoneRecording: false, 
      durationMillis: 0 
    };
    
    try {
      if (localRecording) {
        status = await localRecording.getStatusAsync();
        log('当前录音状态:', {
          isRecording: status.isRecording,
          isDoneRecording: status.isDoneRecording,
          canRecord: status.canRecord,
          durationMillis: status.durationMillis,
          isPausedRecording: isPausedRecording
        });
      } else {
        log('录音对象不存在，无法获取状态');
      }
    } catch (statusError) {
      warn('获取录音状态失败，可能录音对象已失效:', statusError);
    }
    
    // 停止录音
    let uri = currentRecordingUri;
    
    // 修复：正确处理录音处于暂停状态的情况
    const needsToStopRecording = localRecording && (
      status.isRecording ||           // 正在录音
      status.isDoneRecording ||       // 已完成录音
      isPausedRecording ||            // 处于暂停状态
      status.canRecord               // 录音器已准备好（可能是暂停状态）
    );
    
    if (needsToStopRecording) {
      log('准备停止录音器，当前状态:', {
        isRecording: status.isRecording,
        isDoneRecording: status.isDoneRecording,
        isPausedRecording: isPausedRecording,
        canRecord: status.canRecord
      });
      
      try {
        // 如果录音处于暂停状态，需要先恢复然后立即停止
        if (isPausedRecording && !status.isRecording && !status.isDoneRecording) {
          log('录音处于暂停状态，先恢复后立即停止');
          try {
            // 恢复录音
            await localRecording.startAsync();
            log('录音已恢复，准备立即停止');
            
            // 短暂延迟确保状态变更完成
            await new Promise(resolve => setTimeout(resolve, 100));
          } catch (resumeError) {
            warn('恢复暂停的录音失败，尝试直接停止:', resumeError);
            // 即使恢复失败也继续尝试停止
          }
        }
        
        // 停止并获取录音结果
        const stopResult = await localRecording.stopAndUnloadAsync();
        log('录音停止结果:', stopResult);
        
        // 尝试从停止结果中获取URI (适用于某些expo-av版本)
        if (stopResult && (stopResult as any).uri) {
          uri = (stopResult as any).uri;
          log('从停止结果获取到录音URI:', uri);
        } else if (recordingUri) {
          // 如果之前获取到了URI，优先使用它
          uri = recordingUri;
          log('使用之前获取的录音URI:', uri);
        }
        
        // 获取录音文件信息
        if (uri) {
          try {
            // 尝试两种路径格式
            const fileInfoWithPrefix = await FileSystem.getInfoAsync(`file://${uri}`.replace('file://file://', 'file://'));
            const fileInfoNoPrefix = await FileSystem.getInfoAsync(uri.replace('file://', ''));
            
            if (fileInfoWithPrefix.exists) {
              log(`录音文件信息(带前缀): 大小=${(fileInfoWithPrefix as any).size || '未知'}字节`);
              uri = `file://${uri}`.replace('file://file://', 'file://');
            } else if (fileInfoNoPrefix.exists) {
              log(`录音文件信息(无前缀): 大小=${(fileInfoNoPrefix as any).size || '未知'}字节`);
              uri = uri.replace('file://', '');
            } else {
              warn('录音文件可能不存在，但继续处理');
            }
          } catch (fileInfoError) {
            warn('获取录音文件信息失败，但继续处理:', fileInfoError);
          }
        }
      } catch (stopRecordingError) {
        logError('停止录音出错:', stopRecordingError);
        
        // 即使停止出错，也尝试获取URI
        if (!uri && recordingUri) {
          uri = recordingUri;
          log('停止录音出错，使用之前保存的URI:', uri);
        }
        
        // 如果仍然没有URI，但可能有currentRecordingUri，尝试使用它
        if (!uri && currentRecordingUri) {
          uri = currentRecordingUri;
          log('使用当前记录的URI作为备用:', uri);
        }
        
        // 只有当完全没有URI时才视为严重错误
        if (!uri) {
          logError('无法获取录音文件地址，可能无法完成转写');
          // 强制释放资源
          await releaseAudioResources(true);
          callback?.onError?.(new Error('无法获取录音文件，请重试'));
          return 'error';
        } else {
          // 如果已经获得了URI，记录错误但继续处理
          log('虽然停止录音时出错，但已获取到录音文件URI，继续处理:', uri);
        }
      }
    } else {
      // 录音状态异常的处理
      log('录音状态异常，尝试使用备用URI');
      
      if (recordingUri) {
        uri = recordingUri;
        log('使用之前获取的录音URI:', uri);
      } else if (currentRecordingUri) {
        uri = currentRecordingUri;
        log('使用当前记录的录音URI:', uri);
      }
      
      if (!uri) {
        logError('录音状态异常且没有获取到URI，强制释放资源');
        // 强制释放资源
        await releaseAudioResources(true);
        callback?.onError?.(new Error('录音状态异常，请重试'));
        return 'error';
      } else {
        log('录音状态异常但已获取到URI，继续处理:', uri);
      }
    }
    
    // 更新当前录音URI
    if (uri && uri !== currentRecordingUri) {
      log('更新录音URI:', uri);
      currentRecordingUri = uri;
    }
    
    // 移除事件监听器
    removeAllListeners();
    
    // 如果没有URI，返回错误
    if (!uri) {
      logError('获取录音文件URI失败，强制释放所有资源');
      // 强制释放资源，确保下次录音能正常启动
      await releaseAudioResources(true);
      callback?.onError?.(new Error('无法获取录音文件'));
      currentRecordingStatus = 'error';
      return 'error';
    }
    
    log('录音完成，文件URI:', uri);

    // 将录音文件移动到正确的用户目录
    let finalUri = uri;
    log(`开始处理录音文件移动，原始URI: ${uri}`);

    try {
      // 从原始URI中提取文件名，保持原始文件名不变
      const originalFileName = uri.split('/').pop() || 'recording.m4a';
      log(`提取原始文件名: ${originalFileName}`);

      // 获取用户录音目录
      await userIdService.initialize();
      const currentUserId = await userIdService.getCurrentUserId();
      const userRecordingDir = await recordingFileService.getUserRecordingsDirectory(currentUserId);

      // 构建目标路径，使用原始文件名
      const targetPath = `${userRecordingDir}/${originalFileName}`;
      log(`目标路径（保持原始文件名）: ${targetPath}`);

      // 确保目标目录存在
      log(`确保目标目录存在: ${userRecordingDir}`);
      await FileSystem.makeDirectoryAsync(userRecordingDir, { intermediates: true });

      // 检查源文件是否存在
      const sourceUri = uri.startsWith('file://') ? uri : `file://${uri}`;
      log(`检查源文件是否存在: ${sourceUri}`);

      const sourceExists = await FileSystem.getInfoAsync(sourceUri);
      const sourceSize = sourceExists.exists && 'size' in sourceExists ? sourceExists.size : 'unknown';
      log(`源文件存在状态: ${sourceExists.exists}, 大小: ${sourceSize}`);

      if (sourceExists.exists) {
        // 使用更安全的复制+删除方式替代直接移动
        const targetUri = targetPath.startsWith('file://') ? targetPath : `file://${targetPath}`;
        log(`开始复制文件: ${sourceUri} -> ${targetUri}`);

        try {
          // 先复制文件
          await FileSystem.copyAsync({
            from: sourceUri,
            to: targetUri
          });

          // 验证复制是否成功
          const targetExists = await FileSystem.getInfoAsync(targetUri);
          if (targetExists.exists) {
            const targetSize = 'size' in targetExists ? targetExists.size : 0;
            const sourceFileSize = 'size' in sourceExists ? sourceExists.size : 0;

            // 验证文件大小是否一致（允许小幅差异）
            if (targetSize > 0 && (sourceFileSize === 0 || Math.abs(targetSize - sourceFileSize) <= 100)) {
              // 复制成功，删除原文件
              try {
                await FileSystem.deleteAsync(sourceUri);
                log(`✅ 录音文件移动成功: ${sourceUri} -> ${targetUri}, 大小: ${targetSize}`);
                finalUri = targetUri;
                currentRecordingUri = targetUri;
              } catch (deleteError) {
                warn(`⚠️ 删除原文件失败，但复制成功: ${deleteError}`);
                // 即使删除失败，也使用复制后的文件
                finalUri = targetUri;
                currentRecordingUri = targetUri;
              }
            } else {
              warn(`❌ 复制后文件大小不匹配: 源文件${sourceFileSize}字节, 目标文件${targetSize}字节`);
              // 删除不完整的目标文件
              try {
                await FileSystem.deleteAsync(targetUri);
              } catch (cleanupError) {
                warn('清理不完整目标文件失败:', cleanupError);
              }
              // 使用原始文件
              finalUri = sourceUri;
              currentRecordingUri = sourceUri;
            }
          } else {
            warn(`❌ 文件复制后目标文件不存在: ${targetUri}`);
            // 使用原始文件
            finalUri = sourceUri;
            currentRecordingUri = sourceUri;
          }
        } catch (copyError) {
          logError('❌ 复制录音文件失败:', copyError);
          // 复制失败，使用原始文件
          finalUri = sourceUri;
          currentRecordingUri = sourceUri;
          warn('复制失败，使用原始路径继续处理');
        }
      } else {
        warn(`❌ 源录音文件不存在: ${sourceUri}`);
        // 尝试检查是否文件在其他位置
        log('尝试查找录音文件在其他可能的位置...');

        // 检查是否在Documents目录
        const documentsUri = uri.replace('/Library/Caches/AV/', '/Documents/');
        const documentsExists = await FileSystem.getInfoAsync(documentsUri);
        if (documentsExists.exists) {
          log(`在Documents目录找到文件: ${documentsUri}`);
          finalUri = documentsUri;
          currentRecordingUri = documentsUri;
        } else {
          // 如果找不到文件，但仍需要设置一个路径用于后续处理
          warn('未找到录音文件，使用原始URI继续处理');
          finalUri = uri;
          currentRecordingUri = uri;
        }
      }
    } catch (moveError) {
      logError('❌ 处理录音文件失败:', moveError);
      // 确保有一个可用的URI，优先使用原始URI
      finalUri = uri;
      currentRecordingUri = uri;
      warn('使用原始路径继续处理');
    }

    // 最终验证：确保选择的文件路径确实存在
    try {
      const finalFileInfo = await FileSystem.getInfoAsync(finalUri);
      if (!finalFileInfo.exists) {
        warn(`⚠️ 最终选择的文件路径不存在: ${finalUri}`);
        // 尝试查找任何可用的录音文件作为备选
        const backupFile = await findValidAudioFile(uri);
        if (backupFile) {
          log(`使用备选文件: ${backupFile}`);
          finalUri = backupFile;
          currentRecordingUri = backupFile;
        } else {
          warn('无法找到任何有效的录音文件，但继续处理');
        }
      }
    } catch (verifyError) {
      warn('验证最终文件路径时出错:', verifyError);
    }

    // 保存录音文件记录到数据库
    try {
      await recordingFileService.saveRecordingFileRecord(finalUri);
      log('录音文件记录已保存到数据库');
    } catch (saveError) {
      warn('保存录音文件记录失败，但继续处理:', saveError);
    }

    // 检查是否有意义的文本已被识别
    const hasRecognizedText = currentRecognizedText && 
                             currentRecognizedText.trim().length > 0 && 
                             !currentRecognizedText.startsWith('正在') &&
                             !currentRecognizedText.startsWith('没有检测到');
                             
    log(`录音已停止，文件路径: ${uri} 暂停点: ${JSON.stringify(recordingPauseMarkers)}`);
    log(`文本是有意义的内容: ${hasRecognizedText ? '是' : '否'}, 内容: ${currentRecognizedText}`);
    
    // 检查是否是native模式且已处理完成
    if (selectedModel === 'native') {
      if (hasRecognizedText) {
        log('[Native模式] 已有有效识别文本，跳过后续转写逻辑');
        return 'stopped';
      } else if ((global as any).isNativeModeProcessed) {
        log('[Native模式] 已处理完成，跳过后续转写逻辑');
        // 清除标志
        (global as any).isNativeModeProcessed = false;
        return 'stopped';
      }
    }
    
    // 转写模式处理逻辑
    if (useWebApiModel) {
      // 检查是否已经在进行 API 调用，防止重复调用
      const currentTime = Date.now();
      if (isWhisperApiCallInProgress) {
        log(`[${selectedModel}模式] 已有API调用正在进行，跳过重复调用`);
        return 'stopped';
      }
      
      // 检查是否在冷却时间内
      if (currentTime - lastWhisperApiCallTimestamp < API_CALL_COOLDOWN) {
        log(`[${selectedModel}模式] 距离上次API调用时间过短，跳过重复调用`);
        return 'stopped';
      }
      
      // 设置锁定状态
      isWhisperApiCallInProgress = true;
      lastWhisperApiCallTimestamp = currentTime;
      isProcessingTranscription = true;
      
      // 使用OpenAI的API进行转写处理
      log(`[${selectedModel}模式] 使用${selectedModel}进行一次性转写处理`);
      try {
        // 确保录音已完全停止，再进行后续处理
        log(`[${selectedModel}模式] 确保录音已完全停止`);
        
        // 获取录音文件路径
        let processedUri = uri;

        // 优先使用移动后的最新URI（currentRecordingUri），而不是原始的recordingUri
        if (currentRecordingUri && currentRecordingUri !== recordingUri) {
          processedUri = currentRecordingUri;
          log(`[${selectedModel}模式] 使用移动后的URI:`, processedUri);
        } else if (recordingUri) {
          processedUri = recordingUri;
          log(`[${selectedModel}模式] 使用录音时获取的URI:`, processedUri);
        }
        
        // 检查文件是否存在
        log(`[${selectedModel}模式] 尝试查找录音文件:`, processedUri);
        
        // 检查文件是否存在并获取文件信息
        let fileExists = false;
        let fileSize = 0;
        let validFilePath = '';
        
        try {
          // 检查文件是否存在（尝试带和不带file://前缀两种情况）
          const withoutPrefix = processedUri.replace('file://', '');
          const withPrefix = processedUri.startsWith('file://') ? processedUri : `file://${processedUri}`;
          
          // 添加重试机制，等待录音文件完全写入
          let retryCount = 0;
          const maxRetries = 5;
          const retryDelay = 300; // 300ms间隔
          
          while (retryCount < maxRetries && !fileExists) {
            if (retryCount > 0) {
              log(`[${selectedModel}模式] 第${retryCount}次重试检查录音文件...`);
              await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
            
            // 先尝试不带前缀的路径
            try {
              const fileInfo = await FileSystem.getInfoAsync(withoutPrefix);
              if (fileInfo.exists) {
                const currentSize = (fileInfo as any).size || 0;
                
                if (currentSize >= 1000) { // 文件大小合理
                  fileExists = true;
                  fileSize = currentSize;
                  validFilePath = withoutPrefix;
                  log(`[${selectedModel}模式] 找到有效的录音文件(无前缀): ${withoutPrefix}, 大小: ${fileSize}字节`);
                  break;
                } else if (retryCount < maxRetries - 1) {
                  log(`[${selectedModel}模式] 文件存在但太小(${currentSize}字节)，可能仍在写入中，继续等待...`);
                } else {
                  // 最后一次重试，即使文件较小也接受（可能是很短的录音）
                  warn(`[${selectedModel}模式] 录音文件较小但已是最后重试: ${currentSize}字节`);
                  if (currentSize > 0) {
                    fileExists = true;
                    fileSize = currentSize;
                    validFilePath = withoutPrefix;
                  }
                }
              }
            } catch (error) {
              log(`[${selectedModel}模式] 检查无前缀路径失败:`, error);
            }
            
            // 如果不带前缀的路径不存在或太小，尝试带前缀的路径
            if (!fileExists) {
              try {
                const fileInfo = await FileSystem.getInfoAsync(withPrefix);
                if (fileInfo.exists) {
                  const currentSize = (fileInfo as any).size || 0;
                  
                  if (currentSize >= 1000) { // 文件大小合理
                    fileExists = true;
                    fileSize = currentSize;
                    validFilePath = withPrefix;
                    log(`[${selectedModel}模式] 找到有效的录音文件(带前缀): ${withPrefix}, 大小: ${fileSize}字节`);
                    break;
                  } else if (retryCount < maxRetries - 1) {
                    log(`[${selectedModel}模式] 文件存在但太小(${currentSize}字节)，可能仍在写入中，继续等待...`);
                  } else {
                    // 最后一次重试，即使文件较小也接受（可能是很短的录音）
                    warn(`[${selectedModel}模式] 录音文件较小但已是最后重试: ${currentSize}字节`);
                    if (currentSize > 0) {
                      fileExists = true;
                      fileSize = currentSize;
                      validFilePath = withPrefix;
                    }
                  }
                }
              } catch (error) {
                log(`[${selectedModel}模式] 检查带前缀路径失败:`, error);
              }
            }
            
            retryCount++;
          }
          
          if (!fileExists) {
            throw new Error('无法找到有效的录音文件');
          }
          
          // 降低文件大小阈值，因为短录音可能确实较小
          if (fileSize < 100) { // 如果文件太小（少于100字节），可能是空文件
            warn(`[${selectedModel}模式] 录音文件过小，可能未包含有效音频数据:`, fileSize, '字节');
            callback?.onError?.(new Error('录音文件过小，未检测到有效的语音内容'));
            return 'stopped';
          }
          
          log(`[${selectedModel}模式] 录音文件验证完成，大小: ${fileSize}字节，路径: ${validFilePath}`);
        } catch (fileCheckError) {
          logError(`[${selectedModel}模式] 检查文件路径失败:`, fileCheckError);
          callback?.onError?.(new Error('无法找到录音文件，请重试'));
          return 'error';
        }

        // 确保转写前录音资源已经释放
        log(`[${selectedModel}模式] 首先清理录音资源，确保文件可被访问`);
        if (recording) {
          try {
            await (recording as Audio.Recording).stopAndUnloadAsync();
            log(`[${selectedModel}模式] 录音实例已停止并释放`);
            recording = null;
            log(`[${selectedModel}模式] 录音实例引用已清空`);
          } catch (releaseError) {
            log(`[${selectedModel}模式] 释放录音资源时出错，忽略并继续:`, releaseError);
            recording = null;
          }
        }
        
        // 彻底清理音频模式和资源
        try {
          log(`[${selectedModel}模式] 清理音频模式`);
          await Audio.setAudioModeAsync({
            allowsRecordingIOS: false,
            playsInSilentModeIOS: true,
            staysActiveInBackground: false,
            interruptionModeIOS: InterruptionModeIOS.DoNotMix,
            interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
            shouldDuckAndroid: false,
          });
          log(`[${selectedModel}模式] 音频模式已重置`);
          
          // 强制释放所有音频资源
          await releaseAudioResources(true);
          log(`[${selectedModel}模式] 所有音频资源已释放`);
        } catch (audioCleanupError) {
          warn(`[${selectedModel}模式] 音频清理出错，但继续处理:`, audioCleanupError);
        }
        
        // 清除录音时间显示的定时器
        if (updateRecordingTimeInterval) {
          clearInterval(updateRecordingTimeInterval);
          updateRecordingTimeInterval = null;
          log(`[${selectedModel}模式] 已清除录音时间定时器`);
        }
        
        // 短暂延迟，确保文件写入完成
        await new Promise(resolve => setTimeout(resolve, 300));

        // 开始处理完整的录音文件
        log(`[${selectedModel}模式] 开始调用OpenAI API进行音频转写, 文件大小:`, fileSize, '字节');
        
        try {
        const transcribedText = await OpenAISpeechService.transcribeAudioFile(
            validFilePath, 
          '', 
          selectedModel,
          (message) => {
            // 传递进度信息到UI，使用重试格式
            if (callback?.onRecognizing) {
              callback.onRecognizing(i18n.t('home.textDisplay.retryTranscribingWithModel', { model: selectedModel }));
            }
          }
        );
        
        // 释放API调用锁，允许下一次调用
        isWhisperApiCallInProgress = false;
        isProcessingTranscription = false;
        
        if (transcribedText && transcribedText.trim().length > 0) {
            log(`[${selectedModel}模式] 转写成功，文本长度:`, transcribedText.length);
            log(`[${selectedModel}模式] 转写结果:`, transcribedText);
          
            // 在处理结果前检查任务是否已被取消
            if (isTranscriptionTaskCancelled()) {
              log(`[${selectedModel}模式] 转录任务已被取消，不处理返回结果`);
              return 'stopped';
            }
          
            // 拼接之前保存的文本和新转写的内容
            let finalText = transcribedText.trim();
            
            // 如果有之前暂停时保存的文本，需要拼接
            if (pausedRecognizedText && pausedRecognizedText.trim().length > 0) {
              log(`[${selectedModel}模式] 发现暂停前的文本，进行拼接。之前文本:`, pausedRecognizedText);
              
              // 检查是否需要添加分隔符
              if (!pausedRecognizedText.trim().endsWith('。') && 
                  !pausedRecognizedText.trim().endsWith('！') && 
                  !pausedRecognizedText.trim().endsWith('？') && 
                  !pausedRecognizedText.trim().endsWith('.') && 
                  !pausedRecognizedText.trim().endsWith('!') && 
                  !pausedRecognizedText.trim().endsWith('?')) {
                // 如果之前的文本没有以标点符号结尾，添加空格分隔
                finalText = pausedRecognizedText.trim() + ' ' + transcribedText.trim();
              } else {
                // 如果已有标点符号，直接拼接
                finalText = pausedRecognizedText.trim() + transcribedText.trim();
              }
              
              log(`[${selectedModel}模式] 拼接后的最终文本:`, finalText);
            }
            
            // 更新全局状态变量
            currentRecognizedText = finalText;
            fullRecognizedText = finalText;
          
            // 将转写结果传递给UI - 保证回调被正确调用
            if (callback && callback.onResult) {
              log(`[${selectedModel}模式] 将最终拼接结果传递给UI, 文本长度:`, finalText.length);
              callback.onResult(finalText);
              // 直接返回成功状态，无需后续处理
              return 'stopped';
            } else {
              warn(`[${selectedModel}模式] 未找到有效的回调函数，转写结果可能无法显示在UI上`);
            }
            
            // 返回成功状态
            return 'stopped';
        } else {
          // 即使转写失败也要释放锁
          warn(`[${selectedModel}模式] 转写返回空文本`);
          
          // 如果转写为空但有之前保存的文本，仍然返回之前的文本
          if (pausedRecognizedText && pausedRecognizedText.trim().length > 0) {
            log(`[${selectedModel}模式] 转写为空，但返回之前保存的文本:`, pausedRecognizedText);
            
            // 更新全局状态变量
            currentRecognizedText = pausedRecognizedText;
            fullRecognizedText = pausedRecognizedText;
            
            // 将之前的文本传递给UI
            if (callback && callback.onResult) {
              callback.onResult(pausedRecognizedText);
              return 'stopped';
            }
          }
          
          // 对于短录音或无语音输入的情况，给用户更友好的提示，但不作为严重错误处理
          warn(`[${selectedModel}模式] 转写返回空文本，可能是录音过短或无语音输入`);
          
          // 不调用onError，而是返回一个提示性的文本结果
          if (callback && callback.onResult) {
            // 使用更温和的提示，不会触发错误处理
            callback.onResult('录音时间过短或未检测到语音内容，请重新尝试');
          }
          return 'stopped';
        }
        } catch (transcribeError: any) {
          // 即使出错也要释放锁
          isWhisperApiCallInProgress = false;
          isProcessingTranscription = false;
          
          logError('[Whisper模式] 最终转写失败:', transcribeError);
          
          // 特殊处理：检查是否是whisper每日使用限制错误
          if (transcribeError && transcribeError.isWhisperLimit) {
            log('[Whisper模式] 检测到whisper每日使用限制错误');
            
            // 创建特殊的限制错误对象，包含升级引导信息
            const limitError = new Error(transcribeError.message) as any;
            limitError.code = 'WHISPER_DAILY_LIMIT_EXCEEDED';
            limitError.isWhisperLimit = true;
            limitError.usageInfo = transcribeError.usageInfo;
            limitError.requiresVipUpgrade = true;
            limitError.upgradeMessage = '免费用户每日只能使用5次Whisper转写，升级VIP获得无限使用';
            
            // 调用错误回调，上层会处理VIP升级引导
            callback?.onError?.(limitError);
            return 'error';
          }
          
          // 转写失败错误处理
          
          // 检查是否是超时错误，如果是超时错误，给一些时间等待可能的延迟成功
          const isTimeoutError = transcribeError instanceof Error && 
            (transcribeError.message.includes('超时') || 
             transcribeError.message.includes('timeout') ||
             transcribeError.message.includes('请求超时'));
          
          if (isTimeoutError) {
            log('[Whisper模式] 检测到超时错误，等待可能的延迟成功结果...');
            
            // 等待3秒，检查是否有延迟的成功结果
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 检查当前文本状态，如果有有效文本说明转写实际成功了
            const hasValidResult = currentRecognizedText && 
              currentRecognizedText.trim().length > 0 &&
              !currentRecognizedText.includes('正在') &&
              !currentRecognizedText.includes('转写中');
            
            if (hasValidResult) {
              log('[Whisper模式] 检测到延迟成功结果，忽略超时错误:', currentRecognizedText.substring(0, 50) + '...');
              
              // 更新全局状态
              fullRecognizedText = currentRecognizedText;
              
              // 调用成功回调而不是错误回调
              if (callback && callback.onResult) {
                callback.onResult(currentRecognizedText);
                return 'stopped';
              }
            } else {
              log('[Whisper模式] 确认转写超时失败，没有检测到有效结果');
            }
          }
          
          // 确保错误对象正确传递，保留自定义属性
          if (transcribeError instanceof Error) {
            callback?.onError?.(transcribeError);
          } else {
            // 即使不是Error实例，也要尝试保留isWhisperLimit标志
            const newError = new Error(String(transcribeError)) as any;
            if ((transcribeError as any).isWhisperLimit) {
              newError.isWhisperLimit = true;
              newError.usageInfo = (transcribeError as any).usageInfo;
              newError.requiresVipUpgrade = true;
            }
            callback?.onError?.(newError);
          }
          return 'error';
        }
      } catch (error) {
        // 即使在外层错误中也要释放锁
        isWhisperApiCallInProgress = false;
        isProcessingTranscription = false;
        
        // 外层错误处理
        
        logError('[Whisper模式] 音频处理失败:', error);
        callback?.onError?.(error instanceof Error ? error : new Error(String(error)));
        return 'error';
      }
    }
    // 对于native模式，记录状态即可
    else if (selectedModel === 'native') {
      if (!hasRecognizedText) {
        log('[Native模式] 本地语音识别未返回有效文本');
      } else {
        log('[Native模式] 已有有效的转写文本');
      }
    }

    // 确保所有资源都被正确释放
    try {
      log('最终释放录音资源');
      
      // 录音对象已经在前面被释放，这里不需要再次释放
      
      // 清除所有事件监听器
      removeAllListeners();
      
      // 设置最终状态
      currentRecordingStatus = 'stopped';
      
      // 处理native模式的识别结果
      if ((global as any).isNativeModeProcessed && (global as any).nativeRecognitionResult !== undefined) {
        const nativeResult = (global as any).nativeRecognitionResult;
        log('[Native模式] 在录音文件处理完成后返回识别结果');
        
        if (nativeResult && nativeResult.trim().length > 0) {
          log('[Native模式] 最终返回文本，长度:', nativeResult.length);
          callback?.onResult?.(nativeResult);
        } else {
          log('[Native模式] 没有识别到有效文本');
        }
        
        // 清理临时变量
        (global as any).isNativeModeProcessed = false;
        (global as any).nativeRecognitionResult = undefined;
      }
      
    } catch (finalCleanupError) {
      warn('最终清理过程出错:', finalCleanupError);
    }

    // 回调返回最终状态
    return currentRecordingStatus;
  } catch (error) {
    logError('停止录音过程出错:', error);
    
    // 强制释放所有资源，确保下次录音能正常启动
    try {
      await releaseAudioResources(true);
      log('异常情况下已强制释放所有音频资源');
    } catch (releaseError) {
      logError('强制释放资源时出错:', releaseError);
    }
    
    // 触发错误回调
    if (callback.onError && error instanceof Error) {
      callback.onError(error);
    } else if (callback.onError) {
      callback.onError(new Error('停止录音失败'));
    }
    
    // 确保清除当前持有的全局引用
    if (recording) {
      recording = null;
    }
    
    // 清除事件监听器
    removeAllListeners();
    
    return 'error';
  }
};

/**
 * 获取当前语音识别状态
 * @param callback 回调函数，接收当前识别的文本
 */
export const getRecognitionStatus = (callback: (text: string) => void): void => {
  try {
    // 构建完整的文本内容
    const completeText = constructCompleteText();
    
    // 返回当前识别文本
    callback(completeText);
    
    // 调试日志
    log('获取语音识别状态, 文本长度:', completeText.length);
  } catch (error) {
    logError('获取语音识别状态失败:', error);
    callback(''); // 失败时返回空文本
  }
};

/**
 * 获取完整的多段识别文本
 * @returns 所有段落组合的完整文本
 */
export const getFullRecognizedText = (): string => {
  return constructCompleteText();
};

/**
 * 等待录音真正开始
 * 快速验证录音对象确实在录制状态
 */
const waitForRecordingToStart = async (): Promise<void> => {
  const maxAttempts = 10; // 最多等待1秒 (10 * 100ms)
  const checkInterval = 100;
  let attempts = 0;
  
  log(`快速验证录音启动状态 (最大等待时间: ${maxAttempts * checkInterval}ms)`);
  
  while (attempts < maxAttempts) {
    try {
      if (recording) {
        const status = await recording.getStatusAsync();
        
        if (status.isRecording) {
          log(`录音已确认启动，耗时: ${attempts * checkInterval}ms`);
          return;
        }
      }
    } catch (error) {
      warn(`检查录音状态失败 (尝试 ${attempts + 1}):`, error);
    }
    
    // 等待后重试
    await new Promise(resolve => setTimeout(resolve, checkInterval));
    attempts++;
  }
  
  warn(`录音启动验证超时 (${maxAttempts * checkInterval}ms)，但继续执行`);
};

/**
 * 仅启动音频录制，不启动语音识别
 * 主要用于Whisper等云端转写模型
 */
const startAudioRecording = async (): Promise<void> => {
  try {
    // 如果存在旧的 recording 对象，尝试释放它
    if (recording) {
      try {
        log('清理旧的录音实例');
        await recording.stopAndUnloadAsync();
      } catch (cleanupError) {
        log('清理旧的录音实例时出错，忽略并继续:', cleanupError);
      } finally {
        recording = null;
      }
    }
    
    // 在启动录音前，确保音频模式已正确设置
    log('设置录音前的音频模式');
    await Audio.setAudioModeAsync({
      allowsRecordingIOS: true,
      playsInSilentModeIOS: true,
      staysActiveInBackground: false,
      interruptionModeIOS: InterruptionModeIOS.DuckOthers,
      interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
      shouldDuckAndroid: true,
    });
    
    // 生成录音文件保存路径（使用新的录音文件服务）
    const originalPath = await recordingFileService.getRecordingFilePath();
    currentRecordingUri = originalPath;
    
    log(`准备创建录音文件: ${currentRecordingUri}`);
    
    // 始终创建一个新的录音实例
    log('创建新的录音实例');
    recording = new Audio.Recording();
    
    // Android平台：设置录音对象的引用保护
    if (Platform.OS === 'android') {
      // 确保录音对象有强引用，防止垃圾回收
      (global as any).__currentRecording = recording;
    }
    
    // 快速准备录音配置
    log('准备录音配置...');

    // 尝试使用指定的文件路径
    const recordingOptions: any = {
      android: {
        extension: '.m4a',
        outputFormat: Audio.AndroidOutputFormat.MPEG_4,
        audioEncoder: Audio.AndroidAudioEncoder.AAC,
        sampleRate: 44100,
        numberOfChannels: 1,
        bitRate: 128000,
      },
      ios: {
        extension: '.m4a',
        outputFormat: Audio.IOSOutputFormat.MPEG4AAC,
        audioQuality: Audio.IOSAudioQuality.HIGH,
        sampleRate: 44100,
        numberOfChannels: 1,
        bitRate: 128000,
        linearPCMBitDepth: 16,
        linearPCMIsBigEndian: false,
        linearPCMIsFloat: false,
      },
      web: {
        mimeType: 'audio/webm',
        bitsPerSecond: 128000,
      }
    };

    // 尝试指定文件URI（如果支持的话）
    try {
      log(`尝试使用指定路径创建录音: ${currentRecordingUri}`);
      recordingOptions.uri = currentRecordingUri;
      await recording.prepareToRecordAsync(recordingOptions);
      log('成功使用指定路径创建录音');
    } catch (uriError) {
      // 如果指定URI失败，使用默认配置
      warn('使用指定URI失败，回退到默认配置:', uriError);
      delete recordingOptions.uri;
      await recording.prepareToRecordAsync(recordingOptions);
      log('使用默认配置创建录音，稍后将移动文件');
    }
    
    log('开始音频录音');
    
    // 直接启动录音，移除额外验证
    await recording.startAsync();
    
    // 设置当前录音状态
    currentRecordingStatus = 'recording';
      
    // 异步获取实际录音路径（不阻塞）
    setTimeout(() => {
      if (recording) {
        const realUri = (recording as any)._uri || null;
        if (realUri && realUri !== currentRecordingUri) {
          log(`录音实际URI与预期不同，更新路径: ${realUri}`);
          currentRecordingUri = realUri;
        }
      }
    }, 100);
    
    log('录音启动完成，等待实际文件创建');
    
  } catch (error) {
    logError('启动音频录音失败:', error);
    // 确保状态正确设置为错误
    currentRecordingStatus = 'error';
    throw error;
  }
}; 

/**
 * 查找有效的音频文件路径
 * 使用多种策略查找可用的音频文件
 * @param audioUri 原始音频文件URI
 * @returns 有效的文件路径或null
 */
const findValidAudioFile = async (audioUri: string): Promise<string | null> => {
  let validFilePath = audioUri;

  // 处理文件路径格式
  if (validFilePath.startsWith('file://')) {
    validFilePath = validFilePath.replace('file://', '');
  }

  try {
    // 策略1: 检查原始路径
    let fileInfo = await FileSystem.getInfoAsync(validFilePath);
    if (fileInfo.exists) {
      log(`找到音频文件: 原始路径 ${validFilePath}`);
      return validFilePath;
    }

    // 策略2: 尝试添加file://前缀
    const filePathWithPrefix = `file://${validFilePath}`;
    const infoWithPrefix = await FileSystem.getInfoAsync(filePathWithPrefix);
    if (infoWithPrefix.exists) {
      log(`找到音频文件: 带前缀路径 ${filePathWithPrefix}`);
      return filePathWithPrefix;
    }

    // 策略3: 尝试从用户录音目录查找最新文件
    log('原始路径找不到文件，尝试查找用户录音目录中的最新文件');

    await userIdService.initialize();
    const currentUserId = await userIdService.getCurrentUserId();
    const userRecordingFiles = await recordingFileService.getUserRecordingFiles(currentUserId);

    if (userRecordingFiles.length > 0) {
      // 检查每个文件是否存在，并按文件名排序（通常包含时间戳）
      const validFiles = [];
      for (const filePath of userRecordingFiles) {
        try {
          const info = await FileSystem.getInfoAsync(filePath);
          if (info.exists) {
            validFiles.push(filePath);
          }
        } catch {
          // 忽略无法访问的文件
        }
      }

      if (validFiles.length > 0) {
        // 按文件名降序排序，获取最新的文件
        validFiles.sort((a, b) => b.localeCompare(a));
        const latestFile = validFiles[0];
        log(`找到音频文件: 用户目录最新文件 ${latestFile}`);
        return latestFile;
      }
    }

    log('所有策略都未找到有效的音频文件');
    return null;
  } catch (error) {
    logError('查找音频文件时出错:', error);
    return null;
  }
};

/**
 * 重试转写功能
 * 用于在网络错误或超时后重新尝试转写音频文件
 * @param audioUri 音频文件URI
 * @param model 转写模型
 * @param callback 回调函数
 * @returns 是否成功启动重试
 */
export const retryTranscription = async (
  audioUri: string,
  model: string,
  callback: SpeechRecognitionCallback
): Promise<boolean> => {
  try {
    log(`开始重试转写，音频URI: ${audioUri}, 模型: ${model}`);
    
    // 重置API调用锁状态
    isWhisperApiCallInProgress = false;
    isProcessingTranscription = false;
    
    // 检查是否为Web API模型
    const useWebApiModel = model === 'whisper-1' || model === 'gpt-4o-mini-transcribe';
    
    if (!useWebApiModel) {
      logError('重试转写仅支持Web API模型');
      callback.onError?.(new Error('重试转写仅支持Web API模型'));
      return false;
    }
    
    // 使用改进的文件查找逻辑
    const validFilePath = await findValidAudioFile(audioUri);

    if (!validFilePath) {
      logError('重试转写: 无法找到有效的音频文件');
      callback.onError?.(new Error('无法找到音频文件，请重新录音'));
      return false;
    }

    log(`重试转写: 使用音频文件 ${validFilePath}`);
    
    // 设置API调用锁
    isWhisperApiCallInProgress = true;
    isProcessingTranscription = true;
    
    try {
      // 调用OpenAI转写API
      const transcribedText = await OpenAISpeechService.transcribeAudioFile(
        validFilePath,
        '',
        model,
        (message) => {
          // 传递进度信息到UI，使用重试格式
          if (callback?.onRecognizing) {
            callback.onRecognizing(i18n.t('home.textDisplay.retryTranscribingWithModel', { model }));
          }
        }
      );
      
      // 释放API调用锁
      isWhisperApiCallInProgress = false;
      isProcessingTranscription = false;
      
      if (transcribedText && transcribedText.trim().length > 0) {
        log(`重试转写成功，文本长度: ${transcribedText.length}`);
        
        // 更新全局状态变量
        currentRecognizedText = transcribedText;
        fullRecognizedText = transcribedText;
        
        // 调用成功回调
        callback.onResult?.(transcribedText);
        
        return true;
      } else {
        warn('重试转写返回空文本');
        callback.onError?.(new Error('未检测到有效的语音内容'));
        return false;
      }
      
    } catch (transcribeError) {
      // 释放API调用锁
      isWhisperApiCallInProgress = false;
      isProcessingTranscription = false;
      
      logError('重试转写API调用失败:', transcribeError);
      
      // 根据错误类型提供更具体的错误信息
      let errorMessage = '转写失败';
      if (transcribeError instanceof Error) {
        if (transcribeError.message.includes('timeout') || transcribeError.message.includes('超时')) {
          errorMessage = '请求超时，请检查网络连接';
        } else if (transcribeError.message.includes('network') || transcribeError.message.includes('网络')) {
          errorMessage = '网络连接错误，请检查网络设置';
        } else if (transcribeError.message.includes('API')) {
          errorMessage = transcribeError.message;
        } else {
          errorMessage = `转写失败: ${transcribeError.message}`;
        }
      }
      
      callback.onError?.(new Error(errorMessage));
      return false;
    }
    
  } catch (error) {
    // 确保释放API调用锁
    isWhisperApiCallInProgress = false;
    isProcessingTranscription = false;
    
    logError('重试转写过程出错:', error);
    callback.onError?.(error instanceof Error ? error : new Error('重试转写失败'));
    return false;
  }
};