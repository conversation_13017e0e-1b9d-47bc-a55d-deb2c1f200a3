/**
 * OpenAI语音转写服务
 * 用于VIP用户的高级语音转写功能
 * 支持GPT-4o mini和Whisper模型
 */

import * as FileSystem from 'expo-file-system';
import { Audio } from 'expo-av';
import Constants from 'expo-constants';
import i18n from '@/utils/i18n';
import { callOpenAIAPI } from './unifiedProxyService';
import { log, error as logError, warn } from '@/services/logService';
import { checkAndIncrementWhisperUsage, type WhisperUsageLimitResult } from './dailyWhisperUsageService';
import { supabase } from './supabaseService';

// OpenAI API配置
const OPENAI_API_URL = 'https://api.openai.com/v1/audio/transcriptions';
export const DEFAULT_MODEL = 'gpt-4o-mini-transcribe';
export const WHISPER_MODEL = 'whisper-1';

/**
 * 获取OpenAI API密钥
 * 支持多种获取方式的优先级顺序
 */
const getOpenAIApiKey = async (): Promise<string> => {
  try {
    // 1. 从环境变量获取
    const envApiKey = Constants.expoConfig?.extra?.openaiApiKey;
    if (envApiKey && envApiKey.trim() !== '') {
      log('使用环境变量中的OpenAI API密钥');
      return envApiKey;
    }

    // 2. 从process.env获取（开发环境）
    if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY.trim() !== '') {
      log('使用process.env中的OpenAI API密钥');
      return process.env.OPENAI_API_KEY;
    }

    warn('未找到OpenAI API密钥配置');
    return '';
  } catch (error) {
    logError('获取OpenAI API密钥失败:', error);
    return '';
  }
};

/**
 * 获取音频文件的MIME类型
 */
const getMimeType = (uri: string): string => {
  const extension = uri.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'mp3':
      return 'audio/mpeg';
    case 'wav':
      return 'audio/wav';
    case 'ogg':
      return 'audio/ogg';
    case 'flac':
      return 'audio/flac';
    case 'm4a':
      return 'audio/mp4';
    case 'aac':
      return 'audio/aac';
    case 'webm':
      return 'audio/webm';
    default:
      return 'audio/mp4'; // 默认使用mp4格式
  }
};

/**
 * 计算动态超时时间
 * 根据文件大小和音频类型计算合理的超时时间
 */
const calculateDynamicTimeout = (fileSizeBytes: number, mimeType: string): number => {
  // 基础超时时间（秒）
  const baseTimeout = 30;

  // 根据文件大小计算额外时间（每MB增加10秒）
  const fileSizeMB = fileSizeBytes / (1024 * 1024);
  const sizeBasedTimeout = Math.ceil(fileSizeMB * 10);

  // 根据音频格式调整（压缩格式处理更快）
  let formatMultiplier = 1.0;
  if (mimeType.includes('wav') || mimeType.includes('flac')) {
    formatMultiplier = 1.2; // 无损格式处理时间稍长
  } else if (mimeType.includes('mp3') || mimeType.includes('aac')) {
    formatMultiplier = 0.8; // 压缩格式处理更快
  }

  const totalTimeout = Math.max(
    baseTimeout,
    Math.min((baseTimeout + sizeBasedTimeout) * formatMultiplier, 300) // 最大5分钟
  );

  return totalTimeout * 1000; // 转换为毫秒
};

/**
 * 创建带进度更新的超时Promise
 */
const createTimeoutWithProgress = (
  timeoutMs: number,
  onProgress?: (elapsed: number, total: number) => void
): Promise<never> => {
  return new Promise((_, reject) => {
    const startTime = Date.now();

    // 每2秒更新一次进度
    const progressInterval = setInterval(() => {
      if (onProgress) {
        const elapsed = Date.now() - startTime;
        onProgress(elapsed, timeoutMs);
      }
    }, 2000);

    // 设置超时
    setTimeout(() => {
      clearInterval(progressInterval);
      reject(new Error('请求超时'));
    }, timeoutMs);
  });
};

/**
 * 使用OpenAI API进行音频文件转写
 * @param audioUri 音频文件URI
 * @param language 语言代码，默认为中文
 * @param model 使用的模型，默认gpt-4o-mini，可选whisper-1
 * @param onProgress 进度回调函数
 * @returns 转写后的文本
 */
export const transcribeAudioFile = async (
  audioUri: string,
  language: string = '',
  model: string = DEFAULT_MODEL,
  onProgress?: (message: string) => void
): Promise<string> => {
  try {
    log(`OpenAI转写开始，原始URI: ${audioUri}, 模型: ${model}, 语言: ${language}`);

    // 对于whisper-1模型，检查免费用户的每日使用限制
    if (model === WHISPER_MODEL) {
      log('[WhisperLimit] 检测到whisper-1模型，检查用户每日使用限制');
      
      try {
        // 获取当前用户ID
        const { data: { user } } = await supabase.auth.getUser();
        
        if (user) {
          log('[WhisperLimit] 检查用户使用限制:', user.id);
          
          // 检查并增加使用次数
          const usageResult: WhisperUsageLimitResult = await checkAndIncrementWhisperUsage(user.id);
          
          // 如果不允许使用，抛出错误
          if (!usageResult.canUse) {
            log('[WhisperLimit] 用户已达到每日使用限制');
            
            // 创建一个特殊的错误对象，包含限制信息
            const limitError = new Error(usageResult.message) as any;
            limitError.code = 'WHISPER_DAILY_LIMIT_EXCEEDED';
            limitError.usageInfo = usageResult;
            limitError.isWhisperLimit = true;
            
            log('[WhisperLimit] 准备抛出限制错误:', {
              message: limitError.message,
              isWhisperLimit: limitError.isWhisperLimit,
              code: limitError.code
            });
            
            throw limitError;
          }
          
          log('[WhisperLimit] 使用次数检查通过:', {
            currentUsage: usageResult.currentUsage,
            remaining: usageResult.remaining,
            isVip: usageResult.isVip
          });
          
          // 如果还有剩余次数，在进度回调中显示剩余次数
          if (!usageResult.isVip && usageResult.remaining >= 0) {
            if (onProgress) {
              onProgress(`正在使用Whisper转写（今日还可使用${usageResult.remaining}次）...`);
            }
          }
        } else {
          log('[WhisperLimit] 用户未登录，跳过使用限制检查');
        }
      } catch (error: any) {
        // 如果是我们抛出的限制错误，直接重新抛出
        if (error.isWhisperLimit) {
          log('[WhisperLimit] 内层catch检测到whisper限制错误，重新抛出:', {
            message: error.message,
            isWhisperLimit: error.isWhisperLimit,
            code: error.code
          });
          throw error;
        }
        
        // 其他错误记录日志但不阻止使用，避免因系统错误影响用户体验
        logError('[WhisperLimit] 检查使用限制时出错，允许继续使用:', error);
      }
    }

    // 处理文件URI，确保兼容性 - Android平台优化版本
    let processedUri = audioUri;
    let fileInfo = null;
    let validFilePath = '';

    log('开始检查音频文件:', audioUri);

    // 尝试多种路径格式，特别针对Android平台
    const pathVariants = [
      audioUri,
      audioUri.startsWith('file://') ? audioUri.replace('file://', '') : `file://${audioUri}`,
      audioUri.replace('file://', ''),
      audioUri.startsWith('file://') ? audioUri : `file://${audioUri}`
    ];

    // 逐一尝试路径变体，找到有效的文件路径
    for (const variant of pathVariants) {
      try {
        log(`尝试检查文件路径: ${variant}`);
        const info = await FileSystem.getInfoAsync(variant);
        log(`文件信息: 存在=${info.exists}, URI=${variant}`);
        
        if (info.exists) {
          fileInfo = info;
          validFilePath = variant;
          processedUri = variant;
          log(`找到有效文件路径: ${validFilePath}, 大小: ${info.size} bytes`);
          break;
        }
      } catch (e) {
        log(`路径检查失败: ${variant}`, e);
        continue;
      }
    }

    // 验证文件是否存在且有效
    if (!fileInfo || !fileInfo.exists) {
      throw new Error('音频文件不存在或为空');
    }

    // 类型断言确保size属性存在
    const fileSizeBytes = (fileInfo as any).size || 0;
    if (fileSizeBytes === 0) {
      throw new Error('音频文件为空');
    }
    log(`音频文件验证成功: ${validFilePath}, 大小: ${(fileSizeBytes / 1024 / 1024).toFixed(2)}MB`);

    // 检查文件大小限制（OpenAI限制25MB）
    const maxFileSize = 25 * 1024 * 1024; // 25MB
    if (fileSizeBytes > maxFileSize) {
      throw new Error(`音频文件过大 (${(fileSizeBytes / 1024 / 1024).toFixed(2)}MB)，请使用小于25MB的文件`);
    }

    // 获取文件信息并确保正确的扩展名
    const fileExt = processedUri.split('.').pop()?.toLowerCase() || 'm4a';
    const mimeType = getMimeType(processedUri);
    
    // 确保文件名有正确的扩展名，这对OpenAI API的文件格式识别很重要
    let fileName = `recording.${fileExt}`;
    
    // 针对常见的移动设备录音格式进行优化
    if (fileExt === 'm4a' || mimeType === 'audio/mp4') {
      fileName = `recording.m4a`;  // 明确指定为m4a格式
    } else if (fileExt === 'webm' || mimeType === 'audio/webm') {
      fileName = `recording.webm`;  // Web平台录音格式
    }
    
    log(`文件格式映射: 原始扩展名=${fileExt}, MIME类型=${mimeType}, 最终文件名=${fileName}`);

    // 计算动态超时时间
    const dynamicTimeout = calculateDynamicTimeout(fileSizeBytes, mimeType);

    // 日志记录
    log(`准备调用OpenAI转写API, 音频文件: ${processedUri.substring(0, 30)}..., 模型: ${model}`);
    log(`文件信息: 名称=${fileName}, MIME类型=${mimeType}, 大小=${(fileSizeBytes / 1024 / 1024).toFixed(2)}MB`);
    log(`动态超时设置: ${(dynamicTimeout / 1000).toFixed(1)}秒`);

    // 进度回调
    if (onProgress) {
      onProgress(`正在上传音频文件 (${(fileSizeBytes / 1024 / 1024).toFixed(2)}MB)...`);
    }

    // 准备表单数据，确保使用正确的URI格式
    const formData = new FormData();

    // 确保URI格式正确 - 对于FormData，需要保留file://前缀
    let formUri = processedUri;
    if (!formUri.startsWith('file://') && !formUri.startsWith('http')) {
      formUri = `file://${formUri}`;
    }

    log('添加到FormData的URI:', formUri);

    // 创建文件对象 - React Native FormData格式
    // 确保文件对象包含所有必需的属性
    const fileObject = {
      uri: formUri,
      type: mimeType,
      name: fileName
    } as any;

    log('文件对象详情:', JSON.stringify(fileObject, null, 2));
    log('添加文件到FormData:', { fileName, mimeType, uri: formUri.substring(0, 50) + '...' });

    // 添加文件到表单 - 使用更明确的文件扩展名
    formData.append('file', fileObject);
    
    // 添加其他参数 - 确保model参数是第一个非文件参数
    formData.append('model', model);  // 使用传入的模型参数
    // 使用自动语言检测，不指定language参数让Whisper自动识别
    formData.append('response_format', 'text');
    
    // 验证FormData构建
    log('FormData验证:');
    log('- 文件字段:', fileObject.name, fileObject.type);
    log(`- 模型: ${model}`);
    log('- 语言: 自动检测');
    log('- 响应格式: text');

    // 添加prompt参数，指导模型生成带有标点符号的文本
    // 使用云端管理的语音提示词，支持fallback机制
    try {
      const { SpeechPromptManager } = await import('../utils/speechPromptManager');
      const speechPrompt = await SpeechPromptManager.getSpeechPrompt(language, model);
      log('添加到FormData的prompt:', speechPrompt);
      formData.append('prompt', speechPrompt);
    } catch (error) {
      logError('[SpeechPrompt] 获取语音提示词失败，使用fallback:', error);

      // Fallback提示词
      if (language === 'zh') {
        let fallbackPrompt = '请准确转录语音内容，保持原始语言，添加标点符号。';
        log('使用fallback中文prompt:', fallbackPrompt);
        formData.append('prompt', fallbackPrompt);
      } else {
        const fallbackPrompt = 'Please transcribe accurately, keep original language, add punctuation.';
        log('使用fallback多语言prompt:', fallbackPrompt);
        formData.append('prompt', fallbackPrompt);
      }
    }

    // 获取API密钥（用于fallback）
    const apiKey = await getOpenAIApiKey();

    // 创建AbortController用于请求取消
    const abortController = new AbortController();
    let isRequestCancelled = false;

    // 创建带进度的超时Promise
    const timeoutPromise = createTimeoutWithProgress(dynamicTimeout, (elapsed, total) => {
      const percentage = Math.floor((elapsed / total) * 100);
      const remainingSeconds = Math.ceil((total - elapsed) / 1000);
      if (onProgress) {
        onProgress(i18n.t('home.textDisplay.processingProgress', { percentage, seconds: remainingSeconds }) as string);
      }
    });

    // 修改超时Promise以包含请求取消逻辑
    const enhancedTimeoutPromise: Promise<never> = timeoutPromise.catch((error) => {
      log('请求超时，正在取消API调用...');
      isRequestCancelled = true;
      abortController.abort();
      
      // 等待一小段时间确保取消信号传播，然后抛出错误
      return new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('请求超时'));
        }, 100);
      });
    }) as Promise<never>;

    log('使用统一代理服务调用OpenAI转写API...');
    
    // 通过统一代理服务调用API，传入AbortSignal
    const fetchPromise = callOpenAIAPI(
      '/v1/audio/transcriptions',
      formData,
      apiKey,
      abortController.signal, // 传入AbortSignal
      true // isFormData
    ).then(result => {
      // 检查请求是否已被取消
      if (isRequestCancelled) {
        log('API调用完成但请求已被取消，忽略结果');
        throw new Error('请求已取消');
      }
      return result;
    });

    // 使用Promise.race实现超时控制
    const { response, isProxied } = await Promise.race([fetchPromise, enhancedTimeoutPromise]);

    log(`OpenAI转写API调用${isProxied ? '通过代理' : '直接调用'}完成，状态: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage = '服务器错误';
      try {
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.error?.message || response.statusText;
      } catch (e) {
        errorMessage = errorText || response.statusText;
      }
      logError('OpenAI转写API错误:', errorText);
      throw new Error(`OpenAI API错误: ${errorMessage}`);
    }

    // 获取转写结果
    const transcriptionText = await response.text();

    log(`OpenAI转写成功，结果长度: ${transcriptionText.length}`);
    log(`转写文本预览: ${transcriptionText.substring(0, 50)}...`);

    // 验证结果
    if (!transcriptionText || transcriptionText.trim() === '') {
      warn('OpenAI转写返回空结果');
      throw new Error('转写结果为空，请检查音频内容');
    }

    return transcriptionText.trim();

  } catch (error) {
    logError('OpenAI转写失败:', error);

    // 优先检查是否是whisper限制错误，直接传递
    if ((error as any).isWhisperLimit) {
      log('[OpenAI转写] 最外层catch检测到whisper限制错误，直接重新抛出:', {
        message: (error as any).message,
        isWhisperLimit: (error as any).isWhisperLimit,
        code: (error as any).code
      });
      throw error; // 直接抛出，保留所有自定义属性
    }

    log('[OpenAI转写] 最外层catch处理非whisper限制错误:', {
      message: (error as any).message,
      isWhisperLimit: (error as any).isWhisperLimit || false,
      errorType: typeof error,
      isErrorInstance: error instanceof Error
    });

    // 处理特定错误类型
    if (error instanceof Error) {
      if (error.message.includes('超时') || error.message.includes('timeout')) {
        throw new Error('转写请求超时，请检查网络连接或尝试较短的音频文件');
      } else if (error.message.includes('文件过大')) {
        throw error; // 直接抛出文件过大的错误
      } else if (error.message.includes('密钥') || error.message.includes('API')) {
        throw new Error('API配置错误，请联系管理员');
      } else if (error.message.includes('网络') || error.message.includes('连接')) {
        throw new Error('网络连接失败，请检查网络设置');
      }
    }

    // 默认错误消息
    throw new Error('语音转写失败，请稍后重试');
  }
};

/**
 * 测试动态超时计算功能
 * 用于验证不同文件大小下的超时时间计算
 */
export const testDynamicTimeout = () => {
  log('=== 动态超时计算测试 ===');

  // 测试不同文件大小和格式
  const testCases = [
    { size: 100 * 1024, mimeType: 'audio/mp4', desc: '100KB M4A文件' },
    { size: 500 * 1024, mimeType: 'audio/mp4', desc: '500KB M4A文件' },
    { size: 1 * 1024 * 1024, mimeType: 'audio/mp4', desc: '1MB M4A文件 (约1分钟)' },
    { size: 2 * 1024 * 1024, mimeType: 'audio/mp4', desc: '2MB M4A文件 (约2分钟)' },
    { size: 5 * 1024 * 1024, mimeType: 'audio/mp4', desc: '5MB M4A文件 (约5分钟)' },
    { size: 10 * 1024 * 1024, mimeType: 'audio/wav', desc: '10MB WAV文件 (约1分钟)' },
    { size: 50 * 1024 * 1024, mimeType: 'audio/wav', desc: '50MB WAV文件 (约5分钟)' },
    { size: 2 * 1024 * 1024, mimeType: 'audio/webm', desc: '2MB WebM文件 (约2.5分钟)' },
  ];

  testCases.forEach(({ size, mimeType, desc }) => {
    const timeout = calculateDynamicTimeout(size, mimeType);
    log(`${desc}: ${(timeout / 1000).toFixed(1)}秒超时`);
  });

  log('=== 测试完成 ===');
};

/**
 * 获取模型信息
 * @returns 模型名称和描述
 */
export const getModelInfo = (model: string = DEFAULT_MODEL) => {
  if (model === WHISPER_MODEL) {
    return {
      name: WHISPER_MODEL,
      description: 'OpenAI Whisper 语音转写模型'
    };
  }
  return {
    name: DEFAULT_MODEL,
    description: 'OpenAI GPT-4o mini Transcribe 语音转写模型'
  };
}; 