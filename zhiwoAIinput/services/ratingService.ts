import * as StoreReview from 'expo-store-review';
import * as Linking from 'expo-linking';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { showCenteredToast } from '@/utils/toastConfig';
import { log, error as logError } from '@/services/logService';

/**
 * 评价服务
 * 提供应用内评分和跳转到应用商店评分的功能
 */
export class RatingService {
  // 从环境变量中获取App Store ID
  private static get IOS_APP_ID(): string {
    return Constants.expoConfig?.extra?.iosAppId || '6744967748';
  }
  
  // 从环境变量中获取Android包名
  private static get ANDROID_PACKAGE_NAME(): string {
    return Constants.expoConfig?.extra?.androidPackageName || 'com.mindpowerhk.knowmetype';
  }

  /**
   * 检查是否支持应用内评分
   */
  static async isInAppReviewAvailable(): Promise<boolean> {
    try {
      return await StoreReview.isAvailableAsync();
    } catch (error) {
      logError('检查应用内评分可用性失败:', error);
      return false;
    }
  }

  /**
   * 请求应用内评分
   * 使用系统级评分弹窗，遵循官方指南
   */
  static async requestInAppReview(): Promise<boolean> {
    try {
      // 检查是否支持应用内评分
      const isAvailable = await this.isInAppReviewAvailable();
      
      if (!isAvailable) {
        log('当前设备不支持应用内评分，将跳转到应用商店');
        await this.openStoreForReview();
        return false;
      }

      // 检查是否有评分操作可用
      const hasAction = await StoreReview.hasAction();
      
      if (!hasAction) {
        log('当前无法显示评分弹窗，将跳转到应用商店');
        await this.openStoreForReview();
        return false;
      }

      // 请求应用内评分
      await StoreReview.requestReview();
      log('应用内评分请求已发送');
      return true;
      
    } catch (error) {
      logError('请求应用内评分失败:', error);
      // 如果应用内评分失败，回退到应用商店
      await this.openStoreForReview();
      return false;
    }
  }

  /**
   * 打开应用商店的评分页面
   */
  static async openStoreForReview(): Promise<void> {
    try {
      if (Platform.OS === 'ios') {
        await this.openIOSAppStoreForReview();
      } else if (Platform.OS === 'android') {
        await this.openGooglePlayForReview();
      } else {
        showCenteredToast('当前平台不支持应用商店评分', 'error');
      }
    } catch (error) {
      logError('打开应用商店失败:', error);
      showCenteredToast('打开应用商店失败，请稍后重试', 'error');
    }
  }

  /**
   * 打开iOS App Store的评分页面
   */
  private static async openIOSAppStoreForReview(): Promise<void> {
    const appStoreUrl = `https://apps.apple.com/app/id${this.IOS_APP_ID}?action=write-review`;
    const appStoreDirectUrl = `itms-apps://itunes.apple.com/app/viewContentsUserReviews/id${this.IOS_APP_ID}?action=write-review`;
    
    try {
      // 首先尝试直接打开App Store应用
      const canOpenDirect = await Linking.canOpenURL(appStoreDirectUrl);
      if (canOpenDirect) {
        await Linking.openURL(appStoreDirectUrl);
      } else {
        // 如果无法直接打开，则在浏览器中打开
        await Linking.openURL(appStoreUrl);
      }
    } catch (error) {
      logError('打开iOS App Store失败:', error);
      // 最后的回退方案
      await Linking.openURL(appStoreUrl);
    }
  }

  /**
   * 打开Google Play的评分页面
   */
  private static async openGooglePlayForReview(): Promise<void> {
    const playStoreUrl = `https://play.google.com/store/apps/details?id=${this.ANDROID_PACKAGE_NAME}&showAllReviews=true`;
    const playStoreDirectUrl = `market://details?id=${this.ANDROID_PACKAGE_NAME}&showAllReviews=true`;
    
    try {
      // 首先尝试直接打开Google Play应用
      const canOpenDirect = await Linking.canOpenURL(playStoreDirectUrl);
      if (canOpenDirect) {
        await Linking.openURL(playStoreDirectUrl);
      } else {
        // 如果无法直接打开，则在浏览器中打开
        await Linking.openURL(playStoreUrl);
      }
    } catch (error) {
      logError('打开Google Play失败:', error);
      // 最后的回退方案
      await Linking.openURL(playStoreUrl);
    }
  }

  /**
   * 获取应用商店URL
   */
  static getStoreUrl(): string | null {
    try {
      return StoreReview.storeUrl();
    } catch (error) {
      logError('获取应用商店URL失败:', error);
      return null;
    }
  }

  /**
   * 智能评分请求
   * 根据用户使用情况和设备支持情况选择最佳的评分方式
   */
  static async smartRatingRequest(): Promise<void> {
    try {
      // 首先尝试应用内评分
      const success = await this.requestInAppReview();
      
      if (!success) {
        log('应用内评分不可用，已回退到应用商店评分');
      }
    } catch (error) {
      logError('智能评分请求失败:', error);
      showCenteredToast('评分功能暂时不可用，请稍后重试', 'error');
    }
  }
} 