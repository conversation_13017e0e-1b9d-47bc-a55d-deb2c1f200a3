import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';
const Platform = require('react-native').Platform;
import { log, warn, error as logError, debug, info } from '@/services/logService';

// 从Constants或环境变量中获取Supabase配置
// 由于环境变量在React Native中的处理比较特殊，我们优先从Constants中获取
const supabaseUrl = Constants.expoConfig?.extra?.supabaseUrl;
const supabaseAnonKey = Constants.expoConfig?.extra?.supabaseAnonKey;

// 从Constants.expoConfig.extra(通过app.config.ts配置)获取环境变量
// 这些变量应该在.env文件中定义，然后在app.config.ts中通过process.env引入
const useLocalSupabase = Constants.expoConfig?.extra?.useLocalSupabase === 'true';
const LOCAL_IP = Constants.expoConfig?.extra?.localIp || '*************';

// 开发环境标志 - 在组件中可以导入此变量来判断是否显示开发者功能
// 使用多重检测策略确保在各种情况下都能正确识别开发环境
export const IS_DEV_ENV = Constants.expoConfig?.extra?.isDevEnv === 'true' ||
  Constants.expoConfig?.extra?.isDevEnv === true ||
  Constants.expoConfig?.extra?.appEnv === 'development' ||
  __DEV__;

export const SHOW_DEV_LOGIN = Constants.expoConfig?.extra?.showDevLogin === 'true' ||
  Constants.expoConfig?.extra?.showDevLogin === true ||
  IS_DEV_ENV;

// 日志打印开发环境状态
debug('[Supabase] 开发环境状态:', {
  isDevEnv: IS_DEV_ENV,
  showDevLogin: SHOW_DEV_LOGIN,
  rawIsDevEnv: Constants.expoConfig?.extra?.isDevEnv,
  rawShowDevLogin: Constants.expoConfig?.extra?.showDevLogin,
  isDev: __DEV__
});

// 根据设备平台选择适当的URL
const getLocalUrl = () => {
  const isAndroid = Platform.OS === 'android';
  const isIOS = Platform.OS === 'ios';

  // 在开发环境中打印更多信息
  debug('[Supabase] 平台信息:', {
    platform: Platform.OS,
    isAndroid,
    isIOS,
    version: Platform.Version,
  });

  // 直接使用本机IP
  const url = `http://${LOCAL_IP}:54321`;
  log('[Supabase] 使用本地服务 URL:', url);
  return url;
};

// 获取 Supabase URL 和密钥
const localSupabaseUrl = getLocalUrl();

// 确定使用的配置
const finalUrl = useLocalSupabase ? localSupabaseUrl : (supabaseUrl || 'https://yenwfmoubflrluhqsyim.supabase.co');
const finalKey = supabaseAnonKey || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

info('[Supabase] 连接配置:', {
  useLocalSupabase,
  finalUrl,
  keyPrefix: finalKey.substring(0, 10) + '...',
  platform: Platform.OS,
  isDevEnv: IS_DEV_ENV,
});

// 为本地环境使用特定的存储键
const storageKey = useLocalSupabase ? 'sb-local-auth-token' : 'sb-supabase-auth-token';
log('[Supabase] 使用存储键:', storageKey);

// 使用原生的 setTimeout 实现超时功能
const fetchWithTimeout = (url: RequestInfo | URL, options: any = {}, timeout: number = 30000) => {
  log(`[Supabase] 请求 ${url}，超时设置为 ${timeout}ms`);

  // 根据请求URL动态调整超时时间
  let adjustedTimeout = timeout;
  const urlString = url.toString();

  // 为AI服务相关的数据库查询设置更短的超时时间
  if (urlString.includes('/user_model_settings') ||
    urlString.includes('/profiles') ||
    urlString.includes('/auth/session')) {
    adjustedTimeout = Math.min(timeout, 3000); // 最多3秒
    log(`[Supabase] AI服务相关请求，调整超时为 ${adjustedTimeout}ms`);
  }

  const fetchWithRetry = async (retries = 2): Promise<Response> => { // 减少重试次数
    try {
      return await new Promise<Response>((resolve, reject) => {
        const timer = setTimeout(() => {
          log(`[Supabase] 请求超时: ${url}`);
          reject(new Error('请求超时'));
        }, adjustedTimeout);

        fetch(url, options)
          .then(response => {
            clearTimeout(timer);
            resolve(response);
          })
          .catch(error => {
            clearTimeout(timer);
            log(`[Supabase] 请求错误: ${error.message}`);
            reject(error);
          });
      });
    } catch (error) {
      if (retries > 0) {
        log(`[Supabase] 重试请求 (剩余${retries}次)...`);
        await new Promise(resolve => setTimeout(resolve, 500)); // 减少重试间隔
        return fetchWithRetry(retries - 1);
      }
      throw error;
    }
  };

  return fetchWithRetry();
};

// 创建Supabase客户端
export const supabase = createClient(finalUrl, finalKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false, // 禁用自动URL检测，手动处理
    flowType: 'implicit', // 改为隐式流程，避免PKCE问题
    debug: false, // 关闭调试模式以减少日志干扰
    storageKey: storageKey, // 使用特定的存储键避免会话冲突
  },
  global: {
    fetch: (...args) => {
      log('[Supabase] 发起请求:', args[0]);

      // 根据请求类型设置不同的超时时间
      const url = args[0]?.toString() || '';
      let timeout = 30000; // 默认30秒

      // 为AI服务相关的请求设置更短的超时时间
      if (url.includes('/user_model_settings') ||
        url.includes('/profiles') ||
        url.includes('/auth/session') ||
        url.includes('/rest/v1/')) {
        timeout = 5000; // AI服务相关请求5秒超时
        log('[Supabase] AI服务相关请求，使用5秒超时');
      }

      // 使用自定义的带超时功能的 fetch
      return fetchWithTimeout(args[0], args[1], timeout);
    }
  }
});

// 获取应用的重定向URL，用于OAuth
export const getRedirectUrl = (): string => {
  const scheme = Constants.expoConfig?.scheme || 'knowmetype';
  return `${scheme}://`;
};

// 用户认证相关服务
export const authService = {
  // 用户注册
  async signUp(email: string, password: string) {
    return await supabase.auth.signUp({
      email,
      password,
    });
  },

  // 用户登录
  async signIn(email: string, password: string) {
    try {
      // 先清除现有会话
      log('[Auth] 清除现有会话...');
      await this.signOut();

      // 等待一小段时间确保会话完全清除
      await new Promise(resolve => setTimeout(resolve, 1000));

      log('[Auth] 开始登录...');
      return await supabase.auth.signInWithPassword({
        email,
        password,
      });
    } catch (error) {
      logError('[Auth] 登录错误:', error);
      throw error;
    }
  },

  // OAuth登录
  async signInWithOAuth(provider: 'google' | 'apple' | 'facebook' | { provider: 'google' | 'apple' | 'facebook', options?: { redirectTo?: string, skipBrowserRedirect?: boolean } }) {
    const redirectUrl = getRedirectUrl();
    log(`[OAuth] 尝试 ${typeof provider === 'string' ? provider : provider.provider} 登录，重定向URL: ${redirectUrl}`);

    // 检查是否在本地环境
    if (useLocalSupabase) {
      log('[OAuth] 当前使用本地Supabase环境，OAuth可能无法正常工作');
      log('[OAuth] 建议使用邮箱/密码登录进行本地测试');

      // 提示用户创建测试账号
      if (typeof provider === 'string' && provider === 'apple') {
        log('[OAuth] 在本地环境中Apple登录通常不可用，尝试模拟Apple登录...');
        // 如果是本地环境，尝试使用一个测试用户登录
        return this.signIn('<EMAIL>', 'password123');
      }
    }

    try {
      if (typeof provider === 'string') {
        return await supabase.auth.signInWithOAuth({
          provider,
          options: {
            redirectTo: redirectUrl,
            skipBrowserRedirect: true // 手动处理重定向
          }
        });
      } else {
        // 如果provider是对象，需要合并options
        const providerOptions = provider.options || {};
        return await supabase.auth.signInWithOAuth({
          provider: provider.provider,
          options: {
            ...providerOptions,
            redirectTo: providerOptions.redirectTo || redirectUrl,
            skipBrowserRedirect: true // 手动处理重定向
          }
        });
      }
    } catch (error) {
      logError('[OAuth] 登录错误:', error);
      throw error;
    }
  },

  // 使用Apple ID Token登录
  async signInWithApple(idToken: string, nonce?: string) {
    log('使用Apple ID Token登录');
    log(`Token长度: ${idToken.length}, Nonce: ${nonce || '无'}`);

    // 检查是否在本地环境
    if (useLocalSupabase) {
      log('[Apple登录] 当前使用本地Supabase环境，Apple登录可能不可用');
      log('[Apple登录] 尝试使用测试账号登录...');

      // 本地环境使用测试账号
      return this.signIn('<EMAIL>', 'password123');
    }

    try {
      // 使用Supabase的signInWithIdToken方法
      return await supabase.auth.signInWithIdToken({
        provider: 'apple',
        token: idToken,
        ...(nonce ? { nonce } : {}) // 只有在提供nonce时才传递参数
      });
    } catch (error) {
      logError('Apple登录错误:', error);
      throw error;
    }
  },

  // 登出
  async signOut() {
    return await supabase.auth.signOut();
  },

  // 获取当前用户
  async getCurrentUser() {
    return await supabase.auth.getUser();
  },

  // 获取会话
  async getSession() {
    return await supabase.auth.getSession();
  },

  // 重置密码
  async resetPassword(email: string) {
    return await supabase.auth.resetPasswordForEmail(email);
  },

  // 更新密码
  async updatePassword(password: string) {
    return await supabase.auth.updateUser({ password });
  },
};

// 用户数据服务
export const userService = {
  // 获取用户资料
  async getUserProfile(userId: string) {
    return await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
  },

  // 更新用户资料
  async updateUserProfile(userId: string, data: any) {
    return await supabase
      .from('profiles')
      .update(data)
      .eq('id', userId);
  },

  // 创建用户资料
  async createUserProfile(data: any) {
    return await supabase
      .from('profiles')
      .insert(data);
  },
};

// 订阅服务
export const subscriptionService = {
  // 获取所有可用的订阅计划
  async getSubscriptionPlans() {
    return await supabase
      .from('subscription_plans')
      .select('*')
      .order('price');
  },

  // 获取用户当前订阅
  async getUserSubscription(userId: string) {
    return await supabase
      .from('subscriptions')
      .select('*, subscription_plans(*)')
      .eq('user_id', userId)
      .eq('status', 'active')
      .maybeSingle();
  },

  // 检查用户是否为VIP
  async isUserVIP(userId: string) {
    const { data, error } = await this.getUserSubscription(userId);
    if (error) {
      logError('Error checking VIP status:', error);
      return false;
    }
    return !!data;
  },

  // 创建新订阅记录
  async createSubscription(subscriptionData: any) {
    return await supabase
      .from('subscriptions')
      .insert(subscriptionData);
  },

  // 更新订阅
  async updateSubscription(subscriptionId: string, updateData: any) {
    return await supabase
      .from('subscriptions')
      .update(updateData)
      .eq('id', subscriptionId);
  },
};

// 支付服务
export const paymentService = {
  // 获取支付历史
  async getPaymentHistory(userId: string) {
    return await supabase
      .from('payments')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
  },

  // 创建支付记录
  async createPaymentRecord(paymentData: any) {
    return await supabase
      .from('payments')
      .insert(paymentData);
  },

  // 更新支付记录
  async updatePaymentRecord(paymentId: string, updateData: any) {
    return await supabase
      .from('payments')
      .update(updateData)
      .eq('id', paymentId);
  },
};

// 使用量和限制服务
export const usageService = {
  // 获取用户使用量
  async getUserUsage(userId: string) {
    return await supabase
      .from('usage')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();
  },

  // 更新用户使用量
  async updateUserUsage(userId: string, usageData: any) {
    const { data } = await this.getUserUsage(userId);

    if (data) {
      // 如果已有记录，更新
      return await supabase
        .from('usage')
        .update(usageData)
        .eq('user_id', userId);
    } else {
      // 如果没有记录，创建新记录
      return await supabase
        .from('usage')
        .insert({ user_id: userId, ...usageData });
    }
  },

  // 增加特定类型的使用量
  async incrementUsage(userId: string, usageType: string, amount: number = 1) {
    const { data } = await this.getUserUsage(userId);

    if (data) {
      const currentUsage = data[usageType] || 0;
      const updateData = { [usageType]: currentUsage + amount };

      return await supabase
        .from('usage')
        .update(updateData)
        .eq('user_id', userId);
    } else {
      // 如果没有记录，创建新记录
      return await supabase
        .from('usage')
        .insert({
          user_id: userId,
          [usageType]: amount
        });
    }
  },

  // 检查用户是否超出使用限制
  async checkUserLimit(userId: string, usageType: string) {
    // 先获取用户订阅计划
    const { data: subscription } = await subscriptionService.getUserSubscription(userId);

    if (!subscription) {
      // 如果没有订阅，使用免费计划限制
      const { data: freePlan } = await supabase
        .from('subscription_limits')
        .select('*')
        .eq('plan_id', 'free')
        .single();

      // 获取用户当前使用量
      const { data: usage } = await this.getUserUsage(userId);

      if (freePlan && usage) {
        return {
          isLimited: usage[usageType] >= freePlan[usageType + '_limit'],
          current: usage[usageType] || 0,
          limit: freePlan[usageType + '_limit']
        };
      }
    } else {
      // 如果有订阅，使用订阅计划限制
      const { data: planLimits } = await supabase
        .from('subscription_limits')
        .select('*')
        .eq('plan_id', subscription.subscription_plans.id)
        .single();

      // 获取用户当前使用量
      const { data: usage } = await this.getUserUsage(userId);

      if (planLimits && usage) {
        return {
          isLimited: usage[usageType] >= planLimits[usageType + '_limit'],
          current: usage[usageType] || 0,
          limit: planLimits[usageType + '_limit']
        };
      }
    }

    // 默认不限制
    return { isLimited: false, current: 0, limit: 0 };
  }
};

// AI模型相关服务
export const modelService = {
  // 获取所有可用的AI模型列表
  async getModels() {
    try {
      const { data, error } = await supabase
        .from('ai_models')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) {
        logError('[模型服务] 获取模型列表失败:', error);
        throw error;
      }

      return { data, error: null };
    } catch (error) {
      logError('[模型服务] 获取模型列表错误:', error);
      return { data: null, error };
    }
  },

  // 获取特定类型的模型列表
  async getModelsByType(modelType: 'text' | 'voice') {
    try {
      const { data, error } = await supabase
        .from('ai_models')
        .select('*')
        .eq('is_active', true)
        .eq('model_type', modelType)
        .order('sort_order', { ascending: true });

      if (error) {
        logError(`[模型服务] 获取${modelType}模型列表失败:`, error);
        throw error;
      }

      return { data, error: null };
    } catch (error) {
      logError(`[模型服务] 获取${modelType}模型列表错误:`, error);
      return { data: null, error };
    }
  },

  // 获取用户的模型设置
  async getUserModelSettings(userId: string) {
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        // 如果是没有找到行的错误，返回默认设置而不是错误
        if (error.code === 'PGRST116') {
          log('[模型服务] 用户还没有模型设置，将创建默认设置');

          // 创建用户默认设置
          const defaultSettings = {
            user_id: userId,
            default_text_model: 'qwen2.5-7b', // 默认文本模型
            default_voice_model: 'native',    // 默认语音转写模型
            language: 'zh-CN',                // 默认语言
            theme: 'light',                   // 默认主题
            sync_enabled: true,               // 默认同步开启
            fast_mode: false,                 // 默认关闭极速模式
            style_optimization_enabled: true  // 默认开启风格模板优化
          };

          // 尝试在数据库中创建默认设置
          try {
            const { error: insertError } = await supabase
              .from('user_settings')
              .insert(defaultSettings);

            if (insertError) {
              warn('[模型服务] 创建默认设置失败:', insertError);
            } else {
              log('[模型服务] 成功创建用户默认设置');
            }
          } catch (insertErr) {
            logError('[模型服务] 创建默认设置出错:', insertErr);
          }

          return {
            data: defaultSettings,
            error: null
          };
        }

        logError('[模型服务] 获取用户模型设置失败:', error);
        throw error;
      }

      // 确保返回的数据有默认值（以防数据库中的记录有空值）
      const settingsWithDefaults = {
        ...data,
        default_text_model: data.default_text_model || 'qwen2.5-7b',
        default_voice_model: data.default_voice_model || 'native'
      };

      return { data: settingsWithDefaults, error: null };
    } catch (error) {
      logError('[模型服务] 获取用户模型设置错误:', error);
      return { data: null, error };
    }
  },

  // 保存用户的模型设置
  async saveUserModelSettings(userId: string, settings: { default_text_model?: string, default_voice_model?: string, fast_mode?: boolean, style_optimization_enabled?: boolean }) {
    try {
      log('[模型服务] 准备保存用户模型设置:', { userId, settings });

      // 检查用户设置是否已存在
      const { data: existingSettings, error: getError } = await this.getUserModelSettings(userId);
      log('[模型服务] 现有设置:', existingSettings);

      if (getError) {
        logError('[模型服务] 获取现有设置失败:', getError);
      }

      // 验证用户访问权限
      log('[模型服务] 验证用户身份和权限...');
      const { data: sessionData } = await supabase.auth.getSession();
      if (!sessionData?.session) {
        logError('[模型服务] 用户未登录，无法保存设置');
        return { data: null, error: new Error('用户未登录') };
      }

      log('[模型服务] 当前用户身份:', {
        用户ID: sessionData.session.user.id,
        目标用户ID: userId,
        匹配: sessionData.session.user.id === userId
      });

      let result;
      if (existingSettings) {
        // 更新现有设置
        log('[模型服务] 更新现有设置');

        // 合并现有设置和新设置
        const updatedSettings = {
          ...existingSettings,
          ...settings,
          updated_at: new Date().toISOString()
        };

        // 重要：使用明确的字段列表而不是整个对象
        const updateData = {
          default_text_model: updatedSettings.default_text_model,
          default_voice_model: updatedSettings.default_voice_model,
          fast_mode: updatedSettings.fast_mode,
          style_optimization_enabled: updatedSettings.style_optimization_enabled,
          updated_at: updatedSettings.updated_at
        };

        log('[模型服务] 准备更新的设置:', updateData);

        // 使用明确的字段列表进行更新
        result = await supabase
          .from('user_settings')
          .update(updateData)
          .eq('user_id', userId);

        if (result.error) {
          logError('[模型服务] 更新用户模型设置失败:', result.error);
          throw result.error;
        }

        log('[模型服务] 更新成功，状态:', result);

        // 立即重新查询验证更新
        try {
          const { data: verifyData, error: verifyError } = await supabase
            .from('user_settings')
            .select('*')
            .eq('user_id', userId)
            .single();

          if (verifyError) {
            logError('[模型服务] 验证更新失败:', verifyError);
          } else {
            log('[模型服务] 验证更新结果:', verifyData);
            return { data: verifyData, error: null };
          }
        } catch (verifyErr) {
          logError('[模型服务] 验证更新出错:', verifyErr);
        }

        // 直接返回合并后的设置
        log('[模型服务] 更新后的设置 (本地合并):', updatedSettings);
        return { data: updatedSettings, error: null };
      } else {
        // 创建新设置
        log('[模型服务] 创建新设置');

        // 准备新设置数据
        const newSettings = {
          user_id: userId,
          default_text_model: settings.default_text_model || 'qwen2.5-7b',
          default_voice_model: settings.default_voice_model || 'native',
          fast_mode: settings.fast_mode || false,
          style_optimization_enabled: settings.style_optimization_enabled !== undefined ? settings.style_optimization_enabled : true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        log('[模型服务] 准备创建的完整设置:', newSettings);

        result = await supabase
          .from('user_settings')
          .insert(newSettings);

        if (result.error) {
          logError('[模型服务] 创建用户模型设置失败:', result.error);
          throw result.error;
        }

        log('[模型服务] 创建成功，状态:', result);
        log('[模型服务] 新创建的设置:', newSettings);

        // 立即查询验证是否创建成功
        try {
          const { data: verifyData, error: verifyError } = await supabase
            .from('user_settings')
            .select('*')
            .eq('user_id', userId)
            .single();

          if (verifyError) {
            logError('[模型服务] 验证创建失败:', verifyError);
          } else {
            log('[模型服务] 验证创建结果:', verifyData);
            return { data: verifyData, error: null };
          }
        } catch (verifyErr) {
          logError('[模型服务] 验证创建出错:', verifyErr);
        }

        return { data: newSettings, error: null };
      }
    } catch (error) {
      logError('[模型服务] 保存用户模型设置错误:', error);
      return { data: null, error };
    }
  }
};

// 系统提示词服务
const systemPromptService = {
  /**
   * 获取系统提示词
   */
  getSystemPrompt: async (promptId: string = 'default_system_prompt') => {
    try {
      const { data, error } = await supabase
        .from('system_prompts')
        .select('*')
        .eq('id', promptId)
        .eq('is_active', true)
        .single();

      if (error) {
        logError('获取系统提示词失败:', error);
        return null;
      }

      return data;
    } catch (error) {
      logError('获取系统提示词异常:', error);
      return null;
    }
  },

  /**
   * 更新系统提示词版本（管理员功能）
   */
  updateSystemPrompt: async (promptId: string, content: string, version: number) => {
    try {
      const { data, error } = await supabase
        .from('system_prompts')
        .update({
          prompt_content: content,
          version: version,
          updated_at: new Date().toISOString()
        })
        .eq('id', promptId)
        .select();

      if (error) {
        logError('更新系统提示词失败:', error);
        return null;
      }

      return data;
    } catch (error) {
      logError('更新系统提示词异常:', error);
      return null;
    }
  }
};

export default {
  supabase,
  authService,
  userService,
  subscriptionService,
  paymentService,
  usageService,
  modelService,
  systemPromptService
};

// 导出便捷函数
export const getCurrentUser = () => authService.getCurrentUser();
export const signOut = () => authService.signOut();
export const getVipStatus = async (userId: string) => {
  return await subscriptionService.isUserVIP(userId);
};
export const getUserSubscriptionInfo = (userId: string) => subscriptionService.getUserSubscription(userId);

// 语音提示词服务
const speechPromptService = {
  /**
   * 获取语音提示词
   */
  getSpeechPrompt: async (promptId: string) => {
    try {
      const { data, error } = await supabase
        .from('speech_prompts')
        .select('*')
        .eq('id', promptId)
        .single();

      if (error) {
        logError('获取语音提示词失败:', error);
        return null;
      }

      return data;
    } catch (error) {
      logError('获取语音提示词异常:', error);
      return null;
    }
  },

  /**
   * 根据类型和模型获取语音提示词
   */
  getSpeechPromptByType: async (promptType: string, modelType: string) => {
    try {
      const { data, error } = await supabase
        .from('speech_prompts')
        .select('*')
        .eq('prompt_type', promptType)
        .eq('model_type', modelType)
        .single();

      if (error) {
        logError('根据类型获取语音提示词失败:', error);
        return null;
      }

      return data;
    } catch (error) {
      logError('根据类型获取语音提示词异常:', error);
      return null;
    }
  },

  /**
   * 更新语音提示词（管理员功能）
   */
  updateSpeechPrompt: async (promptId: string, content: string, version: number) => {
    try {
      const { data, error } = await supabase
        .from('speech_prompts')
        .update({
          prompt_content: content,
          version: version,
          updated_at: new Date().toISOString()
        })
        .eq('id', promptId)
        .select();

      if (error) {
        logError('更新语音提示词失败:', error);
        return null;
      }

      return data;
    } catch (error) {
      logError('更新语音提示词异常:', error);
      return null;
    }
  }
};

// 导出新增的系统提示词服务
export {
  systemPromptService,
  speechPromptService
}; 