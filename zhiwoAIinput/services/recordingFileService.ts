/**
 * 录音文件管理服务
 * 处理按用户ID隔离的录音文件存储、路径管理、文件迁移等功能
 */
import * as FileSystem from 'expo-file-system';
import { nanoid } from 'nanoid/non-secure';
import { log, error as logError, warn } from '@/services/logService';
import { userIdService } from './userIdService';
import { sqliteService } from './sqliteService';

/**
 * 录音文件管理服务类
 */
class RecordingFileService {
  /**
   * 获取用户录音文件目录
   * @param userId 用户ID
   * @returns 用户录音文件目录路径
   */
  async getUserRecordingsDirectory(userId: string): Promise<string> {
    const baseDir = FileSystem.documentDirectory + 'recordings/';
    const userDir = baseDir + userId + '/';
    
    try {
      // 确保目录存在
      const dirInfo = await FileSystem.getInfoAsync(userDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(userDir, { intermediates: true });
        log(`RecordingFileService: 创建用户录音目录 ${userDir}`);
      }
      
      return userDir;
    } catch (error) {
      logError(`RecordingFileService: 创建用户录音目录失败 (userId: ${userId}):`, error);
      throw error;
    }
  }

  /**
   * 生成录音文件名
   * @returns 唯一的录音文件名
   */
  generateRecordingFilename(): string {
    return `recording_${nanoid()}.m4a`;
  }

  /**
   * 获取录音文件保存路径
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 录音文件完整路径
   */
  async getRecordingFilePath(userId?: string): Promise<string> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      const userDir = await this.getUserRecordingsDirectory(currentUserId);
      const filename = this.generateRecordingFilename();
      const fullPath = userDir + filename;
      
      log(`RecordingFileService: 生成录音文件路径 ${fullPath}`);
      return fullPath;
    } catch (error) {
      logError('RecordingFileService: 获取录音文件路径失败:', error);
      throw error;
    }
  }

  /**
   * 保存录音文件记录到数据库
   * @param filePath 文件完整路径
   * @param userId 用户ID（可选，默认使用当前用户）
   * @param fileSize 文件大小（可选）
   * @param duration 录音时长（可选）
   * @returns 文件记录ID
   */
  async saveRecordingFileRecord(
    filePath: string, 
    userId?: string, 
    fileSize?: number, 
    duration?: number
  ): Promise<string> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      const relativePath = this.extractRelativePath(filePath);
      const fileName = this.extractFileName(filePath);
      const recordId = nanoid();

      await sqliteService.saveRecordingFile(currentUserId, {
        id: recordId,
        file_path: filePath,
        relative_path: relativePath,
        file_name: fileName,
        file_size: fileSize,
        duration: duration,
      });

      log(`RecordingFileService: 保存录音文件记录 ${recordId} (path: ${filePath})`);
      return recordId;
    } catch (error) {
      logError(`RecordingFileService: 保存录音文件记录失败 (path: ${filePath}):`, error);
      throw error;
    }
  }

  /**
   * 解析原始路径，提取相对路径部分（从 Documents/ 开始）
   * @param originPath 原始路径
   * @returns 相对路径部分
   */
  parseOriginPath(originPath: string): string {
    if (!originPath) return '';

    // 移除 file:// 前缀
    let path = originPath.replace(/^file:\/\//, '');

    // 查找 Documents/ 部分，这是沙盒中的相对路径起点
    const documentsIndex = path.indexOf('Documents/');
    if (documentsIndex !== -1) {
      return path.substring(documentsIndex);
    }

    // 如果没找到 Documents/，查找 recordings/ 部分
    const recordingsIndex = path.indexOf('recordings/');
    if (recordingsIndex !== -1) {
      return 'Documents/' + path.substring(recordingsIndex);
    }

    // 如果都没找到，返回默认的录音路径格式
    const fileName = this.extractFileName(path);
    return `Documents/recordings/${fileName}`;
  }

  /**
   * 提取相对路径（从完整路径中提取 recordings/ 之后的部分）
   * @param fullPath 完整路径
   * @returns 相对路径
   */
  private extractRelativePath(fullPath: string): string {
    const recordingsIndex = fullPath.indexOf('recordings/');
    if (recordingsIndex !== -1) {
      return fullPath.substring(recordingsIndex);
    }
    return fullPath;
  }

  /**
   * 提取文件名
   * @param path 文件路径
   * @returns 文件名
   */
  private extractFileName(path: string): string {
    return path.split('/').pop() || '';
  }

  /**
   * 根据相对路径重建完整路径（基于当前沙盒的 FileSystem.documentDirectory）
   * @param relativePath 相对路径（从 Documents/ 开始）
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 重建的完整路径
   */
  async rebuildFullPath(relativePath: string, userId?: string): Promise<string> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();

      // 获取当前沙盒的 Documents 目录
      const currentDocumentsDir = FileSystem.documentDirectory || '';

      // 如果相对路径以 Documents/ 开头，直接拼接当前沙盒路径
      if (relativePath.startsWith('Documents/')) {
        const pathWithoutDocuments = relativePath.replace('Documents/', '');

        // 检查是否包含当前用户ID
        if (pathWithoutDocuments.includes(`recordings/${currentUserId}/`)) {
          // 路径已经正确，直接拼接
          return currentDocumentsDir + pathWithoutDocuments;
        } else if (pathWithoutDocuments.startsWith('recordings/')) {
          // 包含旧用户ID或其他格式，需要重建到当前用户目录
          const pathAfterRecordings = pathWithoutDocuments.replace('recordings/', '');
          const pathParts = pathAfterRecordings.split('/');

          if (pathParts.length >= 2) {
            // 提取实际的文件名（最后一部分）
            const fileName = pathParts[pathParts.length - 1];
            return currentDocumentsDir + `recordings/${currentUserId}/${fileName}`;
          } else {
            // 直接是文件名的情况
            return currentDocumentsDir + `recordings/${currentUserId}/${pathAfterRecordings}`;
          }
        }
      }

      // 如果相对路径直接以 recordings/ 开头（兼容旧格式）
      if (relativePath.startsWith('recordings/')) {
        const pathAfterRecordings = relativePath.replace('recordings/', '');
        const pathParts = pathAfterRecordings.split('/');

        if (pathParts.length >= 2) {
          // 提取实际的文件名（最后一部分）
          const fileName = pathParts[pathParts.length - 1];
          return currentDocumentsDir + `recordings/${currentUserId}/${fileName}`;
        } else {
          // 直接是文件名的情况
          return currentDocumentsDir + `recordings/${currentUserId}/${pathAfterRecordings}`;
        }
      }
      
      // 其他情况，直接拼接到用户目录
      const userDir = await this.getUserRecordingsDirectory(currentUserId);
      return userDir + this.extractFileName(relativePath);
    } catch (error) {
      logError(`RecordingFileService: 重建完整路径失败 (relativePath: ${relativePath}):`, error);
      throw error;
    }
  }

  /**
   * 修复录音文件路径（用于重装后的路径恢复）
   * @param originalPath 原始路径
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 修复后的路径
   */
  async fixRecordingPath(originalPath: string, userId?: string): Promise<string> {
    try {
      const relativePath = this.parseOriginPath(originalPath);
      const fixedPath = await this.rebuildFullPath(relativePath, userId);
      
      // 检查文件是否存在
      const fileInfo = await FileSystem.getInfoAsync(fixedPath);
      if (fileInfo.exists) {
        log(`RecordingFileService: 路径修复成功 ${originalPath} -> ${fixedPath}`);
        return fixedPath;
      } else {
        warn(`RecordingFileService: 修复后的文件不存在 ${fixedPath}`);
        return fixedPath; // 即使文件不存在也返回修复后的路径
      }
    } catch (error) {
      logError(`RecordingFileService: 修复录音路径失败 (originalPath: ${originalPath}):`, error);
      return originalPath; // 修复失败时返回原始路径
    }
  }

  /**
   * 迁移录音文件从一个用户目录到另一个用户目录
   * @param fromUserId 源用户ID
   * @param toUserId 目标用户ID
   * @returns 迁移的文件数量
   */
  async migrateRecordingFiles(fromUserId: string, toUserId: string): Promise<number> {
    try {
      log(`RecordingFileService: 开始迁移录音文件 ${fromUserId} -> ${toUserId}`);
      
      const fromDir = await this.getUserRecordingsDirectory(fromUserId);
      const toDir = await this.getUserRecordingsDirectory(toUserId);
      
      // 检查源目录是否存在
      const fromDirInfo = await FileSystem.getInfoAsync(fromDir);
      if (!fromDirInfo.exists) {
        log(`RecordingFileService: 源目录不存在 ${fromDir}`);
        return 0;
      }
      
      // 获取源目录中的所有文件
      const files = await FileSystem.readDirectoryAsync(fromDir);
      let migratedCount = 0;
      
      for (const fileName of files) {
        try {
          const fromPath = fromDir + fileName;
          const toPath = toDir + fileName;
          
          // 检查目标文件是否已存在
          const toFileInfo = await FileSystem.getInfoAsync(toPath);
          if (toFileInfo.exists) {
            warn(`RecordingFileService: 目标文件已存在，跳过 ${toPath}`);
            continue;
          }
          
          // 移动文件
          await FileSystem.moveAsync({
            from: fromPath,
            to: toPath,
          });
          
          migratedCount++;
          log(`RecordingFileService: 迁移文件成功 ${fromPath} -> ${toPath}`);
        } catch (fileError) {
          logError(`RecordingFileService: 迁移单个文件失败 ${fileName}:`, fileError);
        }
      }
      
      // 如果源目录为空，删除它
      try {
        const remainingFiles = await FileSystem.readDirectoryAsync(fromDir);
        if (remainingFiles.length === 0) {
          await FileSystem.deleteAsync(fromDir);
          log(`RecordingFileService: 删除空的源目录 ${fromDir}`);
        }
      } catch (cleanupError) {
        warn(`RecordingFileService: 清理源目录失败:`, cleanupError);
      }
      
      log(`RecordingFileService: 录音文件迁移完成，共迁移 ${migratedCount} 个文件`);
      return migratedCount;
    } catch (error) {
      logError(`RecordingFileService: 迁移录音文件失败 (${fromUserId} -> ${toUserId}):`, error);
      throw error;
    }
  }

  /**
   * 获取用户录音文件列表
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 录音文件列表
   */
  async getUserRecordingFiles(userId?: string): Promise<string[]> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      const userDir = await this.getUserRecordingsDirectory(currentUserId);
      
      const dirInfo = await FileSystem.getInfoAsync(userDir);
      if (!dirInfo.exists) {
        return [];
      }
      
      const files = await FileSystem.readDirectoryAsync(userDir);
      return files.map(fileName => userDir + fileName);
    } catch (error) {
      logError(`RecordingFileService: 获取用户录音文件列表失败 (userId: ${userId}):`, error);
      return [];
    }
  }

  /**
   * 删除录音文件
   * @param filePath 文件路径
   * @param userId 用户ID（可选，默认使用当前用户）
   */
  async deleteRecordingFile(filePath: string, userId?: string): Promise<void> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      
      // 从文件系统删除文件
      const fileInfo = await FileSystem.getInfoAsync(filePath);
      if (fileInfo.exists) {
        await FileSystem.deleteAsync(filePath);
        log(`RecordingFileService: 删除录音文件 ${filePath}`);
      }
      
      // 从数据库删除记录
      await sqliteService.deleteRecordingFile(currentUserId, filePath);
      log(`RecordingFileService: 删除录音文件数据库记录成功`);
    } catch (error) {
      logError(`RecordingFileService: 删除录音文件失败 (path: ${filePath}):`, error);
      throw error;
    }
  }
}

// 导出单例实例
export const recordingFileService = new RecordingFileService();
