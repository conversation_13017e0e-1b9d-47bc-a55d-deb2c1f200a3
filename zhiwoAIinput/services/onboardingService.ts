/**
 * 用户引导服务
 * 管理首次启动引导流程和问卷调查
 */
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { supabase } from './supabaseService';
import { log, error as logError, warn } from '@/services/logService';

// 缓存onboarding检查结果，避免重复检查
let lastOnboardingCheck: number = 0;
let cachedOnboardingResult: string[] | null = null;
const ONBOARDING_CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

// 存储键名
const STORAGE_KEYS = {
  ONBOARDING_COMPLETED: 'knowme_onboarding_completed',
  ONBOARDING_SURVEY: 'knowme_onboarding_survey',
  SELECTED_TEMPLATE: 'knowme_selected_template', // 改为单数
  FAST_MODE_ENABLED: 'knowme_fast_mode_enabled', // 新增：极速模式设置
};

// 问卷答案接口
export interface OnboardingSurvey {
  source: string; // 了解渠道
  useCases: string[]; // 使用场景
  selectedTemplate: string; // 改为单选模板ID
  fastModeEnabled: boolean; // 新增：极速模式设置
  completedAt: string; // 完成时间
}

// 引导状态接口
export interface OnboardingStatus {
  completed: boolean;
  survey?: OnboardingSurvey;
}

/**
 * 检查用户是否已完成引导
 */
export const isOnboardingCompleted = async (): Promise<boolean> => {
  try {
    const completed = await AsyncStorage.getItem(STORAGE_KEYS.ONBOARDING_COMPLETED);
    return completed === 'true';
  } catch (error) {
    logError('检查引导状态失败:', error);
    return false;
  }
};

/**
 * 获取引导状态
 */
export const getOnboardingStatus = async (): Promise<OnboardingStatus> => {
  try {
    const completed = await isOnboardingCompleted();
    if (!completed) {
      return { completed: false };
    }

    const surveyData = await AsyncStorage.getItem(STORAGE_KEYS.ONBOARDING_SURVEY);
    const survey = surveyData ? JSON.parse(surveyData) : undefined;

    return { completed, survey };
  } catch (error) {
    logError('获取引导状态失败:', error);
    return { completed: false };
  }
};

/**
 * 保存问卷数据到云端数据库
 */
const saveOnboardingSurveyToCloud = async (survey: OnboardingSurvey): Promise<void> => {
  try {
    // 获取当前用户信息
    const { data: { user } } = await supabase.auth.getUser();
    
    // 收集设备信息
    const deviceInfo = {
      platform: Platform.OS,
      version: Platform.Version,
      timestamp: new Date().toISOString(),
    };

    // 准备插入数据库的数据（保持向后兼容，将单选模板转为数组）
    const surveyData = {
      user_id: user?.id || null, // 允许匿名用户
      source: survey.source,
      use_cases: survey.useCases,
      selected_templates: [survey.selectedTemplate], // 将单选模板转为数组保存
      device_info: deviceInfo,
      completed_at: survey.completedAt,
    };

    // 插入到数据库
    const { error } = await supabase
      .from('onboarding_surveys')
      .insert([surveyData]);

    if (error) {
      logError('保存问卷数据到云端失败:', error);
      // 不抛出错误，避免影响用户体验
    } else {
      log('问卷数据已保存到云端');
    }
  } catch (error) {
    logError('保存问卷数据到云端异常:', error);
    // 不抛出错误，避免影响用户体验
  }
};

/**
 * 保存问卷答案
 */
export const saveSurveyAnswers = async (survey: OnboardingSurvey): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.ONBOARDING_SURVEY, JSON.stringify(survey));
    log('问卷答案已保存');
  } catch (error) {
    logError('保存问卷答案失败:', error);
    throw error;
  }
};

/**
 * 完成引导流程
 */
export const completeOnboarding = async (survey?: OnboardingSurvey): Promise<void> => {
  try {
    if (survey) {
      await saveSurveyAnswers(survey);
      
      // 保存单选模板和极速模式设置
      await saveSelectedTemplate(survey.selectedTemplate);
      await saveFastModeEnabled(survey.fastModeEnabled);
      
      // 异步保存到云端（不阻塞用户流程）
      saveOnboardingSurveyToCloud(survey).catch(error => {
        warn('云端保存失败，但不影响本地流程:', error);
      });
    }
    await AsyncStorage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, 'true');
    log('引导流程已完成');
  } catch (error) {
    logError('完成引导流程失败:', error);
    throw error;
  }
};

/**
 * 重置引导状态（用于开发调试）
 * 清除引导完成标记，下次启动会重新显示引导流程
 */
export const resetOnboardingStatus = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.ONBOARDING_COMPLETED,
      STORAGE_KEYS.ONBOARDING_SURVEY,
      STORAGE_KEYS.SELECTED_TEMPLATE,
      STORAGE_KEYS.FAST_MODE_ENABLED
    ]);
    
    // 清理录音相关状态，确保AbortController被正确重置
    try {
      const { store } = await import('../store');
      const { resetRecording } = await import('../store/slices/recordingSlice');
      
      log('[OnboardingService] 重置引导状态时同时清理录音状态');
      store.dispatch(resetRecording());
    } catch (storeError) {
      warn('[OnboardingService] 清理录音状态失败，但不影响引导状态重置:', storeError);
    }
    
    log('引导状态已重置');
  } catch (error) {
    logError('重置引导状态失败:', error);
    throw error;
  }
};

/**
 * 保存用户选择的模板（单选）
 */
export const saveSelectedTemplate = async (templateId: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.SELECTED_TEMPLATE, templateId);
    log('用户选择的模板已保存:', templateId);
  } catch (error) {
    logError('保存选择的模板失败:', error);
    throw error;
  }
};

/**
 * 获取用户选择的模板（单选）
 */
export const getSelectedTemplate = async (): Promise<string | null> => {
  try {
    const templateId = await AsyncStorage.getItem(STORAGE_KEYS.SELECTED_TEMPLATE);
    return templateId;
  } catch (error) {
    logError('获取选择的模板失败:', error);
    return null;
  }
};

/**
 * 保存极速模式设置
 */
export const saveFastModeEnabled = async (enabled: boolean): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.FAST_MODE_ENABLED, enabled.toString());
    log('极速模式设置已保存:', enabled);
  } catch (error) {
    logError('保存极速模式设置失败:', error);
    throw error;
  }
};

/**
 * 获取极速模式设置
 */
export const getFastModeEnabled = async (): Promise<boolean> => {
  try {
    const enabled = await AsyncStorage.getItem(STORAGE_KEYS.FAST_MODE_ENABLED);
    return enabled === 'true';
  } catch (error) {
    logError('获取极速模式设置失败:', error);
    return false; // 默认关闭极速模式
  }
};

// 保留旧的函数名以向后兼容
export const saveSelectedTemplates = saveSelectedTemplate;
export const getSelectedTemplates = async (useCache: boolean = true): Promise<string[]> => {
  // 检查缓存
  if (useCache) {
    const now = Date.now();
    if (cachedOnboardingResult !== null && (now - lastOnboardingCheck) < ONBOARDING_CACHE_DURATION) {
      log('[OnboardingService] 使用缓存的onboarding模板选择:', cachedOnboardingResult);
      return cachedOnboardingResult;
    }
  }

  const template = await getSelectedTemplate();
  const result = template ? [template] : [];

  // 缓存结果
  cachedOnboardingResult = result;
  lastOnboardingCheck = Date.now();

  return result;
};

/**
 * 清除onboarding缓存
 * 在模板选择发生变化时调用
 */
export const clearOnboardingCache = (): void => {
  cachedOnboardingResult = null;
  lastOnboardingCheck = 0;
  log('[OnboardingService] onboarding缓存已清除');
};

/**
 * 初始化用户模板偏好设置
 * 根据问卷选择的默认模板设置初始偏好，并将其设置为默认模板
 *
 * 注意：此函数应该只在用户完成引导问卷时调用，不应该在应用的其他地方被自动调用
 * 调用时机：
 * 1. 用户完成完整问卷流程时
 * 2. 用户跳过问卷但选择了模板时
 */
export const initializeTemplatePreferences = async (): Promise<void> => {
  try {
    // 获取问卷选择的模板（单选）
    const selectedTemplate = await getSelectedTemplate();

    if (!selectedTemplate) {
      log('[OnboardingService] 没有选择的模板，跳过偏好设置初始化');
      log('[OnboardingService] 提示：此函数应该只在用户完成引导问卷并选择了模板时调用');
      return;
    }

    log('[OnboardingService] 开始初始化模板偏好设置，选择的默认模板:', selectedTemplate);

    // 检查是否已经设置了相同的默认模板，避免重复设置
    try {
      const { getDefaultTemplateId } = await import('./templateService');
      const currentDefaultId = await getDefaultTemplateId();

      if (currentDefaultId === selectedTemplate) {
        log('[OnboardingService] 默认模板已正确设置为用户选择的模板，跳过重复初始化');
        return;
      } else {
        log('[OnboardingService] 当前默认模板与用户选择不符，需要更新');
        log('[OnboardingService] 当前默认模板ID:', currentDefaultId);
        log('[OnboardingService] 用户选择的模板ID:', selectedTemplate);
      }
    } catch (error) {
      warn('[OnboardingService] 检查当前默认模板失败:', error);
    }

    // 导入模板相关服务
    const { getUserTemplatePreferences, saveUserTemplatePreferences } = await import('./templatePreferenceService');
    const { syncSystemTemplates } = await import('./templateSyncService');
    const { setDefaultTemplateId, getDefaultTemplateId } = await import('./templateService');
    
    // 获取系统模板（从云端同步的数据）
    let allSystemTemplates: Array<{
      id: string;
      title: string;
      isSystem: boolean;
      category?: string;
    }> = [];
    
    try {
      const cloudTemplates = await syncSystemTemplates();
      allSystemTemplates = cloudTemplates.map(ct => ({
        id: ct.id,
        title: ct.title,
        isSystem: ct.isSystem,
        category: ct.category
      }));
    } catch (error) {
      warn('[OnboardingService] 获取云端模板失败，获取本地缓存模板:', error);
      try {
        const { getSystemTemplatesFromLocal } = await import('./templateSyncService');
        const localTemplates = await getSystemTemplatesFromLocal();
        allSystemTemplates = localTemplates.map(lt => ({
          id: lt.id,
          title: lt.title,
          isSystem: lt.isSystem,
          category: lt.category
        }));
      } catch (localError) {
        logError('[OnboardingService] 获取本地模板也失败:', localError);
        allSystemTemplates = [];
      }
    }

    // 验证选择的模板是否存在
    const selectedTemplateInfo = allSystemTemplates.find(t => t.id === selectedTemplate);
    if (!selectedTemplateInfo) {
      warn('[OnboardingService] 选择的模板不存在:', selectedTemplate);
      return;
    }

    // 获取当前偏好设置
    const currentPreferences = await getUserTemplatePreferences();
    
    // 创建新的偏好设置，将选择的模板设置为第一位
    const newPreferences: Array<{
      templateId: string;
      templateType: 'system' | 'user';
      isHidden: boolean;
      displayOrder: number;
    }> = [];
    const existingPreferenceMap = new Map(currentPreferences.map(p => [p.templateId, p]));
    let displayOrder = 0;

    // 首先添加用户选择的模板，给予最高优先级
    const existingSelectedPref = existingPreferenceMap.get(selectedTemplate);
    newPreferences.push({
      templateId: selectedTemplate,
      templateType: 'system' as const,
      isHidden: existingSelectedPref?.isHidden || false,
      displayOrder: displayOrder++,
    });
    log(`[OnboardingService] 添加选中模板到偏好设置: ${selectedTemplateInfo.title || selectedTemplate}, 排序: ${displayOrder - 1}`);

    // 然后添加其他系统模板，排在选中模板之后
    for (const template of allSystemTemplates) {
      if (template.id !== selectedTemplate) {
        const existingPref = existingPreferenceMap.get(template.id);
        newPreferences.push({
          templateId: template.id,
          templateType: 'system' as const,
          isHidden: existingPref?.isHidden || false,
          displayOrder: displayOrder++,
        });
      }
    }

    // 添加现有的用户模板偏好设置（如果有的话）
    currentPreferences.forEach(pref => {
      if (pref.templateType === 'user' && !newPreferences.some(np => np.templateId === pref.templateId)) {
        newPreferences.push({
          ...pref,
          displayOrder: displayOrder++,
        });
        log(`[OnboardingService] 保留现有用户模板偏好设置: ${pref.templateId}, 排序: ${displayOrder - 1}`);
      }
    });

    // 保存偏好设置
    log('[OnboardingService] 即将保存的偏好设置:', newPreferences);
    const success = await saveUserTemplatePreferences(newPreferences);
    
    if (success) {
      log('[OnboardingService] 模板偏好设置初始化成功，选中的模板已设置为优先显示');
      
      // 设置选择的模板为默认模板
      try {
        log('[OnboardingService] 准备设置默认模板ID:', selectedTemplate);
        
        // 设置默认模板
        await setDefaultTemplateId(selectedTemplate);
        log('[OnboardingService] 已调用setDefaultTemplateId，设置模板:', selectedTemplate);
        
        // 立即验证设置是否成功
        const verifyDefaultId = await getDefaultTemplateId();
        log('[OnboardingService] 验证默认模板ID设置结果:', verifyDefaultId);
        
        if (verifyDefaultId === selectedTemplate) {
          log('[OnboardingService] ✅ 默认模板设置验证成功');
        } else {
          logError('[OnboardingService] ❌ 默认模板设置验证失败，期望:', selectedTemplate, '实际:', verifyDefaultId);
          
          // 尝试重新设置
          log('[OnboardingService] 尝试重新设置默认模板...');
          await setDefaultTemplateId(selectedTemplate);
          
          // 再次验证
          const secondVerify = await getDefaultTemplateId();
          log('[OnboardingService] 重新设置后验证结果:', secondVerify);
        }
        
      } catch (defaultError) {
        logError('[OnboardingService] 设置默认模板失败:', defaultError);
      }
      
      // 立即验证偏好设置是否保存成功
      const savedPreferences = await getUserTemplatePreferences();
      log('[OnboardingService] 验证保存的偏好设置:', savedPreferences);
      
      // 验证选中的模板是否确实排在前面
      const selectedTemplatePreference = savedPreferences.find(p => p.templateId === selectedTemplate);
      log('[OnboardingService] 选中模板的偏好设置:', selectedTemplatePreference);
    } else {
      logError('[OnboardingService] 模板偏好设置初始化失败');
    }
  } catch (error) {
    logError('[OnboardingService] 初始化模板偏好设置异常:', error);
  }
}; 