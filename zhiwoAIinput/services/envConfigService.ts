/**
 * 环境配置管理服务
 * 统一管理应用的环境变量配置，支持多环境部署
 */
import Constants from 'expo-constants';
import { log } from '@/services/logService';

export type AppEnvironment = 'development' | 'staging' | 'production';

class EnvConfigService {
  private static instance: EnvConfigService;

  static getInstance(): EnvConfigService {
    if (!EnvConfigService.instance) {
      EnvConfigService.instance = new EnvConfigService();
    }
    return EnvConfigService.instance;
  }

  /**
   * 获取当前应用环境
   */
  getAppEnvironment(): AppEnvironment {
    // 优先级：EAS环境变量 > Constants.extra > 默认值
    const env = Constants.expoConfig?.extra?.appEnv || 
                Constants.expoConfig?.extra?.APP_ENV || 
                'development';
    
    // 确保返回有效的环境值
    if (['development', 'staging', 'production'].includes(env)) {
      return env as AppEnvironment;
    }
    
    // 根据其他标识推断环境
    if (__DEV__ || Constants.expoConfig?.extra?.isDevEnv === 'true') {
      return 'development';
    }
    
    return 'production';
  }

  /**
   * 是否为开发环境
   */
  isDevelopment(): boolean {
    return this.getAppEnvironment() === 'development';
  }

  /**
   * 是否为生产环境
   */
  isProduction(): boolean {
    return this.getAppEnvironment() === 'production';
  }

  /**
   * 获取Supabase配置
   * 新架构：直接从环境文件读取配置，无需复杂切换逻辑
   */
  getSupabaseConfig() {
    const url = Constants.expoConfig?.extra?.supabaseUrl || 'https://yenwfmoubflrluhqsyim.supabase.co';
    const anonKey = Constants.expoConfig?.extra?.supabaseAnonKey || '';
    const serviceKey = Constants.expoConfig?.extra?.supabaseServiceKey || '';
    
    // 根据URL判断是否为本地环境
    const isLocal = url.includes('127.0.0.1');

    return {
      url,
      anonKey,
      serviceKey,
      isLocal
    };
  }

  /**
   * 获取RevenueCat配置
   */
  getRevenueCatConfig() {
    return {
      ios: Constants.expoConfig?.extra?.revenueCatIosApiKey || '',
      android: Constants.expoConfig?.extra?.revenueCatAndroidApiKey || ''
    };
  }

  /**
   * 获取应用商店配置
   */
  getAppStoreConfig() {
    return {
      iosAppId: Constants.expoConfig?.extra?.iosAppId || '6744967748',
      androidPackageName: Constants.expoConfig?.extra?.androidPackageName || 'com.mindpowerhk.knowmetype'
    };
  }

  /**
   * 获取统一代理服务URL
   * 所有AI服务调用都通过这个代理，避免客户端直接存储API密钥
   */
  getUnifiedProxyUrl(): string {
    const supabaseConfig = this.getSupabaseConfig();
    return `${supabaseConfig.url}/functions/v1/unified-proxy`;
  }

  /**
   * 获取开发环境特定配置
   */
  getDevConfig() {
    if (!this.isDevelopment()) {
      return null;
    }

    return {
      showDevLogin: Constants.expoConfig?.extra?.showDevLogin === 'true',
      enableDebugLogs: true,
      // 其他开发环境专用配置
    };
  }

  /**
   * 打印配置信息（仅开发环境）
   */
  logConfig() {
    if (!this.isDevelopment()) {
      return;
    }

    const supabaseConfig = this.getSupabaseConfig();
    log('=== 🔧 环境配置信息 ===');
    log(`📍 当前环境: ${this.getAppEnvironment()}`);
    log(`🗄️  Supabase模式: ${supabaseConfig.isLocal ? '本地数据库 (localhost:54321)' : '云端数据库'}`);
    log(`🔗 Supabase URL: ${supabaseConfig.url}`);
    log(`🚀 统一代理URL: ${this.getUnifiedProxyUrl()}`);
    log(`🛠️  开发模式: ${this.isDevelopment()}`);
    log(`🔄 使用本地Supabase: ${supabaseConfig.isLocal}`);
    
    const devConfig = this.getDevConfig();
    if (devConfig) {
      log('📱 开发配置:', devConfig);
    }
    log('=========================');
  }

  /**
   * 验证必要的配置是否完整
   */
  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const supabaseConfig = this.getSupabaseConfig();

    if (!supabaseConfig.url) {
      errors.push('Supabase URL 未配置');
    }

    if (!supabaseConfig.anonKey) {
      errors.push('Supabase Anon Key 未配置');
    }

    // 生产环境额外检查
    if (this.isProduction()) {
      const revenueCatConfig = this.getRevenueCatConfig();
      if (!revenueCatConfig.ios && !revenueCatConfig.android) {
        errors.push('RevenueCat 配置不完整');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// 导出单例实例
export const envConfig = EnvConfigService.getInstance();

// 便捷方法导出
export const getAppEnvironment = () => envConfig.getAppEnvironment();
export const isDevelopment = () => envConfig.isDevelopment();
export const isProduction = () => envConfig.isProduction();
export const getSupabaseConfig = () => envConfig.getSupabaseConfig();
export const getUnifiedProxyUrl = () => envConfig.getUnifiedProxyUrl(); 