/**
 * 模板服务
 * 处理风格模板的保存、加载和管理
 */
import AsyncStorage from '@react-native-async-storage/async-storage';
import { generateId, generateUUID } from '@/utils/helpers';
import { getVipStatus } from './storageService';
import {
  syncSystemTemplates,
  getSystemTemplatesFromLocal,
  saveUserTemplateToCloud,
  deleteUserTemplateFromCloud,
  syncUserTemplates
} from './templateSyncService';
import {
  getUserTemplatePreferences,
  hideTemplate as hideTemplatePreference
} from './templatePreferenceService';
import { log, warn, error as logError } from '@/services/logService';

// 存储键名
const STORAGE_KEYS = {
  USER_TEMPLATES: 'knowme_user_templates',
  DEFAULT_TEMPLATE_ID: 'knowme_default_template_id',
  DELETED_TEMPLATES: 'knowme_deleted_templates', // 删除记录
};

// 缓存机制
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

class TemplateCache {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly DEFAULT_CACHE_DURATION = 30000; // 30秒缓存
  private readonly SHORT_CACHE_DURATION = 5000; // 5秒短缓存，用于防抖

  set<T>(key: string, data: T, duration?: number): void {
    const now = Date.now();
    this.cache.set(key, {
      data,
      timestamp: now,
      expiry: now + (duration || this.DEFAULT_CACHE_DURATION)
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now > entry.expiry) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    const now = Date.now();
    if (now > entry.expiry) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  clear(): void {
    this.cache.clear();
  }

  // 获取短缓存持续时间，用于防抖
  getShortCacheDuration(): number {
    return this.SHORT_CACHE_DURATION;
  }
}

// 全局缓存实例
const templateCache = new TemplateCache();

// 模板数据接口
export interface Template {
  id: string;
  title: string;
  description: string;
  name_key?: string;
  description_key?: string;
  prompt: string;
  isSystem: boolean;
  isDefault?: boolean;
  color?: string;
  borderColor?: string;
  backgroundColor?: string;
  isSynced?: boolean; // 是否已同步到云端（仅对自定义模板有意义）
  category?: string; // 模板分类
  isVipOnly?: boolean; // 是否为VIP专用模板
  semanticId?: string; // 语义化ID，用于稳定地识别模板
  isHidden?: boolean; // 是否隐藏（根据用户偏好设置）
  displayOrder?: number; // 显示顺序（根据用户偏好设置）
  icon?: string; // 模板图标（emoji或图标名）
}

// 删除记录接口
export interface DeletedTemplateRecord {
  templateId: string;
  deletedAt: number; // 删除时间戳
  title?: string; // 模板标题，便于调试
}

// 语义化ID到模板名称的映射
export const SEMANTIC_ID_MAP = {
  'email': '发邮件',
  'chat': '日常发信息', 
  'notes': '做notes记录',
  'markdown_notes': 'markdown格式的要点记录',
  'text_optimize': '原文优化',
  'emoji': 'emoji表情',
  'xiaohongshu': '小红书文案',
  'moments': '朋友圈文案',
  'en_translation': '英语翻译',
  'ja_translation': '日语翻译',
  'ko_translation': '韩语翻译',
  'id_translation': '印尼语翻译',
  'de_translation': '德语翻译',
  'fr_translation': '法语翻译',
  'es_translation': '西班牙语翻译',
  'recraft': 'Recraft生图',
  'midjourney_v7': 'Midjourney V7',
  'stable_diffusion': 'Stable Diffusion',
  'jimeng_ai': '即梦AI',
  'official': '正式公文',
  'academic': '学术论文',
  'news': '新闻报道',
  'creative': '创意文案',
  'casual': '口语化表达'
} as const;

/**
 * 获取所有模板
 * @returns 模板数组，包含系统预设和用户自定义模板
 */
export const getAllTemplates = async (forceRefresh: boolean = false): Promise<Template[]> => {
  try {
    const cacheKey = 'getAllTemplates';

    // 检查缓存（除非强制刷新）
    if (!forceRefresh) {
      const cachedTemplates = templateCache.get<Template[]>(cacheKey);
      if (cachedTemplates) {
        log('[TemplateService] 使用缓存的模板数据:', cachedTemplates.length, '个');
        return cachedTemplates;
      }
    }

    // 获取用户自定义模板
    const userTemplates = await getUserTemplates();

    // 获取默认模板ID
    const defaultTemplateId = await getDefaultTemplateId();

    // 优化：先尝试本地缓存的系统模板
    let syncedSystemTemplates: Template[] = [];
    try {
      const { getSystemTemplatesFromLocal } = await import('./templateSyncService');
      const localSystemTemplates = await getSystemTemplatesFromLocal();
      if (localSystemTemplates && localSystemTemplates.length > 0) {
        syncedSystemTemplates = localSystemTemplates.map(ct => ({
          id: ct.id,
          title: ct.title,
          description: ct.description,
          name_key: ct.name_key,
          description_key: ct.description_key,
          prompt: ct.prompt,
          isSystem: ct.isSystem,
          color: ct.color,
          borderColor: ct.borderColor,
          backgroundColor: ct.backgroundColor,
          isVipOnly: ct.isVipOnly || false,
          category: ct.category,
          semanticId: ct.semantic_id, // 映射语义化ID
          icon: ct.icon // 添加图标字段
        }));
        log('[TemplateService] 本地缓存有系统模板，优先返回本地数据:', syncedSystemTemplates.length, '个');
      } else {
        // 没有本地缓存再尝试云端
        const cloudTemplates = await syncSystemTemplates();
        syncedSystemTemplates = cloudTemplates.map(ct => ({
          id: ct.id,
          title: ct.title,
          description: ct.description,
          name_key: ct.name_key,
          description_key: ct.description_key,
          prompt: ct.prompt,
          isSystem: ct.isSystem,
          color: ct.color,
          borderColor: ct.borderColor,
          backgroundColor: ct.backgroundColor,
          isVipOnly: ct.isVipOnly || false,
          category: ct.category,
          semanticId: ct.semantic_id, // 映射语义化ID
          icon: ct.icon // 添加图标字段
        }));
        log('[TemplateService] 使用同步的系统模板:', syncedSystemTemplates.length, '个');
      }
    } catch (syncError) {
      warn('[TemplateService] 同步系统模板失败，使用本地fallback模板:', syncError);
      // 使用本地fallback模板（基础模板）
      syncedSystemTemplates = await getLocalFallbackTemplates();
    }

    // 获取用户偏好设置
    const userPreferences = await getUserTemplatePreferences();
    const preferenceMap = new Map(userPreferences.map(p => [p.templateId, p]));

    // 合并系统模板和用户模板
    const allTemplates = [...syncedSystemTemplates, ...userTemplates];

    // 设置默认模板
    const templatesWithDefault = allTemplates.map(template => ({
      ...template,
      isDefault: template.id === defaultTemplateId
    }));

    // 应用用户偏好（但保持默认模板状态）
    const templatesWithPreferences = templatesWithDefault.map(template => {
      const preference = preferenceMap.get(template.id);
      if (preference) {
        return {
          ...template,
          isHidden: preference.isHidden,
          displayOrder: preference.displayOrder,
          // 保持从 templatesWithDefault 中设置的 isDefault 状态
          // 不允许偏好设置覆盖默认模板的判断
        };
      }
      return {
        ...template,
        isHidden: false,
        displayOrder: 999, // 没有偏好设置的模板排在最后
      };
    });

    // 过滤掉隐藏的模板
    const visibleTemplates = templatesWithPreferences.filter(template => !template.isHidden);

    // 根据用户偏好排序，但确保默认模板始终在第一位
    const sortedTemplates = visibleTemplates.sort((a, b) => {
      // 默认模板始终排在第一位
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;
      // 如果都是默认模板或都不是默认模板，按displayOrder排序
      const orderA = a.displayOrder !== undefined ? a.displayOrder : 999;
      const orderB = b.displayOrder !== undefined ? b.displayOrder : 999;
      return orderA - orderB;
    });

    log('[TemplateService] 获取模板列表:', {
      total: sortedTemplates.length,
      totalWithHidden: templatesWithPreferences.length,
      system: syncedSystemTemplates.length,
      user: userTemplates.length,
      defaultId: defaultTemplateId,
      hiddenCount: templatesWithPreferences.length - sortedTemplates.length
    });

    // 调试信息：显示排序后的模板顺序
    log('[TemplateService] 排序后的模板顺序:');
    sortedTemplates.forEach((template, index) => {
      log(`[TemplateService] ${index}: ${template.title} (${template.id}) - displayOrder: ${template.displayOrder}, isHidden: ${template.isHidden}`);
    });

    // 缓存结果
    templateCache.set(cacheKey, sortedTemplates);

    return sortedTemplates;
  } catch (error) {
    logError('[TemplateService] 获取模板失败:', error);
    // 返回本地fallback模板
    return await getLocalFallbackTemplates();
  }
};

/**
 * 获取本地fallback模板（当云端同步失败时使用）
 */
const getLocalFallbackTemplates = async (): Promise<Template[]> => {
  return [
    {
      id: generateUUID(),
      semanticId: 'text_optimize',
      title: "原文优化",
      description: "保持原文基础上修正错误、删除重复和语气词，优化段落结构。",
      prompt: '仅做必要的文本优化。修正错误，删除重复词汇和语气词，优化段落呈现，尽可能保持原文表达。\n\n示例：\n输入：这个这个方案呢其实还是很不错的嗯我觉得可以考虑一下但是呢还需要再优化优化一下\n输出：\n这个方案很不错，可以考虑，但还需要进一步优化。',
      isSystem: true,
      color: "#059669",
      borderColor: "#059669",
      backgroundColor: "#D1FAE5",
      isDefault: true,
      category: "scenario",
      isVipOnly: false,
      icon: "✨"
    },
    {
      id: generateUUID(),
      semanticId: 'email',
      title: "发邮件",
      description: "一键生成专业得体的商务邮件，自动适配收件人身份和邮件场景。",
      prompt: '写成正式邮件格式。开头用"尊敬的"或"您好"，结尾加署名。语言正式礼貌，每段不超过3行。\n\n示例：\n输入：李经理 项目报告周五前交 会议室改B栋201\n输出：\n尊敬的李经理：\n您好！本周项目汇报调整至周五，地点变更为B栋201会议室。请准备项目进度报告参会。\n期待您的出席。\n\n此致\n敬礼！\n[你的姓名]',
      isSystem: true,
      color: "#1E40AF",
      borderColor: "#1E40AF", 
      backgroundColor: "#DBEAFE",
      category: "scenario",
      isVipOnly: false,
      icon: "📧"
    },
    {
      id: generateUUID(),
      semanticId: 'chat',
      title: "日常发信息",
      description: "让闲聊消息更自然生动，自动匹配好友亲密度和对话场景。",
      prompt: '转换成自然的聊天语气。用短句，口语化表达，适当添加表情符号。\n\n示例：\n输入：晚上聚餐地点改到万象城5楼那家川菜馆七点半不见不散\n输出：\n今晚聚餐换地方啦！🌶️\n万象城5楼川菜馆\n7:30集合，不见不散哦！🙌',
      isSystem: true,
      color: "#059669",
      borderColor: "#059669",
      backgroundColor: "#D1FAE5",
      category: "scenario",
      isVipOnly: false,
      icon: "💬"
    },
    {
      id: generateUUID(),
      semanticId: 'notes',
      title: "做notes记录",
      description: "将零散信息转化为结构化的纯文本笔记，支持学习/会议/灵感记录场景。",
      prompt: '整理成结构化的纯文本笔记。用段落分层，序号标记，重要信息用【】高亮，用→标示要点。\n\n示例：\n输入：客户需求UI界面要简洁支付流程三步完成增加夜间模式\n输出：\n客户需求文档\n\n核心功能：\n1. UI设计 → 【极简风格】要求\n2. 支付流程 → 必须【3步内完成】\n3. 视觉模式 → 【增加暗黑主题】功能\n\n→ 优先级：支付流程优化最重要',
      isSystem: true,
      color: "#7C3AED",
      borderColor: "#7C3AED",
      backgroundColor: "#E9D5FF",
      category: "scenario",
      isVipOnly: false,
      icon: "📝"
    },
    {
      id: generateUUID(),
      semanticId: 'markdown_notes',
      title: "markdown格式的要点记录",
      description: "使用markdown格式生成结构化要点记录，支持标题层级和格式化。",
      prompt: '整理成markdown格式的结构化笔记。用标题、列表、要点分层。关键信息加粗，行动项用→标记。\n\n示例：\n输入：客户需求UI界面要简洁支付流程三步完成增加夜间模式\n输出：\n## 客户需求文档\n### 核心功能\n• **UI设计** → 极简风格\n• **支付流程** → 3步内完成\n• **视觉模式** → 增加暗黑主题',
      isSystem: true,
      color: "#8B5CF6",
      borderColor: "#8B5CF6",
      backgroundColor: "#E9D5FF",
      category: "scenario",
      isVipOnly: true,
      icon: "📋"
    },
    {
      id: generateUUID(),
      semanticId: 'zh_cn_translation',
      title: "中文翻译（简体）",
      description: "精准翻译各种语言为规范简体中文，保持原意并符合中文表达习惯。",
      prompt: '翻译成标准简体中文。保持原文含义，使用规范汉字，符合中文表达习惯。\n\n示例：\n输入：Hello, how are you today?\n输出：你好，你今天怎么样？',
      isSystem: true,
      color: "#DC2626",
      borderColor: "#DC2626",
      backgroundColor: "#FEE2E2",
      category: "translation",
      isVipOnly: true,
      icon: "🇨🇳"
    },
    {
      id: generateUUID(),
      semanticId: 'zh_tw_translation',
      title: "中文翻译（繁体）",
      description: "精准翻译各种语言为标准繁体中文，保持原意并符合繁体中文表达习惯。",
      prompt: '翻译成標準繁體中文。保持原文含義，使用標準繁體漢字，符合繁體中文表達習慣。\n\n示例：\n輸入：Hello, how are you today?\n輸出：你好，你今天怎麼樣？',
      isSystem: true,
      color: "#DC2626",
      borderColor: "#DC2626", 
      backgroundColor: "#FEE2E2",
      category: "translation",
      isVipOnly: true,
      icon: "🇭🇰"
    }
  ];
};

/**
 * 通过语义化ID获取模板
 * @param semanticId 语义化ID
 * @returns 模板对象或null
 */
export const getTemplateBySemanticId = async (semanticId: string): Promise<Template | null> => {
  try {
    const allTemplates = await getAllTemplates();
    return allTemplates.find(template => template.semanticId === semanticId) || null;
  } catch (error) {
    logError('[TemplateService] 通过语义化ID获取模板失败:', error);
    return null;
  }
};

/**
 * 获取默认模板（优先使用语义化ID为'text_optimize'的模板）
 * @returns 默认模板
 */
export const getDefaultTemplate = async (): Promise<Template> => {
  try {
    const allTemplates = await getAllTemplates();
    log('[TemplateService] 获取默认模板，总模板数:', allTemplates.length);
    
    // 首先尝试获取当前设置的默认模板
    const defaultTemplateId = await getDefaultTemplateId();
    log('[TemplateService] 当前设置的默认模板ID:', defaultTemplateId);

    if (defaultTemplateId && defaultTemplateId !== '') {
      const defaultTemplate = allTemplates.find(t => t.id === defaultTemplateId);
      if (defaultTemplate) {
        log('[TemplateService] 找到设置的默认模板:', defaultTemplate.title);
        return defaultTemplate;
      } else {
        log('[TemplateService] 设置的默认模板ID未找到对应模板，可能模板已被删除');
      }
    }

    // 如果没有找到，尝试使用语义化ID为'text_optimize'的模板
    const textOptimizeTemplate = allTemplates.find(t => t.semanticId === 'text_optimize');
    if (textOptimizeTemplate) {
      log('[TemplateService] 使用text_optimize模板作为默认模板:', textOptimizeTemplate.title);
      // 只有在没有设置默认模板ID时才设置新的默认模板ID，避免覆盖用户设置
      if (!defaultTemplateId) {
        await setDefaultTemplateId(textOptimizeTemplate.id);
      }
      return textOptimizeTemplate;
    }

    // 如果还没有找到，使用第一个系统模板
    const firstSystemTemplate = allTemplates.find(t => t.isSystem);
    if (firstSystemTemplate) {
      log('[TemplateService] 使用第一个系统模板作为默认模板:', firstSystemTemplate.title);
      // 只有在没有设置默认模板ID时才设置新的默认模板ID，避免覆盖用户设置
      if (!defaultTemplateId) {
        await setDefaultTemplateId(firstSystemTemplate.id);
      }
      return firstSystemTemplate;
    }

    // 最后fallback - 返回第一个模板
    if (allTemplates.length > 0) {
      log('[TemplateService] 使用第一个模板作为默认模板:', allTemplates[0].title);
      // 只有在没有设置默认模板ID时才设置新的默认模板ID，避免覆盖用户设置
      if (!defaultTemplateId) {
        await setDefaultTemplateId(allTemplates[0].id);
      }
      return allTemplates[0];
    }

    // 如果没有任何模板，创建一个基础模板
    const fallbackTemplate: Template = {
      id: generateUUID(),
      semanticId: 'text_optimize',
      title: "原文优化",
      description: "保持原文基础上修正错误、删除重复和语气词，优化段落结构。",
      prompt: '仅做必要的文本优化。修正错误，删除重复词汇和语气词，优化段落呈现，尽可能保持原文表达。\n\n示例：\n输入：这个这个方案呢其实还是很不错的嗯我觉得可以考虑一下但是呢还需要再优化优化一下\n输出：\n这个方案很不错，可以考虑，但还需要进一步优化。',
      isSystem: true,
      color: "#059669",
      borderColor: "#059669",
      backgroundColor: "#D1FAE5",
      isDefault: true,
      category: "scenario",
      isVipOnly: false,
      icon: "✨"
    };

    await setDefaultTemplateId(fallbackTemplate.id);
    return fallbackTemplate;
  } catch (error) {
    logError('[TemplateService] 获取默认模板失败:', error);
    
    // 返回fallback模板
    const fallbackTemplate: Template = {
      id: generateUUID(),
      semanticId: 'text_optimize',
      title: "原文优化",
      description: "保持原文基础上修正错误、删除重复和语气词，优化段落结构。",
      prompt: '仅做必要的文本优化。修正错误，删除重复词汇和语气词，优化段落呈现，尽可能保持原文表达。\n\n示例：\n输入：这个这个方案呢其实还是很不错的嗯我觉得可以考虑一下但是呢还需要再优化优化一下\n输出：\n这个方案很不错，可以考虑，但还需要进一步优化。',
      isSystem: true,
      color: "#059669",
      borderColor: "#059669",
      backgroundColor: "#D1FAE5",
      isDefault: true,
      category: "scenario",
      isVipOnly: false,
      icon: "✨"
    };

    return fallbackTemplate;
  }
};

/**
 * 确保默认模板存在（优先保护用户设置，仅在必要时设置默认值）
 */
export const ensureDefaultTemplateExists = async (): Promise<void> => {
  try {
    const defaultTemplateId = await getDefaultTemplateId();
    log('[TemplateService] 当前默认模板ID:', defaultTemplateId);

    const allTemplates = await getAllTemplates();
    log('[TemplateService] 可用模板数量:', allTemplates.length);

    if (defaultTemplateId && defaultTemplateId !== '') {
      // 检查默认模板是否仍然存在
      const defaultTemplate = allTemplates.find(t => t.id === defaultTemplateId);
      if (defaultTemplate) {
        log('[TemplateService] 默认模板存在且有效:', defaultTemplate.title, '- 保持用户设置');
        return; // 用户已有有效的默认模板，不要覆盖
      } else {
        log('[TemplateService] 默认模板ID无效，模板不存在，需要重新设置');
        log('[TemplateService] 查找的默认模板ID:', defaultTemplateId);
        log('[TemplateService] 可用模板列表:', allTemplates.map(t => `${t.title}(${t.id})`).join(', '));
      }
    } else {
      log('[TemplateService] 没有设置默认模板，需要初始化');
    }

    // 只有在默认模板ID为空或无效时才设置新的默认模板
    log('[TemplateService] 开始设置新的默认模板...');

    // 优先使用email模板
    const emailTemplate = allTemplates.find(t => t.semanticId === 'email');
    if (emailTemplate) {
      await setDefaultTemplateId(emailTemplate.id);
      log('[TemplateService] 设置email模板为默认:', emailTemplate.title);
      return;
    }

    // 如果没有email模板，使用第一个系统模板
    const firstSystemTemplate = allTemplates.find(t => t.isSystem);
    if (firstSystemTemplate) {
      await setDefaultTemplateId(firstSystemTemplate.id);
      log('[TemplateService] 设置第一个系统模板为默认:', firstSystemTemplate.title);
      return;
    }

    // 如果都没有，使用第一个可用模板
    if (allTemplates.length > 0) {
      await setDefaultTemplateId(allTemplates[0].id);
      log('[TemplateService] 设置第一个模板为默认:', allTemplates[0].title);
    } else {
      warn('[TemplateService] 没有找到任何可用模板');
    }
  } catch (error) {
    logError('[TemplateService] 确保默认模板存在失败:', error);
  }
};

/**
 * 获取用户自定义模板
 * @returns 用户自定义模板数组
 */
export const getUserTemplates = async (): Promise<Template[]> => {
  try {
    const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.USER_TEMPLATES);
    const templates = jsonValue ? JSON.parse(jsonValue) : [];

    // 获取删除记录
    const deletedRecords = await getDeletedTemplateRecords();
    const deletedIds = new Set(deletedRecords.map(record => record.templateId));

    // 过滤掉已删除的模板，并为没有同步状态字段和图标字段的历史模板设置默认值
    const migratedTemplates = templates
      .filter((template: Template) => !deletedIds.has(template.id))
      .map((template: Template) => ({
        ...template,
        isSynced: template.isSynced !== undefined ? template.isSynced : false,
        icon: template.icon || '👤' // 为历史用户模板设置默认图标
      }));

    // 如果有模板被迁移了图标，保存回存储
    const hasIconMigration = templates.some((template: Template) => !template.icon);
    if (hasIconMigration && migratedTemplates.length > 0) {
      try {
        await AsyncStorage.setItem(STORAGE_KEYS.USER_TEMPLATES, JSON.stringify(migratedTemplates));
        log('[TemplateService] 已为历史用户模板迁移图标字段');
      } catch (error) {
        logError('[TemplateService] 图标迁移保存失败:', error);
      }
    }

    return migratedTemplates;
  } catch (error) {
    logError('获取用户模板失败:', error);
    return [];
  }
};

/**
 * 保存用户自定义模板
 * @param template 模板对象
 * @returns 成功返回true，失败返回false
 */
export const saveUserTemplate = async (template: Template): Promise<boolean> => {
  try {
    // 检查VIP状态
    if (!template.isSystem) {
      const vipStatus = await getVipStatus();
      const userTemplates = await getUserTemplates();

      // 非VIP用户只能创建1个自定义模板
      if (!vipStatus.isVip && userTemplates.length >= 1 && !userTemplates.some(t => t.id === template.id)) {
        throw new Error('非VIP用户最多只能创建1个自定义模板');
      }
    }

    // 获取现有模板
    const userTemplates = await getUserTemplates();

    // 检查是否存在相同ID的模板
    const existingIndex = userTemplates.findIndex(t => t.id === template.id);

    let updatedTemplates: Template[];

    if (existingIndex >= 0) {
      // 更新现有模板
      updatedTemplates = userTemplates.map((t, index) =>
        index === existingIndex ? { ...template, isSystem: false } : t
      );
    } else {
      // 添加新模板，确保isSystem为false，使用UUID格式的ID
      const newTemplate = { ...template, isSystem: false, id: template.id || generateUUID() };
      updatedTemplates = [...userTemplates, newTemplate];
    }

    // 保存到本地存储
    await AsyncStorage.setItem(STORAGE_KEYS.USER_TEMPLATES, JSON.stringify(updatedTemplates));

    // 如果是默认模板，更新默认模板ID
    if (template.isDefault) {
      await setDefaultTemplateId(template.id);
    }

    // VIP用户同步到云端
    const syncSuccess = await syncUserTemplateToCloudIfVip(template);

    // 如果同步成功，更新本地模板的同步状态
    if (syncSuccess) {
      await updateTemplateSyncStatus(template.id, true);
    }

    return true;
  } catch (error) {
    logError('保存用户模板失败:', error);
    return false;
  }
};

/**
 * 删除模板（优先使用偏好设置隐藏，必要时物理删除）
 * @param templateId 要删除的模板ID
 * @returns 成功返回true，失败返回false
 */
export const deleteUserTemplate = async (templateId: string): Promise<boolean> => {
  try {
    // 获取所有模板
    const allTemplates = await getAllTemplates();
    const templateToDelete = allTemplates.find(t => t.id === templateId);

    if (!templateToDelete) {
      return false;
    }

    // 对于系统模板，使用偏好设置隐藏
    if (templateToDelete.isSystem) {
      const hideSuccess = await hideTemplatePreference(templateId, 'system');
      if (!hideSuccess) {
        logError('隐藏系统模板失败:', templateId);
        return false;
      }
      log(`[TemplateService] 系统模板已隐藏: ${templateToDelete.title}`);
      return true;
    }

    // 对于用户模板，进行物理删除
    const userTemplates = await getUserTemplates();
    const userTemplate = userTemplates.find(t => t.id === templateId);

    if (!userTemplate) {
      return false;
    }

    // 过滤掉要删除的模板
    const updatedTemplates = userTemplates.filter(t => t.id !== templateId);

    // 保存更新后的模板
    await AsyncStorage.setItem(STORAGE_KEYS.USER_TEMPLATES, JSON.stringify(updatedTemplates));

    // 如果删除的是默认模板，设置一个新的默认模板
    if (userTemplate.isDefault) {
      // 获取剩余的所有模板
      const remainingTemplates = await getAllTemplates();
      if (remainingTemplates.length > 0) {
        // 设置第一个剩余模板为默认
        await setDefaultTemplateId(remainingTemplates[0].id);
        log(`[TemplateService] 已设置新的默认模板: ${remainingTemplates[0].title}`);
      } else {
        // 如果没有剩余模板，使用默认email模板ID
        await setDefaultTemplateId("b7a3f2c8-4e6d-4a2b-8f1c-9e7d6c5b4a3f");
        log(`[TemplateService] 已设置默认email模板为默认`);
      }
    }

    // 记录删除操作（防止VIP降级后删除再升级VIP时重新同步）
    await addDeletedTemplateRecord(templateId, userTemplate.title);

    // 同时设置偏好隐藏（双重保险）
    await hideTemplatePreference(templateId, 'user');

    // VIP用户从云端删除
    await deleteUserTemplateFromCloudIfVip(templateId);

    log(`[TemplateService] 用户模板已删除: ${userTemplate.title}`);
    return true;
  } catch (error) {
    logError('删除模板失败:', error);
    return false;
  }
};

/**
 * 获取默认模板ID
 * @returns 默认模板ID
 */
export const getDefaultTemplateId = async (useCache: boolean = true): Promise<string> => {
  try {
    const cacheKey = 'defaultTemplateId';

    // 检查缓存
    if (useCache) {
      const cachedId = templateCache.get<string>(cacheKey);
      if (cachedId !== null) {
        log('[TemplateService] 使用缓存的默认模板ID:', cachedId);
        return cachedId;
      }
    }

    log('[TemplateService] 开始获取默认模板ID...');

    // 检查AsyncStorage的原始值
    const rawValue = await AsyncStorage.getItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID);
    log('[TemplateService] AsyncStorage原始值:', rawValue);

    const result = rawValue || '';

    if (rawValue) {
      log('[TemplateService] 找到存储的默认模板ID:', rawValue);
    } else {
      log('[TemplateService] AsyncStorage中没有默认模板ID，返回空字符串');
    }

    // 缓存结果（使用短缓存时间）
    templateCache.set(cacheKey, result, templateCache.getShortCacheDuration());

    return result;
  } catch (error) {
    logError('[TemplateService] 获取默认模板ID失败:', error);
    return '';
  }
};

/**
 * 设置默认模板ID
 * @param templateId 要设置为默认的模板ID
 */
export const setDefaultTemplateId = async (templateId: string): Promise<void> => {
  try {
    log('[TemplateService] 正在设置默认模板ID:', templateId);
    await AsyncStorage.setItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID, templateId);
    log('[TemplateService] 默认模板ID设置成功:', templateId);

    // 清除相关缓存
    templateCache.set('defaultTemplateId', templateId, templateCache.getShortCacheDuration());
    templateCache.clear(); // 清除所有模板缓存，因为默认模板变了

    // 验证设置是否成功
    const savedId = await AsyncStorage.getItem(STORAGE_KEYS.DEFAULT_TEMPLATE_ID);
    log('[TemplateService] 验证保存的默认模板ID:', savedId);
  } catch (error) {
    logError('[TemplateService] 设置默认模板ID失败:', error);
    throw error;
  }
};

/**
 * VIP用户模板云端同步辅助函数
 * 将本地模板转换为云端格式并同步
 */
const syncUserTemplateToCloudIfVip = async (template: Template): Promise<boolean> => {
  try {
    // 检查用户是否为VIP
    const vipStatus = await getVipStatus();
    if (!vipStatus.isVip) {
      log('[TemplateService] 非VIP用户，跳过云端同步');
      return false;
    }

    // 转换为LocalTemplate格式 (templateSyncService需要这个格式)
    const localTemplate = {
      id: template.id,
      title: template.title,
      description: template.description,
      prompt: template.prompt,
      isSystem: template.isSystem,
      color: template.color,
      borderColor: template.borderColor,
      backgroundColor: template.backgroundColor,
      position: 999 // 用户模板排在最后
    };

    // 调用云端同步
    const success = await saveUserTemplateToCloud(localTemplate);
    if (success) {
      log(`[TemplateService] 成功同步用户模板到云端: ${template.title}`);
      return true;
    } else {
      warn(`[TemplateService] 同步用户模板到云端失败: ${template.title}`);
      return false;
    }
  } catch (error) {
    logError('[TemplateService] VIP用户模板云端同步失败:', error);
    return false;
  }
};

/**
 * VIP用户模板云端删除辅助函数
 */
const deleteUserTemplateFromCloudIfVip = async (templateId: string): Promise<void> => {
  try {
    // 检查用户是否为VIP
    const vipStatus = await getVipStatus();
    if (!vipStatus.isVip) {
      log('[TemplateService] 非VIP用户，跳过云端删除');
      return;
    }

    // 调用云端删除
    const success = await deleteUserTemplateFromCloud(templateId);
    if (success) {
      log(`[TemplateService] 成功从云端删除用户模板: ${templateId}`);
    } else {
      warn(`[TemplateService] 从云端删除用户模板失败: ${templateId}`);
    }
  } catch (error) {
    logError('[TemplateService] VIP用户模板云端删除失败:', error);
  }
};

/**
 * 清除模板缓存
 * 在模板数据发生变化时调用
 */
export const clearTemplateCache = (): void => {
  templateCache.clear();
  log('[TemplateService] 模板缓存已清除');
};

/**
 * 登录后同步用户模板
 * 从云端获取VIP用户的自定义模板并合并到本地
 */
export const syncUserTemplatesOnLogin = async (): Promise<void> => {
  try {
    log('[TemplateService] 开始登录后用户模板同步...');

    // 检查用户是否为VIP
    const vipStatus = await getVipStatus();
    if (!vipStatus.isVip) {
      log('[TemplateService] 非VIP用户，跳过云端模板同步');
      return;
    }

    // 第一步：同步删除记录到云端（优先处理删除操作）
    const deletedCount = await syncDeletedRecordsToCloud();
    if (deletedCount > 0) {
      log(`[TemplateService] 已同步 ${deletedCount} 条删除记录到云端`);
    }

    // 第二步：从云端同步用户模板
    const cloudTemplates = await syncUserTemplates();

    if (cloudTemplates.length > 0) {
      log(`[TemplateService] 成功同步 ${cloudTemplates.length} 个云端用户模板`);
    } else {
      log('[TemplateService] 云端没有用户模板需要同步');
    }

    // 第三步：VIP用户：同步本地历史模板到云端
    await syncLocalTemplatesToCloud();
  } catch (error) {
    logError('[TemplateService] 登录后用户模板同步失败:', error);
  }
};

/**
 * 同步本地用户模板到云端（VIP升级后使用）
 * 将本地已有的用户模板同步到云端
 */
export const syncLocalTemplatesToCloud = async (): Promise<void> => {
  try {
    log('[TemplateService] 开始同步本地用户模板到云端...');

    // 检查用户是否为VIP
    const vipStatus = await getVipStatus();
    if (!vipStatus.isVip) {
      log('[TemplateService] 非VIP用户，跳过本地模板上传');
      return;
    }

    // 获取本地用户模板
    const localUserTemplates = await getUserTemplates();

    if (localUserTemplates.length === 0) {
      log('[TemplateService] 本地没有用户模板需要同步');
      return;
    }

    log(`[TemplateService] 发现 ${localUserTemplates.length} 个本地用户模板，开始上传到云端...`);

    // 逐个同步到云端
    let syncedCount = 0;
    for (const template of localUserTemplates) {
      try {
        // 如果模板ID不是UUID格式，重新生成UUID
        let templateToSync = { ...template };
        if (!isValidUUID(template.id)) {
          log(`[TemplateService] 模板 ${template.title} 使用非UUID ID，重新生成UUID`);
          const newId = generateUUID();
          templateToSync.id = newId;

          // 更新本地存储中的模板ID
          await updateTemplateId(template.id, newId);
        }

        const syncSuccess = await syncUserTemplateToCloudIfVip(templateToSync);
        if (syncSuccess) {
          syncedCount++;
          // 更新本地模板的同步状态
          await updateTemplateSyncStatus(templateToSync.id, true);
          log(`[TemplateService] 成功同步模板到云端: ${template.title}`);
        }
      } catch (error) {
        logError(`[TemplateService] 同步模板失败: ${template.title}`, error);
      }
    }

    log(`[TemplateService] 本地模板云端同步完成: ${syncedCount}/${localUserTemplates.length}`);
  } catch (error) {
    logError('[TemplateService] 同步本地模板到云端失败:', error);
  }
};

/**
 * 更新模板的ID（用于将非UUID格式的ID转换为UUID格式）
 */
const updateTemplateId = async (oldId: string, newId: string): Promise<void> => {
  try {
    const userTemplates = await getUserTemplates();
    const updatedTemplates = userTemplates.map(template =>
      template.id === oldId ? { ...template, id: newId } : template
    );

    await AsyncStorage.setItem(STORAGE_KEYS.USER_TEMPLATES, JSON.stringify(updatedTemplates));

    // 如果这是默认模板，也要更新默认模板ID
    const defaultTemplateId = await getDefaultTemplateId();
    if (defaultTemplateId === oldId) {
      await setDefaultTemplateId(newId);
    }

    log(`[TemplateService] 成功更新模板ID: ${oldId} → ${newId}`);
  } catch (error) {
    logError('[TemplateService] 更新模板ID失败:', error);
    throw error;
  }
};

/**
 * 更新模板的同步状态
 */
const updateTemplateSyncStatus = async (templateId: string, isSynced: boolean): Promise<void> => {
  try {
    const userTemplates = await getUserTemplates();
    const updatedTemplates = userTemplates.map(template =>
      template.id === templateId ? { ...template, isSynced } : template
    );

    await AsyncStorage.setItem(STORAGE_KEYS.USER_TEMPLATES, JSON.stringify(updatedTemplates));
    log(`[TemplateService] 更新模板同步状态: ${templateId} → ${isSynced}`);
  } catch (error) {
    logError('[TemplateService] 更新模板同步状态失败:', error);
  }
};

/**
 * 检查字符串是否为有效的UUID格式
 */
const isValidUUID = (str: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
};

/**
 * 添加删除记录
 * @param templateId 被删除的模板ID
 * @param title 模板标题（可选，用于调试）
 */
const addDeletedTemplateRecord = async (templateId: string, title?: string): Promise<void> => {
  try {
    // 获取现有删除记录
    const existingRecords = await getDeletedTemplateRecords();

    // 检查是否已经存在该记录
    const exists = existingRecords.some(record => record.templateId === templateId);
    if (exists) {
      log(`[TemplateService] 删除记录已存在: ${templateId}`);
      return;
    }

    // 创建新的删除记录
    const newRecord: DeletedTemplateRecord = {
      templateId,
      deletedAt: Date.now(),
      title
    };

    // 添加到删除记录列表
    const updatedRecords = [...existingRecords, newRecord];

    // 保存到本地存储
    await AsyncStorage.setItem(STORAGE_KEYS.DELETED_TEMPLATES, JSON.stringify(updatedRecords));

    log(`[TemplateService] 添加删除记录: ${templateId} (${title || '未知'})`);
  } catch (error) {
    logError('[TemplateService] 添加删除记录失败:', error);
  }
};

/**
 * 获取删除记录列表
 * @returns 删除记录数组
 */
export const getDeletedTemplateRecords = async (): Promise<DeletedTemplateRecord[]> => {
  try {
    const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.DELETED_TEMPLATES);
    return jsonValue ? JSON.parse(jsonValue) : [];
  } catch (error) {
    logError('[TemplateService] 获取删除记录失败:', error);
    return [];
  }
};

/**
 * 检查模板是否已被删除
 * @param templateId 模板ID
 * @returns 是否已被删除
 */
const isTemplateDeleted = async (templateId: string): Promise<boolean> => {
  try {
    const deletedRecords = await getDeletedTemplateRecords();
    return deletedRecords.some(record => record.templateId === templateId);
  } catch (error) {
    logError('[TemplateService] 检查删除状态失败:', error);
    return false;
  }
};

/**
 * 同步删除记录到云端（VIP功能）
 * @returns 成功同步的删除记录数量
 */
export const syncDeletedRecordsToCloud = async (): Promise<number> => {
  try {
    // 检查VIP状态
    const vipStatus = await getVipStatus();
    if (!vipStatus.isVip) {
      log('[TemplateService] 非VIP用户，跳过删除记录同步');
      return 0;
    }

    // 获取本地删除记录
    const deletedRecords = await getDeletedTemplateRecords();
    if (deletedRecords.length === 0) {
      log('[TemplateService] 没有删除记录需要同步');
      return 0;
    }

    log(`[TemplateService] 开始同步 ${deletedRecords.length} 条删除记录到云端`);

    let successCount = 0;

    // 逐个同步删除记录到云端
    for (const record of deletedRecords) {
      try {
        const success = await deleteUserTemplateFromCloud(record.templateId);
        if (success) {
          successCount++;
          log(`[TemplateService] 云端删除成功: ${record.templateId}`);
        } else {
          warn(`[TemplateService] 云端删除失败: ${record.templateId}`);
        }
      } catch (error) {
        logError(`[TemplateService] 云端删除异常: ${record.templateId}`, error);
      }
    }

    // 清空本地删除记录（已同步到云端）
    if (successCount > 0) {
      await AsyncStorage.removeItem(STORAGE_KEYS.DELETED_TEMPLATES);
      log(`[TemplateService] 已清空本地删除记录，成功同步 ${successCount} 条`);
    }

    return successCount;
  } catch (error) {
    logError('[TemplateService] 同步删除记录到云端失败:', error);
    return 0;
  }
}; 