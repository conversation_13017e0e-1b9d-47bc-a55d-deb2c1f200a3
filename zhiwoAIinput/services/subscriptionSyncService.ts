/**
 * 订阅状态同步服务
 * 处理Revenue Cat事件丢失或延迟的情况，确保客户端订阅状态与实际状态一致
 */
import { supabase } from './supabaseService';
import { purchaseService } from './purchaseService';
import { store } from '@/store';
import { setVIPStatus } from '@/store/slices/authSlice';
import { log, warn, error as logError } from './logService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Purchases from 'react-native-purchases';

interface SyncStatus {
  lastSyncTime: number;
  lastServerCheck: number;
  syncInProgress: boolean;
}

class SubscriptionSyncService {
  private static instance: SubscriptionSyncService;
  private syncInterval: NodeJS.Timeout | null = null;
  private readonly SYNC_INTERVAL = 5 * 60 * 1000; // 5分钟检查一次
  private readonly STORAGE_KEY = 'subscription_sync_status';
  private readonly FORCE_SYNC_INTERVAL = 30 * 60 * 1000; // 30分钟强制同步一次

  private constructor() {}

  static getInstance(): SubscriptionSyncService {
    if (!SubscriptionSyncService.instance) {
      SubscriptionSyncService.instance = new SubscriptionSyncService();
    }
    return SubscriptionSyncService.instance;
  }

  /**
   * 启动定期同步
   */
  async startPeriodicSync(): Promise<void> {
    log('[SubscriptionSync] 启动定期订阅状态同步');
    
    // 立即执行一次同步
    await this.syncSubscriptionStatus();
    
    // 设置定期同步
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    this.syncInterval = setInterval(async () => {
      await this.syncSubscriptionStatus();
    }, this.SYNC_INTERVAL);
  }

  /**
   * 停止定期同步
   */
  stopPeriodicSync(): void {
    log('[SubscriptionSync] 停止定期订阅状态同步');
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * 主要的订阅状态同步方法
   */
  async syncSubscriptionStatus(force: boolean = false): Promise<boolean> {
    try {
      // 检查用户是否登录
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        log('[SubscriptionSync] 用户未登录，跳过同步');
        store.dispatch(setVIPStatus(false));
        return false;
      }

      const now = Date.now();
      const syncStatus = await this.getSyncStatus();

      // 检查是否需要同步
      if (!force && !this.shouldSync(syncStatus, now)) {
        return true;
      }

      // 防止重复同步
      if (syncStatus.syncInProgress) {
        log('[SubscriptionSync] 同步正在进行中，跳过');
        return true;
      }

      // 标记同步开始
      await this.setSyncStatus({ ...syncStatus, syncInProgress: true });

      log('[SubscriptionSync] 开始同步订阅状态');

      // 1. 从Revenue Cat获取客户端状态
      let clientVipStatus = false;
      let clientExpiryDate: Date | null = null;
      
      try {
        const customerInfo = await Purchases.getCustomerInfo();
        clientVipStatus = purchaseService.checkIsVIP(customerInfo);
        
        // 获取过期时间
        const activeEntitlements = customerInfo?.entitlements?.active || {};
        const vipEntitlement = Object.values(activeEntitlements).find(
          (entitlement: any) => entitlement.identifier?.toLowerCase() === 'vip'
        );
        
        if (vipEntitlement?.expirationDate) {
          clientExpiryDate = new Date(vipEntitlement.expirationDate);
        }
        
        log('[SubscriptionSync] Revenue Cat客户端状态:', {
          isVip: clientVipStatus,
          expiryDate: clientExpiryDate?.toISOString()
        });
      } catch (clientError) {
        warn('[SubscriptionSync] 获取Revenue Cat客户端状态失败:', clientError);
      }

      // 2. 从服务器获取状态
      const serverVipStatus = await this.getServerVipStatus(user.id);
      
      // 3. 比较并决定最终状态
      const finalVipStatus = this.determineFinalStatus(
        clientVipStatus, 
        serverVipStatus, 
        clientExpiryDate
      );

      // 4. 更新Redux状态
      store.dispatch(setVIPStatus(finalVipStatus));

      // 5. 如果状态不一致，尝试修复
      if (serverVipStatus !== finalVipStatus) {
        await this.fixServerStatus(user.id, finalVipStatus, clientExpiryDate);
      }

      // 更新同步状态
      await this.setSyncStatus({
        lastSyncTime: now,
        lastServerCheck: now,
        syncInProgress: false
      });

      log('[SubscriptionSync] 订阅状态同步完成:', {
        clientStatus: clientVipStatus,
        serverStatus: serverVipStatus,
        finalStatus: finalVipStatus
      });

      return true;
    } catch (error) {
      logError('[SubscriptionSync] 订阅状态同步失败:', error);
      
      // 重置同步状态
      const syncStatus = await this.getSyncStatus();
      await this.setSyncStatus({ ...syncStatus, syncInProgress: false });
      
      return false;
    }
  }

  /**
   * 从服务器获取VIP状态
   */
  private async getServerVipStatus(userId: string): Promise<boolean> {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('is_vip, vip_expires_at')
        .eq('id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // 用户资料不存在
          return false;
        }
        throw error;
      }

      const isVip = profile?.is_vip || false;
      const isExpired = profile?.vip_expires_at 
        ? new Date(profile.vip_expires_at) < new Date() 
        : false;

      const finalStatus = isVip && !isExpired;
      
      log('[SubscriptionSync] 服务器VIP状态:', {
        isVip,
        expiryDate: profile?.vip_expires_at,
        isExpired,
        finalStatus
      });

      return finalStatus;
    } catch (error) {
      logError('[SubscriptionSync] 获取服务器VIP状态失败:', error);
      return false;
    }
  }

  /**
   * 决定最终的VIP状态
   */
  private determineFinalStatus(
    clientStatus: boolean, 
    serverStatus: boolean, 
    clientExpiryDate: Date | null
  ): boolean {
    // 如果客户端检测到VIP但已过期，应该是false
    if (clientStatus && clientExpiryDate && clientExpiryDate < new Date()) {
      log('[SubscriptionSync] 客户端VIP已过期，设置为false');
      return false;
    }

    // 如果客户端和服务器都同意，使用该状态
    if (clientStatus === serverStatus) {
      return clientStatus;
    }

    // 如果不一致，优先使用服务器状态，除非客户端有更新的信息
    if (clientStatus && clientExpiryDate && clientExpiryDate > new Date()) {
      log('[SubscriptionSync] 客户端有更新的有效VIP状态，使用客户端状态');
      return true;
    }

    log('[SubscriptionSync] 状态不一致，使用服务器状态');
    return serverStatus;
  }

  /**
   * 修复服务器状态
   */
  private async fixServerStatus(
    userId: string, 
    targetStatus: boolean, 
    expiryDate: Date | null
  ): Promise<void> {
    try {
      log('[SubscriptionSync] 修复服务器VIP状态:', { targetStatus, expiryDate });
      
      const updateData: any = {
        is_vip: targetStatus,
        vip_updated_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      if (targetStatus && expiryDate) {
        updateData.vip_expires_at = expiryDate.toISOString();
      }

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId);

      if (error) {
        throw error;
      }

      log('[SubscriptionSync] 服务器VIP状态已修复');
    } catch (error) {
      logError('[SubscriptionSync] 修复服务器状态失败:', error);
    }
  }

  /**
   * 应用启动时的完整同步
   */
  async performStartupSync(): Promise<void> {
    log('[SubscriptionSync] 执行应用启动同步');
    await this.syncSubscriptionStatus(true);
  }

  /**
   * 应用恢复前台时的快速检查
   */
  async performForegroundCheck(): Promise<void> {
    const now = Date.now();
    const syncStatus = await this.getSyncStatus();
    
    // 如果超过强制同步间隔，执行完整同步
    if (now - syncStatus.lastServerCheck > this.FORCE_SYNC_INTERVAL) {
      log('[SubscriptionSync] 超过强制同步间隔，执行完整同步');
      await this.syncSubscriptionStatus(true);
    } else {
      // 否则只检查本地过期状态
      await this.checkLocalExpiry();
    }
  }

  /**
   * 检查本地过期状态
   */
  private async checkLocalExpiry(): Promise<void> {
    try {
      const currentVipStatus = store.getState().auth.isVIP;
      if (!currentVipStatus) return;

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: profile } = await supabase
        .from('profiles')
        .select('vip_expires_at')
        .eq('id', user.id)
        .single();

      if (profile?.vip_expires_at) {
        const isExpired = new Date(profile.vip_expires_at) < new Date();
        if (isExpired) {
          log('[SubscriptionSync] 检测到VIP已过期，更新状态');
          store.dispatch(setVIPStatus(false));
          
          // 更新服务器状态
          await this.fixServerStatus(user.id, false, null);
        }
      }
    } catch (error) {
      logError('[SubscriptionSync] 检查本地过期状态失败:', error);
    }
  }

  /**
   * 获取同步状态
   */
  private async getSyncStatus(): Promise<SyncStatus> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      logError('[SubscriptionSync] 获取同步状态失败:', error);
    }

    return {
      lastSyncTime: 0,
      lastServerCheck: 0,
      syncInProgress: false
    };
  }

  /**
   * 设置同步状态
   */
  private async setSyncStatus(status: SyncStatus): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(status));
    } catch (error) {
      logError('[SubscriptionSync] 设置同步状态失败:', error);
    }
  }

  /**
   * 判断是否需要同步
   */
  private shouldSync(syncStatus: SyncStatus, now: number): boolean {
    // 如果从未同步过
    if (syncStatus.lastSyncTime === 0) {
      return true;
    }

    // 如果超过了同步间隔
    if (now - syncStatus.lastSyncTime > this.SYNC_INTERVAL) {
      return true;
    }

    return false;
  }

  /**
   * 手动触发完整同步
   */
  async forceSync(): Promise<boolean> {
    log('[SubscriptionSync] 手动触发完整同步');
    return await this.syncSubscriptionStatus(true);
  }
}

// 导出单例
export const subscriptionSyncService = SubscriptionSyncService.getInstance();
export default subscriptionSyncService; 