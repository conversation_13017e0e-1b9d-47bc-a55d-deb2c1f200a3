/**
 * 系统提示词服务
 * 处理系统提示词的获取和缓存
 */
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabaseService';
import { log, error as logError } from '@/services/logService';

// 存储键名
const STORAGE_KEYS = {
  SYSTEM_PROMPT: 'knowme_system_prompt',
  SYSTEM_PROMPT_VERSION: 'knowme_system_prompt_version',
  SYSTEM_PROMPT_CACHE_TIME: 'knowme_system_prompt_cache_time',
};

// 缓存有效期（1小时）
const CACHE_DURATION = 60 * 60 * 1000;

// 系统提示词接口
export interface SystemPrompt {
  id: string;
  name: string;
  description?: string;
  prompt_content: string;
  version: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Fallback系统提示词（当云端获取失败时使用）
const FALLBACK_SYSTEM_PROMPT = `你是文本优化专家。将用户的文字优化为流畅、准确、符合语法的内容。

核心要求：
- 保持原意和语气不变
- 修正错别字和语法错误  
- 优化标点符号（中文全角，英文半角）
- 删除无意义重复词语
- 保持原始语言不变，除非明确要求翻译，否则不要改变用户输入的语言
- 直接返回优化结果，不要解释

风格要求：
\${templatePrompt}

请严格按照风格要求优化文本。`;

/**
 * 从云端获取系统提示词
 */
const fetchSystemPromptFromCloud = async (): Promise<SystemPrompt | null> => {
  try {
    log('[SystemPrompt] 开始从云端获取系统提示词...');
    
    const { data, error } = await supabase
      .from('system_prompts')
      .select('*')
      .eq('is_active', true)
      .eq('id', 'default_system_prompt')
      .single();

    if (error) {
      logError('[SystemPrompt] 获取云端系统提示词失败:', error);
      return null;
    }

    if (!data) {
      log('[SystemPrompt] 云端没有找到系统提示词');
      return null;
    }

    log('[SystemPrompt] 成功获取云端系统提示词, 版本:', data.version);
    return data as SystemPrompt;
  } catch (error) {
    logError('[SystemPrompt] 获取云端系统提示词异常:', error);
    return null;
  }
};

/**
 * 保存系统提示词到本地缓存
 */
const saveSystemPromptToCache = async (systemPrompt: SystemPrompt): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.SYSTEM_PROMPT, systemPrompt.prompt_content);
    await AsyncStorage.setItem(STORAGE_KEYS.SYSTEM_PROMPT_VERSION, systemPrompt.version.toString());
    await AsyncStorage.setItem(STORAGE_KEYS.SYSTEM_PROMPT_CACHE_TIME, Date.now().toString());
    
    log('[SystemPrompt] 系统提示词已保存到本地缓存, 版本:', systemPrompt.version);
  } catch (error) {
    logError('[SystemPrompt] 保存系统提示词到缓存失败:', error);
  }
};

/**
 * 从本地缓存获取系统提示词
 */
const getSystemPromptFromCache = async (): Promise<string | null> => {
  try {
    const cachedPrompt = await AsyncStorage.getItem(STORAGE_KEYS.SYSTEM_PROMPT);
    const cachedVersion = await AsyncStorage.getItem(STORAGE_KEYS.SYSTEM_PROMPT_VERSION);
    const cachedTime = await AsyncStorage.getItem(STORAGE_KEYS.SYSTEM_PROMPT_CACHE_TIME);

    if (!cachedPrompt || !cachedTime) {
      log('[SystemPrompt] 本地缓存中没有系统提示词');
      return null;
    }

    // 检查缓存是否过期
    const cacheAge = Date.now() - parseInt(cachedTime);
    if (cacheAge > CACHE_DURATION) {
      log('[SystemPrompt] 本地缓存已过期');
      return null;
    }

    log('[SystemPrompt] 使用本地缓存的系统提示词, 版本:', cachedVersion);
    return cachedPrompt;
  } catch (error) {
    logError('[SystemPrompt] 获取本地缓存系统提示词失败:', error);
    return null;
  }
};

/**
 * 检查是否需要更新缓存
 */
const shouldUpdateCache = async (cloudVersion: number): Promise<boolean> => {
  try {
    const cachedVersion = await AsyncStorage.getItem(STORAGE_KEYS.SYSTEM_PROMPT_VERSION);
    const cachedTime = await AsyncStorage.getItem(STORAGE_KEYS.SYSTEM_PROMPT_CACHE_TIME);

    if (!cachedVersion || !cachedTime) {
      return true;
    }

    // 检查版本是否更新
    const localVersion = parseInt(cachedVersion);
    if (cloudVersion > localVersion) {
      log('[SystemPrompt] 云端版本更新:', { cloud: cloudVersion, local: localVersion });
      return true;
    }

    // 检查缓存是否过期
    const cacheAge = Date.now() - parseInt(cachedTime);
    if (cacheAge > CACHE_DURATION) {
      log('[SystemPrompt] 缓存已过期');
      return true;
    }

    return false;
  } catch (error) {
    logError('[SystemPrompt] 检查缓存更新失败:', error);
    return true;
  }
};

/**
 * 获取系统提示词（优先从缓存，再从云端，最后使用fallback）
 * @param templatePrompt 风格模板提示词，会替换${templatePrompt}占位符
 * @returns 完整的系统提示词
 */
export const getSystemPrompt = async (templatePrompt: string): Promise<string> => {
  try {
    // 首先尝试从本地缓存获取
    const cachedPrompt = await getSystemPromptFromCache();
    if (cachedPrompt) {
      log('[SystemPrompt] 使用缓存的系统提示词');
      return cachedPrompt.replace('${templatePrompt}', templatePrompt);
    }

    // 从云端获取
    const cloudPrompt = await fetchSystemPromptFromCloud();
    if (cloudPrompt) {
      // 保存到缓存
      await saveSystemPromptToCache(cloudPrompt);
      log('[SystemPrompt] 使用云端系统提示词');
      return cloudPrompt.prompt_content.replace('${templatePrompt}', templatePrompt);
    }

    // 使用fallback
    log('[SystemPrompt] 使用fallback系统提示词');
    return FALLBACK_SYSTEM_PROMPT.replace('${templatePrompt}', templatePrompt);
  } catch (error) {
    logError('[SystemPrompt] 获取系统提示词失败，使用fallback:', error);
    return FALLBACK_SYSTEM_PROMPT.replace('${templatePrompt}', templatePrompt);
  }
};

/**
 * 强制刷新系统提示词缓存
 */
export const refreshSystemPromptCache = async (): Promise<boolean> => {
  try {
    log('[SystemPrompt] 强制刷新系统提示词缓存');
    
    const cloudPrompt = await fetchSystemPromptFromCloud();
    if (cloudPrompt) {
      await saveSystemPromptToCache(cloudPrompt);
      log('[SystemPrompt] 系统提示词缓存刷新成功');
      return true;
    }

    log('[SystemPrompt] 无法从云端获取系统提示词');
    return false;
  } catch (error) {
    logError('[SystemPrompt] 刷新系统提示词缓存失败:', error);
    return false;
  }
};

/**
 * 清除系统提示词缓存
 */
export const clearSystemPromptCache = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEYS.SYSTEM_PROMPT);
    await AsyncStorage.removeItem(STORAGE_KEYS.SYSTEM_PROMPT_VERSION);
    await AsyncStorage.removeItem(STORAGE_KEYS.SYSTEM_PROMPT_CACHE_TIME);
    
    log('[SystemPrompt] 系统提示词缓存已清除');
  } catch (error) {
    logError('[SystemPrompt] 清除系统提示词缓存失败:', error);
  }
};

/**
 * 获取当前缓存的系统提示词信息
 */
export const getCachedSystemPromptInfo = async (): Promise<{
  hasCache: boolean;
  version?: number;
  cacheTime?: Date;
  isExpired: boolean;
} | null> => {
  try {
    const cachedVersion = await AsyncStorage.getItem(STORAGE_KEYS.SYSTEM_PROMPT_VERSION);
    const cachedTime = await AsyncStorage.getItem(STORAGE_KEYS.SYSTEM_PROMPT_CACHE_TIME);
    const cachedPrompt = await AsyncStorage.getItem(STORAGE_KEYS.SYSTEM_PROMPT);

    if (!cachedPrompt || !cachedTime) {
      return {
        hasCache: false,
        isExpired: true,
      };
    }

    const cacheAge = Date.now() - parseInt(cachedTime);
    const isExpired = cacheAge > CACHE_DURATION;

    return {
      hasCache: true,
      version: cachedVersion ? parseInt(cachedVersion) : undefined,
      cacheTime: new Date(parseInt(cachedTime)),
      isExpired,
    };
  } catch (error) {
    logError('[SystemPrompt] 获取缓存信息失败:', error);
    return null;
  }
}; 