/**
 * 数据迁移服务
 * 负责将现有的AsyncStorage数据迁移到新的SQLite数据库
 */
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userIdService } from './userIdService';
import { newHistoryService } from './newHistoryService';
import { recordingFileService } from './recordingFileService';
import { log, error as logError, warn } from '@/services/logService';

// 存储键名（与原有storageService保持一致）
const STORAGE_KEYS = {
  HISTORY_RECORDS: 'knowme_history_records',
  MIGRATION_COMPLETED: 'knowme_migration_completed_v1',
};

// 原有的历史记录接口
interface LegacyHistoryRecord {
  id: string;
  timestamp: number;
  originalText: string;
  optimizedText: string;
  templateId: number | string;
  templateName: string;
  audioUri?: string;
}

/**
 * 数据迁移服务类
 */
class DataMigrationService {
  /**
   * 检查是否需要进行数据迁移
   * @returns 是否需要迁移
   */
  async needsMigration(): Promise<boolean> {
    try {
      // 检查迁移标记
      const migrationCompleted = await AsyncStorage.getItem(STORAGE_KEYS.MIGRATION_COMPLETED);
      if (migrationCompleted === 'true') {
        log('DataMigrationService: 数据迁移已完成，跳过');
        return false;
      }

      // 检查是否有AsyncStorage中的历史记录
      const legacyRecords = await AsyncStorage.getItem(STORAGE_KEYS.HISTORY_RECORDS);
      if (!legacyRecords) {
        log('DataMigrationService: 没有发现需要迁移的历史记录');
        // 标记迁移完成，避免重复检查
        await AsyncStorage.setItem(STORAGE_KEYS.MIGRATION_COMPLETED, 'true');
        return false;
      }

      const records = JSON.parse(legacyRecords);
      if (!Array.isArray(records) || records.length === 0) {
        log('DataMigrationService: 历史记录为空，无需迁移');
        await AsyncStorage.setItem(STORAGE_KEYS.MIGRATION_COMPLETED, 'true');
        return false;
      }

      log(`DataMigrationService: 发现 ${records.length} 条历史记录需要迁移`);
      return true;
    } catch (error) {
      logError('DataMigrationService: 检查迁移需求失败:', error);
      return false;
    }
  }

  /**
   * 执行数据迁移
   * @returns 迁移是否成功
   */
  async performMigration(): Promise<boolean> {
    try {
      log('DataMigrationService: 开始执行数据迁移');

      // 确保用户ID服务已初始化
      await userIdService.initialize();
      const currentUserId = await userIdService.getCurrentUserId();
      log(`DataMigrationService: 当前用户ID: ${currentUserId}`);

      // 获取AsyncStorage中的历史记录
      const legacyRecordsStr = await AsyncStorage.getItem(STORAGE_KEYS.HISTORY_RECORDS);
      if (!legacyRecordsStr) {
        log('DataMigrationService: 没有找到需要迁移的数据');
        await AsyncStorage.setItem(STORAGE_KEYS.MIGRATION_COMPLETED, 'true');
        return true;
      }

      const legacyRecords: LegacyHistoryRecord[] = JSON.parse(legacyRecordsStr);
      log(`DataMigrationService: 开始迁移 ${legacyRecords.length} 条记录`);

      let successCount = 0;
      let failureCount = 0;

      // 逐条迁移记录
      for (const legacyRecord of legacyRecords) {
        try {
          // 转换为新格式
          const newRecord = {
            id: legacyRecord.id,
            timestamp: legacyRecord.timestamp,
            originalText: legacyRecord.originalText,
            optimizedText: legacyRecord.optimizedText,
            templateId: String(legacyRecord.templateId),
            templateName: legacyRecord.templateName,
            audioUri: legacyRecord.audioUri,
          };

          // 如果有音频文件，修复路径
          if (newRecord.audioUri) {
            try {
              newRecord.audioUri = await recordingFileService.fixRecordingPath(newRecord.audioUri, currentUserId);
              log(`DataMigrationService: 修复音频路径 ${legacyRecord.audioUri} -> ${newRecord.audioUri}`);
            } catch (pathError) {
              warn(`DataMigrationService: 修复音频路径失败 (recordId: ${legacyRecord.id}):`, pathError);
              // 继续迁移，但不包含音频
              newRecord.audioUri = undefined;
            }
          }

          // 保存到新的SQLite数据库
          const success = await newHistoryService.saveHistoryRecord(newRecord, currentUserId);
          if (success) {
            successCount++;
            log(`DataMigrationService: 成功迁移记录 ${legacyRecord.id}`);
          } else {
            failureCount++;
            warn(`DataMigrationService: 迁移记录失败 ${legacyRecord.id}`);
          }
        } catch (recordError) {
          failureCount++;
          logError(`DataMigrationService: 迁移单条记录失败 (recordId: ${legacyRecord.id}):`, recordError);
        }
      }

      log(`DataMigrationService: 迁移完成 - 成功: ${successCount}, 失败: ${failureCount}`);

      // 如果大部分记录迁移成功，标记迁移完成
      if (successCount > 0 && successCount >= failureCount) {
        await AsyncStorage.setItem(STORAGE_KEYS.MIGRATION_COMPLETED, 'true');
        log('DataMigrationService: 数据迁移标记为完成');

        // 可选：备份原始数据而不是删除
        try {
          const backupKey = `${STORAGE_KEYS.HISTORY_RECORDS}_backup_${Date.now()}`;
          await AsyncStorage.setItem(backupKey, legacyRecordsStr);
          log(`DataMigrationService: 原始数据已备份到 ${backupKey}`);
          
          // 清除原始数据
          await AsyncStorage.removeItem(STORAGE_KEYS.HISTORY_RECORDS);
          log('DataMigrationService: 原始AsyncStorage数据已清除');
        } catch (backupError) {
          warn('DataMigrationService: 备份原始数据失败:', backupError);
        }

        return true;
      } else {
        logError(`DataMigrationService: 迁移失败过多，不标记为完成 (成功: ${successCount}, 失败: ${failureCount})`);
        return false;
      }
    } catch (error) {
      logError('DataMigrationService: 执行数据迁移失败:', error);
      return false;
    }
  }

  /**
   * 自动检查并执行迁移（如果需要）
   * @returns 迁移结果
   */
  async autoMigrate(): Promise<{ needed: boolean; success: boolean }> {
    try {
      const needed = await this.needsMigration();
      if (!needed) {
        return { needed: false, success: true };
      }

      const success = await this.performMigration();
      return { needed: true, success };
    } catch (error) {
      logError('DataMigrationService: 自动迁移失败:', error);
      return { needed: true, success: false };
    }
  }

  /**
   * 强制重新迁移（用于测试或修复）
   * @returns 迁移是否成功
   */
  async forceMigration(): Promise<boolean> {
    try {
      log('DataMigrationService: 强制重新迁移');
      
      // 清除迁移标记
      await AsyncStorage.removeItem(STORAGE_KEYS.MIGRATION_COMPLETED);
      
      // 执行迁移
      return await this.performMigration();
    } catch (error) {
      logError('DataMigrationService: 强制迁移失败:', error);
      return false;
    }
  }

  /**
   * 获取迁移状态信息
   * @returns 迁移状态信息
   */
  async getMigrationStatus(): Promise<{
    migrationCompleted: boolean;
    legacyRecordsCount: number;
    sqliteRecordsCount: number;
  }> {
    try {
      // 检查迁移标记
      const migrationCompleted = await AsyncStorage.getItem(STORAGE_KEYS.MIGRATION_COMPLETED) === 'true';
      
      // 检查AsyncStorage中的记录数量
      let legacyRecordsCount = 0;
      try {
        const legacyRecordsStr = await AsyncStorage.getItem(STORAGE_KEYS.HISTORY_RECORDS);
        if (legacyRecordsStr) {
          const legacyRecords = JSON.parse(legacyRecordsStr);
          legacyRecordsCount = Array.isArray(legacyRecords) ? legacyRecords.length : 0;
        }
      } catch (parseError) {
        warn('DataMigrationService: 解析AsyncStorage记录失败:', parseError);
      }
      
      // 检查SQLite中的记录数量
      let sqliteRecordsCount = 0;
      try {
        await userIdService.initialize();
        sqliteRecordsCount = await newHistoryService.getRealHistoryRecordsCount();
      } catch (sqliteError) {
        warn('DataMigrationService: 获取SQLite记录数量失败:', sqliteError);
      }
      
      return {
        migrationCompleted,
        legacyRecordsCount,
        sqliteRecordsCount,
      };
    } catch (error) {
      logError('DataMigrationService: 获取迁移状态失败:', error);
      return {
        migrationCompleted: false,
        legacyRecordsCount: 0,
        sqliteRecordsCount: 0,
      };
    }
  }

  /**
   * 清理迁移相关的临时数据
   */
  async cleanupMigrationData(): Promise<void> {
    try {
      // 清理备份数据（保留最近的一个备份）
      const allKeys = await AsyncStorage.getAllKeys();
      const backupKeys = allKeys.filter(key => key.startsWith(`${STORAGE_KEYS.HISTORY_RECORDS}_backup_`));
      
      if (backupKeys.length > 1) {
        // 按时间戳排序，保留最新的
        backupKeys.sort();
        const keysToRemove = backupKeys.slice(0, -1);
        
        for (const key of keysToRemove) {
          await AsyncStorage.removeItem(key);
          log(`DataMigrationService: 清理旧备份 ${key}`);
        }
      }
      
      log('DataMigrationService: 迁移数据清理完成');
    } catch (error) {
      logError('DataMigrationService: 清理迁移数据失败:', error);
    }
  }
}

// 导出单例实例
export const dataMigrationService = new DataMigrationService();
