/**
 * 模板同步服务
 * 处理本地模板与云端数据库模板的同步
 */
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, authService } from './supabaseService';
import { log, error as logError, warn } from '@/services/logService';

// 存储键名
const STORAGE_KEYS = {
  SYSTEM_TEMPLATES: 'knowme_system_templates',
  SYSTEM_TEMPLATES_VERSION: 'knowme_system_templates_version',
  LAST_SYNC_TIME: 'knowme_templates_last_sync',
};

// 云端模板数据接口
export interface CloudTemplate {
  id: string;
  user_id: string | null;
  name: string;
  description: string;
  name_key?: string;
  description_key?: string;
  prompt_text: string;
  is_system: boolean;
  is_active: boolean;
  color: string;
  icon: string;
  position: number;
  is_vip_only?: boolean;
  category?: string;
  semantic_id?: string;
  created_at: string;
  updated_at: string;
}

// 本地模板数据接口
export interface LocalTemplate {
  id: string;
  title: string;
  description: string;
  name_key?: string;
  description_key?: string;
  prompt: string;
  isSystem: boolean;
  isDefault?: boolean;
  color?: string;
  borderColor?: string;
  backgroundColor?: string;
  position?: number;
  isVipOnly?: boolean;
  category?: string;
  semantic_id?: string;
  icon?: string; // 模板图标（emoji或图标名）
  isSynced?: boolean; // 是否已同步到云端（仅对自定义模板有意义）
}

// 颜色映射表，将云端颜色名映射为本地颜色值
const COLOR_MAPPING: Record<string, {color: string, borderColor: string, backgroundColor: string}> = {
  'blue': {
    color: '#3B82F6',
    borderColor: '#3B82F6',
    backgroundColor: '#EFF6FF'
  },
  'green': {
    color: '#10B981',
    borderColor: '#10B981',
    backgroundColor: '#ECFDF5'
  },
  'purple': {
    color: '#8B5CF6',
    borderColor: '#8B5CF6',
    backgroundColor: '#F5F3FF'
  },
  'orange': {
    color: '#F59E0B',
    borderColor: '#F59E0B',
    backgroundColor: '#FEF3C7'
  },
  'pink': {
    color: '#EC4899',
    borderColor: '#EC4899',
    backgroundColor: '#FCE7F3'
  },
  'indigo': {
    color: '#6366F1',
    borderColor: '#6366F1',
    backgroundColor: '#EEF2FF'
  },
  'amber': {
    color: '#F97316',
    borderColor: '#F97316',
    backgroundColor: '#FFF7ED'
  },
  'sky': {
    color: '#0EA5E9',
    borderColor: '#0EA5E9',
    backgroundColor: '#F0F9FF'
  },
  'teal': {
    color: '#14B8A6',
    borderColor: '#14B8A6',
    backgroundColor: '#F0FDFA'
  }
};

// 描述映射表，仅作为兜底方案使用（当云端description字段为空时）
const DESCRIPTION_MAPPING: Record<string, string> = {
  '发送邮件模板': '将文本整理成正式的商务邮件格式，包含称呼、正文、结束语等标准结构。',
  '富含emoji格式': '为文本添加生动有趣的表情符号，使内容更加活泼形象。',
  '发送给领导': '将内容整理成简明扼要的汇报格式，突出重点，适合向上级汇报。',
  '发送给朋友': '将内容整理成轻松随意的聊天风格，适合与朋友分享。',
  '发送给恋人': '将内容整理成温情浪漫的表达方式，适合与恋人交流。',
  '邮件格式': '将文本整理成正式的商务邮件格式，包含称呼、正文、结束语等标准结构。',
  'Emoji风格': '为文本添加生动有趣的表情符号，使内容更加活泼形象。',
  '领导汇报': '将内容整理成简明扼要的汇报格式，突出重点，适合向上级汇报。',
  '朋友聊天': '将内容整理成轻松随意的聊天风格，适合与朋友分享。',
  '恋人表达': '将内容整理成温情浪漫的表达方式，适合与恋人交流。',
  '学术论文': '将内容整理成严谨专业的学术风格，适合用于论文和研究。',
  '演讲稿': '将内容整理成具有感染力的演讲风格，适合公开演讲。',
  '新闻报道': '将内容整理成客观公正的新闻风格，注重事实描述。',
  '故事叙述': '将内容整理成生动有趣的故事风格，增强叙事感。'
};

/**
 * 根据主色生成对应的浅色背景
 */
const generateBackgroundColor = (hexColor: string): string => {
  // 移除#号
  const color = hexColor.replace('#', '');
  
  // 解析RGB值
  const r = parseInt(color.substring(0, 2), 16);
  const g = parseInt(color.substring(2, 4), 16);
  const b = parseInt(color.substring(4, 6), 16);
  
  // 生成浅色版本 (将RGB值向255靠近，透明度约85%)
  const lightR = Math.round(r + (255 - r) * 0.85);
  const lightG = Math.round(g + (255 - g) * 0.85);
  const lightB = Math.round(b + (255 - b) * 0.85);
  
  // 转换回十六进制
  const toHex = (num: number) => num.toString(16).padStart(2, '0').toUpperCase();
  
  return `#${toHex(lightR)}${toHex(lightG)}${toHex(lightB)}`;
};

/**
 * 将云端模板转换为本地模板格式
 */
const transformCloudToLocal = (cloudTemplate: CloudTemplate): LocalTemplate => {
  // 直接使用数据库中的颜色值，如果是简单颜色名则使用映射表
  let colorConfig;
  if (cloudTemplate.color.startsWith('#')) {
    // 如果是完整的十六进制颜色值，直接使用
    colorConfig = {
      color: cloudTemplate.color,
      borderColor: cloudTemplate.color,
      backgroundColor: generateBackgroundColor(cloudTemplate.color)
    };
  } else {
    // 如果是简单颜色名，使用映射表
    colorConfig = COLOR_MAPPING[cloudTemplate.color] || COLOR_MAPPING['blue'];
  }
  
  // 优先使用云端的description字段，如果为空则使用映射表或默认值
  const description = cloudTemplate.description || 
                     DESCRIPTION_MAPPING[cloudTemplate.name] || 
                     '自定义风格模板';
  
  const transformed = {
    id: cloudTemplate.id,
    title: cloudTemplate.name,
    description,
    name_key: cloudTemplate.name_key,
    description_key: cloudTemplate.description_key,
    prompt: cloudTemplate.prompt_text,
    isSystem: cloudTemplate.is_system,
    color: colorConfig.color,
    borderColor: colorConfig.borderColor,
    backgroundColor: colorConfig.backgroundColor,
    position: cloudTemplate.position,
    isVipOnly: cloudTemplate.is_vip_only || false,
    category: cloudTemplate.category,
    semantic_id: cloudTemplate.semantic_id,
    icon: cloudTemplate.icon, // 添加图标字段
    isSynced: true // 从云端获取的模板都标记为已同步
  };

  // 添加icon字段的调试日志
  log(`[TemplateSync] 模板 ${cloudTemplate.name} 图标信息:`, {
    originalIcon: cloudTemplate.icon,
    transformedIcon: transformed.icon,
    iconType: typeof cloudTemplate.icon,
    iconLength: cloudTemplate.icon ? cloudTemplate.icon.length : 0,
    allCloudKeys: Object.keys(cloudTemplate),
    allTransformedKeys: Object.keys(transformed)
  });
  
  log(`[TemplateSync] 转换模板 ${cloudTemplate.name}:`, {
    id: transformed.id,
    title: transformed.title,
    description: transformed.description,
    nameKey: transformed.name_key,
    descriptionKey: transformed.description_key,
    promptLength: transformed.prompt.length,
    promptPreview: transformed.prompt.substring(0, 100)
  });
  
  return transformed;
};

/**
 * 从云端获取系统模板
 */
export const fetchSystemTemplatesFromCloud = async (): Promise<LocalTemplate[]> => {
  try {
    log('[TemplateSync] 开始从云端获取系统模板...');
    
    const { data, error } = await supabase
      .from('style_templates')
      .select('id, user_id, name, description, name_key, description_key, prompt_text, is_system, is_active, color, icon, position, is_vip_only, category, semantic_id, created_at, updated_at')
      .eq('is_system', true)
      .eq('is_active', true)
      .order('position', { ascending: true });

    if (error) {
      logError('[TemplateSync] 获取云端系统模板失败:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      log('[TemplateSync] 云端没有找到系统模板');
      return [];
    }

    const localTemplates = data.map(transformCloudToLocal);
    log('[TemplateSync] 成功获取云端系统模板:', localTemplates.length, '个');
    
    return localTemplates;
  } catch (error) {
    logError('[TemplateSync] 获取云端系统模板失败:', error);
    throw error;
  }
};

/**
 * 从云端获取用户自定义模板
 */
export const fetchUserTemplatesFromCloud = async (): Promise<LocalTemplate[]> => {
  try {
    const { data: userData, error: authError } = await authService.getCurrentUser();
    if (authError || !userData.user) {
      log('[TemplateSync] 用户未登录，跳过用户模板同步');
      return [];
    }
    const userId = userData.user.id;

    log('[TemplateSync] 开始从云端获取用户模板...');
    
    const { data, error } = await supabase
      .from('style_templates')
      .select('id, user_id, name, description, name_key, description_key, prompt_text, is_system, is_active, color, icon, position, is_vip_only, category, semantic_id, created_at, updated_at')
      .eq('user_id', userId)
      .eq('is_system', false)
      .eq('is_active', true)
      .order('position', { ascending: true });

    if (error) {
      logError('[TemplateSync] 获取云端用户模板失败:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      log('[TemplateSync] 云端没有找到用户模板');
      return [];
    }

    const localTemplates = data.map(transformCloudToLocal);
    log('[TemplateSync] 成功获取云端用户模板:', localTemplates.length, '个');
    
    return localTemplates;
  } catch (error) {
    logError('[TemplateSync] 获取云端用户模板失败:', error);
    throw error;
  }
};

/**
 * 保存用户自定义模板到云端
 */
export const saveUserTemplateToCloud = async (template: LocalTemplate): Promise<boolean> => {
  try {
    const { data: userData, error: authError } = await authService.getCurrentUser();
    if (authError || !userData.user) {
      log('[TemplateSync] 用户未登录，无法同步用户模板到云端');
      return false;
    }
    const userId = userData.user.id;

    log(`[TemplateSync] 开始同步用户模板到云端: ${template.title}`);

    // 将本地模板转换为云端格式
    const cloudTemplate = {
      id: template.id,
      user_id: userId,
      name: template.title,
      description: template.description, // 保存描述字段
      prompt_text: template.prompt,
      is_system: false,
      is_active: true,
      color: template.color || '#3B82F6', // 保持完整的十六进制颜色值
      icon: template.icon || '👤', // 用户模板默认图标
      position: template.position || 999, // 用户模板排在后面
    };

    // 检查模板是否已存在
    const { data: existingData, error: checkError } = await supabase
      .from('style_templates')
      .select('id')
      .eq('id', template.id)
      .eq('user_id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = not found
      logError('[TemplateSync] 检查用户模板存在性失败:', checkError);
      throw checkError;
    }

    let result;
    if (existingData) {
      // 更新现有模板
      log(`[TemplateSync] 更新云端用户模板: ${template.title}`);
      result = await supabase
        .from('style_templates')
        .update(cloudTemplate)
        .eq('id', template.id)
        .eq('user_id', userId);
    } else {
      // 创建新模板
      log(`[TemplateSync] 创建云端用户模板: ${template.title}`);
      result = await supabase
        .from('style_templates')
        .insert(cloudTemplate);
    }

    if (result.error) {
      logError('[TemplateSync] 同步用户模板到云端失败:', result.error);
      throw result.error;
    }

    log(`[TemplateSync] 成功同步用户模板到云端: ${template.title}`);
    return true;
  } catch (error) {
    logError('[TemplateSync] 同步用户模板到云端失败:', error);
    return false;
  }
};

/**
 * 从云端删除用户自定义模板
 */
export const deleteUserTemplateFromCloud = async (templateId: string): Promise<boolean> => {
  try {
    const { data: userData, error: authError } = await authService.getCurrentUser();
    if (authError || !userData.user) {
      log('[TemplateSync] 用户未登录，无法从云端删除用户模板');
      return false;
    }
    const userId = userData.user.id;

    log(`[TemplateSync] 开始从云端删除用户模板: ${templateId}`);

    // 软删除：将is_active设为false
    const { error } = await supabase
      .from('style_templates')
      .update({ is_active: false })
      .eq('id', templateId)
      .eq('user_id', userId)
      .eq('is_system', false);

    if (error) {
      logError('[TemplateSync] 从云端删除用户模板失败:', error);
      throw error;
    }

    log(`[TemplateSync] 成功从云端删除用户模板: ${templateId}`);
    return true;
  } catch (error) {
    logError('[TemplateSync] 从云端删除用户模板失败:', error);
    return false;
  }
};

/**
 * 同步用户自定义模板（VIP功能）
 * 从云端获取用户模板并与本地模板合并
 */
export const syncUserTemplates = async (): Promise<LocalTemplate[]> => {
  try {
    log('[TemplateSync] 开始同步用户自定义模板...');
    
    // 检查用户是否为VIP
    const { getVipStatus } = await import('./storageService');
    const vipStatus = await getVipStatus();
    
    if (!vipStatus.isVip) {
      log('[TemplateSync] 非VIP用户，跳过云端模板同步');
      return [];
    }

    // 获取云端用户模板
    const cloudTemplates = await fetchUserTemplatesFromCloud();
    
    // 获取本地用户模板
    const { getUserTemplates } = await import('./templateService');
    const localTemplates = await getUserTemplates();
    
    // 获取本地删除记录，过滤掉已删除的模板
    const { getDeletedTemplateRecords } = await import('./templateService');
    const deletedRecords = await getDeletedTemplateRecords();
    const deletedIds = new Set(deletedRecords.map((record: any) => record.templateId));
    
    // 过滤掉已删除的云端模板
    const validCloudTemplates = cloudTemplates.filter(template => !deletedIds.has(template.id));
    
    if (validCloudTemplates.length < cloudTemplates.length) {
      log(`[TemplateSync] 已过滤 ${cloudTemplates.length - validCloudTemplates.length} 个已删除的模板`);
    }

    // 创建模板合并逻辑
    const mergedTemplatesMap = new Map<string, LocalTemplate>();
    
    // 第一步：添加本地模板（本地优先）
    localTemplates.forEach(localTemplate => {
      const convertedTemplate: LocalTemplate = {
        id: localTemplate.id,
        title: localTemplate.title,
        description: localTemplate.description,
        name_key: localTemplate.name_key,
        description_key: localTemplate.description_key,
        prompt: localTemplate.prompt,
        isSystem: localTemplate.isSystem,
        color: localTemplate.color,
        borderColor: localTemplate.borderColor,
        backgroundColor: localTemplate.backgroundColor,
        position: 999, // 用户模板默认位置
        isVipOnly: localTemplate.isVipOnly,
        category: localTemplate.category,
        semantic_id: localTemplate.semanticId, // 使用正确的字段名
        icon: localTemplate.icon,
        isSynced: localTemplate.isSynced || false // 保持本地的同步状态
      };
      mergedTemplatesMap.set(localTemplate.id, convertedTemplate);
      log(`[TemplateSync] 保留本地模板: ${localTemplate.title}`);
    });
    
    // 第二步：添加云端模板（如果本地没有相同ID的模板）
    validCloudTemplates.forEach(cloudTemplate => {
      if (!mergedTemplatesMap.has(cloudTemplate.id)) {
        // 云端模板在本地不存在，添加到合并结果
        const mergedTemplate = {
          ...cloudTemplate,
          isSynced: true // 从云端获取的模板都是已同步状态
        };
        mergedTemplatesMap.set(cloudTemplate.id, mergedTemplate);
        log(`[TemplateSync] 添加云端模板: ${cloudTemplate.title}`);
      } else {
        log(`[TemplateSync] 跳过云端模板(本地已存在): ${cloudTemplate.title}`);
      }
    });
    
    // 转换为数组并保存到本地存储
    const mergedTemplates = Array.from(mergedTemplatesMap.values());
    
    // 转换回Template格式保存到本地
    const templatesForStorage = mergedTemplates.map(template => ({
      id: template.id,
      title: template.title,
      description: template.description,
      name_key: template.name_key,
      description_key: template.description_key,
      prompt: template.prompt,
      isSystem: template.isSystem,
      color: template.color,
      borderColor: template.borderColor,
      backgroundColor: template.backgroundColor,
      isSynced: template.isSynced,
      category: template.category,
      isVipOnly: template.isVipOnly,
      semanticId: template.semantic_id,
      icon: template.icon
    }));
    
    await AsyncStorage.setItem('knowme_user_templates', JSON.stringify(templatesForStorage));
    
    log(`[TemplateSync] 模板合并完成: 本地${localTemplates.length}个 + 云端${validCloudTemplates.length}个 = 合并后${mergedTemplates.length}个`);
    return mergedTemplates;
  } catch (error) {
    logError('[TemplateSync] 同步用户模板失败:', error);
    return [];
  }
};

/**
 * 保存系统模板到本地存储
 */
export const saveSystemTemplatesToLocal = async (templates: LocalTemplate[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.SYSTEM_TEMPLATES, JSON.stringify(templates));
    await AsyncStorage.setItem(STORAGE_KEYS.LAST_SYNC_TIME, Date.now().toString());
    log('[TemplateSync] 系统模板已保存到本地存储');
  } catch (error) {
    logError('[TemplateSync] 保存系统模板到本地存储失败:', error);
    throw error;
  }
};

/**
 * 从本地存储获取系统模板
 */
export const getSystemTemplatesFromLocal = async (): Promise<LocalTemplate[]> => {
  try {
    const templatesJson = await AsyncStorage.getItem(STORAGE_KEYS.SYSTEM_TEMPLATES);
    if (!templatesJson) {
      log('[TemplateSync] 本地没有系统模板数据');
      return [];
    }
    
    const templates = JSON.parse(templatesJson);
    log('[TemplateSync] 从本地获取系统模板:', templates.length, '个');
    return templates;
  } catch (error) {
    logError('[TemplateSync] 从本地获取系统模板失败:', error);
    return [];
  }
};

/**
 * 检查是否需要同步（超过1分钟或没有本地数据）
 */
export const shouldSync = async (forceSync: boolean = false): Promise<boolean> => {
  try {
    // 如果强制同步，直接返回true
    if (forceSync) {
      log('[TemplateSync] 强制同步模式，需要同步');
      return true;
    }
    
    const lastSyncTime = await AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC_TIME);
    if (!lastSyncTime) {
      log('[TemplateSync] 从未同步过，需要同步');
      return true; // 从未同步过
    }
    
    const timeDiff = Date.now() - parseInt(lastSyncTime);
    const oneMinute = 1 * 60 * 1000; // 1分钟的毫秒数（缩短缓存时间）
    
    const needSync = timeDiff > oneMinute;
    log(`[TemplateSync] 距离上次同步 ${Math.round(timeDiff / 1000)} 秒，${needSync ? '需要' : '不需要'}同步`);
    
    return needSync;
  } catch (error) {
    logError('[TemplateSync] 检查同步状态失败:', error);
    return true; // 出错时触发同步
  }
};

/**
 * 同步系统模板
 * 优先使用云端数据，如果获取失败则使用本地缓存
 */
export const syncSystemTemplates = async (forceSync: boolean = false): Promise<LocalTemplate[]> => {
  try {
    log('[TemplateSync] 开始同步系统模板...');
    // 先读取本地缓存
    const localTemplates = await getSystemTemplatesFromLocal();
    if (localTemplates.length > 0) {
      log('[TemplateSync] 本地缓存有系统模板，优先返回本地数据');
      // 异步后台刷新云端（不阻塞UI）
      if (!forceSync) {
        syncSystemTemplates(true).catch(e => log('[TemplateSync] 后台云端刷新失败:', e));
      }
      return localTemplates;
    }
    // 如果本地没有，才尝试云端
    // 检查是否需要同步
    const needSync = await shouldSync(forceSync);
    if (!needSync && localTemplates.length > 0) {
      log('[TemplateSync] 距离上次同步不到1分钟且有本地数据，使用本地缓存');
      return localTemplates;
    }
    try {
      // 尝试从云端获取最新的系统模板
      const cloudTemplates = await fetchSystemTemplatesFromCloud();
      if (cloudTemplates.length > 0) {
        // 保存到本地
        await saveSystemTemplatesToLocal(cloudTemplates);
        log('[TemplateSync] 系统模板同步成功');
        return cloudTemplates;
      }
    } catch (cloudError) {
      warn('[TemplateSync] 从云端获取系统模板失败，尝试使用本地缓存:', cloudError);
    }
    // 如果云端获取失败，使用本地缓存
    if (localTemplates.length > 0) {
      log('[TemplateSync] 使用本地缓存的系统模板');
      return localTemplates;
    }
    // 如果本地也没有，返回空数组（兜底处理）
    warn('[TemplateSync] 云端和本地都没有系统模板数据');
    return [];
  } catch (error) {
    logError('[TemplateSync] 同步系统模板失败:', error);
    // 优化：如果本地有缓存，直接返回本地缓存
    try {
      const localTemplates = await getSystemTemplatesFromLocal();
      if (localTemplates.length > 0) {
        log('[TemplateSync] catch分支-返回本地缓存');
        return localTemplates;
      }
    } catch {}
    throw error;
  }
};

/**
 * 根据模板ID获取模板的提示词
 */
export const getTemplatePromptById = async (templateId: string): Promise<string> => {
  try {
    log('[TemplateSync] 获取模板提示词，ID:', templateId);
    
    // 先获取系统模板
    const systemTemplates = await getSystemTemplatesFromLocal();
    const systemTemplate = systemTemplates.find(t => t.id === templateId);
    
    if (systemTemplate) {
      log('[TemplateSync] 找到系统模板提示词');
      return systemTemplate.prompt;
    }
    
    // 如果系统模板中没有找到，尝试从用户模板中查找（从templateService获取）
    // 这里我们需要导入templateService来获取用户模板
    const { getUserTemplates } = await import('./templateService');
    const userTemplates = await getUserTemplates();
    const userTemplate = userTemplates.find(t => t.id === templateId);
    
    if (userTemplate) {
      log('[TemplateSync] 找到用户模板提示词');
      return userTemplate.prompt;
    }
    
    warn('[TemplateSync] 未找到模板ID对应的提示词:', templateId);
    return '';
    
  } catch (error) {
    logError('[TemplateSync] 获取模板提示词失败:', error);
    return '';
  }
};

/**
 * 初始化模板数据
 * 应用启动时调用，确保有基础的模板数据
 */
export const initializeTemplates = async (): Promise<void> => {
  try {
    log('[TemplateSync] 初始化模板数据...');
    
    // 每次应用启动都尝试同步模板（1分钟间隔控制）
    const templates = await syncSystemTemplates();
    
    if (templates.length === 0) {
      warn('[TemplateSync] 未获取到任何模板数据，可能需要检查网络连接或数据库');
    } else {
      log(`[TemplateSync] 模板数据初始化完成，共 ${templates.length} 个模板`);
      
      // 确保默认模板存在
      try {
        const { ensureDefaultTemplateExists } = await import('./templateService');
        await ensureDefaultTemplateExists();
      } catch (defaultTemplateError) {
        warn('[TemplateSync] 确保默认模板存在失败:', defaultTemplateError);
      }
      
      // 注意：不在应用启动时自动初始化模板偏好设置
      // 模板偏好设置应该只在用户完成引导问卷时初始化，避免在非引导场景下被意外调用
    }
  } catch (error) {
    logError('[TemplateSync] 模板数据初始化失败:', error);
    // 不抛出错误，允许应用继续运行
  }
};

/**
 * 强制刷新模板数据
 * 忽略缓存，直接从云端获取最新的模板数据
 */
export const forceRefreshTemplates = async (): Promise<{
  systemTemplates: LocalTemplate[];
  userTemplates: LocalTemplate[];
}> => {
  try {
    log('[TemplateSync] 开始强制刷新模板数据...');
    
    // 1. 强制同步系统模板
    const systemTemplates = await syncSystemTemplates(true);
    log(`[TemplateSync] 强制刷新系统模板完成: ${systemTemplates.length}个`);
    
    // 2. 强制同步用户模板（如果用户是VIP）
    let userTemplates: LocalTemplate[] = [];
    try {
      const { getVipStatus } = await import('./storageService');
      const vipStatus = await getVipStatus();
      
      if (vipStatus.isVip) {
        userTemplates = await syncUserTemplates();
        log(`[TemplateSync] 强制刷新用户模板完成: ${userTemplates.length}个`);
      } else {
        log('[TemplateSync] 非VIP用户，跳过用户模板刷新');
      }
    } catch (userSyncError) {
      warn('[TemplateSync] 强制刷新用户模板失败:', userSyncError);
    }
    
    // 注意：不在强制刷新时重新初始化模板偏好设置
    // 模板偏好设置应该只在用户完成引导问卷时初始化，不应该在模板刷新时被调用
    log('[TemplateSync] 强制刷新模板数据完成，跳过偏好设置初始化');
    
    log('[TemplateSync] 强制刷新模板数据完成');
    return {
      systemTemplates,
      userTemplates
    };
  } catch (error) {
    logError('[TemplateSync] 强制刷新模板数据失败:', error);
    throw error;
  }
};

/**
 * 清除模板缓存
 * 删除本地缓存的模板数据和同步时间戳
 */
export const clearTemplateCache = async (): Promise<void> => {
  try {
    log('[TemplateSync] 清除模板缓存...');
    
    // 删除系统模板缓存
    await AsyncStorage.removeItem(STORAGE_KEYS.SYSTEM_TEMPLATES);
    
    // 删除同步时间戳
    await AsyncStorage.removeItem(STORAGE_KEYS.LAST_SYNC_TIME);
    
    log('[TemplateSync] 模板缓存已清除');
  } catch (error) {
    logError('[TemplateSync] 清除模板缓存失败:', error);
    throw error;
  }
}; 