import supabaseService from './supabaseService';
import { saveUserSettings, getUserSettings } from './storageService';
import { log, error as logError, warn } from '@/services/logService';

interface SyncQueueItem {
  userId: string;
  fastMode: boolean;
  timestamp: number;
  retryCount: number;
}

class FastModeSyncService {
  private syncQueue: SyncQueueItem[] = [];
  private isSyncing = false;
  private maxRetries = 3;
  private retryDelay = 2000; // 2秒

  /**
   * 添加同步任务到队列
   */
  async queueSync(userId: string, fastMode: boolean) {
    // 移除相同用户的旧任务，保留最新的设置
    this.syncQueue = this.syncQueue.filter(item => item.userId !== userId);
    
    // 添加新任务
    this.syncQueue.push({
      userId,
      fastMode,
      timestamp: Date.now(),
      retryCount: 0
    });

    log(`[FastModeSync] 已将极速模式同步任务添加到队列: ${fastMode} (用户: ${userId})`);
    
    // 启动同步处理
    this.processSyncQueue();
  }

  /**
   * 处理同步队列
   */
  private async processSyncQueue() {
    if (this.isSyncing || this.syncQueue.length === 0) {
      return;
    }

    this.isSyncing = true;

    try {
      while (this.syncQueue.length > 0) {
        const item = this.syncQueue.shift();
        if (!item) continue;

        const success = await this.syncToCloud(item);
        
        if (!success && item.retryCount < this.maxRetries) {
          // 重试：增加重试次数并重新加入队列
          item.retryCount++;
          this.syncQueue.unshift(item);
          
          log(`[FastModeSync] 同步失败，${this.retryDelay}ms后重试 (${item.retryCount}/${this.maxRetries})`);
          
          // 等待重试延迟
          await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        } else if (!success) {
          warn(`[FastModeSync] 同步失败，已达到最大重试次数，放弃同步`);
        }
      }
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * 同步到云端
   */
  private async syncToCloud(item: SyncQueueItem): Promise<boolean> {
    try {
      const { data: sessionData } = await supabaseService.supabase.auth.getSession();
      
      if (!sessionData?.session?.user?.id) {
        log(`[FastModeSync] 用户未登录，跳过云端同步`);
        return true; // 未登录不算失败
      }

      if (sessionData.session.user.id !== item.userId) {
        log(`[FastModeSync] 用户已切换，跳过旧用户的同步任务`);
        return true; // 用户切换不算失败
      }

      await supabaseService.modelService.saveUserModelSettings(
        item.userId,
        { fast_mode: item.fastMode }
      );

      log(`[FastModeSync] 极速模式设置已成功同步到云端: ${item.fastMode}`);
      return true;
    } catch (error) {
      logError(`[FastModeSync] 云端同步失败:`, error);
      return false;
    }
  }

  /**
   * 强制同步当前本地设置到云端
   */
  async forceSyncFromLocal() {
    try {
      const { data: sessionData } = await supabaseService.supabase.auth.getSession();
      if (!sessionData?.session?.user?.id) {
        log(`[FastModeSync] 用户未登录，无法强制同步`);
        return;
      }

      const localSettings = await getUserSettings();
      await this.queueSync(sessionData.session.user.id, localSettings.fastMode || false);
    } catch (error) {
      logError(`[FastModeSync] 强制同步失败:`, error);
    }
  }

  /**
   * 从云端同步到本地（仅在必要时使用）
   */
  async syncFromCloud(): Promise<boolean> {
    try {
      const { data: sessionData } = await supabaseService.supabase.auth.getSession();
      if (!sessionData?.session?.user?.id) {
        return false;
      }

      const { data: cloudSettings } = await supabaseService.modelService.getUserModelSettings(
        sessionData.session.user.id
      );

      if (cloudSettings?.fast_mode !== undefined) {
        const localSettings = await getUserSettings();
        
        // 只有当云端设置与本地不同时才更新本地
        if (localSettings.fastMode !== cloudSettings.fast_mode) {
          await saveUserSettings({ fastMode: cloudSettings.fast_mode });
          log(`[FastModeSync] 已从云端同步极速模式设置到本地: ${cloudSettings.fast_mode}`);
          return true;
        }
      }

      return false;
    } catch (error) {
      logError(`[FastModeSync] 从云端同步失败:`, error);
      return false;
    }
  }

  /**
   * 清空同步队列
   */
  clearQueue() {
    this.syncQueue = [];
    log(`[FastModeSync] 同步队列已清空`);
  }

  /**
   * 获取当前队列状态
   */
  getQueueStatus() {
    return {
      queueLength: this.syncQueue.length,
      isSyncing: this.isSyncing
    };
  }
}

// 导出单例实例
export const fastModeSyncService = new FastModeSyncService();
export default fastModeSyncService; 