/**
 * 日志管理服务
 * 根据环境变量控制日志打印行为
 * - development: 允许所有日志
 * - staging: 允许错误和警告日志
 * - production: 禁用所有日志
 */
import { envConfig, type AppEnvironment } from './envConfigService';

class LogService {
  /**
   * 检查是否应该打印日志
   */
  private shouldLog(level: 'log' | 'info' | 'warn' | 'error'): boolean {
    const environment = envConfig.getAppEnvironment();
    
    switch (environment) {
      case 'development':
        // 开发环境：允许所有级别的日志
        return true;
      
      case 'staging':
        // 测试环境：只允许警告和错误日志
        return level === 'warn' || level === 'error';
      
      case 'production':
        // 生产环境：禁用所有日志
        return false;
      
      default:
        // 未知环境：谨慎起见，禁用日志
        return false;
    }
  }

  /**
   * 普通日志 (console.log)
   */
  log(...args: any[]): void {
    if (this.shouldLog('log')) {
      console.log(...args);
    }
  }

  /**
   * 信息日志 (console.info)
   */
  info(...args: any[]): void {
    if (this.shouldLog('info')) {
      console.info(...args);
    }
  }

  /**
   * 警告日志 (console.warn)
   */
  warn(...args: any[]): void {
    if (this.shouldLog('warn')) {
      console.warn(...args);
    }
  }

  /**
   * 错误日志 (console.error)
   */
  error(...args: any[]): void {
    if (this.shouldLog('error')) {
      console.error(...args);
    }
  }

  /**
   * 调试日志 (仅开发环境)
   */
  debug(...args: any[]): void {
    if (envConfig.getAppEnvironment() === 'development') {
      console.log('[DEBUG]', ...args);
    }
  }

  /**
   * 分组日志开始
   */
  group(label?: string): void {
    if (this.shouldLog('log')) {
      console.group(label);
    }
  }

  /**
   * 分组日志结束
   */
  groupEnd(): void {
    if (this.shouldLog('log')) {
      console.groupEnd();
    }
  }

  /**
   * 表格日志
   */
  table(data: any): void {
    if (this.shouldLog('log')) {
      console.table(data);
    }
  }

  /**
   * 带时间戳的日志
   */
  logWithTimestamp(level: 'log' | 'info' | 'warn' | 'error', ...args: any[]): void {
    if (this.shouldLog(level)) {
      const timestamp = new Date().toISOString();
      const levelTag = level.toUpperCase();
      console[level](`[${timestamp}] [${levelTag}]`, ...args);
    }
  }

  /**
   * 获取当前环境的日志配置信息
   */
  getLogConfig(): { environment: string; loggingEnabled: boolean; allowedLevels: string[] } {
    const environment = envConfig.getAppEnvironment();
    const loggingEnabled = this.shouldLog('log');
    
    let allowedLevels: string[] = [];
    
    switch (environment) {
      case 'development':
        allowedLevels = ['log', 'info', 'warn', 'error', 'debug'];
        break;
      case 'staging':
        allowedLevels = ['warn', 'error'];
        break;
      case 'production':
        allowedLevels = [];
        break;
    }

    return {
      environment,
      loggingEnabled,
      allowedLevels
    };
  }
}

// 创建单例实例
const logService = new LogService();

// 导出实例和便捷方法
export { logService };

// 导出便捷的日志函数
export const log = (...args: any[]) => logService.log(...args);
export const info = (...args: any[]) => logService.info(...args);
export const warn = (...args: any[]) => logService.warn(...args);
export const error = (...args: any[]) => logService.error(...args);
export const debug = (...args: any[]) => logService.debug(...args);

// 导出日志组函数
export const logGroup = (label?: string) => logService.group(label);
export const logGroupEnd = () => logService.groupEnd();

// 导出其他实用函数
export const logTable = (data: any) => logService.table(data);
export const logWithTimestamp = (level: 'log' | 'info' | 'warn' | 'error', ...args: any[]) => 
  logService.logWithTimestamp(level, ...args);

// 获取日志配置
export const getLogConfig = () => logService.getLogConfig();

export default logService; 