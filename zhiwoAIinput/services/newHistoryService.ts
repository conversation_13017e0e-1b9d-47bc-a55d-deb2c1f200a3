/**
 * 新的历史记录服务
 * 使用SQLite数据库替代AsyncStorage，支持按用户ID隔离的历史记录管理
 */
import { sqliteService } from './sqliteService';
import { userIdService } from './userIdService';
import { recordingFileService } from './recordingFileService';
import { getVipStatus } from './storageService';
import { generateId, formatDate } from '@/utils/helpers';
import { log, error as logError, warn } from '@/services/logService';

// 优化结果接口
export interface OptimizedResult {
  text: string;
  timestamp: number;
  templateId: string;
  templateName: string;
}

// 历史记录接口（兼容现有代码）
export interface HistoryRecordCompat {
  id: string;
  timestamp: number;
  originalText: string;
  optimizedText: string;
  templateId: string;
  templateName: string;
  audioUri?: string;
  sourceRecordId?: string; // 二次优化的原始记录ID
  optimizedResults?: OptimizedResult[]; // 多次优化结果数组
}

// 添加一些扩展属性，用于UI显示
export interface HistoryRecordWithUI extends HistoryRecordCompat {
  formattedDate: string; // 格式化后的日期
  displayOriginalText: string; // 用于显示的原文文本（可能被截断）
  displayOptimizedText: string; // 用于显示的优化文本（可能被截断）
}

// 历史记录分组接口
export interface HistoryGroup {
  title: string; // 分组标题，如"今天"、"昨天"、"2023-11-10"
  data: HistoryRecordWithUI[]; // 分组下的历史记录
}

/**
 * 新的历史记录服务类
 */
class NewHistoryService {
  /**
   * 保存历史记录（包含复杂的业务逻辑）
   * @param record 历史记录
   * @param userId 用户ID（可选，默认使用当前用户）
   */
  async saveHistoryRecord(record: HistoryRecordCompat, userId?: string): Promise<boolean> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();

      // 确保记录有ID
      if (!record.id) {
        record.id = generateId();
      }

      // 确保时间戳存在
      if (!record.timestamp) {
        record.timestamp = Date.now();
      }

      // 获取现有历史记录
      const records = await this.getHistoryRecords(currentUserId);

      // 检查是否已经存在相同ID的记录，如果存在，说明这是一个更新操作（来自recordingSlice的逻辑）
      const existingRecordById = records.find(r => r.id === record.id);
      const isUpdateOperation = !!existingRecordById;

      if (isUpdateOperation) {
        log('NewHistoryService: 检测到更新操作，直接保存记录:', record.id);
        // 对于更新操作，直接保存，不重新应用合并逻辑
        // recordingSlice已经处理了所有的合并逻辑，我们只需要保存
      } else {
        log('NewHistoryService: 新记录，检查是否需要应用合并逻辑:', record.id);
      }

      // 处理音频URI，对于新记录直接使用传入的路径（已经是移动后的正确路径）
      let processedAudioUri = record.audioUri;
      if (processedAudioUri) {
        // 检查路径是否已经是正确的用户目录路径
        const isCorrectPath = processedAudioUri.includes(`/recordings/${currentUserId}/`);

        if (!isCorrectPath) {
          // 只有当路径不正确时才进行修复（用于历史数据）
          log('NewHistoryService: 检测到旧路径，进行修复:', processedAudioUri);
          processedAudioUri = await recordingFileService.fixRecordingPath(processedAudioUri, currentUserId);
        } else {
          log('NewHistoryService: 路径已正确，无需修复:', processedAudioUri);
        }

        // 保存录音文件记录到数据库
        try {
          await recordingFileService.saveRecordingFileRecord(processedAudioUri, currentUserId);
        } catch (fileError) {
          warn('NewHistoryService: 保存录音文件记录失败，但继续保存历史记录:', fileError);
        }
      }

      // 🔥 重要：recordingSlice已经处理了所有的合并逻辑，newHistoryService只负责保存
      // 只有在不是更新操作且记录没有optimizedResults时，才初始化optimizedResults数组
      if (!isUpdateOperation && !record.optimizedResults) {
        log('NewHistoryService: 初始化optimizedResults数组:', record.id);
        // 初始化优化结果数组
        record.optimizedResults = [{
          text: record.optimizedText,
          timestamp: record.timestamp,
          templateId: record.templateId,
          templateName: record.templateName
        }];
      } else if (isUpdateOperation) {
        log('NewHistoryService: 更新操作，保持recordingSlice传入的optimizedResults:', record.id);
        // 对于更新操作，直接使用recordingSlice传入的数据，不做任何修改
      } else {
        log('NewHistoryService: 记录已有optimizedResults，保持不变:', record.id);
        // 记录已经有optimizedResults，保持不变
      }

      // 更新音频URI
      record.audioUri = processedAudioUri;

      // 转换为数据库格式并保存
      const dbRecord = {
        id: record.id,
        timestamp: record.timestamp,
        original_text: record.originalText,
        optimized_text: record.optimizedText,
        template_id: record.templateId,
        template_name: record.templateName,
        audio_uri: record.audioUri,
        source_record_id: record.sourceRecordId,
        optimized_results: record.optimizedResults ? JSON.stringify(record.optimizedResults) : undefined,
      };

      await sqliteService.saveHistoryRecord(currentUserId, dbRecord);
      log(`NewHistoryService: 保存历史记录成功 (userId: ${currentUserId}, recordId: ${record.id})`);

      return true;
    } catch (error) {
      logError('NewHistoryService: 保存历史记录失败:', error);
      return false;
    }
  }

  /**
   * 获取历史记录列表（不带VIP限制，内部使用）
   * @param userId 用户ID（可选，默认使用当前用户）
   * @param limit 限制数量
   * @param offset 偏移量
   * @returns 历史记录列表
   */
  async getHistoryRecords(userId?: string, limit: number = 1000, offset: number = 0): Promise<HistoryRecordCompat[]> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      const dbRecords = await sqliteService.getHistoryRecords(currentUserId, limit, offset);

      // 转换为兼容格式
      const compatRecords: HistoryRecordCompat[] = dbRecords.map(dbRecord => ({
        id: dbRecord.id,
        timestamp: dbRecord.timestamp,
        originalText: dbRecord.original_text,
        optimizedText: dbRecord.optimized_text,
        templateId: dbRecord.template_id,
        templateName: dbRecord.template_name,
        audioUri: dbRecord.audio_uri,
        sourceRecordId: dbRecord.source_record_id,
        optimizedResults: dbRecord.optimized_results ? JSON.parse(dbRecord.optimized_results) : undefined,
      }));

      // 智能修复音频文件路径（只修复需要修复的路径）
      for (const record of compatRecords) {
        if (record.audioUri) {
          try {
            // 检查路径是否需要修复
            const isCorrectPath = record.audioUri.includes(`/recordings/${currentUserId}/`);

            if (!isCorrectPath) {
              log(`NewHistoryService: 修复音频路径 (recordId: ${record.id}): ${record.audioUri}`);
              record.audioUri = await recordingFileService.fixRecordingPath(record.audioUri, currentUserId);
            }
          } catch (pathError) {
            warn(`NewHistoryService: 修复音频路径失败 (recordId: ${record.id}):`, pathError);
          }
        }
      }

      log(`NewHistoryService: 获取历史记录成功 (userId: ${currentUserId}, count: ${compatRecords.length})`);
      return compatRecords;
    } catch (error) {
      logError('NewHistoryService: 获取历史记录失败:', error);
      return [];
    }
  }

  /**
   * 根据ID获取历史记录
   * @param recordId 记录ID
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 历史记录或null
   */
  async getHistoryRecordById(recordId: string, userId?: string): Promise<HistoryRecordCompat | null> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      const dbRecord = await sqliteService.getHistoryRecordById(currentUserId, recordId);
      
      if (!dbRecord) {
        return null;
      }
      
      // 转换为兼容格式
      const compatRecord: HistoryRecordCompat = {
        id: dbRecord.id,
        timestamp: dbRecord.timestamp,
        originalText: dbRecord.original_text,
        optimizedText: dbRecord.optimized_text,
        templateId: dbRecord.template_id,
        templateName: dbRecord.template_name,
        audioUri: dbRecord.audio_uri,
        sourceRecordId: dbRecord.source_record_id,
        optimizedResults: dbRecord.optimized_results ? JSON.parse(dbRecord.optimized_results) : undefined,
      };
      
      // 智能修复音频文件路径（只修复需要修复的路径）
      if (compatRecord.audioUri) {
        try {
          // 检查路径是否需要修复
          const isCorrectPath = compatRecord.audioUri.includes(`/recordings/${currentUserId}/`);

          if (!isCorrectPath) {
            log(`NewHistoryService: 修复音频路径 (recordId: ${recordId}): ${compatRecord.audioUri}`);
            compatRecord.audioUri = await recordingFileService.fixRecordingPath(compatRecord.audioUri, currentUserId);
          }
        } catch (pathError) {
          warn(`NewHistoryService: 修复音频路径失败 (recordId: ${recordId}):`, pathError);
        }
      }
      
      log(`NewHistoryService: 获取历史记录成功 (userId: ${currentUserId}, recordId: ${recordId})`);
      return compatRecord;
    } catch (error) {
      logError(`NewHistoryService: 获取历史记录失败 (recordId: ${recordId}):`, error);
      return null;
    }
  }

  /**
   * 删除历史记录
   * @param recordId 记录ID
   * @param userId 用户ID（可选，默认使用当前用户）
   */
  async deleteHistoryRecord(recordId: string, userId?: string): Promise<void> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      
      // 先获取记录，检查是否有关联的音频文件
      const record = await this.getHistoryRecordById(recordId, currentUserId);
      if (record?.audioUri) {
        try {
          await recordingFileService.deleteRecordingFile(record.audioUri, currentUserId);
        } catch (fileError) {
          warn('NewHistoryService: 删除关联音频文件失败，但继续删除历史记录:', fileError);
        }
      }
      
      await sqliteService.deleteHistoryRecord(currentUserId, recordId);
      log(`NewHistoryService: 删除历史记录成功 (userId: ${currentUserId}, recordId: ${recordId})`);
    } catch (error) {
      logError(`NewHistoryService: 删除历史记录失败 (recordId: ${recordId}):`, error);
      throw error;
    }
  }

  /**
   * 更新历史记录
   * @param recordId 记录ID
   * @param updates 更新内容
   * @param userId 用户ID（可选，默认使用当前用户）
   */
  async updateHistoryRecord(recordId: string, updates: Partial<HistoryRecordCompat>, userId?: string): Promise<void> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      
      // 先获取现有记录
      const existingRecord = await this.getHistoryRecordById(recordId, currentUserId);
      if (!existingRecord) {
        throw new Error(`历史记录不存在: ${recordId}`);
      }
      
      // 合并更新
      const updatedRecord: HistoryRecordCompat = {
        ...existingRecord,
        ...updates,
      };
      
      // 保存更新后的记录
      await this.saveHistoryRecord(updatedRecord, currentUserId);
      log(`NewHistoryService: 更新历史记录成功 (userId: ${currentUserId}, recordId: ${recordId})`);
    } catch (error) {
      logError(`NewHistoryService: 更新历史记录失败 (recordId: ${recordId}):`, error);
      throw error;
    }
  }

  /**
   * 迁移历史记录从匿名用户到登录用户
   * @param fromUserId 源用户ID（匿名用户）
   * @param toUserId 目标用户ID（登录用户）
   * @returns 迁移的记录数量
   */
  async migrateHistoryRecords(fromUserId: string, toUserId: string): Promise<number> {
    try {
      log(`NewHistoryService: 开始迁移历史记录 ${fromUserId} -> ${toUserId}`);
      
      // 迁移数据库记录
      const migratedCount = await sqliteService.migrateHistoryRecords(fromUserId, toUserId);
      
      // 迁移录音文件
      try {
        const migratedFilesCount = await recordingFileService.migrateRecordingFiles(fromUserId, toUserId);
        log(`NewHistoryService: 迁移录音文件成功，共 ${migratedFilesCount} 个文件`);
      } catch (fileError) {
        warn('NewHistoryService: 迁移录音文件失败，但历史记录迁移成功:', fileError);
      }
      
      log(`NewHistoryService: 历史记录迁移完成，共 ${migratedCount} 条记录`);
      return migratedCount;
    } catch (error) {
      logError(`NewHistoryService: 迁移历史记录失败 (${fromUserId} -> ${toUserId}):`, error);
      throw error;
    }
  }

  /**
   * 清除用户的所有历史记录
   * @param userId 用户ID（可选，默认使用当前用户）
   */
  async clearAllHistoryRecords(userId?: string): Promise<boolean> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();

      // 获取所有记录，删除关联的音频文件
      const records = await this.getHistoryRecords(currentUserId, 1000, 0);
      for (const record of records) {
        if (record.audioUri) {
          try {
            await recordingFileService.deleteRecordingFile(record.audioUri, currentUserId);
          } catch (fileError) {
            warn(`NewHistoryService: 删除音频文件失败 (recordId: ${record.id}):`, fileError);
          }
        }
      }

      // 清除数据库记录
      const db = await sqliteService.getDatabase(currentUserId);
      await db.runAsync(`DELETE FROM history_records WHERE user_id = ?`, [currentUserId]);

      log(`NewHistoryService: 清除所有历史记录成功 (userId: ${currentUserId})`);
      return true;
    } catch (error) {
      logError('NewHistoryService: 清除所有历史记录失败:', error);
      return false;
    }
  }

  /**
   * 获取按日期分组的历史记录（带VIP权限控制）
   * @param limit 限制加载的记录条数，默认不限制
   * @param t 翻译函数，用于多语言支持
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 按日期分组的历史记录数组
   */
  async getGroupedHistoryRecords(limit?: number, t?: (key: string) => string, userId?: string): Promise<HistoryGroup[]> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();

      // 获取全部历史记录
      const records = await this.getHistoryRecords(currentUserId);

      // 如果限制了条数，则只返回指定条数的记录
      let filteredRecords = records;
      if (limit && limit > 0) {
        // 检查VIP状态，非VIP用户最多只能看到50条记录
        const vipStatus = await getVipStatus();
        let actualLimit = limit;

        if (!vipStatus.isVip) {
          // 非VIP用户限制最多50条
          actualLimit = Math.min(limit, 50);
          log(`非VIP用户，限制显示最多50条记录: 共${records.length}条，请求${limit}条，实际返回${actualLimit}条`);
        } else {
          log(`VIP用户，按请求加载: 共${records.length}条，加载前${limit}条`);
        }

        filteredRecords = records.slice(0, actualLimit);
      }

      return this.groupRecordsByDate(filteredRecords, t);
    } catch (error) {
      logError('NewHistoryService: 获取分组历史记录失败:', error);
      return [];
    }
  }

  /**
   * 根据模板名称获取分组的历史记录（带VIP权限控制）
   * @param templateName 模板名称，如果为空则返回所有记录
   * @param limit 限制加载的记录条数
   * @param t 翻译函数，用于多语言支持
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 按日期分组的历史记录数组
   */
  async getGroupedHistoryRecordsByTemplate(
    templateName?: string,
    limit?: number,
    t?: (key: string) => string,
    userId?: string
  ): Promise<HistoryGroup[]> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();

      // 获取全部历史记录
      const allRecords = await this.getHistoryRecords(currentUserId);

      // 检查VIP状态
      const vipStatus = await getVipStatus();

      // 首先根据VIP状态限制可见记录范围
      let visibleRecords = allRecords;
      if (!vipStatus.isVip) {
        // 非VIP用户只能看最近的50条记录
        visibleRecords = allRecords.slice(0, 50);
        log(`非VIP用户，限制在最近50条记录范围内: 总共${allRecords.length}条，可见${visibleRecords.length}条`);
      } else {
        log(`VIP用户，可查看所有${allRecords.length}条记录`);
      }

      // 然后在可见记录范围内根据模板名称筛选
      let filteredRecords = visibleRecords;
      if (templateName && templateName !== "全部") {
        filteredRecords = visibleRecords.filter(record => record.templateName === templateName);
        log(`在可见记录中筛选模板"${templateName}": 找到${filteredRecords.length}条记录`);
      }

      // 最后应用分页限制
      if (limit && limit > 0) {
        filteredRecords = filteredRecords.slice(0, limit);
        log(`应用分页限制: 最终返回${filteredRecords.length}条记录`);
      }

      return this.groupRecordsByDate(filteredRecords, t);
    } catch (error) {
      logError('NewHistoryService: 根据模板获取分组历史记录失败:', error);
      return [];
    }
  }

  /**
   * 按日期分组记录的私有方法
   * @param records 历史记录数组
   * @param t 翻译函数
   * @returns 分组后的历史记录
   */
  private groupRecordsByDate(records: HistoryRecordCompat[], t?: (key: string) => string): HistoryGroup[] {
    // 按日期分组
    const groups: { [key: string]: HistoryRecordWithUI[] } = {};

    // 获取多语言日期标签
    const todayLabel = t ? t("history.today") : "今天";
    const yesterdayLabel = t ? t("history.yesterday") : "昨天";
    const twoDaysAgoLabel = t ? t("history.dayBeforeYesterday") : "前天";

    // 转换为UI显示格式并按日期分组
    records.forEach(record => {
      // 格式化日期，传递翻译函数
      let dateKey = formatDate(record.timestamp, t);

      // 创建带UI属性的记录
      const recordWithUI: HistoryRecordWithUI = {
        ...record,
        formattedDate: dateKey,
        // 截断长文本以提高显示效率
        displayOriginalText: record.originalText.length > 100
          ? record.originalText.substring(0, 100) + "..."
          : record.originalText,
        displayOptimizedText: record.optimizedText.length > 100
          ? record.optimizedText.substring(0, 100) + "..."
          : record.optimizedText
      };

      // 添加到相应分组
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(recordWithUI);
    });

    // 将分组对象转换为数组格式并按时间倒序排序
    return Object.entries(groups)
      .map(([title, data]) => ({ title, data }))
      .sort((a, b) => {
        // 特殊处理多语言日期分组
        if (a.title === todayLabel) return -1;
        if (b.title === todayLabel) return 1;
        if (a.title === yesterdayLabel) return -1;
        if (b.title === yesterdayLabel) return 1;
        if (a.title === twoDaysAgoLabel) return -1;
        if (b.title === twoDaysAgoLabel) return 1;
        // 其他情况按日期倒序排序
        return new Date(b.title).getTime() - new Date(a.title).getTime();
      });
  }

  /**
   * 获取真实的历史记录总数（不受VIP限制）
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 真实的历史记录总数
   */
  async getRealHistoryRecordsCount(userId?: string): Promise<number> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      const records = await this.getHistoryRecords(currentUserId);
      return records.length;
    } catch (error) {
      logError('NewHistoryService: 获取真实历史记录总数失败:', error);
      return 0;
    }
  }

  /**
   * 获取用户可见的历史记录总数（考虑VIP限制）
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 用户可见的历史记录总数
   */
  async getVisibleHistoryRecordsCount(userId?: string): Promise<number> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      const records = await this.getHistoryRecords(currentUserId);
      const vipStatus = await getVipStatus();

      if (!vipStatus.isVip) {
        // 非VIP用户最多只能看到50条
        return Math.min(records.length, 50);
      } else {
        // VIP用户可以看到所有记录
        return records.length;
      }
    } catch (error) {
      logError('NewHistoryService: 获取可见历史记录总数失败:', error);
      return 0;
    }
  }

  /**
   * 获取所有历史记录中使用过的模板名称（用于标签筛选）
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 模板名称数组
   */
  async getAllTemplateNamesFromHistory(userId?: string): Promise<string[]> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      const records = await this.getHistoryRecords(currentUserId);
      const templateNames = new Set<string>();

      records.forEach(record => {
        if (record.templateName) {
          templateNames.add(record.templateName);
        }
      });

      return Array.from(templateNames).sort();
    } catch (error) {
      logError('NewHistoryService: 获取历史记录模板名称失败:', error);
      return [];
    }
  }

  /**
   * 获取在前50条记录范围内可用的模板名称（仅非VIP用户需要）
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 在前50条记录中存在的模板名称数组
   */
  async getAvailableTemplateNamesInVisibleRange(userId?: string): Promise<string[]> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      const allRecords = await this.getHistoryRecords(currentUserId);
      const vipStatus = await getVipStatus();

      // VIP用户看到所有模板
      if (vipStatus.isVip) {
        const templateNames = new Set<string>();
        allRecords.forEach(record => {
          if (record.templateName) {
            templateNames.add(record.templateName);
          }
        });
        return Array.from(templateNames).sort();
      }

      // 非VIP用户只看前50条记录中的模板
      const visibleRecords = allRecords.slice(0, 50);
      const templateNames = new Set<string>();

      visibleRecords.forEach(record => {
        if (record.templateName) {
          templateNames.add(record.templateName);
        }
      });

      return Array.from(templateNames).sort();
    } catch (error) {
      logError('NewHistoryService: 获取可见范围内的模板名称失败:', error);
      return [];
    }
  }

  /**
   * 获取指定模板的可见历史记录总数（考虑VIP限制）
   * @param templateName 模板名称，如果为空则返回所有记录数
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 指定模板的可见历史记录总数
   */
  async getVisibleHistoryRecordsCountByTemplate(templateName?: string, userId?: string): Promise<number> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      const allRecords = await this.getHistoryRecords(currentUserId);
      const vipStatus = await getVipStatus();

      // 首先根据VIP状态限制可见记录范围
      let visibleRecords = allRecords;
      if (!vipStatus.isVip) {
        // 非VIP用户只能看最近的50条记录
        visibleRecords = allRecords.slice(0, 50);
      }

      // 然后在可见记录范围内根据模板名称筛选
      if (templateName && templateName !== "全部") {
        visibleRecords = visibleRecords.filter(record => record.templateName === templateName);
      }

      return visibleRecords.length;
    } catch (error) {
      logError('NewHistoryService: 获取指定模板的可见历史记录总数失败:', error);
      return 0;
    }
  }

  /**
   * 获取指定模板的真实历史记录总数（不受VIP限制）
   * @param templateName 模板名称，如果为空则返回所有记录数
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 指定模板的真实历史记录总数
   */
  async getRealHistoryRecordsCountByTemplate(templateName?: string, userId?: string): Promise<number> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      const allRecords = await this.getHistoryRecords(currentUserId);

      // 如果指定了模板名称，则筛选对应的记录
      let filteredRecords = allRecords;
      if (templateName && templateName !== "全部") {
        filteredRecords = allRecords.filter(record => record.templateName === templateName);
      }

      return filteredRecords.length;
    } catch (error) {
      logError('NewHistoryService: 获取指定模板的真实历史记录总数失败:', error);
      return 0;
    }
  }

  /**
   * 检查指定模板是否有被VIP限制隐藏的记录
   * @param templateName 模板名称，如果为空则检查所有记录
   * @param userId 用户ID（可选，默认使用当前用户）
   * @returns 隐藏的记录数量
   */
  async getHiddenRecordsCountByTemplate(templateName?: string, userId?: string): Promise<number> {
    try {
      const currentUserId = userId || await userIdService.getCurrentUserId();
      const allRecords = await this.getHistoryRecords(currentUserId);
      const vipStatus = await getVipStatus();

      // VIP用户没有隐藏记录
      if (vipStatus.isVip) {
        return 0;
      }

      // 筛选指定模板的所有记录
      let filteredRecords = allRecords;
      if (templateName && templateName !== "全部") {
        filteredRecords = allRecords.filter(record => record.templateName === templateName);
      }

      // 获取前50条记录中该模板的记录数
      const visibleRecords = allRecords.slice(0, 50);
      let visibleFilteredRecords = visibleRecords;
      if (templateName && templateName !== "全部") {
        visibleFilteredRecords = visibleRecords.filter(record => record.templateName === templateName);
      }

      // 返回隐藏的记录数量
      return Math.max(0, filteredRecords.length - visibleFilteredRecords.length);
    } catch (error) {
      logError('NewHistoryService: 获取指定模板的隐藏记录总数失败:', error);
      return 0;
    }
  }
}

// 导出单例实例
export const newHistoryService = new NewHistoryService();
