/**
 * AI模型辅助工具
 * 提供模型ID、API名称和显示名称之间的映射
 */

import { log } from '@/services/logService';
// 模型ID类型
export type ModelId = 'qwen2.5-7b' | 'qwen3-8b' | 'qwen3-14b' | 'deepseek-v3';

// 模型完整信息接口
export interface ModelInfo {
  id: ModelId;        // 内部模型ID - 存储在设置中
  apiName: string;    // API调用名称 - 发送给SiliconFlow API
  displayName: string; // 显示名称 - 用于UI显示
  isPremium: boolean;  // 是否高级模型
}

// 定义所有支持的模型信息
export const MODEL_LIST: ModelInfo[] = [
  {
    id: 'qwen2.5-7b',
    apiName: 'Qwen/Qwen2.5-7B-Instruct',
    displayName: 'Qwen2.5-7B',
    isPremium: false
  },
  {
    id: 'qwen3-8b',
    apiName: 'Qwen/Qwen3-8B',
    displayName: 'Qwen3-8B',
    isPremium: true
  },
  {
    id: 'qwen3-14b',
    apiName: 'Qwen/Qwen3-14B',
    displayName: 'Qwen3-14B',
    isPremium: true
  },
  {
    id: 'deepseek-v3',
    apiName: 'deepseek-ai/DeepSeek-V3',
    displayName: 'DeepSeek-V3',
    isPremium: true
  }
];

// 默认模型ID
export const DEFAULT_MODEL_ID: ModelId = 'qwen2.5-7b';

/**
 * 根据模型ID获取完整的模型信息
 * @param modelId 模型ID
 * @returns 模型完整信息
 */
export const getModelInfo = (modelId: string | undefined): ModelInfo => {
  log("getModelInfo被调用，传入参数:", modelId, "类型:", typeof modelId);
  
  if (!modelId) {
    log("modelId为空，返回默认模型:", DEFAULT_MODEL_ID);
    // 如果未提供模型ID，返回默认模型
    return MODEL_LIST.find(model => model.id === DEFAULT_MODEL_ID)!;
  }
  
  // 将传入的modelId规范化为小写，方便比较
  const normalizedModelId = modelId.toLowerCase();
  log("规范化后的modelId:", normalizedModelId);
  
  // 打印所有可用的模型ID，用于调试
  log("可用的模型列表:", MODEL_LIST.map(m => ({
    id: m.id,
    apiName: m.apiName,
    displayName: m.displayName
  })));
  
  // 查找匹配的模型
  const model = MODEL_LIST.find(model => {
    // 记录每个比较的结果
    const matchesId = model.id === modelId;
    const matchesApiName = model.apiName === modelId;
    const matchesApiNameLower = model.apiName.toLowerCase() === normalizedModelId;
    
    log(`比较模型 ${model.id}:`, {
      匹配ID: matchesId,
      匹配API名称: matchesApiName,
      匹配小写API名称: matchesApiNameLower
    });
    
    return matchesId || matchesApiName || matchesApiNameLower;
  });
  
  if (model) {
    log("找到匹配的模型:", model.id, model.displayName);
    return model;
  } else {
    log("未找到匹配的模型，返回默认模型:", DEFAULT_MODEL_ID);
    return MODEL_LIST.find(model => model.id === DEFAULT_MODEL_ID)!;
  }
};

/**
 * 根据模型ID获取API名称
 * @param modelId 模型ID
 * @returns API名称
 */
export const getApiModelName = (modelId: string | undefined): string => {
  return getModelInfo(modelId).apiName;
};

/**
 * 根据模型ID获取显示名称
 * @param modelId 模型ID
 * @returns 显示名称
 */
export const getModelDisplayName = (modelId: string | undefined): string => {
  return getModelInfo(modelId).displayName;
};

/**
 * 判断模型是否为高级模型
 * @param modelId 模型ID
 * @returns 是否为高级模型
 */
export const isModelPremium = (modelId: string | undefined): boolean => {
  return getModelInfo(modelId).isPremium;
}; 