/**
 * 应用配置信息
 * 包含API密钥等配置信息
 */
import Constants from 'expo-constants';
import { log } from '@/services/logService';

// 尝试从多个来源获取 API 密钥
let envApiKey = '';
try {
  // 尝试直接从 @env 模块导入（需要 react-native-dotenv 支持）
  const { SILICONFLOW_API_KEY } = require('@env');
  envApiKey = SILICONFLOW_API_KEY || '';
} catch (error) {
  log('无法从 @env 模块获取环境变量，使用 Constants 配置');
}

// 从环境变量或应用配置中获取SiliconFlow API密钥
export const defaultApiKey = envApiKey || 
  Constants.expoConfig?.extra?.siliconflowApiKey || 
  "";

// 默认超时时间
export const defaultTimeout = 30000; // 30秒

// API端点
export const apiEndpoints = {
  siliconflow: "https://api.siliconflow.cn/v1/chat/completions",
  openai: "https://api.openai.com/v1/chat/completions"
};

// 环境配置
export const environment = {
  isDev: __DEV__,
  isProduction: !__DEV__
}; 