/**
 * 调试工具集
 * 包含各种调试和修复功能
 */

import { clearSystemPromptCache, refreshSystemPromptCache } from '../services/systemPromptService';
import { clearSpeechPromptCache, refreshSpeechPromptCache } from '../services/speechPromptService';
import { syncUserPreferencesOnLogin } from '../services/templatePreferenceService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { log, error as logError } from '@/services/logService';

export class DebugUtils {
  /**
   * 清理所有缓存数据
   */
  static async clearAllCache(): Promise<void> {
    try {
      log('[DebugUtils] 开始清理所有缓存...');
      
      // 清理系统提示词缓存
      await clearSystemPromptCache();
      
      // 清理语音提示词缓存
      await clearSpeechPromptCache();
      
      // 清理模板偏好设置缓存
      await AsyncStorage.removeItem('knowme_template_preferences');
      await AsyncStorage.removeItem('knowme_preferences_last_sync');
      
      // 清理其他相关缓存
      const keysToRemove = [
        'knowme_user_templates',
        'knowme_default_template_id',
        'knowme_deleted_templates',
        'knowme_system_templates',
        'knowme_system_templates_version',
        'knowme_templates_last_sync',
      ];
      
      await AsyncStorage.multiRemove(keysToRemove);
      
      log('[DebugUtils] 所有缓存已清理完成');
    } catch (error) {
      logError('[DebugUtils] 清理缓存失败:', error);
      throw error;
    }
  }

  /**
   * 强制重新同步所有数据
   */
  static async forceResync(): Promise<void> {
    try {
      log('[DebugUtils] 开始强制重新同步...');
      
      // 先清理缓存
      await this.clearAllCache();
      
      // 重新获取系统提示词
      await refreshSystemPromptCache();
      
      // 重新获取语音提示词
      await refreshSpeechPromptCache();
      
      // 重新同步模板偏好设置
      await syncUserPreferencesOnLogin();
      
      log('[DebugUtils] 强制重新同步完成');
    } catch (error) {
      logError('[DebugUtils] 强制重新同步失败:', error);
      throw error;
    }
  }

  /**
   * 修复模板偏好设置重复问题
   */
  static async fixTemplatePreferencesDuplicates(): Promise<void> {
    try {
      log('[DebugUtils] 开始修复模板偏好设置重复问题...');
      
      // 清理本地偏好设置缓存
      await AsyncStorage.removeItem('knowme_template_preferences');
      await AsyncStorage.removeItem('knowme_preferences_last_sync');
      
      // 重新同步（会触发云端的UPSERT逻辑）
      await syncUserPreferencesOnLogin();
      
      log('[DebugUtils] 模板偏好设置重复问题修复完成');
    } catch (error) {
      logError('[DebugUtils] 修复模板偏好设置重复问题失败:', error);
      throw error;
    }
  }

  /**
   * 检查当前缓存状态
   */
  static async getCacheStatus(): Promise<{
    systemPrompt: any;
    templatePreferences: any;
    userTemplates: any;
  }> {
    try {
      const keys = [
        'knowme_system_prompt',
        'knowme_system_prompt_version',
        'knowme_system_prompt_cache_time',
        'knowme_template_preferences',
        'knowme_preferences_last_sync',
        'knowme_user_templates',
        'knowme_default_template_id',
      ];

      const values = await AsyncStorage.multiGet(keys);
      const cacheStatus = Object.fromEntries(values);

      // 解析时间戳
      const systemPromptCacheTime = cacheStatus['knowme_system_prompt_cache_time'];
      const preferencesSyncTime = cacheStatus['knowme_preferences_last_sync'];

      return {
        systemPrompt: {
          hasCache: !!cacheStatus['knowme_system_prompt'],
          version: cacheStatus['knowme_system_prompt_version'],
          cacheTime: systemPromptCacheTime ? new Date(parseInt(systemPromptCacheTime)) : null,
          isExpired: systemPromptCacheTime ? (Date.now() - parseInt(systemPromptCacheTime) > 60 * 60 * 1000) : true,
        },
        templatePreferences: {
          hasCache: !!cacheStatus['knowme_template_preferences'],
          lastSync: preferencesSyncTime ? new Date(parseInt(preferencesSyncTime)) : null,
          data: cacheStatus['knowme_template_preferences'] ? JSON.parse(cacheStatus['knowme_template_preferences']) : null,
        },
        userTemplates: {
          hasCache: !!cacheStatus['knowme_user_templates'],
          defaultTemplateId: cacheStatus['knowme_default_template_id'],
          data: cacheStatus['knowme_user_templates'] ? JSON.parse(cacheStatus['knowme_user_templates']) : null,
        },
      };
    } catch (error) {
      logError('[DebugUtils] 获取缓存状态失败:', error);
      throw error;
    }
  }

  /**
   * 打印调试信息
   */
  static async printDebugInfo(): Promise<void> {
    try {
      log('=== 知我AI输入法调试信息 ===');
      
      const cacheStatus = await this.getCacheStatus();
      
      log('系统提示词缓存状态:', {
        hasCache: cacheStatus.systemPrompt.hasCache,
        version: cacheStatus.systemPrompt.version,
        cacheTime: cacheStatus.systemPrompt.cacheTime,
        isExpired: cacheStatus.systemPrompt.isExpired,
      });
      
      log('模板偏好设置状态:', {
        hasCache: cacheStatus.templatePreferences.hasCache,
        lastSync: cacheStatus.templatePreferences.lastSync,
        dataCount: cacheStatus.templatePreferences.data ? cacheStatus.templatePreferences.data.length : 0,
      });
      
      log('用户模板状态:', {
        hasCache: cacheStatus.userTemplates.hasCache,
        defaultTemplateId: cacheStatus.userTemplates.defaultTemplateId,
        dataCount: cacheStatus.userTemplates.data ? cacheStatus.userTemplates.data.length : 0,
      });
      
      log('=== 调试信息结束 ===');
    } catch (error) {
      logError('[DebugUtils] 打印调试信息失败:', error);
    }
  }
}

export default DebugUtils; 