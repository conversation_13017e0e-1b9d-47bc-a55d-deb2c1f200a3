# 调试和管理工具使用指南

本文档介绍知我AI输入法项目中创建的调试和管理工具的使用方法。

## 工具概览

### 1. DebugUtils（调试工具集）
位置：`utils/debugUtils.ts`

提供应用缓存管理和调试功能：
- 清理所有缓存数据
- 强制重新同步数据
- 修复模板偏好设置重复问题
- 获取缓存状态信息
- 打印调试信息

### 2. SystemPromptManager（系统提示词管理器）
位置：`utils/systemPromptManager.ts`

提供系统提示词的管理和操作功能：
- 获取完整的系统提示词
- 刷新和清除缓存
- 验证提示词格式
- 检查和自动更新
- 生成预览

### 3. FixSystemPrompt（系统提示词修复工具）
位置：`utils/fixSystemPrompt.ts`

专门用于修复系统提示词相关问题：
- 立即修复缓存问题
- 检查系统提示词内容
- 手动更新云端版本

## 如何使用

### 在应用中的集成位置

#### 1. 应用启动时自动检查
在 `app/_layout.tsx` 中已集成：
```typescript
// 异步初始化系统提示词（自动检查更新）
SystemPromptManager.autoUpdate().catch(error => {
  console.warn('[App] 系统提示词自动更新失败:', error);
});
```

#### 2. 主页面焦点时检查
在 `app/(tabs)/index.tsx` 中已集成：
```typescript
// 异步检查系统提示词更新
const { SystemPromptManager } = await import("../../utils/systemPromptManager");
const updateCheck = await SystemPromptManager.checkForUpdates();

if (updateCheck.needsUpdate) {
  console.log('[HomePage] 检测到系统提示词需要更新');
  await SystemPromptManager.autoUpdate();
}
```

#### 3. 设置页面手动操作
在 `app/(tabs)/settings/data-management.tsx` 中已集成调试按钮（仅开发模式显示）：
- 修复系统提示词缓存
- 检查系统提示词
- 清理所有缓存
- 重置引导状态

### 手动使用示例

#### 使用 DebugUtils
```typescript
import { DebugUtils } from './utils/debugUtils';

// 获取缓存状态
const status = await DebugUtils.getCacheStatus();
console.log('缓存状态:', status);

// 清理所有缓存
await DebugUtils.clearAllCache();

// 强制重新同步
await DebugUtils.forceResync();

// 打印调试信息
await DebugUtils.printDebugInfo();
```

#### 使用 SystemPromptManager
```typescript
import { SystemPromptManager } from './utils/systemPromptManager';

// 获取系统提示词
const prompt = await SystemPromptManager.getCompleteSystemPrompt('你的模板内容');

// 检查是否需要更新
const updateCheck = await SystemPromptManager.checkForUpdates();

// 自动更新
if (updateCheck.needsUpdate) {
  await SystemPromptManager.autoUpdate();
}

// 获取缓存信息
const cacheInfo = await SystemPromptManager.getCacheInfo();
```

#### 使用 FixSystemPrompt
```typescript
import { fixSystemPromptCache, checkSystemPromptContent } from './utils/fixSystemPrompt';

// 检查系统提示词内容
const contentCheck = await checkSystemPromptContent();
console.log('内容检查:', contentCheck);

// 修复缓存问题
if (!contentCheck.isLatest) {
  const result = await fixSystemPromptCache();
  console.log('修复结果:', result);
}
```

### 运行演示

可以使用 `utils/toolsDemo.ts` 中的演示函数：

```typescript
import { runAllDemos } from './utils/toolsDemo';

// 运行所有工具演示
await runAllDemos();
```

或者单独运行：
```typescript
import { demoDebugUtils, demoSystemPromptManager, demoFixSystemPrompt } from './utils/toolsDemo';

// 单独演示各个工具
await demoDebugUtils();
await demoSystemPromptManager();
await demoFixSystemPrompt();
```

## 常见使用场景

### 1. 应用刷新后出现重复数据错误
```typescript
// 使用 DebugUtils 修复
await DebugUtils.fixTemplatePreferencesDuplicates();
```

### 2. 系统提示词内容过时
```typescript
// 使用 FixSystemPrompt 修复
const result = await fixSystemPromptCache();
```

### 3. 缓存数据异常
```typescript
// 清理所有缓存并重新同步
await DebugUtils.clearAllCache();
await DebugUtils.forceResync();
```

### 4. 检查应用状态
```typescript
// 打印完整的调试信息
await DebugUtils.printDebugInfo();

// 检查系统提示词状态
const promptCheck = await checkSystemPromptContent();
```

## 注意事项

1. **动态导入**: 由于TypeScript类型实例化过深的问题，建议使用动态导入这些工具：
   ```typescript
   const { DebugUtils } = await import('./utils/debugUtils');
   ```

2. **开发模式**: 大部分调试工具在生产环境中应该被禁用或限制使用。

3. **错误处理**: 所有工具调用都应该包装在try-catch中，确保错误不会影响应用正常运行。

4. **缓存一致性**: 使用清理缓存功能时要注意可能会导致用户需要重新登录或重新配置。

## 故障排除

如果遇到"类型实例化过深"的错误，请：
1. 使用动态导入而不是静态导入
2. 确保没有循环引用
3. 检查TypeScript配置

如果工具无法正常工作，请：
1. 检查网络连接
2. 确认数据库连接正常
3. 查看控制台日志获取详细错误信息 