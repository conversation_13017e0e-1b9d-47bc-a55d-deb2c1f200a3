import { Dimensions } from 'react-native';
import Toast, { ToastShowParams } from 'react-native-toast-message';
import i18n from './i18n';
import { log } from '@/services/logService';

/**
 * 获取自定义Toast配置
 * 使Toast显示在屏幕距离底部15%的位置
 */
export const getToastOptions = (message: string, type: 'success' | 'error' | 'info' = 'success'): ToastShowParams => {
  // 获取屏幕尺寸
  const { height } = Dimensions.get('window');
  // 计算Toast应该距离底部的距离，使其在页面距离底部15%高度处显示
  const bottomOffset = Math.round(height * 0.15);
  
  return {
    type,
    text1: message,
    position: 'bottom' as const, // 使用const断言确保类型正确
    visibilityTime: 2000,
    bottomOffset,
  };
};

/**
 * 显示居中的Toast提示
 * @param message 提示信息
 * @param type 提示类型
 */
export const showCenteredToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
  Toast.show(getToastOptions(message, type));
};

// 全局变量，用于跟踪Badge组件的显示状态和设置函数
interface BadgeState {
  setShowCopyBadge?: (show: boolean) => void;
}

export const badgeState: BadgeState = {};

/**
 * 注册Badge显示函数
 * 各页面组件可以调用此函数注册自己的setShowCopyBadge函数
 * @param setShowBadge 显示Badge的函数
 */
export const registerBadgeShowFunction = (setShowBadge: (show: boolean) => void) => {
  badgeState.setShowCopyBadge = setShowBadge;
};

/**
 * 显示复制成功的Badge
 * 替代Toast提示，提供更优雅的用户体验
 */
export const showCopyBadge = () => {
  // 使用log而不是console.debug，确保在浏览器和终端都能看到日志
  log("📋 showCopyBadge函数被调用，badgeState:", {
    hasBadgeFunction: !!badgeState.setShowCopyBadge
  });
  
  if (badgeState.setShowCopyBadge) {
    log("📋 使用Badge显示复制成功提示");
    badgeState.setShowCopyBadge(true);
    // 2秒后自动隐藏
    setTimeout(() => {
      if (badgeState.setShowCopyBadge) {
        badgeState.setShowCopyBadge(false);
      }
    }, 2000);
  } else {
    // 如果没有注册Badge显示函数，则使用Toast作为备选
    log("📋 未找到Badge显示函数，使用Toast作为备选");
    showCenteredToast(i18n.t('common.copySuccess'));
  }
}; 