/**
 * 通用辅助函数
 */

/**
 * 生成唯一ID，不依赖crypto
 * @returns 随机生成的唯一ID字符串
 */
export const generateId = (): string => {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
};

/**
 * 生成UUID v4格式的ID（兼容数据库UUID字段）
 * @returns UUID格式的字符串
 */
export const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

/**
 * 格式化日期为人类可读形式
 * @param timestamp 时间戳（毫秒）
 * @param t 翻译函数，可选参数，如果不提供则返回中文
 * @returns 格式化的日期字符串
 */
export const formatDate = (timestamp: number, t?: (key: string) => string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  const twoDaysAgo = new Date(now);
  twoDaysAgo.setDate(now.getDate() - 2);
  
  // 检查是否是今天
  if (
    date.getDate() === now.getDate() &&
    date.getMonth() === now.getMonth() &&
    date.getFullYear() === now.getFullYear()
  ) {
    return t ? t("history.today") : "今天";
  }
  
  // 检查是否是昨天
  if (
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear()
  ) {
    return t ? t("history.yesterday") : "昨天";
  }
  
  // 检查是否是前天
  if (
    date.getDate() === twoDaysAgo.getDate() &&
    date.getMonth() === twoDaysAgo.getMonth() &&
    date.getFullYear() === twoDaysAgo.getFullYear()
  ) {
    return t ? t("history.dayBeforeYesterday") : "前天";
  }
  
  // 其他日期显示日期（不带时间）
  return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`;
};

/**
 * 格式化时间为HH:MM格式
 * @param date 日期对象
 * @returns 格式化的时间字符串
 */
const formatTime = (date: Date): string => {
  return `${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
};

/**
 * 数字补零
 * @param num 需要补零的数字
 * @returns 补零后的字符串
 */
const padZero = (num: number): string => {
  return num < 10 ? `0${num}` : `${num}`;
};

/**
 * 截断文本，超出部分显示省略号
 * @param text 需要截断的文本
 * @param length 最大长度
 * @returns 截断后的文本
 */
export const truncateText = (text: string, length: number): string => {
  if (!text) return '';
  return text.length <= length ? text : `${text.substring(0, length)}...`;
};

/**
 * 检查对象是否为空
 * @param obj 需要检查的对象
 * @returns 是否为空对象
 */
export const isEmptyObject = (obj: Record<string, any>): boolean => {
  return Object.keys(obj).length === 0;
}; 