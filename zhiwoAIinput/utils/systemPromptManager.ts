/**
 * 系统提示词管理工具
 * 提供系统提示词的管理和操作功能
 */
import { getSystemPrompt, refreshSystemPromptCache, clearSystemPromptCache, getCachedSystemPromptInfo } from '../services/systemPromptService';
import { systemPromptService } from '../services/supabaseService';
import { error as logError, log } from '@/services/logService';

/**
 * 系统提示词管理器
 */
export class SystemPromptManager {
  /**
   * 获取完整的系统提示词（包含模板替换）
   * @param templatePrompt 风格模板提示词
   * @returns 完整的系统提示词
   */
  static async getCompleteSystemPrompt(templatePrompt: string): Promise<string> {
    return await getSystemPrompt(templatePrompt);
  }

  /**
   * 刷新系统提示词缓存
   * @returns 是否刷新成功
   */
  static async refreshCache(): Promise<boolean> {
    return await refreshSystemPromptCache();
  }

  /**
   * 清除系统提示词缓存
   */
  static async clearCache(): Promise<void> {
    await clearSystemPromptCache();
  }

  /**
   * 获取缓存信息
   */
  static async getCacheInfo() {
    return await getCachedSystemPromptInfo();
  }

  /**
   * 从云端获取系统提示词信息
   * @param promptId 提示词ID
   */
  static async getFromCloud(promptId: string = 'default_system_prompt') {
    return await systemPromptService.getSystemPrompt(promptId);
  }

  /**
   * 更新云端系统提示词（管理员功能）
   * @param promptId 提示词ID
   * @param content 新的提示词内容
   * @param version 版本号
   */
  static async updateInCloud(promptId: string, content: string, version: number) {
    return await systemPromptService.updateSystemPrompt(promptId, content, version);
  }

  /**
   * 验证系统提示词格式
   * @param content 提示词内容
   * @returns 验证结果
   */
  static validatePromptFormat(content: string): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // 检查是否包含模板占位符
    if (!content.includes('${templatePrompt}')) {
      issues.push('缺少 ${templatePrompt} 占位符');
    }

    // 检查基本内容要求
    const requiredKeywords = ['文本优化', '原意', '语法', '标点符号'];
    for (const keyword of requiredKeywords) {
      if (!content.includes(keyword)) {
        issues.push(`缺少关键词: ${keyword}`);
      }
    }

    // 检查内容长度 - 优化后的提示词应该更简洁
    if (content.length < 50) {
      issues.push('提示词内容过短');
    }

    if (content.length > 2000) {
      issues.push('提示词内容过长，建议精简');
    }

    // 检查是否包含冗余的XML标签（已废弃）
    if (content.includes('<目标>') || content.includes('<正面标准>')) {
      issues.push('包含已废弃的XML标签，建议使用简化格式');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * 生成系统提示词预览
   * @param content 提示词模板内容
   * @param sampleTemplate 示例模板提示词
   * @returns 预览内容
   */
  static generatePreview(content: string, sampleTemplate: string = '示例风格模板内容'): string {
    return content.replace('${templatePrompt}', sampleTemplate);
  }

  /**
   * 检查系统提示词是否需要更新
   * @returns 检查结果
   */
  static async checkForUpdates(): Promise<{
    needsUpdate: boolean;
    localVersion?: number;
    cloudVersion?: number;
    cacheExpired: boolean;
  }> {
    try {
      // 获取本地缓存信息
      const cacheInfo = await this.getCacheInfo();

      // 获取云端信息
      const cloudInfo = await this.getFromCloud();

      if (!cloudInfo) {
        return {
          needsUpdate: false,
          cacheExpired: cacheInfo?.isExpired || true
        };
      }

      const needsUpdate = !cacheInfo?.hasCache ||
        Boolean(cacheInfo?.isExpired) ||
        (cacheInfo?.version && cacheInfo.version < cloudInfo.version) ||
        false;

      return {
        needsUpdate,
        localVersion: cacheInfo?.version,
        cloudVersion: cloudInfo.version,
        cacheExpired: Boolean(cacheInfo?.isExpired)
      };
    } catch (error) {
      logError('检查系统提示词更新失败:', error);
      return {
        needsUpdate: true,
        cacheExpired: true
      };
    }
  }

  /**
   * 自动更新系统提示词（如果需要）
   * @returns 是否进行了更新
   */
  static async autoUpdate(): Promise<boolean> {
    try {
      const updateCheck = await this.checkForUpdates();

      if (updateCheck.needsUpdate) {
        log('[SystemPromptManager] 检测到需要更新系统提示词');
        const success = await this.refreshCache();

        if (success) {
          log('[SystemPromptManager] 系统提示词自动更新成功');
          return true;
        } else {
          log('[SystemPromptManager] 系统提示词自动更新失败');
        }
      } else {
        log('[SystemPromptManager] 系统提示词无需更新');
      }

      return false;
    } catch (error) {
      logError('[SystemPromptManager] 系统提示词自动更新出错:', error);
      return false;
    }
  }
}

export default SystemPromptManager; 