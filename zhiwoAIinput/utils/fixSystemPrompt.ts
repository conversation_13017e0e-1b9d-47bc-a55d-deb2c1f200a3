/**
 * 系统提示词缓存修复工具
 * 用于立即清除缓存并强制从云端重新获取最新的系统提示词
 */

import { clearSystemPromptCache, refreshSystemPromptCache, getCachedSystemPromptInfo } from '../services/systemPromptService';
import { systemPromptService } from '../services/supabaseService';
import { log, error as logError } from '@/services/logService';

/**
 * 立即修复系统提示词缓存问题
 */
export const fixSystemPromptCache = async (): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> => {
  try {
    log('[FixSystemPrompt] 开始修复系统提示词缓存...');
    
    // 1. 获取当前缓存状态
    const oldCacheInfo = await getCachedSystemPromptInfo();
    log('[FixSystemPrompt] 修复前缓存状态:', oldCacheInfo);
    
    // 2. 清除本地缓存
    await clearSystemPromptCache();
    log('[FixSystemPrompt] 已清除本地缓存');
    
    // 3. 从云端强制刷新
    const refreshSuccess = await refreshSystemPromptCache();
    
    if (!refreshSuccess) {
      return {
        success: false,
        message: '无法从云端获取系统提示词，可能网络或数据库连接有问题',
        details: { oldCacheInfo }
      };
    }
    
    // 4. 验证新缓存
    const newCacheInfo = await getCachedSystemPromptInfo();
    log('[FixSystemPrompt] 修复后缓存状态:', newCacheInfo);
    
    // 5. 检查云端版本
    const cloudData = await systemPromptService.getSystemPrompt('default_system_prompt');
    
    return {
      success: true,
      message: '系统提示词缓存修复成功',
      details: {
        oldVersion: oldCacheInfo?.version,
        newVersion: newCacheInfo?.version,
        cloudVersion: cloudData?.version,
        cacheTime: newCacheInfo?.cacheTime,
        hasNewContent: newCacheInfo?.hasCache
      }
    };
    
  } catch (error) {
    logError('[FixSystemPrompt] 修复失败:', error);
    return {
      success: false,
      message: `修复失败: ${(error as Error).message}`,
      details: { error: (error as Error).toString() }
    };
  }
};

/**
 * 检查系统提示词是否包含最新内容
 */
export const checkSystemPromptContent = async (): Promise<{
  isLatest: boolean;
  hasTargetContent: boolean;
  version?: number;
  message: string;
}> => {
  try {
    // 获取当前使用的系统提示词（使用测试模板）
    const { getSystemPrompt } = await import('../services/systemPromptService');
    const currentPrompt = await getSystemPrompt('测试模板内容');
    
    // 检查是否包含新添加的内容
    const hasTargetContent = currentPrompt.includes('修改标点符号，以符合对应语言的标点符号使用习惯');
    
    // 获取缓存信息
    const cacheInfo = await getCachedSystemPromptInfo();
    
    // 获取云端版本
    const cloudData = await systemPromptService.getSystemPrompt('default_system_prompt');
    
    return {
      isLatest: hasTargetContent && (cacheInfo?.version === cloudData?.version),
      hasTargetContent,
      version: cacheInfo?.version,
      message: hasTargetContent 
        ? '系统提示词包含最新内容' 
        : '系统提示词缺少最新内容，建议运行修复程序'
    };
    
  } catch (error) {
    logError('[CheckSystemPrompt] 检查失败:', error);
    return {
      isLatest: false,
      hasTargetContent: false,
      message: `检查失败: ${(error as Error).message}`
    };
  }
};

/**
 * 手动更新云端系统提示词版本（管理员功能）
 */
export const incrementCloudSystemPromptVersion = async (): Promise<{
  success: boolean;
  newVersion: number;
  message: string;
}> => {
  try {
    // 获取当前云端版本
    const currentData = await systemPromptService.getSystemPrompt('default_system_prompt');
    
    if (!currentData) {
      return {
        success: false,
        newVersion: 0,
        message: '无法获取当前云端系统提示词'
      };
    }
    
    const newVersion = currentData.version + 1;
    
    // 更新版本号
    const updateResult = await systemPromptService.updateSystemPrompt(
      'default_system_prompt',
      currentData.prompt_content,
      newVersion
    );
    
    if (updateResult) {
      return {
        success: true,
        newVersion,
        message: `版本号已更新至 ${newVersion}`
      };
    } else {
      return {
        success: false,
        newVersion: currentData.version,
        message: '更新版本号失败'
      };
    }
    
  } catch (error) {
    logError('[IncrementVersion] 更新版本失败:', error);
    return {
      success: false,
      newVersion: 0,
      message: `更新版本失败: ${(error as Error).message}`
    };
  }
};

export default {
  fixSystemPromptCache,
  checkSystemPromptContent,
  incrementCloudSystemPromptVersion
}; 