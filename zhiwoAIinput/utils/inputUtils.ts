/**
 * 输入工具模块
 * 提供输入相关的辅助函数
 */
import { Template } from '@/components/input/TemplateCard';
import { nanoid } from 'nanoid/non-secure';
import * as AIService from '@/services/aiService';
import { saveHistoryRecord } from '@/services/storageService';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';
import { log, error as logError, warn } from '@/services/logService';
import * as FileSystem from 'expo-file-system';

/**
 * 安全地调用触觉反馈，在web平台上会自动跳过
 */
const safeHaptics = {
  impactAsync: async (style: Haptics.ImpactFeedbackStyle) => {
    if (Platform.OS !== 'web') {
      try {
        await Haptics.impactAsync(style);
      } catch (error) {
        log('触觉反馈失败，但不影响功能', error);
      }
    }
  },
  notificationAsync: async (type: Haptics.NotificationFeedbackType) => {
    if (Platform.OS !== 'web') {
      try {
        await Haptics.notificationAsync(type);
      } catch (error) {
        log('触觉反馈失败，但不影响功能', error);
      }
    }
  }
};

// 输入状态类型
export type InputState = 'initial' | 'recording' | 'template' | 'result';

/**
 * 获取模板的提示词
 * @param template 模板对象
 * @returns 模板提示词
 */
export const getTemplatePrompt = async (template: Template | null): Promise<string> => {
  if (!template) return '';

  try {
    // 尝试从模板同步服务获取提示词
    const { getTemplatePromptById } = await import('@/services/templateSyncService');
    const prompt = await getTemplatePromptById(template.id.toString());
    
    if (prompt) {
      log('[InputUtils] 使用同步服务获取的提示词');
      return prompt;
    }
    
    log('[InputUtils] 同步服务未找到提示词，使用兜底逻辑');
  } catch (error) {
    warn('[InputUtils] 获取同步提示词失败，使用兜底逻辑:', error);
  }

  // 兜底逻辑：使用硬编码的提示词（保持向后兼容）
  const promptPrefix = "# 文本优化要求\n\n";
  
  // 各模板专属提示词
  let promptContent = "";
  
  switch (template.id) {
    case 1: // 邮件格式
      promptContent = `## 邮件格式要求
- 按照邮件格式优化用户输入的文字
- 包含适当的问候语和结束语
- 突出显示行动项或请求内容，在格式上可以列成条目，以便阅读
- 确保语法和标点的正确性
- 保持原有的语气不变
- 尽可能多地使用原始文本内容
- 使用"谢谢，"或"祝好，"作为结束语
- 不知道收件人姓名时使用"您好，"作为问候语`;
      break;

    case 2: // Emoji风格 
      promptContent = `## Emoji添加规则
- 添加与内容高度相关的emoji
- 在关键词或重点内容旁添加相应emoji
- 在情感表达处添加对应情绪的emoji
- 确保emoji增强而不破坏阅读体验
- 避免过度使用，保持适当密度
- 根据语境选择合适的emoji，体现文本情感色彩`;
      break;

    case 3: // 领导汇报
      promptContent = `## 领导汇报格式
- 开头简明扼要，点明主题和目的
- 采用总分结构，先概述后详述
- 内容分点罗列，层次分明，重点突出
- 数据和事实客观呈现，用简洁语言描述复杂情况
- 有明确的问题分析和建议
- 结尾简洁，请领导指示或批示
- 使用恰当敬语，语气坚定专业但不失礼节`;
      break;

    case 4: // 朋友聊天
      promptContent = `## 朋友聊天风格
- 使用日常口语化表达，避免过于书面化
- 适当添加网络流行语和俏皮表达
- 保持轻松、亲切、自然的语气
- 可使用缩略语和简化句式
- 保持随意但有逻辑，像真实对话般有情感起伏
- 适当使用幽默元素和亲昵称呼`;
      break;

    case 5: // 恋人表达
      promptContent = `## 恋人交流风格
- 使用温柔深情的语气，传达恋人情感
- 适当使用温馨甜蜜的称呼和爱称
- 加入柔和优美的修辞和比喻
- 表达真挚的关心、想念或爱意
- 语言亲密但不做作，自然流露感情
- 整体风格温暖、甜蜜、真诚`;
      break;

    case 6: // 学术论文
      promptContent = `## 学术论文风格
- 使用专业、客观、精确的学术用语
- 句式严谨完整，逻辑性强，避免口语化
- 分段清晰，每段一个核心观点
- 陈述事实时引用数据或理论支持
- 避免主观情感色彩，保持客观中立
- 使用学术术语但避免不必要的晦涩
- 整体结构完整，有论证过程`;
      break;

    case 7: // 演讲稿
      promptContent = `## 演讲稿格式
- 形成有力的开场，吸引听众注意
- 使用排比、对比、设问等修辞手法增强表现力
- 适当重复关键信息以强调重点
- 使用简短有力的句子，保持节奏感
- 增加与听众的互动和共鸣元素
- 情感起伏明显，具有感染力和号召力
- 结尾有力量，留下深刻印象，呼应开头`;
      break;

    case 8: // 新闻报道
      promptContent = `## 新闻报道风格
- 开头简明扼要，包含核心信息(何人、何时、何地、何事)
- 采用倒金字塔结构，重要信息在前
- 客观陈述事实，避免主观评价
- 语言准确、简洁、清晰
- 适当引用消息来源或权威声明
- 使用第三人称叙述，保持专业中立
- 必要时提供背景信息和事件影响分析`;
      break;

    case 9: // 故事叙述
      promptContent = `## 故事叙述风格
- 增强情节性和叙事节奏感
- 添加生动的环境和场景描写
- 丰富人物形象和情感变化
- 增加感官描写和细节刻画
- 使用直接或间接引语增加真实感
- 构建起承转合的叙事结构
- 营造适当的故事氛围和情感基调
- 保持语言生动有画面感`;
      break;

    default:
      promptContent = `## 通用优化要求
- 保持原意完整不变
- 修正错别字、语法错误和不通顺表达
- 调整段落结构，使逻辑更清晰
- 改进用词，使表达更准确精炼
- 统一语言风格，保持一致性
- 增强可读性和流畅度`;
      break;
  }

  return promptPrefix + promptContent;
};

/**
 * 使用AI优化文本
 * @param text 原始文本
 * @param template 选择的模板
 * @param useRealApi 是否使用真实API（否则使用模拟数据）
 * @returns 优化后的文本
 */
export const processTextWithAI = async (
  text: string,
  template: Template | null,
  useRealApi: boolean = true
): Promise<string> => {
  try {
    // 触发轻微的触觉反馈
    safeHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // 获取模板提示词
    const prompt = await getTemplatePrompt(template);

    // 获取当前用户选择的模型
    const currentModel = await AIService.getCurrentLanguageModel();
    log(`[InputUtils] 获取到当前模型: ${currentModel}`);
    
    // 使用API服务
    const result = await AIService.optimizeText(text, prompt, currentModel);
    const optimizedResult = result.text;

    // 确保结果没有多余空格
    return optimizedResult.trim();
  } catch (error) {
    logError('处理文本失败:', error);
    // 触发错误反馈
    safeHaptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    throw error;
  }
};

/**
 * 保存输入会话到历史记录
 * @param originalText 原始文本
 * @param optimizedText 优化后的文本
 * @param template 使用的模板
 * @param audioUri 可选的音频URI
 * @returns 成功时返回记录ID，失败时返回void
 */
export const saveInputSession = async (
  originalText: string,
  optimizedText: string,
  template: Template | null,
  audioUri?: string
): Promise<string | void> => {
  if (!template) {
    log('saveInputSession: 无模板信息，跳过保存');
    return;
  }

  // 敏感词检查 - 双重保护机制，防止敏感词内容保存到历史记录
  try {
    const { filterSensitiveWords } = await import('@/services/sensitiveWordService');
    
    // 检查原始文本（理论上转录阶段已过滤，这里作为保险）
    const originalFilterResult = await filterSensitiveWords(originalText);
    if (originalFilterResult.isFiltered) {
      log('saveInputSession: 原始文本包含敏感词，跳过保存历史记录', originalFilterResult.detectedWords);
      return;
    }
    
    // 检查优化后文本
    const optimizedFilterResult = await filterSensitiveWords(optimizedText);
    if (optimizedFilterResult.isFiltered) {
      log('saveInputSession: 优化文本包含敏感词，跳过保存历史记录', optimizedFilterResult.detectedWords);
      return;
    }
  } catch (sensitiveWordError) {
    // 敏感词检查失败不应该阻止保存，只记录警告
    log('saveInputSession: 敏感词检查失败，继续保存:', sensitiveWordError);
  }

  try {
    // 创建唯一ID
    const recordId = nanoid();
    log('saveInputSession: 创建新记录，ID:', recordId);

    const record = {
      id: recordId,
      timestamp: Date.now(),
      originalText,
      optimizedText,
      templateId: template.id,
      templateName: template.title,
      audioUri
    };

    log('saveInputSession: 保存记录摘要:', {
      id: recordId,
      原文长度: originalText.length,
      优化文本长度: optimizedText.length,
      模板: template.title,
      音频: audioUri ? '有' : '无'
    });

    await saveHistoryRecord(record);
    log('saveInputSession: 记录保存成功，ID:', recordId);
    
    // 发送历史记录新增事件，通知历史记录页面刷新
    const { DeviceEventEmitter } = require('react-native');
    DeviceEventEmitter.emit('HISTORY_RECORD_ADDED');
    log('saveInputSession: 发送历史记录新增事件');
    
    return recordId;
  } catch (error) {
    logError('saveInputSession: 保存会话历史失败:', error);
  }
}; 

/**
 * 提取音频文件的相对路径（兼容老数据）
 * 只保留 Library/Caches/AV/recording-xxx.m4a 这种格式
 */
export function extractRelativeAudioPath(originPath: string): string {
  if (!originPath) return '';
  // 兼容 file:// 开头
  const path = originPath.replace(/^file:\/\//, '');
  // iOS: 匹配 Library/Caches/AV/recording-xxx.m4a
  const match = path.match(/(Library\/Caches\/AV\/recording-[^/]+\.m4a)$/);
  if (match) return match[1];
  // Android 或其它情况直接返回
  return path;
}

/**
 * 根据路径获取当前App下的音频文件绝对路径
 * 支持动态路径修复，适应沙盒路径变化
 */
export function getAudioFileAbsolutePath(audioPath: string): string {
  if (!audioPath) return '';

  // 对于绝对路径，需要进行动态路径修复以适应沙盒变化
  if (/^file:\/\//.test(audioPath)) {
    // 使用录音文件服务的路径修复逻辑
    return fixAudioPathForCurrentSandbox(audioPath);
  }

  // 对于相对路径，直接拼接到当前documentDirectory
  if (Platform.OS === 'ios') {
    // 兼容历史数据，去掉前导的 Library/ 前缀
    const rel = audioPath.replace(/^Library\//, '');
    return FileSystem.documentDirectory + rel;
  }

  // Android: 拼接到缓存目录
  return FileSystem.cacheDirectory + audioPath;
}

/**
 * 修复音频路径以适应当前沙盒
 * @param absolutePath 原始绝对路径
 * @returns 修复后的当前沙盒路径
 */
function fixAudioPathForCurrentSandbox(absolutePath: string): string {
  try {
    // 解析相对路径部分（从 Documents/ 开始）
    const relativePath = parseAudioOriginPath(absolutePath);

    // 基于当前沙盒重建完整路径
    return rebuildAudioFullPath(relativePath);
  } catch (error) {
    console.warn('音频路径修复失败，使用原始路径:', error);
    return absolutePath;
  }
}

/**
 * 解析音频文件的原始路径，提取相对路径部分
 * @param originPath 原始完整路径
 * @returns 相对路径（从 Documents/ 开始）
 */
function parseAudioOriginPath(originPath: string): string {
  if (!originPath) return '';

  // 移除 file:// 前缀
  let path = originPath.replace(/^file:\/\//, '');

  // 查找 Documents/ 部分，这是沙盒中的相对路径起点
  const documentsIndex = path.indexOf('Documents/');
  if (documentsIndex !== -1) {
    return path.substring(documentsIndex);
  }

  // 如果没找到 Documents/，查找 recordings/ 部分
  const recordingsIndex = path.indexOf('recordings/');
  if (recordingsIndex !== -1) {
    return 'Documents/' + path.substring(recordingsIndex);
  }

  // 如果都没找到，返回默认的录音路径格式
  const fileName = path.split('/').pop() || '';
  return `Documents/recordings/${fileName}`;
}

/**
 * 基于当前沙盒重建音频文件的完整路径
 * @param relativePath 相对路径（从 Documents/ 开始）
 * @returns 重建的完整路径
 */
function rebuildAudioFullPath(relativePath: string): string {
  // 获取当前沙盒的 Documents 目录
  const currentDocumentsDir = FileSystem.documentDirectory || '';

  // 如果相对路径以 Documents/ 开头，直接拼接当前沙盒路径
  if (relativePath.startsWith('Documents/')) {
    const pathWithoutDocuments = relativePath.replace('Documents/', '');
    return currentDocumentsDir + pathWithoutDocuments;
  }

  // 如果相对路径直接以 recordings/ 开头（兼容旧格式）
  if (relativePath.startsWith('recordings/')) {
    return currentDocumentsDir + relativePath;
  }

  // 其他情况，直接拼接
  return currentDocumentsDir + relativePath;
}