/**
 * 调试工具使用演示
 * 展示如何使用我们创建的调试和管理工具
 */

import { log, error as logError } from '@/services/logService';

/**
 * 演示如何使用 DebugUtils 清理缓存
 */
export const demoDebugUtils = async () => {
  log('=== DebugUtils 使用演示 ===');
  
  try {
    // 动态导入避免循环引用
    const { DebugUtils } = await import('./debugUtils');
    
    // 1. 获取当前缓存状态
    log('1. 获取缓存状态...');
    const cacheStatus = await DebugUtils.getCacheStatus();
    log('缓存状态:', cacheStatus);
    
    // 2. 打印调试信息
    log('2. 打印调试信息...');
    await DebugUtils.printDebugInfo();
    
    // 3. 清理所有缓存（可选）
    // await DebugUtils.clearAllCache();
    
    // 4. 强制重新同步（可选）
    // await DebugUtils.forceResync();
    
    log('DebugUtils 演示完成');
  } catch (error) {
    logError('DebugUtils 演示失败:', error);
  }
};

/**
 * 演示如何使用 SystemPromptManager 管理系统提示词
 */
export const demoSystemPromptManager = async () => {
  log('=== SystemPromptManager 使用演示 ===');
  
  try {
    // 动态导入避免循环引用
    const { SystemPromptManager } = await import('./systemPromptManager');
    
    // 1. 检查是否需要更新
    log('1. 检查系统提示词更新...');
    const updateCheck = await SystemPromptManager.checkForUpdates();
    log('更新检查结果:', updateCheck);
    
    // 2. 获取缓存信息
    log('2. 获取缓存信息...');
    const cacheInfo = await SystemPromptManager.getCacheInfo();
    log('缓存信息:', cacheInfo);
    
    // 3. 获取完整的系统提示词（示例）
    log('3. 获取系统提示词示例...');
    const prompt = await SystemPromptManager.getCompleteSystemPrompt('测试模板内容');
    log('系统提示词长度:', prompt.length);
    log('包含模板内容:', prompt.includes('测试模板内容'));
    
    // 4. 自动更新（如果需要）
    if (updateCheck.needsUpdate) {
      log('4. 执行自动更新...');
      await SystemPromptManager.autoUpdate();
    }
    
    log('SystemPromptManager 演示完成');
  } catch (error) {
    logError('SystemPromptManager 演示失败:', error);
  }
};

/**
 * 演示如何使用 fixSystemPrompt 修复系统提示词问题
 */
export const demoFixSystemPrompt = async () => {
  log('=== FixSystemPrompt 使用演示 ===');
  
  try {
    // 动态导入避免循环引用
    const { fixSystemPromptCache, checkSystemPromptContent } = await import('./fixSystemPrompt');
    
    // 1. 检查当前系统提示词内容
    log('1. 检查系统提示词内容...');
    const contentCheck = await checkSystemPromptContent();
    log('内容检查结果:', contentCheck);
    
    // 2. 如果需要，修复系统提示词缓存
    if (!contentCheck.isLatest || !contentCheck.hasTargetContent) {
      log('2. 修复系统提示词缓存...');
      const fixResult = await fixSystemPromptCache();
      log('修复结果:', fixResult);
    } else {
      log('2. 系统提示词已是最新，无需修复');
    }
    
    // 3. 再次检查确认修复结果
    log('3. 再次检查修复结果...');
    const finalCheck = await checkSystemPromptContent();
    log('最终检查结果:', finalCheck);
    
    log('FixSystemPrompt 演示完成');
  } catch (error) {
    logError('FixSystemPrompt 演示失败:', error);
  }
};

/**
 * 运行所有演示
 */
export const runAllDemos = async () => {
  log('🚀 开始运行所有调试工具演示...\n');
  
  await demoDebugUtils();
  log('\n');
  
  await demoSystemPromptManager();
  log('\n');
  
  await demoFixSystemPrompt();
  log('\n');
  
  log('✅ 所有演示完成');
};

export default {
  demoDebugUtils,
  demoSystemPromptManager,
  demoFixSystemPrompt,
  runAllDemos
}; 