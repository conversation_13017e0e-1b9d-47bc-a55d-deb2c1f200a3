/**
 * 语音提示词管理器
 * 提供语音提示词的管理和操作功能
 */

import { error as logError, log } from '@/services/logService';
/**
 * 语音提示词管理器
 */
export class SpeechPromptManager {
  /**
   * 获取语音提示词
   * @param language 语言代码
   * @param model 模型名称
   * @returns 语音提示词
   */
  static async getSpeechPrompt(language: string, model: string): Promise<string> {
    try {
      const { getSpeechPrompt } = await import('../services/speechPromptService');
      return await getSpeechPrompt(language, model);
    } catch (error) {
      logError('[SpeechPromptManager] 获取语音提示词失败:', error);
      // 返回fallback
      if (language === 'zh') {
        if (model === 'gpt-4o-mini-transcribe') {
          return '请准确转录语音内容，保持原始语言，添加标点符号。';
        } else {
          return '请准确转录语音内容，保持原始语言，添加标点符号。';
        }
      } else {
        return 'Please transcribe accurately, keep original language, add punctuation.';
      }
    }
  }

  /**
   * 刷新语音提示词缓存
   * @param promptKey 特定提示词键，可选
   * @returns 是否刷新成功
   */
  static async refreshCache(promptKey?: string): Promise<boolean> {
    try {
      const { refreshSpeechPromptCache } = await import('../services/speechPromptService');
      return await refreshSpeechPromptCache(promptKey);
    } catch (error) {
      logError('[SpeechPromptManager] 刷新缓存失败:', error);
      return false;
    }
  }

  /**
   * 清除语音提示词缓存
   * @param promptKey 特定提示词键，可选
   */
  static async clearCache(promptKey?: string): Promise<void> {
    try {
      const { clearSpeechPromptCache } = await import('../services/speechPromptService');
      await clearSpeechPromptCache(promptKey);
    } catch (error) {
      logError('[SpeechPromptManager] 清除缓存失败:', error);
    }
  }

  /**
   * 获取缓存信息
   * @param promptKey 特定提示词键，可选
   */
  static async getCacheInfo(promptKey?: string) {
    try {
      const { getCachedSpeechPromptInfo } = await import('../services/speechPromptService');
      return await getCachedSpeechPromptInfo(promptKey);
    } catch (error) {
      logError('[SpeechPromptManager] 获取缓存信息失败:', error);
      return null;
    }
  }

  /**
   * 检查语音提示词是否需要更新
   * @param promptKey 特定提示词键，可选
   * @returns 检查结果
   */
  static async checkForUpdates(promptKey?: string): Promise<{
    needsUpdate: boolean;
    localVersions?: Record<string, number>;
    cloudVersions?: Record<string, number>;
    cacheExpired: boolean;
  }> {
    try {
      // 获取本地缓存信息
      const cacheInfo = await this.getCacheInfo(promptKey);
      
      // 获取云端信息
      const { speechPromptService } = await import('../services/supabaseService');
      
      if (promptKey) {
        // 检查特定提示词
        const promptId = this.getPromptId(promptKey);
        const cloudInfo = await speechPromptService.getSpeechPrompt(promptId);
        
        if (!cloudInfo) {
          return {
            needsUpdate: false,
            cacheExpired: true
          };
        }

        const localInfo = cacheInfo as any;
        const needsUpdate = !localInfo?.hasCache || 
                           Boolean(localInfo?.isExpired) || 
                           (localInfo?.version && localInfo.version < cloudInfo.version) ||
                           false;

        return {
          needsUpdate,
          localVersions: { [promptKey]: localInfo?.version },
          cloudVersions: { [promptKey]: cloudInfo.version },
          cacheExpired: Boolean(localInfo?.isExpired)
        };
      } else {
        // 检查所有提示词
        const keys = ['chinese_gpt4o_mini', 'chinese_whisper', 'multilingual_common'];
        let needsUpdate = false;
        const localVersions: Record<string, number> = {};
        const cloudVersions: Record<string, number> = {};
        let cacheExpired = false;

        for (const key of keys) {
          const promptId = this.getPromptId(key);
          const cloudInfo = await speechPromptService.getSpeechPrompt(promptId);
          const localInfo = (cacheInfo as any)?.[key];

          if (localInfo) {
            localVersions[key] = localInfo.version || 0;
            if (localInfo.isExpired) cacheExpired = true;
          }

          if (cloudInfo) {
            cloudVersions[key] = cloudInfo.version;
            
            if (!localInfo?.hasCache || 
                Boolean(localInfo?.isExpired) || 
                (localInfo?.version && localInfo.version < cloudInfo.version)) {
              needsUpdate = true;
            }
          }
        }

        return {
          needsUpdate,
          localVersions,
          cloudVersions,
          cacheExpired
        };
      }
    } catch (error) {
      logError('[SpeechPromptManager] 检查更新失败:', error);
      return {
        needsUpdate: true,
        cacheExpired: true
      };
    }
  }

  /**
   * 自动更新语音提示词（如果需要）
   * @param promptKey 特定提示词键，可选
   * @returns 是否进行了更新
   */
  static async autoUpdate(promptKey?: string): Promise<boolean> {
    try {
      const updateCheck = await this.checkForUpdates(promptKey);
      
      if (updateCheck.needsUpdate) {
        log('[SpeechPromptManager] 检测到需要更新语音提示词');
        const success = await this.refreshCache(promptKey);
        
        if (success) {
          log('[SpeechPromptManager] 语音提示词自动更新成功');
          return true;
        } else {
          log('[SpeechPromptManager] 语音提示词自动更新失败');
        }
      } else {
        log('[SpeechPromptManager] 语音提示词无需更新');
      }

      return false;
    } catch (error) {
      logError('[SpeechPromptManager] 语音提示词自动更新出错:', error);
      return false;
    }
  }

  /**
   * 将内部key转换为数据库ID
   */
  private static getPromptId(promptKey: string): string {
    switch (promptKey) {
      case 'chinese_gpt4o_mini':
        return 'chinese_gpt4o_mini_transcribe';
      case 'chinese_whisper':
        return 'chinese_whisper';
      case 'multilingual_common':
        return 'multilingual_common';
      default:
        return promptKey;
    }
  }
}

export default SpeechPromptManager; 