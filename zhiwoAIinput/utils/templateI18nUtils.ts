/**
 * 风格模板多语言工具函数
 * 提供风格模板的国际化支持
 */

import { warn } from "@/services/logService";

// 数据库中的风格模板接口
export interface StyleTemplateFromDB {
  id: string;
  name: string;
  description: string;
  name_key?: string;
  description_key?: string;
  prompt_text: string;
  is_system: boolean;
  color?: string;
  icon?: string;
  position?: number;
  is_active?: boolean;
  user_id?: string;
  created_at?: string;
  updated_at?: string;
}

// 风格模板显示数据接口
export interface StyleTemplateDisplayData {
  id: string;
  name: string;
  description: string;
  prompt_text: string;
  is_system: boolean;
  color?: string;
  icon?: string;
  position?: number;
  is_active?: boolean;
  user_id?: string;
  created_at?: string;
  updated_at?: string;
}

// 模板名称到semantic_id的映射表
const templateNameToSemanticId: { [key: string]: string } = {
  '发邮件': 'email',
  '日常发信息': 'chat',
  'emoji表情': 'emoji',
  '做notes记录': 'notes',
  'markdown格式的要点记录': 'markdown_notes',
  '原文优化': 'text_optimize',
  '小红书文案': 'xiaohongshu',
  '朋友圈文案': 'moments',
  '英语翻译': 'en_translation',
  '日语翻译': 'ja_translation',
  '韩语翻译': 'ko_translation',
  '印尼语翻译': 'id_translation',
  '德语翻译': 'de_translation',
  '法语翻译': 'fr_translation',
  '西班牙语翻译': 'es_translation',
  'Recraft生图': 'recraft',
  'Midjourney V7': 'midjourney_v7',
  'Stable Diffusion': 'stable_diffusion',
  '即梦AI': 'jimeng_ai',
  '正式公文': 'official',
  '学术论文': 'academic',
  '新闻报道': 'news',
  '创意文案': 'creative',
  '口语化表达': 'casual',
  'direct_transcription': 'direct_transcription'
};

// semantic_id 到 i18n key 的映射
export const semanticIdToI18nKey: Record<string, string> = {
  email: "email",
  chat: "chat",
  emoji: "emoji",
  notes: "notes",
  markdown_notes: "markdown_notes",
  text_optimize: "text_optimize",
  xiaohongshu: "xhs",
  moments: "moment",
  en_translation: "enTrans",
  ja_translation: "jaTrans",
  ko_translation: "koTrans",
  id_translation: "idTrans",
  de_translation: "deTrans",
  fr_translation: "frTrans",
  es_translation: "esTrans",
  recraft: "recraft",
  midjourney_v7: "midjourney",
  stable_diffusion: "stableDiffusion",
  jimeng_ai: "dreamAI",
  official: "official",
  academic: "academic",
  news: "news",
  creative: "creative",
  casual: "casual",
  direct_transcription: "direct_transcription"
};

/**
 * 根据模板名称获取semantic_id
 */
export function getTemplateSemanticId(templateName: string): string | undefined {
  return templateNameToSemanticId[templateName];
}

/**
 * 获取风格模板的本地化名称
 */
export function getTemplateDisplayName(
  template: StyleTemplateFromDB,
  t: (key: string) => string
): string {
  // 如果是系统模板且有name_key，使用翻译
  if (template.is_system && template.name_key) {
    try {
      const translatedName = t(template.name_key);
      if (translatedName && translatedName !== template.name_key) {
        return translatedName;
      }
    } catch (error) {
      warn(`Failed to translate template name key: ${template.name_key}`, error);
    }
  }
  
  // 降级到原始名称
  return template.name || '';
}

/**
 * 获取风格模板的本地化描述
 */
export function getTemplateDisplayDescription(
  template: StyleTemplateFromDB,
  t: (key: string) => string
): string {
  // 优先使用多语言key
  if (template.description_key) {
    try {
      const translatedDescription = t(template.description_key);
      // 如果翻译成功且不等于key本身，使用翻译结果
      if (translatedDescription && translatedDescription !== template.description_key) {
        return translatedDescription;
      }
    } catch (error) {
      warn(`Failed to translate template description key: ${template.description_key}`, error);
    }
  }
  
  // 降级到原始描述
  return template.description || '';
}

/**
 * 将数据库风格模板数组转换为多语言显示版本
 */
export function transformTemplatesWithI18n(
  templates: StyleTemplateFromDB[],
  t: (key: string) => string
): StyleTemplateDisplayData[] {
  return templates.map(template => ({
    ...template,
    name: getTemplateDisplayName(template, t),
    description: getTemplateDisplayDescription(template, t)
  }));
}

/**
 * 将单个数据库风格模板转换为多语言显示版本
 */
export function transformTemplateWithI18n(
  template: StyleTemplateFromDB,
  t: (key: string) => string
): StyleTemplateDisplayData {
  return {
    ...template,
    name: getTemplateDisplayName(template, t),
    description: getTemplateDisplayDescription(template, t)
  };
} 