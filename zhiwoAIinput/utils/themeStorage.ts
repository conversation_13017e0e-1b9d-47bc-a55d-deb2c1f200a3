import AsyncStorage from '@react-native-async-storage/async-storage';
import { ThemeMode } from '@/store/slices/themeSlice';
import { error as logError } from '@/services/logService';

// 存储键
const THEME_STORAGE_KEY = '@knowme_app_theme_mode';

/**
 * 保存主题模式到AsyncStorage
 * @param themeMode 主题模式
 */
export async function saveThemeMode(themeMode: ThemeMode): Promise<void> {
  try {
    await AsyncStorage.setItem(THEME_STORAGE_KEY, themeMode);
  } catch (error) {
    logError('保存主题模式失败:', error);
  }
}

/**
 * 从AsyncStorage获取保存的主题模式
 * @returns 保存的主题模式或默认值'system'
 */
export async function getThemeMode(): Promise<ThemeMode> {
  try {
    const value = await AsyncStorage.getItem(THEME_STORAGE_KEY);
    // 检查值是否存在并且是有效的ThemeMode
    if (value !== null && (value === 'light' || value === 'dark' || value === 'system')) {
      return value as ThemeMode;
    }
  } catch (error) {
    logError('获取主题模式失败:', error);
  }
  // 如果没有保存的值或出现错误，返回默认值
  return 'system';
} 