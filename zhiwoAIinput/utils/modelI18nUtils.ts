/**
 * AI模型多语言支持工具
 * 提供模型名称和描述的多语言翻译功能
 */

// 从数据库返回的AI模型接口
export interface AIModelFromDB {
  id: string;
  model_id: string;
  name: string;
  description: string;
  name_key?: string;        // 多语言key
  description_key?: string; // 多语言key
  is_vip_only: boolean;
  model_type: 'text' | 'voice';
  provider: string;
  api_key_type: string;
  sort_order: number;
  is_active: boolean;
}

// 带多语言支持的AI模型接口
export interface AIModelWithI18n extends AIModelFromDB {
  displayName: string;       // 本地化显示名称
  displayDescription: string; // 本地化显示描述
}

/**
 * 获取模型的本地化显示名称
 * @param model 模型数据
 * @param t 翻译函数
 * @returns 本地化的显示名称
 */
export const getModelDisplayName = (model: AIModelFromDB, t: (key: string) => string): string => {
  // 如果有多语言key，使用翻译
  if (model.name_key) {
    const translated = t(model.name_key);
    // 如果翻译成功（不等于key本身），返回翻译结果
    if (translated && translated !== model.name_key) {
      return translated;
    }
  }
  
  // 否则回退到原始名称
  return model.name || model.model_id;
};

/**
 * 获取模型的本地化显示描述
 * @param model 模型数据
 * @param t 翻译函数
 * @returns 本地化的显示描述
 */
export const getModelDisplayDescription = (model: AIModelFromDB, t: (key: string) => string): string => {
  // 如果有多语言key，使用翻译
  if (model.description_key) {
    const translated = t(model.description_key);
    // 如果翻译成功（不等于key本身），返回翻译结果
    if (translated && translated !== model.description_key) {
      return translated;
    }
  }
  
  // 否则回退到原始描述
  return model.description || '';
};

/**
 * 转换模型数组为带多语言支持的模型数组
 * @param models 原始模型数组
 * @param t 翻译函数
 * @returns 带多语言支持的模型数组
 */
export const transformModelsWithI18n = (
  models: AIModelFromDB[], 
  t: (key: string) => string
): AIModelWithI18n[] => {
  return models.map(model => ({
    ...model,
    displayName: getModelDisplayName(model, t),
    displayDescription: getModelDisplayDescription(model, t),
  }));
};

/**
 * 根据模型ID获取单个模型的多语言信息
 * @param models 模型数组
 * @param modelId 模型ID
 * @param t 翻译函数
 * @returns 带多语言支持的模型，如果未找到返回null
 */
export const getModelByIdWithI18n = (
  models: AIModelFromDB[], 
  modelId: string, 
  t: (key: string) => string
): AIModelWithI18n | null => {
  const model = models.find(m => m.model_id === modelId);
  if (!model) return null;
  
  return {
    ...model,
    displayName: getModelDisplayName(model, t),
    displayDescription: getModelDisplayDescription(model, t),
  };
};

/**
 * 创建模型选择列表项
 * @param model 模型数据
 * @param t 翻译函数
 * @returns 用于显示的模型信息
 */
export const createModelOption = (model: AIModelFromDB, t: (key: string) => string) => {
  return {
    value: model.model_id,
    label: getModelDisplayName(model, t),
    description: getModelDisplayDescription(model, t),
    isVip: model.is_vip_only,
    provider: model.provider,
    modelType: model.model_type,
  };
}; 