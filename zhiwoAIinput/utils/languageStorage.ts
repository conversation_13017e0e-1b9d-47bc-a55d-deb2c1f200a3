import AsyncStorage from '@react-native-async-storage/async-storage';
import { LanguageCode } from '@/store/slices/languageSlice';
import { error as logError } from '@/services/logService';

// 存储键
const LANGUAGE_STORAGE_KEY = '@knowme_app_language';

/**
 * 保存语言代码到AsyncStorage
 * @param language 语言代码
 */
export async function saveLanguage(language: LanguageCode): Promise<void> {
  try {
    await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language);
  } catch (error) {
    logError('保存语言设置失败:', error);
  }
}

/**
 * 从AsyncStorage获取保存的语言代码
 * @returns 保存的语言代码或默认值'zh-Hans'
 */
export async function getLanguage(): Promise<LanguageCode> {
  try {
    const value = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
    // 检查值是否存在并且是有效的LanguageCode
    if (value !== null && (value === 'zh-Hans' || value === 'zh-Hant' || value === 'en')) {
      return value as LanguageCode;
    }
  } catch (error) {
    logError('获取语言设置失败:', error);
  }
  // 如果没有保存的值或出现错误，返回默认值
  return 'zh-Hans';
}
