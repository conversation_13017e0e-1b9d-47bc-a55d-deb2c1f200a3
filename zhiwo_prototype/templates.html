<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知我AI输入法 - 风格模板</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-white">
    <div class="device-frame">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="time font-semibold">9:41</div>
            <div class="right-icons flex items-center">
                <i class="fas fa-signal mr-1"></i>
                <i class="fas fa-wifi mr-1"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="app-content">
            <!-- 模板管理页面 -->
            <div class="p-4">
                <div class="flex items-center justify-between mb-5">
                    <div class="flex items-center">
                        <button class="text-gray-500 mr-2">
                            <i class="fas fa-chevron-left text-lg"></i>
                        </button>
                        <h1 class="text-xl font-bold text-gray-800">风格模板管理</h1>
                    </div>
                    <button class="text-blue-500">
                        <i class="fas fa-plus text-lg"></i>
                    </button>
                </div>

                <!-- 系统模板 -->
                <div class="mb-6">
                    <div class="text-sm text-gray-500 mb-3">系统模板</div>
                    
                    <div class="template-item bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-3">
                        <div class="flex justify-between items-start mb-2">
                            <div class="font-medium text-gray-800">邮件格式</div>
                            <div class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded">系统</div>
                        </div>
                        <div class="text-gray-600 text-sm mb-3">
                            将文本整理成正式的商务邮件格式，包含称呼、正文、结束语等标准结构。
                        </div>
                        <div class="text-xs text-gray-500 italic bg-gray-50 p-2 rounded">
                            请将以下文本整理成一封正式的商务邮件，包含适当的称呼、正文段落划分、结束语，保持专业的语气和格式。
                        </div>
                    </div>
                    
                    <div class="template-item bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-3">
                        <div class="flex justify-between items-start mb-2">
                            <div class="font-medium text-gray-800">Emoji风格</div>
                            <div class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded">系统</div>
                        </div>
                        <div class="text-gray-600 text-sm mb-3">
                            为文本添加生动有趣的表情符号，使内容更加活泼形象。
                        </div>
                        <div class="text-xs text-gray-500 italic bg-gray-50 p-2 rounded">
                            请为以下文本添加适当的emoji表情符号，使其更加生动有趣，每个关键点或段落都应有相关的表情，但要保持文本的原意和清晰度。
                        </div>
                    </div>
                    
                    <div class="template-item bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-3">
                        <div class="flex justify-between items-start mb-2">
                            <div class="font-medium text-gray-800">领导汇报</div>
                            <div class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded">系统</div>
                        </div>
                        <div class="text-gray-600 text-sm mb-3">
                            将内容整理成简明扼要的汇报格式，突出重点，适合向上级汇报。
                        </div>
                        <div class="text-xs text-gray-500 italic bg-gray-50 p-2 rounded">
                            请将以下内容整理成向领导汇报的格式，语言简洁明了，重点突出，条理清晰，使用恰当的敬语，但不过于冗长。
                        </div>
                    </div>
                </div>

                <!-- 自定义模板 -->
                <div class="mb-6">
                    <div class="text-sm text-gray-500 mb-3">自定义模板</div>
                    
                    <div class="template-item bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-3">
                        <div class="flex justify-between items-start mb-2">
                            <div class="font-medium text-gray-800">周报模板</div>
                            <div class="flex space-x-2">
                                <button class="text-gray-400">
                                    <i class="fas fa-pencil-alt"></i>
                                </button>
                                <button class="text-gray-400">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="text-gray-600 text-sm mb-3">
                            将内容整理成规范的周工作总结，包含本周工作、问题和计划。
                        </div>
                        <div class="text-xs text-gray-500 italic bg-gray-50 p-2 rounded">
                            请将以下文本整理成周报格式，分为三部分：1. 本周完成工作；2. 遇到的问题与挑战；3. 下周工作计划。保持条理清晰，语言简练专业。
                        </div>
                    </div>
                    
                    <div class="template-item bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-3">
                        <div class="flex justify-between items-start mb-2">
                            <div class="font-medium text-gray-800">会议纪要</div>
                            <div class="flex space-x-2">
                                <button class="text-gray-400">
                                    <i class="fas fa-pencil-alt"></i>
                                </button>
                                <button class="text-gray-400">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="text-gray-600 text-sm mb-3">
                            将内容整理成标准会议纪要格式，包含参会人员、讨论内容和下一步计划。
                        </div>
                        <div class="text-xs text-gray-500 italic bg-gray-50 p-2 rounded">
                            请将以下内容整理为会议纪要，包含会议主题、参会人员、主要讨论内容、达成的决定以及后续行动项。格式规范，要点清晰。
                        </div>
                    </div>
                </div>

                <!-- 添加模板按钮 -->
                <div class="mt-6">
                    <button class="w-full border border-blue-500 rounded-lg py-3 text-blue-500 bg-white flex items-center justify-center">
                        <i class="fas fa-plus mr-2"></i>
                        添加新模板
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <div class="tab-icon"><i class="fas fa-microphone"></i></div>
                <div>输入</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon"><i class="fas fa-history"></i></div>
                <div>历史</div>
            </div>
            <div class="tab-item active">
                <div class="tab-icon"><i class="fas fa-sliders-h"></i></div>
                <div>设置</div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html> 