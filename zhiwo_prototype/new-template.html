<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知我AI输入法 - 新建模板</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-white">
    <div class="device-frame">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="time font-semibold">9:41</div>
            <div class="right-icons flex items-center">
                <i class="fas fa-signal mr-1"></i>
                <i class="fas fa-wifi mr-1"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="app-content">
            <!-- 新建模板页面 -->
            <div class="p-4">
                <div class="flex items-center justify-between mb-5">
                    <div class="flex items-center">
                        <button class="text-gray-500 mr-2">
                            <i class="fas fa-chevron-left text-lg"></i>
                        </button>
                        <h1 class="text-xl font-bold text-gray-800">新建模板</h1>
                    </div>
                    <button class="text-blue-500 font-medium">
                        保存
                    </button>
                </div>

                <!-- 模板表单 -->
                <div class="form-container">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">模板名称</label>
                        <input 
                            type="text" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                            placeholder="例如：销售报告、会议纪要..."
                            value="产品开发周报"
                        >
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">模板描述</label>
                        <input 
                            type="text" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                            placeholder="简要描述模板的用途..."
                            value="整理产品迭代进展，包含进度、问题和下一步计划"
                        >
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">指导提示词</label>
                        <textarea 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-40" 
                            placeholder="请输入AI处理文本时使用的详细指导提示词，越具体越好..."
                        >请将以下内容整理为产品开发周报格式，分为四个部分：
1. 本周产品开发概述（简要总结）
2. 主要进展与成果（按照重要性排序，条理清晰）
3. 遇到的问题与解决方案（简明扼要）
4. 下周工作计划（可使用清单格式）

整体风格要专业、简洁，重点突出，便于产品经理向团队和领导汇报。</textarea>
                        <div class="text-xs text-gray-500 mt-1">
                            提示词越具体，AI优化效果越好。
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">模板分类</label>
                        <div class="flex flex-wrap gap-2">
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700 flex items-center">
                                工作
                                <i class="fas fa-check-circle ml-1 text-blue-500"></i>
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700">
                                个人
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700">
                                邮件
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700 flex items-center">
                                会议
                                <i class="fas fa-check-circle ml-1 text-blue-500"></i>
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700">
                                社交
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700 flex items-center">
                                正式
                                <i class="fas fa-check-circle ml-1 text-blue-500"></i>
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700">
                                休闲
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700 bg-gray-100">
                                <i class="fas fa-plus mr-1 text-xs"></i>
                                自定义
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">测试文本</label>
                        <textarea 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-24" 
                            placeholder="输入样例文本来测试你的模板..."
                        >本周我们完成了移动端新功能的开发，包括用户个人资料页改版和消息通知系统优化。团队遇到了一些API兼容性问题，导致测试阶段发现了几个bug，目前正在修复中。下周计划开始新的支付流程优化，同时需要准备月底的版本发布。另外，设计团队提出了新的色彩方案，需要与开发团队讨论实施可行性。</textarea>
                    </div>
                    
                    <div class="mt-6">
                        <button id="preview-btn" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 rounded-lg flex items-center justify-center">
                            <i class="fas fa-bolt mr-2"></i>
                            预览效果
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <div class="tab-icon"><i class="fas fa-microphone"></i></div>
                <div>输入</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon"><i class="fas fa-history"></i></div>
                <div>历史</div>
            </div>
            <div class="tab-item active">
                <div class="tab-icon"><i class="fas fa-sliders-h"></i></div>
                <div>设置</div>
            </div>
        </div>
    </div>

    <!-- 预览模态框 -->
    <div id="preview-modal" class="modal-backdrop">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="font-medium text-lg">模板预览效果</h3>
                <button id="close-modal" class="text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="comparison-toggle">
                    <div id="optimized-toggle" class="toggle-option active">优化后</div>
                    <div class="toggle-divider"></div>
                    <div id="original-toggle" class="toggle-option">原文本</div>
                </div>

                <div id="optimized-view" class="preview-container">
                    <div class="preview-content">
                        <h4 class="font-bold mb-3">本周产品开发概述</h4>
                        <p class="mb-4">完成移动端新功能开发，主要包括用户个人资料页改版和消息通知系统优化，同时进行了相关测试和bug修复工作。</p>
                        
                        <h4 class="font-bold mb-2">主要进展与成果</h4>
                        <ol class="list-decimal pl-5 mb-4">
                            <li class="mb-1">完成用户个人资料页面改版开发</li>
                            <li class="mb-1">优化消息通知系统，提升用户体验</li>
                            <li>开始测试阶段，发现并记录相关问题</li>
                        </ol>
                        
                        <h4 class="font-bold mb-2">遇到的问题与解决方案</h4>
                        <p class="mb-4">API兼容性问题导致测试阶段发现几个bug，目前团队正在进行修复工作，预计下周初完成。</p>
                        
                        <h4 class="font-bold mb-2">下周工作计划</h4>
                        <ul class="list-disc pl-5">
                            <li class="mb-1">开始新的支付流程优化开发</li>
                            <li class="mb-1">准备月底版本发布相关工作</li>
                            <li class="mb-1">与设计团队讨论新色彩方案实施可行性</li>
                            <li>完成本周遗留bug修复工作</li>
                        </ul>
                    </div>
                </div>

                <div id="original-view" class="preview-container hidden">
                    <div class="preview-content">
                        <p>本周我们完成了移动端新功能的开发，包括用户个人资料页改版和消息通知系统优化。团队遇到了一些API兼容性问题，导致测试阶段发现了几个bug，目前正在修复中。下周计划开始新的支付流程优化，同时需要准备月底的版本发布。另外，设计团队提出了新的色彩方案，需要与开发团队讨论实施可行性。</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="back-btn" class="btn-secondary px-4">返回调整</button>
                <button id="apply-btn" class="btn-primary px-4">直接应用</button>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script>
        // 模板预览功能
        document.addEventListener('DOMContentLoaded', function() {
            const previewBtn = document.getElementById('preview-btn');
            const previewModal = document.getElementById('preview-modal');
            const closeModalBtn = document.getElementById('close-modal');
            const backBtn = document.getElementById('back-btn');
            const applyBtn = document.getElementById('apply-btn');
            const optimizedToggle = document.getElementById('optimized-toggle');
            const originalToggle = document.getElementById('original-toggle');
            const optimizedView = document.getElementById('optimized-view');
            const originalView = document.getElementById('original-view');

            // 显示预览模态框
            if (previewBtn) {
                previewBtn.addEventListener('click', function() {
                    previewModal.classList.add('show');
                });
            }

            // 关闭模态框
            if (closeModalBtn) {
                closeModalBtn.addEventListener('click', function() {
                    previewModal.classList.remove('show');
                });
            }

            // 返回调整按钮
            if (backBtn) {
                backBtn.addEventListener('click', function() {
                    previewModal.classList.remove('show');
                });
            }

            // 直接应用按钮
            if (applyBtn) {
                applyBtn.addEventListener('click', function() {
                    // 显示保存成功提示（实际应用中会保存到服务器）
                    previewModal.classList.remove('show');
                    alert('模板已保存成功！');
                    // 在实际应用中，这里会跳转到模板管理页面
                });
            }

            // 切换显示优化后/原文本
            if (optimizedToggle && originalToggle) {
                optimizedToggle.addEventListener('click', function() {
                    optimizedToggle.classList.add('active');
                    originalToggle.classList.remove('active');
                    optimizedView.classList.remove('hidden');
                    originalView.classList.add('hidden');
                });

                originalToggle.addEventListener('click', function() {
                    originalToggle.classList.add('active');
                    optimizedToggle.classList.remove('active');
                    originalView.classList.remove('hidden');
                    optimizedView.classList.add('hidden');
                });
            }
        });
    </script>
</body>
</html> 