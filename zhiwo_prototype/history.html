<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知我AI输入法 - 历史记录</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        /* 语音播放控件样式 */
        .audio-player {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .play-button {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #4f46e5;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            flex-shrink: 0;
            transition: all 0.2s;
        }
        
        .play-button:hover {
            background-color: #4338ca;
            transform: scale(1.05);
        }
        
        .progress-bar {
            height: 4px;
            background-color: #e5e7eb;
            border-radius: 2px;
            margin: 0 8px;
            position: relative;
            flex-grow: 1;
            overflow: hidden;
        }
        
        .progress {
            height: 100%;
            background-color: #4f46e5;
            border-radius: 2px;
            width: 0%;
            transition: width 0.1s linear;
        }
        
        .time-display {
            font-size: 10px;
            color: #6b7280;
            min-width: 34px;
            text-align: right;
        }
        
        /* 详情页样式 */
        .detail-view {
            display: none;
            height: 100%;
            flex-direction: column;
        }
        
        .detail-view.active {
            display: flex;
        }
        
        .detail-content {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            overflow: hidden;
        }
        
        .split-view {
            display: flex;
            height: 100%;
        }
        
        .column {
            flex: 1;
            overflow-y: auto;
            padding: 12px;
        }
        
        .column-left {
            border-right: 1px solid #e5e7eb;
            background-color: #f9fafb;
        }
        
        .column-right {
            background-color: white;
        }
        
        .timestamp-mark {
            display: inline-flex;
            align-items: center;
            font-size: 10px;
            color: #6b7280;
            background-color: #f3f4f6;
            padding: 0 4px;
            border-radius: 4px;
            margin-right: 4px;
            vertical-align: middle;
        }
        
        .diff-highlight {
            background-color: #dcfce7;
            border-radius: 2px;
            padding: 0 2px;
        }
        
        /* 返回按钮动画 */
        .back-button {
            transition: transform 0.2s;
        }
        
        .back-button:hover {
            transform: translateX(-2px);
        }
        
        /* 内容摘要 */
        .content-summary {
            position: relative;
        }
        
        .expand-indicator {
            position: absolute;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 30%);
            padding-left: 20px;
            color: #4f46e5;
            font-size: 11px;
            cursor: pointer;
        }
    </style>
</head>
<body class="bg-white">
    <div class="device-frame">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="time font-semibold">9:41</div>
            <div class="right-icons flex items-center">
                <i class="fas fa-signal mr-1"></i>
                <i class="fas fa-wifi mr-1"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="app-content">
            <!-- 历史记录列表页 -->
            <div id="history-list-view" class="p-4">
                <div class="flex items-center justify-between mb-5">
                    <h1 class="text-2xl font-bold text-gray-800">历史记录</h1>
                    <div class="flex">
                        <button class="text-gray-500 p-2 bg-gray-100 rounded-full">
                            <i class="fas fa-search text-sm"></i>
                        </button>
                    </div>
                </div>

                <div class="flex mb-4 space-x-2 overflow-x-auto -mx-4 px-4 pb-2">
                    <div class="inline-block px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm whitespace-nowrap">
                        全部
                    </div>
                    <div class="inline-block px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">
                        工作相关
                    </div>
                    <div class="inline-block px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">
                        邮件
                    </div>
                    <div class="inline-block px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">
                        会议纪要
                    </div>
                    <div class="inline-block px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">
                        聊天
                    </div>
                </div>

                <!-- 历史记录列表 -->
                <div class="history-list">
                    <!-- 日期分组 -->
                    <div class="date-group mb-4">
                        <div class="text-xs text-gray-500 mb-2">今天</div>
                        
                        <!-- 历史项目 -->
                        <div class="history-item bg-white rounded-xl shadow-sm border border-gray-100 mb-3 p-3" onclick="showDetailView()">
                            <div class="flex justify-between items-start mb-2">
                                <div class="font-medium text-gray-800">邮件格式</div>
                                <div class="text-xs text-gray-400">今天 14:30</div>
                            </div>

                            <!-- 新增：语音播放控件 -->
                            <div class="audio-player">
                                <div class="play-button">
                                    <i class="fas fa-play text-xs"></i>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress" style="width: 30%;"></div>
                                </div>
                                <div class="time-display">1:25</div>
                            </div>

                            <!-- 新增：内容摘要（限制60字符） -->
                            <div class="content-summary text-gray-600 text-sm mb-2">
                                <div class="truncated-text">尊敬的张总：您好！感谢您今天抽出宝贵时间讨论新项目的进展情况。非常高兴您对我们...</div>
                                <div class="expand-indicator">展开</div>
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="flex space-x-2">
                                    <span class="inline-block px-2 py-1 bg-blue-50 text-blue-600 rounded text-xs">
                                        工作
                                    </span>
                                </div>
                                <div class="flex space-x-3">
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-copy text-sm"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-ellipsis-v text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="history-item bg-white rounded-xl shadow-sm border border-gray-100 mb-3 p-3" onclick="showDetailView()">
                            <div class="flex justify-between items-start mb-2">
                                <div class="font-medium text-gray-800">Emoji风格</div>
                                <div class="text-xs text-gray-400">今天 11:15</div>
                            </div>

                            <!-- 语音播放控件 -->
                            <div class="audio-player">
                                <div class="play-button">
                                    <i class="fas fa-play text-xs"></i>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress" style="width: 50%;"></div>
                                </div>
                                <div class="time-display">2:10</div>
                            </div>

                            <!-- 内容摘要 -->
                            <div class="content-summary text-gray-600 text-sm mb-2">
                                <div class="truncated-text">📝 会议总结 ✨ 今天早上的产品讨论超有收获！🔍 我们确定了三个关键功能：1️⃣...</div>
                                <div class="expand-indicator">展开</div>
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="flex space-x-2">
                                    <span class="inline-block px-2 py-1 bg-green-50 text-green-600 rounded text-xs">
                                        会议
                                    </span>
                                </div>
                                <div class="flex space-x-3">
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-copy text-sm"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-ellipsis-v text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 昨天 -->
                    <div class="date-group mb-4">
                        <div class="text-xs text-gray-500 mb-2">昨天</div>
                        
                        <div class="history-item bg-white rounded-xl shadow-sm border border-gray-100 mb-3 p-3" onclick="showDetailView()">
                            <div class="flex justify-between items-start mb-2">
                                <div class="font-medium text-gray-800">朋友聊天</div>
                                <div class="text-xs text-gray-400">昨天 20:45</div>
                            </div>

                            <!-- 语音播放控件 -->
                            <div class="audio-player">
                                <div class="play-button">
                                    <i class="fas fa-play text-xs"></i>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress" style="width: 0%;"></div>
                                </div>
                                <div class="time-display">0:45</div>
                            </div>

                            <!-- 内容摘要 -->
                            <div class="content-summary text-gray-600 text-sm mb-2">
                                <div class="truncated-text">嘿，最近怎么样？想告诉你一个好消息，我终于搞定了那个项目！这周末有空出来...</div>
                                <div class="expand-indicator">展开</div>
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="flex space-x-2">
                                    <span class="inline-block px-2 py-1 bg-purple-50 text-purple-600 rounded text-xs">
                                        聊天
                                    </span>
                                </div>
                                <div class="flex space-x-3">
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-copy text-sm"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-ellipsis-v text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="history-item bg-white rounded-xl shadow-sm border border-gray-100 mb-3 p-3" onclick="showDetailView()">
                            <div class="flex justify-between items-start mb-2">
                                <div class="font-medium text-gray-800">领导汇报</div>
                                <div class="text-xs text-gray-400">昨天 14:20</div>
                            </div>

                            <!-- 语音播放控件 -->
                            <div class="audio-player">
                                <div class="play-button">
                                    <i class="fas fa-play text-xs"></i>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress" style="width: 10%;"></div>
                                </div>
                                <div class="time-display">3:05</div>
                            </div>

                            <!-- 内容摘要 -->
                            <div class="content-summary text-gray-600 text-sm mb-2">
                                <div class="truncated-text">关于本季度销售业绩的总结报告：第一，销售额同比增长15%，超出预期目标3个...</div>
                                <div class="expand-indicator">展开</div>
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="flex space-x-2">
                                    <span class="inline-block px-2 py-1 bg-blue-50 text-blue-600 rounded text-xs">
                                        工作
                                    </span>
                                </div>
                                <div class="flex space-x-3">
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-copy text-sm"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-ellipsis-v text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 更早 -->
                    <div class="date-group mb-4">
                        <div class="text-xs text-gray-500 mb-2">更早</div>
                        
                        <div class="history-item bg-white rounded-xl shadow-sm border border-gray-100 mb-3 p-3" onclick="showDetailView()">
                            <div class="flex justify-between items-start mb-2">
                                <div class="font-medium text-gray-800">恋人表达</div>
                                <div class="text-xs text-gray-400">6月15日 19:24</div>
                            </div>

                            <!-- 语音播放控件 -->
                            <div class="audio-player">
                                <div class="play-button">
                                    <i class="fas fa-play text-xs"></i>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress" style="width: 75%;"></div>
                                </div>
                                <div class="time-display">0:55</div>
                            </div>

                            <!-- 内容摘要 -->
                            <div class="content-summary text-gray-600 text-sm mb-2">
                                <div class="truncated-text">亲爱的，想你了。这几天出差的日子里，每天都会想起你的笑容。期待周末回家后...</div>
                                <div class="expand-indicator">展开</div>
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="flex space-x-2">
                                    <span class="inline-block px-2 py-1 bg-red-50 text-red-600 rounded text-xs">
                                        个人
                                    </span>
                                </div>
                                <div class="flex space-x-3">
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-copy text-sm"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-ellipsis-v text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新增：详情页视图 -->
            <div id="history-detail-view" class="detail-view p-4">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <button class="back-button text-gray-800 mr-3" onclick="showListView()">
                            <i class="fas fa-arrow-left"></i>
                        </button>
                        <h1 class="text-xl font-bold text-gray-800">邮件格式</h1>
                    </div>
                    <div class="text-xs text-gray-500">今天 14:30</div>
                </div>

                <!-- 语音播放控件 - 详情页版本 -->
                <div class="audio-player mb-4">
                    <div class="play-button">
                        <i class="fas fa-play text-xs"></i>
                    </div>
                    <div class="progress-bar">
                        <div class="progress" style="width: 30%;"></div>
                    </div>
                    <div class="time-display">1:25</div>
                </div>

                <!-- 双栏对比视图 -->
                <div class="detail-content">
                    <div class="split-view">
                        <!-- 左侧：原始语音转文字 -->
                        <div class="column column-left">
                            <div class="text-sm font-medium text-gray-700 mb-2">原始语音内容</div>
                            <div class="text-gray-700 leading-relaxed text-sm">
                                <p class="mb-2">
                                    <span class="timestamp-mark">0:05</span>
                                    今天和张总讨论了新项目的进展情况，他对我们的工作表示满意，但是有几点需要注意的地方。
                                </p>
                                <p class="mb-2">
                                    <span class="timestamp-mark">0:20</span>
                                    第一，项目进度需要加快，目前的进度比计划慢了大约一周时间。
                                </p>
                                <p class="mb-2">
                                    <span class="timestamp-mark">0:35</span>
                                    第二，用户反馈需要更加及时地收集和分析。
                                </p>
                                <p class="mb-2">
                                    <span class="timestamp-mark">0:45</span>
                                    第三，下周二之前需要提交一份详细的项目报告，包括目前的进展、遇到的问题以及解决方案。
                                </p>
                            </div>
                        </div>
                        
                        <!-- 右侧：AI优化后内容 -->
                        <div class="column column-right">
                            <div class="text-sm font-medium text-gray-700 mb-2">AI优化结果</div>
                            <div class="text-gray-700 leading-relaxed text-sm">
                                <p class="mb-2">
                                    尊敬的<span class="diff-highlight">张总</span>：
                                </p>
                                <p class="mb-2">
                                    <span class="diff-highlight">您好！</span>
                                </p>
                                <p class="mb-2">
                                    <span class="diff-highlight">感谢您今天抽出宝贵时间</span>讨论新项目的进展情况。<span class="diff-highlight">非常高兴</span>您对我们的工作表示满意<span class="diff-highlight">。</span>
                                </p>
                                <p class="mb-2">
                                    <span class="diff-highlight">根据我们的讨论，以下是</span>需要注意的<span class="diff-highlight">几点：</span>
                                </p>
                                <p class="mb-2">
                                    <span class="diff-highlight">1. 项目进度：</span>需要加快<span class="diff-highlight">当前</span>进度，目前<span class="diff-highlight">比</span>计划<span class="diff-highlight">滞后</span>约一周时间<span class="diff-highlight">；</span>
                                </p>
                                <p class="mb-2">
                                    <span class="diff-highlight">2. 用户反馈：</span>需更加及时<span class="diff-highlight">地</span>收集和分析<span class="diff-highlight">用户反馈数据；</span>
                                </p>
                                <p class="mb-2">
                                    <span class="diff-highlight">3. 项目报告：</span>需<span class="diff-highlight">在</span>下周二<span class="diff-highlight">前</span>提交<span class="diff-highlight">详细的</span>项目报告，包括目前<span class="diff-highlight">进展</span>、遇到的问题<span class="diff-highlight">及</span>解决方案<span class="diff-highlight">。</span>
                                </p>
                                <p class="mb-2">
                                    <span class="diff-highlight">我们团队将立即着手解决这些问题，确保项目顺利推进。</span>
                                </p>
                                <p class="mb-2">
                                    <span class="diff-highlight">如有任何疑问，随时欢迎您的指导。</span>
                                </p>
                                <p class="mb-2">
                                    <span class="diff-highlight">此致</span>
                                </p>
                                <p>
                                    <span class="diff-highlight">敬礼</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <div class="tab-icon"><i class="fas fa-microphone"></i></div>
                <div>输入</div>
            </div>
            <div class="tab-item active">
                <div class="tab-icon"><i class="fas fa-history"></i></div>
                <div>历史</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon"><i class="fas fa-sliders-h"></i></div>
                <div>设置</div>
            </div>
        </div>
    </div>

    <script>
        // 历史记录详情页视图切换
        function showDetailView() {
            document.getElementById('history-list-view').style.display = 'none';
            document.getElementById('history-detail-view').style.display = 'flex';
        }

        function showListView() {
            document.getElementById('history-list-view').style.display = 'block';
            document.getElementById('history-detail-view').style.display = 'none';
        }

        // 初始状态
        document.addEventListener('DOMContentLoaded', function() {
            // 设置语音播放按钮交互
            const playButtons = document.querySelectorAll('.play-button');
            playButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation(); // 防止点击播放按钮时触发项目点击

                    // 切换播放/暂停图标
                    const icon = this.querySelector('i');
                    if (icon.classList.contains('fa-play')) {
                        icon.classList.remove('fa-play');
                        icon.classList.add('fa-pause');
                    } else {
                        icon.classList.remove('fa-pause');
                        icon.classList.add('fa-play');
                    }
                });
            });

            // 处理"展开"交互
            const expandIndicators = document.querySelectorAll('.expand-indicator');
            expandIndicators.forEach(indicator => {
                indicator.addEventListener('click', function(e) {
                    e.stopPropagation(); // 防止点击展开时触发项目点击
                    
                    const parent = this.parentElement;
                    const truncatedText = parent.querySelector('.truncated-text');
                    
                    if (this.textContent === '展开') {
                        truncatedText.style.whiteSpace = 'normal';
                        this.textContent = '收起';
                        this.style.background = 'white';
                    } else {
                        truncatedText.style.whiteSpace = 'nowrap';
                        this.textContent = '展开';
                        this.style.background = 'linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 30%)';
                    }
                });
            });
            
            // 设置进度条交互
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                bar.addEventListener('click', function(e) {
                    e.stopPropagation(); // 防止点击进度条时触发项目点击
                    
                    const rect = this.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const percent = (x / rect.width) * 100;
                    
                    const progress = this.querySelector('.progress');
                    progress.style.width = percent + '%';
                    
                    // 更新时间显示（模拟）
                    const timeDisplay = this.nextElementSibling;
                    const totalSeconds = parseInt(timeDisplay.textContent.split(':')[0]) * 60 + 
                                       parseInt(timeDisplay.textContent.split(':')[1]);
                    const newSeconds = Math.floor((totalSeconds * percent) / 100);
                    const minutes = Math.floor(newSeconds / 60);
                    const seconds = newSeconds % 60;
                    timeDisplay.textContent = minutes + ':' + (seconds < 10 ? '0' + seconds : seconds);
                });
            });
        });
    </script>
</body>
</html> 