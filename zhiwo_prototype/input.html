<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知我AI输入法 - 语音输入</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .feature-tag {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 16px;
            font-size: 10px;
            font-weight: 500;
            margin: 0 2px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            letter-spacing: 0.2px;
            animation: fadeInUp 0.6s both;
            opacity: 0.85;
        }
        
        .feature-tag:hover {
            opacity: 1;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
        }
        
        .tag-primary {
            background: rgba(79, 70, 229, 0.1);
            color: #4f46e5;
            border: 1px solid rgba(79, 70, 229, 0.2);
            animation-delay: 0.1s;
        }
        
        .tag-accent {
            background: rgba(236, 72, 153, 0.1);
            color: #ec4899;
            border: 1px solid rgba(236, 72, 153, 0.2);
            animation-delay: 0.3s;
        }
        
        .tag-secondary {
            background: rgba(139, 92, 246, 0.1);
            color: #8b5cf6;
            border: 1px solid rgba(139, 92, 246, 0.2);
            animation-delay: 0.5s;
        }
        
        .feature-tags-container {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 10px;
            padding: 0 10px;
            max-width: 100%;
        }
        
        .feature-tag i {
            font-size: 9px;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 0.85;
                transform: translateY(0);
            }
        }
        
        /* 增强语音按钮的视觉效果 */
        .voice-btn {
            transform: scale(1.05);
            box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            z-index: 5;
        }
        
        .voice-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.4);
        }
        
        .voice-btn::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            opacity: 0.5;
            z-index: -1;
            animation: pulse-ring 2.5s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }
    </style>
</head>
<body class="bg-white">
    <div class="device-frame">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="time font-semibold">9:41</div>
            <div class="right-icons flex items-center">
                <i class="fas fa-signal mr-1"></i>
                <i class="fas fa-wifi mr-1"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="app-content">
            <!-- 初始状态：未开始输入 -->
            <div id="initial-state" class="p-6">
                <div class="flex items-center justify-between mb-5">
                    <h1 class="text-2xl font-bold text-gray-800">知我AI输入法</h1>
                    <div class="flex">
                        <button class="text-gray-500 mr-4">
                            <i class="fas fa-cog text-xl"></i>
                        </button>
                    </div>
                </div>
                
                <div class="bg-gray-50 rounded-xl p-5 h-96 flex flex-col">
                    <div class="empty-state text-center mt-16 mb-4">
                        <img src="https://images.unsplash.com/photo-1617791160505-6f00504e3519?w=200&h=200&fit=crop" alt="语音输入" class="w-32 h-32 mx-auto mb-6 rounded-full opacity-70">
                        <p class="text-gray-500 mb-3">点击下方按钮开始语音输入</p>
                        <p class="text-gray-400 text-sm mb-2">支持多段语音输入，AI将自动优化文本</p>
                        
                        <!-- 新增彩色标签展示核心功能 -->
                        <div class="feature-tags-container">
                            <span class="feature-tag tag-primary">
                                <i class="fas fa-magic mr-1"></i> 智能润色
                            </span>
                            <span class="feature-tag tag-accent">
                                <i class="fas fa-tachometer-alt mr-1"></i> 场景适配
                            </span>
                            <span class="feature-tag tag-secondary">
                                <i class="fas fa-star mr-1"></i> 个性化模板
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-center mt-12">
                    <button class="voice-btn">
                        <i class="fas fa-microphone"></i>
                    </button>
                </div>
            </div>

            <!-- 语音输入状态 -->
            <div id="recording-state" class="p-6 hidden">
                <div class="flex items-center justify-between mb-5">
                    <h1 class="text-2xl font-bold text-gray-800">正在录音...</h1>
                    <div>
                        <span class="text-red-500 animate-pulse mr-2">⚫</span>
                        <span class="text-gray-500 text-sm">00:15</span>
                    </div>
                </div>
                
                <div class="bg-gray-50 rounded-xl p-5 h-96 flex flex-col">
                    <div class="overflow-y-auto flex-grow">
                        <p class="text-gray-700 leading-relaxed">
                            今天和张总讨论了新项目的进展情况，他对我们的工作表示满意，但是有几点需要注意的地方。第一，项目进度需要加快，目前的进度比计划慢了大约一周时间。第二，用户反馈需要更加及时地收集和分析。第三，下周二之前需要提交一份详细的项目报告，包括目前的进展、遇到的问题以及解决方案。
                        </p>
                    </div>
                    
                    <div class="voice-wave flex justify-center items-center my-4 h-12">
                        <div class="w-1 h-3 bg-blue-400 mx-0.5 animate-pulse"></div>
                        <div class="w-1 h-5 bg-blue-400 mx-0.5 animate-pulse"></div>
                        <div class="w-1 h-8 bg-blue-400 mx-0.5 animate-pulse"></div>
                        <div class="w-1 h-6 bg-blue-400 mx-0.5 animate-pulse"></div>
                        <div class="w-1 h-4 bg-blue-400 mx-0.5 animate-pulse"></div>
                        <div class="w-1 h-7 bg-blue-400 mx-0.5 animate-pulse"></div>
                        <div class="w-1 h-4 bg-blue-400 mx-0.5 animate-pulse"></div>
                        <div class="w-1 h-6 bg-blue-400 mx-0.5 animate-pulse"></div>
                    </div>
                </div>
                
                <div class="flex justify-center mt-8 space-x-6">
                    <button class="btn-secondary flex items-center">
                        <i class="fas fa-pause mr-2"></i>
                        暂停
                    </button>
                    <button class="btn-primary flex items-center">
                        <i class="fas fa-check mr-2"></i>
                        完成
                    </button>
                </div>
            </div>

            <!-- 风格选择状态 -->
            <div id="template-selection" class="p-6 hidden">
                <div class="flex items-center justify-between mb-5">
                    <h1 class="text-2xl font-bold text-gray-800">选择风格</h1>
                    <div>
                        <button class="text-gray-500">
                            <i class="fas fa-plus text-xl"></i>
                        </button>
                    </div>
                </div>
                
                <div class="bg-gray-50 rounded-xl p-5 mb-4 max-h-64 overflow-y-auto">
                    <p class="text-gray-700 leading-relaxed">
                        今天和张总讨论了新项目的进展情况，他对我们的工作表示满意，但是有几点需要注意的地方。第一，项目进度需要加快，目前的进度比计划慢了大约一周时间。第二，用户反馈需要更加及时地收集和分析。第三，下周二之前需要提交一份详细的项目报告，包括目前的进展、遇到的问题以及解决方案。
                    </p>
                </div>
                
                <div class="templates-container">
                    <p class="text-sm text-gray-500 mb-2">选择风格模板：</p>
                    <div class="overflow-x-auto whitespace-nowrap pb-2 -mx-6 px-6">
                        <div class="inline-flex space-x-3">
                            <div class="template-card active w-32">
                                <div class="mb-1 font-medium text-gray-800">邮件格式</div>
                                <div class="text-xs text-gray-500">正式商务邮件格式</div>
                            </div>
                            <div class="template-card w-32">
                                <div class="mb-1 font-medium text-gray-800">Emoji风格</div>
                                <div class="text-xs text-gray-500">生动有趣多表情</div>
                            </div>
                            <div class="template-card w-32">
                                <div class="mb-1 font-medium text-gray-800">领导汇报</div>
                                <div class="text-xs text-gray-500">简明扼要有重点</div>
                            </div>
                            <div class="template-card w-32">
                                <div class="mb-1 font-medium text-gray-800">朋友聊天</div>
                                <div class="text-xs text-gray-500">轻松随意话家常</div>
                            </div>
                            <div class="template-card w-32">
                                <div class="mb-1 font-medium text-gray-800">恋人表达</div>
                                <div class="text-xs text-gray-500">深情浪漫有温度</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6">
                    <button class="w-full btn-primary py-3">
                        开始优化文本
                    </button>
                </div>
            </div>

            <!-- 结果展示状态 -->
            <div id="result-state" class="p-6 hidden">
                <div class="flex items-center justify-between mb-5">
                    <h1 class="text-2xl font-bold text-gray-800">优化结果</h1>
                    <div class="text-xs text-gray-500">
                        邮件格式
                    </div>
                </div>
                
                <div class="bg-white border border-gray-200 rounded-xl p-5 mb-4 max-h-96 overflow-y-auto shadow-sm">
                    <div class="text-gray-700 leading-relaxed">
                        <p class="mb-3">尊敬的张总：</p>
                        <p class="mb-3">您好！</p>
                        <p class="mb-3">感谢您今天抽出宝贵时间讨论新项目的进展情况。非常高兴您对我们的工作表示满意。</p>
                        <p class="mb-3">根据我们的讨论，以下是需要注意的几点：</p>
                        <p class="mb-3">1. 项目进度：需要加快当前进度，目前比计划滞后约一周时间；</p>
                        <p class="mb-3">2. 用户反馈：需更加及时地收集和分析用户反馈数据；</p>
                        <p class="mb-3">3. 项目报告：需在下周二前提交详细的项目报告，包括目前进展、遇到的问题及解决方案。</p>
                        <p class="mb-3">我们团队将立即着手解决这些问题，确保项目顺利推进。</p>
                        <p class="mb-3">如有任何疑问，随时欢迎您的指导。</p>
                        <p>此致</p>
                        <p>敬礼</p>
                    </div>
                </div>
                
                <div class="flex justify-between mt-6 space-x-4">
                    <button class="flex-1 btn-secondary py-3 flex justify-center items-center">
                        <i class="fas fa-copy mr-2"></i>
                        复制文本
                    </button>
                    <button class="flex-1 btn-primary py-3 flex justify-center items-center">
                        <i class="fas fa-share-alt mr-2"></i>
                        分享
                    </button>
                </div>
                
                <div class="mt-4">
                    <button class="w-full border border-gray-300 rounded-lg py-3 text-gray-700 bg-gray-50">
                        新的语音输入
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item active">
                <div class="tab-icon"><i class="fas fa-microphone"></i></div>
                <div>输入</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon"><i class="fas fa-history"></i></div>
                <div>历史</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon"><i class="fas fa-sliders-h"></i></div>
                <div>设置</div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html> 