// 基础交互功能实现

document.addEventListener('DOMContentLoaded', function() {
    // 获取各个界面状态元素
    const initialState = document.getElementById('initial-state');
    const recordingState = document.getElementById('recording-state');
    const templateSelection = document.getElementById('template-selection');
    const resultState = document.getElementById('result-state');

    // 如果在输入页面，绑定事件
    if (initialState && recordingState && templateSelection && resultState) {
        // 语音按钮点击事件
        const voiceBtn = initialState.querySelector('.voice-btn');
        if (voiceBtn) {
            voiceBtn.addEventListener('click', function() {
                initialState.classList.add('hidden');
                recordingState.classList.remove('hidden');
            });
        }

        // 完成按钮点击事件
        const completeBtn = recordingState.querySelector('.btn-primary');
        if (completeBtn) {
            completeBtn.addEventListener('click', function() {
                recordingState.classList.add('hidden');
                templateSelection.classList.remove('hidden');
            });
        }

        // 暂停按钮点击事件
        const pauseBtn = recordingState.querySelector('.btn-secondary');
        if (pauseBtn) {
            pauseBtn.addEventListener('click', function() {
                // 切换暂停/继续状态
                if (this.querySelector('i').classList.contains('fa-pause')) {
                    this.querySelector('i').classList.remove('fa-pause');
                    this.querySelector('i').classList.add('fa-play');
                    this.textContent = ' 继续';
                    this.prepend(document.createElement('i'));
                    this.querySelector('i').classList.add('fas', 'fa-play', 'mr-2');
                } else {
                    this.querySelector('i').classList.remove('fa-play');
                    this.querySelector('i').classList.add('fa-pause');
                    this.textContent = ' 暂停';
                    this.prepend(document.createElement('i'));
                    this.querySelector('i').classList.add('fas', 'fa-pause', 'mr-2');
                }
            });
        }

        // 模板选择事件
        const templateCards = templateSelection.querySelectorAll('.template-card');
        if (templateCards.length > 0) {
            templateCards.forEach(card => {
                card.addEventListener('click', function() {
                    // 移除其他卡片的选中状态
                    templateCards.forEach(c => c.classList.remove('active'));
                    // 添加当前卡片的选中状态
                    this.classList.add('active');
                });
            });
        }

        // 开始优化文本按钮点击事件
        const optimizeBtn = templateSelection.querySelector('.btn-primary');
        if (optimizeBtn) {
            optimizeBtn.addEventListener('click', function() {
                templateSelection.classList.add('hidden');
                resultState.classList.remove('hidden');
                
                // 更新结果页面中显示的模板名称
                const selectedTemplate = templateSelection.querySelector('.template-card.active');
                if (selectedTemplate) {
                    const templateName = selectedTemplate.querySelector('.font-medium').textContent;
                    const resultTemplateName = resultState.querySelector('.text-xs.text-gray-500');
                    if (resultTemplateName) {
                        resultTemplateName.textContent = templateName;
                    }
                }
            });
        }

        // 新的语音输入按钮点击事件
        const newInputBtn = resultState.querySelector('button:last-child');
        if (newInputBtn) {
            newInputBtn.addEventListener('click', function() {
                resultState.classList.add('hidden');
                initialState.classList.remove('hidden');
            });
        }
    }

    // 导航栏切换效果
    const tabItems = document.querySelectorAll('.tab-item');
    if (tabItems.length > 0) {
        tabItems.forEach(item => {
            item.addEventListener('click', function() {
                // 在实际应用中，这里会跳转到相应页面
                tabItems.forEach(tab => tab.classList.remove('active'));
                this.classList.add('active');
            });
        });
    }
}); 