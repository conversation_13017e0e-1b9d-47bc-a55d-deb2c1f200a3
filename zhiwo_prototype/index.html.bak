<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知我AI输入法 - 原型展示</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        body {
            background-color: #f0f2f5;
        }
        .prototype-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            padding: 40px 20px;
        }
        .prototype-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .title-header {
            background-color: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="title-header">
        <h1 class="text-3xl font-bold">知我AI输入法 - 高保真原型</h1>
        <p class="mt-2 text-blue-100">精美UI界面设计，支持多场景语音文字优化</p>
    </div>

    <div class="prototype-container">
        <!-- 语音输入界面 -->
        <div class="prototype-item">
            <h2 class="text-xl font-semibold text-gray-800 mb-3">语音输入界面</h2>
            <iframe src="input.html" width="375" height="812" frameborder="0" class="shadow-xl rounded-3xl"></iframe>
        </div>

        <!-- 历史记录界面 -->
        <div class="prototype-item">
            <h2 class="text-xl font-semibold text-gray-800 mb-3">历史记录界面</h2>
            <iframe src="history.html" width="375" height="812" frameborder="0" class="shadow-xl rounded-3xl"></iframe>
        </div>

        <!-- 设置界面 -->
        <div class="prototype-item">
            <h2 class="text-xl font-semibold text-gray-800 mb-3">设置界面</h2>
            <iframe src="settings.html" width="375" height="812" frameborder="0" class="shadow-xl rounded-3xl"></iframe>
        </div>

        <!-- 模板管理界面 -->
        <div class="prototype-item">
            <h2 class="text-xl font-semibold text-gray-800 mb-3">模板管理界面</h2>
            <iframe src="templates.html" width="375" height="812" frameborder="0" class="shadow-xl rounded-3xl"></iframe>
        </div>

        <!-- 新建模板界面 -->
        <div class="prototype-item">
            <h2 class="text-xl font-semibold text-gray-800 mb-3">新建模板界面</h2>
            <iframe src="new-template.html" width="375" height="812" frameborder="0" class="shadow-xl rounded-3xl"></iframe>
        </div>
    </div>

    <footer class="bg-white py-6 text-center border-t">
        <p class="text-gray-600">知我AI输入法 UI原型 | 设计于 2023</p>
    </footer>
</body>
</html> 