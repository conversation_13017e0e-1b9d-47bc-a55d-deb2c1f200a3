:root {
  --primary-color: #4f46e5;
  --secondary-color: #8b5cf6;
  --accent-color: #ec4899;
  --gradient-start: #4f46e5;
  --gradient-end: #ec4899;
  --background-color: #f8fafc;
  --text-color: #1e293b;
  --border-color: #e2e8f0;
  --light-gray: #f1f5f9;
  --dark-gray: #64748b;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  margin: 0;
  padding: 0;
}

.device-frame {
  width: 375px;
  height: 812px;
  background-color: white;
  border-radius: 40px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 10px 30px rgba(79, 70, 229, 0.2);
  margin: 20px;
  border: 10px solid #121212;
}

.status-bar {
  height: 44px;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: relative;
  z-index: 10;
}

.tab-bar {
  height: 84px;
  background-color: #ffffff;
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-around;
  position: absolute;
  bottom: 0;
  width: 100%;
  padding-bottom: 20px;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--dark-gray);
  font-size: 10px;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: var(--primary-color);
}

.tab-icon {
  margin-bottom: 4px;
  font-size: 24px;
  transition: transform 0.2s ease;
}

.tab-item:hover .tab-icon {
  transform: scale(1.1);
}

.app-content {
  height: calc(100% - 128px); /* 扣除状态栏和标签栏的高度 */
  overflow-y: auto;
  position: relative;
}

.btn-primary {
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  color: white;
  border: none;
  border-radius: 50px;
  padding: 12px 24px;
  font-weight: 500;
  box-shadow: 0 4px 10px rgba(79, 70, 229, 0.3);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  box-shadow: 0 6px 15px rgba(79, 70, 229, 0.4);
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: white;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 50px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background-color: rgba(79, 70, 229, 0.05);
}

.voice-btn {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);
  border: none;
  position: relative;
  transition: all 0.3s ease;
}

.voice-btn::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  opacity: 0.6;
  z-index: -1;
  animation: pulse-ring 2s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 0;
  }
  100% {
    transform: scale(0.95);
    opacity: 0;
  }
}

.voice-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.5);
}

.voice-btn i {
  color: white;
  font-size: 28px;
}

.voice-recording .voice-btn {
  background: linear-gradient(135deg, #ef4444, #ec4899);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 10px rgba(239, 68, 68, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 20px rgba(239, 68, 68, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 10px rgba(239, 68, 68, 0.3);
  }
}

.template-card {
  background-color: white;
  border-radius: 16px;
  padding: 12px;
  margin: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.15);
}

.template-card.active {
  border: 2px solid var(--primary-color);
  box-shadow: 0 4px 10px rgba(79, 70, 229, 0.2);
}

.history-item {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.history-item:hover {
  background-color: rgba(79, 70, 229, 0.03);
}

.demo-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.frame-title {
  margin-top: -10px;
  margin-bottom: 20px;
  text-align: center;
  font-weight: 600;
  color: #475569;
}

/* 新增样式：模拟模态框 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-backdrop.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 20px;
  width: 90%;
  max-width: 420px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.modal-backdrop.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-body {
  padding: 20px;
}

.comparison-toggle {
  display: flex;
  align-items: center;
  margin: 16px 0;
  padding: 6px 12px;
  background-color: var(--light-gray);
  border-radius: 99px;
  width: fit-content;
}

.toggle-option {
  padding: 8px 16px;
  border-radius: 99px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-option.active {
  background-color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.toggle-divider {
  width: 1px;
  height: 20px;
  background-color: var(--border-color);
  margin: 0 4px;
}

.preview-container {
  background-color: var(--light-gray);
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
  max-height: 300px;
  overflow-y: auto;
}

.preview-content {
  font-size: 14px;
  line-height: 1.6;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 设置项目卡片添加动画 */
.setting-group > div {
  transition: all 0.2s ease;
}

.setting-group > div:hover {
  background-color: rgba(79, 70, 229, 0.03);
}

/* 语音波形更美观 */
.voice-wave div {
  border-radius: 2px;
  transition: height 0.4s ease;
}

/* 输入框样式美化 */
input:focus, textarea:focus {
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
} 