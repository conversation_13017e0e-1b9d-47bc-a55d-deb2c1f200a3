# 知我AI输入法应用需求说明

## 产品概述
"知我AI输入法"是一款利用AI技术帮助用户优化文字输入的跨平台应用。用户可以通过语音输入文字，应用会调用AI接口对文字进行整理润色，使其符合用户选择的风格模板。无需注册登录，轻量级使用，同时提供高级VIP功能满足更高需求的用户。

## 功能需求

### 1. 语音输入功能
- 用户可通过点击屏幕上的语音按钮输入语音
  - 语音按钮设计为圆形，采用渐变色突出显示
  - 按钮周围有轻微的阴影和悬浮效果，提升视觉层次感
- 支持多段语音输入
  - 输入过程中语音按钮变为暂停按钮，图标从麦克风变为暂停图标
  - 完成按钮位于屏幕底部，为圆角矩形设计，确保可及性
- 输入过程中提供声波动效反馈用户输入状态
- 实时显示语音转文字的内容，转写文本区域采用灰色背景与其他区域区分
- 调用设备原生接口进行语音识别（iOS和Android各自调用本地能力）

### 2. AI文本优化功能
- 将用户输入的文字发送给AI进行优化整理润色
  - 优化进行中显示动态加载指示器，使用紫色系渐变
- 基础版本使用Qwen/Qwen2.5-7B-Instruct模型
  - VIP用户可选择高级模型，提供更精准的优化结果
- 调用接口地址：https://docs.siliconflow.cn/cn/api-reference/chat-completions/chat-completions
- 优化后的文字显示在主界面，与原始输入形成对比
  - 优化文本区域采用白色背景，字体更大，便于阅读
- 优化后的文字应符合用户预期（无多余字符，格式规范，符合个性化要求）

### 3. 风格模板功能
- 提供多种预设风格模板供用户选择，以彩色标签形式展示
  - 标签设计为圆角矩形，浅色背景+彩色边框
  - 图标+文字形式，提高辨识度
  - 模板列表采用水平滚动方式展示
- 预设模板包括：
  - 模板1：发送邮件的模板（蓝色系）
  - 模板2：整理成富含emoji的格式模板（绿色系）
  - 模板3：发送给领导的模板（紫色系）
  - 模板4：发送给朋友的模板（橙色系）
  - 模板5：发送给恋人的模板（粉色系）
- 用户可自定义并添加新模板
  - 免费用户限制1个自定义模板
  - VIP用户无限制添加模板
- 每个模板包含名称和用于AI指导的提示词
  - 模板编辑界面使用表单形式，包含名称和提示词两个输入框
  - 提示词输入框支持多行输入
- 用户选择模板后，系统将提示词与用户输入一起发送给AI

### 4. 文本操作功能
- 显示AI优化后的文字内容，使用清晰的字体和适当的行间距
- 支持复制全部或部分文字
  - 复制按钮位于文本区域右下角，使用浅色图标
  - 点击后显示"已复制"提示，1秒后自动消失
- 支持以纯文本形式分享到第三方应用
  - 分享按钮位于复制按钮旁边，使用标准分享图标
  - 调用系统分享菜单

### 5. 历史记录管理
- 支持上滑输入新内容，历史记录以日期分组显示
- 历史记录列表展示内容：
  - 标题（模板名称）
  - 时间戳（精确到分钟：今天 14:30，昨天 20:45，或具体日期）
  - 内容摘要（限制60字符，超出显示"..."并提供展开功能）
  - 标签（分类信息）
  - 语音播放按钮（带有进度条控制功能）
- 历史详情页设计：
  - 双栏对比视图：左侧为原始语音转文字内容（带时间轴标记），右侧为AI优化后内容（差异高亮显示）
  - 顶部显示返回按钮、标题和时间信息
  - 支持语音播放和进度控制
- 支持删除历史记录中的对话
  - 左滑操作显示删除按钮，或通过更多菜单操作
  - 删除前提供确认对话框

### 6. VIP会员功能
- VIP特权内容：
  - 无限自定义风格模板（无数量限制）
  - 高级AI模型选择（支持选择多种大模型）
  - 优先处理请求（减少等待时间）
  - 模板云端同步（云端同步自定义模板，换个设备随时使用你喜欢的风格模板）
- VIP会员价格：¥18/月
- 会员状态展示：
  - 非会员状态：显示升级卡片，突出VIP权益
  - 会员状态：显示会员到期时间和详情查看按钮

## 界面设计需求

### 1. 总体设计风格
- 整体色调：以紫色系为主色调（#6a5ae1, #8364e2），搭配白色和浅灰色
- 字体：系统默认字体，标题使用粗体，正文使用常规字重
- 圆角设计：统一使用16px圆角，按钮使用更大的圆角（24px）
- 卡片样式：白底带轻微阴影，圆角边框
- 动效：按钮点击有轻微缩放效果，切换有平滑过渡动画

### 2. 主界面（输入页面）
- **顶部区域**：
  - 状态栏（时间和系统图标）
  - 应用标题"知我AI输入法"
- **文字展示区**：
  - 灰色背景区域显示实时识别的文字
  - 白色背景区域显示AI优化后的文字
  - 文字大小设置合适的对比度
- **功能特性提示**：
  - 位于灰色背景区域下方
  - 以小标签形式展示"智能润色"、"场景适配"等功能
  - 标签采用浅色背景，轻量化设计
- **操作区**：
  - 未开始输入时：显示语音按钮（圆形，紫色渐变，带悬浮效果）
  - 输入语音时：显示暂停按钮、带动画的声波状态指示、完成按钮
  - 完成输入后：显示风格模板选项（水平滚动列表）、复制和分享按钮
  - 底部固定的标签栏：输入、历史、设置三个主要功能入口

### 3. 历史记录页面
- **列表页视图**：
  - 顶部标题区域："历史记录"文字和搜索图标
  - 标签筛选区：全部、工作相关、邮件等类别标签，水平滚动
  - 按日期分组：今天、昨天、更早
  - 列表项设计：
    - 左侧显示标题和时间戳
    - 中间为语音播放控件（播放按钮+进度条+时间显示）
    - 下方为内容摘要（60字符限制，带展开功能）
    - 底部为标签和操作按钮（复制、更多）
- **详情页视图**：
  - 顶部返回按钮和标题、时间信息
  - 语音播放控件（与列表项相同设计）
  - 双栏对比视图：
    - 左侧：原始语音转文字内容，浅灰背景
    - 右侧：AI优化后内容，白色背景
  - 原始内容带时间轴标记（时间点+内容）
  - 优化内容带差异高亮（绿色背景突出改动部分）

### 4. 设置页面
- **顶部区域**：页面标题"设置"
- **VIP卡片**：
  - 免费用户：显示升级卡片（紫色渐变背景）
    - VIP标志和介绍文字
    - 特权标签展示（风格模板、高级模型等）
    - "立即升级"按钮（白色，突出显示）
  - VIP用户：显示会员卡片（紫蓝渐变背景）
    - 已开通VIP标志
    - 会员到期时间
    - "查看详情"按钮（半透明设计）
- **设置选项**：
  - 分组展示，白色卡片设计
  - 每个选项包含图标、文字和右侧箭头
  - VIP专属功能（如AI模型设置）带锁定标识
- **功能分组**：
  - 基础设置：风格模板、界面风格、语言设置
  - 高级设置：AI模型设置（VIP功能）、给我好评
  - 信息与隐私：用户协议、隐私条款、关于我们
- **底部操作**：
  - "删除所有数据"按钮（红色文字，白色背景）

### 5. VIP详情弹窗
- 模态弹窗设计，半透明黑色背景
- 白色卡片内容，圆角边框
- 顶部：标题"VIP特权详情"和关闭按钮
- 会员状态展示：VIP标志和到期时间
- 特权详情列表：
  - 每项特权包含图标、标题和详细说明
  - 整齐的布局和适当的间距
  - 清晰的视觉层次

## 交互设计细节

### 1. 语音输入交互
- 点击语音按钮开始录音，按钮有轻微的缩放动效
- 录音状态显示动态声波效果，响应用户声音强度
- 暂停/继续按钮允许用户控制录音过程
- 完成录音后，自动发送至AI进行处理

### 2. 模板选择交互
- 模板以水平滚动列表展示，选中状态有明确的视觉反馈
- 点击模板立即应用，无需二次确认
- 选中的模板标签突出显示（颜色更深、轻微放大）

### 3. 历史记录交互
- 列表项点击进入详情页面，带有平滑过渡动画
- 语音播放控件的进度条支持点击任意位置调整进度
- 内容摘要的"展开/收起"按钮控制文本展示长度
- 左滑列表项显示快捷操作按钮（如删除）

### 4. VIP功能交互
- 点击VIP卡片或"立即升级"按钮进入购买流程
- VIP功能锁定提示，点击后显示升级提醒
- VIP详情弹窗支持点击外部区域关闭

## 技术规格

### 开发技术栈
- React Native框架
- Expo开发环境
- TailwindCSS样式设计
- 状态管理：使用React Context API或Redux

### 文件结构建议
- `/components`：UI组件
  - `/common`：通用组件（按钮、卡片等）
  - `/input`：输入页面相关组件
  - `/history`：历史记录相关组件
  - `/settings`：设置页面相关组件
- `/screens`：主要页面
- `/services`：API调用和业务逻辑
- `/utils`：工具函数
- `/assets`：图片和静态资源

### API调用
- 语音识别：使用设备原生接口
  - iOS：Speech Framework
  - Android：Speech Recognizer API
- AI文本处理：
  - 基础版：使用Qwen/Qwen2.5-7B-Instruct模型
  - VIP版：提供多种模型选择
- 接口地址：https://docs.siliconflow.cn/cn/api-reference/chat-completions/chat-completions
- API调用格式：
  ```json
  {
    "model": "Qwen/Qwen2.5-7B-Instruct",
    "messages": [
      {
        "role": "system",
        "content": "你是一个文字润色专家，请按照以下风格对用户输入的文字进行优化：{模板提示词}"
      },
      {
        "role": "user",
        "content": "{用户输入的文字}"
      }
    ]
  }
  ```

### 数据存储
- 使用AsyncStorage存储本地数据：
  - 历史记录（包含原文、优化后文本、使用的模板、时间戳）
  - 自定义模板配置
  - 用户偏好设置
  - VIP会员状态（本地存储，实际应用中可能需要服务端验证）

## 用户体验要求
- 界面设计简洁直观，重点突出核心功能
- 操作流程顺畅，减少不必要的点击和确认
- 响应及时，提供明确的状态反馈
  - AI处理时的加载状态明确展示
  - 操作成功后给予简短明确的提示
- 提供适当的加载指示器和错误处理
  - 网络错误时提供重试选项
  - 语音识别失败时提供友好提示
- 支持浅色主题，界面清晰易读
- 适配不同屏幕尺寸的设备，确保在各种手机上显示正常
- 性能优化，确保应用启动速度快，响应流畅

## 测试要点
- 语音识别在不同环境下的准确率测试（安静/嘈杂环境）
- AI模型生成结果的质量评估
- 不同长度文本的处理效果
- VIP功能解锁/锁定状态的正确显示
- 历史记录的保存和加载性能
- 界面在不同设备上的适配性
