<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知我AI输入法 - 新建模板</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-white">
    <div class="device-frame">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="time font-semibold">9:41</div>
            <div class="right-icons flex items-center">
                <i class="fas fa-signal mr-1"></i>
                <i class="fas fa-wifi mr-1"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="app-content">
            <!-- 新建模板页面 -->
            <div class="p-4">
                <div class="flex items-center justify-between mb-5">
                    <div class="flex items-center">
                        <button class="text-gray-500 mr-2">
                            <i class="fas fa-chevron-left text-lg"></i>
                        </button>
                        <h1 class="text-xl font-bold text-gray-800">新建模板</h1>
                    </div>
                    <button class="text-blue-500 font-medium">
                        保存
                    </button>
                </div>

                <!-- 模板表单 -->
                <div class="form-container">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">模板名称</label>
                        <input 
                            type="text" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                            placeholder="例如：销售报告、会议纪要..."
                        >
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">模板描述</label>
                        <input 
                            type="text" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                            placeholder="简要描述模板的用途..."
                        >
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">指导提示词</label>
                        <textarea 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-40" 
                            placeholder="请输入AI处理文本时使用的详细指导提示词，越具体越好..."
                        ></textarea>
                        <div class="text-xs text-gray-500 mt-1">
                            提示词越具体，AI优化效果越好。
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">模板分类</label>
                        <div class="flex flex-wrap gap-2">
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700 flex items-center">
                                工作
                                <i class="fas fa-check-circle ml-1 text-blue-500"></i>
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700">
                                个人
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700">
                                邮件
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700">
                                会议
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700">
                                社交
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700">
                                正式
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700">
                                休闲
                            </div>
                            <div class="tag-item border border-gray-300 rounded-full px-3 py-1 text-sm text-gray-700 bg-gray-100">
                                <i class="fas fa-plus mr-1 text-xs"></i>
                                自定义
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">测试文本</label>
                        <textarea 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-20" 
                            placeholder="输入样例文本来测试你的模板..."
                        ></textarea>
                    </div>
                    
                    <div class="mt-6">
                        <button class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 rounded-lg flex items-center justify-center">
                            <i class="fas fa-bolt mr-2"></i>
                            测试模板效果
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <div class="tab-icon"><i class="fas fa-microphone"></i></div>
                <div>输入</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon"><i class="fas fa-history"></i></div>
                <div>历史</div>
            </div>
            <div class="tab-item active">
                <div class="tab-icon"><i class="fas fa-sliders-h"></i></div>
                <div>设置</div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html> 