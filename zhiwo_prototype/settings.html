<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知我AI输入法 - 设置</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        /* VIP卡片样式 */
        .vip-card {
            background: linear-gradient(135deg, #8364e2 0%, #6a5ae1 100%);
            border-radius: 16px;
            padding: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 16px rgba(106, 90, 225, 0.2);
        }
        
        .vip-card::before {
            content: "";
            position: absolute;
            top: -20px;
            right: -20px;
            width: 140px;
            height: 140px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
        }
        
        /* VIP已开通卡片样式 */
        .vip-active-card {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            border-radius: 16px;
            padding: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 16px rgba(79, 70, 229, 0.2);
        }
        
        .vip-active-card::before {
            content: "";
            position: absolute;
            top: -20px;
            right: -20px;
            width: 140px;
            height: 140px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .vip-badge {
            background: linear-gradient(135deg, #ffb655 0%, #ff8c37 100%);
            color: white;
            font-size: 12px;
            font-weight: bold;
            padding: 3px 10px;
            border-radius: 12px;
            display: inline-block;
            margin-bottom: 8px;
        }
        
        .vip-active-badge {
            background: linear-gradient(135deg, #fde047 0%, #ffc107 100%);
            color: #713f12;
            font-size: 12px;
            font-weight: bold;
            padding: 3px 10px;
            border-radius: 12px;
            display: inline-block;
            margin-bottom: 8px;
        }
        
        .privilege-tag {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 4px 10px;
            margin-right: 8px;
            margin-bottom: 8px;
            font-size: 12px;
            white-space: nowrap;
        }
        
        .upgrade-button {
            background: white;
            color: #6a5ae1;
            border-radius: 24px;
            padding: 8px 16px;
            font-weight: bold;
            font-size: 14px;
            border: none;
            transition: all 0.2s;
        }
        
        .upgrade-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .detail-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 24px;
            padding: 8px 16px;
            font-weight: bold;
            font-size: 14px;
            border: none;
            transition: all 0.2s;
        }
        
        .detail-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        /* 锁定功能样式 */
        .locked-feature {
            position: relative;
        }
        
        .lock-icon {
            background: rgba(106, 90, 225, 0.1);
            color: #6a5ae1;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            margin-left: 8px;
        }
        
        /* VIP详情弹窗 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 100;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 360px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }
        
        .modal-header {
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            background: white;
            z-index: 1;
            border-radius: 16px 16px 0 0;
        }
        
        .close-button {
            position: absolute;
            right: 16px;
            top: 16px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .close-button:hover {
            background: #e5e7eb;
        }
        
        .vip-feature-row {
            display: flex;
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .vip-feature-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            flex-shrink: 0;
        }
        
        .membership-status {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
        }
        
        /* 用户状态切换控件 */
        .switch-container {
            position: fixed;
            bottom: 16px;
            left: 16px;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            padding: 8px;
            z-index: 50;
            display: flex;
            align-items: center;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 36px;
            height: 20px;
            margin-left: 8px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 20px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #6366f1;
        }
        
        input:checked + .slider:before {
            transform: translateX(16px);
        }
    </style>
</head>
<body class="bg-white">
    <div class="device-frame">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="time font-semibold">9:41</div>
            <div class="right-icons flex items-center">
                <i class="fas fa-signal mr-1"></i>
                <i class="fas fa-wifi mr-1"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="app-content">
            <!-- 设置页面 -->
            <div class="p-4">
                <div class="flex items-center justify-between mb-5">
                    <h1 class="text-2xl font-bold text-gray-800">设置</h1>
                </div>

                <!-- 免费用户卡片 -->
                <div id="free-user-card" class="vip-card mb-5">
                    <div class="vip-badge">
                        <i class="fas fa-crown mr-1"></i> 知我VIP
                    </div>
                    <div class="text-white font-bold text-lg mb-2">解锁全部高级功能</div>
                    <div class="text-white opacity-80 text-sm mb-4">享受无限制的AI优化和高级模型</div>
                    
                    <div class="flex flex-wrap mb-4">
                        <div class="privilege-tag">
                            <i class="fas fa-layer-group mr-1"></i> 无限自定义风格模板
                        </div>
                        <div class="privilege-tag">
                            <i class="fas fa-robot mr-1"></i> 高级AI模型
                        </div>
                        <div class="privilege-tag">
                            <i class="fas fa-bolt mr-1"></i> 优先处理请求
                        </div>
                        <div class="privilege-tag">
                            <i class="fas fa-cloud mr-1"></i> 模板云端同步
                        </div>
                    </div>
                    
                    <button class="upgrade-button">
                        立即升级 <span class="ml-1">¥18/月</span>
                    </button>
                </div>

                <!-- VIP用户卡片 (默认隐藏) -->
                <div id="vip-user-card" class="vip-active-card mb-5" style="display: none;">
                    <div class="vip-active-badge">
                        <i class="fas fa-crown mr-1"></i> 已开通VIP
                    </div>
                    <div class="text-white font-bold text-lg mb-2">尊贵会员专享特权</div>
                    <div class="text-white opacity-80 text-sm mb-4">有效期至 2024年8月30日</div>
                    
                    <button id="view-detail-btn" class="detail-button">
                        查看详情 <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </button>
                </div>

                <!-- 设置选项 -->
                <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-5">
                    <div class="setting-group">
                        <div class="p-4 flex justify-between items-center border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-9 h-9 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-shapes text-purple-500"></i>
                                </div>
                                <div>风格模板</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        
                        <div class="p-4 flex justify-between items-center border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-9 h-9 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-palette text-green-500"></i>
                                </div>
                                <div>界面风格</div>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm text-gray-500 mr-2">浅色</span>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                        
                        <div class="p-4 flex justify-between items-center">
                            <div class="flex items-center">
                                <div class="w-9 h-9 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-language text-blue-500"></i>
                                </div>
                                <div>语言设置</div>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm text-gray-500 mr-2">简体中文</span>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-5">
                    <div class="setting-group">
                        <div id="ai-model-setting" class="p-4 flex justify-between items-center border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-9 h-9 rounded-full bg-orange-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-robot text-orange-500"></i>
                                </div>
                                <div class="flex items-center">
                                    <div>AI模型设置</div>
                                    <div id="ai-model-lock" class="lock-icon">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div id="ai-model-status" class="flex items-center">
                                <span class="text-sm text-gray-400 mr-2">仅限VIP</span>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                        
                        <div class="p-4 flex justify-between items-center">
                            <div class="flex items-center">
                                <div class="w-9 h-9 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-star text-yellow-500"></i>
                                </div>
                                <div>给我好评</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-5">
                    <div class="setting-group">
                        <div class="p-4 flex justify-between items-center border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-9 h-9 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-file-contract text-gray-500"></i>
                                </div>
                                <div>用户协议</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        
                        <div class="p-4 flex justify-between items-center border-b border-gray-100">
                            <div class="flex items-center">
                                <div class="w-9 h-9 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-shield-alt text-gray-500"></i>
                                </div>
                                <div>隐私条款</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        
                        <div class="p-4 flex justify-between items-center">
                            <div class="flex items-center">
                                <div class="w-9 h-9 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-info-circle text-gray-500"></i>
                                </div>
                                <div>关于我们</div>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm text-gray-500 mr-2">v1.0.0</span>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 删除所有数据按钮 -->
                <div class="mt-4">
                    <button class="w-full border border-gray-300 rounded-lg py-3 text-red-500 bg-white">
                        删除所有数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <div class="tab-icon"><i class="fas fa-microphone"></i></div>
                <div>输入</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon"><i class="fas fa-history"></i></div>
                <div>历史</div>
            </div>
            <div class="tab-item active">
                <div class="tab-icon"><i class="fas fa-sliders-h"></i></div>
                <div>设置</div>
            </div>
        </div>
    </div>

    <!-- VIP详情弹窗 -->
    <div id="vip-detail-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header p-4">
                <h2 class="text-xl font-bold text-gray-800">VIP特权详情</h2>
                <div id="close-modal-btn" class="close-button">
                    <i class="fas fa-times"></i>
                </div>
            </div>
            <div class="p-4">
                <div class="membership-status">
                    <div class="flex items-center mb-1">
                        <i class="fas fa-crown text-yellow-500 mr-2"></i>
                        <span class="font-medium">VIP会员</span>
                    </div>
                    <div class="text-sm text-gray-600">
                        有效期至 2024年8月30日
                    </div>
                </div>
                
                <div class="vip-feature-row">
                    <div class="vip-feature-icon bg-indigo-100">
                        <i class="fas fa-layer-group text-indigo-600"></i>
                    </div>
                    <div>
                        <div class="font-medium mb-1">无限自定义风格模板</div>
                        <div class="text-sm text-gray-600">
                            创建并使用无限数量的个性化风格模板，满足多种场景需求
                        </div>
                    </div>
                </div>
                
                <div class="vip-feature-row">
                    <div class="vip-feature-icon bg-blue-100">
                        <i class="fas fa-robot text-blue-600"></i>
                    </div>
                    <div>
                        <div class="font-medium mb-1">高级AI模型</div>
                        <div class="text-sm text-gray-600">
                            使用最先进的AI模型进行文本优化，生成更精准、更自然的内容
                        </div>
                    </div>
                </div>
                
                <div class="vip-feature-row">
                    <div class="vip-feature-icon bg-amber-100">
                        <i class="fas fa-bolt text-amber-600"></i>
                    </div>
                    <div>
                        <div class="font-medium mb-1">优先处理请求</div>
                        <div class="text-sm text-gray-600">
                            您的所有请求都将优先处理，大幅减少等待时间
                        </div>
                    </div>
                </div>
                
                <div class="vip-feature-row">
                    <div class="vip-feature-icon bg-green-100">
                        <i class="fas fa-cloud text-green-600"></i>
                    </div>
                    <div>
                        <div class="font-medium mb-1">模板云端同步</div>
                        <div class="text-sm text-gray-600">
                            云端同步自定义模板，换个设备随时使用你喜欢的风格模板
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户状态切换器（用于演示，实际产品可移除） -->
    <div class="switch-container">
        <span class="text-sm text-gray-700">VIP模式</span>
        <label class="switch">
            <input type="checkbox" id="user-type-toggle">
            <span class="slider"></span>
        </label>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 元素获取
            const freeUserCard = document.getElementById('free-user-card');
            const vipUserCard = document.getElementById('vip-user-card');
            const userTypeToggle = document.getElementById('user-type-toggle');
            const viewDetailBtn = document.getElementById('view-detail-btn');
            const vipDetailModal = document.getElementById('vip-detail-modal');
            const closeModalBtn = document.getElementById('close-modal-btn');
            const aiModelSetting = document.getElementById('ai-model-setting');
            const aiModelLock = document.getElementById('ai-model-lock');
            const aiModelStatus = document.getElementById('ai-model-status');
            
            // 用户类型切换（免费/VIP）
            userTypeToggle.addEventListener('change', function() {
                if (this.checked) {
                    // 切换为VIP用户
                    freeUserCard.style.display = 'none';
                    vipUserCard.style.display = 'block';
                    
                    // 解锁AI模型选项
                    aiModelLock.style.display = 'none';
                    aiModelStatus.innerHTML = '<span class="text-sm text-gray-500 mr-2">DeepSeek-R1</span><i class="fas fa-chevron-right text-gray-400"></i>';
                    
                    // 更新AI模型设置点击事件
                    aiModelSetting.onclick = function() {
                        alert('您可以选择不同的AI模型：DeepSeek-R1, GPT-4或者其他高级模型');
                    };
                } else {
                    // 切换为免费用户
                    freeUserCard.style.display = 'block';
                    vipUserCard.style.display = 'none';
                    
                    // 锁定AI模型选项
                    aiModelLock.style.display = 'flex';
                    aiModelStatus.innerHTML = '<span class="text-sm text-gray-400 mr-2">仅限VIP</span><i class="fas fa-chevron-right text-gray-400"></i>';
                    
                    // 更新AI模型设置点击事件
                    aiModelSetting.onclick = function() {
                        alert('此功能仅限VIP会员使用，请先升级');
                    };
                }
            });
            
            // VIP卡片升级按钮点击事件
            const upgradeButton = document.querySelector('.upgrade-button');
            upgradeButton.addEventListener('click', function(e) {
                e.stopPropagation();
                alert('即将开通VIP会员，享受全部高级功能');
                // 模拟升级成功，切换到VIP状态
                userTypeToggle.checked = true;
                userTypeToggle.dispatchEvent(new Event('change'));
            });
            
            // VIP详情按钮点击事件
            viewDetailBtn.addEventListener('click', function() {
                vipDetailModal.style.display = 'flex';
            });
            
            // 关闭弹窗
            closeModalBtn.addEventListener('click', function() {
                vipDetailModal.style.display = 'none';
            });
            
            // 点击弹窗外区域关闭弹窗
            vipDetailModal.addEventListener('click', function(e) {
                if (e.target === vipDetailModal) {
                    vipDetailModal.style.display = 'none';
                }
            });
            
            // 默认设置为免费用户
            userTypeToggle.checked = false;
            userTypeToggle.dispatchEvent(new Event('change'));
        });
    </script>
</body>
</html> 