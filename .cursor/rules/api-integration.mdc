---
description: 
globs: 
alwaysApply: false
---
# API和服务集成

## AI服务集成
- [aiService.ts](mdc:zhiwoAIinput/services/aiService.ts) 负责集成AI模型API
  - 使用siliconflow API接口：https://docs.siliconflow.cn/cn/api-reference/chat-completions/chat-completions
  - 基础模型：Qwen/Qwen2.5-7B-Instruct
  - VIP用户可使用高级模型
  - API调用格式：
  ```json
  {
    "model": "Qwen/Qwen2.5-7B-Instruct",
    "messages": [
      {
        "role": "system",
        "content": "你是一个文字润色专家，请按照以下风格对用户输入的文字进行优化：{模板提示词}"
      },
      {
        "role": "user",
        "content": "{用户输入的文字}"
      }
    ]
  }
  ```

## 语音服务集成
- [expoSpeechService.ts](mdc:zhiwoAIinput/services/expoSpeechService.ts) 负责语音识别
  - 使用设备原生语音识别能力
  - iOS：Speech Framework
  - Android：Speech Recognizer API
  - 提供跨平台兼容实现

## 数据库服务集成
- [supabaseService.ts](mdc:zhiwoAIinput/services/supabaseService.ts) 负责Supabase服务集成
  - 用户数据同步
  - 历史记录云存储
  - 模板同步

## 支付服务集成
- [purchaseService.ts](mdc:zhiwoAIinput/services/purchaseService.ts) 负责应用内购买
  - 集成RevenueCat服务
  - 处理订阅计划：¥18/月的VIP会员
  - 管理订阅状态验证
  - 处理苹果和谷歌支付平台

## 本地存储服务
- [storageService.ts](mdc:zhiwoAIinput/services/storageService.ts) 负责本地数据存储
  - 使用AsyncStorage存储数据
  - 存储内容包括：
    - 历史记录
    - 自定义模板
    - 用户偏好设置
    - VIP状态缓存

## 跨服务数据流
1. **语音识别到AI处理**:
   - `expoSpeechService` 识别语音
   - 识别结果传给 `aiService`
   - AI处理结果存储到 `historyService`

2. **模板管理**:
   - `templateService` 管理模板
   - 选择的模板传给 `aiService` 用于AI处理
   - 自定义模板存储到本地和云端

3. **订阅与权限**:
   - `purchaseService` 处理订阅
   - 订阅状态影响可用功能
   - VIP状态通过 `supabaseService` 同步到云端
