---
description: 
globs: 
alwaysApply: true
---
# 知我AI输入法开发指南

## 项目说明
"知我AI输入法"是一款利用AI技术帮助用户优化文字输入的跨平台React Native应用。用户可以通过语音输入文字，应用会调用AI接口对文字进行整理润色，使其符合用户选择的风格模板。

## 开发规则与要求

- 请根据需求描述 [Requirements.md](mdc:zhiwoAIinput/Requirements.md) 和原型设计开发应用，不应该跳脱出这个框架限制，原型文件位于zhiwo_prototype文件夹，格式为html文件。

- 使用技术栈如下：
  1. **React Native**：以Expo工作流为主，如有必要才使用bare工作流，或生成EAS Build
  2. **Redux**：处理状态变量的集中储存和状态流转
  3. **React Navigation**：处理导航系统
  4. **NativeWind**：Tailwindcss在React Native上的实现
  5. **RevenueCAT和Supabase**：配合处理应用内支付和VIP订阅管理功能
  6. **SiliconFlow API**：处理AI相关的接口调用，如有必要可以直接使用其他AI服务供应商的服务，比如OpenAI，Anthropic，Elevenlabs等
  7. **TypeScript**：使用typescript而不是javascript
  8. **Languine**：处理应用的全球化
  9. **Sentry**：处理产品相关的信息统计和错误上报

- 跨平台开发注意事项：
  - 应用将发布在Google Play和Apple App Store
  - 开发功能时需要考虑跨平台特性
  - 使用适用范围最广泛、有持续维护的第三方库
  - 如有必要，单独处理不同平台的功能需求

- 代码开发规则：
  - 除非特别说明，应该考虑当前应用的开发进度，不应随意修改已完成的功能
  - 项目文件夹位于 zhiwoAIinput，相关代码操作应该在这个路径下进行
  - 请参考 [project-structure.mdc](mdc:.cursor/rules/project-structure.mdc)、[redux-flow.mdc](mdc:.cursor/rules/redux-flow.mdc)、[core-components.mdc](mdc:.cursor/rules/core-components.mdc) 和 [api-integration.mdc](mdc:.cursor/rules/api-integration.mdc) 获取更详细的项目结构和开发规范