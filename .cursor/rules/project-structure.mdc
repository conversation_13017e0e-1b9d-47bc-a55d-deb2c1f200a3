---
description: 
globs: 
alwaysApply: false
---
# 知我AI输入法项目结构

## 主要目录结构
- [app](mdc:zhiwoAIinput/app)：应用的主要屏幕和导航结构
  - [(tabs)](mdc:zhiwoAIinput/app/(tabs))：标签页主要屏幕
    - [index.tsx](mdc:zhiwoAIinput/app/(tabs)/index.tsx)：输入主页
    - [history](mdc:zhiwoAIinput/app/(tabs)/history)：历史记录页面
    - [settings](mdc:zhiwoAIinput/app/(tabs)/settings)：设置页面
  - [_layout.tsx](mdc:zhiwoAIinput/app/_layout.tsx)：应用主布局
  - [payment.tsx](mdc:zhiwoAIinput/app/payment.tsx)：支付页面
  - [login.tsx](mdc:zhiwoAIinput/app/login.tsx)：登录页面
- [components](mdc:zhiwoAIinput/components)：UI组件
  - [input](mdc:zhiwoAIinput/components/input)：输入相关组件
  - [ui](mdc:zhiwoAIinput/components/ui)：通用UI组件
- [services](mdc:zhiwoAIinput/services)：API调用和业务逻辑
  - [aiService.ts](mdc:zhiwoAIinput/services/aiService.ts)：AI服务接口
  - [expoSpeechService.ts](mdc:zhiwoAIinput/services/expoSpeechService.ts)：语音识别服务
  - [historyService.ts](mdc:zhiwoAIinput/services/historyService.ts)：历史记录服务
  - [templateService.ts](mdc:zhiwoAIinput/services/templateService.ts)：模板管理服务
  - [purchaseService.ts](mdc:zhiwoAIinput/services/purchaseService.ts)：订阅购买服务
  - [supabaseService.ts](mdc:zhiwoAIinput/services/supabaseService.ts)：Supabase服务
- [store](mdc:zhiwoAIinput/store)：Redux状态管理
  - [slices](mdc:zhiwoAIinput/store/slices)：Redux状态切片
- [constants](mdc:zhiwoAIinput/constants)：常量定义
- [utils](mdc:zhiwoAIinput/utils)：工具函数
- [assets](mdc:zhiwoAIinput/assets)：图片和静态资源
- [types](mdc:zhiwoAIinput/types)：TypeScript类型定义

## 主要配置文件
- [app.config.ts](mdc:zhiwoAIinput/app.config.ts)：Expo应用配置
- [app.json](mdc:zhiwoAIinput/app.json)：应用配置
- [tailwind.config.js](mdc:zhiwoAIinput/tailwind.config.js)：NativeWind配置
- [tsconfig.json](mdc:zhiwoAIinput/tsconfig.json)：TypeScript配置
- [eas.json](mdc:zhiwoAIinput/eas.json)：EAS构建配置
- [languine.json](mdc:zhiwoAIinput/languine.json)：国际化配置

## 项目要求参考
- [Requirements.md](mdc:zhiwoAIinput/Requirements.md)：详细的应用需求和设计要求
