---
description: 
globs: 
alwaysApply: false
---
# Redux状态管理流程

## 状态结构
Redux用于管理应用的全局状态，主要包含以下Slice：

- [recordingSlice.ts](mdc:zhiwoAIinput/store/slices/recordingSlice.ts)：管理语音录制相关状态
  - 语音录制状态（开始、暂停、停止）
  - 文本识别结果
  - AI优化结果处理

- [templateSlice.ts](mdc:zhiwoAIinput/store/slices/templateSlice.ts)：管理风格模板
  - 预设模板列表
  - 自定义模板
  - 当前选中模板

- [historySlice.ts](mdc:zhiwoAIinput/store/slices/historySlice.ts)：管理历史记录
  - 保存历史记录
  - 查询历史记录
  - 删除历史记录

- [authSlice.ts](mdc:zhiwoAIinput/store/slices/authSlice.ts)：管理用户认证状态
  - 登录状态
  - 用户信息

- [subscriptionSlice.ts](mdc:zhiwoAIinput/store/slices/subscriptionSlice.ts)：管理订阅相关状态
  - 订阅状态
  - 订阅产品信息

- [vipSlice.ts](mdc:zhiwoAIinput/store/slices/vipSlice.ts)：管理VIP相关状态
  - VIP特权
  - VIP状态

- [themeSlice.ts](mdc:zhiwoAIinput/store/slices/themeSlice.ts)：管理主题设置
  - 深色/浅色模式

## 数据流向

1. **语音输入流程**：
   - [RecordButton.tsx](mdc:zhiwoAIinput/components/input/RecordButton.tsx) 触发录音
   - [expoSpeechService.ts](mdc:zhiwoAIinput/services/expoSpeechService.ts) 处理语音识别
   - `recordingSlice` 更新识别结果
   - [RecognizedTextDisplay.tsx](mdc:zhiwoAIinput/components/input/RecognizedTextDisplay.tsx) 显示识别文本

2. **AI优化流程**：
   - 选择模板后触发AI优化
   - [aiService.ts](mdc:zhiwoAIinput/services/aiService.ts) 调用AI接口
   - `recordingSlice` 更新优化结果
   - [OptimizedTextDisplay.tsx](mdc:zhiwoAIinput/components/input/OptimizedTextDisplay.tsx) 显示优化文本

3. **历史记录管理**：
   - AI优化完成后保存到历史记录
   - [historyService.ts](mdc:zhiwoAIinput/services/historyService.ts) 处理历史数据
   - `historySlice` 更新历史列表
   - 历史页面展示记录

4. **用户管理流程**：
   - 登录/登出操作通过 `authSlice` 管理
   - 用户信息存储在 `authSlice`
   - 登录状态影响功能权限

5. **VIP订阅流程**：
   - 购买操作通过 [purchaseService.ts](mdc:zhiwoAIinput/services/purchaseService.ts) 处理
   - 订阅信息存储在 `subscriptionSlice` 和 `vipSlice`
   - VIP状态影响可用功能和权限

## Store创建
全局状态在 [store/index.ts](mdc:zhiwoAIinput/store/index.ts) 中配置，使用Redux Toolkit创建。
