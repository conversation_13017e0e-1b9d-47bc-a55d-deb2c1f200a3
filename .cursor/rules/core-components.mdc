---
description:
globs:
alwaysApply: false
---
# 核心组件结构与功能

## 输入相关组件
- [RecordButton.tsx](mdc:zhiwoAIinput/components/input/RecordButton.tsx)：语音录制按钮
  - 控制语音录制的开始、暂停和停止
  - 显示录制状态（未录制、录制中、暂停）
  - 显示动画效果

- [AnimatedVoiceWave.tsx](mdc:zhiwoAIinput/components/input/AnimatedVoiceWave.tsx)：声波动画
  - 显示声音输入的实时动画效果
  - 基于声音强度变化动画

- [RecognizedTextDisplay.tsx](mdc:zhiwoAIinput/components/input/RecognizedTextDisplay.tsx)：识别文本显示
  - 显示语音识别的文本结果
  - 处理文本显示样式和格式

- [OptimizedTextDisplay.tsx](mdc:zhiwoAIinput/components/input/OptimizedTextDisplay.tsx)：优化文本显示
  - 显示AI优化后的文本
  - 提供复制和分享功能

- [TemplateSelector.tsx](mdc:zhiwoAIinput/components/input/TemplateSelector.tsx)：模板选择器
  - 显示可选的风格模板列表
  - 处理模板选择的交互逻辑

- [TemplateCard.tsx](mdc:zhiwoAIinput/components/input/TemplateCard.tsx)：模板卡片
  - 单个模板的显示组件
  - 处理选中状态和视觉效果

- [FeatureTags.tsx](mdc:zhiwoAIinput/components/input/FeatureTags.tsx)：功能标签
  - 显示应用功能特性的标签
  - 用于展示"智能润色"、"场景适配"等功能

## 主屏幕页面
- [app/(tabs)/index.tsx](mdc:zhiwoAIinput/app/(tabs)/index.tsx)：输入主页
  - 整合录音、识别、AI处理和显示功能
  - 控制录音流程和AI处理流程
  - 管理用户交互状态

- [app/(tabs)/history](mdc:zhiwoAIinput/app/(tabs)/history)：历史记录页面
  - 显示用户历史记录列表
  - 提供历史记录详情查看
  - 支持历史记录的搜索和筛选

- [app/(tabs)/settings](mdc:zhiwoAIinput/app/(tabs)/settings)：设置页面
  - 提供VIP会员管理
  - 提供应用偏好设置
  - 提供关于应用的信息

## 支付和认证
- [PaymentWall.tsx](mdc:zhiwoAIinput/components/PaymentWall.tsx)：支付墙组件
  - 展示VIP特权和价格
  - 处理订阅购买流程
  - 显示VIP状态信息

- [AuthProvider.tsx](mdc:zhiwoAIinput/components/AuthProvider.tsx)：认证提供者
  - 管理用户认证状态
  - 提供登录和登出功能
  - 处理认证信息的持久化

## 服务接口
- [aiService.ts](mdc:zhiwoAIinput/services/aiService.ts)：AI服务
  - 处理与AI模型的接口调用
  - 发送文本和模板到AI处理
  - 接收和解析AI处理结果

- [expoSpeechService.ts](mdc:zhiwoAIinput/services/expoSpeechService.ts)：语音服务
  - 处理语音识别功能
  - 跨平台管理语音录制和转写
  - 提供语音状态回调

- [historyService.ts](mdc:zhiwoAIinput/services/historyService.ts)：历史记录服务
  - 管理历史记录的存储和检索
  - 提供增删改查功能
  - 处理历史数据的格式化

- [templateService.ts](mdc:zhiwoAIinput/services/templateService.ts)：模板服务
  - 管理模板的存储和检索
  - 处理预设模板和自定义模板
  - 提供模板CRUD操作

- [purchaseService.ts](mdc:zhiwoAIinput/services/purchaseService.ts)：购买服务
  - 管理应用内购买和订阅
  - 处理订阅状态验证
  - 与RevenueCAT集成
